import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/content',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '内容管理', icon: 'datareport' },
        children: [
            {
                path: 'editor',
                name: 'ContentEditor',
                meta: { title: '文章编辑器' },
                component: () => import('./ui/article/editor.vue'),
            },
            {
                path: 'list',
                name: 'ContentList',
                meta: { title: '文章列表' },
                component: () => import('./ui/article/list.vue'),
            }
        ]
    }
]

export default router