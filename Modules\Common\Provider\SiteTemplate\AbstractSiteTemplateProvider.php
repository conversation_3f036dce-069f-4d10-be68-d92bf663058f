<?php

namespace Modules\Common\Provider\SiteTemplate;

/**
 * Class AbstractSiteTemplateProvider
 * @package Modules\Common\Provider\SiteTemplate
 * @since 1.5.0
 */
abstract class AbstractSiteTemplateProvider
{
    abstract public function name();

    abstract public function title();

    public function root(): null
    {
        return null;
    }

    /**
     * 主题是否有额外定制配置
     * @return false
     */
    public function hasConfig(): false
    {
        return false;
    }

}
