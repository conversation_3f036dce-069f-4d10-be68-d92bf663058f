import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/live-channel',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '直播管理', icon: 'datareport' },
        children: [
            {
                path: 'list',
                name: 'LiveChannelList',
                meta: { title: '直播列表' },
                component: () => import('./ui/list.vue'),
            },
            {
                path: 'api-settings',
                name: 'LiveChannelApiSettings',
                meta: { title: 'API設置' },
                component: () => import('./ui/apiSet.vue'),
            }
        ]
    }
]

export default router
