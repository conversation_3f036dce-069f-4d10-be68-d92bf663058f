import { defineStore } from 'pinia'
import Cache from '/admin/support/cache'

export type Breadcrumb = {
  name: string
  path: string
  i18n?: boolean
}
/**
 * app
 */
type app = {
  size: 'small' | 'medium' | 'large'
  isExpand: boolean
  locale: string
  isMobile: boolean
  isDarkMode: boolean
  activeMenu: string
  pageName: string
  breadcrumbs: Breadcrumb[]
  currentSite: any | null
}

export const useAppStore = defineStore('app', {
  state: (): app => ({
    size: 'small',
    isExpand: true,
    locale: Cache.get('language'),
    isMobile: false,
    isDarkMode: false,
    activeMenu: '/dashboard',
    pageName: '',
    breadcrumbs: [],
    currentSite: null
  }),

  getters: {
    getSize(): string {
      return this.size
    },

    getLocale(): string {
      return this.locale
    },

    getIsMobile(): boolean {
      return this.isMobile
    },

    getIsDarkMode(): boolean {
      return this.isDarkMode
    },

    getActiveMenu(): string {
      return this.activeMenu
    },

    getPageName(): string {
      return this.pageName
    },

    getBreadcrumbs(): Breadcrumb[] {
      return this.breadcrumbs
    },

    getCurrentSite(): any | null {
      return this.currentSite
    }
  },

  actions: {
    changeSize(size: 'small' | 'medium' | 'large'): void {
      this.size = size
    },

    changeLocale(locale: string): void {
      Cache.set('language', locale)
      this.locale = locale
    },

    changeExpaned(): void {
      this.isExpand = !this.isExpand
    },

    setDarkMode(isDarkMode: boolean): void {
      this.isDarkMode = isDarkMode
    },

    setActiveMenu(activeMenu: string): void {
      this.activeMenu = activeMenu.startsWith('/') ? activeMenu : '/' + activeMenu
    },

    setPageName(pagename: string): void {
      this.pageName = pagename
    },

    setBreadcrumbs(breadcrumbs: Breadcrumb[]): void {
      this.breadcrumbs = breadcrumbs
    },

    // 添加单个面包屑
    addBreadcrumb(breadcrumb: Breadcrumb): void {
      // 检查是否已存在相同路径的面包屑
      const existIndex = this.breadcrumbs.findIndex(item => item.path === breadcrumb.path)
      if (existIndex !== -1) {
        // 如果存在，则更新名称
        this.breadcrumbs[existIndex].name = breadcrumb.name
      } else {
        // 如果不存在，则添加
        this.breadcrumbs.push(breadcrumb)
      }
    },

    // 清空面包屑
    clearBreadcrumbs(): void {
      this.breadcrumbs = []
    },

    // 设置当前站点
    setCurrentSite(site: any): void {
      Cache.set('site', JSON.stringify(site))
      this.currentSite = site
    }
  },
})
