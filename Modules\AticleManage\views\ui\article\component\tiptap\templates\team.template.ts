export const teamTemplate = `
<div data-bs-component="team-block" class="py-5 team-section">
  <div class="container">
    <div class="mb-4 text-center">
      <div data-bs-component="richTextBlock">
        <h2 class="display-6 fw-bold">Our team</h2>
        <p class="text-muted">Meet the awesome folks who make all of this possible day to day</p>
      </div>
    </div>
    <div class="row">
      <!-- 团队成员 1 -->
      <div class="mb-4 col-md-3 col-sm-6">
        <div class="text-center team-card">
          <div class="mb-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid rounded-4" alt="Team member">
          </div>
          <div data-bs-component="richTextBlock">
            <h5 class="mb-1">Team member</h5>
            <p class="text-muted small">Role here</p>
          </div>
        </div>
      </div>
      <!-- 团队成员 2 -->
      <div class="mb-4 col-md-3 col-sm-6">
        <div class="text-center team-card">
          <div class="mb-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-2.webp" class="img-fluid rounded-4" alt="Team member">
          </div>
          <div data-bs-component="richTextBlock">
            <h5 class="mb-1">Team member</h5>
            <p class="text-muted small">Role here</p>
          </div>
        </div>
      </div>
      <!-- 团队成员 3 -->
      <div class="mb-4 col-md-3 col-sm-6">
        <div class="text-center team-card">
          <div class="mb-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-3.webp" class="img-fluid rounded-4" alt="Team member">
          </div>
          <div data-bs-component="richTextBlock">
            <h5 class="mb-1">Team member</h5>
            <p class="text-muted small">Role here</p>
          </div>
        </div>
      </div>
      <!-- 团队成员 4 -->
      <div class="mb-4 col-md-3 col-sm-6">
        <div class="text-center team-card">
          <div class="mb-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid rounded-4" alt="Team member">
          </div>
          <div data-bs-component="richTextBlock">
            <h5 class="mb-1">Team member</h5>
            <p class="text-muted small">Role here</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.team-section {
  padding: 80px 0;
}

.team-card {
  transition: transform 0.3s ease;
}

.team-card img {
  transition: transform 0.5s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-card:hover {
  transform: translateY(-5px);
}

.team-card:hover img {
  transform: scale(1.03);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

@media (max-width: 767.98px) {
  .team-card {
    margin-bottom: 1.5rem;
  }
}
</style>
` 