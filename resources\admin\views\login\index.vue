<template>
  <div class="flex items-center justify-center h-screen bg-gray-50">
    <div class="flex w-full sm:w-[32rem] shadow bg-white lg:rounded-lg">
      <!--<div class="hidden w-1/2 sm:block">
              <img src="@/assets/login-left.png" />
            </div>-->
      <div class="w-full pt-6 pb-6 pl-4 pr-4 mx-auto">
        <div class="flex mt-2">
          <img src="https://www.hk-bingo.com/images/website/logo/logo.png" style="width: 200px; height: 85px" class="mx-auto img-responsive center-block img-circle" />
        </div>
        <div class="w-full mt-6 mb-8 text-2xl text-center text-indigo-700">Hi, {{ $t('login.welcome') }}</div>
        <el-divider>{{ $t('login.sign_in') }}</el-divider>
        <div>
          <el-form
            ref="form"
            :model="params"
            status-icon
            v-loading.fullscreen.lock="loading"
            :rules="rules"
            element-loading-background="rgba(0, 0, 0, 0.7)"
            label-width="70px"
            class="w-11/12 pt-2 mx-auto space-y-8 sm:w-4/5"
          >
            <el-form-item prop="email">
              <el-input v-model="params.email" type="email" autocomplete="off" :placeholder="$t('login.email')" size="large" :prefix-icon="Message" class="h-12 text-base" />
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="params.password" type="password" autocomplete="off" size="large" :placeholder="$t('login.password')" show-password :prefix-icon="Lock" class="h-12 text-base" />
            </el-form-item>
            <el-form-item prop="lang">
              <el-select v-model="lang" size="large">
                <el-option v-for="item in langList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex justify-between w-11/12 mx-auto mt-3 sm:w-4/5">
          <el-checkbox v-model="params.remember" class="top-2">
            {{ $t('login.remember') }}
          </el-checkbox>
          <div class="pt-3 text-sm text-indigo-600 cursor-pointer">
            {{ $t('login.lost_password') }}
          </div>
        </div>
        <div class="w-11/12 mx-auto mt-5 sm:w-4/5">
          <el-button type="primary" @click="loginHandle" size="large" class="w-full text-xl">
            {{ $t('login.sign_in') }}
          </el-button>
        </div>

        <div class="w-full mt-8 mb-10 text-sm text-center text-gray-400">{{ $t('system.name') }} @copyright 2018-{{ new Date().getFullYear() }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Lock, Message } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useLogin } from './login'
import { useAppStore } from '/admin/stores/modules/app'

const appStore = useAppStore()

const { params, loading, submit, form, rules } = useLogin()

const lang = ref(appStore.getLocale)
const langList = reactive([
  {
    label: '简体中文',
    value: 'zh_CN',
  },
  {
    label: '繁體中文',
    value: 'zh_HK',
  },
  {
    label: 'English',
    value: 'en',
  },
])
const loginHandle = () => {
  localStorage.setItem('bwms_language', lang.value)
  submit(form.value)
}

// set default color-theme light
onMounted(() => {
  document.querySelector('html')?.setAttribute('class', 'light')
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-divider__text) {
  @apply text-xl text-slate-400;
}
</style>
