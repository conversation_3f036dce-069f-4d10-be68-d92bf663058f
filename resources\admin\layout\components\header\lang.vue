<template>
  <div class="w-10 h-10 grid place-items-center rounded-full hover:cursor-pointer">
    <div class="flex hover:cursor-pointer pl-1 pr-1">
      <el-dropdown size="large" class="flex items-center justify-center hover:cursor-pointer w-full" @command="selectLanguage">
        <Icon name="language" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="lang in langs" :key="lang.value" :command="lang.value" :disabled="lang.value == defaultLang">
<!--              {{ $t('system.' + lang.label) }}-->
                {{ lang.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed } from 'vue'
import { useAppStore } from '/admin/stores/modules/app'
import http from '/admin/support/http'
import { Code } from '/admin/enum/app'
import { Message } from '@element-plus/icons-vue'

interface Language {
    value: string;
    label: string;
}

// 使用接口来注明langs的类型
const langs = reactive<Language[]>([]);

const appStore = useAppStore()
// select default languages
const defaultLang = computed(() => {
  return appStore.getLocale
})

// select language
const selectLanguage = (value: string) => {
  appStore.changeLocale(value)
  location.reload()
}

// 定义获取语言列表的函数
const fetchLanguages = () => {
    http.get('/user/lang')
        .then(response => {
            const { code, data } = response.data;
            if (code === Code.SUCCESS) {
                langs.splice(0, langs.length, ...data);
            } else {
                Message.error('Failed to load languages');
            }
        })
        .catch(error => {
            console.error('Error fetching languages:', error);
            Message.error('Error fetching languages');
        })
        .finally(() => {
            // 这里可以放置任何清理代码，比如停止加载指示器
        });
};

// 在组件挂载后获取语言列表
onMounted(fetchLanguages);
</script>
