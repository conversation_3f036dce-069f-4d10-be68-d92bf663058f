/**
 * Feature List 模板
 * 提供多种功能特性列表展示
 */

export const featureListTemplate = `
<div data-bs-component="feature-list" class="py-5 bootstrap-feature-list responsive-block">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="row feature-list-container">
          <!-- 内容创作 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-file-alt text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">内容创作</h5>
                <p class="feature-description">多样化的内容编辑工具，支持富文本、图片和视频等多种媒体格式。</p>
              </div>
            </div>
          </div>
          
          <!-- 数据分析 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-chart-line text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">数据分析</h5>
                <p class="feature-description">强大的数据分析和报告功能，帮助您了解用户行为和内容效果。</p>
              </div>
            </div>
          </div>
          
          <!-- 消息互动 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-comments text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">消息互动</h5>
                <p class="feature-description">实时消息通知和用户互动功能，提高用户参与度和留存率。</p>
              </div>
            </div>
          </div>
          
          <!-- 系统集成 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-puzzle-piece text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">系统集成</h5>
                <p class="feature-description">无缝集成各类第三方服务和API，扩展系统功能和应用场景。</p>
              </div>
            </div>
          </div>
          
          <!-- 自动化工作流 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-magic text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">自动化工作流</h5>
                <p class="feature-description">自动化处理重复任务，提高工作效率和减少人为错误。</p>
              </div>
            </div>
          </div>
          
          <!-- 活动计划 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-calendar text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">活动计划</h5>
                <p class="feature-description">高效的活动策划和安排工具，支持团队协作和进度跟踪。</p>
              </div>
            </div>
          </div>
          
          <!-- AI智能助手 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-robot text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">AI智能助手</h5>
                <p class="feature-description">人工智能驱动的智能助手，提供个性化建议和自动化决策支持。</p>
              </div>
            </div>
          </div>
          
          <!-- 多渠道分发 -->
          <div class="mb-4 col-12 col-md-6">
            <div class="feature-list-card">
              <div class="feature-icon">
                <i class="fas fa-share-alt text-primary"></i>
              </div>
              <div class="feature-content">
                <h5 class="feature-title">多渠道分发</h5>
                <p class="feature-description">一键发布内容到多个平台，扩大内容影响力和用户覆盖范围。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <style>
    .bootstrap-feature-list {
      background-color: #fff;
      padding: 30px 0;
    }
    .feature-list-container {
      margin-top: 20px;
    }
    .feature-list-card {
      display: flex;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      height: 100%;
      transition: all 0.3s ease;
    }
    .feature-list-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transform: translateY(-5px);
    }
    .feature-icon {
      flex: 0 0 40px;
      margin-right: 15px;
      font-size: 24px;
      color: #4e73df;
    }
    .feature-content {
      flex: 1;
    }
    .feature-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    .feature-description {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 0;
    }

    /* 移动端预览模式样式 */
    .mobile-preview .bootstrap-feature-list [class*="col-"] {
      width: 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }

    .mobile-preview .bootstrap-feature-list .feature-list-card {
      flex-direction: row;
      text-align: left;
      padding: 15px;
      margin-bottom: 10px;
    }

    .mobile-preview .bootstrap-feature-list .feature-icon {
      flex: 0 0 40px;
      margin-right: 15px;
      margin-bottom: 0;
      font-size: 24px;
    }

    .mobile-preview .bootstrap-feature-list .feature-title {
      font-size: 1rem;
      margin-bottom: 5px;
    }

    .mobile-preview .bootstrap-feature-list .feature-description {
      font-size: 0.85rem;
      line-height: 1.4;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .bootstrap-feature-list [class*="col-"] {
        width: 100%;
        flex: 0 0 100%;
        max-width: 100%;
      }

      .bootstrap-feature-list .feature-list-card {
        flex-direction: row;
        text-align: left;
        padding: 15px;
        margin-bottom: 10px;
      }

      .bootstrap-feature-list .feature-icon {
        flex: 0 0 40px;
        margin-right: 15px;
        margin-bottom: 0;
        font-size: 24px;
      }

      .bootstrap-feature-list .feature-title {
        font-size: 1rem;
        margin-bottom: 5px;
      }

      .bootstrap-feature-list .feature-description {
        font-size: 0.85rem;
        line-height: 1.4;
      }
    }

    /* 平板端样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .bootstrap-feature-list .feature-list-card {
        padding: 15px;
      }

      .bootstrap-feature-list .feature-icon {
        flex: 0 0 35px;
        font-size: 22px;
      }

      .bootstrap-feature-list .feature-title {
        font-size: 1rem;
      }

      .bootstrap-feature-list .feature-description {
        font-size: 0.85rem;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .bootstrap-feature-list .feature-list-card {
        padding: 20px;
      }

      .bootstrap-feature-list .feature-icon {
        flex: 0 0 40px;
        font-size: 24px;
      }

      .bootstrap-feature-list .feature-title {
        font-size: 1.1rem;
      }

      .bootstrap-feature-list .feature-description {
        font-size: 0.9rem;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .bootstrap-feature-list [class*="col-"] {
      width: 50%;
      flex: 0 0 50%;
      max-width: 50%;
    }

    .desktop-preview .bootstrap-feature-list .feature-list-card {
      padding: 20px;
      flex-direction: row;
      text-align: left;
    }

    .desktop-preview .bootstrap-feature-list .feature-icon {
      flex: 0 0 40px;
      margin-right: 15px;
      margin-bottom: 0;
      font-size: 24px;
    }

    .desktop-preview .bootstrap-feature-list .feature-title {
      font-size: 1.1rem;
    }

    .desktop-preview .bootstrap-feature-list .feature-description {
      font-size: 0.9rem;
    }
  </style>
</div>
`; 