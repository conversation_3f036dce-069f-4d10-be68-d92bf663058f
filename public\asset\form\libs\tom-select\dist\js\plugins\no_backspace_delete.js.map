{"version": 3, "file": "no_backspace_delete.js", "sources": ["../../../src/plugins/no_backspace_delete/plugin.ts"], "sourcesContent": ["/**\n * Plugin: \"input_autogrow\" (Tom <PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tvar orig_deleteSelection = self.deleteSelection;\n\n\tthis.hook('instead','deleteSelection',(evt:KeyboardEvent) => {\n\n\t\tif( self.activeItems.length ){\n\t\t\treturn orig_deleteSelection.call(self, evt);\n\t\t}\n\n\t\treturn false;\n\t});\n\n};\n"], "names": ["self", "orig_deleteSelection", "deleteSelection", "hook", "evt", "activeItems", "length", "call"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAIe,eAAyB,IAAA;CACvC,EAAIA,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,EAAA,IAAIC,oBAAoB,GAAGD,IAAI,CAACE,eAAhC,CAAA;CAEA,EAAA,IAAA,CAAKC,IAAL,CAAU,SAAV,EAAoB,iBAApB,EAAuCC,GAAD,IAAuB;CAE5D,IAAA,IAAIJ,IAAI,CAACK,WAAL,CAAiBC,MAArB,EAA6B;CAC5B,MAAA,OAAOL,oBAAoB,CAACM,IAArB,CAA0BP,IAA1B,EAAgCI,GAAhC,CAAP,CAAA;CACA,KAAA;;CAED,IAAA,OAAO,KAAP,CAAA;CACA,GAPD,CAAA,CAAA;CASA;;;;;;;;"}