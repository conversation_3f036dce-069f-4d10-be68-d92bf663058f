<template>
  <div class="layout-container">
    <div class="layout-content">
      <!-- 左侧菜单 - 直接使用传入的数据 -->
      <div class="layout-aside">
        <div class="menu-container">
          <!-- 菜单列表 -->
          <div class="config-menu-list">
            <template v-for="group in menuGroups" :key="group.name">
              <template v-for="item in group.list" :key="item.name">
                <div 
                  class="menu-item" 
                  :class="{ active: activeMenu === item.name }"
                  @click="onMenuItemClick(item)"
                >
                  <div class="menu-item-icon">
                    <el-icon class="menu-icon">
                      <component :is="item.icon" />
                    </el-icon>
                  </div>
                  <div class="menu-item-content">
                    <div class="menu-label">{{ item.label }}</div>
                    <div class="menu-desc">{{ item.description }}</div>
                  </div>
                  <el-icon class="menu-arrow"><arrow-right /></el-icon>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <el-main class="layout-main">
        <div class="content-container">
          <router-view />
        </div>
      </el-main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'


// 定义props接收菜单数据和动态组件
const props = defineProps<{
  menuGroups: Array<{
    name?: string;
    label?: string;
    list?: Array<{
      name?: string;
      label?: string;
      description?: string;
      icon?: string;
      url?: string;
    }>;
  }>;
  // 可选的动态组件
  dynamicComponent?: any;
  // 当前激活的菜单项
  activeMenuName?: string;
}>()

// 定义emit以通知父组件
const emit = defineEmits<{
  (e: 'onMenuItemClick', data: any): void;
  (e: 'update:activeMenuName', name: string): void;
}>()

// 获取路由
const route = useRoute()

// 活动菜单项
const activeMenu = computed(() => {
  return props.activeMenuName || route.path
})

// 菜单项点击，通知父组件进行导航
const onMenuItemClick = (item: any) => {
  emit('onMenuItemClick', item)
  emit('update:activeMenuName', item.name)
}

// 暴露组件名称，使其可以被引用
defineExpose({
  name: 'ConfigLayout',
  activeMenu
})
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;

  .layout-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .layout-aside {
      position: relative;
      width: 300px;
      transition: width 0.3s;
      padding-top: 10px;
      overflow-y: auto;
      
      .menu-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        gap: 10px;
        padding: 12px 0;
        border-radius: 20px;
        box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        background-color: #fff;
      }
    }
    
    .layout-main {
      flex: 1;
      overflow: hidden;
      padding: 12px 0 0 12px;
      
      .content-container {
        background-color: #fff;
        border-radius: 20px;
        box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        padding-bottom: 12px;
        padding-top: 12px;
        overflow: auto;
        height: 100%;
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(64, 158, 255, 0.3);
          border-radius: 3px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.5);
          }
        }
      }
    }
  }
}

// 菜单折叠后的样式
.el-menu--collapse {
  width: 80px;
  
  .menu-desc {
    display: none;
  }
  
  .menu-item-icon {
    margin: 14px auto !important;
  }
  
  .menu-arrow {
    display: none;
  }
}

// 路由切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 菜单容器样式
.config-menu-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 10px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.active {
      background-color: #ecf5ff;
      
      .menu-label {
        color: #409eff;
        font-weight: 500;
      }
      
      .menu-item-icon {
        background-color: #409eff;
        
        .menu-icon {
          color: #fff;
        }
      }
      
      .menu-arrow {
        color: #409eff;
      }
    }
    
    .menu-item-icon {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f7ff;
      border-radius: 8px;
      margin-right: 12px;
      
      .menu-icon {
        font-size: 18px;
        color: #409eff;
      }
    }
    
    .menu-item-content {
      flex: 1;
      overflow: hidden;
      
      .menu-label {
        font-size: 14px;
        font-weight: 400;
        color: #303133;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .menu-desc {
        font-size: 12px;
        color: #909399;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .menu-arrow {
      color: #c0c4cc;
      font-size: 14px;
      margin-left: 8px;
    }
  }
}
</style>

<script lang="ts">
export default {
  name: 'ConfigLayout'
}
</script>
