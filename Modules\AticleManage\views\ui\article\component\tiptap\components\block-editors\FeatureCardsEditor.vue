<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.featureCardsEditor.contentTab')" name="content">
        <el-form label-position="top" size="small">
          <!-- 背景设置 -->
          <el-form-item :label="$t('Editor.featureCardsEditor.bgColor')">
            <el-select v-model="bgColor" @change="markAsChanged" style="width: 100%">
              <el-option :label="$t('Editor.featureCardsEditor.bgColorDark')" value="dark" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorLight')" value="light" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorPrimary')" value="primary" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorSecondary')" value="secondary" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorSuccess')" value="success" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorInfo')" value="info" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorWarning')" value="warning" />
              <el-option :label="$t('Editor.featureCardsEditor.bgColorDanger')" value="danger" />
            </el-select>
          </el-form-item>
          
          <!-- 特性卡片列表 -->
          <el-form-item :label="$t('Editor.featureCardsEditor.featureCards')">
            <div v-for="(card, index) in featureCards" :key="index" class="card-item-edit">
              <div class="card-item-header">
                <span>{{$t('Editor.featureCardsEditor.card')}} #{{ index + 1 }}</span>
                <div class="card-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    :title="$t('Editor.featureCardsEditor.edit')"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="featureCards.length > 1"
                    :title="$t('Editor.featureCardsEditor.delete')"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="card-item-content">
                <el-form-item :label="$t('Editor.featureCardsEditor.icon')">
                  <el-select v-model="card.icon" :placeholder="$t('Editor.featureCardsEditor.iconPlaceholder')" style="width: 100%" @change="markAsChanged">
                    <el-option :label="$t('Editor.featureCardsEditor.play')" value="play-circle" />
                    <el-option :label="$t('Editor.featureCardsEditor.barChart')" value="bar-chart" />
                    <el-option :label="$t('Editor.featureCardsEditor.graphUp')" value="graph-up" />
                    <el-option :label="$t('Editor.featureCardsEditor.star')" value="star" />
                    <el-option :label="$t('Editor.featureCardsEditor.heart')" value="heart" />
                    <el-option :label="$t('Editor.featureCardsEditor.lightning')" value="lightning" />
                    <el-option :label="$t('Editor.featureCardsEditor.tools')" value="tools" />
                    <el-option :label="$t('Editor.featureCardsEditor.gear')" value="gear" />
                    <el-option :label="$t('Editor.featureCardsEditor.phone')" value="phone" />
                    <el-option :label="$t('Editor.featureCardsEditor.globe')" value="globe" />
                    <el-option :label="$t('Editor.featureCardsEditor.wallet')" value="wallet" />
                    <el-option :label="$t('Editor.featureCardsEditor.person')" value="person" />
                  </el-select>
                </el-form-item>
                
                <el-form-item :label="$t('Editor.featureCardsEditor.title')">
                  <el-input v-model="card.title" @input="markAsChanged" />
                </el-form-item>
                
                <el-form-item :label="$t('Editor.featureCardsEditor.description')">
                  <el-input v-model="card.description" type="textarea" :rows="3" @input="markAsChanged" />
                </el-form-item>
              </div>
            </div>
            
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">{{$t('Editor.featureCardsEditor.addCard')}}</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane :label="$t('Editor.featureCardsEditor.styleTab')" name="style">
        <StyleEditor
          :styles="localStyles"
          :active-tab="activeTab"
          @update-styles="updateStyles"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 應用按鈕，只在有更改時顯示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.featureCardsEditor.applyChanges')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'
import { defineAsyncComponent } from 'vue'
import { useI18n } from 'vue-i18n'
const StyleEditor = defineAsyncComponent(() => import('./StyleEditor.vue'))

// 定義組件名稱
defineOptions({
  name: 'FeatureCardsEditor'
})

// 定義Props
const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  },
  styles: {
    type: Object,
    default: () => ({})
  }
})

// 定義Emits
const emit = defineEmits(['update-block', 'update-styles'])

// 當前激活的標籤
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 編輯中的卡片索引
const editingIndex = ref(0)

// 原始HTML
const originalHtml = ref('')

// 背景顏色
const bgColor = ref('dark')

// 本地樣式
const localStyles = ref({ ...props.styles })

// 卡片項定義
interface FeatureCard {
  icon: string;
  title: string;
  description: string;
}

// 特性卡片列表
const featureCards = ref<FeatureCard[]>([
  {
    icon: 'play-circle',
    title: '內容創建',
    description: '用我們引人入勝的內容在社交服務、視頻中脫穎而出，吸引數位受眾'
  },
  {
    icon: 'bar-chart',
    title: '行銷分析',
    description: '我們的平台助力明智決策，追蹤活動成效、投資回報率（ROI）'
  },
  {
    icon: 'graph-up',
    title: '旅程優化',
    description: '利用客戶數據推送個性化訊息與內容，提升互動參與度'
  }
])

/**
 * 設置默認值
 */
const setDefaultValues = () => {
  featureCards.value = [
    {
      icon: 'play-circle',
      title: '內容創建',
      description: '用我們引人入勝的內容在社交服務、視頻中脫穎而出，吸引數位受眾'
    },
    {
      icon: 'bar-chart',
      title: '行銷分析',
      description: '我們的平台助力明智決策，追蹤活動成效、投資回報率（ROI）'
    },
    {
      icon: 'graph-up',
      title: '旅程優化',
      description: '利用客戶數據推送個性化訊息與內容，提升互動參與度'
    }
  ]
  bgColor.value = 'dark'
  editingIndex.value = 0
  localStyles.value = { ...props.styles }
}

/**
 * 提取特性卡片數據
 */
const extractFeatureCards = (): boolean => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 提取背景顏色
    const blockClasses = Array.from(props.blockElement.classList || [])
    const bgColorClass = blockClasses.find(cls => 
      ['bg-dark', 'bg-light', 'bg-primary', 'bg-secondary', 'bg-success', 'bg-info', 'bg-warning', 'bg-danger'].includes(cls)
    )
    
    if (bgColorClass) {
      bgColor.value = bgColorClass.replace('bg-', '')
    }
    
    // 提取卡片數據
    const cardElements = props.blockElement.querySelectorAll('.feature-card')
    if (cardElements && cardElements.length > 0) {
      const extractedCards: FeatureCard[] = []
      
      cardElements.forEach(card => {
        // 提取圖標
        const iconElement = card.querySelector('svg')
        let iconType = 'play-circle'
        
        if (iconElement) {
          // 嘗試從class獲取圖標類型
          const iconClass = iconElement.classList[0]
          if (iconClass && iconClass.startsWith('bi-')) {
            iconType = iconClass.replace('bi-', '')
          } else {
            // 或從viewBox或其他屬性推斷
            const paths = iconElement.querySelectorAll('path')
            if (paths.length > 0) {
              if (paths.length === 2 && paths[0].getAttribute('d')?.includes('A7 7')) {
                iconType = 'play-circle'
              } else if (paths.length === 1 && paths[0].getAttribute('d')?.includes('bar-chart')) {
                iconType = 'bar-chart'
              } else if (paths.length === 1 && paths[0].getAttribute('d')?.includes('graph-up')) {
                iconType = 'graph-up'
              }
            }
          }
        }
        
        // 提取標題
        const titleElement = card.querySelector('h3')
        const title = titleElement ? titleElement.textContent || '' : ''
        
        // 提取描述
        const descElement = card.querySelector('p')
        const description = descElement ? descElement.textContent || '' : ''
        
        extractedCards.push({
          icon: iconType,
          title,
          description
        })
      })
      
      if (extractedCards.length > 0) {
        featureCards.value = extractedCards
        return true
      }
    }
    
    return false
  } catch (error) {
    console.error('提取特性卡片數據時出錯:', error)
    return false
  }
}

// 監聽 blockElement 的變化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改狀態
    isChanged.value = false
    
    // 重新提取數據
  const extracted = extractFeatureCards()
  
    // 如果提取失敗，使用默認值
  if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 標記為已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加卡片
const addItem = () => {
  featureCards.value.push({
    icon: 'star',
    title: '新特性',
    description: '添加新特性的描述文本'
  })
  editingIndex.value = featureCards.value.length - 1
  markAsChanged()
}

// 編輯卡片
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 刪除卡片
const removeItem = (index: number) => {
  if (featureCards.value.length > 1) {
    featureCards.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = Math.min(index, featureCards.value.length - 1)
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

// 獲取圖標SVG代碼
const getIconSvg = (iconType: string): string => {
  switch (iconType) {
    case 'play-circle':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-play-circle" viewBox="0 0 16 16">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
        <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
      </svg>`;
    case 'bar-chart':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bar-chart" viewBox="0 0 16 16">
        <path d="M4 11H2v3h2v-3zm5-4H7v7h2V7zm5-5v12h-2V2h2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1h-2zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-3z"/>
      </svg>`;
    case 'graph-up':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-graph-up" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
      </svg>`;
    case 'star':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-star" viewBox="0 0 16 16">
        <path d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.522-3.356c.33-.314.16-.888-.282-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288L8 2.223l1.847 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.565.565 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z"/>
      </svg>`;
    case 'heart':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-heart" viewBox="0 0 16 16">
        <path m="8 2.748-.717-.737C5.6.281 2.514.878 1.4 3.053c-.523 1.023-.641 2.5.314 4.385.92 1.815 2.834 3.989 6.286 6.357 3.452-2.368 5.365-4.542 6.286-6.357.955-1.886.838-3.362.314-4.385C13.486.878 10.4.28 8.717 2.01L8 2.748zM8 15C-7.333 4.868 3.279-3.04 7.824 1.143c.06.055.119.112.176.171a3.12 3.12 0 0 1 .176-.17C12.72-3.042 23.333 4.867 8 15z"/>
      </svg>`;
    case 'lightning':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-lightning" viewBox="0 0 16 16">
        <path d="M5.52.359A.5.5 0 0 1 6 0h4a.5.5 0 0 1 .474.658L8.694 6H12.5a.5.5 0 0 1 .395.807l-7 9a.5.5 0 0 1-.873-.454L6.823 9.5H3.5a.5.5 0 0 1-.48-.641l2.5-8.5zM6.374 1 4.168 8.5H7.5a.5.5 0 0 1 .478.647L6.78 13.04 11.478 7H8a.5.5 0 0 1-.474-.658L9.306 1H6.374z"/>
      </svg>`;
    case 'tools':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-tools" viewBox="0 0 16 16">
        <path d="M1 0 0 1l2.2 3.081a1 1 0 0 0 .815.419h.07a1 1 0 0 1 .708.293l2.675 2.675-2.617 2.654A3.003 3.003 0 0 0 0 13a3 3 0 1 0 5.878-.851l2.654-2.617.968.968-.305.914a1 1 0 0 0 .242 1.023l3.356 3.356a1 1 0 0 0 1.414 0l1.586-1.586a1 1 0 0 0 0-1.414l-3.356-3.356a1 1 0 0 0-1.023-.242L10.5 9.5l-.96-.96 2.68-2.643A3.005 3.005 0 0 0 16 3c0-.269-.035-.53-.102-.777l-2.14 2.141L12 4l-.364-1.757L13.777.102a3 3 0 0 0-3.675 3.68L7.462 6.46 4.793 3.793a1 1 0 0 1-.293-.707v-.071a1 1 0 0 0-.419-.814L1 0zm9.646 10.646a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708zM3 11l.471.242.529.026.287.445.445.287.026.529L5 13l-.242.471-.026.529-.445.287-.287.445-.529.026L3 15l-.471-.242L2 14.732l-.287-.445L1.268 14l-.026-.529L1 13l.242-.471.026-.529.445-.287.287-.445.529-.026L3 11z"/>
      </svg>`;
    case 'gear':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-gear" viewBox="0 0 16 16">
        <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
        <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
      </svg>`;
    case 'phone':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-phone" viewBox="0 0 16 16">
        <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
        <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
      </svg>`;
    default:
      return `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bookmark" viewBox="0 0 16 16">
        <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
      </svg>`;
  }
}

// 獲取文本顏色類
const getTextColorClass = () => {
  return bgColor.value === 'light' ? 'text-dark' : 'text-white'
}

// 獲取次要文本顏色類
const getSecondaryTextColorClass = () => {
  return bgColor.value === 'light' ? 'text-dark-50' : 'text-white-50'
}

// 準備最終的HTML
const prepareFeatureCardsHTML = (): string => {
  try {
    // 使用與原始結構相似的HTML結構，但根據編輯的內容更新
    const textColorClass = getTextColorClass()
    const secondaryTextColorClass = getSecondaryTextColorClass()
    
    // 生成卡片HTML
    let cardsHtml = ''
    featureCards.value.forEach(card => {
      cardsHtml += `
      <div class="mb-4 col-md-4">
        <div class="text-center feature-card">
          <div class="mb-3">
            ${getIconSvg(card.icon)}
          </div>
          <h3>${card.title}</h3>
          <p class="${secondaryTextColorClass}">${card.description}</p>
        </div>
      </div>`
    })
    
    // 完整HTML結構
    return `
<div class="py-5 ${textColorClass} bg-${bgColor.value}" data-bs-component="feature-cards">
  <div class="container">
    <div class="row">
      ${cardsHtml}
    </div>
  </div>
</div>`
  } catch (error) {
    console.error('生成特性卡片HTML時出錯:', error)
    // 出錯時返回原始HTML
    return originalHtml.value || ''
  }
}

// 應用更改
const applyChanges = () => {
  try {
    const html = prepareFeatureCardsHTML()
    
    // 發出更新事件
    emit('update-block', { html })
    
    // 重置更改狀態
    isChanged.value = false
  } catch (error) {
    console.error('應用特性卡片更改時出錯:', error)
  }
}

// 監聽並更新樣式變化
watch(() => localStyles.value, (newStyles) => {
  emit('update-styles', newStyles)
}, { deep: true })

// 更新樣式
const updateStyles = (styles: any) => {
  localStyles.value = styles
  markAsChanged()
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.card-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.card-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.card-item-actions {
  display: flex;
  gap: 5px;
}

.card-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 