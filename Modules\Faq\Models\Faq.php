<?php

namespace Modules\Faq\Models;

use Modules\Common\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 常见问题模型
 * 对应数据库表：tvb_faq
 */
class Faq extends BaseModel
{
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'faq';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'code',           // 问题代码
        'category_id',    // 分类ID
        'title',          // 问题标题
        'content',        // 问题内容
        'lang',           // 语言
        'status',         // 状态: 0-草稿, 1-已发布, 2-已下架
        'sort_order',     // 排序顺序
        'created_by',     // 创建人ID
        'updated_by',     // 更新人ID
        'is_deleted',     // 是否删除: 0-未删除, 1-已删除
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at' => 'datetime Y-m-d H:i:s',
        'updated_at' => 'datetime Y-m-d H:i:s',
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $casts = [
        'is_deleted' => 'integer',
        'status' => 'integer',
        'sort_order' => 'integer',
        'category_id' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
    ];

    /**
     * 分类关联关系
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        // 关联常见问题分类表
        return $this->belongsTo(FaqCategory::class, 'category_id', 'id');
    }

    /**
     * SEO关联关系
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function seo()
    {
        return $this->hasOne(FaqSeo::class, 'faq_id', 'id');
    }

    /**
     * 标签关联关系（多对多）
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function tags()
    {
        return $this->belongsToMany(
            \Modules\Tags\Models\Tag::class,
            'faq_tags',
            'faq_id',
            'tag_id',
            'id',
            'id'
        )->withTimestamps();
    }

    /**
     * FAQ标签关联关系
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function faqTags()
    {
        return $this->hasMany(FaqTag::class, 'faq_id', 'id');
    }

    /**
     * 查询已发布的常见问题作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        // 只查询已发布状态
        return $query->where('status', 1);
    }

    /**
     * 查询草稿状态的常见问题作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDraft($query)
    {
        // 只查询草稿状态
        return $query->where('status', 0);
    }

    /**
     * 查询已下架的常见问题作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeArchived($query)
    {
        // 只查询已下架状态
        return $query->where('status', 2);
    }

    /**
     * 查询未删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('is_deleted', 0);
    }

    /**
     * 查询已删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDeleted($query)
    {
        return $query->where('is_deleted', 1);
    }
}
