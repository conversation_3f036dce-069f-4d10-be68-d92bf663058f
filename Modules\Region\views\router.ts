import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/region',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '區域內容管理', icon: 'datareport' },
        children: [
            {
                path: 'list',
                name: 'RegionList',
                meta: { title: '區域管理' },
                component: () => import('./ui/list.vue'),
            },
            {
                path: 'edit/:id',
                name: 'RegionEdit',
                meta: { title: '區域編輯' },
                component: () => import('./ui/components/regionEdit.vue'),
            },
            {
                path: 'channel-list',
                name: 'ChannelList',
                meta: { title: '頻道管理' },
                component: () => import('./ui/ChannelList.vue'),
            },
        ]
    }
]

export default router
