@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .product-info {
    margin-bottom: 50px;
    .df();

    .pic {
      width: 40%;
    }
  
    .name-price {
      padding-left: 60px;
      padding-top: 30px;
      width: 60%;

      .df(flex-start, space-between, column);

      .price {
        .product-name {
          margin-bottom: 40px;
          font-size: 28px;
          font-weight: bold;
          line-height: 1.4;
          color: #333;
        }
    
        .product-price {
          color: #ff9600;
          font-size: 24px;
          line-height: 1.3;
        }
      }

      .buy-btn {
        margin-bottom: 20px;
        .btn-radius(0, 20px, 40px, #fff, #FF9600, #FCB319);
      }
    }
  }

  .product-nav {
    border-bottom: 1px solid #EFEFEF;
    background-color: #fff;

    .tab-nav {
      margin-bottom: -1px;
      .df();

      li {
        border-bottom: 3px solid #ff9600;
        padding: 20px 0;
        cursor: pointer;
        color: #ff9600;
        font-size: 18px;
        line-height: 1.33;
      }
    }
  }

  .product-details {
    padding: 30px 0 50px;
    background-color: #fff;
  }

  .product-list {
    padding-top: 50px;
    padding-bottom: 90px;
    position: relative;

    .tit {
      margin-bottom: 30px;
      line-height: 1.11;
      color: #333;
      font-size: 18px;
      .df(stretch);

      &::before {
        margin-right: 15px;
        content: '';
        display: block;
        width: 5px;
        background-color: #ff9600;
      }
    }

    .swiper-container {
      .swiper-slide {
        padding: 30px;
        background-color: #fff;

        &:hover {
          box-shadow: 0 5px 20px 5px rgba(0,0,0,.05);
          transition: box-shadow .3s ease-in-out;

          .text-box {
            h6 {
              color: #ff9600;
            }
          }
        }

        .text-box {
          margin-top: 14px;

          h6 {
            font-size: 16px;
            line-height: 1.3;
            color: #6E6E6E;
            transition: color .35s ease-in-out;
          }

          .price-views {
            margin-top: 20px;
            .df(center, space-between);

            .price {
              color: #ff9600;
              font-size: 20px;
              font-weight: bold;
            }

            .views-num {
              font-size: 14px;
              color: #888;

              .iconfont {
                margin-right: 5px;
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    .swiper-pagination {
      bottom: 50px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}