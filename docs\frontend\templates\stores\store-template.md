# Store 模板

## 概述

Store 用于管理前端应用的状态，实现状态的集中管理和共享。本文档提供了 Store 的标准模板和最佳实践。

## 基本结构

### Store 定义

```typescript
// stores/counter.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCounterStore = defineStore('counter', () => {
  // 状态
  const count = ref(0)
  const loading = ref(false)
  
  // 计算属性
  const doubleCount = computed(() => count.value * 2)
  const isPositive = computed(() => count.value > 0)
  
  // 方法
  const increment = () => {
    count.value++
  }
  
  const decrement = () => {
    count.value--
  }
  
  const reset = () => {
    count.value = 0
  }
  
  const incrementAsync = async () => {
    try {
      loading.value = true
      await new Promise(resolve => setTimeout(resolve, 1000))
      increment()
    } finally {
      loading.value = false
    }
  }
  
  return {
    // 状态
    count,
    loading,
    
    // 计算属性
    doubleCount,
    isPositive,
    
    // 方法
    increment,
    decrement,
    reset,
    incrementAsync
  }
})
```

### 列表 Store

```typescript
// stores/list.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ListItem, ListParams, ListResponse } from '@/types'
import Api from '@/services/api'

export const useListStore = defineStore('list', () => {
  // 状态
  const list = ref<ListItem[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // 计算属性
  const isEmpty = computed(() => list.value.length === 0)
  const isLastPage = computed(() => {
    const totalPages = Math.ceil(total.value / pageSize.value)
    return currentPage.value >= totalPages
  })
  
  // 方法
  const getList = async (params: ListParams): Promise<ListResponse> => {
    try {
      loading.value = true
      const response = await Api.getList(params)
      list.value = response.data
      total.value = response.total
      return response
    } finally {
      loading.value = false
    }
  }
  
  const loadMore = async () => {
    if (loading.value || isLastPage.value) {
      return
    }
    
    currentPage.value++
    const response = await getList({
      page: currentPage.value,
      pageSize: pageSize.value
    })
    
    list.value = [...list.value, ...response.data]
  }
  
  const refresh = async () => {
    currentPage.value = 1
    await getList({
      page: currentPage.value,
      pageSize: pageSize.value
    })
  }
  
  const reset = () => {
    list.value = []
    total.value = 0
    currentPage.value = 1
    pageSize.value = 10
  }
  
  return {
    // 状态
    list,
    total,
    loading,
    currentPage,
    pageSize,
    
    // 计算属性
    isEmpty,
    isLastPage,
    
    // 方法
    getList,
    loadMore,
    refresh,
    reset
  }
})
```

### 表单 Store

```typescript
// stores/form.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { FormData } from '@/types'
import Api from '@/services/api'

export const useFormStore = defineStore('form', () => {
  // 状态
  const formData = ref<FormData>({} as FormData)
  const loading = ref(false)
  const saving = ref(false)
  
  // 计算属性
  const isNew = computed(() => !formData.value.id)
  const isDirty = computed(() => Object.keys(formData.value).length > 0)
  
  // 方法
  const getDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await Api.getDetail(id)
      formData.value = response.data
    } finally {
      loading.value = false
    }
  }
  
  const save = async () => {
    try {
      saving.value = true
      if (isNew.value) {
        await Api.create(formData.value)
      } else {
        await Api.update(formData.value.id, formData.value)
      }
    } finally {
      saving.value = false
    }
  }
  
  const reset = () => {
    formData.value = {} as FormData
  }
  
  return {
    // 状态
    formData,
    loading,
    saving,
    
    // 计算属性
    isNew,
    isDirty,
    
    // 方法
    getDetail,
    save,
    reset
  }
})
```

## 规范要求

1. Store 结构
   - 状态定义
   - 计算属性
   - 方法实现
   - 类型声明

2. 状态管理
   - 响应式数据
   - 计算属性
   - 异步操作
   - 状态重置

3. 数据处理
   - 数据加载
   - 数据缓存
   - 数据更新
   - 数据重置

4. 错误处理
   - 加载状态
   - 错误状态
   - 异常处理
   - 状态恢复

## 最佳实践

1. 持久化
```typescript
// 使用 pinia-plugin-persistedstate
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 在 store 中配置
export const useUserStore = defineStore('user', () => {
  // store 实现...
}, {
  persist: {
    key: 'user',
    storage: localStorage,
    paths: ['token', 'userInfo']
  }
})
```

2. 订阅状态
```typescript
// 在组件中订阅状态变化
const store = useCounterStore()

watch(
  () => store.count,
  (newValue, oldValue) => {
    console.log(`count changed from ${oldValue} to ${newValue}`)
  }
)

// 在 store 中订阅状态变化
store.$subscribe((mutation, state) => {
  console.log('store changed:', mutation.type, mutation.payload)
})
```

## 常见问题

1. 状态重置
```typescript
// 好的实践 - 提供重置方法
const useStore = defineStore('store', () => {
  const state = ref(initialState)
  
  const reset = () => {
    state.value = initialState
  }
  
  return { state, reset }
})

// 不好的实践 - 直接修改状态
const store = useStore()
store.state = initialState
```

2. 模块化
```typescript
// 好的实践 - 按功能拆分
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  // 用户相关状态和方法
})

// stores/settings.ts
export const useSettingsStore = defineStore('settings', () => {
  // 设置相关状态和方法
})

// 不好的实践 - 所有状态放在一起
export const useStore = defineStore('store', () => {
  // 混合了用户、设置等多个模块的状态
})
```

## 注意事项

1. 合理拆分 Store 模块
2. 避免状态冗余
3. 及时清理无用状态
4. 处理好异步操作
5. 实现状态持久化
6. 注意性能优化
