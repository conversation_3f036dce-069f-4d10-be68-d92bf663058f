<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本处理助手</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 0;
            line-height: 1.6;
            background-color: #f0f2f5;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #fff;
        }
        .chat-header {
            background-color: #133c81;
            padding: 15px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .chat-header h2 {
            margin: 0;
            margin-left: 10px;
            font-size: 18px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
        }
        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        .message.user {
            justify-content: flex-end;
        }
        .message .avatar {
            margin-right: 10px;
        }
        .message.user .avatar {
            margin-left: 10px;
            margin-right: 0;
        }
        .message-content {
            max-width: 70%;
            padding: 10px 12px;
            border-radius: 10px;
            position: relative;
        }
        .user .message-content {
            background-color: #e1f0ff;
            color: #000;
            border-bottom-right-radius: 0;
        }
        .ai .message-content {
            background-color: #f0f0f0;
            border-bottom-left-radius: 0;
        }
        .buttons-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
            margin-left: 50px;
        }
        .action-button {
            padding: 8px 15px;
            background-color: #dce6f1;
            border: 1px solid #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
        }
        .action-button:hover {
            background-color: #c5d3e3;
        }
        .chat-input {
            display: flex;
            padding: 10px;
            border-top: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            margin-right: 10px;
        }
        .chat-input button {
            background-color: transparent;
            border: none;
            padding: 0;
            cursor: pointer;
        }
        .chat-input button img {
            width: 36px;
            height: 36px;
        }
        .special-button {
            background-color: #4a90e2;
            color: white;
            border: none;
        }
        .special-button:hover {
            background-color: #3a80d2;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin: 10px 0 10px 50px;
            border-radius: 10px;
            cursor: pointer;
            max-width: 70%;
        }
        .upload-area.dragover {
            border-color: #4a90e2;
            background-color: rgba(74, 144, 226, 0.1);
        }
        .file-list {
            margin: 10px 0 10px 50px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 8px;
            max-width: 70%;
        }
        .file-item {
            margin-bottom: 5px;
        }
        .replace-form {
            margin: 10px 0 10px 50px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            max-width: 70%;
        }
        .replace-row {
            display: flex;
            margin-bottom: 10px;
            gap: 10px;
        }
        .replace-row input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            flex: 1;
        }
        .row-buttons {
            display: flex;
            gap: 5px;
        }
        .add-rule-btn {
            display: block;
            margin-top: 10px;
            padding: 5px 10px;
            border: none;
            background-color: #4a90e2;
            color: white;
            border-radius: 5px;
            cursor: pointer;
        }
        .form-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .summary-container {
            margin: 10px 0 10px 50px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            max-width: 70%;
        }
        .summary-detail {
            margin-bottom: 10px;
        }
        .loading {
            margin: 10px 0 10px 50px;
            color: #666;
            font-style: italic;
            max-width: 70%;
        }
        .hidden {
            display: none;
        }
        .print-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .print-button:hover {
            background-color: #3a80d2;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="avatar">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="阿博">
            </div>
            <h2>阿博</h2>
            <button id="printJsonButton" class="print-button">打印聊天记录</button>
        </div>
        <div class="chat-messages" id="chatMessages"></div>
        <div class="chat-input">
            <input type="text" id="userInput" placeholder="请输入您的问题...">
            <button id="sendButton">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTUgMTJIMTkiIHN0cm9rZT0iIzEzM2M4MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEyIDUMMTkgMTJMMTIgMTkiIHN0cm9rZT0iIzEzM2M4MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==" alt="Send">
            </button>
        </div>
    </div>
    <script src="index.js"></script>
</body>
</html> 