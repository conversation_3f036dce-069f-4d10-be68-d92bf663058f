# Region 模块 API 测试示例

## 多语言支持

Region 模块支持多语言，通过 `lang` 参数指定语言：

- `lang=en` - 英文
- `lang=zh_cn` - 中文简体  
- `lang=zh_hk` - 中文繁体

## API 测试示例

### 1. 区域管理 API

#### 获取区域列表（中文简体）
```bash
curl -X GET "http://localhost:8000/api/region?lang=zh_cn" \
  -H "Content-Type: application/json"
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 3,
    "page": 1,
    "limit": 20,
    "items": [
      {
        "id": 1,
        "name": "突发新闻",
        "description": "突发新闻专区",
        "status": 1,
        "sort": 1,
        "created_at": "2025-07-24 11:30:00",
        "updated_at": "2025-07-24 11:30:00"
      }
    ]
  }
}
```

#### 获取区域列表（英文）
```bash
curl -X GET "http://localhost:8000/api/region?lang=en" \
  -H "Content-Type: application/json"
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Operation successful",
  "data": {
    "total": 3,
    "page": 1,
    "limit": 20,
    "items": [
      {
        "id": 1,
        "name": "Breaking News",
        "description": "Breaking news section",
        "status": 1,
        "sort": 1,
        "created_at": "2025-07-24 11:30:00",
        "updated_at": "2025-07-24 11:30:00"
      }
    ]
  }
}
```

#### 创建区域（中文繁体）
```bash
curl -X POST "http://localhost:8000/api/region?lang=zh_hk" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "港澳地區",
    "description": "港澳地區新聞",
    "status": 1,
    "sort": 2
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "message": "創建成功",
  "data": {
    "id": 4,
    "name": "港澳地區",
    "description": "港澳地區新聞",
    "status": 1,
    "sort": 2,
    "created_at": "2025-07-24 11:30:00",
    "updated_at": "2025-07-24 11:30:00"
  }
}
```

### 2. 频道管理 API

#### 获取频道列表（中文简体）
```bash
curl -X GET "http://localhost:8000/api/channel?lang=zh_cn" \
  -H "Content-Type: application/json"
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 2,
    "page": 1,
    "limit": 20,
    "items": [
      {
        "id": 1,
        "name": "新闻频道",
        "description": "24小时新闻频道",
        "status": 1,
        "sort": 1,
        "url": "https://news.example.com",
        "cover_img": "https://news.example.com/cover.jpg",
        "region_num": 3,
        "created_at": "2025-07-24 11:30:00",
        "updated_at": "2025-07-24 11:30:00"
      }
    ]
  }
}
```

#### 创建频道（英文）
```bash
curl -X POST "http://localhost:8000/api/channel?lang=en" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Sports Channel",
    "description": "24-hour sports channel",
    "status": 1,
    "sort": 2,
    "url": "https://sports.example.com",
    "cover_img": "https://sports.example.com/cover.jpg",
    "region_ids": [1, 2, 3]
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Created successfully",
  "data": {
    "id": 3,
    "name": "Sports Channel",
    "description": "24-hour sports channel",
    "status": 1,
    "sort": 2,
    "url": "https://sports.example.com",
    "cover_img": "https://sports.example.com/cover.jpg",
    "created_at": "2025-07-24 11:30:00",
    "updated_at": "2025-07-24 11:30:00",
    "regions": [
      {
        "id": 1,
        "name": "Breaking News",
        "pivot": {
          "channel_id": "3",
          "regions_id": "1",
          "created_at": "2025-07-24 11:30:00",
          "updated_at": "2025-07-24 11:30:00"
        }
      }
    ]
  }
}
```

### 3. 错误处理示例

#### 验证错误（中文简体）
```bash
curl -X POST "http://localhost:8000/api/region?lang=zh_cn" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "status": 5
  }'
```

**响应示例：**
```json
{
  "code": 15600,
  "message": "验证失败",
  "errors": {
    "name": ["名称不能为空"],
    "status": ["状态值无效"]
  }
}
```

#### 验证错误（英文）
```bash
curl -X POST "http://localhost:8000/api/region?lang=en" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "status": 5
  }'
```

**响应示例：**
```json
{
  "code": 15600,
  "message": "Validation failed",
  "errors": {
    "name": ["Name cannot be empty"],
    "status": ["Invalid status value"]
  }
}
```

## 错误码说明

### 区域模块错误码 (15000-15999)

- `15000` - 区域不存在
- `15100` - 创建区域失败
- `15200` - 更新区域失败
- `15300` - 删除区域失败
- `15400` - 查询区域失败
- `15500` - 区域状态无效
- `15600` - 区域验证失败

### 频道模块错误码 (16000-16999)

- `16000` - 频道不存在
- `16100` - 创建频道失败
- `16200` - 更新频道失败
- `16300` - 删除频道失败
- `16400` - 查询频道失败
- `16500` - 频道状态无效
- `16600` - 频道验证失败

## 语言文件结构

```
Modules/Region/Lang/
├── en/
│   └── region.php          # 英文语言文件
├── zh_CN/
│   └── region.php          # 中文简体语言文件
└── zh_HK/
    └── region.php          # 中文繁体语言文件
```

## 中间件配置

语言中间件会自动处理 `lang` 参数：

- 支持的语言：`en`, `zh_cn`, `zh_hk`
- 默认语言：`zh_cn`
- 参数位置：URL 查询参数 `?lang=zh_cn` 