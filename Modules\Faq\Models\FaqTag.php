<?php

namespace Modules\Faq\Models;

use Modules\Common\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 常见问题标签关联模型
 * 对应数据库表: tvb_faq_tags
 */
class FaqTag extends BaseModel
{
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'faq_tags';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'faq_id',        // 常见问题ID
        'tag_id',        // 标签ID
        'is_deleted',    // 是否删除: 0-未删除, 1-已删除
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $casts = [
        'faq_id' => 'integer',
        'tag_id' => 'integer',
        'is_deleted' => 'integer',
    ];

    /**
     * 关联到常见问题
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function faq()
    {
        return $this->belongsTo(Faq::class, 'faq_id', 'id');
    }

    /**
     * 关联到标签
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tag()
    {
        return $this->belongsTo(\Modules\Tags\Models\Tag::class, 'tag_id', 'id');
    }

    /**
     * 查询未删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('is_deleted', 0);
    }

    /**
     * 查询已删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDeleted($query)
    {
        return $query->where('is_deleted', 1);
    }
} 