<template>
  <div class="dashboard-statistics" v-if="showLeftPanel || showRightPanel">
    <!-- 最新情況/實用工具  -->
    <el-row :gutter="20">
      <!-- 最新情況 (50%) -->
      <el-col :span="showRightPanel ? 12 : 24" v-if="showLeftPanel">
        <div class="news-panel ">
          <div class="panel-header">
            <div class="panel-title">
              <span class="title-text">最新情況</span>
            </div>
            <el-button type="text" link class="close-btn" @click="closeLeftPanel">
              <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
            </el-button>
          </div>
          
          <div class="news-content" :class="{ 'single-row-layout': !showRightPanel }">
            <!-- 当实用工具关闭时显示Dashboard样式的一行布局 -->
            <template v-if="!showRightPanel">
              <div class="dashboard-section">
                <div class="stats-cards">
                  <el-row :gutter="20">
                    <el-col :xs="6" :sm="6" :md="6" :lg="6" v-for="(item, index) in newsItems" :key="index">
                      <div class="dashboard-card" @click="handleNewsClick(item, index)">
                        <div class="card-header">
                          <div class="card-title">{{ item.title }}</div>
                        </div>
                        <div class="card-content">
                          <div class="icon-wrapper">
                            <el-icon size="18" color="#007EE5">
                              <img :src="$asset(item.icon)" class="icons-img" />
                            </el-icon>
                          </div>
                          <div class="number">
                            <span class="value">{{ item.value }}</span>
                            <span class="unit">{{ item.unit }}</span>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </template>
            
            <!-- 当实用工具显示时保持上下布局 -->
            <template v-else>
              <!-- 上半部分 -->
              <div class="news-section top-section">
                <el-row :gutter="16">
                  <el-col :xs="12" :sm="12" :md="12" :lg="12" v-for="(item, index) in topNewsItems" :key="index">
                    <div class="news-card" @click="handleNewsClick(item, index)">
                      <div class="card-header">
                        <div class="card-title">{{ item.title }}</div>
                      </div>
                      <div class="card-content">
                        <div class="icon-wrapper">
                          <el-icon size="18" color="#007EE5">
                            <img :src="$asset(item.icon)" class="icons-img" />
                          </el-icon>
                        </div>
                        <div class="number">
                          <span class="value">{{ item.value }}</span>
                          <span class="unit">{{ item.unit }}</span>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              
              <!-- 下半部分 -->
              <div class="news-section bottom-section">
                <el-row :gutter="16">
                  <el-col :xs="12" :sm="12" :md="12" :lg="12" v-for="(item, index) in bottomNewsItems" :key="index + 2">
                    <div class="news-card" @click="handleNewsClick(item, index + 2)">
                      <div class="card-header">
                        <div class="card-title">{{ item.title }}</div>
                      </div>
                      <div class="card-content">
                        <div class="icon-wrapper">
                          <el-icon size="18" color="#007EE5">
                            <img :src="$asset(item.icon)" class="icons-img" />
                          </el-icon>
                        </div>
                        <div class="number">
                          <span class="value">{{ item.value }}</span>
                          <span class="unit">{{ item.unit }}</span>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </template>
          </div>
        </div>
      </el-col>

      <!-- 實用工具 (50%) -->
      <el-col :span="showLeftPanel ? 12 : 24" v-if="showRightPanel">
        <div class="tools-panel">
          <div class="panel-header">
            <div class="panel-title">
              <span class="title-text">實用工具</span>
            </div>
            <el-button type="text" link class="close-btn" @click="closeRightPanel">
              <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
            </el-button>
          </div>
          
          <div class="tools-content" :style="{height: showLeftPanel ? '254px' : '100%'}">
            <el-row :gutter="16">
              <el-col 
                v-for="tool in toolsList" 
                :key="tool.id" 
                :xs="showLeftPanel ? 12 : 4"
                :sm="showLeftPanel ? 12 : 4"
                :md="showLeftPanel ? 12 : 4"
                :lg="showLeftPanel ? 12 : 4"
                class="tool-column"
                :style="{marginBottom: showLeftPanel ? '7px' : '0px'}"
              >
                <div class="tool-card" @click="handleToolClick(tool)">
                  <div class="icon-wrapper">
                    <img :src="$asset(tool.icon)" :alt="tool.title" />
                  </div>
                  <div class="title">{{ tool.title }}</div>
                </div>
              </el-col>

              <!-- 骨架屏 -->
              <template v-if="toolsLoading && toolsList.length === 0">
                <el-col 
                  v-for="index in 6" 
                  :key="'skeleton-' + index"
                  :xs="showLeftPanel ? 12 : 4"
                  :sm="showLeftPanel ? 12 : 4"
                  :md="showLeftPanel ? 12 : 4"
                  :lg="showLeftPanel ? 12 : 4"
                  class="tool-column"
                >
                  <div class="tool-card skeleton">
                    <div class="icon-wrapper"></div>
                    <div class="title"></div>
                  </div>
                </el-col>
              </template>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'
import { Document, Bell, Monitor, Warning, Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'


const { t } = useI18n()
const router = useRouter()

const loading = ref(true)

// 定义emits
const emit = defineEmits(['close'])

// 面板显示控制
const showLeftPanel = ref(true)
const showRightPanel = ref(true)

// 安全检测动态数据
const securityDetectionCount = ref(3)

// 最新情况数据
const newsItems = ref([
  {
    title: '待審核文章',
    value: 5,
    unit: '篇',
    icon: 'Dashboard/Asset/ionic-ios-paper.png',
    route: '/cms/cmsList?model_id=1&active_tab=awaiting'
  },
  {
    title: '待處理消息',
    value: 4,
    unit: '條', 
    icon: 'Dashboard/Asset/ionic-ios-notifications.png',
    route: '/message/messageList'
  },
  {
    title: '待審核頁面',
    value: 3,
    unit: '頁',
    icon: 'Dashboard/Asset/ionic-md-laptop.png',
    route: '/cms/cmsList?model_id=5&active_tab=awaiting'
  },
  {
    title: '今日新增查詢',
    value: computed(() => securityDetectionCount.value),
    unit: '次',
    icon: 'Dashboard/Asset/open-shield.png',
    route: '/auditlog/logs'
  }
])

// 分割新闻项目为上下两部分
const topNewsItems = computed(() => newsItems.value.slice(0, 2))
const bottomNewsItems = computed(() => newsItems.value.slice(2, 4))

// 工具列表数据
const toolsList = ref<any>([])
const toolsLoading = ref(true)

let securityTimer: number | null = null

// 处理标题文本
const processTitle = (title: string): string => {
  // 如果标题长度小于等于4个字符，添加换行
  if (title.length <= 4) {
    return title + '\n'
  }
  return title
}

// 获取工具列表数据
const getTools = async () => {
  toolsLoading.value = true
  try {
    const res = await http.get('/dashboard/toolsSection/data')
    // 处理数据，取前6个
    toolsList.value = res.data.data.slice(0, 6).map((tool: any) => ({
      ...tool,
      title: processTitle(tool.title),
      link: tool.path // 保持与原有代码兼容
    }))
  } catch (error) {
  } finally {
    toolsLoading.value = false
  }
}

// 启动安全检测数据动态变化
const startSecurityAnimation = () => {
  securityTimer = setInterval(() => {
    // 生成1-5之间的随机数
    securityDetectionCount.value = Math.floor(Math.random() * 5) + 1
  }, 3000) // 3秒更换一次
}

// 清理定时器
const clearAllTimers = () => {
  if (securityTimer) {
    clearInterval(securityTimer)
    securityTimer = null
  }
}

// 关闭面板
const closeLeftPanel = () => {
  showLeftPanel.value = false
  // 如果两个面板都关闭了，则关闭整个组件
  if (!showLeftPanel.value && !showRightPanel.value) {
    emit('close')
  }
}

const closeRightPanel = async () => {
  showRightPanel.value = false
  // 如果两个面板都关闭了，则关闭整个组件
  if (!showLeftPanel.value && !showRightPanel.value) {
    emit('close')
  }
  // 当右侧面板关闭后，左侧面板会自动变为一行布局（通过模板中的条件渲染实现）
}

// 处理最新情况点击
const handleNewsClick = (item: any, index: number) => {
  if (item.route) {
    router.push(item.route)
  }
}

// 处理工具点击
const handleToolClick = (tool: any) => {
  if (tool.path) {
    if (tool.path.startsWith('http')) {
      window.open(tool.path, '_blank')
    } else {
      router.push(tool.path)
    }
  }
}

onMounted(async () => {
  // 启动安全检测动态数据
  startSecurityAnimation()
  
  // 获取工具列表数据
  await getTools()
})

onUnmounted(() => {
  clearAllTimers()
})
</script>

<script lang="ts">
export default {
  name: 'ToolsTitle',
}
</script>

<style lang="scss" scoped>
.dashboard-statistics {
  .news-panel,
  .tools-panel {
    height: 100%;
    display: flex;
    flex-direction: column;

    .panel-header {
      margin-top: 25px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #232323;
      font-weight: bold;
      font-size: 18px;
      line-height: 24px;

      .panel-title {
        .title-text {
          line-height: 24px;
        }
      }
    }
  }

  .news-panel {
    .news-content {
      height: 254px;
      background-color: #fff;
      box-shadow: 0px 3px 6px #00000029;
      padding: 26px 27px 21px 27px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;

      // Dashboard布局样式
      &.single-row-layout {
        height: auto;
        padding: 0;
        background: transparent;
        box-shadow: none;
        border-radius: 0;
        
        .dashboard-section {
          flex: 1;
          
          .stats-cards {
            width: 100%;
            
            .el-row {
              margin: 0 !important;
              background-color: #fff;
              box-shadow: 0px 3px 6px #00000029;
              border-radius: 10px;
              padding: 0;
            }
          }
          
          .dashboard-card {
            padding: 20px 27px;
            cursor: pointer;
            height: 100%;
            position: relative;
            
            &::after{
              content: '';
              position: absolute;
              top: 22px;
              right: 0;
              width: 1px;
              height: 77px;
              background-color: #707070;
              opacity: 0.16;
            }

            &:last-child::after {
              display: none;
            }

            &:hover {
              transform: translateY(-2px);
              transition: transform 0.3s ease;
            }

            .card-header {
              margin-bottom: 14px;

              .card-title {
                font-size: 16px;
                color: #232323;
                font-weight: bold;
                line-height: 21px;
              }
            }

            .card-content {
              display: flex;
              align-items: center;
              gap: 16px;

              .icon-wrapper {
                border-radius: 50%;
                width: 45px;
                height: 45px;
                background-color: #E9EEF2;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;

                .el-icon {
                  font-size: 18px;
                  color: #007EE5;
                }

                .icons-img {
                  width: 100%;
                  height: 100%;
                }
              }

              .number {
                display: flex;
                align-items: baseline;
                gap: 10px;

                .value {
                  font-size: 37px;
                  font-weight: bold;
                  color: #007ee5;
                  line-height: 1.21;
                  font-family: Arial;
                }

                .unit {
                  font-size: 16px;
                  color: #000000;
                  line-height: 1;
                }
              }
            }
          }
        }
      }

      .news-section {
        flex: 1;
        
        &.top-section {
          border-bottom: 1px solid rgba(112, 112, 112, 0.16);
          padding-bottom: 16px;
          margin-bottom: 20px;
          
        }

        &.bottom-section {
          padding-top: 10px;
        }
      }

      .news-card {
        cursor: pointer;
        transition: transform 0.3s ease;
        position: relative;



        .card-header {
          margin-bottom: 14px;

          .card-title {
            font-size: 16px;
            color: #232323;
            font-weight: bold;
            line-height: 21px;
          }
        }

        .card-content {
          display: flex;
          align-items: center;
          gap: 16px;

          .icon-wrapper {
            border-radius: 50%;
            width: 45px;
            height: 45px;
            background-color: #E9EEF2;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-icon {
              font-size: 18px;
              color: #007EE5;
            }

            .icons-img {
              width: 100%;
              height: 100%;
            }
          }

          .number {
            display: flex;
            align-items: baseline;
            gap: 10px;

            .value {
              font-size: 37px;
              font-weight: bold;
              color: #007ee5;
              line-height: 42px;
              font-family: Arial;
            }

            .unit {
              font-size: 16px;
              color: #000000;
              line-height: 21px;
            }
          }
        }
      }
    }
  }

  .tools-panel {
    .tools-content {
      height: 254px;
      background-color: #fff;
      box-shadow: 0px 3px 6px #00000029;
      border-radius: 10px;
      padding: 21px 23px 21px 20px;

      .tool-column {
        padding-left: 5px !important;
        padding-right: 5px !important;
      }

      .tool-card {
        height: 66px;
        border: 2px solid #E2E2E2;
        border-radius: 5px;
        background-color: #fff;
        transition: transform 0.3s ease;
        cursor: pointer;
        
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;

        &:hover {
          transform: translateY(-2px);
        }

        .icon-wrapper {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
          }
        }

        .title {
          font-size: 14px;
          color: #19496a;
          text-align: center;
          line-height: 1.4375;
          white-space: pre-line;
          word-break: break-word;
        }

        &.skeleton {
          .icon-wrapper {
            background-color: #f5f7fa;
            border-radius: 4px;
          }

          .title {
            background-color: #f5f7fa;
            border-radius: 4px;
            width: 70%;
            height: 28px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 1200px) {
    .news-panel,
    .tools-panel {
      margin-bottom: 20px;
    }
  }
}
</style>
