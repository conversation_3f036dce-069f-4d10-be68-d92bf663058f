{"version": 3, "file": "drag_drop.js", "sources": ["../../../src/plugins/drag_drop/plugin.ts"], "sourcesContent": ["/**\n * Plugin: \"drag_drop\" (Tom Select)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tif (!$.fn.sortable) throw new Error('The \"drag_drop\" plugin requires jQuery UI \"sortable\".');\n\tif (self.settings.mode !== 'multi') return;\n\n\tvar orig_lock\t\t= self.lock;\n\tvar orig_unlock\t\t= self.unlock;\n\n\tself.hook('instead','lock',()=>{\n\t\tvar sortable = $(self.control).data('sortable');\n\t\tif (sortable) sortable.disable();\n\t\treturn orig_lock.call(self);\n\t});\n\n\tself.hook('instead','unlock',()=>{\n\t\tvar sortable = $(self.control).data('sortable');\n\t\tif (sortable) sortable.enable();\n\t\treturn orig_unlock.call(self);\n\t});\n\n\tself.on('initialize',()=>{\n\t\tvar $control = $(self.control).sortable({\n\t\t\titems: '[data-value]',\n\t\t\tforcePlaceholderSize: true,\n\t\t\tdisabled: self.isLocked,\n\t\t\tstart: (e, ui) => {\n\t\t\t\tui.placeholder.css('width', ui.helper.css('width'));\n\t\t\t\t$control.css({overflow: 'visible'});\n\t\t\t},\n\t\t\tstop: ()=>{\n\t\t\t\t$control.css({overflow: 'hidden'});\n\n\t\t\t\tvar values:string[] = [];\n\t\t\t\t$control.children('[data-value]').each(function(this:HTMLElement){\n\t\t\t\t\tif( this.dataset.value ) values.push(this.dataset.value);\n\t\t\t\t});\n\n\t\t\t\tself.setValue(values);\n\t\t\t}\n\t\t});\n\n\t});\n\n};\n"], "names": ["self", "$", "fn", "sortable", "Error", "settings", "mode", "orig_lock", "lock", "orig_unlock", "unlock", "hook", "control", "data", "disable", "call", "enable", "on", "$control", "items", "forcePlaceholderSize", "disabled", "isLocked", "start", "e", "ui", "placeholder", "css", "helper", "overflow", "stop", "values", "children", "each", "dataset", "value", "push", "setValue"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAIe,eAAyB,IAAA;CACvC,EAAIA,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,EAAA,IAAI,CAACC,CAAC,CAACC,EAAF,CAAKC,QAAV,EAAoB,MAAM,IAAIC,KAAJ,CAAU,uDAAV,CAAN,CAAA;CACpB,EAAA,IAAIJ,IAAI,CAACK,QAAL,CAAcC,IAAd,KAAuB,OAA3B,EAAoC,OAAA;CAEpC,EAAA,IAAIC,SAAS,GAAIP,IAAI,CAACQ,IAAtB,CAAA;CACA,EAAA,IAAIC,WAAW,GAAIT,IAAI,CAACU,MAAxB,CAAA;CAEAV,EAAAA,IAAI,CAACW,IAAL,CAAU,SAAV,EAAoB,MAApB,EAA2B,MAAI;CAC9B,IAAA,IAAIR,QAAQ,GAAGF,CAAC,CAACD,IAAI,CAACY,OAAN,CAAD,CAAgBC,IAAhB,CAAqB,UAArB,CAAf,CAAA;CACA,IAAA,IAAIV,QAAJ,EAAcA,QAAQ,CAACW,OAAT,EAAA,CAAA;CACd,IAAA,OAAOP,SAAS,CAACQ,IAAV,CAAef,IAAf,CAAP,CAAA;CACA,GAJD,CAAA,CAAA;CAMAA,EAAAA,IAAI,CAACW,IAAL,CAAU,SAAV,EAAoB,QAApB,EAA6B,MAAI;CAChC,IAAA,IAAIR,QAAQ,GAAGF,CAAC,CAACD,IAAI,CAACY,OAAN,CAAD,CAAgBC,IAAhB,CAAqB,UAArB,CAAf,CAAA;CACA,IAAA,IAAIV,QAAJ,EAAcA,QAAQ,CAACa,MAAT,EAAA,CAAA;CACd,IAAA,OAAOP,WAAW,CAACM,IAAZ,CAAiBf,IAAjB,CAAP,CAAA;CACA,GAJD,CAAA,CAAA;CAMAA,EAAAA,IAAI,CAACiB,EAAL,CAAQ,YAAR,EAAqB,MAAI;CACxB,IAAIC,IAAAA,QAAQ,GAAGjB,CAAC,CAACD,IAAI,CAACY,OAAN,CAAD,CAAgBT,QAAhB,CAAyB;CACvCgB,MAAAA,KAAK,EAAE,cADgC;CAEvCC,MAAAA,oBAAoB,EAAE,IAFiB;CAGvCC,MAAAA,QAAQ,EAAErB,IAAI,CAACsB,QAHwB;CAIvCC,MAAAA,KAAK,EAAE,CAACC,CAAD,EAAIC,EAAJ,KAAW;CACjBA,QAAAA,EAAE,CAACC,WAAH,CAAeC,GAAf,CAAmB,OAAnB,EAA4BF,EAAE,CAACG,MAAH,CAAUD,GAAV,CAAc,OAAd,CAA5B,CAAA,CAAA;CACAT,QAAAA,QAAQ,CAACS,GAAT,CAAa;CAACE,UAAAA,QAAQ,EAAE,SAAA;CAAX,SAAb,CAAA,CAAA;CACA,OAPsC;CAQvCC,MAAAA,IAAI,EAAE,MAAI;CACTZ,QAAAA,QAAQ,CAACS,GAAT,CAAa;CAACE,UAAAA,QAAQ,EAAE,QAAA;CAAX,SAAb,CAAA,CAAA;CAEA,QAAIE,IAAAA,MAAe,GAAG,EAAtB,CAAA;CACAb,QAAAA,QAAQ,CAACc,QAAT,CAAkB,cAAlB,CAAkCC,CAAAA,IAAlC,CAAuC,YAA0B;CAChE,UAAA,IAAI,IAAKC,CAAAA,OAAL,CAAaC,KAAjB,EAAyBJ,MAAM,CAACK,IAAP,CAAY,IAAA,CAAKF,OAAL,CAAaC,KAAzB,CAAA,CAAA;CACzB,SAFD,CAAA,CAAA;CAIAnC,QAAAA,IAAI,CAACqC,QAAL,CAAcN,MAAd,CAAA,CAAA;CACA,OAAA;CAjBsC,KAAzB,CAAf,CAAA;CAoBA,GArBD,CAAA,CAAA;CAuBA;;;;;;;;"}