# 领域服务模板

## 概述

领域服务用于处理跨实体的业务逻辑，实现领域层的核心业务规则。本文档提供了领域服务的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Services;

use Modules\YourModule\Domain\Entities\YourEntity;
use Modules\YourModule\Domain\Repositories\YourRepositoryInterface;
use Modules\YourModule\Domain\ValueObjects\YourValueObject;
use Modules\YourModule\Exceptions\YourException;
use Modules\YourModule\Enums\YourErrorCode;

final class YourDomainService
{
    public function __construct(
        private readonly YourRepositoryInterface $repository,
        private readonly AnotherDomainService $anotherService
    ) {
    }

    public function processBusinessLogic(YourEntity $entity, YourValueObject $value): void
    {
        // 验证业务规则
        $this->validateBusinessRules($entity);

        // 执行领域逻辑
        $entity->updateWithValue($value);
        
        // 调用仓储保存
        $this->repository->save($entity);
        
        // 触发领域事件
        event(new YourDomainEvent($entity));
    }

    private function validateBusinessRules(YourEntity $entity): void
    {
        if (!$this->isValid($entity)) {
            throw YourException::invalidEntity($entity->getId());
        }
    }

    private function isValid(YourEntity $entity): bool
    {
        // 实现具体的业务规则验证逻辑
        return true;
    }
}
```

## 规范要求

1. 命名规范
   - 类名使用 DomainService 后缀
   - 方法名应体现业务含义
   - 私有方法用于内部逻辑封装

2. 职责划分
   - 处理跨实体业务逻辑
   - 实现领域规则验证
   - 协调多个领域对象
   - 不处理技术细节

3. 依赖注入
   - 注入所需的仓储接口
   - 注入其他领域服务
   - 遵循依赖倒置原则
   - 避免服务循环依赖

4. 异常处理
   - 使用领域异常类
   - 异常信息要明确
   - 统一错误码管理
   - 合理的异常粒度

## 最佳实践

1. 领域事件处理
```php
namespace Modules\YourModule\Domain\Events;

class YourDomainEvent
{
    public function __construct(
        private readonly YourEntity $entity
    ) {
    }
}

// 在领域服务中触发事件
event(new YourDomainEvent($entity));
```

2. 值对象使用
```php
namespace Modules\YourModule\Domain\ValueObjects;

class Money
{
    public function __construct(
        private readonly float $amount,
        private readonly string $currency
    ) {
        $this->validate();
    }

    private function validate(): void
    {
        if ($this->amount < 0) {
            throw new InvalidArgumentException('金额不能为负');
        }
    }
}
```

3. 领域规则封装
```php
namespace Modules\YourModule\Domain\Rules;

class BusinessRule
{
    public function isSatisfiedBy(YourEntity $entity): bool
    {
        // 实现具体的业务规则判断逻辑
        return true;
    }
}
```

## 常见问题

1. 服务职责划分
```php
// 好的实践 - 单一职责
class OrderDomainService
{
    public function placeOrder(Order $order): void
    {
        $this->validateOrder($order);
        $this->calculateTotal($order);
        $this->checkInventory($order);
        $this->repository->save($order);
    }
}

// 不好的实践 - 职责混乱
class OrderService
{
    public function process(array $data)
    {
        // 混合了应用层、领域层、基础设施层的逻辑
    }
}
```

2. 领域事件使用
```php
// 好的实践
class OrderDomainService
{
    public function confirmOrder(Order $order): void
    {
        $order->confirm();
        $this->repository->save($order);
        event(new OrderConfirmedEvent($order));
    }
}

// 不好的实践
class OrderService
{
    public function confirm(Order $order): void
    {
        // 直接在服务中修改状态，没有领域事件
        $order->status = 'confirmed';
        $this->repository->save($order);
    }
}
```

## 注意事项

1. 保持领域服务的纯粹性
2. 避免在领域服务中处理技术细节
3. 合理使用值对象和领域事件
4. 确保业务规则的完整性
5. 注意服务间的依赖关系
6. 保持代码的可测试性
