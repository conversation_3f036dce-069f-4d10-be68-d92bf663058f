<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="1000" xmlns="http://www.w3.org/2000/svg" version="1.1">
  <defs>
    <style type="text/css">
      .layer-title { font-size: 20px; font-weight: bold; text-anchor: middle; }
      .section-title { font-size: 18px; font-weight: bold; text-anchor: middle; }
      .component-text { font-size: 13px; text-anchor: middle; dominant-baseline: middle; }
      .component-box { stroke: #333; stroke-width: 1; }
      .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
      .data-flow { stroke: #FF6B6B; stroke-width: 2; marker-end: url(#redarrow); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="redarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#FF6B6B" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1200" height="1000" fill="white"/>
  
  <!-- 标题 -->
  <text x="600" y="40" font-size="24" font-weight="bold" text-anchor="middle">BWMS 前台架构图</text>
  <text x="600" y="65" font-size="16" font-style="italic" text-anchor="middle">Bootstrap + BWMS API + JavaScript</text>

  <!-- 用户访问层 -->
  <rect x="50" y="100" width="1100" height="80" fill="#E6F7FF" stroke="#0099CC" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="125" class="section-title">用户访问层</text>
  <g transform="translate(80, 145)">
    <rect x="0" y="0" width="180" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white">网站访客</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white">注册用户</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white">会员用户</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white">移动端用户</text>
    
    <rect x="800" y="0" width="180" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="890" y="13" class="component-text" fill="white">搜索引擎</text>
  </g>

  <!-- 前端展示层 - Bootstrap -->
  <rect x="50" y="200" width="1100" height="140" fill="#FFF4E6" stroke="#FF8C00" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="225" class="section-title">前端展示层 - Bootstrap 技术栈</text>
  
  <!-- Bootstrap 核心 -->
  <g transform="translate(80, 245)">
    <rect x="0" y="0" width="240" height="35" fill="#7952B3" class="component-box" rx="5" ry="5"/>
    <text x="120" y="18" class="component-text" fill="white">Bootstrap 5.x 框架</text>
    
    <rect x="260" y="0" width="240" height="35" fill="#F7DF1E" class="component-box" rx="5" ry="5"/>
    <text x="380" y="18" class="component-text">JavaScript ES6+</text>
    
    <rect x="520" y="0" width="240" height="35" fill="#E34F26" class="component-box" rx="5" ry="5"/>
    <text x="640" y="18" class="component-text" fill="white">HTML5 + CSS3</text>
    
    <rect x="780" y="0" width="240" height="35" fill="#1572B6" class="component-box" rx="5" ry="5"/>
    <text x="900" y="18" class="component-text" fill="white">响应式设计</text>
  </g>
  
  <!-- 前端功能模块 -->
  <g transform="translate(80, 290)">
    <rect x="0" y="0" width="190" height="30" fill="#FF8C00" class="component-box" rx="5" ry="5"/>
    <text x="95" y="15" class="component-text" fill="white">jQuery / Axios</text>
    
    <rect x="210" y="0" width="190" height="30" fill="#FF8C00" class="component-box" rx="5" ry="5"/>
    <text x="305" y="15" class="component-text" fill="white">UI 组件库</text>
    
    <rect x="420" y="0" width="190" height="30" fill="#FF8C00" class="component-box" rx="5" ry="5"/>
    <text x="515" y="15" class="component-text" fill="white">表单验证</text>
    
    <rect x="630" y="0" width="190" height="30" fill="#FF8C00" class="component-box" rx="5" ry="5"/>
    <text x="725" y="15" class="component-text" fill="white">图片处理</text>
    
    <rect x="840" y="0" width="180" height="30" fill="#FF8C00" class="component-box" rx="5" ry="5"/>
    <text x="930" y="15" class="component-text" fill="white">SEO优化</text>
  </g>

  <!-- API 接口层 -->
  <rect x="50" y="360" width="1100" height="100" fill="#E6FFE6" stroke="#00AA00" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="385" class="section-title">BWMS API 接口层</text>
  <g transform="translate(80, 405)">
    <rect x="0" y="0" width="180" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white">RESTful API</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white">内容管理 API</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white">用户管理 API</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white">媒体管理 API</text>
    
    <rect x="800" y="0" width="180" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="890" y="13" class="component-text" fill="white">搜索 API</text>
  </g>
  
  <g transform="translate(80, 435)">
    <rect x="0" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="120" y="10" class="component-text" font-size="11">JWT 认证</text>
    
    <rect x="260" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="380" y="10" class="component-text" font-size="11">数据缓存</text>
    
    <rect x="520" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="640" y="10" class="component-text" font-size="11">限流控制</text>
    
    <rect x="780" y="0" width="200" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="880" y="10" class="component-text" font-size="11">错误处理</text>
  </g>

  <!-- 后端服务层 -->
  <rect x="50" y="480" width="1100" height="100" fill="#FFE6E6" stroke="#FF6666" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="505" class="section-title">BWMS 后端服务层</text>
  <g transform="translate(80, 525)">
    <rect x="0" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white">Laravel 框架</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white">业务逻辑层</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white">数据仓储层</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white">缓存服务</text>
    
    <rect x="800" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="890" y="13" class="component-text" fill="white">队列处理</text>
  </g>
  
  <g transform="translate(80, 555)">
    <rect x="0" y="0" width="240" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="120" y="10" class="component-text" font-size="11">Eloquent ORM + 数据库操作</text>
    
    <rect x="260" y="0" width="240" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="380" y="10" class="component-text" font-size="11">文件存储 + 媒体处理</text>
    
    <rect x="520" y="0" width="240" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="640" y="10" class="component-text" font-size="11">第三方集成</text>
    
    <rect x="780" y="0" width="200" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="880" y="10" class="component-text" font-size="11">监控日志</text>
  </g>

  <!-- 数据存储层 -->
  <rect x="50" y="600" width="1100" height="80" fill="#E6F0FF" stroke="#6699FF" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="625" class="section-title">数据存储层</text>
  <g transform="translate(130, 645)">
    <rect x="0" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">MySQL 数据库</text>
    
    <rect x="220" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="320" y="13" class="component-text" fill="white">Redis 缓存</text>
    
    <rect x="440" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="540" y="13" class="component-text" fill="white">文件存储</text>
    
    <rect x="660" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="760" y="13" class="component-text" fill="white">CDN加速</text>
  </g>

  <!-- 技术特点说明 -->
  <rect x="50" y="700" width="1100" height="280" fill="#F8F9FA" stroke="#6C757D" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="725" class="section-title">Bootstrap 前台架构特点</text>
  
  <g transform="translate(80, 750)">
    <circle cx="10" cy="10" r="5" fill="#28A745"/>
    <text x="25" y="15" font-size="14" font-weight="bold">优势：</text>
    <text x="80" y="15" font-size="13">• 快速开发，学习成本低</text>
    <text x="240" y="15" font-size="13">• 兼容性好，浏览器支持广泛</text>
    <text x="440" y="15" font-size="13">• SEO友好，服务端渲染</text>
    <text x="620" y="15" font-size="13">• 轻量级，加载速度快</text>
    <text x="800" y="15" font-size="13">• 成熟生态系统</text>
    
    <circle cx="10" cy="40" r="5" fill="#FFC107"/>
    <text x="25" y="45" font-size="14" font-weight="bold">适用：</text>
    <text x="80" y="45" font-size="13">• 企业官网、展示类网站</text>
    <text x="240" y="45" font-size="13">• 内容管理系统前台</text>
    <text x="440" y="45" font-size="13">• 移动端H5页面</text>
    <text x="620" y="45" font-size="13">• 快速原型开发</text>
    <text x="800" y="45" font-size="13">• 营销落地页</text>
    
    <circle cx="10" cy="70" r="5" fill="#17A2B8"/>
    <text x="25" y="75" font-size="14" font-weight="bold">技术：</text>
    <text x="80" y="75" font-size="13">• Bootstrap 5.x 响应式框架</text>
    <text x="280" y="75" font-size="13">• jQuery + Axios HTTP请求</text>
    <text x="500" y="75" font-size="13">• 原生JavaScript增强</text>
    <text x="700" y="75" font-size="13">• BWMS RESTful API</text>
    
    <circle cx="10" cy="100" r="5" fill="#DC3545"/>
    <text x="25" y="105" font-size="14" font-weight="bold">注意：</text>
    <text x="80" y="105" font-size="13">• 复杂交互可能需要更多JavaScript代码</text>
    <text x="360" y="105" font-size="13">• 状态管理相对简单</text>
    <text x="540" y="105" font-size="13">• 需要手动处理数据绑定</text>
    <text x="720" y="105" font-size="13">• 大型应用考虑Vue/React</text>
    
    <circle cx="10" cy="130" r="5" fill="#6F42C1"/>
    <text x="25" y="135" font-size="14" font-weight="bold">性能：</text>
    <text x="80" y="135" font-size="13">• 首屏加载速度快</text>
    <text x="200" y="135" font-size="13">• 浏览器缓存友好</text>
    <text x="360" y="135" font-size="13">• CDN分发优化</text>
    <text x="500" y="135" font-size="13">• 资源压缩合并</text>
    <text x="640" y="135" font-size="13">• 图片懒加载</text>
    <text x="780" y="135" font-size="13">• Service Worker</text>
    
    <circle cx="10" cy="160" r="5" fill="#FD7E14"/>
    <text x="25" y="165" font-size="14" font-weight="bold">发展：</text>
    <text x="80" y="165" font-size="13">• 组件化开发趋势</text>
    <text x="200" y="165" font-size="13">• PWA支持</text>
    <text x="300" y="165" font-size="13">• 微前端架构</text>
    <text x="420" y="165" font-size="13">• Jamstack部署</text>
    <text x="540" y="165" font-size="13">• TypeScript集成</text>
    <text x="680" y="165" font-size="13">• 无头CMS对接</text>
    
    <circle cx="10" cy="190" r="5" fill="#198754"/>
    <text x="25" y="195" font-size="14" font-weight="bold">维护：</text>
    <text x="80" y="195" font-size="13">• 团队学习成本低</text>
    <text x="180" y="195" font-size="13">• 文档完善</text>
    <text x="280" y="195" font-size="13">• 社区活跃</text>
    <text x="380" y="195" font-size="13">• 版本稳定</text>
    <text x="480" y="195" font-size="13">• 向后兼容性好</text>
    <text x="580" y="195" font-size="13">• 长期支持</text>
    
    <circle cx="10" cy="220" r="5" fill="#6C757D"/>
    <text x="25" y="225" font-size="14" font-weight="bold">工具：</text>
    <text x="80" y="225" font-size="13">• SASS/SCSS支持</text>
    <text x="180" y="225" font-size="13">• 自定义主题</text>
    <text x="280" y="225" font-size="13">• 构建工具集成</text>
    <text x="420" y="225" font-size="13">• 模板引擎</text>
    <text x="540" y="225" font-size="13">• 调试工具</text>
    <text x="640" y="225" font-size="13">• 测试框架</text>
  </g>

  <!-- 数据流箭头 -->
  <line x1="600" y1="180" x2="600" y2="200" class="arrow"/>
  <line x1="600" y1="340" x2="600" y2="360" class="arrow"/>
  <line x1="600" y1="460" x2="600" y2="480" class="arrow"/>
  <line x1="600" y1="580" x2="600" y2="600" class="arrow"/>
  
  <!-- 数据流说明 -->
  <line x1="180" y1="270" x2="180" y2="405" class="data-flow"/>
  <text x="190" y="335" font-size="12" fill="#FF6B6B">AJAX请求</text>
  
  <line x1="1020" y1="405" x2="1020" y2="270" class="data-flow"/>
  <text x="940" y="335" font-size="12" fill="#FF6B6B">JSON响应</text>
</svg> 