@import "../variable.less";

.bwms-page {
  .left-content {
    margin-top: 10px;

    .article-tit {
      margin-bottom: 16px;
      color: #34495e;
      font-size: 26px;
      line-height: 1.6;
    }

    .article-info {
      margin-bottom: 16px;
      padding-top: 8px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e5e9ee;
      .df(center);

      .label-text {
        margin-right: 4px;
        color: #9ca3af;
        font-size: 13px;

        .df(center);

        .label {
          margin-right: 3px;

          .iconfont {
            font-size: 14px;
          }
        }
      }
    }

    .tree-box {
      padding-right: 10px;
      position: sticky;
      top: 70px;
      z-index: 9;

      .df();
      justify-content: flex-end;

      .title-tree {
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 12px;
        background-color: #fff;
        min-width: 240px;

        .tit-word {
          a {
            padding: 5px 0;
            color: #34495e;
            font-size: 13px;
            line-height: 1.1;
            display: block;
          }

          .tit-word {
            padding-left: 10px;
          }
        }
      }
    }

    .article-banner {
      .pic {
        width: 100%;

        img {
          border-radius: 4px;
        }
      }
    }

    h1 {
      margin: 30px 0;
      color: #34495e;
      font-size: 30px;
      line-height: 2;
    }

    h2 {
      margin: 30px 0 15px;
      border-bottom: 1px solid #eee;
      color: #34495e;
      font-size: 25px;
      line-height: 2;
    }

    h3 {
      margin: 27px 0 13px;
      color: #34495e;
      font-size: 22px;
      line-height: 2;
    }

    p {
      margin: 8px 0;
      line-height: 1.8;
      word-spacing: 1px;
      color: #34495e;
    }

    .share-list {
      margin-top: 30px;
      .df(center, center);

      a {
        margin: 5px;
        border-radius: 50%;
        border: 1px solid;
        font-size: 32px;
        width: 32px;
        height: 32px;
        display: block;
        font-size: 20px;
        transition: all .35s ease-in-out;
        .df(center, center);

        &.icon-weibo {
          @color: #ff763b;
          color: @color;
          border-color: @color;

          &:hover {
            background-color: @color;
            color: #fff;
          }
        }

        &.icon-QQ {
          @color: #56b6e7;
          color: @color;
          border-color: @color;

          &:hover {
            background-color: @color;
            color: #fff;
          }
        }

        &.icon-kong {
          @color: #FDBE3D;
          color: @color;
          border-color: @color;

          &:hover {
            background-color: @color;
            color: #fff;
          }
        }

        &.icon-weixin {
          @color: #7bc549;
          color: @color;
          border-color: @color;

          &:hover {
            background-color: @color;
            color: #fff;
          }
        }
      }
    }

    .more-blog {
      .df(center, space-between);

      .more-btn {
        line-height: 1.3;

        span,
        a {
          display: block;
          font-size: 13px;
          color: #34495e;
        }

        a {
          padding-top: 8px;

          &:hover {
            color: #333;
          }
        }

        &.next {
          text-align: right;
        }
      }
    }

    .comment-list {
      .comment-item {
        margin-bottom: 24px;
        border-bottom: 1px solid #f3f4f6;
        padding-bottom: 24px;

        .comment-userinfo {
          .df(center);

          .avatar {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            overflow: hidden;
          }

          .user-info {
            padding-left: 16px;

            .nickname {
              font-size: 16px;
              color: #34495e;
              line-height: 1.5;
            }

            .comment-time {
              color: #c4cfdb;
              line-height: 1.2;
              font-size: 13px;
            }
          }
        }

        .comment-content {
          padding-left: 56px;
          margin-top: 16px;

          p {
            color: #34495e;
            font-size: 15px;
            line-height: 2;
          }
        }
      }
    }

    .pagination-box {
      .df(center, center);

      a {
        margin: 5px;
        border-radius: 5px;
        color: #666;
        font-size: 13px;

        min-width: 30px;
        min-height: 30px;
        .df(center, center);

        &.active {
          background-color: #3555CC;
          color: #fff;
        }

        &:hover {
          color: #3555CC;
        }
      }

      .more {
        color: #666;
        font-size: 13px;
      }
    }

    .form {
      .input-box {
        margin-top: 16px;
        border-radius: 3px;
        border: 1px solid #e5e9ee;
        padding: 12px;
        font-size: 16px;
        color: #34495e;
      }

      .identity {
        .df(center);

        .avatar {
          border-radius: 50%;
          width: 40px;
          height: 40px;
          overflow: hidden;
        }


        .input-box {
          margin-top: 0;
          margin-left: 16px;
          border: none;
          border-bottom: 1px solid #e5e9ee;
        }
      }

      .input-list {
        .df(center, space-between);
        flex-wrap: wrap;

        .input-box {
          width: calc(50% - 5px);

          &.validate {
            background-image: linear-gradient(180deg, #ffffff 0%, #f3f3f3 100%);
            color: #999;
            font-size: 13px;

            .iconfont {
              color: #4374F6;
              font-size: 16px;
            }
          }
        }
      }

      .tips {
        margin-top: 16px;
        color: #9ca3af;
        font-size: 13px;
      }
    }
  }

  .right-side {
    padding-top: 10px;

    .classify {
      padding: 12px;

      .col-6 {
        padding-left: 5px;
        padding-right: 5px;
      }

      .classify-item {
        margin-bottom: 12px;
        border-radius: 50px;
        border: 1px solid #e5e9ee;
        padding: 6px 14px;
        font-size: 13px;
        line-height: 1.6;
        color: #666;
        .df(center, center);

        .iconfont {
          margin-right: 4px;
          font-size: 12px;
        }
      }
    }

    .popular-blogs {
      padding: 12px;

      .list {
        .blog-item {
          margin-bottom: 8px;
          .df(center);

          &:last-child {
            margin-bottom: 0;
          }

          .iconfont {
            font-size: 13px;
            color: #c4cfdb;
          }

          a {
            color: #34495e;
            font-size: 13px;
            line-height: 1.2;
          }
        }
      }
    }
  }
}