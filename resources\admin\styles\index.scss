// 后台管理定义的 css 变量
@use 'var' as *;

// element-plus 样式
@use 'element' as *;

// theme
@use 'theme/index' as *;

// amis 相关样式
@use 'amis' as *;

// table 相关样式
@use 'table' as *;


// tailwindcss
@import 'tailwind.css';
@import 'iconfont/iconfont.css';

// element style 必须在 tailwindcss 之后，不然样式会被 tailwindcss 覆盖
// issue 在这里 https://github.com/tailwindlabs/tailwindcss/discussions/5969

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter/Inter-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}