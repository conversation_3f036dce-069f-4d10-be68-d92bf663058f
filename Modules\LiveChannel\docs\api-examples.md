# LiveChannel API 使用示例

## 📋 API 概述

LiveChannel 模块提供完整的 RESTful API 接口，支持频道的增删改查、状态管理、批量操作等功能。

## 🔗 基础 URL

```
https://your-domain.com/api/admin/live-channel
```

## 📝 请求/响应格式

### 请求格式
- Content-Type: `application/json`
- 认证: <PERSON>er <PERSON>

### 响应格式
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {}
}
```

## 🚀 API 接口示例

### 1. 获取频道列表

**请求**
```bash
GET /api/admin/live-channel?page=1&per_page=15&keyword=翡翠台&status=1&live_status=1
```

**响应**
```json
{
    "code": 0,
    "message": "获取成功",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "channel_num": "CH001",
                "name": "翡翠台",
                "name_hk": "翡翠台",
                "description": "TVB翡翠台高清直播",
                "description_hk": "TVB翡翠台高清直播",
                "cover_image_url": "https://example.com/cover.jpg",
                "stream_url": "https://example.com/stream.m3u8",
                "start_time": "2024-01-15T14:00:00.000000Z",
                "end_time": "2024-01-15T18:00:00.000000Z",
                "is_audio_only": false,
                "is_breaking_news": false,
                "is_hk_only": true,
                "live_status": 1,
                "live_status_text": "直播中",
                "status": 1,
                "status_text": "启用",
                "sort": 1,
                "created_at": "2024-01-15T10:00:00.000000Z",
                "updated_at": "2024-01-15T14:30:00.000000Z"
            }
        ],
        "first_page_url": "http://localhost/api/admin/live-channel?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "http://localhost/api/admin/live-channel?page=1",
        "next_page_url": null,
        "path": "http://localhost/api/admin/live-channel",
        "per_page": 15,
        "prev_page_url": null,
        "to": 1,
        "total": 1
    }
}
```

### 2. 创建频道

**请求**
```bash
POST /api/admin/live-channel
Content-Type: application/json

{
    "name": "明珠台",                    # 必填：频道名称
    "name_hk": "明珠台",                # 必填：繁体名称
    "description": "TVB明珠台高清直播",   # 必填：频道描述
    "description_hk": "TVB明珠台高清直播", # 必填：繁体描述
    "cover_image_url": "https://example.com/pearl.jpg", # 必填：封面图片
    "stream_url": "https://example.com/pearl.m3u8",    # 可选：直播流地址
    "stream_key": "pearl_stream_key_123",              # 可选：直播密钥
    "start_time": "2024-01-15 16:00:00",              # 必填：开始时间
    "end_time": "2024-01-15 20:00:00",                # 必填：结束时间
    "is_audio_only": false,                            # 必填：是否仅音频
    "is_breaking_news": false,                         # 必填：是否突发直播
    "is_hk_only": true,                               # 必填：是否仅限香港
    "live_status": 0,                                 # 可选：直播状态（默认0）
    "status": 1,                                      # 可选：状态（默认1）
    "sort": 2                                         # 可选：排序（默认0）
}
```

**响应**
```json
{
    "code": 0,
    "message": "创建成功",
    "data": {
        "id": 2,
        "channel_num": "CH002",
        "name": "明珠台",
        "name_hk": "明珠台",
        "description": "TVB明珠台高清直播",
        "description_hk": "TVB明珠台高清直播",
        "cover_image_url": "https://example.com/pearl.jpg",
        "stream_url": "https://example.com/pearl.m3u8",
        "start_time": "2024-01-15T16:00:00.000000Z",
        "end_time": "2024-01-15T20:00:00.000000Z",
        "is_audio_only": false,
        "is_breaking_news": false,
        "is_hk_only": true,
        "live_status": 0,
        "live_status_text": "关闭",
        "status": 1,
        "status_text": "启用",
        "sort": 2,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    }
}
```

### 3. 获取频道详情

**请求**
```bash
GET /api/admin/live-channel/1
```

**响应**
```json
{
    "code": 0,
    "message": "获取成功",
    "data": {
        "id": 1,
        "channel_num": "CH001",
        "name": "翡翠台",
        "name_hk": "翡翠台",
        "description": "TVB翡翠台高清直播",
        "description_hk": "TVB翡翠台高清直播",
        "cover_image_url": "https://example.com/cover.jpg",
        "stream_url": "https://example.com/stream.m3u8",
        "start_time": "2024-01-15T14:00:00.000000Z",
        "end_time": "2024-01-15T18:00:00.000000Z",
        "is_audio_only": false,
        "is_breaking_news": false,
        "is_hk_only": true,
        "live_status": 1,
        "live_status_text": "直播中",
        "status": 1,
        "status_text": "启用",
        "sort": 1,
        "created_at": "2024-01-15T10:00:00.000000Z",
        "updated_at": "2024-01-15T14:30:00.000000Z"
    }
}
```

### 4. 更新频道

**请求**
```bash
PUT /api/admin/live-channel/1
Content-Type: application/json

{
    "name": "翡翠台高清",
    "description": "TVB翡翠台高清直播，精彩节目不间断",
    "live_status": 1,
    "sort": 1
}
```

**响应**
```json
{
    "code": 0,
    "message": "更新成功",
    "data": {
        "id": 1,
        "channel_num": "CH001",
        "name": "翡翠台高清",
        "name_hk": "翡翠台",
        "description": "TVB翡翠台高清直播，精彩节目不间断",
        "description_hk": "TVB翡翠台高清直播",
        "cover_image_url": "https://example.com/cover.jpg",
        "stream_url": "https://example.com/stream.m3u8",
        "start_time": "2024-01-15T14:00:00.000000Z",
        "end_time": "2024-01-15T18:00:00.000000Z",
        "is_audio_only": false,
        "is_breaking_news": false,
        "is_hk_only": true,
        "live_status": 1,
        "live_status_text": "直播中",
        "status": 1,
        "status_text": "启用",
        "sort": 1,
        "created_at": "2024-01-15T10:00:00.000000Z",
        "updated_at": "2024-01-15T15:00:00.000000Z"
    }
}
```

### 5. 删除频道

**请求**
```bash
DELETE /api/admin/live-channel/2
```

**响应**
```json
{
    "code": 0,
    "message": "删除成功",
    "data": null
}
```

### 6. 批量删除

**请求**
```bash
DELETE /api/admin/live-channel/batch
Content-Type: application/json

{
    "ids": [3, 4, 5]
}
```

**响应**
```json
{
    "code": 0,
    "message": "批量删除成功",
    "data": null
}
```

### 7. 更新直播状态

**请求**
```bash
PUT /api/admin/live-channel/1/live-status
Content-Type: application/json

{
    "live_status": 1
}
```

**响应**
```json
{
    "code": 0,
    "message": "状态更新成功",
    "data": {
        "id": 1,
        "channel_num": "CH001",
        "name": "翡翠台高清",
        "live_status": 1,
        "live_status_text": "直播中",
        "updated_at": "2024-01-15T15:30:00.000000Z"
    }
}
```

### 8. 批量更新状态

**请求**
```bash
PUT /api/admin/live-channel/batch/status
Content-Type: application/json

{
    "ids": [1, 2, 3],
    "status": 0
}
```

**响应**
```json
{
    "code": 0,
    "message": "批量更新状态成功",
    "data": null
}
```

### 9. 获取直播中的频道

**请求**
```bash
GET /api/admin/live-channel/live/list
```

**响应**
```json
{
    "code": 0,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "channel_num": "CH001",
            "name": "翡翠台高清",
            "name_hk": "翡翠台",
            "description": "TVB翡翠台高清直播，精彩节目不间断",
            "cover_image_url": "https://example.com/cover.jpg",
            "stream_url": "https://example.com/stream.m3u8",
            "live_status": 1,
            "live_status_text": "直播中",
            "sort": 1
        }
    ]
}
```

### 10. 获取统计信息

**请求**
```bash
GET /api/admin/live-channel/statistics
```

**响应**
```json
{
    "code": 0,
    "message": "获取成功",
    "data": {
        "total": 5,
        "enabled": 4,
        "live": 2,
        "disabled": 1
    }
}
```

## 🔍 查询参数

### 列表查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| keyword | string | 关键词搜索 | `?keyword=翡翠台` |
| status | integer | 状态筛选 | `?status=1` |
| live_status | integer | 直播状态筛选 | `?live_status=1` |
| start_date | string | 开始时间筛选 | `?start_date=2024-01-15` |
| end_date | string | 结束时间筛选 | `?end_date=2024-01-16` |
| sort_field | string | 排序字段 | `?sort_field=created_at` |
| sort_order | string | 排序方向 | `?sort_order=desc` |
| per_page | integer | 每页数量 | `?per_page=20` |
| page | integer | 页码 | `?page=2` |

### 状态值说明

#### 频道状态 (status)
- `0`: 禁用
- `1`: 启用

#### 直播状态 (live_status)
- `0`: 关闭
- `1`: 直播中
- `2`: 暂停

## ⚠️ 错误响应

### 验证错误
```json
{
    "code": 18005,
    "message": "频道名称不能为空",
    "data": null
}
```

### 业务错误
```json
{
    "code": 18001,
    "message": "频道不存在",
    "data": null
}
```

### 系统错误
```json
{
    "code": 500,
    "message": "Internal Server Error",
    "data": null
}
```

## 🔐 认证要求

所有 API 接口都需要 Bearer Token 认证：

```bash
Authorization: Bearer your-token-here
```

## 📊 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 422 | 验证失败 |
| 500 | 服务器错误 |

## 🚀 最佳实践

1. **错误处理**: 始终检查响应状态码和错误信息
2. **分页**: 使用分页参数避免一次性加载大量数据
3. **缓存**: 对不经常变化的数据进行缓存
4. **重试**: 对网络错误实现重试机制
5. **日志**: 记录重要的 API 调用和错误信息 