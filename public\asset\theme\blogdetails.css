.bwms-page .left-content {
  margin-top: 10px;
}
.bwms-page .left-content .article-tit {
  margin-bottom: 16px;
  color: #34495e;
  font-size: 26px;
  line-height: 1.6;
}
.bwms-page .left-content .article-info {
  margin-bottom: 16px;
  padding-top: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e9ee;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .article-info .label-text {
  margin-right: 4px;
  color: #9ca3af;
  font-size: 13px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .article-info .label-text .label {
  margin-right: 3px;
}
.bwms-page .left-content .article-info .label-text .label .iconfont {
  font-size: 14px;
}
.bwms-page .left-content .tree-box {
  padding-right: 10px;
  position: sticky;
  top: 70px;
  z-index: 9;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  justify-content: flex-end;
}
.bwms-page .left-content .tree-box .title-tree {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 12px;
  background-color: #fff;
  min-width: 240px;
}
.bwms-page .left-content .tree-box .title-tree .tit-word a {
  padding: 5px 0;
  color: #34495e;
  font-size: 13px;
  line-height: 1.1;
  display: block;
}
.bwms-page .left-content .tree-box .title-tree .tit-word .tit-word {
  padding-left: 10px;
}
.bwms-page .left-content .article-banner .pic {
  width: 100%;
}
.bwms-page .left-content .article-banner .pic img {
  border-radius: 4px;
}
.bwms-page .left-content h1 {
  margin: 30px 0;
  color: #34495e;
  font-size: 30px;
  line-height: 2;
}
.bwms-page .left-content h2 {
  margin: 30px 0 15px;
  border-bottom: 1px solid #eee;
  color: #34495e;
  font-size: 25px;
  line-height: 2;
}
.bwms-page .left-content h3 {
  margin: 27px 0 13px;
  color: #34495e;
  font-size: 22px;
  line-height: 2;
}
.bwms-page .left-content p {
  margin: 8px 0;
  line-height: 1.8;
  word-spacing: 1px;
  color: #34495e;
}
.bwms-page .left-content .share-list {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .share-list a {
  margin: 5px;
  border-radius: 50%;
  border: 1px solid;
  font-size: 32px;
  width: 32px;
  height: 32px;
  display: block;
  font-size: 20px;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .share-list a.icon-weibo {
  color: #ff763b;
  border-color: #ff763b;
}
.bwms-page .left-content .share-list a.icon-weibo:hover {
  background-color: #ff763b;
  color: #fff;
}
.bwms-page .left-content .share-list a.icon-QQ {
  color: #56b6e7;
  border-color: #56b6e7;
}
.bwms-page .left-content .share-list a.icon-QQ:hover {
  background-color: #56b6e7;
  color: #fff;
}
.bwms-page .left-content .share-list a.icon-kong {
  color: #FDBE3D;
  border-color: #FDBE3D;
}
.bwms-page .left-content .share-list a.icon-kong:hover {
  background-color: #FDBE3D;
  color: #fff;
}
.bwms-page .left-content .share-list a.icon-weixin {
  color: #7bc549;
  border-color: #7bc549;
}
.bwms-page .left-content .share-list a.icon-weixin:hover {
  background-color: #7bc549;
  color: #fff;
}
.bwms-page .left-content .more-blog {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.bwms-page .left-content .more-blog .more-btn {
  line-height: 1.3;
}
.bwms-page .left-content .more-blog .more-btn span,
.bwms-page .left-content .more-blog .more-btn a {
  display: block;
  font-size: 13px;
  color: #34495e;
}
.bwms-page .left-content .more-blog .more-btn a {
  padding-top: 8px;
}
.bwms-page .left-content .more-blog .more-btn a:hover {
  color: #333;
}
.bwms-page .left-content .more-blog .more-btn.next {
  text-align: right;
}
.bwms-page .left-content .comment-list .comment-item {
  margin-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 24px;
}
.bwms-page .left-content .comment-list .comment-item .comment-userinfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .comment-list .comment-item .comment-userinfo .avatar {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  overflow: hidden;
}
.bwms-page .left-content .comment-list .comment-item .comment-userinfo .user-info {
  padding-left: 16px;
}
.bwms-page .left-content .comment-list .comment-item .comment-userinfo .user-info .nickname {
  font-size: 16px;
  color: #34495e;
  line-height: 1.5;
}
.bwms-page .left-content .comment-list .comment-item .comment-userinfo .user-info .comment-time {
  color: #c4cfdb;
  line-height: 1.2;
  font-size: 13px;
}
.bwms-page .left-content .comment-list .comment-item .comment-content {
  padding-left: 56px;
  margin-top: 16px;
}
.bwms-page .left-content .comment-list .comment-item .comment-content p {
  color: #34495e;
  font-size: 15px;
  line-height: 2;
}
.bwms-page .left-content .pagination-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .pagination-box a {
  margin: 5px;
  border-radius: 5px;
  color: #666;
  font-size: 13px;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .pagination-box a.active {
  background-color: #3555CC;
  color: #fff;
}
.bwms-page .left-content .pagination-box a:hover {
  color: #3555CC;
}
.bwms-page .left-content .pagination-box .more {
  color: #666;
  font-size: 13px;
}
.bwms-page .left-content .form .input-box {
  margin-top: 16px;
  border-radius: 3px;
  border: 1px solid #e5e9ee;
  padding: 12px;
  font-size: 16px;
  color: #34495e;
}
.bwms-page .left-content .form .identity {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .form .identity .avatar {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  overflow: hidden;
}
.bwms-page .left-content .form .identity .input-box {
  margin-top: 0;
  margin-left: 16px;
  border: none;
  border-bottom: 1px solid #e5e9ee;
}
.bwms-page .left-content .form .input-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.bwms-page .left-content .form .input-list .input-box {
  width: calc(50% - 5px);
}
.bwms-page .left-content .form .input-list .input-box.validate {
  background-image: linear-gradient(180deg, #ffffff 0%, #f3f3f3 100%);
  color: #999;
  font-size: 13px;
}
.bwms-page .left-content .form .input-list .input-box.validate .iconfont {
  color: #4374F6;
  font-size: 16px;
}
.bwms-page .left-content .form .tips {
  margin-top: 16px;
  color: #9ca3af;
  font-size: 13px;
}
.bwms-page .right-side {
  padding-top: 10px;
}
.bwms-page .right-side .classify {
  padding: 12px;
}
.bwms-page .right-side .classify .col-6 {
  padding-left: 5px;
  padding-right: 5px;
}
.bwms-page .right-side .classify .classify-item {
  margin-bottom: 12px;
  border-radius: 50px;
  border: 1px solid #e5e9ee;
  padding: 6px 14px;
  font-size: 13px;
  line-height: 1.6;
  color: #666;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .right-side .classify .classify-item .iconfont {
  margin-right: 4px;
  font-size: 12px;
}
.bwms-page .right-side .popular-blogs {
  padding: 12px;
}
.bwms-page .right-side .popular-blogs .list .blog-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .right-side .popular-blogs .list .blog-item:last-child {
  margin-bottom: 0;
}
.bwms-page .right-side .popular-blogs .list .blog-item .iconfont {
  font-size: 13px;
  color: #c4cfdb;
}
.bwms-page .right-side .popular-blogs .list .blog-item a {
  color: #34495e;
  font-size: 13px;
  line-height: 1.2;
}
