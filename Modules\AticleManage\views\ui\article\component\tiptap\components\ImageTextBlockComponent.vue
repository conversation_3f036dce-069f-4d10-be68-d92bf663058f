<template>
  <node-view-wrapper class="image-text-block-component">
    <div 
      class="image-text-block" 
      :class="blockClasses"
      data-type="image-text-block"
      :data-title="node.attrs.title"
      :data-content="node.attrs.content"
      :data-image-url="node.attrs.imageUrl"
      :data-image-alt="node.attrs.imageAlt"
      :data-layout="node.attrs.layout"
    >
      <div class="image-container">
        <img 
          :src="node.attrs.imageUrl" 
          :alt="node.attrs.imageAlt" 
          class="image-content"
        />
      </div>
      
      <div class="text-container">
        <h3 class="text-title">{{ node.attrs.title }}</h3>
        <p class="text-content">{{ node.attrs.content }}</p>
      </div>
      
      <div v-if="editor.isEditable" class="block-toolbar">
        <button 
          class="edit-button" 
          @click.stop="openEditDialog"
          title="编辑"
        >
          ✏️
        </button>
        <button 
          class="layout-button" 
          @click.stop="toggleLayout"
          :title="node.attrs.layout === 'image-left' ? '图片居右' : '图片居左'"
        >
          🔄
        </button>
      </div>
      
      <!-- 编辑对话框 -->
      <el-dialog v-model="showEditDialog" title="编辑图文区块" width="500px" append-to-body >
        <div class="edit-dialog-content">
          <h4>编辑图文区块</h4>
          
          <div class="edit-form-group">
            <label for="title">标题</label>
            <input 
              id="title" 
              v-model="editForm.title" 
              type="text" 
              class="edit-input"
            />
          </div>
          
          <div class="edit-form-group">
            <label for="content">内容</label>
            <textarea 
              id="content" 
              v-model="editForm.content" 
              class="edit-textarea"
            ></textarea>
          </div>
          
          <div class="edit-form-group">
            <label for="image-url">图片URL</label>
            <input 
              id="image-url" 
              v-model="editForm.imageUrl" 
              type="text" 
              class="edit-input"
            />
          </div>
          
          <div class="edit-form-group">
            <label for="image-alt">图片Alt文本</label>
            <input 
              id="image-alt" 
              v-model="editForm.imageAlt" 
              type="text" 
              class="edit-input"
            />
          </div>
          
          <div class="edit-form-group">
            <label>布局</label>
            <div class="layout-options">
              <div 
                class="layout-option" 
                :class="{ active: editForm.layout === 'image-left' }"
                @click="editForm.layout = 'image-left'"
              >
                <div class="layout-preview image-left-preview"></div>
                <span>图片居左</span>
              </div>
              <div 
                class="layout-option" 
                :class="{ active: editForm.layout === 'image-right' }"
                @click="editForm.layout = 'image-right'"
              >
                <div class="layout-preview image-right-preview"></div>
                <span>图片居右</span>
              </div>
            </div>
          </div>
          
          <div class="edit-form-actions">
            <button 
              class="edit-cancel-button" 
              @click="showEditDialog = false"
            >
              取消
            </button>
            <button 
              class="edit-save-button" 
              @click="saveChanges"
            >
              保存
            </button>
          </div>
        </div>
      </el-dialog>
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { NodeViewWrapper } from '@tiptap/vue-3'

const props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  updateAttributes: {
    type: Function,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
})

const showEditDialog = ref(false)

// 编辑表单数据
const editForm = ref({
  title: props.node.attrs.title || '图文标题',
  content: props.node.attrs.content || '这里是描述文字，介绍你的产品或服务特点。可以添加更多内容来吸引读者的注意力。',
  imageUrl: props.node.attrs.imageUrl || 'https://via.placeholder.com/400x300',
  imageAlt: props.node.attrs.imageAlt || '示例图片',
  layout: props.node.attrs.layout || 'image-left',
})

// 区块样式计算属性
const blockClasses = computed(() => {
  return {
    [`layout-${props.node.attrs.layout}`]: true
  }
})

// 打开编辑对话框
const openEditDialog = () => {
  // 更新表单数据为当前节点的属性
  editForm.value = {
    title: props.node.attrs.title,
    content: props.node.attrs.content,
    imageUrl: props.node.attrs.imageUrl,
    imageAlt: props.node.attrs.imageAlt,
    layout: props.node.attrs.layout,
  }
  showEditDialog.value = true
}

// 切换布局（图片左/右）
const toggleLayout = () => {
  const newLayout = props.node.attrs.layout === 'image-left' ? 'image-right' : 'image-left'
  props.updateAttributes({
    layout: newLayout
  })
}

// 保存修改
const saveChanges = () => {
  props.updateAttributes({
    title: editForm.value.title,
    content: editForm.value.content,
    imageUrl: editForm.value.imageUrl,
    imageAlt: editForm.value.imageAlt,
    layout: editForm.value.layout,
  })
  
  showEditDialog.value = false
}

// 点击页面其他区域关闭对话框
const closeDialogOnClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const dialog = document.querySelector('.edit-dialog-content')
  
  if (showEditDialog.value && dialog && !dialog.contains(target) && !target.closest('.edit-button')) {
    showEditDialog.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDialogOnClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', closeDialogOnClickOutside)
})
</script>

<style scoped>
.image-text-block-component {
  margin: 2em 0;
  position: relative;
}

.image-text-block {
  display: flex;
  gap: 20px;
  border: 1px dashed #dcdfe6;
  padding: 16px;
  border-radius: 4px;
  background-color: rgba(245, 247, 250, 0.5);
  position: relative;
}

/* 图片左侧布局 */
.layout-image-left {
  flex-direction: row;
}

/* 图片右侧布局 */
.layout-image-right {
  flex-direction: row-reverse;
}

.image-container {
  flex: 4;
  min-width: 150px;
}

.image-content {
  width: 100%;
  height: auto;
  border-radius: 4px;
  object-fit: cover;
}

.text-container {
  flex: 6;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.text-title {
  font-size: 1.4em;
  color: #303133;
  margin: 0 0 0.6em 0;
}

.text-content {
  font-size: 1em;
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

/* 工具栏 */
.block-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.image-text-block:hover .block-toolbar {
  opacity: 1;
}

.edit-button,
.layout-button {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.edit-button:hover,
.layout-button:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

/* 编辑对话框 */
.edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.edit-dialog-content {
  background-color: white;
  border-radius: 6px;
  padding: 1.5em;
  width: 500px;
  max-width: 90vw;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.edit-dialog-content h4 {
  margin: 0 0 1em 0;
  font-size: 1.2em;
  color: #303133;
}

.edit-form-group {
  margin-bottom: 1em;
}

.edit-form-group label {
  display: block;
  margin-bottom: 0.5em;
  font-size: 0.9em;
  color: #606266;
}

.edit-input,
.edit-textarea {
  width: 100%;
  padding: 0.5em;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.5;
  color: #606266;
}

.edit-textarea {
  min-height: 80px;
  resize: vertical;
}

.layout-options {
  display: flex;
  gap: 15px;
}

.layout-option {
  text-align: center;
  cursor: pointer;
}

.layout-preview {
  width: 100px;
  height: 70px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 5px;
  display: flex;
  padding: 10px;
  background-color: #f5f7fa;
}

.layout-option.active .layout-preview {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.image-left-preview {
  position: relative;
  flex-direction: row;
}

.image-left-preview:before,
.image-left-preview:after {
  content: '';
  border-radius: 2px;
}

.image-left-preview:before {
  flex: 4;
  background-color: #c0c4cc;
  margin-right: 5px;
}

.image-left-preview:after {
  flex: 6;
  background-color: #e4e7ed;
  position: relative;
}

.image-right-preview {
  position: relative;
  flex-direction: row-reverse;
}

.image-right-preview:before,
.image-right-preview:after {
  content: '';
  border-radius: 2px;
}

.image-right-preview:before {
  flex: 4;
  background-color: #c0c4cc;
  margin-left: 5px;
}

.image-right-preview:after {
  flex: 6;
  background-color: #e4e7ed;
  position: relative;
}

.edit-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 1.5em;
}

.edit-cancel-button,
.edit-save-button {
  padding: 0.5em 1em;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.edit-cancel-button {
  background-color: white;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.edit-save-button {
  background-color: #409eff;
  border: 1px solid #409eff;
  color: white;
}

.edit-cancel-button:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.edit-save-button:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-text-block {
    flex-direction: column !important;
    padding: 12px;
  }
  
  .image-container {
    width: 100%;
  }
  
  .text-title {
    font-size: 1.2em;
    margin-top: 0.8em;
  }
  
  .layout-options {
    flex-direction: column;
    align-items: center;
  }
}
</style> 