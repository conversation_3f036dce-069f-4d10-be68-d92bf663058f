<?php

namespace Modules\Common\Provider\Schedule;

use Bingo\Core\Type\BaseType;

class RunStatus implements BaseType
{
    public const RUNNING = 1;
    public const SUCCESS = 2;
    public const FAILED = 3;

    public static function getList(): array
    {
        return [
            self::RUNNING => '运行中',
            self::SUCCESS => '成功',
            self::FAILED => '失败',
        ];
    }


}
