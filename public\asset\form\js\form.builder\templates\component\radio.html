{{ if (field.containerClass) { }}<!-- Radio -->
<div class="{{= field.containerClass }}">
    <div class="form-group{{ if(field.required) { }} required-control{{ } }}">{{ if (field.label) { }}
        <label {{ if (field.labelClass) { }} class="{{= field.labelClass }}"{{ } }} for="{{= field.id }}">{{= field.label }}</label>{{ } }}{{ if (field.helpText && field.helpTextPlacement === "above") { }}
        <p class="form-text">{{= field.helpText }}</p>{{ } }}{{ _.each(field.radios, function(radio, i){ var items = radio.split("|"); var last = items[items.length-1]; var checked = (last==="selected"||last==="select"|last==="check"); radio = items[0]; var value = ((items.length >= 3) || (items.length == 2) && !checked) ? items[1] : items[0]; var figure = ((items.length >= 4) || (items.length == 3 && !checked)) ? items[2] : false; }}
        <div{{ if (field.cssClass) { }} class="{{= field.cssClass }}{{ if (field.inline) { }} form-check-inline{{ } }}"{{ } }}{{ if(field.disabled) { }} disabled{{ } }}>
            <input type="radio" name="{{= field.id }}" id="{{= field.id+'_'+i }}" class="form-check-input" value="{{- value.replace(/(<([^>]+)>)/ig,'') }}" data-alias="{{= field.alias }}"{{ if(field.required){ }}data-required="true" {{ } }}{{ if(field.readOnly) { }}readOnly {{ } }}{{ if(field.disabled) {}}disabled {{ } }} {{ if(checked) { }}checked {{ } }}{{ _.each(field.customAttributes, function(attribute, i){ var attrs = attribute.split("|"); var attr = attrs[0]; var value = (attrs.length > 1) ? attrs[1] : attrs[0]; }}{{ if (attr) { }} {{- attr }}="{{- value }}"{{ } }}{{ }) }}>
            <label for="{{= field.id+'_'+i }}" class="form-check-label">{{ if(figure) { }}
                <span class="figure">{{ if (figure.indexOf("http") == 0) { }}<img src="{{= figure }}" />{{ } else { }}<span class="{{= figure }}"> </span>{{ } }}</span>{{ } }}
                {{= radio }}
            </label>
        </div>{{ }); }}
        <span id="{{= field.id }}"></span>
        {{ if (field.helpText && field.helpTextPlacement === "below") { }}<p class="form-text">{{= field.helpText }}</p>{{ } }}
    </div>
</div>
{{ } else { }}<!-- Radio -->
<div class="form-group{{ if(field.required) { }} required-control{{ } }}">{{ if (field.label) { }}
    <label {{ if (field.labelClass) { }} class="{{= field.labelClass }}"{{ } }} for="{{= field.id }}">{{= field.label }}</label>{{ } }}{{ if (field.helpText && field.helpTextPlacement === "above") { }}
    <p class="form-text">{{= field.helpText }}</p>{{ } }}{{ _.each(field.radios, function(radio, i){ var items = radio.split("|"); var last = items[items.length-1]; var checked = (last==="selected"||last==="select"|last==="check"); radio = items[0]; var value = ((items.length >= 3) || (items.length == 2) && !checked) ? items[1] : items[0]; var figure = ((items.length >= 4) || (items.length == 3 && !checked)) ? items[2] : false; }}
    <div{{ if (field.cssClass) { }} class="{{= field.cssClass }}{{ if (field.inline) { }} form-check-inline{{ } }}"{{ } }}{{ if(field.disabled) { }} disabled{{ } }}>
        <input type="radio" name="{{= field.id }}" id="{{= field.id+'_'+i }}" class="form-check-input" value="{{- value.replace(/(<([^>]+)>)/ig,'') }}" data-alias="{{= field.alias }}"{{ if(field.required){ }}data-required="true" {{ } }}{{ if(field.readOnly) { }}readOnly {{ } }}{{ if(field.disabled) {}}disabled {{ } }} {{ if(checked) { }}checked {{ } }}{{ _.each(field.customAttributes, function(attribute, i){ var attrs = attribute.split("|"); var attr = attrs[0]; var value = (attrs.length > 1) ? attrs[1] : attrs[0]; }}{{ if (attr) { }} {{- attr }}="{{- value }}"{{ } }}{{ }) }}>
        <label class="form-check-label" for="{{= field.id+'_'+i }}">{{ if(figure) { }}
        <span class="figure">{{ if (figure.indexOf("http") == 0) { }}<img src="{{= figure }}" />{{ } else { }}<span class="{{= figure }}"> </span>{{ } }}</span>{{ } }}
            {{= radio }}
        </label>
    </div>{{ }); }}
    <span id="{{= field.id }}"></span>
    {{ if (field.helpText && field.helpTextPlacement === "below") { }}<p class="form-text">{{= field.helpText }}</p>{{ } }}
</div>
{{ } }}