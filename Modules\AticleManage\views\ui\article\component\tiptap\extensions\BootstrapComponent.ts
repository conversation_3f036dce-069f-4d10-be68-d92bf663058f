import { Node, mergeAttributes } from '@tiptap/core'
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap/dist/js/bootstrap.bundle.min.js'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    bootstrapComponent: {
      /**
       * 插入Bootstrap组件
       */
      insertBootstrapComponent: (options: { html: string }) => ReturnType
    }
  }
}

export const BootstrapComponent = Node.create({
  name: 'bootstrapComponent',
  
  group: 'block',
  
  atom: true,
  
  draggable: true,
  
  inline: false,
  
  selectable: true,

  // 支持添加自定义HTML属性
  addAttributes() {
    return {
      // 保存组件类型
      componentType: {
        default: 'generic',
        parseHTML: element => {
          return element.getAttribute('data-bs-component') || 'generic'
        }
      },
      // 保存原始HTML内容
      content: {
        default: '',
        parseHTML: element => {
          const temp = document.createElement('div')
          Array.from(element.childNodes).forEach(node => {
            temp.appendChild(node.cloneNode(true))
          })
          return temp.innerHTML
        }
      },
      // 保存class
      class: {
        default: null,
        parseHTML: element => {
          return element.getAttribute('class')
        }
      },
      // 保存style
      style: {
        default: null,
        parseHTML: element => {
          return element.getAttribute('style')
        }
      },
      // 保存其他data属性
      dataAttributes: {
        default: {},
        parseHTML: element => {
          const attrs = {}
          Array.from(element.attributes).forEach(attr => {
            if (attr.name.startsWith('data-') && attr.name !== 'data-bs-component') {
              attrs[attr.name] = attr.value
            }
          })
          return attrs
        }
      }
    }
  },

  // 定义HTML解析规则
  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }
          
          return {
            componentType: element.getAttribute('data-bs-component'),
            class: element.getAttribute('class'),
            style: element.getAttribute('style'),
            content: element.innerHTML
          }
        }
      }
    ]
  },

  // 定义HTML渲染规则
  renderHTML({ node }) {
    try {
      // 基础属性
      const attrs: Record<string, string> = {
        'data-bs-component': node.attrs.componentType || 'generic'
      }

      // 添加class
      if (node.attrs.class) {
        attrs.class = node.attrs.class
      }

      // 添加style
      if (node.attrs.style) {
        attrs.style = node.attrs.style
      }

      // 使用 prosemirror-model 的数组格式来渲染
      return [
        'div',
        mergeAttributes(attrs),
        ...createChildNodes(node.attrs.content)
      ]
    } catch (error) {
      console.error('Bootstrap component render error:', error)
      // 返回一个基础的错误提示组件
      return ['div', { 
        'data-bs-component': 'error',
        'class': 'alert alert-danger'
      }, '组件渲染错误']
    }
  },

  // 添加命令
  addCommands() {
    return {
      insertBootstrapComponent: options => ({ commands }) => {
        try {
          // 创建临时容器解析HTML
          const temp = document.createElement('div')
          temp.innerHTML = options.html.trim()
          
          // 获取第一个元素
          const element = temp.firstElementChild
          if (!element || !(element instanceof HTMLElement)) {
            return false
          }

          // 确保有data-bs-component属性
          const componentType = element.getAttribute('data-bs-component') || 'generic'
          element.setAttribute('data-bs-component', componentType)

          // 收集属性
          const attrs = {
            componentType,
            class: element.getAttribute('class'),
            style: element.getAttribute('style'),
            content: element.innerHTML
          }

          // 插入内容
          return commands.insertContent({
            type: this.name,
            attrs
          })
        } catch (error) {
          console.error('Bootstrap component insert error:', error)
          return false
        }
      }
    }
  }
})

// 辅助函数：将 HTML 字符串转换为 ProseMirror 节点数组
function createChildNodes(html: string): any[] {
  if (!html) return []

  const temp = document.createElement('div')
  temp.innerHTML = html

  return Array.from(temp.childNodes).map(node => {
    if (node.nodeType === 1) { // ELEMENT_NODE
      if (node instanceof HTMLElement) {
        const attrs: Record<string, string> = {}
        
        // 收集属性
        Array.from(node.attributes).forEach(attr => {
          attrs[attr.name] = attr.value
        })

        // 递归处理子节点
        return [
          node.tagName.toLowerCase(),
          attrs,
          ...createChildNodes(node.innerHTML)
        ]
      }
    } else if (node.nodeType === 3) { // TEXT_NODE
      return node.textContent || ''
    }

    return ''
  })
} 