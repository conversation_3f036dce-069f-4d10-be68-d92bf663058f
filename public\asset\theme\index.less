@import "./variable.less";

.bwms-page {
  .banner {
    position: relative;

    .navigation-pagination {
      .iconfont {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 9;

        border-radius: 50%;
        width: 42px;
        height: 42px;
        background-color: rgba(0, 0, 0, 0);
        color: #fff;
        opacity: 0;
        transition: opacity .35s ease-in-out, background-color .35s ease-in-out;

        .df(center, center);

        &.icon-arrow-left {
          left: 20px;
        }

        &.icon-arrow-right {
          right: 20px;
        }
      }
    }

    .swiper-pagination {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
    }

    &:hover {
      .navigation-pagination {
        .iconfont {
          opacity: 1;
          background-color: rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .recommend-module {
    background-color: #F6F6F6;

    .tabs {
      .tab-list {
        margin-bottom: 30px;
        .df(center, center);

        .tab-item {
          margin: 0 10px;
          border-radius: 30px;
          padding: 0px 20px;
          background-color: #fff;
          color: #6e6e6e;
          font-size: 16px;
          cursor: pointer;
          line-height: 2.625;
          transition: background .35s ease-in-out;

          &.active,
          &:hover {
            border-bottom: 0px solid #FD3C6B;
            background-image: linear-gradient(-45deg, #FFD100, #ff9600);
            color: #fff;
          }
        }
      }

      .pane-list {
        position: relative;

        .tab-pane {
          opacity: 0;
          position: absolute;
          top: 0;
          left: 0;
          z-index: -1;
          width: 100%;
          transition: opacity .35s ease-in-out;

          &.active {
            position: static;
            opacity: 1;
          }

          .swiper-slide {
            padding: 30px;
            background-color: #fff;

            .pic {
              width: 100%;
            }

            .text-box {
              margin-top: 13px;

              a {
                color: #383838;
                font-size: 16px;
                line-height: 1.625;
                width: 100%;
                text-align: center;
                display: block;
              }
            }
          }

          .swiper-pagination {
            margin-top: 20px;
            width: 100%;
            .df(center, center);
          }
        }
      }
    }
  }

  .about-module {
    background: url(../image/index/about-bg.jpg) no-repeat center center / cover;

    .text-img {
      display: flex;

      .left-text {
        .df(flex-start, flex-start, column);

        padding: 40px 50px;
        background-color: #fff;
        width: 50%;

        h6 {
          position: relative;
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 40px;
          word-break: break-all;

          &:before {
            content: '';
            display: block;
            position: absolute;
            background: #FFD100;
            width: 30px;
            height: 3px;
            bottom: -15px;
            left: 0;
          }
        }

        .desc {
          font-size: 14px;
          color: #6E6E6E;
          line-height: 2;
          .vertical(6)
        }

        .btn-box {
          margin-top: 20px;
          background-color: #ff9600;
          border-radius: 50px;
          padding: 8px 14px;
          color: #fff;
          transition: background-color 1s cubic-bezier(0.175,0.885,0.32,1) 0s;
          display: block;

          &:hover {
            background-color: #FCB319;
          }
        }
      }

      .right-img {
        width: 50%;
        position: relative;
        overflow: hidden;

        img {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          object-fit: cover;
        }
      }
    }
  }

  .case-module {
    .list {
      @space: 1px;
      margin-left: -@space;
      margin-right: -@space;
      margin-bottom: calc(-@space * 2);

      .item {
        padding-left: @space;
        padding-right: @space;
        padding-bottom: calc(@space * 2);

        .box {
          @padding: 25px;
          position: relative;

          .mask {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 3;

            padding: @padding;
            background: url(../image/index/case-bg.png) no-repeat center center / cover;
            color: #fff;
            opacity: 0;
            transform: scale(1);
            transition: transform .35s ease-in-out, opacity .35s ease-in-out;

            h2 {
              margin-bottom: 10px;
              padding-top: 20px;
              font-size: 20px;
            }

            p {
              font-size: 14px;
              line-height: 1.71;
            }

            .btn-box {
              padding-bottom: 10px;
              font-size: 14px;

              position: absolute;
              bottom: @padding;
              right: @padding;
            }
          }

          .text-box {
            padding: @padding;

            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            z-index: 1;
            background-image: linear-gradient(to bottom,rgba(0,0,0,.2) 65%,rgba(0,0,0,.8) 100%);

            .df(stretch, flex-start, column-reverse);

            p {
              margin-bottom: 5px;
              color: #fff;
              font-size: 16px;
            }
          }

          &:hover {
            .mask {
              opacity: 1;
              transform: scale(1.1);
            }
          }
        }
      }
    }
  }

  .news-module {
    background-color: #F7F7F7;

    .img-list {
      .df();

      .left-img {
        padding-right: 10px;
        width: 50%;
        position: relative;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          right: 10px;
          bottom: 0;
          z-index: 1;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .post-title {
          position: absolute;
          bottom: -30px;
          left: 30px;
          z-index: 2;

          box-shadow: 0 5px 15px 0 rgba(0,0,0,0.06);
          padding: 20px;
          background: #fff;
          width: 65%;
          color: #333;
          transition: bottom 0.5s ease-in-out;

          h3 {
            a {
              color: #333;
              font-size: 18px;
              font-weight: 600;
              line-height: 1.55;
              display: block;
            }
          }

          p {
            padding-top: 10px;
            color: #888888;
            font-size: 14px;
          }

          .btn-box {
            .btn-square();
          }
        }

        &:hover {
          .post-title {
            bottom: 30px;

            .btn-box {
              background-color: #ff9600;
            }
          }
        }
      }

      .right-list {
        margin-bottom: -10px;
        padding-left: 10px;
        width: 50%;

        .item {
          .df();

          margin-bottom: 10px;
          background-color: #fff;

          .pic {
            width: 25%;
            flex-shrink: 0;
          }

          .time {
            padding: 10px 0;
            width: 16.66666667%;
            flex-shrink: 0;

            .date {
              border-right: 2px solid #eee;
              text-align: center;
              height: 100%;

              .df(stretch, space-between, column);

              .day {
                font-size: 42px;
                line-height: 1.14;
                color: #6E6E6E;
                font-weight: bold;
              }

              .year-month {
                color: #888;
                font-size: 16px;
              }
            }
          }

          .tit-desc {
            padding: 15px 0 15px 15px;
            flex-grow: 1;
            .df(stretch, space-between, column);

            h2 {
              a {
                font-weight: 600;
                font-size: 18px;
                margin-bottom: 10px;
                color: #383838;
                font-weight: bold;
                transition: color .35s ease-in-out;
                .vertical(1);
              }
            }

            p {
              color: #888888;
              font-size: 14px;
              .vertical(1);
            }
          }

          &:hover {
            .tit-desc {
              h2 {
                a {
                  color: #ff9600;
                }
              }
            }
          }
        }
      }
    }
  }
}
