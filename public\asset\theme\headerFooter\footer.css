footer.footer {
  padding-top: 70px;
  background-color: #383838;
  width: 100%;
}
footer.footer .footer-con {
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
  max-width: 1500px;
}
footer.footer .footer-con .link-contact-qr {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
footer.footer .footer-con .link-contact-qr .qiuck-link {
  width: 60%;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list {
  flex-grow: 1;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list .qiuck-tit {
  margin-bottom: 20px;
  color: #fff;
  font-size: 16px;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list .qiuck-tit a {
  color: #fff;
  font-size: 16px;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list ul {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list ul li a {
  line-height: 2.28;
  color: #888;
  font-size: 14px;
  transition: color 0.35s ease-in-out;
}
footer.footer .footer-con .link-contact-qr .qiuck-link .link-list ul li a:hover {
  color: #fff;
}
footer.footer .footer-con .link-contact-qr .contact {
  flex-grow: 1;
}
footer.footer .footer-con .link-contact-qr .contact .item {
  margin-bottom: 15px;
  color: #888;
  line-height: 1.71;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
footer.footer .footer-con .link-contact-qr .contact .item:last-child {
  margin-bottom: 0;
}
footer.footer .footer-con .link-contact-qr .contact .item .iconfont {
  margin-right: 10px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  background-color: #ff9600;
  color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
footer.footer .footer-con .link-contact-qr .qr-code {
  margin-left: 20px;
  border-radius: 4px;
  padding: 10px;
  width: 10%;
  background: #484848;
}
footer.footer .footer-con .link-contact-qr .qr-code .text-box {
  color: #888888;
  line-height: 1.85;
  font-size: 14px;
  text-align: center;
}
footer.footer .footer-con .friendship-link .tit {
  border-bottom: 1px solid #404040;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
footer.footer .footer-con .friendship-link .tit a {
  border-bottom: 1px solid #FFB133;
  padding: 10px 0;
  color: #888;
  line-height: 1.25;
  font-size: 16px;
}
footer.footer .footer-con .friendship-link .link-list {
  padding: 25px 0;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
footer.footer .footer-con .friendship-link .link-list a {
  margin-right: 15px;
  line-height: 1.71;
  font-size: 14px;
  color: #6E6E6E;
  transition: color 0.35s ease-in-out;
}
footer.footer .footer-con .friendship-link .link-list a:hover {
  color: #fff;
}
footer.footer .footer-con .copyright-site {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #404040;
  padding: 20px 0 35px;
  line-height: 1.71;
}
footer.footer .footer-con .copyright-site .copyright {
  margin-left: 5px;
  color: #888;
  font-size: 14px;
}
footer.footer .footer-con .copyright-site .site a {
  color: #888;
  font-size: 14px;
  display: block;
  transition: color 0.35s ease-in-out;
}
footer.footer .footer-con .copyright-site .site a:hover {
  color: #fff;
}
