<template>
  <div class="dashboard-statistics">
    <!-- 统计卡片区域 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="6" :sm="6" :md="6" :lg="6" v-for="(item, index) in limitedInfoValue" :key="index">
          <div class="statistic-card" @click="handleCardClick(item)">
            <div class="card-header">
              <div class="card-title">
                <template v-if="index === 0">待審核文章</template>
                <template v-else-if="index === 1">待處理消息</template>
                <template v-else-if="index === 2">待審核頁面</template>
                <template v-else-if="index === 3">今日新增查詢</template>
                <template v-else>{{ item.title }}</template>
              </div>
            </div>
            <div class="card-content">
              <div class="icon-wrapper">
                <el-icon size="18" color="#007EE5">
                  <img v-if="index === 0" :src="$asset('Dashboard/Asset/ionic-ios-paper.png')"  class="icons-img" />
                  <img v-if="index === 1" :src="$asset('Dashboard/Asset/ionic-ios-notifications.png')"  class="icons-img" />
                  <img v-if="index === 2" :src="$asset('Dashboard/Asset/ionic-md-laptop.png')"  class="icons-img" />
                  <img v-if="index === 3" :src="$asset('Dashboard/Asset/open-shield.png')"  class="icons-img" />
                </el-icon>
              </div>
              <div class="number">
                <template v-if="index === 0">
                  <span class="value">5</span>
                  <span class="unit">篇</span>
                </template>
                <template v-else-if="index === 1">
                  <span class="value">4</span>
                  <span class="unit">條</span>
                </template>
                <template v-else-if="index === 2">
                  <span class="value">3</span>
                  <span class="unit">頁</span>
                </template>
                <template v-else-if="index === 3">
                  <span class="value">{{ securityDetectionCount }}</span>
                  <span class="unit">次</span>
                </template>
                <template v-else>
                  <span class="value">{{ item.count }}</span>
                  <span class="unit">{{ item.text.count_text }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'
import { Document, Bell, Monitor, Warning } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()

const loading = ref(true)
const infoValue = ref<any>([])

// 安全检测动态数据
const securityDetectionCount = ref(3)

let securityTimer: number | null = null

// 启动安全检测数据动态变化
const startSecurityAnimation = () => {
  securityTimer = setInterval(() => {
    // 生成1-5之间的随机数
    securityDetectionCount.value = Math.floor(Math.random() * 5) + 1
  }, 3000) // 3秒更换一次
}

// 清理定时器
const clearAllTimers = () => {
  if (securityTimer) {
    clearInterval(securityTimer)
    securityTimer = null
  }
}

const limitedInfoValue = computed(() => {
  return infoValue.value.slice(0, 4)
})

const getCardIcon = (index: number) => {
  const icons = {
    0: Document,
    1: Bell,
    2: Monitor,
    3: Warning,
  }
  return icons[index as keyof typeof icons]
}

const getDashboard = async () => {
  loading.value = true
  try {
    const res = await http.get('/dashboard/dashboard/data')
    console.log(res.data.data)
    infoValue.value = res.data.data.map((item: any, index: number) => {
      const icons = {
        0: Document,
        1: Bell,
        2: Monitor,
        3: Warning,
      }
      return {
        ...item,
        icon: icons[index as keyof typeof icons],
      }
    })
  } catch (err) {
    console.error('Failed to fetch dashboard data:', err)
  } finally {
    loading.value = false
  }
}

// 处理卡片点击事件
const handleCardClick = (item: any) => {
  const index = limitedInfoValue.value.indexOf(item)
  let route = ''
  
  switch (index) {
    case 0:
      route = '/cms/cmsList?model_id=1&active_tab=awaiting'  // 待審核文章
      break
    case 1:
      route = '/message/messageList'  // 待處理消息
      break
    case 2:
      route = '/cms/cmsList?model_id=5&active_tab=awaiting'  // 待審核頁面
      break
    case 3:
      route = '/auditlog/logs'  // 安全檢測
      break
    default:
      return
  }
  
  if (route) {
    router.push(route)
  }
}

onMounted(async () => {
  getDashboard()
  
  // 启动安全检测动态数据
  startSecurityAnimation()
})

onUnmounted(() => {
  clearAllTimers()
})
</script>

<script lang="ts">
export default {
  name: 'Dashboard',
}
</script>

<style lang="scss" scoped>
.dashboard-statistics {
  .stats-cards {
    width: 100%;
    background-color: #fff;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 10px;

    .statistic-card {
      padding: 20px 27px;
      cursor: pointer;
      height: 100%;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        top: 22px;
        right: 0;
        width: 1px;
        height: 77px;
        background-color: #707070;
        opacity: 0.16;
      }


      .card-header {
        margin-bottom: 14px;

        .card-title {
          font-size: 16px;
          color: #232323;
          font-weight: bold;
          line-height: 21px;
        }
      }

      .card-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .icon-wrapper {
          border-radius: 50%;
          width: 45px;
          height: 45px;
          background-color: #E9EEF2;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          .el-icon {
            font-size: 18px;
            color: #007EE5;
          }
          .icons-img {
            width: 100%;
            height: 100%;
          }
        }

        .number {
          display: flex;
          align-items: baseline;
          gap: 10px;

          .value {
            font-size: 37px;
            font-weight: bold;
            color: #007ee5;
            line-height: 1.21;
          }

          .unit {
            font-size: 16px;
            color: #000000;
            line-height: 1;
          }
        }
      }

    }
  }
}
</style>
