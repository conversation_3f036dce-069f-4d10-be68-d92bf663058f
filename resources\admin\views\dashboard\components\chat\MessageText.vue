<template>
  <div class="message-content">
    <!-- 进度条模式 -->
    <div v-if="hasProgressData" class="progress-container">
      <div class="progress-header">
        <span class="progress-title">
          {{ progressData.isSingleReplace ? '🔄 正在替换文章...' : '🔄 正在批量替换...' }}
        </span>
      </div>
      <div class="progress-bar-wrapper">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: progressData.percentage + '%' }"
            :class="{ 'progress-complete': !progressData.isActive }"
          ></div>
        </div>
      </div>
      <div class="progress-text">{{ progressData.text }}</div>
    </div>
    
    <!-- 普通文本模式 -->
    <div v-else class="message-body" v-html="formattedContent"></div>
    
    <div class="message-time">{{ message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TextMessage } from './types'

const props = defineProps<{
  message: TextMessage & { progressData?: any }
}>()

// 检查是否有进度数据
const hasProgressData = computed(() => {
  return !!(props.message as any).progressData
})

// 获取进度数据
const progressData = computed(() => {
  return (props.message as any).progressData || {}
})

// 格式化消息内容
const formattedContent = computed(() => {
  if (!props.message.content) return ''
  
  return props.message.content
    // 处理换行符
    .replace(/\n/g, '<br>')
    // 处理markdown样式的粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 移除可能导致emoji乱码的处理
    // .replace(/([🔧📊📝👋])\s*/g, '$1 ')
    // 确保每个功能项之间有适当的间距
    .replace(/<br><br>/g, '<br>')
})
</script>

<script lang="ts">
export default {
  name: 'MessageText'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 85%;
  width: 100%;
}

.message-body {
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.6;
  word-wrap: break-word;
  background: #e8f4ff;
  border-radius: 0 12px 12px 12px;
  
  // 优化文本格式
  :deep(strong) {
    font-weight: 600;
    color: #2c3e50;
  }
  
  :deep(br) {
    line-height: 1.8;
  }
  
  // 确保适当的段落间距
  :deep(p) {
    margin: 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  opacity: 0.7;
}

/* 进度条样式 */
.progress-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
  min-width: 280px;
}

.progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.progress-bar-wrapper {
  margin-bottom: 8px;
}

.progress-bar {
  width: 100%;
  height: 14px;
  background-color: #e9ecef;
  border-radius: 7px;
  overflow: hidden;
  border: 1px solid #dee2e6;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
  border-radius: 6px;
  transition: width 0.5s ease;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
    animation: progress-shine 1.5s ease-in-out infinite;
  }
  
  &.progress-complete::after {
    animation: none;
  }
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 13px;
  color: #6c757d;
  text-align: center;
  font-weight: 500;
}
</style> 