<template>
    <div class="dashboard-statistics">
    <!-- 安全防護工具 -->
    <div class="security-panel">
      <div class="traffic-monitor">
        <!-- 左侧内容区域 -->
        <div class="left-content">
          <!-- 請求總數模块 -->
          <div class="monitor-header">
            <div class="monitor-title">
              <span class="title-text">請求總數</span>
              <span class="ddos-indicator">
                <el-icon size="15">
                  <img :src="$asset('Dashboard/Asset/open-shield.png')" alt="" />
                </el-icon>
                DDoS攻擊
              </span>
            </div>
          </div>

          <!-- 主要数据显示 -->
          <div class="main-data-section">
            <div class="primary-stat">
              <span class="big-number">6.03M</span>
              <span class="trend-indicator">↑100%</span>
            </div>
          </div>

          <!-- 流量图表 -->
          <div class="traffic-chart">
            <div ref="chartContainer" class="chart-container"></div>
          </div>

          <!-- 次要统计项 -->
          <div class="secondary-stats">
            <div class="stat-item">
              <div class="stat-value">
                <div class="value">0</div>
                <div class="stat-label">惡意</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-value">
                <div class="value">0</div>
                <div class="stat-label">
                  <el-icon size="19" color="#9DA3AE">
                    <img :src="$asset('Dashboard/Asset/cloudflare.png')" alt="" />
                  </el-icon>
                  Cloudflare攔截
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧统计指标 -->
        <div class="right-content">
          <div class="stats-list">
            <div class="stat-box">
              <div class="stat-number">{{ dailyProtectionCount }}</div>
              <div class="stat-desc">今日防護次數</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">{{ scanFilesCount }}</div>
              <div class="stat-desc">正掃碼檔案</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">0</div>
              <div class="stat-desc">發現危險</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">{{ totalProtectionCount }}</div>
              <div class="stat-desc">總防護次數</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'
import { Document, Bell, Monitor, Warning, Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import useEventBus from '/admin/support/eventBus'
import { useDebounceFn } from '@vueuse/core'

const { t } = useI18n()
const router = useRouter()
const { $on, $off } = useEventBus()

const loading = ref(true)

// 定义emits
const emit = defineEmits(['close'])
const infoValue = ref<any>([])
const chartContainer = ref<HTMLElement>()

// 安全检测动态数据
const securityDetectionCount = ref(3)

// 底部统计指标数据
const dailyProtectionCount = ref(8) // 今日防護次數
const scanFilesCount = ref(6) // 正掃碼檔案
const totalProtectionCount = ref(1190) // 總防護次數

// 工具列表数据
const toolsList = ref<any>([])
const toolsLoading = ref(true)

let securityTimer: number | null = null
let scanFilesTimer: number | null = null
let totalProtectionTimer: number | null = null
let chartInstance: echarts.ECharts | null = null
let resizeHandler: (() => void) | null = null

// 处理标题文本
const processTitle = (title: string): string => {
  // 如果标题长度小于等于4个字符，添加换行
  if (title.length <= 4) {
    return title + '\n'
  }
  return title
}

// 获取工具列表数据
const getTools = async () => {
  toolsLoading.value = true
  try {
    const res = await http.get('/dashboard/toolsSection/data')
    // 处理数据，取前6个
    toolsList.value = res.data.data.slice(0, 6).map((tool: any) => ({
      ...tool,
      title: processTitle(tool.title),
      link: tool.path // 保持与原有代码兼容
    }))
  } catch (error) {
  } finally {
    toolsLoading.value = false
  }
}

// 启动安全检测数据动态变化
const startSecurityAnimation = () => {
  securityTimer = setInterval(() => {
    // 生成1-5之间的随机数
    securityDetectionCount.value = Math.floor(Math.random() * 5) + 1
  }, 3000) // 3秒更换一次
}

// 启动扫描文件数据动态变化（每2-3秒随机切换）
const startScanFilesAnimation = () => {
  // 保持固定值，不需要动态变化
  scanFilesTimer = setInterval(() => {
    scanFilesCount.value = Math.floor(Math.random() * 10) + 1
  }, 2500) // 2.5秒更换一次
}

// 启动总防护次数动态变化（每周3次，这里用20分钟模拟）
const startTotalProtectionAnimation = () => {
  // 保持固定值，不需要动态变化
  totalProtectionTimer = setInterval(() => {
    totalProtectionCount.value = Math.floor(Math.random() * 1000) + 1
  }, 1200000) // 20分钟更换一次（模拟一周3次）
}

// 检查是否需要更新今日防护次数（每天随机1次）
const checkDailyProtectionUpdate = () => {
  // 保持固定值，不需要动态变化
  const today = new Date().toDateString()
  const lastUpdateDate = localStorage.getItem('dailyProtectionUpdateDate')
  
  if (lastUpdateDate !== today) {
    // 新的一天，更新数据并记录日期
    dailyProtectionCount.value = Math.floor(Math.random() * 10) + 1
    localStorage.setItem('dailyProtectionUpdateDate', today)
    localStorage.setItem('dailyProtectionCount', dailyProtectionCount.value.toString())
  } else {
    // 同一天，从localStorage获取数据
    const savedCount = localStorage.getItem('dailyProtectionCount')
    if (savedCount) {
      dailyProtectionCount.value = parseInt(savedCount)
    }
  }
}

// 清理定时器
const clearAllTimers = () => {
  if (securityTimer) {
    clearInterval(securityTimer)
    securityTimer = null
  }
  if (scanFilesTimer) {
    clearInterval(scanFilesTimer)
    scanFilesTimer = null
  }
  if (totalProtectionTimer) {
    clearInterval(totalProtectionTimer)
    totalProtectionTimer = null
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value) return

  // 销毁旧的图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }

  // 等待DOM更新完成
  await nextTick()
  
  // 额外等待以确保容器尺寸已计算
  await new Promise(resolve => setTimeout(resolve, 100))
  
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    grid: {
      left: '-10%',
      right: '-10%',
      top: '0%',
      bottom: '0%',
      containLabel: true
    },
      xAxis: {
        type: 'category',
        data: ['', '', '', '', '', '', ''],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { 
          show: true,
          lineStyle: {
            color: '#E3E3E3',
            type: 'solid',
            width: 1
          }
        },
        splitNumber: 3,
        min: 0,
        max: 6
      },
      series: [
        {
          data: [1.2, 2.8, 1.5, 2.8, 3.2, 4.0, 5.2],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#007EE5',
            width: 2
          },
          itemStyle: {
            color: '#007EE5',
            borderWidth: 0
          },
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 126, 229, 0.2)' },
              { offset: 0.5, color: 'rgba(0, 126, 229, 0.1)' },
              { offset: 1, color: 'rgba(0, 126, 229, 0.05)' }
            ])
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 2,
              borderColor: '#007EE5',
              scale: 1.2
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const values = ['6.01M', '6.05M', '6.02M', '5.98M', '6.08M', '6.12M', '6.03M']
          return values[params[0].dataIndex]
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#007EE5',
        borderWidth: 1,
        textStyle: {
          color: '#fff',
          fontSize: 12
        }
      },
      animation: true,
      animationDuration: 1000
    }
    
    chartInstance.setOption(option)
    
    // 立即触发一次 resize
    chartInstance.resize()
  
}

// 处理图表重绘
const handleChartResize = async () => {
  if (!chartInstance) return
  
  await nextTick()
  // 额外等待以确保容器尺寸已计算
  await new Promise(resolve => setTimeout(resolve, 100))
  chartInstance?.resize()
}

// 使用防抖处理图表重绘
const debouncedResize = useDebounceFn(handleChartResize, 200)

// 重新初始化图表
const reinitChart = async () => {
  await initChart()
}

onMounted(async () => {
  // 启动所有动态数据更新 
  checkDailyProtectionUpdate()
  startSecurityAnimation()
  startScanFilesAnimation()
  startTotalProtectionAnimation()
  
  // 获取工具列表数据
  await getTools()
  
  // 初始化图表
  await initChart()

  // 添加窗口大小变化监听
  window.addEventListener('resize', debouncedResize)
  
  // 监听版本切换事件
  $on('version-changed', reinitChart)
  
  // 监听刷新事件
  $on('refresh-dashboard', reinitChart)
  
  // 监听售后服务切换事件
  $on('toggle-after-sales', handleChartResize)
  
  // 初始化完成后额外触发一次resize
  await handleChartResize()
})

onUnmounted(() => {
  clearAllTimers()
  
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  
  // 移除所有事件监听 
  window.removeEventListener('resize', debouncedResize)
  $off('version-changed', reinitChart)
  $off('refresh-dashboard', reinitChart)
  $off('toggle-after-sales', handleChartResize)
})
</script>

<script lang="ts">
export default {
  name: 'SafeTitle',
}
</script>

<style lang="scss" scoped>
.dashboard-statistics {
  width: 100%;
  height: 100%;
  
  .security-panel {
    height: 100%;
    
    .traffic-monitor {
      height: 328px;
      background-color: #fff;
      box-shadow: 0px 3px 6px #00000029;
      border-radius: 10px;
      display: flex;
      gap: 36px;
      
      // 左侧内容区域
      .left-content {
        flex: 1;
        padding: 27px 0 27px 33px;
        width: calc(100% - 328px);
        
        // 请求总数模块
        .monitor-header {
          margin-bottom: 5px;
          
          .monitor-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
            
            .title-text {
              font-size: 14px;
              font-weight: normal;
              color: #7E7E7E;
            }
            
            .ddos-indicator {
              display: flex;
              align-items: center;
              gap: 10px;
              font-size: 14px;
              color: #474747;
            }
          }
        }
        
        // 主要数据显示
        .main-data-section {
          margin-bottom: 20px;
          
          .primary-stat {
            .big-number {
              font-size: 30px;
              font-weight: bold;
              color: #007EE5;
              line-height: 34px;
              font-family: Arial;
            }
            
            .trend-indicator {
              font-size: 14px;
              font-weight: bold;
              color: #43956C;
              margin-left: 8px;
            }
          }
        }
        
        // 流量图表
        .traffic-chart {
          height: 142px;
          margin-bottom: 25px;
          
          .chart-container {
            width: 100%;
            height: 100%;
          }
        }
        
        // 次要统计项
        .secondary-stats {
          display: flex;
          justify-content: space-between;
          
          .stat-item {
            text-align: center;
            
            .stat-value {
              display: flex;
              gap: 6px;
              
              .value {
                font-size: 30px;
                font-weight: bold;
                color: #2A2A2A;
                line-height: 34px;
                font-family: Arial;
              }
              
              .stat-label {
                font-size: 16px;
                color: #9DA3AE;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                line-height: 21px;
              }
            }
          }
        }
      }
      
      // 右侧统计指标
      .right-content {
        padding: 27px 22px 27px 0;
        display: flex;
        flex-direction: column;
        
        .stats-list {
          display: flex;
          flex-direction: column;
          gap: 11px;
          width: 228px;
          
          .stat-box {
            height: 60px;
            text-align: center;
            border: 1px solid #E4E4E4;
            border-radius: 5px;
            display: flex;
            align-items: center;
            
            .stat-number {
              min-width: 114px;
              font-size: 30px;
              font-weight: bold;
              color: #2a2a2a;
              line-height: 34px;
              font-family: Arial;
              text-align: center;
            }
            
            .stat-desc {
              font-size: 14px;
              color: #000000;
              line-height: 19px;
              word-break: keep-all;
            }
          }
        }
      }
    }
  }
}
</style>
