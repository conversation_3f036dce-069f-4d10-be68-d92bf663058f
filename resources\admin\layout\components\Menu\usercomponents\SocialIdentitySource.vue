<template>
  <div class="bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box">
        <div class="identity-source">
          <el-table 
            :data="socialSources" 
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column prop="name" :label="t('dashboard.socialIdentity.table.account')" width="180">
              <template #default="scope">
                <div class="source-info">
                  <img :src="scope.row.logo" :alt="scope.row.name" class="source-icon" />
                  <span>{{ t(`dashboard.socialIdentity.sources.${scope.row.type}.name`) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="t('dashboard.socialIdentity.table.status')" width="180">
              <template #default="scope">
                <el-tag :type="hasUserInfo(scope.row) ? 'success' : 'info'">
                  {{ hasUserInfo(scope.row) 
                    ? t('dashboard.socialIdentity.table.statusTags.bound')
                    : t('dashboard.socialIdentity.table.statusTags.notBound') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('dashboard.socialIdentity.table.boundAccount')" width="180">
              <template #default="scope">
                <span v-if="hasUserInfo(scope.row)">
                  {{ scope.row.user_info[0].value }}
                </span>
                <span v-else>{{ t('dashboard.socialIdentity.table.statusTags.notBound') }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="t('dashboard.socialIdentity.table.actions')">
              <template #default="scope">
                <el-button 
                  :type="hasUserInfo(scope.row) ? 'danger' : 'primary'"
                  link
                  @click="toggleBinding(scope.row)"
                >
                  {{ hasUserInfo(scope.row) 
                    ? t('dashboard.socialIdentity.actions.unbind')
                    : t('dashboard.socialIdentity.actions.bind') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

const { t } = useI18n()

interface SocialSource {
  type: string
  name: string
  desc: string
  logo: string
  'bind-url': string
  status: number
  user_info: {
    name: string
    value: string
  }[] | null
}

const socialSources = ref<SocialSource[]>([])
const loading = ref(false)

// 添加判断用户信息是否存在的工具函数
const hasUserInfo = (source: SocialSource): boolean => {
  return !!source.user_info && Array.isArray(source.user_info) && source.user_info.length > 0
}

const getSocialSources = async () => {
  loading.value = true
  try {
    const response = await http.get('/iam/identity/bindlist?type=social')
    socialSources.value = (response.data.data || []).map((item: SocialSource) => ({
      ...item,
      user_info: Array.isArray(item.user_info) ? item.user_info : []
    }))
  } catch (error) {
    console.error('Failed to fetch social sources:', error)
    ElMessage.error(t('dashboard.socialIdentity.messages.fetchError'))
    // Mock data for demonstration
    socialSources.value = [
      {
        type: 'wechat',
        name: '微信',
        desc: '使用微信账号登录',
        logo: '/path/to/wechat-icon.png',
        'bind-url': 'https://example.com/bind/wechat',
        status: 1,
        user_info: [{ name: '微信号', value: '57' }],
      },
      {
        type: 'github',
        name: 'Github',
        desc: '使用Github账号登录',
        logo: '/path/to/github-icon.png',
        'bind-url': 'https://example.com/bind/github',
        status: 1,
        user_info: [],
      },
      {
        type: 'dingtalk',
        name: '钉钉',
        desc: '使用钉钉账号登录',
        logo: '/path/to/dingtalk-icon.png',
        'bind-url': 'https://example.com/bind/dingtalk',
        status: 1,
        user_info: [],
      },
    ]
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getSocialSources()
})

const toggleBinding = (source: SocialSource) => {
  if (hasUserInfo(source)) {
    // 解除绑定
    source.user_info = []
    ElMessage.success(t('dashboard.socialIdentity.messages.unbindSuccess', { name: source.name }))
  } else {
    // 绑定
    window.location.href = source['bind-url']
  }
}
</script>

<style lang="scss" scoped>
.identity-source {
  padding: 20px;
}

.source-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .source-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
  }

  span {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      margin: 0;
    }
  }
}
</style>
