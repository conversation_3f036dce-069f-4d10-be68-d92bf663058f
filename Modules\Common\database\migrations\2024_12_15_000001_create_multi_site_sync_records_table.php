<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('multi_site_sync_records', function (Blueprint $table) {
            $table->comment('通用多站点同步记录表');
            $table->bigIncrements('id');
            $table->string('module', 50)->comment('模块名称');
            $table->string('data_type', 50)->comment('数据类型');
            $table->unsignedInteger('source_site_id')->comment('源站点ID');
            $table->unsignedInteger('target_site_id')->comment('目标站点ID');
            $table->unsignedInteger('source_data_id')->comment('源数据ID');
            $table->unsignedInteger('target_data_id')->nullable()->comment('目标数据ID');
            $table->enum('sync_status', ['pending', 'syncing', 'success', 'failed', 'skipped'])->default('pending');
            $table->json('sync_languages')->nullable()->comment('同步的语言列表');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->unsignedInteger('retry_count')->default(0)->comment('重试次数');
            $table->unsignedInteger('last_sync_at')->default(0)->comment('最后同步时间');
            $table->unsignedInteger('created_at')->default(0);
            $table->unsignedInteger('updated_at')->default(0);
            
            $table->index(['module', 'data_type']);
            $table->index(['source_site_id', 'source_data_id']);
            $table->index(['sync_status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('multi_site_sync_records');
    }
}; 