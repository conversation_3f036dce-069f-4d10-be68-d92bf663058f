<template>
  <div class="table-page bwms-module">
    <!-- 头部 -->
    <div class="module-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </el-button>
      <el-button type="primary" @click="saveRegion" :loading="saving">
        <el-icon><Check /></el-icon>
        <span>保存</span>
      </el-button>
    </div>

    <!-- 主要内容 -->
    <div class="module-con">
      <div class="box">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="region-tabs">
          <el-tab-pane label="基礎設置" name="basic">
            <div class="tab-content">
              <el-form 
                :model="basicForm" 
                :rules="basicRules" 
                ref="basicFormRef" 
                label-position="top"
              >
                <el-form-item label="區域名稱" prop="name">
                  <el-input 
                    v-model="basicForm.name" 
                    placeholder="請輸入區域名稱"
                    style="width: 60%;"
                  />
                </el-form-item>
                <el-form-item label="區域描述" prop="description">
                  <el-input 
                    v-model="basicForm.description" 
                    placeholder="請輸入區域描述"
                    type="textarea"
                    :rows="4"
                    resize="vertical"
                    style="width: 60%;"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <el-tab-pane label="關聯頻道" name="channels">
            <div class="tab-content">
              <div class="section-header">
                <h3 class="section-title">關聯頻道列表</h3>
                <el-button type="primary" @click="openAssociateDialog">
                  <el-icon><Plus /></el-icon>
                  <span>關聯更多頻道</span>
                </el-button>
              </div>

              <el-table :data="associatedChannels" style="width: 100%" v-loading="loading">
                <template #empty>
                  <el-empty description="暫無關聯頻道" image-size="100px" />
                </template>
                <el-table-column prop="channelId" label="頻道ID" width="120" />
                <el-table-column prop="channelName" label="頻道名稱" min-width="180" />
                <el-table-column prop="channelType" label="頻道類型" width="120" />
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="scope">
                    <el-tag type="success" v-if="scope.row.status === '啟用'">啟用</el-tag>
                    <el-tag type="info" v-else>停用</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <div class="bwms-operate-btn-box">
                      <el-button class="bwms-operate-btn" @click="editChannel(scope.row)">
                        <el-icon>
                          <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                        </el-icon>
                      </el-button>
                      <el-button class="bwms-operate-btn" @click="removeChannel(scope.row)">
                        <el-icon>
                          <img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" />
                        </el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页器 -->
              <div class="box-footer">
                <div class="table-pagination-style">
                  <div class="pagination-left">
                    <span class="page-size-text">每頁顯示</span>
                    <el-select
                      v-model="channelLimit"
                      class="page-size-select"
                      @change="changeChannelPage"
                    >
                      <el-option
                        v-for="size in [10, 20, 50, 100]"
                        :key="size"
                        :label="size"
                        :value="size"
                        class="page-size-option"
                      />
                      <template #empty>
                        <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                          暫無數據
                        </div>
                      </template>
                    </el-select>
                    <span class="total-text">共 {{ channelTotal }} 條記錄</span>
                  </div>
                  <div class="pagination-right">
                    <el-pagination
                      v-model:current-page="channelPage"
                      background
                      layout="prev, pager, next"
                      :page-size="channelLimit"
                      :total="channelTotal"
                      @current-change="changeChannelPage"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

        <!-- 关联频道对话框 -->
    <el-dialog 
      v-model="associateDialogVisible" 
      title="關聯 Zone" 
      width="600"
      class="associate-dialog el-dialog-common-cls"
    >
      <div class="associate-content">
        <!-- 未关联区域 -->
        <div class="unassociated-section">
          <div class="tag-list">
            <el-tag 
              v-for="zone in unassociatedZones" 
              :key="zone.id"
              :class="['zone-tag', 'unassociated', { 'selected': isZoneSelected(zone) }]"
              @click="toggleZoneSelection(zone)"
            >
              {{ zone.name }}
            </el-tag>
          </div>
        </div>

        <!-- 已选中区域 -->
        <div class="selected-section" v-if="selectedZones.length > 0">
          <h4 class="section-subtitle">已選中 ({{ selectedCount }})</h4>
          <div class="tag-list">
            <el-tag 
              v-for="zone in selectedZones" 
              :key="zone.id"
              class="zone-tag selected"
              closable
              @close="removeSelectedZone(zone)"
            >
              {{ zone.name }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="associateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssociate">
          確定關聯
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import http from '/admin/support/http'

const router = useRouter()
const route = useRoute()

// 页面数据
const regionId = route.params.id as string
const activeTab = ref('basic')
const loading = ref(false)
const saving = ref(false)

// 区域数据
const regionData = reactive({
  id: '',
  name: '',
  description: ''
})

// 基础设置表单
const basicForm = reactive({
  name: '',
  description: ''
})

const basicFormRef = ref()

// 表单验证规则
const basicRules = {
  name: [
    { required: true, message: '請輸入區域名稱', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '請輸入區域描述', trigger: 'blur' }
  ]
}

// 关联频道数据
const associatedChannels = ref([
  {
    id: 1,
    channelId: 'CH001',
    channelName: '突發新聞直播',
    channelType: '新聞頻道',
    status: '啟用'
  },
  {
    id: 2,
    channelId: 'CH002',
    channelName: '突發事件報導',
    channelType: '新聞頻道',
    status: '啟用'
  },
  {
    id: 3,
    channelId: 'CH003',
    channelName: '突發新聞快報',
    channelType: '新聞頻道',
    status: '啟用'
  },
  {
    id: 4,
    channelId: 'CH004',
    channelName: '突發事件追蹤',
    channelType: '新聞頻道',
    status: '停用'
  },
  {
    id: 5,
    channelId: 'CH005',
    channelName: '突發新聞深度報導',
    channelType: '新聞頻道',
    status: '啟用'
  }
])

// 频道分页相关
const channelPage = ref(1)
const channelLimit = ref(10)
const channelTotal = ref(5)

// 关联对话框
const associateDialogVisible = ref(false)
const unassociatedZones = ref([
  { id: 1, name: '突發' },
  { id: 2, name: '港澳' },
  { id: 3, name: '國際' },
  { id: 4, name: '財經' },
  { id: 5, name: '體育' },
  { id: 6, name: '兩岸' },
  { id: 7, name: 'TVB News' }
])

// 已选中的区域
const selectedZones = ref<any[]>([])

// 计算选中数量
const selectedCount = computed(() => selectedZones.value.length)

// 获取区域详情
const getRegionDetail = async () => {
  loading.value = true
  try {
    const response = await http.get(`/admin/region/${regionId}`)
    if (response.data && response.data.code === 200) {
      const data = response.data.data
      regionData.id = data.id
      regionData.name = data.name
      regionData.description = data.description
      
      basicForm.name = data.name
      basicForm.description = data.description
    }
  } catch (error) {
    ElMessage.error('獲取區域詳情失敗')
  } finally {
    loading.value = false
  }
}

// 保存区域
const saveRegion = async () => {
  try {
    await basicFormRef.value.validate()
    
    saving.value = true
    const response = await http.put(`/admin/region/${regionId}`, {
      name: basicForm.name,
      description: basicForm.description
    })
    
    if (response.data && response.data.code === 200) {
      ElMessage.success('保存成功')
      regionData.name = basicForm.name
      regionData.description = basicForm.description
    } else {
      ElMessage.error(response.data?.message || '保存失敗')
    }
  } catch (error) {
    if (error === false) {
      ElMessage.error('請檢查表單輸入')
    } else {
      ElMessage.error('保存失敗')
    }
  } finally {
    saving.value = false
  }
}

// 打开关联对话框
const openAssociateDialog = () => {
  associateDialogVisible.value = true
}

// 切换区域选中状态
const toggleZoneSelection = (zone: any) => {
  const isSelected = isZoneSelected(zone)
  if (isSelected) {
    // 如果已选中，则移除
    removeSelectedZone(zone)
  } else {
    // 如果未选中，则添加
    selectedZones.value.push(zone)
  }
}

// 检查区域是否已选中
const isZoneSelected = (zone: any) => {
  return selectedZones.value.some((z: any) => z.id === zone.id)
}

// 移除选中的区域
const removeSelectedZone = (zone: any) => {
  const index = selectedZones.value.findIndex((z: any) => z.id === zone.id)
  if (index > -1) {
    selectedZones.value.splice(index, 1)
  }
}

// 确认关联
const confirmAssociate = () => {
  ElMessage.success('關聯成功')
  associateDialogVisible.value = false
  selectedZones.value = []
}

// 编辑频道
const editChannel = (channel: any) => {
  ElMessage.info('編輯頻道功能')
}

// 移除频道
const removeChannel = (channel: any) => {
  ElMessageBox.confirm(
    `確定要移除頻道「${channel.channelName}」嗎？`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = associatedChannels.value.findIndex(c => c.id === channel.id)
    if (index > -1) {
      associatedChannels.value.splice(index, 1)
      channelTotal.value--
      ElMessage.success('移除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消移除')
  })
}

// 获取关联频道列表
const getAssociatedChannels = async () => {
  loading.value = true
  try {
    const params: any = {
      page: channelPage.value,
      limit: channelLimit.value,
      regionId: regionId
    }

    const response = await http.get('/admin/region/channels', params)
    
    if (response.data && response.data.code === 200) {
      associatedChannels.value = response.data.data.items || []
      channelTotal.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data?.message || '獲取頻道數據失敗')
      associatedChannels.value = []
      channelTotal.value = 0
    }
  } catch (error) {
    associatedChannels.value = []
    channelTotal.value = 0
  } finally {
    loading.value = false
  }
}

// 频道分页处理
const changeChannelPage = () => {
  getAssociatedChannels()
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(() => {
  getRegionDetail()
  // getAssociatedChannels()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 0;
    }
  }
}

.region-tabs {
  .tab-content {
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.associate-dialog {
  .unassociated-section,
  .selected-section {
    margin-bottom: 20px;
    
    .section-subtitle {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin: 0 0 12px 0;
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
  
  .zone-tag {
    cursor: pointer;
    transition: all 0.3s;
    
    &.unassociated {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
      
      &:hover {
        background: #d9f7be;
      }
      
      &.selected {
        background: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        
        &:hover {
          background: #bae7ff;
        }
      }
    }
    
    &.selected {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
      
      &:hover {
        background: #bae7ff;
      }
    }
  }
}

</style>
