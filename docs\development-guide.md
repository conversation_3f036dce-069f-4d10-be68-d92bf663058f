# 开发与部署指南

## 目录
- [开发环境](#开发环境)
- [开发流程](#开发流程)
- [调试工具](#调试工具)
- [测试规范](#测试规范)
- [发布流程](#发布流程)
- [部署环境配置](#部署环境配置)
- [Swoole 安装与配置](#swoole-安装与配置)
- [PHP 配置调整](#php-配置调整)
- [Laravel Octane 配置](#laravel-octane-配置)
- [Supervisor 配置](#supervisor-配置)
- [Nginx 配置](#nginx-配置)
- [常见问题排查](#常见问题排查)

## 开发环境

### 1. 环境要求

- PHP 8.2+
- MySQL 8.0+
- Redis 6.0+
- Node.js 16+
- Composer 2.0+
- Git

### 2. 开发工具

```bash
# IDE 推荐
- PHPStorm 或 VSCode
- DataGrip（数据库管理）
- Postman（API 测试）
- Redis Desktop Manager（缓存管理）

# VSCode 插件
- PHP Intelephense
- Laravel Blade Snippets
- Laravel Extra Intellisense
- Laravel Goto View
- PHP Debug
- Git Lens
```

### 3. 环境配置

```bash
# 1. 克隆项目
<NAME_EMAIL>:your-org/bwms.git

# 2. 安装依赖
composer install
npm install

# 3. 环境配置
cp .env.example .env
php artisan key:generate

# 4. 数据库迁移
php artisan migrate

# 5. 启动服务
php artisan serve
npm run dev
```

## 开发流程

### 1. 分支管理

```bash
# 分支命名规范
feature/module-name/feature-name  # 功能分支
bugfix/module-name/bug-name      # 修复分支
hotfix/module-name/issue-name    # 紧急修复
release/v1.x.x                   # 发布分支

# 示例
git checkout -b feature/course/add-quiz-function
```

### 2. 提交规范

```bash
# 提交信息格式
<type>(<scope>): <subject>

# type 类型
feat: 新功能
fix: 修复
docs: 文档
style: 格式
refactor: 重构
test: 测试
chore: 构建

# 示例
feat(course): 添加课程测验功能
fix(auth): 修复登录验证失败问题
```

### 3. 代码审查

```markdown
## 代码审查清单

1. 代码规范
   - 遵循 PSR-12 规范
   - 使用类型声明
   - 适当的注释

2. 业务逻辑
   - 业务逻辑正确性
   - 异常处理完整性
   - 数据验证充分性

3. 性能考虑
   - 查询优化
   - 缓存使用
   - N+1 问题
```

## 调试工具

### 1. Laravel Telescope

```php
// 配置 Telescope
'enabled' => env('TELESCOPE_ENABLED', true),

// 监控内容
- 请求/响应
- 数据库查询
- 缓存操作
- 队列任务
- 调度任务
- 异常错误
```

### 2. Laravel Debug Bar

```php
// 安装
composer require barryvdh/laravel-debugbar --dev

// 配置项
'enabled' => env('DEBUGBAR_ENABLED', true),

// 监控内容
- 路由信息
- 控制器信息
- 视图数据
- 查询构建器
```

### 3. Xdebug 配置

```ini
; php.ini 配置
[xdebug]
xdebug.mode = debug
xdebug.start_with_request = yes
xdebug.client_port = 9003
xdebug.client_host = "127.0.0.1"
```

## 测试规范

### 1. 单元测试

```php
namespace Tests\Unit\Course;

class CourseTest extends TestCase
{
    /** @test */
    public function it_can_create_a_course()
    {
        // 准备测试数据
        $courseData = [
            'title' => '测试课程',
            'description' => '课程描述'
        ];

        // 执行测试
        $course = $this->courseService->create($courseData);

        // 断言结果
        $this->assertEquals($courseData['title'], $course->title);
        $this->assertDatabaseHas('courses', $courseData);
    }
}
```

### 2. 功能测试

```php
namespace Tests\Feature\Course;

class CourseApiTest extends TestCase
{
    /** @test */
    public function it_can_list_courses()
    {
        // 创建测试数据
        Course::factory()->count(3)->create();

        // 发送请求
        $response = $this->getJson('/api/courses');

        // 断言响应
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'title', 'description']
                ]
            ]);
    }
}
```

### 3. 测试数据工厂

```php
namespace Database\Factories;

class CourseFactory extends Factory
{
    protected $model = Course::class;

    public function definition()
    {
        return [
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'duration' => $this->faker->numberBetween(30, 180),
            'status' => 1
        ];
    }
}
```

## 发布流程

### 1. 版本管理

```bash
# 版本号规范
主版本号.次版本号.修订号
1.0.0

# 版本说明
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正
```

### 2. 发布检查清单

```markdown
## 发布前检查

1. 代码审查
   - 完成代码评审
   - 解决所有评审意见

2. 测试验证
   - 单元测试通过
   - 功能测试通过
   - 集成测试通过

3. 文档更新
   - API 文档更新
   - 更新日志完善
   - 使用文档更新

4. 数据处理
   - 数据库迁移脚本
   - 数据备份方案
   - 回滚方案
```

### 3. 发布步骤

```bash
# 1. 创建发布分支
git checkout -b release/v1.0.0

# 2. 版本更新
composer version 1.0.0

# 3. 更新文档
- 更新 CHANGELOG.md
- 更新版本号
- 更新 API 文档

# 4. 合并分支
git checkout main
git merge release/v1.0.0

# 5. 打标签
git tag -a v1.0.0 -m "Release version 1.0.0"

# 6. 部署生产
- 执行部署脚本
- 执行数据库迁移
- 清理缓存
```

## Laravel Octane

Laravel Octane 通过高性能应用服务器（如 Swoole 或 RoadRunner）来提升 Laravel 应用的性能。它保持应用程序在内存中常驻，避免了每个请求都需要重新引导框架的开销。

### 1. 安装 Laravel Octane

```bash
# 通过 Composer 安装 Octane
composer require laravel/octane

# 安装 Swoole
pecl install swoole

# 或者安装 RoadRunner (二选一)
composer require spiral/roadrunner
```

### 2. 配置 Octane

```bash
# 发布 Octane 配置文件
php artisan octane:install

# 编辑配置文件
vim config/octane.php
```

主要配置项：

```php
// config/octane.php
return [
    // 服务器类型: 'swoole' 或 'roadrunner'
    'server' => env('OCTANE_SERVER', 'swoole'),

    // 监听主机和端口
    'host' => '0.0.0.0',
    'port' => 8000,

    // 工作进程数量
    'workers' => env('OCTANE_WORKERS', swoole_cpu_num() * 2),

    // 任务工作进程数量
    'task_workers' => env('OCTANE_TASK_WORKERS', swoole_cpu_num() * 2),

    // 最大请求数
    'max_requests' => 500,
];
```

### 3. 启动 Octane 服务

```bash
# 启动 Octane 服务
php artisan octane:start

# 以监视模式启动（开发环境）
php artisan octane:start --watch

# 指定端口启动
php artisan octane:start --port=8000

# 停止 Octane 服务
php artisan octane:stop
```

### 4. Octane 性能优化

1. **预加载服务**

   在 `config/octane.php` 中配置预加载的服务：

   ```php
   'listeners' => [
       WorkerStarting::class => [
           PreloadApplicationServices::class,
       ],
   ],
   ```

2. **对象重置**

   确保在请求之间正确重置服务状态：

   ```php
   'warm' => [
       ...
   ],
   'reset' => [
       ...
   ],
   ```

3. **并发任务处理**

   使用 Octane 的并发特性：

   ```php
   use Laravel\Octane\Facades\Octane;

   [$userData, $postData] = Octane::concurrently([
       fn () => User::findOrFail($id),
       fn () => Post::where('user_id', $id)->get(),
   ]);
   ```

## Supervisor 配置

Supervisor 是一个进程控制系统，用于监控和控制多个进程。在我们的项目中，我们使用 Supervisor 来确保 Laravel Octane 服务持续运行。

### 1. 安装 Supervisor

```bash
# Ubuntu/Debian
sudo apt-get install supervisor

# CentOS/RHEL
sudo yum install supervisor

# 启动 Supervisor
sudo systemctl enable supervisor
sudo systemctl start supervisor
```

### 2. 配置文件

创建一个新的配置文件：

```bash
# 宝塔面板环境
sudo vim /www/server/panel/plugin/supervisor/conf/dev_bmws.conf

# 标准 Linux 环境
sudo vim /etc/supervisor/conf.d/dev_bmws.conf
```

添加以下配置内容：

```ini
[program:dev_bmws]
command=php /www/wwwroot/dev_bwms/artisan octane:start --watch --port=8000
autorestart=true
startsecs=3
startretries=3
stdout_logfile=/www/server/panel/plugin/supervisor/log/dev_bmws.out.log
stderr_logfile=/www/server/panel/plugin/supervisor/log/dev_bmws.err.log
stdout_logfile_maxbytes=2MB
stderr_logfile_maxbytes=2MB
user=www
priority=999
numprocs=1
process_name=%(program_name)s_%(process_num)02d
```

### 3. 配置说明

| 参数 | 说明 |
|------|------|
| `program` | 程序名称，用于标识进程 |
| `command` | 要执行的命令，这里是启动 Laravel Octane 服务 |
| `autorestart` | 当进程退出时是否自动重启 |
| `startsecs` | 进程启动多少秒后视为成功运行 |
| `startretries` | 启动失败时的重试次数 |
| `stdout_logfile` | 标准输出日志文件路径 |
| `stderr_logfile` | 错误输出日志文件路径 |
| `stdout_logfile_maxbytes` | 标准输出日志文件最大大小 |
| `stderr_logfile_maxbytes` | 错误输出日志文件最大大小 |
| `user` | 运行进程的用户 |
| `priority` | 进程优先级 |
| `numprocs` | 启动进程的数量 |
| `process_name` | 进程名称格式 |

### 4. 管理 Supervisor

```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动程序
sudo supervisorctl start dev_bmws:*

# 停止程序
sudo supervisorctl stop dev_bmws:*

# 重启程序
sudo supervisorctl restart dev_bmws:*

# 查看状态
sudo supervisorctl status
```

### 5. 常见问题

#### 5.1 权限问题

如果遇到权限问题，确保 Supervisor 配置中指定的用户（如 `www`）对项目目录和日志目录有适当的权限：

```bash
sudo chown -R www:www /www/wwwroot/dev_bwms
sudo chown -R www:www /www/server/panel/plugin/supervisor/log
```

#### 5.2 日志查看

查看 Supervisor 管理的进程日志：

```bash
# 查看标准输出日志
tail -f /www/server/panel/plugin/supervisor/log/dev_bmws.out.log

# 查看错误日志
tail -f /www/server/panel/plugin/supervisor/log/dev_bmws.err.log
```

## 部署环境配置

### 1. PHP 扩展要求

项目依赖以下 PHP 扩展：

- ext-bcmath
- ext-ctype
- ext-curl
- ext-dom
- ext-fileinfo
- ext-json
- ext-mbstring
- ext-openssl
- ext-pcntl
- ext-pdo
- ext-tokenizer
- ext-xml
- ext-zip
- ext-exif
- ext-intl
- ext-ftp
- ext-swoole (4.8.0+)

### 2. 系统依赖包安装

```bash
# Debian/Ubuntu
apt-get update
apt-get install -y \
    zip \
    unzip \
    git \
    libzip-dev \
    libpng-dev \
    iputils-ping \
    libjpeg-dev \
    libpq-dev \
    net-tools \
    libfreetype6-dev \
    libxml2-dev \
    libicu-dev \
    pkg-config

# CentOS/RHEL
yum install -y \
    zip \
    unzip \
    git \
    libzip-devel \
    libpng-devel \
    iputils \
    libjpeg-devel \
    libpq-devel \
    net-tools \
    freetype-devel \
    libxml2-devel \
    libicu-devel \
    pkgconfig
```

### 3. PHP 扩展安装

```bash
# 安装 PHP 扩展
docker-php-ext-configure gd --with-freetype --with-jpeg
docker-php-ext-install gd
docker-php-ext-configure intl
docker-php-ext-install intl
docker-php-ext-install zip pdo pdo_mysql exif ftp pcntl
```

## Swoole 安装与配置

### 1. 安装 Swoole 依赖

```bash
# 安装 Swoole 依赖
apt-get install -y \
    libcurl4-openssl-dev \
    libssl-dev \
    libnghttp2-dev
```

### 2. 安装 Swoole

```bash
# 通过 PECL 安装 Swoole
pecl install swoole

# 启用 Swoole 扩展
echo "extension=swoole.so" > /usr/local/etc/php/conf.d/swoole.ini
```

### 3. 验证 Swoole 安装

```bash
# 检查 Swoole 是否正确安装
php -m | grep swoole

# 查看 Swoole 版本
php --ri swoole
```

## PHP 配置调整

### 1. 禁用 pcntl_* 函数限制

在某些共享主机或受限环境中，`pcntl_*` 函数可能被禁用，这会影响 Swoole 的正常运行。需要修改 PHP 配置以启用这些函数。

#### 检查 pcntl_* 函数是否被禁用

```bash
# 查看禁用的函数列表
php -r "echo ini_get('disable_functions');"
```

#### 修改 PHP.ini 配置

1. 找到 PHP.ini 文件位置：

```bash
php -i | grep "Loaded Configuration File"
```

2. 编辑 PHP.ini 文件，找到 `disable_functions` 行，移除所有 `pcntl_*` 相关函数：

```ini
; 原始配置可能类似这样
disable_functions = pcntl_alarm,pcntl_fork,pcntl_waitpid,pcntl_wait,pcntl_wifexited,pcntl_wifstopped,pcntl_wifsignaled,pcntl_wifcontinued,pcntl_wexitstatus,pcntl_wtermsig,pcntl_wstopsig,pcntl_signal,pcntl_signal_get_handler,pcntl_signal_dispatch,pcntl_get_last_error,pcntl_strerror,pcntl_sigprocmask,pcntl_sigwaitinfo,pcntl_sigtimedwait,pcntl_exec,pcntl_getpriority,pcntl_setpriority,pcntl_async_signals

; 修改后的配置（移除所有 pcntl_* 函数）
disable_functions = [其他非 pcntl_ 开头的函数]
```

3. 如果无法直接修改主 PHP.ini 文件，可以创建一个自定义配置文件：

```bash
# 在 Docker 环境中
echo "disable_functions = " > /usr/local/etc/php/conf.d/enable-pcntl.ini

# 在共享主机环境中（如果支持）
echo "disable_functions = " > ~/public_html/php.ini
```

### 2. 其他重要的 PHP 配置

```ini
; 增加内存限制
memory_limit = 512M

; 增加最大执行时间
max_execution_time = 300

; 增加上传文件大小限制
upload_max_filesize = 20M
post_max_size = 20M

; 优化 OPcache 设置
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.validate_timestamps=0
opcache.save_comments=1
opcache.fast_shutdown=0
```

## Nginx 配置

### 1. 创建 Nginx 配置文件

```bash
# 宝塔面板环境
sudo vim /www/server/panel/vhost/nginx/bwms.conf

# 标准 Linux 环境
sudo vim /etc/nginx/sites-available/bwms.conf
```

### 2. 配置内容

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/bwms/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ @octane;
    }

    location @octane {
        proxy_http_version 1.1;
        proxy_set_header Host $http_host;
        proxy_set_header Scheme $scheme;
        proxy_set_header SERVER_PORT $server_port;
        proxy_set_header REMOTE_ADDR $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';

        proxy_pass http://127.0.0.1:8000;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 3. 启用配置

```bash
# 标准 Linux 环境
sudo ln -s /etc/nginx/sites-available/bwms.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 常见问题排查

### 1. Swoole 安装问题

**问题**: 安装 Swoole 时出现编译错误。

**解决方案**:
```bash
# 确保安装所有依赖
apt-get install -y libcurl4-openssl-dev libssl-dev libnghttp2-dev

# 清除之前的安装
pecl uninstall swoole

# 重新安装指定版本
pecl install swoole-4.8.12
```

### 2. pcntl 函数被禁用

**问题**: 启动 Octane 时报错，提示 pcntl 函数被禁用。

**解决方案**:
```bash
# 创建自定义 PHP 配置文件
echo "disable_functions = " > /usr/local/etc/php/conf.d/enable-pcntl.ini

# 重启 PHP-FPM 或 Web 服务器
systemctl restart php8.2-fpm
```

### 3. Octane 无法启动

**问题**: 尝试启动 Octane 时出现错误。

**解决方案**:
```bash
# 检查 Swoole 是否正确安装
php -m | grep swoole

# 检查 .env 配置
cat .env | grep OCTANE

# 尝试使用调试模式启动
php artisan octane:start --server=swoole --host=0.0.0.0 --port=8000 --workers=1
```

### 4. 文件权限问题

**问题**: 应用无法写入日志或缓存文件。

**解决方案**:
```bash
# 设置正确的权限
chmod -R 777 /www/wwwroot/bwms/storage
chmod -R 777 /www/wwwroot/bwms/bootstrap/cache

# 确保用户所有权正确
chown -R www:www /www/wwwroot/bwms
```
