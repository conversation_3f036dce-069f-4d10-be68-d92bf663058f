import { defineStore } from 'pinia'
import { Permission } from '/admin/types/Permission'
import { MenuType } from '/admin/enum/app'
import { Menu } from '/admin/types/Menu'
import { constantRoutes } from '/admin/router'
import { RouteRecordRaw } from 'vue-router'
import { toRaw } from 'vue'
import { getModuleViewComponents } from '/admin/router/constantRoutes'

// 权限存储接口定义
interface Permissions {
  menus: Menu[] // 所有菜单（包括静态和动态）
  asyncMenus: Menu[] // 动态菜单
  permissions: Record<string, Permission> // 权限映射
  menuPathMap: Map<string, string> // 菜单路径映射
}

export const usePermissionsStore = defineStore('PermissionsStore', {
  // 状态定义
  state: (): Permissions => {
    return {
      menus: [],
      asyncMenus: [],
      permissions: {},
      menuPathMap: new Map(),
    }
  },

  // getter 定义
  getters: {
    // 获取所有菜单
    getMenus(): Menu[] {
      return this.menus
    },

    // 获取动态菜单
    getAsyncMenus(): Menu[] {
      return this.asyncMenus
    },

    // 获取所有权限
    getPermissions(): Record<string, Permission> {
      return this.permissions
    },

    // 获取菜单路径映射
    getMenuPathMap(): Map<string, string> {
      return this.menuPathMap
    },
  },

  // actions 定义
  actions: {
    /**
     * 生成动态菜单
     * @param permissions 权限列表
     * @param force 是否强制重新生成
     * @returns 动态菜单列表
     */
    getAsyncMenusFrom(permissions: Record<string, Permission>, force: boolean = false): Menu[] {
      // 如果非强制获取并且已有动态菜单，直接返回
      if (!force && this.asyncMenus.length > 0) {
        return this.asyncMenus
      }

      const menus: Permission[] = []

      // 遍历权限，筛选出菜单类型的权限
      Object.values(permissions).forEach(permission => {
        if (permission.type === MenuType.PAGE_TYPE || permission.type === MenuType.TOP_TYPE) {
          menus.push(permission)
        }

        // 设置菜单路径映射
        this.menuPathMap.set(permission.route, permission.permission_name)
      })

      // 生成并设置动态菜单
      this.setAsyncMenus(this.getAsnycMenus(menus, '0', '', getModuleViewComponents()))

      return this.asyncMenus
    },

    /**
     * 获取所有菜单
     * @param permissions 权限列表
     * @param force 是否强制重新生成
     * @returns 所有菜单列表
     */
    getMenusFrom(permissions: Record<string, Permission>, force: boolean = false): Menu[] {
      // 如果非强制获取并且已有菜单，直接返回
      if (!force && this.menus.length > 0) {
        return this.menus
      }
      const asyncMenus = this.getAsyncMenusFrom(permissions, force)

      // 设置所有菜单（静态 + 动态）
      this.setMenus(toRaw(asyncMenus))

      return this.menus
    },

    /**
     * 设置所有菜单
     * @param menus 菜单列表
     */
    setMenus(menus: Menu[]) {
      this.menus = this.transformRoutesToMenus(constantRoutes).concat(menus)
    },

    /**
     * 设置动态菜单
     * @param menus 动态菜单列表
     */
    setAsyncMenus(menus: Menu[]) {
      this.asyncMenus = menus
    },

    /**
     * 生成动态菜单
     * @param permissions 权限列表
     * @param parentId 父级ID
     * @param path 当前路径
     * @param viewComponents 视图组件映射
     * @returns 动态菜单列表
     */
    getAsnycMenus(permissions: Permission[], parentId: string = '0', path: string = '', viewComponents: any): Menu[] {
      const menus: Menu[] = []
      permissions.forEach(permission => {
        if (permission.parent_id === parentId) {
          // 确定组件
          let importComponent
          if (permission.component === '/admin/views/AMisPage.vue') {
            importComponent = () => import('/admin/views/AMisPage.vue')
          } else if (permission.component === '/admin/views/SettingAMisPage.vue') {
            importComponent = () => import('/admin/views/SettingAMisPage.vue')
          } else if (permission.component === '/admin/views/LaravelPage.vue') {
            importComponent = () => import('/admin/views/LaravelPage.vue')
          } else if (permission.type === MenuType.TOP_TYPE) {
            importComponent = () => import('/admin/layout/index.vue')
          } else {
            importComponent = viewComponents['/modules' + permission.component]
          }

          // 构建菜单项
          const menu: Menu = Object.assign({
            path: this.resolveRoutePathRoutePath(permission.route, path),
            name: permission.module + '_' + permission.permission_mark + permission.route.replace('/', '_'),
            component: importComponent,
            // redirect: permission.redirect,
            meta: Object.assign({
              title: permission.permission_name,
              icon: permission.icon,
              // hidden: permission.hidden,
              // active_menu: permission.active_menu,
            }),
          })

          // 递归处理子菜单
          const children = this.getAsnycMenus(permissions, permission.permission_mark, menu.path, viewComponents)
          if (children.length > 0) {
            menu.children = children
          }
          menus.push(menu)
        }
      })

      return menus
    },

    /**
     * 将路由转换为菜单
     * @param routes 路由列表
     * @param path 当前路径
     * @returns 菜单列表
     */
    transformRoutesToMenus(routes: Menu[] | Array<RouteRecordRaw>, path: string = ''): Menu[] {
      const menus: Menu[] = []

      routes.forEach(route => {
        if (route.meta?.hidden) {
          return false
        }

        const menu: Menu = Object.assign({
          path: this.resolveRoutePathRoutePath(route.path, path),
          name: route.name,
          meta: route.meta,
          component: route.component,
        })

        if (route.children?.length) {
          menu.children = this.transformRoutesToMenus(route.children, menu.path)
        }

        menus.push(menu)
      })
      return menus
    },

    /**
     * 解析路由路径
     * @param route 路由
     * @param path 当前路径
     * @returns 解析后的路径
     */
    resolveRoutePathRoutePath(route: string, path: string): string {
      if (path.length) {
        return (path + (!route.startsWith('/') ? '/' : '') + route).replace(/\/$/g, '')
      }
      // 去除尾部的 /
      return route.replace(/\/$/g, '')
    },
  },
})
