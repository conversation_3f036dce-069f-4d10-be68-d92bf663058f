# 服务层最佳实践

## 概述

本文档提供了服务层开发的最佳实践指南，包括设计原则、代码组织、错误处理等方面的建议。

## 设计原则

### 1. 单一职责原则

```php
// 好的实践 - 单一职责
class OrderService
{
    public function createOrder(array $data): Order
    {
        // 只处理订单创建相关的逻辑
    }
}

class PaymentService
{
    public function processPayment(Order $order): Payment
    {
        // 只处理支付相关的逻辑
    }
}

// 不好的实践 - 职责混杂
class OrderService
{
    public function createOrder(array $data): Order
    {
        // 处理订单创建
        // 处理支付
        // 处理物流
        // 处理通知
    }
}
```

### 2. 依赖注入

```php
// 好的实践 - 依赖注入
class OrderService
{
    private OrderRepository $repository;
    private PaymentService $paymentService;
    private LogisticsService $logisticsService;

    public function __construct(
        OrderRepository $repository,
        PaymentService $paymentService,
        LogisticsService $logisticsService
    ) {
        $this->repository = $repository;
        $this->paymentService = $paymentService;
        $this->logisticsService = $logisticsService;
    }
}

// 不好的实践 - 直接实例化
class OrderService
{
    public function createOrder(array $data): Order
    {
        $repository = new OrderRepository();
        $paymentService = new PaymentService();
        $logisticsService = new LogisticsService();
    }
}
```

### 3. 接口分离

```php
// 好的实践 - 接口分离
interface OrderServiceInterface
{
    public function createOrder(array $data): Order;
    public function cancelOrder(int $id): bool;
    public function completeOrder(int $id): bool;
}

class OrderService implements OrderServiceInterface
{
    public function createOrder(array $data): Order
    {
        // 实现创建订单
    }

    public function cancelOrder(int $id): bool
    {
        // 实现取消订单
    }

    public function completeOrder(int $id): bool
    {
        // 实现完成订单
    }
}

// 不好的实践 - 接口过于庞大
interface OrderServiceInterface
{
    public function createOrder(array $data): Order;
    public function processPayment(Order $order): Payment;
    public function createShipment(Order $order): Shipment;
    public function sendNotification(Order $order): void;
}
```

## 代码组织

### 1. 事务处理

```php
// 好的实践 - 统一的事务处理
class OrderService
{
    public function createOrder(array $data): Order
    {
        try {
            DB::beginTransaction();
            
            // 创建订单
            $order = $this->repository->create($data);
            
            // 处理订单项
            $this->processOrderItems($order, $data['items']);
            
            // 更新库存
            $this->updateInventory($order);
            
            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            BizException::throws(OrderErrorCode::CREATE_FAILED, '创建订单失败：' . $e->getMessage());
        }
    }
}

// 不好的实践 - 分散的事务处理
class OrderService
{
    public function createOrder(array $data): Order
    {
        DB::beginTransaction();
        $order = $this->repository->create($data);
        DB::commit();

        DB::beginTransaction();
        $this->processOrderItems($order, $data['items']);
        DB::commit();

        DB::beginTransaction();
        $this->updateInventory($order);
        DB::commit();

        return $order;
    }
}
```

### 2. 错误处理

```php
// 好的实践 - 统一的错误处理
class OrderService
{
    public function createOrder(array $data): Order
    {
        try {
            // 业务逻辑
        } catch (ValidationException $e) {
            Log::warning('Order validation failed', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            BizException::throws(OrderErrorCode::VALIDATION_FAILED, '订单数据验证失败：' . $e->getMessage());
        } catch (InventoryException $e) {
            Log::error('Inventory update failed', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            BizException::throws(OrderErrorCode::STOCK_UPDATE_FAILED, '库存更新失败：' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Order creation failed', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            BizException::throws(OrderErrorCode::CREATE_FAILED, '创建订单失败：' . $e->getMessage());
        }
    }
}

// 不好的实践 - 不统一的错误处理
class OrderService
{
    public function createOrder(array $data): Order
    {
        if (!$this->validate($data)) {
            return null;
        }

        try {
            return $this->repository->create($data);
        } catch (\Exception $e) {
            return null;
        }
    }
}
```

### 3. 日志记录

```php
// 好的实践 - 结构化日志
class OrderService
{
    public function createOrder(array $data): Order
    {
        try {
            Log::info('Creating order', ['data' => $data]);
            
            $order = $this->repository->create($data);
            
            Log::info('Order created', [
                'order_id' => $order->id,
                'total_amount' => $order->total_amount
            ]);
            
            return $order;
        } catch (\Exception $e) {
            Log::error('Order creation failed', [
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}

// 不好的实践 - 简单字符串日志
class OrderService
{
    public function createOrder(array $data): Order
    {
        Log::info('Creating order');
        
        try {
            $order = $this->repository->create($data);
            Log::info('Order created');
            return $order;
        } catch (\Exception $e) {
            Log::error('Error: ' . $e->getMessage());
            throw $e;
        }
    }
}
```

## 性能优化

### 1. 批量处理

```php
// 好的实践 - 批量处理
class OrderService
{
    public function batchCreateOrders(array $orderDataList): Collection
    {
        return DB::transaction(function () use ($orderDataList) {
            return collect($orderDataList)->map(function ($data) {
                return $this->repository->create($data);
            });
        });
    }
}

// 不好的实践 - 循环处理
class OrderService
{
    public function batchCreateOrders(array $orderDataList): array
    {
        $orders = [];
        foreach ($orderDataList as $data) {
            DB::beginTransaction();
            try {
                $orders[] = $this->repository->create($data);
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
        }
        return $orders;
    }
}
```

### 2. 缓存使用

```php
// 好的实践 - 合理使用缓存
class OrderService
{
    public function getOrderStatistics(): array
    {
        return Cache::remember('order_statistics', 3600, function () {
            return $this->repository->getStatistics();
        });
    }
}

// 不好的实践 - 没有缓存
class OrderService
{
    public function getOrderStatistics(): array
    {
        return $this->repository->getStatistics();
    }
}
```

## 注意事项

1. 服务层职责划分要清晰
2. 合理使用依赖注入
3. 统一的错误处理机制
4. 完善的日志记录
5. 事务处理要完整
6. 考虑性能优化
7. 代码可测试性
8. 接口设计要合理
9. 避免业务逻辑泄露
10. 保持代码简洁清晰
