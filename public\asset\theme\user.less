@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;

  .df(stretch, flex-start, column);

  .container {
    flex-grow: 1;
    margin-top: 20px;
    .df(flex-start);

    .left-side {
      background-color: #fff;
      width: 200px;
      flex-shrink: 0;
      min-height: 650px;

      .ul {
        padding: 15px 48px;

        .li {
          padding: 8px 0;

          &.th {
            font-size: 16px;
            color: #222;
          }

          a {
            font-size: 14px;
            color: #666;
            transition: color .35s ease-in-out;
          }

          &.active,
          &:hover {
            a {
              color: #ff9600;
            }
          }
        }
      }
    }

    .right-content {
      padding-left: 20px;
      flex-grow: 1;

      .white-box {
        margin-bottom: 14px;
        background-color: #fff;
      }

      .userinfo {
        padding: 42px 44px;
        .df(center);

        .avatar {
          .df(center);
          flex-grow: 1;

          .pic {
            border-radius: 50%;
            border: 4px solid #e0e0e0;
            width: 130px;
            height: 130px;
            overflow: hidden;
          }

          .info-box {
            padding-left: 50px;
            flex-grow: 1;

            .username {
              font-weight: bold;
              font-size: 24px;
              color: #616161;
              line-height: 1.3;
            }
  
            .call-text {
              margin-bottom: 10px;
              font-size: 14px;
              color: #B0b0b0;
            }
  
            .modify-userinfo {
              a {
                font-size: 12px;
                color: #ff9600;
              }
            }
          }
        }

        .level-phone-mail {
          padding-right: 100px;

          .label-box {
            padding: 6px 0;
            color: #757575;
            font-size: 14px;
            .df(center);
          }
        }
      }

      .num-info {
        padding: 30px 0;
        .df(center);

        .num-item {
          .df(center, center, column);
          flex-grow: 1;

          .num {
            font-size: 28px;
            color: #666;
            line-height: 1.32;
          }

          .label {
            font-size: 18px;
            color: #37474f;
            line-height: 1.33;
          }
        }
      }
    }
  }
}