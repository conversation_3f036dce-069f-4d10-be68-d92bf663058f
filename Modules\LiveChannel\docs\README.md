# LiveChannel 直播频道管理模块

## 📋 模块概述

LiveChannel 模块是 TVB 直播频道管理系统，提供完整的频道 CRUD 操作、状态管理、批量操作等功能。

## 🏗️ 功能特性

### 核心功能
- ✅ 频道 CRUD 操作
- ✅ 多语言支持（简体中文、繁体中文、英文）
- ✅ 直播状态管理（关闭、直播中、暂停）
- ✅ 批量操作（删除、状态更新）
- ✅ 时间冲突检测
- ✅ 自动生成频道编号
- ✅ 搜索和筛选
- ✅ 分页支持
- ✅ 统计功能

### 高级功能
- 🔒 数据验证和错误处理
- 📊 频道统计信息
- 🕒 时间范围管理
- 🌐 多语言界面
- 🔍 智能搜索
- 📱 响应式设计

## 📁 目录结构

```
Modules/LiveChannel/
├── Api/                          # API层
│   ├── Controllers/              # 控制器
│   │   └── LiveChannelController.php
│   └── route.php                 # 路由定义
├── Services/                     # 服务层
│   └── LiveChannelService.php
├── Models/                       # 模型层
│   └── LiveChannel.php
├── Enums/                        # 枚举定义
│   └── LiveChannelErrorCode.php
├── Providers/                    # 服务提供者
│   └── LiveChannelServiceProvider.php
├── Lang/                         # 多语言文件
│   ├── en/
│   │   └── livechannel.php
│   ├── zh_CN/
│   │   └── livechannel.php
│   └── zh_HK/
│       └── livechannel.php
├── database/                     # 数据库迁移
│   └── migrations/
│       └── 2024_01_15_000000_create_tvb_channels_table.php
├── docs/                         # 文档
│   └── README.md
├── config/                       # 配置文件
│   └── config.json
├── composer.json                 # 模块依赖
└── package.json                  # 前端依赖
```

## 🚀 快速开始

### 1. 安装模块

```bash
# 运行数据库迁移
php artisan migrate

# 注册模块服务提供者
# 在 config/app.php 中添加：
'providers' => [
    // ...
    Modules\LiveChannel\Providers\LiveChannelServiceProvider::class,
];
```

### 2. API 接口

#### 基础 CRUD 操作

```bash
# 获取频道列表
GET /api/admin/live-channel

# 创建频道
POST /api/admin/live-channel

# 获取频道详情
GET /api/admin/live-channel/{id}

# 更新频道
PUT /api/admin/live-channel/{id}

# 删除频道
DELETE /api/admin/live-channel/{id}
```

#### 批量操作

```bash
# 批量删除
DELETE /api/admin/live-channel/batch

# 批量更新状态
PUT /api/admin/live-channel/batch/status
```

#### 状态管理

```bash
# 更新直播状态
PUT /api/admin/live-channel/{id}/live-status
```

#### 特殊查询

```bash
# 获取直播中的频道
GET /api/admin/live-channel/live/list

# 获取统计信息
GET /api/admin/live-channel/statistics
```

## 📊 数据库设计

### tvb_channels 表结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 频道ID |
| channel_num | varchar(20) | 频道编号 |
| name | varchar(100) | 频道名称 |
| name_hk | varchar(100) | 繁体名称 |
| description | text | 频道描述 |
| description_hk | text | 繁体描述 |
| cover_image_url | varchar(255) | 封面图片URL |
| stream_url | varchar(500) | 直播流地址 |
| stream_key | varchar(500) | 直播密钥 |
| start_time | timestamp | 开始时间 |
| end_time | timestamp | 结束时间 |
| is_audio_only | tinyint(1) | 是否仅音频 |
| is_breaking_news | tinyint(1) | 是否突发直播 |
| is_hk_only | tinyint(1) | 是否仅限香港 |
| live_status | tinyint(4) | 直播状态 |
| status | tinyint(4) | 状态 |
| sort | int | 排序 |
| created_by | bigint | 创建人 |
| updated_by | bigint | 更新人 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |
| deleted_at | timestamp | 删除时间 |

## 🔧 配置说明

### 错误码定义

模块使用 18xxx 系列错误码：

- 18001: 频道不存在
- 18002: 创建频道失败
- 18003: 更新频道失败
- 18004: 删除频道失败
- 18005: 验证失败
- 18006: 状态变更失败
- 18007: 获取列表失败
- 18008: 批量操作失败
- 18009: 频道名称已存在
- 18010: 频道编号已存在
- 18011: 直播密钥无效
- 18012: 频道时间冲突

## 🌐 多语言支持

模块支持三种语言：

- **简体中文 (zh_CN)**: 默认语言
- **繁体中文 (zh_HK)**: 香港地区
- **英文 (en)**: 国际化

## 📝 使用示例

### 创建频道

```php
use Modules\LiveChannel\Services\LiveChannelService;

$service = app(LiveChannelService::class);

$data = [
    'name' => '翡翠台',
    'name_hk' => '翡翠台',
    'description' => 'TVB翡翠台高清直播',
    'description_hk' => 'TVB翡翠台高清直播',
    'cover_image_url' => 'https://example.com/cover.jpg',
    'start_time' => '2024-01-15 14:00:00',
    'end_time' => '2024-01-15 18:00:00',
    'is_audio_only' => false,
    'is_breaking_news' => false,
    'is_hk_only' => true,
];

$channel = $service->create($data);
```

### 获取直播中的频道

```php
$liveChannels = $service->getLiveChannels();
```

### 更新直播状态

```php
$service->updateLiveStatus($channelId, 1); // 开始直播
```

## 🔍 搜索和筛选

支持以下筛选条件：

- `keyword`: 关键词搜索（频道名称、编号、描述）
- `status`: 状态筛选
- `live_status`: 直播状态筛选
- `start_date`: 开始时间筛选
- `end_date`: 结束时间筛选
- `sort_field`: 排序字段
- `sort_order`: 排序方向
- `per_page`: 每页数量

## 🛡️ 安全特性

- 数据验证和清理
- SQL 注入防护
- XSS 攻击防护
- CSRF 保护
- 权限控制
- 日志记录

## 📈 性能优化

- 数据库索引优化
- 查询缓存
- 分页处理
- 批量操作
- 延迟加载

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请联系开发团队。 