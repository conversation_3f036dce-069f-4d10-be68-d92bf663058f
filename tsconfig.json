{
    "compilerOptions": {
        "baseUrl": "./",
        "target": "esnext",
        "useDefineForClassFields": true,
        "module": "esnext",
        "moduleResolution": "node",
        "strict": true,
        "jsx": "preserve",
        "sourceMap": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "esModuleInterop": true,
        "allowJs": true,
        "lib": [
            "esnext",
            "dom"
        ],
        "skipLibCheck": true,
        // "types": ["element-plus/golbal"],
        "paths": {
            "@/*": [
                "resources/*"
            ],
            "/admin/*": [
                "resources/admin/*"
            ]
        },
        "allowSyntheticDefaultImports": true,
        "types": ["vite/client"],
        "typeRoots": [
            "./node_modules/@types",
            "./Modules/Chat/views/ui/conversation/types"
        ]
    },
    "include": [
        "resources/admin/**/*.ts",
        "resources/admin/**/*.d.ts",
        "resources/admin/**/*.tsx",
        "resources/admin/**/*.vue",
        "resources/admin/**/*.js",
        "resources/admin/env.d.ts",
        "./auto-imports.d.ts",
        "env.d.ts"
    ],
    "exclude": [
        "resources/admin/styles/iconfont/iconfont.js"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}
