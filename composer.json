{"name": "bingo/bwms", "type": "project", "description": "The bwms Background management", "keywords": ["framework", "bingostart", "management", "permissions", "<PERSON><PERSON><PERSON>"], "license": "MIT", "repositories": [{"type": "path", "url": "../bingostart", "options": {"symlink": true}}], "require": {"php": "^8.2", "ext-curl": "*", "ext-dom": "*", "ext-exif": "*", "ext-gd": "*", "ext-json": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-zip": "*", "alibabacloud/sdk": "^1.8", "barryvdh/laravel-dompdf": "^3.0", "bingostart/core": "*", "doctrine/dbal": "^4.0.2", "dompdf/dompdf": "^3.0", "guzzlehttp/guzzle": "^7.9", "ilovepdf/iloveimg-php": "^1.1", "ilovepdf/ilovepdf-php": "^1.2", "intervention/image": "^3.10", "laravel/framework": "^11.25.0", "laravel/octane": "^2.9", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9.0", "league/flysystem-ftp": "^3.28", "nunomaduro/termwind": "^2.0.1", "twilio/sdk": "^7.0", "tymon/jwt-auth": "^2.1", "wechatpay/wechatpay": "^1.4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.6", "fakerphp/faker": "^1.23.1", "friendsofphp/php-cs-fixer": "^3.58.0", "kitloong/laravel-migrations-generator": "^7.0.3", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.34.7", "php-mock/php-mock": "^2.5"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "<PERSON><PERSON><PERSON>"}}, "autoload-dev": {"psr-4": {"Tests\\": "Tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@composer dump-autoload --no-scripts"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "cs-diff": "php-cs-fixer fix --dry-run --diff", "cs": "php-cs-fixer fix"}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}