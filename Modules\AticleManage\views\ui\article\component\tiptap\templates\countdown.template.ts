/**
 * Bootstrap 倒计时组件模板
 * 实现一个可以定制的倒计时组件
 * 增加媒体查询相关的响应式类，适配不同设备显示效果
 */

const countdownTemplate = `
<div data-bs-component="countdown" class="bootstrap-countdown responsive-block">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="p-4 text-center">
          <h3 class="mb-4 countdown-title fw-bold">倒计时</h3>
          
          <div class="countdown-wrapper">
            <!-- 天 -->
            <div class="countdown-item">
              <div class="countdown-value days-item">
                <span>06</span>
              </div>
              <div class="countdown-label">天</div>
            </div>
            
            <!-- 时 -->
            <div class="countdown-item">
              <div class="countdown-value hours-item">
                <span>23</span>
              </div>
              <div class="countdown-label">时</div>
            </div>
            
            <!-- 分 -->
            <div class="countdown-item">
              <div class="countdown-value minutes-item">
                <span>59</span>
              </div>
              <div class="countdown-label">分</div>
            </div>
            
            <!-- 秒 -->
            <div class="countdown-item">
              <div class="countdown-value seconds-item">
                <span>54</span>
              </div>
              <div class="countdown-label">秒</div>
            </div>
          </div>
          
          <div class="mt-4 countdown-message">活动即将开始，敬请期待！</div>
        </div>
      </div>
    </div>
  </div>
  
  <style>
    .bootstrap-countdown .countdown-title {
      font-size: 24px;
      color: #333;
    }
    
    .bootstrap-countdown .countdown-wrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 15px;
      margin: 0 auto;
      max-width: 600px;
    }
    
    .bootstrap-countdown .countdown-item {
      text-align: center;
      width: 100px;
      max-width: 120px;
    }
    
    .bootstrap-countdown .countdown-value {
      background-color: #5470FF;
      color: white;
      width: 100%;
      height: 0;
      padding-bottom: 100%;
      position: relative;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    
    .bootstrap-countdown .countdown-value span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 2.5rem;
      font-weight: bold;
      width: 100%;
    }
    
    .bootstrap-countdown .countdown-label {
      font-size: 1rem;
      color: #666;
    }
    
    .bootstrap-countdown .countdown-message {
      color: #666;
      font-size: 16px;
      margin-top: 20px;
      width: 100%;
      text-align: center;
    }

    /* 移动端样式 - 响应容器宽度 */
    .mobile-preview .bootstrap-countdown .countdown-wrapper,
    .tablet-preview .bootstrap-countdown .countdown-wrapper {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 15px;
      padding: 0 10px;
    }
    
    .mobile-preview .bootstrap-countdown .countdown-item,
    .tablet-preview .bootstrap-countdown .countdown-item {
      width: 100%;
      max-width: none;
    }
    
    .mobile-preview .bootstrap-countdown .countdown-value span,
    .tablet-preview .bootstrap-countdown .countdown-value span {
      font-size: 2rem;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .bootstrap-countdown .countdown-wrapper {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 15px;
        padding: 0 10px;
      }
      
      .bootstrap-countdown .countdown-item {
        width: 100%;
        max-width: none;
      }
      
      .bootstrap-countdown .countdown-value span {
        font-size: 2rem;
      }
    }
    
    /* 桌面端样式 */
    @media (min-width: 768px) {
      .bootstrap-countdown .countdown-wrapper {
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        gap: 20px;
      }
      
      .bootstrap-countdown .countdown-item {
        width: 100px;
        max-width: 120px;
      }
      
      .bootstrap-countdown .countdown-value span {
        font-size: 2.5rem;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .bootstrap-countdown .countdown-wrapper {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;
      gap: 20px;
    }
    
    .desktop-preview .bootstrap-countdown .countdown-item {
      width: 100px;
      max-width: 120px;
    }
    
    .desktop-preview .bootstrap-countdown .countdown-value span {
      font-size: 2.5rem;
    }
  </style>
  
  <script>
    (function() {
      // 找到所有倒计时组件
      const countdowns = document.querySelectorAll('[data-bs-component="countdown"]');
      
      countdowns.forEach(countdown => {
        // 提取目标日期，默认为当前时间加7天
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + 7);
        
        // 获取显示元素
        const daysEl = countdown.querySelector('.days-item span');
        const hoursEl = countdown.querySelector('.hours-item span');
        const minutesEl = countdown.querySelector('.minutes-item span');
        const secondsEl = countdown.querySelector('.seconds-item span');
        
        // 更新倒计时函数
        function updateCountdown() {
          const now = new Date();
          const diff = targetDate.getTime() - now.getTime();
          
          if (diff <= 0) {
            // 倒计时结束
            daysEl.textContent = '00';
            hoursEl.textContent = '00';
            minutesEl.textContent = '00';
            secondsEl.textContent = '00';
            return;
          }
          
          // 计算时间差
          const days = Math.floor(diff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diff % (1000 * 60)) / 1000);
          
          // 格式化显示
          daysEl.textContent = String(days).padStart(2, '0');
          hoursEl.textContent = String(hours).padStart(2, '0');
          minutesEl.textContent = String(minutes).padStart(2, '0');
          secondsEl.textContent = String(seconds).padStart(2, '0');
        }
        
        // 初始更新
        updateCountdown();
        
        // 每秒更新
        setInterval(updateCountdown, 1000);
      });
    })();
  </script>
</div>
`;

export default countdownTemplate; 