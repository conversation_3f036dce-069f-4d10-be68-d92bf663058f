# 管理后台布局模板

## 概述

管理后台布局是后台管理系统的基础框架。本文档提供了管理后台布局的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <el-container class="admin-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="admin-layout__aside">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo">
        <span v-show="!isCollapse">系统名称</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        class="admin-menu"
        @select="handleMenuSelect"
      >
        <template v-for="menu in menus" :key="menu.path">
          <!-- 无子菜单 -->
          <el-menu-item v-if="!menu.children?.length" :index="menu.path">
            <el-icon><component :is="menu.icon" /></el-icon>
            <template #title>{{ menu.title }}</template>
          </el-menu-item>
          
          <!-- 有子菜单 -->
          <el-sub-menu v-else :index="menu.path">
            <template #title>
              <el-icon><component :is="menu.icon" /></el-icon>
              <span>{{ menu.title }}</span>
            </template>
            
            <el-menu-item
              v-for="child in menu.children"
              :key="child.path"
              :index="child.path"
            >
              {{ child.title }}
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-aside>

    <el-container class="admin-layout__container">
      <!-- 头部 -->
      <el-header class="admin-layout__header">
        <div class="header-left">
          <el-icon
            class="collapse-btn"
            @click="isCollapse = !isCollapse"
          >
            <Fold v-if="isCollapse" />
            <Expand v-else />
          </el-icon>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userInfo.avatar" />
              <span class="username">{{ userInfo.name }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="password">修改密码</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主体内容 -->
      <el-main class="admin-layout__main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 菜单配置
interface MenuItem {
  path: string
  title: string
  icon?: string
  children?: MenuItem[]
}

const menus: MenuItem[] = [
  {
    path: '/dashboard',
    title: '仪表盘',
    icon: 'Odometer'
  },
  {
    path: '/system',
    title: '系统管理',
    icon: 'Setting',
    children: [
      {
        path: '/system/user',
        title: '用户管理'
      },
      {
        path: '/system/role',
        title: '角色管理'
      }
    ]
  }
]

// 状态
const isCollapse = ref(false)
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta?.title,
    path: item.path
  }))
})

// 菜单选择
const handleMenuSelect = (index: string) => {
  router.push(index)
}

// 下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'password':
      router.push('/password')
      break
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  
  &__aside {
    background-color: #304156;
    transition: width 0.3s;
    overflow: hidden;
    
    .logo-container {
      height: 60px;
      padding: 10px;
      display: flex;
      align-items: center;
      background: #2b2f3a;
      
      img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      span {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        white-space: nowrap;
      }
    }
    
    .admin-menu {
      border-right: none;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        color: #bfcbd9;
        
        &:hover {
          color: #fff;
          background-color: #263445;
        }
        
        &.is-active {
          color: #409eff;
          background-color: #263445;
        }
      }
    }
  }
  
  &__header {
    background-color: #fff;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .collapse-btn {
        font-size: 20px;
        cursor: pointer;
        margin-right: 20px;
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .username {
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
  
  &__main {
    background-color: #f0f2f5;
    padding: 20px;
    
    .fade-transform-enter-active,
    .fade-transform-leave-active {
      transition: all 0.3s;
    }
    
    .fade-transform-enter-from {
      opacity: 0;
      transform: translateX(-30px);
    }
    
    .fade-transform-leave-to {
      opacity: 0;
      transform: translateX(30px);
    }
  }
}
</style>
```

## 规范要求

1. 布局结构
   - 侧边栏菜单
   - 顶部导航
   - 内容区域
   - 响应式设计

2. 功能实现
   - 菜单折叠
   - 面包屑导航
   - 用户信息
   - 路由切换动画

3. 交互体验
   - 菜单高亮
   - 页面过渡
   - 响应式适配
   - 主题定制

4. 权限控制
   - 路由权限
   - 菜单权限
   - 按钮权限
   - 数据权限

## 最佳实践

1. 路由配置
```typescript
import type { RouteRecordRaw } from 'vue-router'

interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'children'> {
  name: string
  meta: {
    title: string
    icon?: string
    roles?: string[]
    hidden?: boolean
  }
  children?: AppRouteRecordRaw[]
}

const routes: AppRouteRecordRaw[] = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: {
      title: '仪表盘',
      icon: 'Odometer'
    }
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/RouterView.vue'),
    meta: {
      title: '系统管理',
      icon: 'Setting',
      roles: ['admin']
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: '用户管理'
        }
      }
    ]
  }
]
```

2. 权限控制
```typescript
import { useUserStore } from '@/stores/user'

const hasPermission = (roles: string[], route: AppRouteRecordRaw) => {
  if (route.meta?.roles) {
    return roles.some(role => route.meta.roles?.includes(role))
  }
  return true
}

const filterAsyncRoutes = (routes: AppRouteRecordRaw[], roles: string[]) => {
  const res: AppRouteRecordRaw[] = []
  
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })
  
  return res
}

// 生成可访问路由
const generateAccessRoutes = () => {
  const userStore = useUserStore()
  const accessRoutes = filterAsyncRoutes(asyncRoutes, userStore.roles)
  accessRoutes.forEach(route => router.addRoute(route))
}
```

## 注意事项

1. 布局设计
   - 合理使用空间
   - 保持视觉层次
   - 注意交互细节
   - 考虑响应式

2. 性能优化
   - 路由懒加载
   - 组件缓存
   - 动态导入
   - 按需加载

3. 权限管理
   - 细粒度控制
   - 动态权限
   - 权限缓存
   - 权限更新

4. 主题定制
   - 变量配置
   - 主题切换
   - 样式隔离
   - 深色模式
</code_block_to_apply_changes_from>
