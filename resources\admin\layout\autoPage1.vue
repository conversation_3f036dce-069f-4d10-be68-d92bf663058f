<template>
  <div class="header">
    <el-header>
        <Headers />
      </el-header>
  <div class="h-screen common-layout">
    
    <el-container class="h-full">
      <router-view></router-view>
    </el-container>
  </div>
</div>
</template>

<script lang="ts" setup>
import Headers from './components/header/index.vue'
</script>

<style lang="scss" scoped>
.el-header {
  --el-header-height: 80px;
  --el-header-padding: 16px 30px 16px 20px;
  box-shadow: 0px 3px 6px #0000004d;
  position: relative;
  z-index: 999;
}

</style>
