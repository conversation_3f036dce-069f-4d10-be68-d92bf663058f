export const footerTemplate = `
<div data-bs-component="footer" class="py-5 text-white bg-dark responsive-block">
    <div class="container">
        <div data-bs-component="rich-text" class="text-center footer-content">
            <p>No menus installed from the sidebar, create a new one by navigating to the Navigation Menu tool</p>
        </div>
        <div data-bs-component="social-media" class="gap-3 mt-3 d-flex justify-content-center social-icons">
            <a href="#" class="text-white social-icon">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="text-white social-icon">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-white social-icon">
                <i class="fab fa-linkedin-in"></i>
            </a>
            <a href="#" class="text-white social-icon">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="text-white social-icon">
                <i class="fab fa-youtube"></i>
            </a>
        </div>
    </div>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        .bg-dark {
            background-color: #212529 !important;
        }
        
        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .footer-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .social-icons {
            flex-wrap: wrap;
        }
        
        .social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }
        
        .social-icon i {
            font-size: 20px;
        }

        /* 移动端预览模式样式 */
        .mobile-preview .footer-content {
            padding: 0 15px;
        }
        
        .mobile-preview .footer-content p {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .mobile-preview .social-icons {
            gap: 10px !important;
        }
        
        .mobile-preview .social-icon {
            width: 36px;
            height: 36px;
        }
        
        .mobile-preview .social-icon i {
            font-size: 16px;
        }

        /* 移动端样式 - 响应屏幕宽度 */
        @media (max-width: 767.98px) {
            .footer-content {
                padding: 0 15px;
            }
            
            .footer-content p {
                font-size: 14px;
                line-height: 1.5;
            }
            
            .social-icons {
                gap: 10px !important;
            }
            
            .social-icon {
                width: 36px;
                height: 36px;
            }
            
            .social-icon i {
                font-size: 16px;
            }
        }

        /* 平板端样式 */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .footer-content {
                padding: 0 20px;
            }
            
            .footer-content p {
                font-size: 15px;
            }
            
            .social-icons {
                gap: 15px !important;
            }
            
            .social-icon {
                width: 38px;
                height: 38px;
            }
            
            .social-icon i {
                font-size: 18px;
            }
        }

        /* 桌面端样式 */
        @media (min-width: 992px) {
            .footer-content {
                padding: 0;
            }
            
            .footer-content p {
                font-size: 16px;
            }
            
            .social-icons {
                gap: 20px !important;
            }
            
            .social-icon {
                width: 40px;
                height: 40px;
            }
            
            .social-icon i {
                font-size: 20px;
            }
        }

        /* 桌面预览模式覆盖样式 */
        .desktop-preview .footer-content {
            padding: 0;
        }
        
        .desktop-preview .footer-content p {
            font-size: 16px;
        }
        
        .desktop-preview .social-icons {
            gap: 20px !important;
        }
        
        .desktop-preview .social-icon {
            width: 40px;
            height: 40px;
        }
        
        .desktop-preview .social-icon i {
            font-size: 20px;
        }
    </style>
</div>
` 