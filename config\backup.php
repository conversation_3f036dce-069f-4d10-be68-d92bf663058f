<?php

return [
    'backup' => [
        'name' => env('APP_NAME', 'laravel-backup'),
        'source' => [
            'files' => [
                'include' => [
                    base_path(),
                ],
                'exclude' => [
                    base_path('vendor'),
                    base_path('node_modules'),
                    storage_path('app/backups'),
                ],
                'follow_links' => false,
                'ignore_unreadable_directories' => true,
                'relative_path' => null
            ],
            'databases' => [
                'mysql',
            ],
        ],
        'database_dump_compressor' => null,
        'database_dump_file_timestamp_format' => 'Y-m-d-H-i-s',
        'database_dump_filename_base' => 'db-dump',
        'database_dump_file_extension' => '.sql',
        'password' => env('BACKUP_ARCHIVE_PASSWORD', null),
        'destination' => [
            'filename_prefix' => 'backup-',
            'disks' => [
                'backups',
            ],
        ],
        'temporary_directory' => storage_path('app/backup-temp'),
    ],

    'notifications' => [
        'notifications' => [
            'Spatie\Backup\Notifications\Notifications\BackupHasFailed' => ['mail'],
            'Spatie\Backup\Notifications\Notifications\UnhealthyBackupWasFound' => ['mail'],
            'Spatie\Backup\Notifications\Notifications\CleanupHasFailed' => ['mail'],
            'Spatie\Backup\Notifications\Notifications\BackupWasSuccessful' => ['mail'],
            'Spatie\Backup\Notifications\Notifications\HealthyBackupWasFound' => ['mail'],
            'Spatie\Backup\Notifications\Notifications\CleanupWasSuccessful' => ['mail'],
        ],

        'notifiable' => 'Spatie\Backup\Notifications\Notifiable',

        'mail' => [
            'to' => env('BACKUP_NOTIFICATION_EMAIL'),
        ],
    ],

    'monitor_backups' => [
        [
            'name' => env('APP_NAME', 'laravel-backup'),
            'disks' => ['backups'],
            'health_checks' => [
                'Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumAgeInDays' => 1,
                'Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumStorageInMegabytes' => 5000,
            ],
        ],
    ],

    'cleanup' => [
        'strategy' => 'Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy',
        'default_strategy' => [
            'keep_all_backups_for_days' => 7,
            'keep_daily_backups_for_days' => 16,
            'keep_weekly_backups_for_weeks' => 8,
            'keep_monthly_backups_for_months' => 4,
            'keep_yearly_backups_for_years' => 2,
            'delete_oldest_backups_when_using_more_megabytes_than' => 5000,
        ],
    ],
];
