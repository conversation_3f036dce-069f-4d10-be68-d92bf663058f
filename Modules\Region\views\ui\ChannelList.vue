<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover v-model="filterDialog">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="FilterIcon" />
            </el-icon>
            <span>篩選</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="狀態">
            <el-select v-model="search.status" placeholder="全部狀態" size="large">
              <el-option label="全部狀態" value="" />
              <el-option label="啟用" value="啟用" />
              <el-option label="停用" value="停用" />
            </el-select>
          </el-form-item>
          <el-form-item label="頻道類型">
            <el-select v-model="search.type" placeholder="全部類型" size="large">
              <el-option label="全部類型" value="" />
              <el-option label="電視頻道" value="電視頻道" />
              <el-option label="新聞頻道" value="新聞頻道" />
              <el-option label="體育頻道" value="體育頻道" />
            </el-select>
          </el-form-item>
          <el-form-item label="搜索關鍵詞">
            <el-input 
              v-model="search.keyword" 
              placeholder="搜索頻道名稱或ID" 
              size="large"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="resetSearch">
              <el-icon size="16"><Refresh /></el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="doSearch" type="primary">
              <el-icon size="16"><Filter /></el-icon>
              <span>篩選</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" class="add-btn" @click="openAddDialog">
        <el-icon><Plus /></el-icon>
        <span>新增頻道</span>
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="module-con">
      <div class="box">
        <el-table :data="filteredChannelList" style="width: 100%" v-loading="loading">
          <template #empty>
            <el-empty description="暫無數據" image-size="100px" />
          </template>
          <el-table-column prop="channelId" label="頻道ID" width="120" />
          <el-table-column prop="channelName" label="頻道名稱" min-width="150" />
          <el-table-column prop="channelType" label="頻道類型" width="120" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="urlPath" label="URL路徑" width="150" />
          <el-table-column prop="status" label="狀態" width="100">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.status === '啟用'">啟用</el-tag>
              <el-tag type="info" v-else>停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="associatedRegions" label="關聯區域" width="100">
            <template #default="scope">
              <span>{{ scope.row.associatedRegions }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editChannel(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="deleteChannel(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每頁顯示</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  暫無數據
                </div>
              </template>
            </el-select>
            <span class="total-text">共 {{ total }} 條記錄</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/編輯对话框 -->
    <el-dialog class="el-dialog-common-cls" v-model="addDialogVisible" :title="dialogTitle" width="600">
      <el-form label-position="top" :model="addForm" :rules="formRules" ref="addFormRef">
        <el-form-item label="頻道名稱" prop="channelName">
          <el-input v-model="addForm.channelName" placeholder="頻道名稱" />
        </el-form-item>
        <el-form-item label="頻道標題" prop="channelTitle">
          <el-input v-model="addForm.channelTitle" placeholder="頻道標題" />
        </el-form-item>
        <el-form-item label="頻道描述" prop="description">
          <el-input 
            v-model="addForm.description" 
            placeholder="頻道描述" 
            type="textarea" 
            :rows="4"
            resize="vertical"
          />
        </el-form-item>
        <el-form-item label="Open Graph Image" prop="openGraphImage">
          <div class="upload-area" @click="triggerImageUpload">
            <div class="upload-content">
              <el-icon class="upload-icon"><Upload /></el-icon>
              <div class="upload-text">Open Graph Image</div>
            </div>
            <input 
              ref="imageInput" 
              type="file" 
              accept="image/*" 
              style="display: none;" 
              @change="handleImageUpload"
            />
          </div>
        </el-form-item>
        <el-form-item label="URL Path" prop="urlPath">
          <el-input 
            v-model="addForm.urlPath" 
            placeholder="例如: /tvb-jade 或 /news-channel" 
          />
          <div class="input-tip">請輸入頻道的URL路徑，例如:/channel-name</div>
        </el-form-item>
        <el-form-item label="" prop="status" label-position="left">
          <div class="flex justify-between" style="width: 100%;">
              <div class="el-form-item__label">
              <span>啟用狀態</span>
            </div>
            <el-switch 
              v-model="addForm.status" 
              :active-value="1" 
              :inactive-value="0"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="關聯區域" prop="associatedRegions">
          <div class="associated-regions">
            <div class="region-tags">
              <el-tag 
                v-for="region in addForm.associatedRegions" 
                :key="region.id"
                class="region-tag"
                closable
                @close="removeAssociatedRegion(region)"
              >
                {{ region.name }}
              </el-tag>
            </div>
            <el-button 
              type="primary" 
              size="small" 
              @click="openRegionSelector"
              class="add-region-btn"
            >
              <el-icon><Plus /></el-icon>
              <span>新增區域</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAdd">儲存</el-button>
      </div>
    </el-dialog>

    <!-- 区域选择对话框 -->
    <el-dialog 
      v-model="regionSelectorVisible" 
      title="選擇關聯區域" 
      width="500"
      class="region-selector-dialog el-dialog-common-cls"
    >
      <div class="region-selector-content">
        <div class="available-regions">
          <h4 class="section-title">可選區域</h4>
          <div class="region-list">
            <el-tag 
              v-for="region in availableRegions" 
              :key="region.id"
              :class="['region-option', { 'selected': isRegionSelected(region) }]"
              @click="toggleRegionSelection(region)"
            >
              {{ region.name }}
            </el-tag>
          </div>
        </div>
        
        <div class="selected-regions" v-if="selectedRegions.length > 0">
          <h4 class="section-title">已選區域</h4>
          <div class="region-list">
            <el-tag 
              v-for="region in selectedRegions" 
              :key="region.id"
              class="region-option selected"
              closable
              @close="removeSelectedRegion(region)"
            >
              {{ region.name }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="regionSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmRegionSelection">
          確定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Edit, Delete, Search, Filter, Refresh, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FilterPopover from '/resources/admin/components/popover/index.vue'
import http from '/admin/support/http'

const router = useRouter()

// 搜索相关
const filterDialog = ref(false)
const search = reactive({ 
  status: '', 
  type: '', 
  keyword: '' 
})
const loading = ref(false)

// 频道数据
const channelList = ref([
  {
    id: 1,
    channelId: 'CH001',
    channelName: 'TVB翡翠台',
    channelType: '電視頻道',
    description: '香港主要粵語電視頻道',
    urlPath: '/tvb-jade',
    status: '啟用',
    associatedRegions: 2
  },
  {
    id: 2,
    channelId: 'CH002',
    channelName: 'TVB明珠台',
    channelType: '電視頻道',
    description: '香港英語電視頻道',
    urlPath: '/tvb-pearl',
    status: '啟用',
    associatedRegions: 1
  },
  {
    id: 3,
    channelId: 'CH003',
    channelName: 'ViuTV',
    channelType: '電視頻道',
    description: '香港免費電視頻道',
    urlPath: '/viu-tv',
    status: '停用',
    associatedRegions: 0
  },
  {
    id: 4,
    channelId: 'CH004',
    channelName: '無綫新聞台',
    channelType: '新聞頻道',
    description: '24小時新聞頻道',
    urlPath: '/tvb-news',
    status: '啟用',
    associatedRegions: 1
  }
])

// 过滤后的频道列表
const filteredChannelList = computed(() => {
  let arr = channelList.value
  
  // 状态筛选
  if (search.status) {
    arr = arr.filter(channel => channel.status === search.status)
  }
  
  // 类型筛选
  if (search.type) {
    arr = arr.filter(channel => channel.channelType === search.type)
  }
  
  // 关键词搜索
  if (search.keyword) {
    arr = arr.filter(channel => 
      channel.channelName.includes(search.keyword) || 
      channel.channelId.includes(search.keyword)
    )
  }
  
  return arr
})

// 对话框相关
const addDialogVisible = ref(false)
const addForm = reactive<{ 
  channelName: string;
  channelTitle: string;
  description: string;
  openGraphImage: string;
  urlPath: string;
  status: number;
  associatedRegions: any[];
}>({ 
  channelName: '', 
  channelTitle: '',
  description: '',
  openGraphImage: '',
  urlPath: '',
  status: 1,
  associatedRegions: []
})
const dialogTitle = ref('新增頻道')
const editIndex = ref(-1)
const addFormRef = ref()
const imageInput = ref()

// 区域选择相关
const regionSelectorVisible = ref(false)
const availableRegions = ref([
  { id: 1, name: '港劇頻道' },
  { id: 2, name: '綜藝頻道' },
  { id: 3, name: '新聞頻道' },
  { id: 4, name: '體育頻道' },
  { id: 5, name: '財經頻道' },
  { id: 6, name: '兒童頻道' }
])
const selectedRegions = ref<any[]>([])

// 表单验证规则
const formRules = {
  channelName: [
    { required: true, message: '請輸入頻道名稱', trigger: 'blur' }
  ],
  channelTitle: [
    { required: true, message: '請輸入頻道標題', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '請輸入頻道描述', trigger: 'blur' }
  ],
  urlPath: [
    { required: true, message: '請輸入URL路徑', trigger: 'blur' }
  ]
}

// 分页相关
const page = ref(1)
const limit = ref(10)
const total = ref(4)

// 搜索相关函数
function resetSearch() {
  search.status = ''
  search.type = ''
  search.keyword = ''
}

function doSearch() {
  filterDialog.value = false
}

// 打开新增对话框
const openAddDialog = () => {
  addForm.channelName = ''
  addForm.channelTitle = ''
  addForm.description = ''
  addForm.openGraphImage = ''
  addForm.urlPath = ''
  addForm.status = 1
  addForm.associatedRegions = []
  dialogTitle.value = '新增頻道'
  editIndex.value = -1
  addDialogVisible.value = true
}

// 提交新增/编辑
const submitAdd = async () => {
  try {
    await addFormRef.value.validate()
    
    if (editIndex.value === -1) {
      // 新增
      const response = await http.post('/admin/channel', {
        channelName: addForm.channelName,
        channelTitle: addForm.channelTitle,
        description: addForm.description,
        openGraphImage: addForm.openGraphImage,
        urlPath: addForm.urlPath,
        status: addForm.status === 1 ? '啟用' : '停用'
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success('新增成功')
        getChannelList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '新增失敗')
      }
    } else {
      // 编辑
      const row = channelList.value[editIndex.value]
      const response = await http.put(`/admin/channel/${row.id}`, {
        channelName: addForm.channelName,
        channelTitle: addForm.channelTitle,
        description: addForm.description,
        openGraphImage: addForm.openGraphImage,
        urlPath: addForm.urlPath,
        status: addForm.status === 1 ? '啟用' : '停用'
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success('編輯成功')
        getChannelList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '編輯失敗')
      }
    }
  } catch (error) {
    if (error === false) {
      ElMessage.error('請檢查表單輸入')
    } else {
      ElMessage.error('操作失敗')
    }
  }
}

// 编辑频道
const editChannel = (row: any) => {
  addForm.channelName = row.channelName
  addForm.channelTitle = row.channelTitle || ''
  addForm.description = row.description
  addForm.openGraphImage = row.openGraphImage || ''
  addForm.urlPath = row.urlPath
  addForm.status = row.status === '啟用' ? 1 : 0
  addForm.associatedRegions = row.associatedRegions || []
  dialogTitle.value = '編輯頻道'
  editIndex.value = channelList.value.findIndex(item => item.id === row.id)
  addDialogVisible.value = true
}

// 删除频道
const deleteChannel = (row: any) => {
  ElMessageBox.confirm(
    `確定要刪除頻道「${row.channelName}」嗎？此操作不可恢復！`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await http.delete(`/admin/channel/${row.id}`)
      if (response.data && response.data.code === 200) {
        ElMessage.success('刪除成功')
        getChannelList()
      } else {
        ElMessage.error(response.data?.message || '刪除失敗')
      }
    } catch (error) {
      ElMessage.error('刪除失敗')
    }
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}

// 分页处理
const changePage = () => {
  getChannelList()
}

// 触发图片上传
const triggerImageUpload = () => {
  imageInput.value.click()
}

// 处理图片上传
const handleImageUpload = (event: any) => {
  const file = event.target.files[0]
  if (file) {
    // 这里可以添加文件类型和大小验证
    if (file.size > 5 * 1024 * 1024) {
      ElMessage.error('圖片大小不能超過5MB')
      return
    }
    
    // 创建文件预览URL
    const reader = new FileReader()
    reader.onload = (e) => {
      addForm.openGraphImage = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

// 打开区域选择器
const openRegionSelector = () => {
  selectedRegions.value = [...addForm.associatedRegions]
  regionSelectorVisible.value = true
}

// 切换区域选择状态
const toggleRegionSelection = (region: any) => {
  const isSelected = isRegionSelected(region)
  if (isSelected) {
    removeSelectedRegion(region)
  } else {
    selectedRegions.value.push(region)
  }
}

// 检查区域是否已选中
const isRegionSelected = (region: any) => {
  return selectedRegions.value.some((r: any) => r.id === region.id)
}

// 移除选中的区域
const removeSelectedRegion = (region: any) => {
  const index = selectedRegions.value.findIndex((r: any) => r.id === region.id)
  if (index > -1) {
    selectedRegions.value.splice(index, 1)
  }
}

// 移除关联区域
const removeAssociatedRegion = (region: any) => {
  const index = addForm.associatedRegions.findIndex((r: any) => r.id === region.id)
  if (index > -1) {
    addForm.associatedRegions.splice(index, 1)
  }
}

// 确认区域选择
const confirmRegionSelection = () => {
  addForm.associatedRegions = [...selectedRegions.value]
  regionSelectorVisible.value = false
  ElMessage.success('區域選擇已更新')
}

// 获取频道列表
const getChannelList = async () => {
  loading.value = true
  try {
    const params: any = {
      page: page.value,
      limit: limit.value
    }
    
    if (search.status) {
      params.status = search.status
    }
    
    if (search.type) {
      params.type = search.type
    }
    
    if (search.keyword) {
      params.keyword = search.keyword
    }

    const response = await http.get('/admin/channel', params)
    
    if (response.data && response.data.code === 200) {
      channelList.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data?.message || '獲取數據失敗')
      channelList.value = []
      total.value = 0
    }
  } catch (error) {
    channelList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 页面初始化
// onMounted(() => {
//   getAreaList()
// })
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.upload-area {
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
  }
  
  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    
    .upload-icon {
      font-size: 32px;
      color: #c0c4cc;
    }
    
    .upload-text {
      font-size: 14px;
      color: #606266;
    }
  }
}

.input-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.associated-regions {
  .region-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
    
    .region-tag {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }
  }
  
  .add-region-btn {
    font-size: 12px;
    padding: 4px 12px;
  }
}

.region-selector-dialog {
  .region-selector-content {
    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin: 0 0 12px 0;
    }
    
    .region-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 20px;
    }
    
    .region-option {
      cursor: pointer;
      transition: all 0.3s;
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
      
      &:hover {
        background: #d9f7be;
      }
      
      &.selected {
        background: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        
        &:hover {
          background: #bae7ff;
        }
      }
    }
  }
}

</style>
