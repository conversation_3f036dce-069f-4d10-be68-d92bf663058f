<?php

return [
    // General messages
    'success' => 'Operation successful',
    'failed' => 'Operation failed',
    'not_found' => 'Data not found',
    'already_exists' => 'Data already exists',
    'validation_failed' => 'Validation failed',
    'permission_denied' => 'Permission denied',
    'system_error' => 'System error',

    // Live channel related
    'live_channel' => 'Live Channel',
    'live_channels' => 'Live Channel List',
    'channel_name' => 'Channel Name',
    'channel_name_hk' => 'Traditional Name',
    'channel_description' => 'Channel Description',
    'channel_description_hk' => 'Traditional Description',
    'channel_num' => 'Channel Number',
    'channel_status' => 'Channel Status',
    'channel_live_status' => 'Live Status',
    'channel_sort' => 'Channel Sort',
    'channel_cover_image' => 'Cover Image',
    'channel_stream_url' => 'Stream URL',
    'channel_stream_key' => 'Stream Key',
    'channel_start_time' => 'Start Time',
    'channel_end_time' => 'End Time',
    'channel_is_audio_only' => 'Audio Only',
    'channel_is_breaking_news' => 'Breaking News',
    'channel_is_hk_only' => 'Hong Kong Only',
    'channel_created_at' => 'Created At',
    'channel_updated_at' => 'Updated At',

    // Status
    'status_enabled' => 'Enabled',
    'status_disabled' => 'Disabled',
    'live_status_closed' => 'Closed',
    'live_status_live' => 'Live',
    'live_status_paused' => 'Paused',
    'audio_only_yes' => 'Yes',
    'audio_only_no' => 'No',
    'breaking_news_yes' => 'Yes',
    'breaking_news_no' => 'No',
    'hk_only_yes' => 'Yes',
    'hk_only_no' => 'No',

    // Operations
    'create' => 'Create',
    'update' => 'Update',
    'delete' => 'Delete',
    'enable' => 'Enable',
    'disable' => 'Disable',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'batch' => 'Batch Operation',

    // Success messages
    'create_success' => 'Created successfully',
    'update_success' => 'Updated successfully',
    'delete_success' => 'Deleted successfully',
    'enable_success' => 'Enabled successfully',
    'disable_success' => 'Disabled successfully',
    'batch_success' => 'Batch operation successful',
    'status_update_success' => 'Status updated successfully',
    'live_status_update_success' => 'Live status updated successfully',

    // Failed messages
    'create_failed' => 'Creation failed',
    'update_failed' => 'Update failed',
    'delete_failed' => 'Delete failed',
    'enable_failed' => 'Enable failed',
    'disable_failed' => 'Disable failed',
    'batch_failed' => 'Batch operation failed',
    'status_change_failed' => 'Status update failed',
    'live_status_change_failed' => 'Live status update failed',

    // Validation messages
    'name_required' => 'Channel name cannot be empty',
    'name_hk_required' => 'Traditional name cannot be empty',
    'description_required' => 'Channel description cannot be empty',
    'description_hk_required' => 'Traditional description cannot be empty',
    'cover_image_url_required' => 'Cover image cannot be empty',
    'cover_image_url_url' => 'Cover image format is incorrect',
    'start_time_required' => 'Start time cannot be empty',
    'end_time_required' => 'End time cannot be empty',
    'end_time_after' => 'End time must be later than start time',
    'is_audio_only_required' => 'Audio only setting cannot be empty',
    'is_breaking_news_required' => 'Breaking news setting cannot be empty',
    'is_hk_only_required' => 'Hong Kong only setting cannot be empty',
    'name_max' => 'Name length cannot exceed :max characters',
    'description_max' => 'Description length cannot exceed :max characters',
    'status_invalid' => 'Status value is invalid',
    'live_status_invalid' => 'Live status value is invalid',
    'sort_invalid' => 'Sort value is invalid',
    'stream_url_invalid' => 'Stream URL format is invalid',
    'stream_key_invalid' => 'Stream key format is invalid',

    // Error messages
    'channel_not_found' => 'Channel not found',
    'channel_already_exists' => 'Channel already exists',
    'channel_name_exists' => 'Channel name already exists',
    'channel_num_exists' => 'Channel number already exists',
    'channel_create_failed' => 'Failed to create channel',
    'channel_update_failed' => 'Failed to update channel',
    'channel_delete_failed' => 'Failed to delete channel',
    'channel_status_invalid' => 'Channel status is invalid',
    'channel_live_status_invalid' => 'Live status is invalid',
    'channel_list_failed' => 'Failed to get channel list',
    'channel_query_failed' => 'Failed to query channel',
    'channel_time_conflict' => 'Time conflict, please check start and end time',
    'channel_stream_key_invalid' => 'Stream key is invalid',

    // Batch operation messages
    'batch_delete_success' => 'Successfully deleted :count channels',
    'batch_enable_success' => 'Successfully enabled :count channels',
    'batch_disable_success' => 'Successfully disabled :count channels',
    'batch_live_status_success' => 'Successfully updated live status for :count channels',

    // Parameter validation
    'action_required' => 'Action type cannot be empty',
    'action_invalid' => 'Action type is invalid',
    'ids_required' => 'ID list cannot be empty',
    'ids_invalid' => 'ID list format is invalid',
    'id_invalid' => 'ID format is invalid',
    'live_status_required' => 'Live status cannot be empty',
    'live_status_in' => 'Live status value is incorrect',
    'status_required' => 'Status cannot be empty',

    // Pagination related
    'page_info' => 'Page :current of :total records',
    'no_data' => 'No data',
    'loading' => 'Loading...',

    // Search and filter
    'search_placeholder' => 'Please enter search keywords',
    'filter_all' => 'All',
    'filter_enabled' => 'Enabled',
    'filter_disabled' => 'Disabled',
    'filter_live' => 'Live',
    'filter_closed' => 'Closed',
    'filter_paused' => 'Paused',

    // Sort
    'sort_by_name' => 'Sort by name',
    'sort_by_created_at' => 'Sort by creation time',
    'sort_by_updated_at' => 'Sort by update time',
    'sort_by_sort' => 'Sort by sort value',
    'sort_asc' => 'Ascending',
    'sort_desc' => 'Descending',

    // Statistics
    'total_count' => 'Total count',
    'enabled_count' => 'Enabled count',
    'disabled_count' => 'Disabled count',
    'live_count' => 'Live count',
    'closed_count' => 'Closed count',
    'paused_count' => 'Paused count',

    // Channel number generation
    'channel_num_generated' => 'Channel number has been automatically generated',
    'channel_num_format' => 'Channel number format: CH + number',

    // Time related
    'time_range' => 'Time range',
    'start_date' => 'Start date',
    'end_date' => 'End date',
    'time_conflict' => 'Time conflict',
    'time_valid' => 'Time valid',

    // Live stream related
    'live_stream' => 'Live stream',
    'stream_quality' => 'Stream quality',
    'stream_status' => 'Stream status',
    'breaking_news' => 'Breaking news',
    'audio_only' => 'Audio only',
    'hk_only' => 'Hong Kong only',

    // Operation prompts
    'confirm_delete' => 'Are you sure you want to delete this channel?',
    'confirm_batch_delete' => 'Are you sure you want to delete the selected :count channels?',
    'confirm_status_change' => 'Are you sure you want to change the channel status?',
    'confirm_live_status_change' => 'Are you sure you want to change the live status?',

    // Bind stream config related
    'bind_stream_config_success' => 'Stream configuration bound successfully',
    'bind_stream_config_failed' => 'Failed to bind stream configuration',
    'id_required' => 'Channel ID cannot be empty',
    'id_integer' => 'Channel ID format is invalid',
    'id_exists' => 'Channel does not exist',
    'stream_url_required' => 'Stream URL cannot be empty',
    'stream_url_url' => 'Stream URL format is invalid',
    'stream_key_required' => 'Stream key cannot be empty',
    'stream_key_string' => 'Stream key format is invalid',

    // Channel options related
    'get_options_success' => 'Get channel options successfully',
    'get_options_failed' => 'Failed to get channel options',
]; 