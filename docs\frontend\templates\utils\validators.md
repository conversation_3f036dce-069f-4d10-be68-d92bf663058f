# 验证器模板

## 概述

本文档提供了前端表单验证的常用验证器模板和最佳实践。

## 基本结构

```typescript
// src/utils/validators.ts

/**
 * 必填验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function required(message = '该字段不能为空'): any {
  return {
    required: true,
    message,
    trigger: ['blur', 'change']
  }
}

/**
 * 字符串长度验证
 * @param min 最小长度
 * @param max 最大长度
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function length(min: number, max: number, message?: string): any {
  return {
    min,
    max,
    message: message || `长度必须在${min}到${max}个字符之间`,
    trigger: 'blur'
  }
}

/**
 * 正则表达式验证
 * @param pattern 正则表达式
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function pattern(pattern: RegExp, message: string): any {
  return {
    pattern,
    message,
    trigger: 'blur'
  }
}

/**
 * 邮箱验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function email(message = '请输入正确的邮箱地址'): any {
  return pattern(
    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    message
  )
}

/**
 * 手机号验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function mobile(message = '请输入正确的手机号'): any {
  return pattern(/^1[3-9]\d{9}$/, message)
}

/**
 * URL验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function url(message = '请输入正确的URL地址'): any {
  return pattern(
    /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
    message
  )
}

/**
 * 数字范围验证
 * @param min 最小值
 * @param max 最大值
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function range(min: number, max: number, message?: string): any {
  return {
    type: 'number',
    min,
    max,
    message: message || `数值必须在${min}到${max}之间`,
    trigger: 'blur'
  }
}

/**
 * 自定义验证函数
 * @param validator 验证函数
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function custom(
  validator: (value: any) => boolean,
  message: string
): any {
  return {
    validator: (_: any, value: any) => {
      if (!validator(value)) {
        return Promise.reject(message)
      }
      return Promise.resolve()
    },
    trigger: 'blur'
  }
}

/**
 * 密码强度验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function password(message = '密码必须包含大小写字母、数字和特殊字符'): any {
  return pattern(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/,
    message
  )
}

/**
 * 身份证号验证
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function idCard(message = '请输入正确的身份证号'): any {
  return {
    validator: (_: any, value: string) => {
      if (!value) return Promise.resolve()
      
      // 身份证号码验证
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      if (!reg.test(value)) {
        return Promise.reject(message)
      }
      
      // 校验生日
      if (value.length === 18) {
        const year = parseInt(value.substr(6, 4))
        const month = parseInt(value.substr(10, 2))
        const day = parseInt(value.substr(12, 2))
        const date = new Date(year, month - 1, day)
        
        if (
          date.getFullYear() !== year ||
          date.getMonth() + 1 !== month ||
          date.getDate() !== day
        ) {
          return Promise.reject(message)
        }
      }
      
      return Promise.resolve()
    },
    trigger: 'blur'
  }
}

/**
 * 确认密码验证
 * @param password 密码字段
 * @param message 错误提示信息
 * @returns 验证函数
 */
export function confirmPassword(
  password: string | (() => string),
  message = '两次输入的密码不一致'
): any {
  return {
    validator: (_: any, value: string) => {
      const pwd = typeof password === 'function' ? password() : password
      if (value !== pwd) {
        return Promise.reject(message)
      }
      return Promise.resolve()
    },
    trigger: 'blur'
  }
}

/**
 * 组合验证规则
 * @param rules 验证规则数组
 * @returns 组合后的验证规则
 */
export function compose(rules: any[]): any[] {
  return rules
}
```

## 使用示例

1. 基本表单验证
```typescript
import { reactive } from 'vue'
import type { FormRules } from 'element-plus'
import * as validators from '@/utils/validators'

const formRules = reactive<FormRules>({
  username: [
    validators.required('请输入用户名'),
    validators.length(3, 20, '用户名长度必须在3-20个字符之间')
  ],
  email: [
    validators.required('请输入邮箱'),
    validators.email()
  ],
  mobile: [
    validators.required('请输入手机号'),
    validators.mobile()
  ]
})
```

2. 密码验证
```typescript
const formData = reactive({
  password: '',
  confirmPassword: ''
})

const formRules = reactive<FormRules>({
  password: [
    validators.required('请输入密码'),
    validators.password()
  ],
  confirmPassword: [
    validators.required('请确认密码'),
    validators.confirmPassword(() => formData.password)
  ]
})
```

3. 自定义验证
```typescript
const formRules = reactive<FormRules>({
  age: [
    validators.required('请输入年龄'),
    validators.range(18, 60, '年龄必须在18-60岁之间')
  ],
  website: [
    validators.custom(
      (value: string) => value.startsWith('https'),
      '网址必须以https开头'
    )
  ]
})
```

## 注意事项

1. 验证规则
   - 规则组合合理
   - 错误提示友好
   - 触发时机适当
   - 验证逻辑完整

2. 性能优化
   - 避免复杂验证
   - 合理使用异步
   - 减少重复验证
   - 优化验证时机

3. 用户体验
   - 即时反馈
   - 清晰提示
   - 合理默认值
   - 容错处理

4. 最佳实践
   - 统一管理规则
   - 复用验证逻辑
   - 类型安全
   - 易于扩展

5. 测试要点
   - 边界值测试
   - 特殊字符测试
   - 组合规则测试
   - 异步验证测试
</rewritten_file>
