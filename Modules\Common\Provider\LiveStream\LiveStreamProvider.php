<?php

namespace Modules\Common\Provider\LiveStream;

class LiveStreamProvider
{
    /**
     * @var AbstractLiveStreamProvider[]
     */
    private static $instances = [
    ];

    public static function register($provider)
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractLiveStreamProvider[]
     */
    public static function all()
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function nameTitleMap(): array
    {
        return array_build(self::all(), function ($k, $provider) {
            return [
                $provider->name(),
                $provider->title(),
            ];
        });
    }

    public static function first(): string
    {
        foreach (self::all() as $provider) {
            return $provider->name();
        }
        return "";
    }

    /**
     * @param $name
     * @return AbstractLiveStreamProvider|null
     */
    public static function get($name): ?AbstractLiveStreamProvider
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }
        return null;

    }
}
