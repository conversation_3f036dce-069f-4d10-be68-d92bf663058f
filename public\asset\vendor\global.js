$(document).ready(function () {
  $(".bwms-page .tab-list .tab-item").click(function () {
    $(".tab-list .tab-item").removeClass("active");
    $(this).addClass('active');
    const tab = $(this).attr("data-tab");
    $(`.bwms-page .pane-list .tab-pane`).removeClass('active');
    $(`.bwms-page .pane-list .tab-pane[data-tab='${tab}']`).addClass('active');
  });

  // 搜索弹出层
  let $searchMask = $(".bwms-page .search-mask");
  let $close = $(".bwms-page .search-mask .icon-close");
  let $searchIcon = $("header .header-con .more .icon-search");
  $searchIcon.click(function () {
    $searchMask.addClass("open");
  });

  $close.click(function () {
    $searchMask.removeClass("open");
  });

  $searchMask.click(function (e) {
    if (e.target === this) {
      $searchMask.removeClass("open");
    }
  })
})