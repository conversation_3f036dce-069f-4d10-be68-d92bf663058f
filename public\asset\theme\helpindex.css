.bwms-page .qiuck-nav {
  margin-top: 5px;
}
.bwms-page .qiuck-nav .list .item {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}
.bwms-page .qiuck-nav .list .item .box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
}
.bwms-page .qiuck-nav .list .item .box .pic {
  max-width: 40px;
}
.bwms-page .qiuck-nav .list .item .box .text-box {
  padding-left: 20px;
  flex-grow: 1;
}
.bwms-page .qiuck-nav .list .item .box .text-box h6 {
  color: #34495e;
  font-size: 20px;
  line-height: 1.5;
  font-weight: normal;
}
.bwms-page .qiuck-nav .list .item .box .text-box p {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #c4cfdb;
  font-size: 14px;
  line-height: 1.4;
}
.bwms-page .link-page {
  margin-top: 5px;
}
.bwms-page .link-page .list .item {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}
.bwms-page .link-page .list .item .box {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
  height: 100%;
}
.bwms-page .link-page .list .item .box .tit {
  padding: 10px;
  font-size: 20px;
  color: #34495e;
}
.bwms-page .link-page .list .item .box .link-list {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.bwms-page .link-page .list .item .box .link-list .link-item {
  margin: 5px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  background-color: #fff;
  width: calc(50% - 10px);
  font-size: 14px;
  line-height: 2;
  transition: background-color 0.35s ease-in-out;
  display: block;
  color: #34495e;
  text-align: center;
}
.bwms-page .link-page .list .item .box .link-list .link-item:hover {
  background-color: #eee;
}
