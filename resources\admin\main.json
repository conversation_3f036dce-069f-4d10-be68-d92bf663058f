{"name": "bwms-admin", "version": "1.0.0", "private": true, "type": "module", "dependencies": {"@chatui/core": "^2.4.2", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@heroicons/vue": "^2.1.1", "@iconify/vue": "^4.1.2", "@vueuse/core": "^10.9.0", "amis": "6.9.0", "axios": "^1.4.0", "element-plus": "^2.8.8", "lodash-es": "^4.17.21", "mitt": "3.0.1", "pinia": "^2.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "vue": "^3.4.34", "vue-i18n": "9", "vue-router": "4.3.0"}, "devDependencies": {"@types/node": "^20.12.8", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-vue": "^5.0.4", "sass": "^1.71.1", "typescript": "^5.4.5", "vite": "^5.1.4"}, "merge-plugin": {"include": ["Modules/*/package.json"]}}