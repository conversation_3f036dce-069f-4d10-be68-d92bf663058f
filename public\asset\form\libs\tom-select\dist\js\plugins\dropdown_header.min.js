(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.dropdown_header=factory())})(this,function(){"use strict";const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const getDom=query=>{if(query.jquery){return query[0]}if(query instanceof HTMLElement){return query}if(isHtmlString(query)){var tpl=document.createElement("template");tpl.innerHTML=query.trim();return tpl.content.firstChild}return document.querySelector(query)};const isHtmlString=arg=>{if(typeof arg==="string"&&arg.indexOf("<")>-1){return true}return false};const preventDefault=(evt,stop=false)=>{if(evt){evt.preventDefault();if(stop){evt.stopPropagation()}}};function plugin(userOptions){const self=this;const options=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:data=>{return'<div class="'+data.headerClass+'">'+'<div class="'+data.titleRowClass+'">'+'<span class="'+data.labelClass+'">'+data.title+"</span>"+'<a class="'+data.closeClass+'">&times;</a>'+"</div>"+"</div>"}},userOptions);self.on("initialize",()=>{var header=getDom(options.html(options));var close_link=header.querySelector("."+options.closeClass);if(close_link){close_link.addEventListener("click",evt=>{preventDefault(evt,true);self.close()})}self.dropdown.insertBefore(header,self.dropdown.firstChild)})}return plugin});