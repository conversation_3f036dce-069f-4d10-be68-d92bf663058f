<?php

declare(strict_types=1);

namespace Modules\Common\Listeners;

use Modules\Common\Events\MultiSiteSyncRequested;
use Modules\Common\Services\MultiSiteSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class MultiSiteSyncListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private readonly MultiSiteSyncService $syncService
    ) {}

    /**
     * 处理多站点同步事件
     */
    public function handle(MultiSiteSyncRequested $event): void
    {
        try {
            Log::info('开始处理多站点同步', [
                'module' => $event->module,
                'dataType' => $event->dataType,
                'dataId' => $event->dataId,
                'sourceSiteId' => $event->sourceSiteId,
                'targetSiteIds' => $event->targetSiteIds,
            ]);

            // 创建回调函数
            $saveCallback = $this->createSaveCallback(
                $event->callbackClass,
                $event->callbackMethod
            );
            $this->syncService->syncToSites(
                $event->module,
                $event->dataType,
                $event->dataId,
                $event->data,
                $event->targetSiteIds,
                $saveCallback,
                $event->options
            );

            Log::info('多站点同步完成');
        } catch (\Exception $e) {
            Log::error('多站点同步失败', [
                'error' => $e->getMessage(),
                'module' => $event->module,
                'dataType' => $event->dataType,
                'dataId' => $event->dataId,
            ]);
            
            throw $e;
        }
    }

    /**
     * 创建保存回调函数
     */
    protected function createSaveCallback(string $callbackClass, string $callbackMethod): callable
    {
        return function (array $data) use ($callbackClass, $callbackMethod) {
            $instance = app($callbackClass);
            return $instance->$callbackMethod($data);
        };
    }

    /**
     * 处理失败的任务
     */
    public function failed(MultiSiteSyncRequested $event, \Throwable $exception): void
    {
        Log::error('多站点同步任务失败', [
            'module' => $event->module,
            'dataType' => $event->dataType,
            'dataId' => $event->dataId,
            'error' => $exception->getMessage(),
        ]);
    }
}
