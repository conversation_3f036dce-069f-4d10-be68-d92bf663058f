<template>
  <el-dialog class="el-dialog-common-cls" v-model="visible" title="FAQ詳情" width="700">
    <el-tabs v-model="activeLang" class="faq-detail-tabs" @tab-change="onTabChange">
      <el-tab-pane name="zh_HK" label="繁體中文">
        <template v-if="faq.zh_HK">
          <el-form label-position="top">
            <el-form-item label="FAQ 編號">
              <el-input v-model="faq.zh_HK.faqNumber" readonly />
            </el-form-item>
            <el-form-item label="問題標題">
              <el-input v-model="faq.zh_HK.title" readonly />
            </el-form-item>
            <el-form-item label="分類">
              <el-input v-model="faq.zh_HK.category" readonly />
            </el-form-item>
            <el-form-item label="狀態">
              <el-input v-model="faq.zh_HK.status" readonly />
            </el-form-item>
            <el-form-item label="更新時間">
              <el-input v-model="faq.zh_HK.updateTime" readonly />
            </el-form-item>
            <el-form-item label="內容">
              <div style="min-height:120px; width: 100%; border:1px solid #e4e7ed; border-radius:4px; padding:12px; background:#fafbfc; color:#333;" v-html="faq.zh_HK.content"></div>
            </el-form-item>
          </el-form>
        </template>
        <template v-else>
          <el-empty description="暫無繁體中文內容" />
        </template>
      </el-tab-pane>
      <el-tab-pane name="zh_CN" label="簡體中文">
        <template v-if="faq.zh_CN">
          <el-form label-position="top">
            <el-form-item label="FAQ 編號">
              <el-input v-model="faq.zh_CN.faqNumber" readonly />
            </el-form-item>
            <el-form-item label="問題標題">
              <el-input v-model="faq.zh_CN.title" readonly />
            </el-form-item>
            <el-form-item label="分類">
              <el-input v-model="faq.zh_CN.category" readonly />
            </el-form-item>
            <el-form-item label="狀態">
              <el-input v-model="faq.zh_CN.status" readonly />
            </el-form-item>
            <el-form-item label="更新時間">
              <el-input v-model="faq.zh_CN.updateTime" readonly />
            </el-form-item>
            <el-form-item label="內容">
              <div style="min-height:120px; width: 100%; border:1px solid #e4e7ed; border-radius:4px; padding:12px; background:#fafbfc; color:#333;" v-html="faq.zh_CN.content"></div>
            </el-form-item>
          </el-form>
        </template>
        <template v-else>
          <el-empty description="暫無簡體中文內容" />
        </template>
      </el-tab-pane>
      <el-tab-pane name="en" label="英文">
        <template v-if="faq.en">
          <el-form label-position="top">
            <el-form-item label="FAQ 編號">
              <el-input v-model="faq.en.faqNumber" readonly />
            </el-form-item>
            <el-form-item label="問題標題">
              <el-input v-model="faq.en.title" readonly />
            </el-form-item>
            <el-form-item label="分類">
              <el-input v-model="faq.en.category" readonly />
            </el-form-item>
            <el-form-item label="狀態">
              <el-input v-model="faq.en.status" readonly />
            </el-form-item>
            <el-form-item label="更新時間">
              <el-input v-model="faq.en.updateTime" readonly />
            </el-form-item>
            <el-form-item label="內容">
              <div style="min-height:120px; width: 100%; border:1px solid #e4e7ed; border-radius:4px; padding:12px; background:#fafbfc; color:#333;" v-html="faq.en.content"></div>
            </el-form-item>
          </el-form>
        </template>
        <template v-else>
          <el-empty description="No English content" />
        </template>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: 'DetailList'
}
</script>

<script setup lang="ts">
import { ref, watch, defineProps, defineExpose } from 'vue'
import http from '/admin/support/http'

const props = defineProps<{
  modelValue: boolean,
  faqId: number|null
}>()

const visible = ref(false)
const activeLang = ref('zh_HK')
const faq = ref<{ [lang: string]: any }>({})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.faqId) {
    fetchAllLangFaq(props.faqId)
  }
})

watch(() => props.faqId, (id) => {
  if (visible.value && id) {
    fetchAllLangFaq(id)
  }
})

const fetchAllLangFaq = async (id: number) => {
  const langs = ['zh_HK', 'zh_CN', 'en']
  const result: { [lang: string]: any } = {}
  await Promise.all(langs.map(async lang => {
    try {
      const res = await http.get(`/admin/faq/${id}?lang=${lang}`)
      if (res.data && res.data.code === 200) {
        result[lang] = res.data.data
      }
    } catch {}
  }))
  faq.value = result
}

const onTabChange = (lang: string) => {
  activeLang.value = lang
}

defineExpose({ visible })
</script>

<style scoped>
.faq-detail-tabs {
  margin-bottom: 16px;
  margin-top: -10px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
</style>
