<template>
    <div class="amis-page" style="margin-top:40px; padding: 0 34px 34px;">
        <transition name="slide-up" mode="out-in">
            <AMisRenderer
                :key="route.path"
                :amis-json="thisPage.pageJson"
                v-if="!thisPage.error && thisPage.pageJson"
            />
        </transition>
        <div v-if="thisPage.error && !thisPage.loading">
            <el-alert :closable="false" type="error" show-icon>{{ thisPage.errorMessage }}</el-alert>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted, watch } from 'vue'
import AMisRenderer from '/admin/components/amis/AMisRenderer.vue'
import { storeToRefs } from 'pinia'
import { usePagesStore } from '/admin/stores/pages'
import { ElAlert } from 'element-plus'

const { thisPage } = storeToRefs(usePagesStore())
const { getPageJson } = usePagesStore()
const route = useRoute()

// 监听路由变化，更新页面 JSON 数据
watch(
    () => route.fullPath,
    async (fullPath: string) => {
        await getPageJson(fullPath)
    },
)

onMounted(async () => {
    await getPageJson(route.fullPath)
})
</script>

