@extends($_viewFrame)

@section('pageTitle'){{(!empty($pageTitle)?$pageTitle.' - ':'').bingostart_config('siteName')}}@endsection

@section('bodyContent')
    <div class="container">
        <div class="left-side">
            @foreach(\Modules\Member\Domain\Config\MemberMenu::get() as $menu)
                <div class="ul">
                    <div class="li th">{{$menu['title']}}</div>
                    @foreach($menu['children'] as $item)
                        <div class="li {{bingostart_baseurl_active($item['url'])}}">
                            <a href="{{$item['url']}}">{{$item['title']}}</a>
                        </div>
                    @endforeach
                </div>
            @endforeach
        </div>

        <div class="right-content">
            @section('memberBodyContent')
            @show
        </div>
    </div>
@endsection
