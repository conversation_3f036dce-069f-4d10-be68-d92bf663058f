export const downloadResourceTemplate = `
<div class="download-resource-page responsive-block">
  <!-- 主要内容区域 -->
  <div class="py-5">
    <div class="container">
      <div class="row">
        <!-- 左侧内容区域 -->
        <div class="mb-4 col-lg-6 mb-lg-0">
          <!-- 标签 -->
          <div data-bs-component="bootstrap-badge" class="mb-4">
            <span class="badge bg-dark">iShot (图片编辑器)</span>
          </div>
          
          <!-- 标题 -->
          <div data-bs-component="bootstrap-heading">
            <h1 class="mb-4 display-4 fw-bold">Download your digital resource</h1>
          </div>
          
          <!-- 描述文本 -->
          <div data-bs-component="richTextBlock" class="mb-4">
            <p>Address your reader's challenges or goals and explain how the resource will help solve them</p>
          </div>
          
          <!-- 资源图片 -->
          <div class="mb-4 resource-image-container">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/marketing-playbook.webp" alt="Digital Resource" class="img-fluid resource-image">
          </div>
        </div>
        
        <!-- 右侧表单区域 -->
        <div class="col-lg-6">
          <div class="form-container">
            <!-- 表单字段 -->
            <div data-bs-component="form-field" class="mb-3">
              <label for="firstName" class="form-label">First Name</label>
              <input type="text" class="form-control" id="firstName" placeholder="">
            </div>
            
            <div data-bs-component="form-field" class="mb-3">
              <label for="lastName" class="form-label">Last Name</label>
              <input type="text" class="form-control" id="lastName" placeholder="">
            </div>
            
            <div data-bs-component="form-field" class="mb-3">
              <label for="company" class="form-label">Company</label>
              <input type="text" class="form-control" id="company" placeholder="">
            </div>
            
            <div data-bs-component="form-field" class="mb-4">
              <label for="email" class="form-label">Email</label>
              <input type="email" class="form-control" id="email" placeholder="">
            </div>
            
            <!-- 提交按钮 -->
            <div data-bs-component="bootstrap-button" class="mb-3">
              <button class="py-3 btn btn-primary w-100">Get Free Widget</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 客户推荐轮播 -->
  <div data-bs-component="testimonial-slider" class="py-5 testimonial-slider-block">
    <div class="container testimonial-slider-container">
      <div class="testimonial-slider">
        <button class="nav-button prev-button"><i class="fas fa-chevron-left"></i></button>
        <div class="testimonial-slide active">
          <div class="row">
            <div class="mb-3 text-center col-md-3 mb-md-0">
              <div class="testimonial-avatar-wrapper">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="testimonial-avatar" alt="Testimonial avatar">
              </div>
            </div>
            <div class="col-md-9">
              <div data-bs-component="richTextBlock">
                <p class="mb-4 lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
                <h5 class="mb-1">Neil Kumar</h5>
                <p class="text-muted">VP of Marketing @ Lyrakonics</p>
              </div>
              <div data-bs-component="bootstrap-button">
                <a href="#" class="text-decoration-none">Read case study →</a>
              </div>
            </div>
          </div>
        </div>
        <button class="nav-button next-button"><i class="fas fa-chevron-right"></i></button>
      </div>
      <div class="slider-dots">
        <span class="dot active" data-slide="0"></span>
        <span class="dot" data-slide="1"></span>
        <span class="dot" data-slide="2"></span>
      </div>
    </div>
    
    <style>
    .testimonial-slider-block {
      position: relative;
      padding: 40px 0;
      width: 100%;
      background-color: #f8f9fa;
    }
    
    .testimonial-slider-container {
      max-width: 100%;
      margin: 0 auto;
      padding: 20px;
      overflow: hidden;
    }
    
    .testimonial-slider {
      position: relative;
      width: 100%;
      min-height: 300px;
      display: flex;
      align-items: center;
    }
    
    .testimonial-slide {
      display: none;
      width: 100%;
      padding: 30px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 15px rgba(0,0,0,0.08);
      transition: all 0.3s ease;
    }
    
    .testimonial-slide.active {
      display: block;
    }
    
    .nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: #fff;
      border: none;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .prev-button {
      left: -20px;
    }
    
    .next-button {
      right: -20px;
    }
    
    .nav-button:hover {
      background-color: #6c5ce7;
      color: white;
      box-shadow: 0 4px 12px rgba(108,92,231,0.3);
      transform: translateY(-50%) scale(1.1);
    }
    
    .slider-dots {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 20px;
    }
    
    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #d1d1d1;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .dot.active {
      background-color: #6c5ce7;
      transform: scale(1.2);
    }

    .testimonial-avatar-wrapper {
      width: 120px;
      height: 120px;
      margin: 0 auto;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid #fff;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .testimonial-avatar {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    .testimonial-avatar:hover {
      transform: scale(1.05);
    }
    
    @media (max-width: 767.98px) {
      .testimonial-slider-block {
        padding: 20px 0;
      }

      .testimonial-slider-container {
        padding: 10px;
      }

      .testimonial-slide {
        padding: 15px;
      }

      .nav-button {
        width: 36px;
        height: 36px;
      }

      .prev-button {
        left: 5px;
      }

      .next-button {
        right: 5px;
      }

      .testimonial-avatar-wrapper {
        width: 100px;
        height: 100px;
      }
    }
    </style>
  </div>
  
  <!-- 营销特性展示 -->
  <div data-bs-component="marketing-banner" class="py-5 text-white marketing-banner bg-dark">
    <div class="container">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <div class="me-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/document-icon.webp" alt="Document Icon" class="icon-image" style="width: 48px; height: 48px;">
          </div>
          <div>
            <h3 class="mb-0">All your marketing needs in one place</h3>
          </div>
        </div>
        <div data-bs-component="bootstrap-button">
          <a href="#" class="btn btn-outline-light">Start free trial →</a>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 常见问题 -->
  <div data-bs-component="bootstrap-accordion" class="py-5 bootstrap-accordion">
    <div class="container">
      <div data-bs-component="bootstrap-heading">
        <h2 class="mb-4 text-center fw-bold">Frequently asked questions</h2>
      </div>
      <div class="accordion" id="faqAccordion">
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
              What services does your digital marketing agency offer?
            </button>
          </h2>
          <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              We offer a comprehensive suite of digital marketing services including social media management, email marketing, SEO optimization, content creation, and performance analytics.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
              How can your digital marketing agency help my business grow?
            </button>
          </h2>
          <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency helps businesses increase their online visibility, attract more qualified leads, engage with their target audience effectively, and convert prospects into loyal customers.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
              Do you provide customized solutions for different businesses?
            </button>
          </h2>
          <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Yes, we tailor our digital marketing strategies to match your specific business goals, target audience, and industry requirements, ensuring you receive personalized solutions.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
              Can you track the performance of digital marketing campaigns?
            </button>
          </h2>
          <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Absolutely! We provide comprehensive analytics and reporting that track key performance indicators, conversion rates, engagement metrics, and ROI for all your marketing campaigns.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
              What sets your digital marketing agency apart from others in the industry?
            </button>
          </h2>
          <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency stands out through our data-driven approach, transparent communication, industry expertise, customized strategies, and measurable results that help our clients achieve sustainable growth.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 页脚 -->
  <div data-bs-component="footer" class="py-4 text-white footer bg-dark">
    <div class="container text-center">
      <div data-bs-component="richTextBlock">
        <p class="mb-0">© 2025. All rights reserved.</p>
      </div>
    </div>
  </div>
</div>

<style>
/* 基础样式 */
.download-resource-page {
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 表单容器样式 */
.form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

/* 按钮样式 */
.btn-primary {
  background-color: #6c5ce7;
  border-color: #6c5ce7;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #5d4fd6;
  border-color: #5d4fd6;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(108, 92, 231, 0.3);
}

/* 资源图片样式 */
.resource-image-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.resource-image {
  width: 100%;
  height: auto;
  transition: transform 0.5s ease;
}

.resource-image:hover {
  transform: scale(1.02);
}

/* 营销横幅样式 */
.marketing-banner {
  background-color: #1e1e2d;
}

.marketing-banner .btn-outline-light:hover {
  background-color: #fff;
  color: #1e1e2d;
}

/* 响应式样式 */
@media (max-width: 991.98px) {
  .form-container {
    margin-top: 30px;
  }
}

@media (max-width: 767.98px) {
  .download-resource-page h1 {
    font-size: 2rem;
  }
  
  .marketing-banner .d-flex {
    flex-direction: column;
    text-align: center;
  }
  
  .marketing-banner .btn {
    margin-top: 15px;
  }
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // 轮播功能
  const slider = document.querySelector('.testimonial-slider');
  const slides = document.querySelectorAll('.testimonial-slide');
  const dots = document.querySelectorAll('.dot');
  const prevButton = document.querySelector('.prev-button');
  const nextButton = document.querySelector('.next-button');
  let currentSlide = 0;
  
  // 如果存在多个轮播项
  if(slides.length > 1) {
    function showSlide(index) {
      slides.forEach(slide => slide.classList.remove('active'));
      dots.forEach(dot => dot.classList.remove('active'));
      
      slides[index].classList.add('active');
      dots[index].classList.add('active');
      currentSlide = index;
    }
    
    function nextSlide() {
      currentSlide = (currentSlide + 1) % slides.length;
      showSlide(currentSlide);
    }
    
    function prevSlide() {
      currentSlide = (currentSlide - 1 + slides.length) % slides.length;
      showSlide(currentSlide);
    }
    
    // 添加事件监听器
    if(dots.length > 0) {
      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
      });
    }
    
    if(prevButton) prevButton.addEventListener('click', prevSlide);
    if(nextButton) nextButton.addEventListener('click', nextSlide);
    
    // 自动轮播
    const autoSlide = setInterval(nextSlide, 5000);
    
    // 用户交互时停止自动轮播
    [prevButton, nextButton, ...dots].forEach(el => {
      if(el) {
        el.addEventListener('click', () => {
          clearInterval(autoSlide);
        });
      }
    });
  }
  
  // 表单验证
  const form = document.querySelector('.form-container');
  const submitButton = form ? form.querySelector('button[type="submit"]') : null;
  
  if(form && submitButton) {
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      
      // 简单验证
      const email = form.querySelector('#email');
      if(email && !email.value.includes('@')) {
        alert('Please enter a valid email address');
        return;
      }
      
      // 提交成功后的操作
      alert('Thank you for your submission! Your download will start shortly.');
    });
  }
  
  // 动画效果
  const animateElements = document.querySelectorAll('.resource-image-container, .form-container, .testimonial-slider-block');
  
  function checkScroll() {
    animateElements.forEach(element => {
      const elementTop = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if(elementTop < windowHeight * 0.8) {
        element.classList.add('animated');
      }
    });
  }
  
  // 初始检查
  checkScroll();
  
  // 滚动时检查
  window.addEventListener('scroll', checkScroll);
});
</script>
`
