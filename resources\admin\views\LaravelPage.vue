<template>
    <div class="external-page-container">
        <iframe :src="url" width="100%" height="100%"></iframe>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

import { env, getAuthToken } from '/admin/support/helper'

const route = useRoute()
const url = ref('')

// 动态设置 iframe 的源 URL
watch(() => route.fullPath, (newPath) => {
    const token = getAuthToken()
    const baseURL = env('VITE_BASE_URL')
    const cleanedPath = newPath.replace(/^\/+/, '')

    // 使用 URL 对象来拼接 URL 和参数
    const fullURL = new URL(`${baseURL}${cleanedPath}`)
    fullURL.searchParams.append('token', typeof token === 'string' ? token : '')

    url.value = fullURL.toString()
}, { immediate: true })

</script>

<style scoped>
.external-page-container {
    width: 100%;
    height: 1100px;
}
</style>
