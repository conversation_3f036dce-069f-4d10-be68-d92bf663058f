$(document).ready(function() {
    // Handlers
    toggleShared = function(e) {
        if (e.val() === '0' || e.val() === '1') {
            $('.field-template-users').hide()
        } else if (e.val() === '2') {
            $('.field-template-users').show()
        }
    }
    $('#template-shared').find('.btn').on('click', function(e) {
        toggleShared($(this).prev())
    })
    toggleShared($('[name$="Template[shared]"]:checked'))
})


jQuery(function($) {
    var $el = jQuery('#w0 .kv-hint-special')
    if ($el.length) {
        $el.each(function() {
            $(this).activeFieldHint()
        })
    }
    kvBs4InitForm()
    jQuery && jQuery.pjax && (jQuery.pjax.defaults.maxCacheLength = 0)
    if (jQuery('#template-category_id').data('select2')) {
        jQuery('#template-category_id').select2('destroy')
    }
    jQuery.when(jQuery('#template-category_id').select2(select2_e15eb275)).done(initS2Loading('template-category_id', 's2options_fd268a17'))

    new TomSelect('#template-created_by', {
        'openOnFocus': false, 'create': false, 'multiple': false, 'selectOnTab': true, 'onType':
            function(str) {
                if (str.length < 3) {
                    $(this.dropdown).hide()
                } else {
                    $(this.dropdown).show()
                }
            }, 'valueField': 'id', 'labelField': 'username', 'searchField': 'username', 'load':
            function(query, callback) {
                var url = options.hasPrettyUrls
                    ? options.userListUrl + '?q=' + encodeURIComponent(query)
                    : options.userListUrl + '&q=' + encodeURIComponent(query)
                fetch(url)
                    .then(function(response) {
                        return response.json()
                    })
                    .then(function(json) {
                        callback(json.items)
                    })
                    .catch(() => {
                        callback()
                    })
            },
    })
    new TomSelect('#template-users', {
        'openOnFocus': false, 'create': false, 'multiple': true, 'selectOnTab': true, 'onType':
            function(str) {
                if (str.length < 3) {
                    $(this.dropdown).hide()
                } else {
                    $(this.dropdown).show()
                }
            }, 'valueField': 'id', 'labelField': 'username', 'searchField': 'username', 'load':
            function(query, callback) {
                var url = '/ajax/user-list?e=1&q=' + encodeURIComponent(query)
                var url = options.hasPrettyUrls
                    ? options.userListUrl + '?q=' + encodeURIComponent(query) + '&e=' + '1'
                    : options.userListUrl + '&q=' + encodeURIComponent(query) + '&e=' + '1'
                fetch(url)
                    .then(function(response) {
                        return response.json()
                    })
                    .then(function(json) {
                        callback(json.items)
                    })
                    .catch(() => {
                        callback()
                    })
            },
    })
    jQuery('#w0').yiiActiveForm([{
        'id': 'template-name',
        'name': 'name',
        'container': '.field-template-name',
        'input': '#template-name',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '名字不能为空。' })
            yii.validation.string(value, messages, {
                'message': '名字必须是一条字符串。',
                'max': 255,
                'tooLong': '名字只能包含至多255个字符。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'template-category_id',
        'name': 'category_id',
        'container': '.field-template-category_id',
        'input': '#template-category_id',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': '类别 ID必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'template-description',
        'name': 'description',
        'container': '.field-template-description',
        'input': '#template-description',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.string(value, messages, {
                'message': '描述必须是一条字符串。',
                'max': 255,
                'tooLong': '描述只能包含至多255个字符。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'template-created_by',
        'name': 'created_by',
        'container': '.field-template-created_by',
        'input': '#template-created_by',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': '创建必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'template-shared',
        'name': 'shared',
        'container': '.field-template-shared',
        'input': '#template-shared',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': 'Shared With必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'template-promoted',
        'name': 'promoted',
        'container': '.field-template-promoted',
        'input': '#template-promoted',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': '促进必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }], [])
})
$('#actions').find('.saveForm').click(function( e ){
    // Do not perform default action when button is clicked
    e.preventDefault();
    $('#actions button').attr("disabled", true); // Disable submit buttons

    // Prepare FormBuilder to POST as JSON
    var data = {};
    $.each($('#w0').serializeArray(), function() {
        data[this.name]= this.value;
    });
    // Send Form Data
    $.ajax({
        method: "POST",
        url: options.endPoint, // From external file configuration
        dataType: 'json',
        data: data
    }).done(function(data) {
        data = data.data;
        $('#actions button').removeAttr("disabled"); // Enable submit buttons

        if (data.success && data.id > 0) {
            // Redirect to another page
            if (options.afterSave === 'redirect' ) {
                window.parent.location.href = options.url;
            }
        } else {

            // Show error message
            $(document).trigger("add-alerts", [
                {
                    'message': "<strong>alert.warning</strong> " + data.message,
                    'priority': 'warning'
                }
            ]);

        }
    }).fail(function(msg){

        // Show error message
        $(document).trigger("add-alerts", [
            {
                'message': "<strong>" + polyglot.t('alert.warning') + "</strong> " + polyglot.t('alert.errorSavingData'),
                'priority': 'warning'
            }
        ]);

    }).always(function(){
    });

});
