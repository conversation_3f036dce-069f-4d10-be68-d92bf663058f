import { User, SecuritySetting, Application } from '../domain/UserCenterDomain'
import axios from 'axios'

export class UserCenterRepository {
  async getUserInfo(): Promise<any> {
    // 使用mock数据
    return {
      id: '123456',
      name: '张三',
      email: '<PERSON><PERSON><PERSON>@example.com',
      phone: '12345678901',
      title: '工程师',
      address: '北京市海淀区',
      gender: '男',
      birthdate: '1990-01-01',
      company: '某公司',
      registrationDate: '2023-10-01',
      idNumber: '110101199001011234',
    }
  }

  async updateUserInfo(user: User): Promise<void> {
    // 模拟更新用户信息
  }

  async getOtpEnrollmentQrCode(profile: { phoneNumber: string; phoneCountryCode: string; email: string }): Promise<string> {
    try {
      const response = await axios.post('/iam/mfa/send-enroll-factor-request', {
        factorType: 'OTP',
        profile,
      })
      return response.data.qrCodeUrl // 假设接口返回的数据中包含 qrCodeUrl 字段
    } catch (error) {
      console.error('Failed to get OTP enrollment QR code:', error)
      throw error
    }
  }

  // 添加其他方法来处理安全设置和应用管理的数据访问
}
