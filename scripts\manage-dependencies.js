const fs = require('fs')
const path = require('path')

// 读取主配置
const mainConfig = require('../resources/admin/main.json')

// 生成根项目的 package.json
function generateRootPackage() {
  const rootPackage = {
    name: mainConfig.name,
    private: true,
    dependencies: {
      ...mainConfig.framework.dependencies,
    },
    devDependencies: {
      ...mainConfig.framework.devDependencies,
    },
  }

  // 添加所有模块的依赖
  Object.entries(mainConfig.modules).forEach(([key, module]) => {
    if (module.dependencies) {
      rootPackage.dependencies = {
        ...rootPackage.dependencies,
        ...module.dependencies,
      }
    }
  })

  fs.writeFileSync(path.resolve(__dirname, '../package.json'), JSON.stringify(rootPackage, null, 2))
}

// 为指定模块生成 package.json
function generateModulePackage(moduleName) {
  const moduleConfig = mainConfig.modules[moduleName]
  if (!moduleConfig) {
    console.error(`Module ${moduleName} not found`)
    return
  }

  const modulePackage = {
    name: `@bwms/${moduleName}-module`,
    version: moduleConfig.version,
    private: true,
    type: 'module',
    dependencies: moduleConfig.dependencies || {},
    peerDependencies: mainConfig.framework.dependencies,
  }

  const modulePath = path.resolve(__dirname, `../Modules/${moduleName}/package.json`)
  fs.writeFileSync(modulePath, JSON.stringify(modulePackage, null, 2))
}

// 执行依赖管理
function manageDependencies() {
  // 生成根项目 package.json
  generateRootPackage()

  // 为每个模块生成 package.json
  Object.keys(mainConfig.modules).forEach(moduleName => {
    generateModulePackage(moduleName)
  })
}

manageDependencies()
