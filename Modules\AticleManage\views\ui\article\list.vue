<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <FilterPopover v-model="filterDialog">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="" />
            </el-icon>
            <span>筛选</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="文章ID">
            <el-input v-model="search.articleId" placeholder="请输入文章ID" size="large" />
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="search.title" placeholder="请输入标题" size="large" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="search.status" placeholder="请选择状态" size="large">
              <el-option label="全部" value="" />
              <el-option label="草稿" value="草稿" />
              <el-option label="已发布" value="已发布" />
              <el-option label="已排程" value="已排程" />
              <el-option label="已归档" value="已归档" />
            </el-select>
          </el-form-item>
          <el-form-item label="作者">
            <el-input v-model="search.author" placeholder="请输入作者" size="large" />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="search.category" placeholder="请选择分类" size="large">
              <el-option label="全部" value="" />
              <el-option label="企业战略" value="企业战略" />
              <el-option label="技术创新" value="技术创新" />
              <el-option label="永续发展" value="永续发展" />
              <el-option label="财务报告" value="财务报告" />
            </el-select>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-date-picker v-model="search.publishTime" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="large" style="width:100%" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="resetFilter">
              <el-icon size="16"><Refresh /></el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="searchArticle" type="primary">
              <el-icon size="16"><Filter /></el-icon>
              <span>筛选</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" @click="addArticle">
        <el-icon><Plus /></el-icon>
        <span>新建文章</span>
      </el-button>
    </div>
    <!-- 主体内容区域 -->
    <div class="module-con">
      <div class="box">
        <el-table :data="articleList" style="width: 100%;" v-loading="loading" :row-class-name="rowClassName">
          <template #empty>
            <el-empty description="暂无数据" image-size="100px" />
          </template>
          <el-table-column prop="id" label="文章ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="300">
            <template #default="scope">
              <div class="article-title">{{ scope.row.title }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="200">
            <template #default="scope">
              <div class="flex">
                <el-tag :type="statusTagType(scope.row.status)">{{ scope.row.status }}</el-tag>
                <span v-if="scope.row.status === '草稿' || scope.row.status === '已发布'" class="status-draft-icon">
                  <el-icon style="margin-right: 4px;" color="#fbbf24"><Lock /></el-icon>
                  <el-icon style="margin-right: 4px;" color="#000"><User /></el-icon>
                  {{ scope.row.author }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="author" label="作者" width="120" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="publishTime" label="发布时间" width="180" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editArticle(scope.row)">
                  <el-icon size="15"><img :src="$asset('Faq/Asset/EditIcon.png')" alt="编辑" /></el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="viewArticle(scope.row)">
                  <el-icon size="15"><img :src="$asset('Faq/Asset/ViewIcon.png')" alt="查看" /></el-icon>
                </el-button>
                <el-button class="bwms-operate-btn del-btn" @click="deleteArticle(scope.row)">
                  <el-icon size="16"><img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="删除" /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每页显示</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="handleSizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">共 {{ total }} 条记录</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Edit, View, MoreFilled, Filter, Search, Refresh, User } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const filterDialog = ref(false)
const page = ref(1)
const limit = ref(10)
const total = ref(24)
const search = reactive({
  articleId: '',
  title: '',
  status: '',
  author: '',
  category: '',
  publishTime: []
})
const articleList = ref([
  { id: 'ART001', title: '2024 年度數位轉型型策略規劃與實施方案', status: '草稿', author: '陳志明', category: '企業戰略', publishTime: '-' },
  { id: 'ART002', title: '人工智能在客戶服務中的應用與效益分析', status: '已发布', author: '林美玲', category: '技術創新', publishTime: '2024-01-15 14:30' },
  { id: 'ART003', title: '永續發展報告：環境社會治理（ESG）實踐案例', status: '已排程', author: '王建國', category: '永續發展', publishTime: '2024-01-20 10:00' },
  { id: 'ART004', title: '2023 年第四季度財務報告分析', status: '已归档', author: '張雅琪', category: '財務報告', publishTime: '2023-12-31 23:59' }
])

function statusTagType(status: any) {
  if (status === '已发布') return 'success'
  if (status === '已排程') return 'info'
  if (status === '已归档') return ''
  return 'warning'
}
function addArticle() {
  // 跳转新建文章
}
function editArticle(row: any) {
  // 编辑逻辑
}
function viewArticle(row: any) {
  // 查看逻辑
}
function deleteArticle(row: any) {
  ElMessageBox.confirm(
    `确定要删除文章「${row.title}」吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 实际删除逻辑
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}
function searchArticle() {
  // 搜索逻辑
}
function resetFilter() {
  Object.assign(search, { articleId: '', title: '', status: '', author: '', category: '', publishTime: [] })
}
function handleCurrentChange(val: any) {
  page.value = val
}
function handleSizeChange(val: any) {
  limit.value = val
}
function rowClassName() {
  return ''
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
    
  }
}
.article-title {
  font-weight: 500;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  cursor: pointer;
}

.status-draft-icon {
  margin-left: 8px;
  color: #000;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
}
</style> 