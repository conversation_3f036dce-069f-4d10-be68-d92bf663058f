export interface User {
  id: string
  name: string
  email: string
  phone: string
  username: string
  title: string
  address: string
  gender: string
  birthdate: string
  company: string
  registrationDate: string
  identityNumber: string
  country: string
  province: string
  city: string
  streetAddress: string
  postalCode: string
  phone_country_code: string
  // 其他可能的字段
}

export interface SecuritySetting {
  // 定义安全设置属性
}

export interface Application {
  id: string
  name: string
  // 其他应用属性
}
