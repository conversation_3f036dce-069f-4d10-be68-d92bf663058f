<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ChatBot</title>
    <script>
        // 立即拦截所有可能的错误输出
        window.errors = [];
        window.consoleError = console.error;
        window.addEventListener('error', function (e) {
            e.preventDefault();
            return false;
        }, true);

        console.error = function () { };

        // 延迟重置console.error，但保持对特定错误的拦截
        setTimeout(function () {
            console.error = function (...args) {
                const errorString = String(args[0] || '');
                if (
                    errorString.includes('useEffect') ||
                    errorString.includes('React') ||
                    errorString.includes('Cannot read properties') ||
                    errorString.includes('null')
                ) {
                    return;
                }
                window.consoleError.apply(console, args);
            };
        }, 0);
    </script>
    <link rel="stylesheet" href="https://dev-bwms.bingo-test.com/chatbot/frontend-chatbot.css">
</head>

<body>
    <div id="app"></div>
    <script src="./config.js"></script>
    <script src="./frontend-chatbot.umd.js"></script>
    <script src="//g.alicdn.com/chatui/icons/2.0.2/index.js"></script>
    <script>
        window.chatWidgetConfig = {
            url: window.process.env.VITE_FRONTEND_API_BASE_URL,
            // token: localStorage.getItem("iam_token"),   //根据需要添加
            // appCode: localStorage.getItem("app_code"),   //根据需要添加
            // userId: 1  //根据需要添加
            // language: localStorage.getItem("bwms_language"), // 根据需要添加
        };
    </script>
</body>

</html>
