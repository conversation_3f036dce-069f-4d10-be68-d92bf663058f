// 基础消息类型
export interface BaseMessage {
  id: string
  type: 'bot' | 'user'
  time: string
  messageType: MessageType
}

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  QUICK_REPLIES = 'quickReplies',
  IMAGE = 'image',
  CARD = 'card',
  FILE = 'file',
  INPUT_FIELD = 'inputField',
  // 可以继续添加更多类型...
}

// 进度数据接口
export interface ProgressData {
  percentage: number
  text: string
  isActive: boolean
  isSingleReplace: boolean
}

// 文本消息
export interface TextMessage extends BaseMessage {
  messageType: MessageType.TEXT
  content: string
  progressData?: ProgressData // 可选的进度数据
}

// 快速回复
export interface QuickReply {
  id: string
  text: string
  action: string
  payload?: any
  data?: any // 添加data属性
  isArticle?: boolean // 标记是否为文章选择按钮
  style?: 'primary' | 'secondary' | 'danger' // 按钮样式类型
}

// 快速回复消息
export interface QuickRepliesMessage extends BaseMessage {
  messageType: MessageType.QUICK_REPLIES
  content: string
  quickReplies: QuickReply[]
}

// 图片消息
export interface ImageMessage extends BaseMessage {
  messageType: MessageType.IMAGE
  url: string
  caption?: string
}

// 卡片消息
export interface CardMessage extends BaseMessage {
  messageType: MessageType.CARD
  title: string
  description: string
  image?: string
  buttons?: Array<{
    text: string
    action: string
    payload?: any
  }>
}

// 文件消息
export interface FileMessage extends BaseMessage {
  messageType: MessageType.FILE
  fileName: string
  fileUrl: string
  fileSize?: number
  fileType?: string
}

// 输入框配置
export interface InputFieldConfig {
  type: 'text' | 'textarea' | 'number'
  placeholder?: string
  required?: boolean
  submitText?: string
  cancelText?: string
  maxLength?: number
  rows?: number
}

// 输入框消息
export interface InputFieldMessage extends BaseMessage {
  messageType: MessageType.INPUT_FIELD
  content: string
  inputField: InputFieldConfig
  quickReplies?: QuickReply[]
}

// 所有消息类型的联合类型
export type ChatMessage = 
  | TextMessage 
  | QuickRepliesMessage 
  | ImageMessage 
  | CardMessage 
  | FileMessage
  | InputFieldMessage 