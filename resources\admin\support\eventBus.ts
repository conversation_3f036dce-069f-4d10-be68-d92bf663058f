import { reactive, readonly, onUnmounted } from 'vue';

interface EventBus {
  $on: (event: string, handler: (...args: any[]) => void) => void;
  $off: (event: string, handler: (...args: any[]) => void) => void;
  $emit: (event: string, ...args: any[]) => void;
}

const eventBus = reactive(new Map<string, Set<(...args: any[]) => void>>());

function useEventBus(): EventBus {
  function $on(event: string, handler: (...args: any[]) => void): void {
    if (!eventBus.has(event)) {
      eventBus.set(event, new Set());
    }
    eventBus.get(event)!.add(handler);
  }

  function $emit(event: string, ...args: any[]): void {
    if (eventBus.has(event)) {
      eventBus.get(event)!.forEach(handler => {
        handler(...args);
      });
    }
  }

  function $off(event: string, handler: (...args: any[]) => void): void {
    if (eventBus.has(event)) {
      const handlers = eventBus.get(event)!;
      handlers.delete(handler);
      if (handlers.size === 0) {
        eventBus.delete(event);
      }
    }
  }

  return { $on, $off, $emit };
}

export default useEventBus;
