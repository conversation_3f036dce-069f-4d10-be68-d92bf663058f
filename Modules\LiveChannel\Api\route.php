<?php

use Illuminate\Support\Facades\Route;
use Modules\LiveChannel\Api\Controllers\LiveChannelController;
use Modules\Common\Middleware\LanguageMiddleware;

/*
|--------------------------------------------------------------------------
| LiveChannel API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the LiveChannel module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 管理后台路由
Route::prefix('admin')->middleware([LanguageMiddleware::class])->group(function () {
    Route::prefix('live-channel')->group(function () {
        // 基础CRUD操作
        Route::get('/', [LiveChannelController::class, 'index'])->name('live-channel.index');
        Route::post('/', [LiveChannelController::class, 'store'])->name('live-channel.store');
        Route::get('/{id}', [LiveChannelController::class, 'show'])->name('live-channel.show');
        Route::put('/{id}', [LiveChannelController::class, 'update'])->name('live-channel.update');
        Route::delete('/{id}', [LiveChannelController::class, 'destroy'])->name('live-channel.destroy');
        
        // 批量操作
        Route::prefix('batch')->group(function () {
            Route::delete('/', [LiveChannelController::class, 'batchDelete'])->name('live-channel.batch_delete');
            Route::put('/status', [LiveChannelController::class, 'batchUpdateStatus'])->name('live-channel.batch_update_status');
        });
        
        // 状态管理
        Route::put('/{id}/live-status', [LiveChannelController::class, 'updateLiveStatus'])->name('live-channel.update_live_status');
        
        // 直播配置绑定
        Route::post('/bind-stream-config', [LiveChannelController::class, 'bindStreamConfig'])->name('live-channel.bind_stream_config');
        
        // 获取频道选项
        Route::get('/options', [LiveChannelController::class, 'getChannelOptions'])->name('live-channel.options');
        
        // 特殊查询
        Route::get('/live/list', [LiveChannelController::class, 'getLiveChannels'])->name('live-channel.live_list');
        Route::get('/statistics', [LiveChannelController::class, 'getStatistics'])->name('live-channel.statistics');
    });
});

// API路由
Route::prefix('api/admin')->middleware([LanguageMiddleware::class])->group(function () {
    Route::prefix('live-channel')->group(function () {
        // 基础CRUD操作
        Route::get('/', [LiveChannelController::class, 'index'])->name('live-channel.api.index');
        Route::post('/', [LiveChannelController::class, 'store'])->name('live-channel.api.store');
        Route::get('/{id}', [LiveChannelController::class, 'show'])->name('live-channel.api.show');
        Route::put('/{id}', [LiveChannelController::class, 'update'])->name('live-channel.api.update');
        Route::delete('/{id}', [LiveChannelController::class, 'destroy'])->name('live-channel.api.destroy');
        
        // 批量操作
        Route::prefix('batch')->group(function () {
            Route::delete('/', [LiveChannelController::class, 'batchDelete'])->name('live-channel.api.batch_delete');
            Route::put('/status', [LiveChannelController::class, 'batchUpdateStatus'])->name('live-channel.api.batch_update_status');
        });
        
        // 状态管理
        Route::put('/{id}/live-status', [LiveChannelController::class, 'updateLiveStatus'])->name('live-channel.api.update_live_status');
        
        // 直播配置绑定
        Route::post('/bind-stream-config', [LiveChannelController::class, 'bindStreamConfig'])->name('live-channel.api.bind_stream_config');
        
        // 获取频道选项
        Route::get('/options', [LiveChannelController::class, 'getChannelOptions'])->name('live-channel.api.options');
        
        // 特殊查询
        Route::get('/live/list', [LiveChannelController::class, 'getLiveChannels'])->name('live-channel.api.live_list');
        Route::get('/statistics', [LiveChannelController::class, 'getStatistics'])->name('live-channel.api.statistics');
    });
}); 