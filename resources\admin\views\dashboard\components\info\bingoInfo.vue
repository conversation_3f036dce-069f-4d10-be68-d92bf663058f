<template>
  <div class="bingo-info">
    <!-- 左侧 Swiper -->
    <div class="swiper-section">
      <el-carousel 
        :interval="5000" 
        arrow="always" 
        height="237px"
        indicator-position="outside"
        class="custom-carousel"
      >
      <el-carousel-item>
        <img :src="$asset('Dashboard/Asset/banner-big22.png')" alt="">
        <!-- <div class="carousel-bg" :class="{ 'big-screen': isLargeScreen }"></div> -->
      </el-carousel-item>  
      <el-carousel-item>
          <img :src="$asset('Dashboard/Asset/banner-big3.png')" alt="">
          <!-- <div class="carousel-bg" :class="{ 'big-screen': isLargeScreen }"></div> -->
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 右侧热门课题 -->
    <div class="hot-topics">
      <h3 class="section-title">熱門課題</h3>
      <div class="topics-grid">
        <div class="topic-item" @click="handleTopicClick('hubspot')">
          <img :src="$asset('Dashboard/Asset/hubspot.png')" alt="HubSpot" />
        </div>
        <div class="topic-item" @click="handleTopicClick('salesforce')">
          <img :src="$asset('Dashboard/Asset/salesforce.png')" alt="Salesforce" />
        </div>
        <div class="topic-item" @click="handleTopicClick('shopify')">
          <img :src="$asset('Dashboard/Asset/web-no.png')" alt="webNo" />
        </div>
        <div class="topic-item" @click="handleTopicClick('shopify')">
          <img :src="$asset('Dashboard/Asset/shopify.png')" alt="Shopify" />
        </div>
        <div class="topic-item" @click="handleTopicClick('google-analytics')">
          <img :src="$asset('Dashboard/Asset/google-analytics.png')" alt="Google Analytics" />
        </div>
        <div class="topic-item view-more" @click="handleViewMore">
          <span>查看更多</span>
          <el-icon size="14" color="#37A0EA"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const isLargeScreen = ref(window.innerWidth >= 1600)

// 监听窗口大小变化
const handleResize = () => {
  isLargeScreen.value = window.innerWidth >= 1600
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 处理主题点击
const handleTopicClick = (topic: string) => {
  // 根据不同主题跳转到相应页面
  switch(topic) {
    case 'hubspot':
      // router.push('/hubspot')
      break
    case 'salesforce':
      // router.push('/salesforce')
      break
    case 'shopify':
      // router.push('/shopify')
      break
    case 'google-analytics':
      // router.push('/google-analytics')
      break
  }
}

// 处理查看更多
const handleViewMore = () => {
  // router.push('/topics')
}
</script>

<style lang="scss" scoped>
.bingo-info {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 307px;
  background: #fff;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 10px;
  overflow: hidden;
  padding: 26px 23px 24px 13px;

  .swiper-section {
    width: 60%;
    flex: none;
    
    .custom-carousel {
      border-radius: 5px;
      overflow: hidden;
      padding: 0 16px;

      :deep(.el-carousel__arrow--left) {
        left: 0;
        top: 45%;
        background: #F8F8F8;
        .el-icon {
          color: #707070;
          font-size: 14px;
        }
      }

      :deep(.el-carousel__arrow--right) {
        right: 0;
        top: 45%;
        background: #F8F8F8;
        .el-icon {
          color: #707070;
          font-size: 14px;
        }
      }

      :deep(.el-carousel__item) {
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 5px;
        }
        .carousel-bg {
          width: 100%;
          height: 100%;
          background-size: 100%;
          background-position: center;
          background-repeat: no-repeat;
          border-radius: 5px;
          background-image: v-bind('`url(${$asset("Dashboard/Asset/banner-big3.png")})`');
          
          &.big-screen {
            background-image: v-bind('`url(${$asset("Dashboard/Asset/banner-big3.png")})`');
          }
        }
      }

      :deep(.el-carousel__indicators) {
        
        .el-carousel__button {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #DFDFDF;
          
          &:hover {
            background-color: #007EE5;
          }
        }
        
        .is-active .el-carousel__button {
          background-color: #007EE5;
        }
      }
    }
  }

  .hot-topics {
    width: 40%;
    flex: none;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #232323;
      margin-bottom: 13px;
      line-height: 21px;
    }

    .topics-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 13px 10px; 

      .topic-item {
        width: 100%;
        height: 66px;
        border: 2px solid #E2E2E2;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }

        &:hover {
          box-shadow: 0 2px 8px rgba(55, 160, 234, 0.2);
        }

        &.view-more {
          background: #F4F9FD;
          border: 2px solid #86C0E9;
          border-radius: 5px;
          color: #37A0EA;
          font-size: 14px;
          font-weight: bold;
          display: flex;
          gap: 8px;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;

          &:hover {
            background: #E8F4FC;
          }
        }
      }
    }
  }
}
</style>
