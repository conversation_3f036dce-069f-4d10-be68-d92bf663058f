<?php
// ========================================
// RBAC权限系统核心类
// ========================================

namespace Modules\Users\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * RBAC权限管理服务类
 * 提供完整的角色权限管理功能
 */
class RBACService
{
    /**
     * 检查用户是否有指定权限
     *
     * @param int $adminId 管理员ID
     * @param string $permissionCode 权限代码
     * @param array $context 上下文信息（如资源ID等）
     * @return bool
     */
    public function hasPermission(int $adminId, string $permissionCode, array $context = []): bool
    {
        // 缓存键
        $cacheKey = "admin_permissions_{$adminId}";

        // 从缓存获取用户权限
        $permissions = Cache::remember($cacheKey, 3600, function () use ($adminId) {
            return $this->getUserPermissions($adminId);
        });

        // 检查是否有指定权限
        if (!in_array($permissionCode, $permissions)) {
            return false;
        }

        // 检查数据权限
        return $this->checkDataPermission($adminId, $permissionCode, $context);
    }

    /**
     * 获取用户所有权限
     *
     * @param int $adminId 管理员ID
     * @return array
     */
    public function getUserPermissions(int $adminId): array
    {
        $permissions = DB::table('admin_roles as ar')
            ->join('role_permissions as rp', 'ar.role_id', '=', 'rp.role_id')
            ->join('permissions as p', 'rp.permission_id', '=', 'p.permission_id')
            ->where('ar.admin_id', $adminId)
            ->where('ar.expires_at', '>', now())
            ->where('p.status', 1)
            ->pluck('p.permission_code')
            ->toArray();

        return array_unique($permissions);
    }

    /**
     * 获取用户菜单权限
     *
     * @param int $adminId 管理员ID
     * @return array
     */
    public function getUserMenus(int $adminId): array
    {
        $menus = DB::table('admin_roles as ar')
            ->join('role_menus as rm', 'ar.role_id', '=', 'rm.role_id')
            ->join('menus as m', 'rm.menu_id', '=', 'm.menu_id')
            ->where('ar.admin_id', $adminId)
            ->where('ar.expires_at', '>', now())
            ->where('m.status', 1)
            ->where('m.is_visible', 1)
            ->orderBy('m.sort_order')
            ->get()
            ->toArray();

        return $this->buildMenuTree($menus);
    }

    /**
     * 检查数据权限
     *
     * @param int $adminId 管理员ID
     * @param string $permissionCode 权限代码
     * @param array $context 上下文信息
     * @return bool
     */
    private function checkDataPermission(int $adminId, string $permissionCode, array $context): bool
    {
        // 获取用户角色
        $roles = DB::table('admin_roles as ar')
            ->join('roles as r', 'ar.role_id', '=', 'r.role_id')
            ->where('ar.admin_id', $adminId)
            ->where('ar.expires_at', '>', now())
            ->where('r.status', 1)
            ->pluck('r.role_code')
            ->toArray();

        // 超级管理员拥有所有数据权限
        if (in_array('super_admin', $roles)) {
            return true;
        }

        // 检查数据权限配置
        $dataPermissions = DB::table('admin_roles as ar')
            ->join('data_permissions as dp', 'ar.role_id', '=', 'dp.role_id')
            ->where('ar.admin_id', $adminId)
            ->where('ar.expires_at', '>', now())
            ->get();

        foreach ($dataPermissions as $dataPermission) {
            if ($this->matchDataPermission($dataPermission, $permissionCode, $context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 匹配数据权限
     *
     * @param object $dataPermission 数据权限对象
     * @param string $permissionCode 权限代码
     * @param array $context 上下文信息
     * @return bool
     */
    private function matchDataPermission(object $dataPermission, string $permissionCode, array $context): bool
    {
        // 检查资源类型是否匹配
        if ($dataPermission->resource_type !== $this->getResourceType($permissionCode)) {
            return false;
        }

        // 根据权限类型进行判断
        switch ($dataPermission->permission_type) {
            case 1: // 全部权限
                return true;
            case 2: // 部门权限
                return $this->checkDepartmentPermission($dataPermission, $context);
            case 3: // 个人权限
                return $this->checkPersonalPermission($dataPermission, $context);
            case 4: // 自定义权限
                return $this->checkCustomPermission($dataPermission, $context);
            default:
                return false;
        }
    }

    /**
     * 获取资源类型
     *
     * @param string $permissionCode 权限代码
     * @return string
     */
    private function getResourceType(string $permissionCode): string
    {
        $parts = explode(':', $permissionCode);
        return $parts[0] ?? '';
    }

    /**
     * 检查部门权限
     *
     * @param object $dataPermission 数据权限对象
     * @param array $context 上下文信息
     * @return bool
     */
    private function checkDepartmentPermission(object $dataPermission, array $context): bool
    {
        // 实现部门权限检查逻辑
        // 这里需要根据具体的业务需求来实现
        return true;
    }

    /**
     * 检查个人权限
     *
     * @param object $dataPermission 数据权限对象
     * @param array $context 上下文信息
     * @return bool
     */
    private function checkPersonalPermission(object $dataPermission, array $context): bool
    {
        // 实现个人权限检查逻辑
        // 检查资源是否属于当前用户
        return true;
    }

    /**
     * 检查自定义权限
     *
     * @param object $dataPermission 数据权限对象
     * @param array $context 上下文信息
     * @return bool
     */
    private function checkCustomPermission(object $dataPermission, array $context): bool
    {
        // 实现自定义权限检查逻辑
        // 根据scope_value中的配置进行判断
        return true;
    }

    /**
     * 构建菜单树
     *
     * @param array $menus 菜单列表
     * @param int $parentId 父级ID
     * @return array
     */
    private function buildMenuTree(array $menus, int $parentId = 0): array
    {
        $tree = [];

        foreach ($menus as $menu) {
            if ($menu->parent_id == $parentId) {
                $children = $this->buildMenuTree($menus, $menu->menu_id);
                if (!empty($children)) {
                    $menu->children = $children;
                }
                $tree[] = $menu;
            }
        }

        return $tree;
    }

    /**
     * 清除用户权限缓存
     *
     * @param int $adminId 管理员ID
     * @return void
     */
    public function clearUserCache(int $adminId): void
    {
        Cache::forget("admin_permissions_{$adminId}");
        Cache::forget("admin_menus_{$adminId}");
    }

    /**
     * 分配角色给用户
     *
     * @param int $adminId 管理员ID
     * @param int $roleId 角色ID
     * @param int $assignedBy 分配人ID
     * @param bool $isPrimary 是否为主角色
     * @param string|null $expiresAt 过期时间
     * @return bool
     */
    public function assignRole(int $adminId, int $roleId, int $assignedBy, bool $isPrimary = false, ?string $expiresAt = null): bool
    {
        try {
            DB::beginTransaction();

            // 如果设置为主角色，先取消其他主角色
            if ($isPrimary) {
                DB::table('admin_roles')
                    ->where('admin_id', $adminId)
                    ->where('is_primary', 1)
                    ->update(['is_primary' => 0]);
            }

            // 分配角色
            DB::table('admin_roles')->insert([
                'admin_id' => $adminId,
                'role_id' => $roleId,
                'is_primary' => $isPrimary,
                'assigned_by' => $assignedBy,
                'expires_at' => $expiresAt,
                'created_at' => now()
            ]);

            // 清除缓存
            $this->clearUserCache($adminId);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * 分配权限给角色
     *
     * @param int $roleId 角色ID
     * @param array $permissionIds 权限ID数组
     * @param int $grantedBy 授权人ID
     * @return bool
     */
    public function assignPermissions(int $roleId, array $permissionIds, int $grantedBy): bool
    {
        try {
            DB::beginTransaction();

            // 删除现有权限
            DB::table('role_permissions')
                ->where('role_id', $roleId)
                ->delete();

            // 分配新权限
            $data = [];
            foreach ($permissionIds as $permissionId) {
                $data[] = [
                    'role_id' => $roleId,
                    'permission_id' => $permissionId,
                    'granted_by' => $grantedBy,
                    'granted_at' => now(),
                    'created_at' => now()
                ];
            }

            if (!empty($data)) {
                DB::table('role_permissions')->insert($data);
            }

            // 清除相关用户缓存
            $this->clearRoleUserCache($roleId);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * 清除角色相关用户缓存
     *
     * @param int $roleId 角色ID
     * @return void
     */
    private function clearRoleUserCache(int $roleId): void
    {
        $adminIds = DB::table('admin_roles')
            ->where('role_id', $roleId)
            ->pluck('admin_id')
            ->toArray();

        foreach ($adminIds as $adminId) {
            $this->clearUserCache($adminId);
        }
    }
}
