$(document).ready(function(){$.when($.getScript("//cdnjs.cloudflare.com/ajax/libs/moment.js/2.8.4/moment.js"),$.getScript("//vitalets.github.io/combodate/combodate.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("form").attr("novalidate",true);var style=$("<style>.combodate { display: block } .form-control-date { display: inline-block; }</style>");$('input[type="date"]').attr("type","text").append(style).combodate({customClass:"form-control form-control-date",format:"YYYY-MM-DD",template:"D MMM YYYY",value:"09-01-2013"})})});