<template>
  <div class="tools-section">
    <el-row :gutter="22">
      <el-col 
        v-for="tool in tools" 
        :key="tool.id" 
        :xs="12"
        :sm="8"
        :md="6"
        :lg="4"
        
      >
        <div class="tool-card" @click="handleToolClick(tool)">
          <div class="icon-wrapper">
            <img :src="$asset(tool.icon)" />
          </div>
          <div class="title">{{ tool.title }}</div>
        </div>
      </el-col>

      <!-- 骨架屏 -->
      <template v-if="loading">
        <el-col 
          v-for="index in 7" 
          :key="'skeleton-' + index"
          :xs="12"
          :sm="8"
          :md="6"
          :lg="4"
        >
          <div class="tool-card skeleton">
            <div class="icon-wrapper"></div>
            <div class="title"></div>
          </div>
        </el-col>
      </template>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { PropType } from 'vue'
import http from '/admin/support/http'
const router = useRouter()
const loading = ref(true)

const props = defineProps({
  tools: {
    type: Array as PropType<Array<{
      id: number
      title: string
      icon: string
      enabled: boolean
    }>>,
    required: true
  }
})

// Mock数据
const tools = ref<any>([])

// 处理标题文本
const processTitle = (title: string): string => {
  // 如果标题长度小于等于4个字符，添加换行
  if (title.length <= 4) {
    return title + '\n'
  }
  return title
}

// 处理工具点击
const handleToolClick = (tool: any) => {
  if (tool.path) {
    if (tool.path.startsWith('http')) {
      window.open(tool.path, '_blank')
    } else {
      router.push(tool.path)
    }
  }
}

const getTools = async () => {
  loading.value = true
  try {
    const res = await http.get('/dashboard/toolsSection/data')
    // 处理数据
    tools.value = res.data.data.map((tool: any) => ({
      ...tool,
      title: processTitle(tool.title),
      link: tool.path // 保持与原有代码兼容
    }))
  } catch (error) {
    console.error('获取工具列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getTools()
})
</script>

<style lang="scss" scoped>
.tools-section {
  .section-title {
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 24px;
  }

  :deep(.el-row) {
    margin-bottom: -22px !important;
    
    .el-col {
      margin-bottom: 22px;
    }
  }

  .tool-card {
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 16px;
    padding: 32px 20px 12px;
    background-color: #fff;
    transition: transform 0.3s ease;
    cursor: pointer;
    height: 100%;

    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      transform: translateY(-2px);
    }

    @media screen and (max-width: 1600px) {
      padding: 20px 20px 10px;
    }

    .icon-wrapper {
      margin-bottom: 14px;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .title {
      font-size: 16px;
      color: #19496a;
      text-align: center;
      line-height: 1.4375;
      font-weight: 500;
      min-height: 46px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: pre-line;

      @media screen and (max-width: 1600px) {
        font-size: 14px;
        min-height: 40px;
      }
    }

    &.skeleton {
      .icon-wrapper {
        background-color: #f5f7fa;
      }

      .title {
        background-color: #f5f7fa;
        width: 100%;
        height: 46px;

        @media screen and (max-width: 1600px) {
          height: 40px;
        }
      }
    }
  }
}
</style>
