<?php

namespace Modules\Common\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Cms\Models\CmsContent;

class ContentGroups extends Model
{
    protected $table = 'content_groups';

    protected $fillable = [
        'id', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    /**
     * 与内容表关联
     * @return HasMany
     */
    public function cmsContents(): HasMany
    {
        return $this->hasMany(CmsContent::class, 'content_group_id');
    }

}
