<template>
  <div class="bwms-module">
    <div class="module-tit">
      <h2>Activity Log</h2>
      <div class="btn-list">
        <div class="btn-box">
          <el-date-picker v-model="time" type="daterange" range-separator="-" start-placeholder="开始时间" format="YYYY/MM/DD" end-placeholder="结束时间" :clearable="false" />
        </div>
        <div class="btn-box" @click="goPage">
          <el-icon size="20" color="#4E4E4E">
            <Right />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="module-con">
      <div class="row">
        <div class="item">
          <div class="box">
            <el-table :data="totalList" style="width: 100%; height: 100%">
              <el-table-column prop="account" :label="t('home.activityLog.th1')" />
              <el-table-column prop="browser" :label="t('home.activityLog.th2')" width="150" />
              <el-table-column prop="platform" :label="t('home.activityLog.th3')" width="150" />
              <el-table-column prop="login_ip" :label="t('home.activityLog.th4')" width="200" />
              <el-table-column prop="status" :label="t('home.activityLog.th5')" width="100">
                <template #default="scope">
                  <el-skeleton :loading="loading" animated>
                    <el-tag type="success" v-if="scope.row.status === 1">{{ $t('system.success') }} </el-tag>
                    <el-tag type="danger" v-else>{{ $t('system.fail') }}</el-tag>
                  </el-skeleton>
                </template>
              </el-table-column>
              <el-table-column prop="login_at" :label="t('home.activityLog.th6')" width="200" />
            </el-table>
            <el-skeleton :loading="loading" animated :rows="8" style="margin-top: -70px"></el-skeleton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface LogItem {
  account: string
  browser: string
  id: number
  login_at: string
  login_ip: string
  platform: string
  status: number
}

const { push } = useRouter()
const api = 'user'
const totalList = ref<LogItem[]>([])
const time = ref(['2024/05/11', '2024/05/17'])
const loading = ref(true)

const goPage = () => {
  push({
    name: 'user-center',
    query: {
      tabsName: 'loginLog',
    },
  } as any)
}

function getLogList() {
  http
    .get(`${api}/login/log`)
    .then(res => {
      totalList.value = res.data.data
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  getLogList()
})
</script>

<script lang="ts">
export default {
  name: 'ActivityLog',
}
</script>

<style lang="scss" scoped>
.bwms-module {
  width: 100%;
  height: 100%;
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  /* 使用新的滚动条隐藏方式 */
  &::-webkit-scrollbar {
    display: none;
  }

  .module-con {
    width: 100%;
    height: 80%;

    .row {
      .item {
        width: 100%;
        height: 100%;
        margin: auto;

        .box {
          padding: 30px 40px 40px;

          /* 使用新的深度选择器语法 */
          :deep(.el-table) {
            --el-table-border-radius: 0;
            --el-table-border-color: #dfdfdf;
            --el-table-header-bg-color: #eaedf7;
            --el-table-header-text-color: #202020;
            --el-table-text-color: #202020;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
