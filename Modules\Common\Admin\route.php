<?php

use Illuminate\Support\Facades\Route;
use Modules\Common\Admin\Controllers\HandleController;
use Modules\Common\Admin\Controllers\OptionController;
use Modules\Common\Admin\Controllers\UploadController;
use Modules\Common\Admin\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('options/modules', [OptionController::class, 'getModules']);
Route::get('options/modulesConfig', [OptionController::class, 'getModulesConfig']);
Route::get('options/{name}', [OptionController::class, 'index']);

Route::get('dashboard/getData', [DashboardController::class, 'index']);



Route::controller(UploadController::class)->group(function () {
    Route::post('upload/file', 'file');
    Route::post('upload/image', 'image');
    // get oss signature
    Route::get('upload/oss', 'oss');
});

Route::any('_handle_action_', [HandleController::class, 'action'])->name('bingo-admin.handle-action');
//Route::post('_handle_upload_image_', [HandleController::class, 'uploadImage'])->name('bingo-admin.handle-upload-image');
//Route::post('_handle_upload_file_', [HandleController::class, 'uploadFile'])->name('bingo-admin.handle-upload-file');
