<?php

namespace Modules\Common\Provider\CensorImage;

use Bingo\Core\Input\Response;

class DefaultCensorImageProvider extends AbstractCensorImageProvider
{
    public function name(): string
    {
        return 'default';
    }

    public function title(): string
    {
        return '无检测';
    }

    public function verify($content, $param = []): array
    {
        return Response::generateSuccessData([
            'pass' => false,
        ]);
    }

}
