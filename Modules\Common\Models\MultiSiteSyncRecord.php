<?php

declare(strict_types=1);

namespace Modules\Common\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Config\Models\Site;

class MultiSiteSyncRecord extends Model
{
    protected $table = 'multi_site_sync_records';

    protected $fillable = [
        'module',
        'data_type',
        'source_site_id',
        'target_site_id',
        'source_data_id',
        'target_data_id',
        'sync_status',
        'sync_languages',
        'error_message',
        'retry_count',
        'last_sync_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'sync_languages' => 'array',
        'retry_count' => 'integer',
        'last_sync_at' => 'integer',
    ];

    /**
     * 源站点关联
     */
    public function sourceSite(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'source_site_id');
    }

    /**
     * 目标站点关联
     */
    public function targetSite(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'target_site_id');
    }

    /**
     * 标记为同步中
     */
    public function markAsSyncing(): void
    {
        $this->update([
            'sync_status' => 'syncing',
            'last_sync_at' => time(),
        ]);
    }

    /**
     * 标记为同步成功
     */
    public function markAsSuccess(int $targetDataId, array $syncLanguages = []): void
    {
        $this->update([
            'sync_status' => 'success',
            'target_data_id' => $targetDataId,
            'sync_languages' => $syncLanguages,
            'error_message' => null,
            'last_sync_at' => time(),
        ]);
    }

    /**
     * 标记为同步失败
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'sync_status' => 'failed',
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
            'last_sync_at' => time(),
        ]);
    }

    /**
     * 标记为跳过
     */
    public function markAsSkipped(string $reason): void
    {
        $this->update([
            'sync_status' => 'skipped',
            'error_message' => $reason,
            'last_sync_at' => time(),
        ]);
    }
} 