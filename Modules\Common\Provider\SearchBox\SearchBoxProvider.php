<?php

namespace Modules\Common\Provider\SearchBox;

use Modules\Common\Provider\ProviderTrait;

class SearchBoxProvider
{
    use ProviderTrait;

    /**
     * @return AbstractSearchBoxProvider[]
     */
    public static function all(): array
    {
        /** @var AbstractSearchBoxProvider[] $records */
        $records = self::listAll();
        usort($records, function ($o1, $o2) {
            if ($o1->order() == $o2->order()) {
                return 0;
            }
            return $o1->order() > $o2->order() ? 1 : -1;
        });
        return $records;
    }

    /**
     * @param $name
     * @return AbstractSearchBoxProvider
     */
    public static function get($name): AbstractSearchBoxProvider
    {
        return self::getByName($name);
    }

    public static function count(): int
    {
        return count(self::all());
    }
}
