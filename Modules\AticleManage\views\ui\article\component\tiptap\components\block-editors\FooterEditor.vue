<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.footerEditor.contentTab')" name="content">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.footerEditor.footerText')">
            <el-input v-model="footerContent.text" @input="markAsChanged" type="textarea" :rows="3" />
          </el-form-item>
          
          <el-divider content-position="left">{{$t('Editor.footerEditor.socialMedia')}}</el-divider>
          
          <div v-for="(social, index) in footerContent.socials" :key="index" class="social-item-edit">
            <div class="social-item-header">
              <span>{{ $t('Editor.footerEditor.platforms.' + social.platform) }}</span>
              <div class="social-item-actions">
                <el-button 
                  type="text" 
                  :icon="Edit" 
                  @click="editSocialItem(index)"
                  :title="$t('Editor.footerEditor.edit')"
                />
                <el-button 
                  type="text" 
                  :icon="Delete" 
                  @click="removeSocialItem(index)" 
                  :title="$t('Editor.footerEditor.delete')"
                />
              </div>
            </div>
            <div v-if="editingSocialIndex === index" class="social-item-content">
              <el-form-item :label="$t('Editor.footerEditor.platform')">
                <el-select v-model="footerContent.socials[index].platform" @change="markAsChanged" style="width: 100%">
                  <el-option :label="$t('Editor.footerEditor.platforms.facebook')" value="facebook" />
                  <el-option :label="$t('Editor.footerEditor.platforms.twitter')" value="twitter" />
                  <el-option :label="$t('Editor.footerEditor.platforms.linkedin')" value="linkedin" />
                  <el-option :label="$t('Editor.footerEditor.platforms.instagram')" value="instagram" />
                  <el-option :label="$t('Editor.footerEditor.platforms.youtube')" value="youtube" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('Editor.footerEditor.link')">
                <el-input v-model="footerContent.socials[index].href" @input="markAsChanged" :placeholder="$t('Editor.footerEditor.linkPlaceholder')" />
              </el-form-item>
            </div>
          </div>
          
          <div class="add-item-button">
            <el-button type="primary" @click="addSocialItem" icon="Plus">{{$t('Editor.footerEditor.addSocial')}}</el-button>
          </div>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane :label="$t('Editor.footerEditor.styleTab')" name="style">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.footerEditor.padding')">
            <el-radio-group v-model="footerStyles.padding" @change="markAsChanged">
              <el-radio-button label="py-2">{{$t('Editor.footerEditor.paddingSmall')}}</el-radio-button>
              <el-radio-button label="py-5">{{$t('Editor.footerEditor.paddingMedium')}}</el-radio-button>
              <el-radio-button label="py-7">{{$t('Editor.footerEditor.paddingLarge')}}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.footerEditor.textAlign')">
            <el-radio-group v-model="footerStyles.textAlign" @change="markAsChanged">
              <el-radio-button label="text-left">{{$t('Editor.footerEditor.alignLeft')}}</el-radio-button>
              <el-radio-button label="text-center">{{$t('Editor.footerEditor.alignCenter')}}</el-radio-button>
              <el-radio-button label="text-right">{{$t('Editor.footerEditor.alignRight')}}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.footerEditor.backgroundColor')">
            <el-color-picker v-model="footerStyles.backgroundColor" @change="markAsChanged" show-alpha />
          </el-form-item>
          
          <el-form-item :label="$t('Editor.footerEditor.textColor')">
            <el-color-picker v-model="footerStyles.textColor" @change="markAsChanged" show-alpha />
          </el-form-item>
          
          <el-form-item :label="$t('Editor.footerEditor.iconColor')">
            <el-color-picker v-model="footerStyles.iconColor" @change="markAsChanged" show-alpha />
          </el-form-item>
          
          <el-form-item :label="$t('Editor.footerEditor.iconSize')">
            <el-slider
              v-model="footerStyles.iconSize"
              :min="16"
              :max="48"
              :step="4"
              show-stops
              @change="markAsChanged"
            />
            <div class="size-display">{{ footerStyles.iconSize }}px</div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.footerEditor.applyChanges')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

// 定义组件名称
defineOptions({
  name: 'FooterEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的社交媒体索引
const editingSocialIndex = ref(0)

// 原始HTML
const originalHtml = ref('')

// 社交媒体数据
interface SocialItem {
  platform: string
  href: string
  icon: string
}

// 社交媒体平台与图标的映射
const socialIcons: Record<string, string> = {
  facebook: `<i class="fab fa-facebook-f" style="font-size: 24px;"></i>`,
  twitter: `<i class="fab fa-twitter" style="font-size: 24px;"></i>`,
  linkedin: `<i class="fab fa-linkedin-in" style="font-size: 24px;"></i>`,
  instagram: `<i class="fab fa-instagram" style="font-size: 24px;"></i>`,
  youtube: `<i class="fab fa-youtube" style="font-size: 24px;"></i>`
}

// 页脚内容数据
interface FooterContent {
  text: string
  socials: SocialItem[]
}

// 页脚样式数据
interface FooterStyles {
  padding: string
  textAlign: string
  backgroundColor: string
  textColor: string
  iconColor: string
  iconSize: number
}

// 初始化页脚内容和样式
const footerContent = ref<FooterContent>({
  text: 'No menus installed from the sidebar, create a new one by navigating to the Navigation Menu tool',
  socials: [
    { platform: 'facebook', href: '#', icon: socialIcons.facebook },
    { platform: 'twitter', href: '#', icon: socialIcons.twitter },
    { platform: 'linkedin', href: '#', icon: socialIcons.linkedin },
    { platform: 'instagram', href: '#', icon: socialIcons.instagram },
    { platform: 'youtube', href: '#', icon: socialIcons.youtube }
  ]
})

const footerStyles = ref<FooterStyles>({
  padding: 'py-5',
  textAlign: 'text-center',
  backgroundColor: '#212529',
  textColor: '#FFFFFF',
  iconColor: '#FFFFFF',
  iconSize: 24
})

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  footerContent.value = {
    text: 'No menus installed from the sidebar, create a new one by navigating to the Navigation Menu tool',
    socials: [
      { platform: 'facebook', href: '#', icon: socialIcons.facebook },
      { platform: 'twitter', href: '#', icon: socialIcons.twitter },
      { platform: 'linkedin', href: '#', icon: socialIcons.linkedin },
      { platform: 'instagram', href: '#', icon: socialIcons.instagram },
      { platform: 'youtube', href: '#', icon: socialIcons.youtube }
    ]
  }
  
  footerStyles.value = {
    padding: 'py-5',
    textAlign: 'text-center',
    backgroundColor: '#212529',
    textColor: '#FFFFFF',
    iconColor: '#FFFFFF',
    iconSize: 24
  }
  
  // 初始化编辑第一个社交媒体
  if (footerContent.value.socials.length > 0) {
    editingSocialIndex.value = 0
  }
}

/**
 * 从页脚元素中提取内容和样式
 */
const extractFooterContent = (): boolean => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 提取padding类
    const paddingClass = Array.from(props.blockElement.classList)
      .find(cls => cls.startsWith('py-'))
    
    if (paddingClass) {
      footerStyles.value.padding = paddingClass
    }
    
    // 提取对齐类
    const alignClass = Array.from(props.blockElement.classList)
      .find(cls => cls.startsWith('text-'))
    
    if (alignClass) {
      footerStyles.value.textAlign = alignClass
    }
    
    // 提取背景色
    const computedStyle = window.getComputedStyle(props.blockElement)
    if (computedStyle.backgroundColor && computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
      footerStyles.value.backgroundColor = computedStyle.backgroundColor
    }
    
    // 提取文本颜色
    if (computedStyle.color && computedStyle.color !== 'rgb(0, 0, 0)') {
      footerStyles.value.textColor = computedStyle.color
    }
    
    // 提取页脚文本内容
    const textElement = props.blockElement.querySelector('[data-bs-component="rich-text"] p')
    if (textElement) {
      footerContent.value.text = textElement.textContent?.trim() || ''
    }
    
    // 提取社交媒体链接
    const socialLinks = props.blockElement.querySelectorAll('.gap-3 a')
    if (socialLinks.length > 0) {
      const extractedSocials: SocialItem[] = []
      
      socialLinks.forEach((link) => {
        // 尝试确定平台类型
        let platform = 'facebook'
        if (link.innerHTML.includes('fa-facebook')) {
          platform = 'facebook'
        } else if (link.innerHTML.includes('fa-twitter')) {
          platform = 'twitter'
        } else if (link.innerHTML.includes('fa-linkedin')) {
          platform = 'linkedin'
        } else if (link.innerHTML.includes('fa-instagram')) {
          platform = 'instagram'
        } else if (link.innerHTML.includes('fa-youtube')) {
          platform = 'youtube'
        }
        
        extractedSocials.push({
          platform,
          href: (link as HTMLAnchorElement).href || '#',
          icon: socialIcons[platform]
        })
        
        // 提取图标颜色和大小
        const iconElement = link.querySelector('i.fab')
        if (iconElement) {
          const iconStyle = window.getComputedStyle(iconElement)
          if (iconStyle.color && iconStyle.color !== 'rgb(0, 0, 0)') {
            footerStyles.value.iconColor = iconStyle.color
          }
          
          // 提取尺寸
          const fontSize = iconStyle.fontSize
          if (fontSize) {
            const size = parseInt(fontSize)
            if (!isNaN(size)) {
              footerStyles.value.iconSize = size
            }
          }
        }
      })
      
      if (extractedSocials.length > 0) {
        footerContent.value.socials = extractedSocials
      }
    }
    
    return true
  } catch (error) {
    console.error('提取页脚内容时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    editingSocialIndex.value = -1
    
    // 重新提取数据
    const extracted = extractFooterContent()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加社交媒体
const addSocialItem = () => {
  // 查找尚未添加的平台
  const availablePlatforms = ['facebook', 'twitter', 'linkedin', 'instagram', 'youtube']
  const existingPlatforms = footerContent.value.socials.map(s => s.platform)
  const newPlatform = availablePlatforms.find(p => !existingPlatforms.includes(p)) || 'facebook'
  
  footerContent.value.socials.push({
    platform: newPlatform,
    href: '#',
    icon: socialIcons[newPlatform]
  })
  
  editingSocialIndex.value = footerContent.value.socials.length - 1
  markAsChanged()
}

// 编辑社交媒体
const editSocialItem = (index: number) => {
  editingSocialIndex.value = editingSocialIndex.value === index ? -1 : index
}

// 删除社交媒体
const removeSocialItem = (index: number) => {
  footerContent.value.socials.splice(index, 1)
  if (editingSocialIndex.value === index) {
    editingSocialIndex.value = -1
  } else if (editingSocialIndex.value > index) {
    editingSocialIndex.value--
  }
  markAsChanged()
}

/**
 * 生成页脚HTML
 */
const generateFooterHtml = (): string => {
  try {
    // 生成社交媒体HTML
    const socialsHtml = footerContent.value.socials.map(social => {
      // 获取图标并更新尺寸
      let icon = socialIcons[social.platform] || socialIcons.facebook
      icon = icon.replace(/font-size: 24px/, `font-size: ${footerStyles.value.iconSize}px`)
      
      return `
            <a href="${social.href}" class="text-white">
                ${icon}
            </a>`
    }).join('\n')
    
    // 生成背景和文本颜色样式
    const colorStyles = `style="background-color: ${footerStyles.value.backgroundColor}; color: ${footerStyles.value.textColor};"`
    
    // 生成页脚HTML
    return `
<div data-bs-component="footer" class="${footerStyles.value.padding} ${footerStyles.value.textAlign}" ${colorStyles}>
    <div class="container">
        <div data-bs-component="rich-text" class="${footerStyles.value.textAlign}">
            <p>${footerContent.value.text}</p>
        </div>
        <div data-bs-component="social-media" class="gap-3 mt-3 d-flex justify-content-center">
${socialsHtml}
        </div>
    </div>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</div>
    `.trim()
  } catch (error) {
    console.error('生成页脚HTML时出错:', error)
    // 返回空字符串，避免崩溃
    return ''
  }
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateFooterHtml()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用页脚更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.social-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.social-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
    text-transform: capitalize;
  }
}

.social-item-actions {
  display: flex;
  gap: 5px;
}

.social-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

.size-display {
  margin-top: 5px;
  text-align: center;
  color: #606266;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 