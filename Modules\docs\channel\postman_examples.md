# 频道管理 API Postman 调用示例

## 基础信息
- **Base URL**: `http://your-domain.com/api`
- **Content-Type**: `application/json`
- **Authorization**: 根据实际需求设置

## 1. 获取频道列表

### 请求信息
- **Method**: `GET`
- **URL**: `{{base_url}}/channel`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 查询参数
```
page=1&limit=20&name=&status=&order_by=sort&order_dir=asc
```

### 示例请求
```
GET {{base_url}}/channel?page=1&limit=10&status=1&order_by=created_at&order_dir=desc
```

### 响应示例
```json
{
    "total": 3,
    "page": 1,
    "limit": 10,
    "items": [
        {
            "id": 3,
            "name": "新闻频道4",
            "description": "24小时新闻频道3",
            "status": 1,
            "sort": 2,
            "url": "https://news.example.com",
            "cover_img": "https://news.example.com/cover.jpg",
            "created_by": 1,
            "updated_by": 1,
            "created_at": "2025-07-23 09:52:47",
            "updated_at": "2025-07-23 09:52:47",
            "deleted_at": null,
            "region_num": 3
        },
        {
            "id": 2,
            "name": "测试频道5",
            "description": "测试验证错误",
            "status": 1,
            "sort": 999,
            "url": null,
            "cover_img": null,
            "created_by": 1,
            "updated_by": 1,
            "created_at": "2025-07-23 09:51:43",
            "updated_at": "2025-07-23 09:51:43",
            "deleted_at": null,
            "region_num": 0
        }
    ]
}
```

## 2. 创建频道

### 请求信息
- **Method**: `POST`
- **URL**: `{{base_url}}/channel`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 请求体示例
```json
{
    "name": "新闻频道4",
    "description": "24小时新闻频道3",
    "status": 1,
    "sort": 2,
    "url": "https://news.example.com",
    "cover_img": "https://news.example.com/cover.jpg",
    "region_ids": [1, 2, 3]
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "name": "新闻频道4",
        "description": "24小时新闻频道3",
        "status": 1,
        "sort": 2,
        "url": "https://news.example.com",
        "cover_img": "https://news.example.com/cover.jpg",
        "created_by": 1,
        "updated_by": 1,
        "updated_at": "2025-07-23 09:52:47",
        "created_at": "2025-07-23 09:52:47",
        "id": 3,
        "regions": [
            {
                "id": 1,
                "name": "突發新聞",
                "description": "突發新聞專區更新版",
                "status": 0,
                "sort": 1,
                "created_by": 1,
                "updated_by": 1,
                "created_at": "2025-07-19 08:48:09",
                "updated_at": "2025-07-20 11:53:48",
                "deleted_at": null,
                "pivot": {
                    "channel_id": "3",
                    "regions_id": "1",
                    "created_by": 1,
                    "updated_by": 1,
                    "created_at": "2025-07-23 10:08:38",
                    "updated_at": "2025-07-23 10:08:38"
                }
            }
        ]
    }
}
```

## 3. 获取频道详情

### 请求信息
- **Method**: `GET`
- **URL**: `{{base_url}}/channel/{id}`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 示例请求
```
GET {{base_url}}/channel/3
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "name": "新闻频道4",
        "description": "24小时新闻频道3",
        "status": 1,
        "sort": 2,
        "url": "https://news.example.com",
        "cover_img": "https://news.example.com/cover.jpg",
        "created_by": 1,
        "updated_by": 1,
        "updated_at": "2025-07-23 09:52:47",
        "created_at": "2025-07-23 09:52:47",
        "id": 3,
        "regions": [
            {
                "id": 1,
                "name": "突發新聞",
                "description": "突發新聞專區更新版",
                "status": 0,
                "sort": 1,
                "created_by": 1,
                "updated_by": 1,
                "created_at": "2025-07-19 08:48:09",
                "updated_at": "2025-07-20 11:53:48",
                "deleted_at": null,
                "pivot": {
                    "channel_id": "3",
                    "regions_id": "1",
                    "created_by": 1,
                    "updated_by": 1,
                    "created_at": "2025-07-23 10:08:38",
                    "updated_at": "2025-07-23 10:08:38"
                }
            }
        ]
    }
}
```

## 4. 更新频道

### 请求信息
- **Method**: `PUT`
- **URL**: `{{base_url}}/channel/{id}`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 请求体示例
```json
{
    "name": "新闻频道4-更新",
    "description": "24小时新闻频道3-更新",
    "status": 1,
    "sort": 1,
    "url": "https://news.example.com/updated",
    "cover_img": "https://news.example.com/cover-updated.jpg",
    "region_ids": [1, 2]
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "name": "新闻频道4-更新",
        "description": "24小时新闻频道3-更新",
        "status": 1,
        "sort": 1,
        "url": "https://news.example.com/updated",
        "cover_img": "https://news.example.com/cover-updated.jpg",
        "created_by": 1,
        "updated_by": 1,
        "updated_at": "2025-07-23 10:15:30",
        "created_at": "2025-07-23 09:52:47",
        "id": 3,
        "regions": [
            {
                "id": 1,
                "name": "突發新聞",
                "description": "突發新聞專區更新版",
                "status": 0,
                "sort": 1,
                "created_by": 1,
                "updated_by": 1,
                "created_at": "2025-07-19 08:48:09",
                "updated_at": "2025-07-20 11:53:48",
                "deleted_at": null,
                "pivot": {
                    "channel_id": "3",
                    "regions_id": "1",
                    "created_by": 1,
                    "updated_by": 1,
                    "created_at": "2025-07-23 10:15:30",
                    "updated_at": "2025-07-23 10:15:30"
                }
            }
        ]
    }
}
```

## 5. 删除频道

### 请求信息
- **Method**: `DELETE`
- **URL**: `{{base_url}}/channel/{id}`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 示例请求
```
DELETE {{base_url}}/channel/3
```

### 响应示例
```json
{
    "code": 200,
    "message": "删除成功"
}
```

## 6. 更新频道状态

### 请求信息
- **Method**: `PATCH`
- **URL**: `{{base_url}}/channel/{id}/status`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 请求体示例
```json
{
    "status": 0
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "状态更新成功"
}
```

## 7. 删除频道与区域的关联

### 请求信息
- **Method**: `DELETE`
- **URL**: `{{base_url}}/channel/{channelId}/region/{regionId}`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 示例请求
```
DELETE {{base_url}}/channel/3/region/1
```

### 响应示例
```json
{
    "code": 200,
    "message": "关联删除成功"
}
```

## 8. 获取频道关联的区域

### 请求信息
- **Method**: `GET`
- **URL**: `{{base_url}}/channel/{id}/regions`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 示例请求
```
GET {{base_url}}/channel/3/regions
```

### 响应示例
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "channel_id": 3,
        "regions": [
            {
                "id": 1,
                "name": "突發新聞",
                "description": "突發新聞專區更新版",
                "status": 0,
                "sort": 1,
                "created_by": 1,
                "updated_by": 1,
                "created_at": "2025-07-19 08:48:09",
                "updated_at": "2025-07-20 11:53:48",
                "deleted_at": null,
                "pivot": {
                    "channel_id": "3",
                    "regions_id": "1",
                    "created_by": 1,
                    "updated_by": 1,
                    "created_at": "2025-07-23 10:08:38",
                    "updated_at": "2025-07-23 11:31:28"
                }
            },
            {
                "id": 2,
                "name": "港澳",
                "description": "港澳地區新聞",
                "status": 1,
                "sort": 2,
                "created_by": 1,
                "updated_by": 1,
                "created_at": "2025-07-19 08:48:25",
                "updated_at": "2025-07-20 12:55:23",
                "deleted_at": null,
                "pivot": {
                    "channel_id": "3",
                    "regions_id": "2",
                    "created_by": 1,
                    "updated_by": 1,
                    "created_at": "2025-07-23 10:08:38",
                    "updated_at": "2025-07-23 11:31:28"
                }
            }
        ]
    }
}
```

## 9. 批量操作

### 请求信息
- **Method**: `POST`
- **URL**: `{{base_url}}/channel/batch`
- **Headers**: 
  ```
  Content-Type: application/json
  ```

### 请求体示例

#### 批量删除
```json
{
    "action": "delete",
    "ids": [1, 2, 3]
}
```

#### 批量启用
```json
{
    "action": "enable",
    "ids": [1, 2, 3]
}
```

#### 批量禁用
```json
{
    "action": "disable",
    "ids": [1, 2, 3]
}
```

#### 批量更新排序
```json
{
    "action": "update_sort",
    "ids": [1, 2, 3],
    "data": {
        "sort_data": [
            {"id": 1, "sort": 10},
            {"id": 2, "sort": 20},
            {"id": 3, "sort": 30}
        ]
    }
}
```

### 响应示例

#### 成功响应
```json
{
    "success": true,
    "message": "成功删除 3 个频道",
    "affected_count": 3
}
```

#### 部分失败响应
```json
{
    "success": true,
    "message": "成功启用 1 个频道",
    "affected_count": 1
}
```

#### 完全失败响应
```json
{
    "success": false,
    "message": "未找到要操作的频道",
    "affected_count": 0
}
```

## 错误响应示例

### 验证错误
```json
{
    "error": true,
    "message": "验证失败",
    "errors": {
        "name": ["频道名称不能为空"],
        "status": ["状态值必须是0或1"]
    },
    "request_data": {
        "name": "",
        "status": 2
    },
    "timestamp": "2025-07-23 10:20:15"
}
```

### 服务器错误
```json
{
    "error": true,
    "message": "创建频道失败: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'test-channel' for key 'channel_name_unique'",
    "error_details": {
        "type": "Illuminate\\Database\\QueryException",
        "file": "/path/to/file.php",
        "line": 123,
        "code": 23000
    },
    "request_data": {
        "name": "test-channel",
        "description": "测试频道"
    },
    "timestamp": "2025-07-23 10:20:15"
}
```

## 环境变量设置

在Postman中设置以下环境变量：

```
base_url: http://your-domain.com/api
```

## 注意事项

1. **认证**：根据实际需求设置Authorization头
2. **错误处理**：所有接口都包含详细的错误信息
3. **时间格式**：所有时间字段格式为 `Y-m-d H:i:s`
4. **软删除**：删除操作使用软删除，数据不会真正从数据库中删除
5. **关联操作**：创建和更新频道时可以同时关联区域
6. **批量操作**：支持批量删除、启用、禁用操作 