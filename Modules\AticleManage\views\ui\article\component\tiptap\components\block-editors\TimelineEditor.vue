<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.timelineEditor.tab.content')" name="content">
        <el-form label-position="top" size="small">
          <!-- 时间线项目列表 -->
          <div class="timeline-items">
            <div v-for="(item, index) in timelineData.items" :key="index" class="timeline-item-edit">
              <div class="item-header">
                <h4 class="item-title">{{$t('Editor.timelineEditor.form.item')}} #{{ index + 1 }}</h4>
                <el-button
                  v-if="timelineData.items.length > 1"
                  type="danger"
                  size="small"
                  circle
                  :icon="Delete"
                  @click="removeTimelineItem(index)"
                />
              </div>

              <el-form-item :label="$t('Editor.timelineEditor.form.date')">
                <el-input v-model="item.date" :placeholder="$t('Editor.timelineEditor.form.datePlaceholder')" @input="markAsChanged" />
              </el-form-item>

              <el-form-item :label="$t('Editor.timelineEditor.form.title')">
                <el-input v-model="item.title" :placeholder="$t('Editor.timelineEditor.form.titlePlaceholder')" @input="markAsChanged" />
              </el-form-item>

              <el-form-item :label="$t('Editor.timelineEditor.form.description')">
                <el-input
                  v-model="item.description"
                  type="textarea"
                  :rows="3"
                  :placeholder="$t('Editor.timelineEditor.form.descriptionPlaceholder')"
                  @input="markAsChanged"
                />
              </el-form-item>

              <el-form-item :label="$t('Editor.timelineEditor.form.icon')">
                <el-select v-model="item.icon" :placeholder="$t('Editor.timelineEditor.form.iconPlaceholder')" style="width: 100%" @change="markAsChanged">
                  <el-option :label="$t('Editor.timelineEditor.icon.rocket')" value="fas fa-rocket" />
                  <el-option :label="$t('Editor.timelineEditor.icon.cogs')" value="fas fa-cogs" />
                  <el-option :label="$t('Editor.timelineEditor.icon.checkCircle')" value="fas fa-check-circle" />
                  <el-option :label="$t('Editor.timelineEditor.icon.flag')" value="fas fa-flag" />
                  <el-option :label="$t('Editor.timelineEditor.icon.star')" value="fas fa-star" />
                  <el-option :label="$t('Editor.timelineEditor.icon.bolt')" value="fas fa-bolt" />
                  <el-option :label="$t('Editor.timelineEditor.icon.user')" value="fas fa-user" />
                  <el-option :label="$t('Editor.timelineEditor.icon.chartLine')" value="fas fa-chart-line" />
                  <el-option :label="$t('Editor.timelineEditor.icon.heart')" value="fas fa-heart" />
                  <el-option :label="$t('Editor.timelineEditor.icon.globe')" value="fas fa-globe" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="timeline-controls">
            <el-button type="primary" @click="addTimelineItem">{{$t('Editor.timelineEditor.button.addItem')}}</el-button>
          </div>
        </el-form>
      </el-tab-pane>

      <el-tab-pane :label="$t('Editor.timelineEditor.tab.style')" name="style">
        <StyleEditor
          :styles="localStyles"
          :active-tab="activeTab"
          @update-styles="updateStyles"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.timelineEditor.button.apply')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { defineAsyncComponent } from 'vue'
import { useI18n } from 'vue-i18n'
const StyleEditor = defineAsyncComponent(() => import('./StyleEditor.vue'))

// 定义组件名称
defineOptions({
  name: 'TimelineEditor'
})

// 自定义类型定义
interface TimelineItem {
  date: string;
  title: string;
  description: string;
  icon: string;
}

interface TimelineData {
  items: TimelineItem[];
  customAttributes?: Record<string, string>;
}

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    required: true
  },
  styles: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update-block', 'update-styles'])

const activeTab = ref('content')
const localStyles = ref({ ...props.styles })

// 原始HTML
const originalHtml = ref('')

// 是否有未保存的更改
const isChanged = ref(false)

// 时间线数据
const timelineData = ref<TimelineData>({
  items: [
    {
      date: '2023年1月',
      title: '项目启动',
      description: '这是我们项目的起点。在这个阶段，我们确定了项目的范围、目标和关键里程碑。团队成员各司其职，为项目的成功奠定了基础。',
      icon: 'fas fa-rocket'
    },
    {
      date: '2023年3月',
      title: '开发阶段',
      description: '经过两个月的紧张开发，我们完成了核心功能的实现。团队通过敏捷开发方法，确保了项目的质量和进度按计划推进。',
      icon: 'fas fa-cogs'
    }
  ],
  customAttributes: {}
})

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

/**
 * 解析时间线组件数据
 */
const extractTimelineData = (): boolean => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 解析当前元素中的数据
    const element = props.blockElement
    
    // 提取自定义属性
    timelineData.value.customAttributes = extractAttributes(element)
    
    // 提取时间线项目
    const timelineItems = element.querySelectorAll('.timeline')
    if (timelineItems.length > 0) {
      const newItems: TimelineItem[] = []
      
      timelineItems.forEach(item => {
        const iconEl = item.querySelector('.timeline-icon i')
        const dateEl = item.querySelector('.date')
        const titleEl = item.querySelector('.title')
        const descriptionEl = item.querySelector('.description')
        
        newItems.push({
          icon: iconEl ? iconEl.className : 'fas fa-rocket',
          date: dateEl ? dateEl.textContent?.trim() || '' : '',
          title: titleEl ? titleEl.textContent?.trim() || '' : '',
          description: descriptionEl ? descriptionEl.textContent?.trim() || '' : ''
        })
      })
      
      if (newItems.length > 0) {
        timelineData.value.items = newItems
      }
    }
    
    return true
  } catch (error) {
    console.error('提取时间线数据时出错:', error)
    return false
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 重置时间线数据到默认值
const resetToDefault = () => {
  timelineData.value = {
    items: [
      {
        date: '2023年1月',
        title: '项目启动',
        description: '这是我们项目的起点。在这个阶段，我们确定了项目的范围、目标和关键里程碑。',
        icon: 'fas fa-rocket'
      },
      {
        date: '2023年3月',
        title: '开发阶段',
        description: '经过两个月的紧张开发，我们完成了核心功能的实现。',
        icon: 'fas fa-cogs'
      }
    ],
    customAttributes: {}
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  if (newValue) {
    const extracted = extractTimelineData()
    if (!extracted) {
      resetToDefault()
    }
  } else {
    resetToDefault()
  }
  isChanged.value = false
}, { immediate: true })

// 修改 onMounted 钩子
onMounted(() => {
  isChanged.value = false
})

// 添加时间线项目
const addTimelineItem = () => {
  timelineData.value.items.push({
    date: '新日期',
    title: '新标题',
    description: '新描述',
    icon: 'fas fa-star'
  })
  markAsChanged()
}

// 移除时间线项目
const removeTimelineItem = (index: number) => {
  timelineData.value.items.splice(index, 1)
  markAsChanged()
}

// 准备提交的HTML
const prepareTimelineHTML = (): string => {
  try {
    // 生成属性字符串
    let attributesStr = ''
    if (timelineData.value.customAttributes) {
      Object.entries(timelineData.value.customAttributes).forEach(([key, value]) => {
        if (key !== 'class' && key !== 'style') {
          attributesStr += ` ${key}="${value}"`
        }
      })
    }

    // 构建HTML
    let html = `
    <div data-bs-component="timeline" class="timeline-block responsive-block"${attributesStr}>
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="main-timeline">
    `

    // 添加时间线项目
    timelineData.value.items.forEach(item => {
      html += `
              <div class="timeline">
                <div class="timeline-icon">
                  <i class="${item.icon}"></i>
                </div>
                <div class="timeline-content">
                  <span class="date">${item.date}</span>
                  <h3 class="title">${item.title}</h3>
                  <p class="description">
                    ${item.description}
                  </p>
                </div>
              </div>
      `
    })

    // 保留原始CSS样式
    const styleMatch = originalHtml.value.match(/<style>([\s\S]*?)<\/style>/)
    const styleContent = styleMatch ? styleMatch[1] : ''

    // 完成HTML
    html += `
            </div>
          </div>
        </div>
      </div>
      
      <style>${styleContent}</style>
      
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </div>
    `
    
    return html.trim()
  } catch (error) {
    console.error('准备时间线HTML时出错:', error)
    return originalHtml.value || ''
  }
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareTimelineHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用时间线更改时出错:', error)
  }
}

// 监听并更新样式变化
watch(() => localStyles.value, (newStyles) => {
  emit('update-styles', newStyles)
}, { deep: true })

// 更新样式
const updateStyles = (styles: any) => {
  localStyles.value = styles
  markAsChanged()
}
</script>

<style lang="scss" scoped>
.edit-section {
  padding: 10px 0;
}

.timeline-item-edit {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 5px;
  margin-bottom: 15px;
  background-color: #f8f9fa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.timeline-controls {
  margin: 15px 0;
  display: flex;
  justify-content: center;
}

.apply-button-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 