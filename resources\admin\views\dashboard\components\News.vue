<template>
  <div class="news-slider">
    <div v-if="loading" class="news-content">
      <div class="news-item skeleton">
        <div class="skeleton-image"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-title last-child"></div>
        <div class="skeleton-text"></div>
        <div class="skeleton-text"></div>
        <div class="skeleton-text"></div>
        <div class="skeleton-text"></div>
        <div class="learn-more-btn"></div>
      </div>
    </div>
    <div v-else class="news-content">
      <div class="news-wrapper" :style="{ transform: `translateX(-${(1 / limitedInfoValue.length) * currentIndex * 100}%)`, width: `${limitedInfoValue.length * 100}%` }">
        <div class="news-item" v-for="(item, index) in limitedInfoValue" :key="index" :style="{ width: `${(1 / limitedInfoValue.length) * 100}%` }">
          <div class="news-image">
            <img :src="item.img" alt="News Image" />
          </div>
          <div class="news-info">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
            <el-button class="learn-more-btn" plain>
              {{ t('dashboard.news.learnMore') }}
              <el-icon class="el-icon--right"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="navigation">
      <div class="nav-controls">
        <button class="nav-arrow prev" @click="prevSlide">
          <el-icon><ArrowLeft /></el-icon>
        </button>
        <div class="nav-dots">
          <span v-for="(_, index) in limitedInfoValue" :key="index" class="dot" :class="{ active: currentIndex === index }" @click="setCurrentIndex(index)"></span>
        </div>
        <button class="nav-arrow next" @click="nextSlide">
          <el-icon><ArrowRight /></el-icon>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const loading = ref(false)
const infoValue = ref<any>([])
const currentIndex = ref(0)

const limitedInfoValue = computed(() => {
  return infoValue.value.slice(0, 3)
})

const getNews = async () => {
  loading.value = true
  try {
    const res = await http.get('/dashboard/news/data')
    infoValue.value = res.data.data
  } catch (err) {
    console.error('Failed to fetch news:', err)
  } finally {
    loading.value = false
  }
}

const setCurrentIndex = (index: number) => {
  currentIndex.value = index
}

const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % limitedInfoValue.value.length
}

const prevSlide = () => {
  currentIndex.value = currentIndex.value === 0 ? limitedInfoValue.value.length - 1 : currentIndex.value - 1
}

const goToNews = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  getNews()
})
</script>

<script lang="ts">
export default {
  name: 'News',
}
</script>

<style lang="scss" scoped>
.news-slider {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 20px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 418px;

  .news-content {
    flex: 1;
    overflow: hidden;
  }

  .navigation {
    background: #dfdfdf;
    border-radius: 0 0 20px 20px;

    .nav-controls {
      display: flex;

      .nav-dots {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-grow: 1;

        .dot {
          margin: 0 7px;
          border: 1px solid #37a0ea;
          border-radius: 50%;
          width: 14px;
          height: 14px;
          background-color: #fff;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &.active {
            background-color: #37a0ea;
          }
        }
      }

      .nav-arrow {
        width: 30px;
        height: 30px;
        background-color: #19496a;
        cursor: pointer;
        color: #fff;
        transition: background-color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(39, 72, 103, 0.9);
        }

        .el-icon {
          font-size: 16px;
        }
      }
      .prev {
        border-radius: 0 0 0 20px;
      }
      .next {
        border-radius: 0 0 20px 0;
      }
    }
  }
}

.news-wrapper {
  height: 100%;
  display: flex;
  transition: transform 0.3s ease;
}

.news-item {
  display: flex;
  flex-direction: column;
  padding: 20px 24px 30px;
  width: 100%;

  .news-image {
    margin-bottom: 22px;
    border-radius: 50%;
    width: 62px;
    height: 62px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .news-info {
    flex-grow: 1;

    h3 {
      margin-bottom: 18px;
      font-size: 18px;
      font-weight: 700;
      color: #000;
      line-height: 1.425;

      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      font-size: 16px;
      color: #666;
      line-height: 2;

      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .learn-more-btn {
      margin-top: 24px;
      border-radius: 10px;
      border: 2px solid #86c0e9;
      padding: 10px 18px;
      background-color: #f4f9fd;
      color: #37a0ea;
      height: auto;

      display: inline-flex;
      align-items: center;

      .el-icon {
        margin-left: 20px;
        font-size: 14px;
      }
    }
  }

  // 骨架屏样式
  &.skeleton {
    height: 100%;
    animation: skeleton-loading 1.5s infinite;

    .skeleton-image {
      margin-bottom: 22px;
      border-radius: 50%;
      width: 62px;
      height: 62px;
      background-color: #e0e0e0;
    }

    .skeleton-title {
      height: 18px;
      margin-bottom: 8px;
      background-color: #e0e0e0;
      width: 100%;

      &.last-child {
        margin-bottom: 18px;
      }
    }

    .skeleton-text {
      margin: 4px 0;
      height: 16px;
      background-color: #e0e0e0;
      width: 100%;
    }

    .learn-more-btn {
      margin-top: 32px;
      height: 38px;
      background-color: #e0e0e0;
      width: 150px;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
