<?php

namespace Modules\Faq\Services;

use Modules\Faq\Models\FaqSeo;
use Modules\Faq\Models\FaqTag;
use Modules\Tags\Models\Tag;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Faq\Models\Faq;

class FaqService
{
    public function createFaq($validated)
    {
       try {
            DB::beginTransaction();

            $createdFaqs = [];

            // 自动生成唯一的 FAQ code
            $faqCode = $this->generateUniqueFaqCode();

            // 1. 批量创建多语言FAQ记录
            $createdFaqs = $this->batchCreateMultiLanguageFaqs($validated, $faqCode);

            // 2. 如果有SEO数据，为每个FAQ创建SEO记录
            if (!empty($validated['seo']) && !empty($createdFaqs)) {
                $this->batchCreateFaqSeoRecords($createdFaqs, $validated['seo']);
            }

            // 3. 如果有标签，批量处理标签关联（只需要处理一次，因为所有语言版本共享标签）
            if (!empty($validated['tags']) && !empty($createdFaqs)) {
                $tagIds = $this->processTagsWithUniqueConstraint($validated['tags']);
                if (!empty($tagIds)) {
                    // 为所有语言版本的FAQ创建标签关联
                    foreach ($createdFaqs as $faq) {
                        $this->batchCreateFaqTagRelations($faq->id, $tagIds);
                    }
                }
            }

            DB::commit();

            // 返回创建的FAQ记录（主要返回第一个语言版本的信息）
            $mainFaq = $createdFaqs->first();
            if ($mainFaq) {
                // 加载分类信息
                $mainFaq->load('category');

                return [
                    'id' => $mainFaq->id,
                    'code' => $mainFaq->code,
                    'category_id' => $mainFaq->category_id,
                    'status' => $mainFaq->status,
                    'sort_order' => $mainFaq->sort_order,
                    'created_at' => $mainFaq->created_at,
                    'updated_at' => $mainFaq->updated_at,
                    'category' => $mainFaq->category ? $mainFaq->category->toArray() : null,
                    'languages_created' => $createdFaqs->pluck('lang')->toArray(),
                    'total_records' => $createdFaqs->count(),
                ];
            }

            return ['message' => 'FAQ created successfully'];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('FAQ creation failed: ' . $e->getMessage());
            throw new \Exception('FAQ创建失败: ' . $e->getMessage());
        }
    }


    /**
     * 处理标签数组，支持标签名称的唯一性约束（优化版本，减少数据库查询）
     *
     * @param array $tags 标签数组（可以是ID或名称）
     * @return array 返回标签ID数组
     */
    private function processTagsWithUniqueConstraint(array $tags): array
    {
        if (empty($tags)) {
            return [];
        }

        $tagIds = [];
        $tagNames = [];
        $numericTags = [];

        // 分离数字ID和字符串名称
        foreach ($tags as $tag) {
            if (is_numeric($tag)) {
                $numericTags[] = (int)$tag;
            } else {
                $tagName = trim($tag);
                if (!empty($tagName)) {
                    $tagNames[] = $tagName;
                }
            }
        }

        // 批量验证数字ID是否存在
        if (!empty($numericTags)) {
            $existingTagIds = Tag::whereIn('id', $numericTags)
                ->pluck('id')
                ->toArray();
            $tagIds = array_merge($tagIds, $existingTagIds);

            // 记录不存在的标签ID
            $missingIds = array_diff($numericTags, $existingTagIds);
            if (!empty($missingIds)) {
                Log::warning("Tag IDs not found: " . implode(', ', $missingIds));
            }
        }

        // 批量处理标签名称
        if (!empty($tagNames)) {
            $nameTagIds = $this->batchFindOrCreateTagsByNames($tagNames);
            $tagIds = array_merge($tagIds, $nameTagIds);
        }

        return array_unique($tagIds);
    }

    /**
     * 批量创建多语言FAQ记录，优化数据库性能
     *
     * @param array $validated 验证后的数据
     * @param string $faqCode 自动生成的FAQ代码
     * @return \Illuminate\Database\Eloquent\Collection 返回创建的FAQ记录集合
     */
    private function batchCreateMultiLanguageFaqs(array $validated, string $faqCode): \Illuminate\Database\Eloquent\Collection
    {
        $faqsData = [];
        $currentTime = now();
        $userId = 0;

        // 准备批量插入的数据
        foreach ($validated['contents'] as $content) {
            $faqsData[] = [
                'code' => $faqCode,
                'category_id' => $validated['category_id'],
                'title' => $content['title'],
                'content' => $content['content'],
                'lang' => $content['lang'],
                'status' => $validated['status'],
                'sort_order' => $validated['sort_order'] ?? 0,
                'created_by' => $userId,
                'updated_by' => $userId,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];
        }

        // 批量插入FAQ记录
        Faq::insert($faqsData);

        // 获取刚插入的FAQ记录
        $createdFaqs = Faq::where('code', $faqCode)
            ->where('created_at', $currentTime)
            ->get();

        return $createdFaqs;
    }

    /**
     * 批量创建FAQ-SEO记录
     *
     * @param \Illuminate\Database\Eloquent\Collection $faqs FAQ记录集合
     * @param array $seoData SEO数据
     * @return void
     */
    private function batchCreateFaqSeoRecords($faqs, array $seoData): void
    {
        if (empty($faqs)) {
            return;
        }

        $seoRecordsData = [];
        $currentTime = now();

        foreach ($faqs as $faq) {
            $seoRecordsData[] = [
                'faq_id' => $faq->id,
                'title' => $seoData['title'] ?? null,
                'description' => $seoData['description'] ?? null,
                'keywords' => $seoData['keywords'] ?? null,
                'regular_url' => $seoData['regular_url'] ?? null,
                'allow_index' => $seoData['allow_index'] ?? true,
                'allow_follow' => $seoData['allow_follow'] ?? true,
                'open_graph_title' => $seoData['open_graph_title'] ?? null,
                'open_graph_description' => $seoData['open_graph_description'] ?? null,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];
        }

        try {
            FaqSeo::insert($seoRecordsData);
        } catch (\Exception $e) {
            Log::error("Failed to batch create FAQ-SEO records", [
                'faq_ids' => $faqs->pluck('id')->toArray(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 批量处理标签名称，优化数据库查询性能
     *
     * @param array $tagNames 标签名称数组
     * @return array 返回标签ID数组
     */
    private function batchFindOrCreateTagsByNames(array $tagNames): array
    {
        if (empty($tagNames)) {
            return [];
        }

        $tagIds = [];
        $uniqueNames = array_unique($tagNames);

        try {
            // 批量查找已存在的标签
            $existingTags = Tag::whereIn('name', $uniqueNames)
                ->whereNull('deleted_at')
                ->get()
                ->keyBy('name');

            $existingNames = $existingTags->keys()->toArray();
            $tagIds = $existingTags->pluck('id')->toArray();

            // 找出需要创建的标签名称
            $newNames = array_diff($uniqueNames, $existingNames);

            if (!empty($newNames)) {
                // 批量创建新标签
                $newTagsData = [];
                $currentTime = now();
                $userId = 0;

                foreach ($newNames as $name) {
                    $newTagsData[] = [
                        'name' => $name,
                        'reference_name' => $name,
                        'category_id' => 1, // 默认分类ID
                        'status' => 1,
                        'sort_order' => 0,
                        'created_by' => $userId,
                        'updated_by' => $userId,
                        'created_at' => $currentTime,
                        'updated_at' => $currentTime,
                    ];
                }

                // 使用批量插入，但需要处理唯一性约束冲突
                $newTagIds = $this->batchInsertTagsWithConflictHandling($newTagsData);
                $tagIds = array_merge($tagIds, $newTagIds);
            }

        } catch (\Exception $e) {
            Log::error("Batch processing tags failed", [
                'tag_names' => $tagNames,
                'error' => $e->getMessage()
            ]);
        }

        return $tagIds;
    }

    /**
     * 批量创建FAQ-标签关联关系
     *
     * @param int $faqId FAQ ID
     * @param array $tagIds 标签ID数组
     * @return void
     */
    private function batchCreateFaqTagRelations(int $faqId, array $tagIds): void
    {
        if (empty($tagIds)) {
            return;
        }

        $relationData = [];
        $currentTime = now();

        foreach (array_unique($tagIds) as $tagId) {
            $relationData[] = [
                'faq_id' => $faqId,
                'tag_id' => $tagId,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];
        }

        try {
            // 批量插入关联关系
            FaqTag::insert($relationData);
        } catch (\Illuminate\Database\QueryException $e) {
            // 如果批量插入失败，记录错误并尝试逐个插入
            Log::warning("Batch insert FAQ-tag relations failed, trying individual inserts", [
                'faq_id' => $faqId,
                'tag_ids' => $tagIds,
                'error' => $e->getMessage()
            ]);

            foreach ($tagIds as $tagId) {
                try {
                    FaqTag::create([
                        'faq_id' => $faqId,
                        'tag_id' => $tagId,
                    ]);
                } catch (\Exception $individualError) {
                    Log::error("Failed to create FAQ-tag relation", [
                        'faq_id' => $faqId,
                        'tag_id' => $tagId,
                        'error' => $individualError->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * 批量插入标签，处理唯一性约束冲突
     *
     * @param array $tagsData 标签数据数组
     * @return array 返回新创建的标签ID数组
     */
    private function batchInsertTagsWithConflictHandling(array $tagsData): array
    {
        $newTagIds = [];

        try {
            // 尝试批量插入
            Tag::insert($tagsData);

            // 获取刚插入的标签ID
            $names = array_column($tagsData, 'name');
            $newTags = Tag::whereIn('name', $names)
                ->whereNull('deleted_at')
                ->get();

            $newTagIds = $newTags->pluck('id')->toArray();

        } catch (\Illuminate\Database\QueryException $e) {
            // 如果批量插入失败（可能由于唯一性约束），逐个处理
            if ($e->getCode() == 23000) { // Integrity constraint violation
                Log::info("Batch insert failed due to constraint, falling back to individual processing");

                foreach ($tagsData as $tagData) {
                    $tagId = $this->findOrCreateTagByName($tagData['name']);
                    if ($tagId) {
                        $newTagIds[] = $tagId;
                    }
                }
            } else {
                throw $e;
            }
        }

        return $newTagIds;
    }

    /**
     * 根据标签名称查找或创建标签（单个处理，用于批量失败时的回退）
     * 处理唯一性约束 UNIQUE KEY uk_name (name, deleted_at)
     *
     * @param string $tagName 标签名称
     * @return int|null 返回标签ID，失败返回null
     */
    private function findOrCreateTagByName(string $tagName): ?int
    {
        try {
            // 首先尝试查找现有的标签（未删除的）
            $existingTag = Tag::where('name', $tagName)
                ->whereNull('deleted_at')
                ->first();

            if ($existingTag) {
                return $existingTag->id;
            }

            // 如果不存在，创建新标签
            $newTag = Tag::create([
                'name' => $tagName,
                'reference_name' => $tagName, // 使用相同的名称作为参考名称
                'category_id' => 1, // 默认分类ID，可以根据业务需求调整
                'status' => 1, // 默认启用
                'sort_order' => 0,
                'created_by' => 0,
                'updated_by' => 0,
            ]);

            return $newTag->id;

        } catch (\Illuminate\Database\QueryException $e) {
            // 处理唯一性约束冲突
            if ($e->getCode() == 23000) { // Integrity constraint violation
                Log::warning("Tag name '{$tagName}' already exists or constraint violation", [
                    'error' => $e->getMessage()
                ]);

                // 再次尝试查找，可能是并发创建导致的
                $existingTag = Tag::where('name', $tagName)
                    ->whereNull('deleted_at')
                    ->first();

                return $existingTag ? $existingTag->id : null;
            }

            Log::error("Failed to create tag '{$tagName}'", [
                'error' => $e->getMessage()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error("Unexpected error when processing tag '{$tagName}'", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 生成唯一的FAQ代码
     * 格式：FAQ-YYYYMMDD-HHMMSS-随机数
     *
     * @return string 返回唯一的FAQ代码
     */
    private function generateUniqueFaqCode(): string
    {
        $maxAttempts = 10;
        $attempt = 0;

        do {
            $attempt++;

            // 生成基础代码：FAQ-日期时间-随机数
            $dateTime = now()->format('YmdHis');
            $randomSuffix = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
            $code = "FAQ-{$dateTime}-{$randomSuffix}";

            // 检查代码是否已存在
            $exists = Faq::where('code', $code)->exists();

            if (!$exists) {
                return $code;
            }

            // 如果存在冲突，稍微延迟后重试
            if ($attempt < $maxAttempts) {
                usleep(1000); // 延迟1毫秒
            }

        } while ($attempt < $maxAttempts);

        // 如果多次尝试都失败，使用UUID作为后缀确保唯一性
        $uuid = substr(str_replace('-', '', \Illuminate\Support\Str::uuid()), 0, 8);
        return "FAQ-{$dateTime}-{$uuid}";
    }
}
