<template>
  <div class="piece-box activity-log-box mt-2.5">
    <div class="piece-tit flex justify-between items-center">
      <div class="tit">Intrution Detection System</div>
    </div>
    <div class="piece-con">
      <div class="e-chart-box">
        <div class="chart-tit">
          <el-icon color="#ababab" size="20"><PieChart /></el-icon>
          <span class="num">6</span>
          <span class="span">No. of times protected</span>
          <el-button color="#FFD100" round>May 2023</el-button>
        </div>

        <div class="bar-chart" ref="ProtectedChart"></div>
      </div>
      <div class="btn-list">
        <el-button color="#fff">
          <el-icon size="20"><component is="Search" /></el-icon>
          <span class="num">1</span>
          Files Scanning
        </el-button>
        <el-button color="#fff">
          <el-icon size="20"><Warning /></el-icon>
          <span class="num">1</span>
          Files Scanning
        </el-button>
      </div>
      <div class="e-chart-box">
        <div class="chart-tit">
          <el-icon color="#ababab" size="20"><Connection /></el-icon>
          <span class="span">DDOS Firewall</span>
          <el-button color="#FFD100" round>May 2023</el-button>
        </div>

        <div class="bar-chart" ref="FirewallChart"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const ProtectedChart = ref(null)
let ProtectedInstance: any = null;
const FirewallChart = ref(null)
let FirewallInstance: any = null;
onMounted(() => {
  ProtectedInstance = echarts.init(ProtectedChart.value);
  ProtectedInstance.setOption({
    color: "#ffd100",
    grid: {
        left: '6%',
        right: '6%',
        top: '6%',
        bottom: '6%',
        containLabel: true
    },
    xAxis: {
      data: [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31],
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      max: 6,
      axisLabel: {
        height: 20
      }
    },
    series: [
      {
        name: 'Date',
        type: 'bar',
        data: [1, 0, 1, 0, 2, 2, 3, 1, 5, 0, 3, 4, 5, 1 ,1, 5],
      },
    ],
  })
  FirewallInstance = echarts.init(FirewallChart.value);
  FirewallInstance.setOption({
    color: "#ffd100",
    grid: {
        left: '6%',
        right: '6%',
        top: '6%',
        bottom: '6%',
        containLabel: true
    },
    xAxis: {
      data: [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31],
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      max: 6,
      axisLabel: {
        height: 20
      }
    },
    series: [
      {
        name: 'Date',
        type: 'bar',
        data: [1, 0, 1, 0, 2, 2, 3, 1, 5, 0, 3, 4, 5, 1 ,1, 5],
      },
    ],
  })
})
onUnmounted(() => {
  if (ProtectedInstance) {
    ProtectedInstance.dispose()
    ProtectedInstance = null
  }
  if (FirewallInstance) {
    FirewallInstance.dispose()
    FirewallInstance = null
  }
})
</script>

<style lang="scss" scoped>
.activity-log-box {
  .piece-con {
    border-radius: var(--el-table-border-radius);
    color: #050505;

    .e-chart-box {
      border-radius: var(--el-table-border-radius);
      padding: 10px;
      background-color: #fff;

      .chart-tit {
        display: flex;
        align-items: center;

        .num {
          margin-left: 10px;
          display: block;
          font-size: 16px;
          color: #ffd100;
          font-weight: bold;
        }

        .span {
          margin-left: 10px;
          margin-right: 10px;
          display: block;
          color: #ababab;
          font-size: 12px;
        }
      }

      .bar-chart {
        width: 100%;
        height: 180px;
      }
    }

    .btn-list {
      margin-top: 10px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;

      .el-button {
        font-size: 12px;
        width: calc(50% - 6px);
        height: auto;
      }

      .num {
        margin-left: 10px;
        margin-right: 10px;
        display: block;
        font-size: 16px;
        color: #ffd100;
        font-weight: bold;
      }
    }
  }
}
</style>
