@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;

  .form-register {
    margin: 60px auto;
    box-shadow: 0 0 10px #eee;
    border-radius: 5px;
    border: 1px solid #cecece;
    padding: 30px 50px;
    background-color: #fff;
    max-width: 480px;

    .df(center, flex-start, column);

    .logo {
      margin-bottom: 30px;
      width: 32%;
    }
    
    .tabs {
      width: 100%;

      .tab-list {
        margin-bottom: 30px;
        .df(center, center);

        .tab-item {
          margin: 0 20px;
          padding: 6px 0;
          color: #333;
          font-size: 24px;
          position: relative;
          cursor: pointer;

          &::before {
            border-radius: 2px;
            content: '';
            width: 0;
            height: 3px;
            transition: height .3s;
            background-color: #ff9600;

            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
          }

          &.active {
            color: #ff9600;

            &::before {
              width: 30%;
            }
          }
        }
      }
    
      .pane-list {
        width: 100%;
        position: relative;

        .tab-pane {
          width: 100%;
          position: absolute;
          left: 0;
          top: 0;
          z-index: -1;
          opacity: 0;
    
          &.active {
            position: static;
            opacity: 1;
          }
    

          form {
            .df(stretch, flex-start, column);

            .inp-box {
              margin-bottom: 16px;
              border-radius: 0px;
              border: 1px solid #eaeaea;
              padding: 6px 14px;
              background-color: #f7f7f7;
              font-size: 18px;
              line-height: 2.55;
              width: 100%;
            }

            .has-btn-inp {
              .df();
              margin-bottom: 16px;
              border: 1px solid #eaeaea;
              background-color: #f7f7f7;

              .inp-box {
                margin-bottom: 0;
                border: none;
              }

              button {
                margin-bottom: 0;
                .btn-radius(4px, 6px, 14px, #fff, #999, #999, #fff);
                white-space: nowrap;
              }
            }
    
            .check-box {
              margin-bottom: 16px;
              .df(center);
    
              label {
                margin-left: 5px;
                font-size: 14px;
                color: #999;

                a {
                  font-size: 14px;
                  color: #999;
                }
              }
            }
    
            button {
              margin-bottom: 16px;
              .btn-radius(0, 22px, 22px, #fff, #ff9600, #ffbe99, #fff);

              &[disabled] {
                color: #ffbe99;
              }
            }
    
            .existing {
              .df(center, flex-end);

              a {
                margin-left: 10px;
                display: block;
                font-size: 14px;
                color: #999;
              }
            }
          }
        }
      }
    }
  }
}