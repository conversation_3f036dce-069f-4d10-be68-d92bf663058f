# Popover 弹出框组件

一个基于 Element Plus 的 Popover 组件封装，用于创建自定义的弹出框内容。

## 基础用法

```vue
<template>
  <popover v-model="visible">
    <template #reference>
      <el-button>点击触发</el-button>
    </template>
    
    <div>弹出框内容</div>
    
    <template #footer>
      <div class="footer-buttons">
        <el-button>取消</el-button>
        <el-button type="primary">确定</el-button>
      </div>
    </template>
  </popover>
</template>

<script setup>
import { ref } from 'vue'
import Popover from '@/components/popover/index.vue'

const visible = ref(false)
</script>
```

## 属性说明

| 属性名 | 说明 | 类型 | 默认值 |
|--------|------|------|--------|
| modelValue | 是否显示弹出框 | boolean | false |
| width | 弹出框宽度 | number | 500 |
| popperClass | 弹出框的自定义类名 | string | 'table-page' |
| maxHeight | 弹出框内容最大高度 | number | 360 |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 弹出框的主要内容 |
| reference | 触发弹出框的元素 |
| footer | 弹出框底部内容，通常用于放置操作按钮 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 当弹出框显示状态改变时触发 | (value: boolean) |

## 特点

1. 自动处理点击外部关闭
2. 支持自定义宽度和高度
3. 提供默认的滚动条样式
4. 支持底部按钮区域
5. 使用 v-model 双向绑定控制显示状态

## 注意事项

1. 组件内部已经处理了点击外部自动关闭的逻辑
2. 弹出框内容区域会自动添加滚动条，当内容超出 maxHeight 时
3. 可以通过 popperClass 自定义弹出框的样式
4. 建议在 footer 插槽中放置操作按钮，以保持统一的交互体验
