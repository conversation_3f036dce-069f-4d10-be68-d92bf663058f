<?php

namespace Modules\Common\Services;

use Modules\Common\Domain\Modules;

class OptionsService
{
    private Modules $modules;
    public function __construct(Modules $modules)
    {
        $this->modules = $modules;

    }

    /**
     * 获取所有启用的模块
     * @return array
     */
    public function getAllEnabledModules(): array
    {
        return $this->modules->getAllEnabledModules();
    }

    public function getAllEnabledModulesConfig(): array
    {
        return $this->modules->getAllEnabledModulesConfig();
    }

}
