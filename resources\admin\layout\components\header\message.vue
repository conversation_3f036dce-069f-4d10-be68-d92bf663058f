<template>
    <el-popover placement="bottom" :width="375" trigger="hover" :show-arrow="false"
                :disabled="!msgInfo.has_unread_messages">
        <template #reference>
            <div class="relative flex items-center justify-center ml-3 bg-white rounded-full cursor-pointer icon-box"
                 @click="router.push(`/message/messageList`)">
                <div
                    class="absolute flex items-center justify-center w-4 h-4 text-xs text-white bg-red-700 rounded-full top-3 right-3"
                    v-if="msgInfo.has_unread_messages">{{ msgInfo.unread_count }}
                </div>
                <div class="icons">
                    <font-awesome-icon :icon="['far', 'bell']" class="fa-lg" :style="{ color: '#000' }" />
                </div>
            </div>
        </template>
        <template #default>
            <div class="flex flex-col menu-box">
                <div class="flex flex-wrap menu-content">
                    <div class="w-full mt-6" v-for="(msg, index) in msgList" :key="index" @click="goPage(msg.id)">
                        <div class="flex items-center justify-center cursor-pointer">
                            <div
                                class="flex items-center justify-center bg-gray-300 rounded-full w-14 h-14 pic shrink-0">
                                <font-awesome-icon :icon="['fas', 'pen']" />
                            </div>
                            <div class="flex-1 pl-3 text-sm text-black truncate">{{ msg.title }}</div>
                        </div>
                    </div>
                </div>
                <div class="cursor-pointer more-btn" @click="router.push(`/message/messageList`)">{{ $t('system.more')
                    }}
                </div>
            </div>
        </template>
    </el-popover>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

import http from '/admin/support/http'
import { useRouter } from 'vue-router'

interface MsgItem {
    id: number
    sender_id: number
    receiver_id: number
    type: number
    title: string
    content: string
    is_read: boolean
    status: boolean
    priority: number
    creator_id: number
    created_at: string
    updated_at: string
    deleted_at?: string
}

const api = 'message'
const router = useRouter()

const msgInfo = ref({
    has_unread_messages: false,
    unread_count: 0,
})
const msgList = ref<MsgItem[]>([])

function getInspect() {
    http.get(`${api}/messages/has-unread`).then(res => {
        msgInfo.value = res.data.data
    })
}

function getMsgList() {
    http.get(`${api}/messages/received`).then(res => {
        const messages = res.data.data
        if (Array.isArray(messages)) {
            msgList.value = messages.slice(0, 4) // 仅保留前4条消息
        } else {
            msgList.value = []
        }
    }).catch(error => {
        console.error('Error fetching messages:', error)
        msgList.value = [] // 确保即使出现错误，msgList 也是一个空数组
    })
}


const goPage = (id: number) => {
    http.put(`${api}/messages/${id}/read`).then(() => {
        getInspect()
        getMsgList()
        router.push(`/message/messageDetail/${id}`)
    })

}

onMounted(() => {
    // getInspect()
    // getMsgList()
})
</script>
