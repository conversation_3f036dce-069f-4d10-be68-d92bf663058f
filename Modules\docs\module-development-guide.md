# TVB项目模块开发最佳实践指南

## 概述

本文档基于Region模块的开发实践，总结了在TVB项目（基于Laravel 11 + Bingo框架）中创建新模块的完整流程和最佳实践。

## 项目架构说明

TVB项目使用Bingo框架的模块化架构：
- **自动发现机制**：模块通过`config/bingo.php`配置自动发现和加载
- **Common模块集成**：新模块可以作为Common模块的子模块集成
- **统一路由前缀**：API路由统一使用`/api`前缀

## 时区和时间格式最佳实践（重要）

### 全局配置

在开始模块开发前，确保项目已正确配置时区和时间格式：

#### 1. 应用时区配置
```php
// config/app.php
'timezone' => 'Asia/Hong_Kong',  // 设置为香港时区
```

#### 2. AppServiceProvider配置
```php
// app/Providers/AppServiceProvider.php
public function boot()
{
    // 设置PHP时区与Laravel应用时区一致
    date_default_timezone_set(config('app.timezone'));
    
    // 全局设置Carbon日期序列化格式
    Carbon::serializeUsing(function ($carbon) {
        return $carbon->format('Y-m-d H:i:s');
    });
}
```

### 时间格式处理原则

1. **数据库存储**：使用TIMESTAMP类型（Laravel标准）
2. **API返回格式**：统一使用`Y-m-d H:i:s`格式
3. **时区处理**：数据库存储UTC，API返回本地时区
4. **模型序列化**：重写`serializeDate`方法确保格式统一

## 模块创建步骤

### 1. 创建模块目录结构

```
Modules/YourModule/
├── Api/
│   ├── Controllers/
│   │   └── YourModuleController.php
│   └── route.php
├── Models/
│   └── YourModel.php
├── Services/
│   └── YourModuleService.php
├── Providers/
│   └── YourModuleServiceProvider.php
├── Lang/
│   └── zh_CN/
│       └── yourmodule.php
├── config.json
└── package.json
```

### 2. 配置文件设置

#### config.json（关键配置）
```json
{
    "name": "Common",  // 重要：设置为Common以便自动加载
    "title": "模块标题",
    "env": ["laravel11"],
    "types": ["Arch"],
    "tags": ["功能标签"],
    "bingostartVersion": ">=0.19",
    "version": "1.0.0",
    "author": "coso",
    "keywords": "模块关键词",
    "description": "模块描述",
    "providers": [],  // 重要：保持空数组
    "config": {}
}
```

#### package.json
```json
{
    "name": "@bwms/yourmodule-module",
    "version": "1.0.0",
    "private": true,
    "type": "module",
    "dependencies": {}
}
```

### 3. 数据库迁移文件

创建`database/migrations/YYYY_MM_DD_HHMMSS_create_tvb_your_table.php`：

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('tvb_your_table', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 100)->comment('名称');
            $table->text('description')->nullable()->comment('描述');
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用 1-启用');
            $table->unsignedBigInteger('created_by')->comment('创建人');
            $table->unsignedBigInteger('updated_by')->comment('更新人');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index('status', 'idx_status');
            $table->index('name', 'idx_name');
            $table->index('created_at', 'idx_created_at');
        });

        // 插入示例数据
        DB::table('tvb_your_table')->insert([
            [
                'name' => '示例数据',
                'description' => '示例描述',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    public function down()
    {
        Schema::dropIfExists('tvb_your_table');
    }
};
```

### 4. 模型文件（重要更新）

`Modules/YourModule/Models/YourModel.php`：

```php
<?php

namespace Modules\YourModule\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class YourModel extends Model
{
    use SoftDeletes;

    // 注意：表名不要包含tvb_前缀，Laravel会自动处理
    protected $table = 'your_table';  // 不是 tvb_your_table
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'description',
        'status',
        'created_by',
        'updated_by',
    ];

    /**
     * 类型转换配置
     * 注意：不要设置时间字段的cast，避免与serializeDate冲突
     */
    protected $casts = [
        'status' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
    ];

    /**
     * 序列化时间字段为指定格式（关键方法）
     * 确保API返回统一的时间格式：Y-m-d H:i:s
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    }

    // 查询作用域
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    public function scopeDisabled($query)
    {
        return $query->where('status', 0);
    }

    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }
}
```

### 5. 服务提供者

`Modules/YourModule/Providers/YourModuleServiceProvider.php`：

```php
<?php

namespace Modules\YourModule\Providers;

use Bingo\Providers\BingoModuleServiceProvider;

class YourModuleServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        // 加载语言文件
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'YourModule'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'YourModule');

        // 注册导航（保持简单）
        $this->registerNavigation();
        
        // 加载视图
        $this->loadViewsFrom(dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'YourModule'.DIRECTORY_SEPARATOR.'views', 'YourModule');
        
        // 注册模块权限
        $this->registerModulePermissions();
        
        // 注册小部件管理器
        $this->registerWidgetManager();
        
        // 注册API路由
        $this->registerApiRoutes();
    }

    protected function navigation(): array
    {
        return []; // 保持空数组，避免导航配置错误
    }

    public function moduleName(): string
    {
        return 'YourModule';
    }

    public function registerSettings(): array
    {
        return [];
    }

    protected function registerApiRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Api/route.php');
    }
}
```

### 6. 路由文件

`Modules/YourModule/Api/route.php`：

```php
<?php

use Illuminate\Support\Facades\Route;
use Modules\YourModule\Api\Controllers\YourModuleController;

Route::prefix('yourmodule')->group(function () {
    
    // 基本CRUD
    Route::get('/', [YourModuleController::class, 'index'])->name('yourmodule.index');
    Route::post('/', [YourModuleController::class, 'store'])->name('yourmodule.store');
    Route::get('/{id}', [YourModuleController::class, 'show'])->name('yourmodule.show');
    Route::put('/{id}', [YourModuleController::class, 'update'])->name('yourmodule.update');
    Route::delete('/{id}', [YourModuleController::class, 'destroy'])->name('yourmodule.destroy');
    
    // 状态管理
    Route::patch('/{id}/status', [YourModuleController::class, 'updateStatus'])->name('yourmodule.status');
    
    // 批量操作
    Route::post('/batch', [YourModuleController::class, 'batchAction'])->name('yourmodule.batch');
    
});
```

### 7. 控制器（重要更新）

`Modules/YourModule/Api/Controllers/YourModuleController.php`：

```php
<?php

namespace Modules\YourModule\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\YourModule\Models\YourModel;
use Modules\YourModule\Services\YourModuleService;

class YourModuleController extends Controller
{
    protected YourModuleService $service;

    public function __construct(YourModuleService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取列表（关键：正确处理分页数据序列化）
     */
    public function index(Request $request): array
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 20);
        $name = $request->input('name', '');
        $status = $request->input('status', '');

        $query = YourModel::query();

        if (!empty($name)) {
            $query->where('name', 'like', '%' . $name . '%');
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $query->orderBy('id', 'desc');
        $items = $query->paginate($limit, ['*'], 'page', $page);

        // 关键：确保分页数据也触发模型的serializeDate方法
        return [
            'total' => $items->total(),
            'page' => $items->currentPage(),
            'limit' => $items->perPage(),
            'items' => $items->getCollection()->map(function ($item) {
                return $item->toArray();  // 触发serializeDate
            })->toArray(),
        ];
    }

    /**
     * 创建记录
     */
    public function store(Request $request): array
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'status' => 'required|integer|in:0,1'
        ]);

        // 固定设置操作人ID（避免认证问题）
        $validated['created_by'] = 1;
        $validated['updated_by'] = 1;

        $item = YourModel::create($validated);

        return $item->toArray();  // 自动触发serializeDate
    }

    /**
     * 更新记录
     */
    public function update(Request $request, $id): array
    {
        $item = YourModel::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'status' => 'required|integer|in:0,1'
        ]);

        $validated['updated_by'] = 1;

        $item->update($validated);

        return $item->fresh()->toArray();
    }

    // ... 其他CRUD方法
}
```

### 8. 服务类

`Modules/YourModule/Services/YourModuleService.php`：

```php
<?php

namespace Modules\YourModule\Services;

use Modules\YourModule\Models\YourModel;
use Illuminate\Pagination\LengthAwarePaginator;

class YourModuleService
{
    public function getList(array $params): LengthAwarePaginator
    {
        $query = YourModel::query();

        // 搜索条件
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        if (isset($params['status'])) {
            if ($params['status'] == 1) {
                $query->enabled();
            } else {
                $query->disabled();
            }
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    // ... 其他业务方法
}
```

### 9. 语言文件

`Modules/YourModule/Lang/zh_CN/yourmodule.php`：

```php
<?php

return [
    // 基础字段
    'name' => '名称',
    'description' => '描述',
    'status' => '状态',
    'created_by' => '创建人',
    'updated_by' => '更新人',
    'created_at' => '创建时间',
    'updated_at' => '更新时间',

    // 状态
    'status_enabled' => '已启用',
    'status_disabled' => '已禁用',

    // 操作
    'create' => '新增',
    'edit' => '编辑',
    'delete' => '删除',
    'view' => '查看',

    // 消息
    'create_success' => '创建成功',
    'update_success' => '更新成功',
    'delete_success' => '删除成功',

    // 验证消息
    'validation' => [
        'name.required' => '名称不能为空',
        'name.unique' => '名称已存在',
        'name.max' => '名称最多100个字符',
    ]
];
```

## 模块激活步骤

### 1. 更新bingo配置（可选）

在`config/bingo.php`中添加模块（通常不需要，因为使用Common模块集成）：

```php
'modules' => [
    'Common' => [
        'enable' => true,
        'path' => 'Modules/Common',
    ],
    // 其他模块...
],
```

### 2. 清除缓存

```bash
php artisan optimize:clear
```

## 常见问题与解决方案

### 问题1：时间格式不统一

**现象**：API返回时间格式为ISO 8601（`2025-07-20T02:56:10.000000Z`），而期望`Y-m-d H:i:s`格式

**解决方案**：
1. 在模型中添加`serializeDate`方法
2. 控制器分页数据使用`map()->toArray()`处理
3. 避免在模型`$casts`中设置时间字段

### 问题2：时区显示不正确

**现象**：API返回UTC时间，与本地时间相差8小时

**解决方案**：
1. 配置正确的应用时区：`Asia/Hong_Kong`
2. 在`AppServiceProvider`中同步PHP时区
3. 模型`serializeDate`中设置时区转换

### 问题3：分页数据序列化问题

**现象**：列表接口时间格式与单个对象不一致

**解决方案**：
```php
// 正确的分页数据处理方式
'items' => $items->getCollection()->map(function ($item) {
    return $item->toArray();  // 触发serializeDate
})->toArray(),
```

### 问题4：数据库表前缀问题

**现象**：查询时出现表不存在错误

**解决方案**：
1. 模型中表名不要包含`tvb_`前缀
2. Laravel会根据配置自动添加前缀
3. 迁移文件中使用完整表名（包含前缀）

### 问题5：认证相关错误

**现象**：`iam_token`驱动不存在等认证错误

**解决方案**：
1. 在开发阶段使用固定的用户ID（如：1）
2. 避免使用`auth()->id()`获取当前用户
3. 后期根据实际认证系统调整

## 最佳实践检查清单

### 开发前检查
- [ ] 确认项目时区配置正确
- [ ] 确认`AppServiceProvider`中的时间处理配置
- [ ] 了解项目的模块加载机制

### 模型开发检查
- [ ] 表名不包含前缀（Laravel自动处理）
- [ ] 添加`serializeDate`方法处理时间格式
- [ ] 正确配置`$fillable`和`$casts`
- [ ] 避免时间字段的cast配置

### 控制器开发检查
- [ ] 列表接口使用正确的分页数据序列化
- [ ] 固定设置操作人ID避免认证问题
- [ ] 统一返回数据格式
- [ ] 添加适当的参数验证

### 部署前检查
- [ ] 清除所有缓存
- [ ] 测试所有API接口
- [ ] 验证时间格式和时区正确性
- [ ] 确认数据库表结构正确

## 相关文档

- [Laravel时区与时间格式最佳实践指南](./timezone-datetime-best-practices.md)
- [TVB项目架构文档](../architecture-guide.md)

遵循这套最佳实践，可以确保新模块与项目整体架构保持一致，避免常见的开发陷阱。

```php
'installed' => [
    'Common' => ['enable' => true],
    'Faq' => ['enable' => true],
    'YourModule' => ['enable' => true], // 如果独立模块才需要
],
```

### 2. 运行迁移

```bash
# 创建数据库表
php artisan migrate

# 清除缓存
php artisan optimize:clear

# 验证路由注册
php artisan route:list | grep yourmodule
```

## 测试API

### Postman测试示例

1. **获取列表**：`GET /api/yourmodule`
2. **创建记录**：`POST /api/yourmodule`
3. **获取单条**：`GET /api/yourmodule/{id}`
4. **更新记录**：`PUT /api/yourmodule/{id}`
5. **删除记录**：`DELETE /api/yourmodule/{id}`
6. **状态更新**：`PATCH /api/yourmodule/{id}/status`
7. **批量操作**：`POST /api/yourmodule/batch`

## 重要注意事项

### ✅ 成功关键点

1. **模块名称配置**：config.json中`name`必须设为`"Common"`
2. **空providers数组**：`"providers": []`确保自动发现
3. **简化导航配置**：`navigation()`方法返回空数组避免验证错误
4. **Common模块启用**：确保`config/bingo.php`中Common模块已启用

### ❌ 常见错误

1. ❌ 在config.json中使用模块自己的名称作为`name`
2. ❌ 在providers数组中手动添加服务提供者
3. ❌ 复杂的导航配置导致验证失败
4. ❌ 忘记运行`php artisan optimize:clear`清除缓存

### 🔧 调试技巧

1. **路由检查**：`php artisan route:list | grep yourmodule`
2. **缓存清除**：`php artisan optimize:clear`
3. **错误日志**：检查`storage/logs/laravel.log`
4. **配置检查**：确认`config/bingo.php`配置正确

## 总结

通过遵循本指南，可以快速创建符合TVB项目架构的新模块。关键是理解Bingo框架的模块自动发现机制，并正确配置模块以作为Common模块的一部分进行集成。

这种方式确保了：
- ✅ 模块自动加载
- ✅ 路由正确注册
- ✅ 与现有架构兼容
- ✅ 开发效率最大化

记住：**简单即美，遵循现有成功模块的模式是最佳实践！** 