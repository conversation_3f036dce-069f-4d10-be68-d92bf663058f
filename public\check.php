<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BWMS 环境检查</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>o, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", <PERSON><PERSON>, sans-self;
            background-color: #f3f4f6; /* Tailwind gray-100 */
            color: #374151; /* Tailwind gray-700 */
            margin: 0;
            padding: 2rem; /* Increased padding for better spacing */
        }
        .container {
            max-width: 1000px; /* Slightly wider for better content display */
            width: 100%;
            background-color: transparent; /* Container itself is transparent, cards will have bg */
            border: none; /* Remove container border */
            box-shadow: none; /* Remove container shadow */
            margin: 0 auto;
        }
        h1 {
            font-family: 'Noto Serif SC', serif;
            color: #1f2937; /* Tailwind gray-800 */
        }
        .page-header {
            margin-bottom: 2.5rem;
            text-align: center;
        }
        .page-header img {
            height: 4rem; /* 64px */
            margin-bottom: 1rem;
        }
        .page-header h1 {
            font-size: 2.25rem; /* text-4xl */
            font-weight: 700;
        }
        .page-header p {
            margin-top: 0.5rem;
            color: #4b5563; /* Tailwind gray-600 */
            font-size: 1.125rem; /* text-lg */
        }

        /* Card styling for sections */
        .content-card {
            background-color: #ffffff;
            border-radius: 0.75rem; /* 12px */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* Tailwind shadow-md */
            margin-bottom: 2rem;
            padding: 1.5rem; /* 24px */
        }

        .section-title {
            font-family: 'Noto Serif SC', serif;
            color: #1e3a8a; /* Tailwind blue-800 or a darker blue */
            padding-bottom: 0.75rem; /* 12px */
            margin-top: 0; /* Reset margin-top as it's inside a card */
            margin-bottom: 1.5rem; /* 24px */
            font-size: 1.5rem; /* text-2xl */
            font-weight: 600;
            border-bottom: 2px solid #dbeafe; /* Tailwind blue-100 */
        }

        /* Env type selection card */
        .env-selection-card h2 {
            font-size: 1.25rem; /* text-xl */
            font-weight: 600;
            color: #1f2937; /* gray-800 */
            margin-bottom: 0.75rem;
        }
        .env-selection-card form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        @media (min-width: 640px) { /* sm breakpoint */
            .env-selection-card form {
                flex-direction: row;
                align-items: center;
            }
        }
        .env-selection-card select {
            border-color: #d1d5db; /* gray-300 */
            box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05); /* shadow-sm */
        }
        .env-selection-card button[type="submit"] {
            background-color: #2563eb; /* blue-600 */
            color: white;
            font-weight: 600;
            padding: 0.625rem 1rem; /* py-2.5 px-4 */
            border-radius: 0.375rem; /* rounded-md */
        }
        .env-selection-card button[type="submit"]:hover {
            background-color: #1d4ed8; /* blue-700 */
        }

        .info-table {
            width: 100%;
            border-collapse: separate; /* Use separate for border-spacing */
            border-spacing: 0;
            margin-bottom: 1rem;
            border: 1px solid #e5e7eb; /* gray-200 */
            border-radius: 0.5rem; /* 8px */
            overflow: hidden; /* To make border-radius work on table */
        }
        .info-table th, .info-table td {
            border-bottom: 1px solid #e5e7eb; /* gray-200 */
            padding: 0.75rem 1rem; /* 12px 16px */
            text-align: left;
            vertical-align: top;
        }
        .info-table tr:last-child th,
        .info-table tr:last-child td {
            border-bottom: none;
        }
        .info-table th {
            background-color: #f9fafb; /* gray-50 */
            width: 25%; /* Adjusted width */
            font-weight: 500; /* Noto Sans SC medium */
            color: #374151; /* gray-700 */
            font-size: 0.875rem; /* text-sm */
        }
        .info-table td {
            background-color: #fff;
            word-break: break-all;
            font-size: 0.875rem; /* text-sm */
            color: #4b5563; /* gray-600 */
        }
        .info-table td strong {
            color: #1f2937; /* gray-800 */
            font-weight: 500;
        }

        .status-icon {
            font-size: 1rem; /* text-base */
            margin-right: 0.5rem; /* 8px */
        }
        .status-icon.success {
            color: #10b981; /* Tailwind green-500 */
        }
        .status-icon.error {
            color: #ef4444; /* Tailwind red-500 */
        }
        .details {
            font-size: 0.875rem; /* text-sm */
            color: #6b7280; /* gray-500 */
            margin-top: 0.25rem; /* 4px */
        }
        .details strong {
            font-weight: 600;
            color: #ef4444; /* red-500 for emphasis on missing extensions */
        }

        /* PHP Extensions List */
        .php-extensions-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 0.5rem;
            list-style: none;
            padding-left: 0;
        }
        .php-extensions-list li {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }
        .php-extensions-list .fa-check-circle { color: #10b981; /* green-500 */ }
        .php-extensions-list .fa-times-circle { color: #ef4444; /* red-500 */ }
        .php-extensions-list .text-red-500 { color: #ef4444; }
        .php-extensions-list .font-bold { font-weight: 600; }

        .alert-box {
            padding: 1.25rem; /* 20px */
            margin-top: 2rem;
            border-radius: 0.5rem; /* 8px */
            text-align: center;
            border-width: 1px;
            border-style: solid;
        }
        .alert-box.success {
            background-color: #f0fdf4; /* Tailwind green-50 */
            border-color: #bbf7d0; /* Tailwind green-200 */
            color: #15803d; /* Tailwind green-700 */
        }
        .alert-box.error {
            background-color: #fef2f2; /* Tailwind red-50 */
            border-color: #fecaca; /* Tailwind red-200 */
            color: #b91c1c; /* Tailwind red-700 */
        }
        .alert-box .icon {
            font-size: 2.5rem; /* 40px */
            margin-bottom: 0.75rem; /* 12px */
        }
        .alert-box h2 {
            font-size: 1.5rem; /* text-2xl */
            font-weight: 600;
            font-family: 'Noto Serif SC', serif;
        }
        .alert-box p {
            margin-top: 0.5rem;
            font-size: 1rem;
        }

        .info-box {
            padding: 1rem;
            background-color: #eff6ff; /* Tailwind blue-50 */
            border: 1px solid #bfdbfe; /* Tailwind blue-200 */
            border-radius: 0.5rem; /* 8px */
            color: #1e40af; /* Tailwind blue-700 */
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
        .info-box i {
            margin-right: 0.5rem;
        }

        .page-footer {
            padding-top: 1rem;
            margin-top: 2rem;
            font-size: 0.875rem; /* text-sm */
            text-align: center;
            color: #6b7280; /* gray-500 */
            border-top: 1px solid #e5e7eb; /* gray-200 */
        }
        .page-footer .font-semibold {
            font-weight: 600;
        }
        .page-footer .text-yellow-600 {
            color: #ca8a04; /* Tailwind yellow-600 */
        }
    </style>
</head>
<body class="p-4 sm:p-8">
    <div class="container mx-auto">
        <header class="page-header">
            <img src="https://www.hk-bingo.com/images/website/logo/logo.webp" alt="Bingo Logo" class="mx-auto">
            <h1>BWMS 环境兼容性检查</h1>
            <p>本页面将帮助您检测当前服务器环境是否满足 BWMS 的安装要求。</p>
        </header>

        <section class="content-card env-selection-card">
            <h2>选择您的部署环境类型：</h2>
            <form method="GET" action="check.php">
                <select name="env_type" id="env_type" class="block p-2 w-full rounded-md border shadow-sm sm:w-auto focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="standard" <?php echo (!isset($_GET['env_type']) || $_GET['env_type'] === 'standard') ? 'selected' : ''; ?>>标准/开发环境 (完整检测)</option>
                    <option value="shared_ftp" <?php echo (isset($_GET['env_type']) && $_GET['env_type'] === 'shared_ftp') ? 'selected' : ''; ?>>共享主机/FTP部署 (已编译)</option>
                </select>
                <button type="submit">选择并检测</button>
            </form>
            <?php
                $current_env_type = isset($_GET['env_type']) ? $_GET['env_type'] : 'standard';
                if ($current_env_type === 'shared_ftp') {
                    echo '<p class="mt-3 text-sm text-gray-500">共享主机/FTP部署模式将跳过 Node.js 和 Storage Link 的检测。</p>';
                }
            ?>
        </section>

        <div class="content-card">
        <?php
        // --- 获取当前选择的环境类型 ---
        $env_type = isset($_GET['env_type']) ? $_GET['env_type'] : 'standard'; // 'standard' 或 'shared_ftp'

        // --- 安全检查：仅在 APP_DEBUG=true 时运行 ---
        $app_debug_enabled = false;
        $env_path_for_debug_check = dirname(__DIR__) . '/.env';
        if (file_exists($env_path_for_debug_check)) {
            $env_content_for_debug_check = file_get_contents($env_path_for_debug_check);
            if (preg_match('/^APP_DEBUG\s*=\s*true/m', $env_content_for_debug_check)) {
                $app_debug_enabled = true;
            }
        }

        if (!$app_debug_enabled) :
        ?>
            <div class="alert-box error">
                <i class="fas fa-shield-alt icon"></i>
                <h2 class="text-2xl font-bold">安全限制</h2>
                <p class="mt-2">为确保安全，环境检查功能仅在 `APP_DEBUG` 设置为 `true` 时可用。</p>
                <p class="mt-1">请在您的 <code>.env</code> 文件中将 <code>APP_DEBUG</code> 设置为 <code>true</code> 后刷新页面以进行环境检查。</p>
                <p class="mt-3 text-sm"><strong>注意：</strong>完成检查后，请务必在生产环境中将 <code>APP_DEBUG</code> 设置回 <code>false</code>。</p>
            </div>
        <?php
        else:
        // --- APP_DEBUG is true, proceed with checks ---

        // --- 配置项 ---
        define('MIN_PHP_VERSION', '8.2.0');
        define('MIN_NODE_VERSION', '20.0.0');
        $required_php_extensions = [
            "bcmath", "Core", "ctype", "curl", "date", "dom", "exif",
            "fileinfo", "filter", "ftp", "gd", "gettext", "hash", "iconv",
            "imagick", "intl", "json", "libxml", "mbstring", "mysqli", "mysqlnd",
            "openssl", "pcntl", "pcre", "PDO", "pdo_mysql", "pdo_sqlite", "Phar", "posix", 
            "random", "Reflection", "session", "shmop", "SimpleXML", "soap", "sockets",
            "sodium", "SPL", "sqlite3", "standard", "sysvsem", "tokenizer", "xml", 
            "xmlreader", "xmlwriter", "Zend OPcache", "zip", "zlib"
        ];

        $project_root = dirname(__DIR__); // 项目根目录，假设 check.php 在 public 目录下

        // --- 辅助函数 ---
        function render_check_row($title, $status, $value = '', $details = '') {
            $icon_class = $status ? 'fa-check-circle success' : 'fa-times-circle error';
            echo "<tr>";
            echo "    <th>{$title}</th>";
            echo "    <td>";
            echo "        <i class='fas {$icon_class} status-icon'></i>";
            echo "        <strong>{$value}</strong>";
            if ($details) {
                echo "        <p class='details'>{$details}</p>";
            }
            echo "    </td>";
            echo "</tr>";
        }

        function render_php_extensions($required_extensions) {
            echo "<tr><th>PHP 扩展检查</th><td>";
            $loaded_extensions = get_loaded_extensions();
            $missing_extensions = [];
            $all_ok = true;

            echo '<ul class="php-extensions-list">';
            foreach ($required_extensions as $ext) {
                $is_loaded = in_array($ext, $loaded_extensions);
                if (!$is_loaded) {
                    $missing_extensions[] = $ext;
                    $all_ok = false;
                }
                $icon_color = $is_loaded ? 'text-green-500' : 'text-red-500 font-bold'; // 加粗缺失的扩展
                $icon = $is_loaded ? 'fa-check-circle' : 'fa-times-circle';
                echo "<li><i class='fas {$icon} {$icon_color} mr-2'></i><span class='{$icon_color}'>{$ext}</span></li>"; // 给缺失的扩展文本也加上醒目颜色和加粗
            }
            echo '</ul>';

            if (!$all_ok) {
                echo "<p class='p-3 mt-4 font-semibold text-red-700 bg-red-100 rounded-md border border-red-300 details'>"; // 增强背景和边框
                echo "<i class='mr-2 fas fa-exclamation-triangle'></i><strong>重要：以下 PHP 扩展缺失，请务必安装：</strong> " . implode(', ', $missing_extensions);
                echo "</p>";
            }
            echo "</td></tr>";
            return $all_ok;
        }

        function get_node_version() {
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                // Windows系统下，尝试使用 where node
                $node_path = trim(shell_exec('where node'));
                if ($node_path) {
                    $node_path_parts = explode("\n", $node_path);
                    $node_path = trim($node_path_parts[0]); // 取第一个找到的路径
                }
            } else {
                // Linux/macOS系统下，尝试使用 which node
                $node_path = trim(shell_exec('which node'));
            }

            if ($node_path && is_executable($node_path)) {
                $version_output = shell_exec('"' . $node_path . '" -v');
                if ($version_output) {
                    return trim(str_replace('v', '', $version_output));
                }
            }
            return null;
        }

        function check_directory_permissions($path, $expected_perms_dir = 0777, $expected_perms_file = 0666, $owner = 'www-data', $group = 'www-data') {
            $path = rtrim($path, '/');
            if (!file_exists($path)) {
                return ['status' => false, 'message' => "目录/文件不存在: {$path}"];
            }

            $actual_perms = substr(sprintf('%o', fileperms($path)), -4);
            $is_dir = is_dir($path);
            $expected_perms_str = $is_dir ? decoct($expected_perms_dir) : decoct($expected_perms_file);

            $perms_ok = true; // 简化权限检查，仅检查基本可读写
            if ($is_dir) {
                if (!is_readable($path) || !is_writable($path) || !is_executable($path)) $perms_ok = false;
            } else {
                if (!is_readable($path) || !is_writable($path)) $perms_ok = false;
            }

            $message = "路径: {$path}, 权限: {$actual_perms}";
            if (!$perms_ok) {
                 $message .= " (期望: {$expected_perms_str} 或更高，且可读写执行)";
            }

            // 递归检查子目录和文件 (简化版，只检查当前目录/文件)
            // 实际项目中，对于 storage 等目录，需要递归检查

            return ['status' => $perms_ok, 'message' => $message];
        }

        function check_env_file($project_root) {
            $env_path = $project_root . '/.env';
            if (file_exists($env_path)) {
                return ['status' => true, 'message' => '.env 文件存在于: ' . $env_path];
            } else {
                return ['status' => false, 'message' => '.env 文件不存在于: ' . $env_path . ' (请从 .env.example 复制并配置)'];
            }
        }

        function check_app_key($project_root) {
            $env_path = $project_root . '/.env';
            if (file_exists($env_path)) {
                $env_content = file_get_contents($env_path);
                if (preg_match('/^APP_KEY=(?!base64:s*$).+/m', $env_content, $matches)) {
                    if (trim(explode('=', $matches[0], 2)[1]) !== '') {
                         return ['status' => true, 'message' => 'APP_KEY 已设置.'];
                    } else {
                        return ['status' => false, 'message' => 'APP_KEY 未设置或为空，请运行 `php artisan key:generate`.'];
                    }
                } else {
                    return ['status' => false, 'message' => 'APP_KEY 未设置或为空，请运行 `php artisan key:generate`.'];
                }
            } else {
                return ['status' => false, 'message' => '.env 文件不存在，无法检查 APP_KEY.'];
            }
        }

        function check_storage_link($project_root) {
            $link_path = $project_root . '/public/storage';

            if (!function_exists('readlink')) {
                return ['status' => false, 'message' => '`readlink()` 函数不可用，无法检查 public/storage 链接。这在某些共享主机环境中是正常的。'];
            }

            if (is_link($link_path)) {
                $target_path = readlink($link_path);
                $expected_target = $project_root . '/storage/app/public';
                if (realpath($target_path) === realpath($expected_target)) {
                    return ['status' => true, 'message' => 'public/storage 链接正确指向 ' . $expected_target];
                } else {
                    return ['status' => false, 'message' => 'public/storage 链接指向错误: ' . $target_path . ' (期望: ' . $expected_target . '). 请运行 `php artisan storage:link`.'];
                }
            } else {
                return ['status' => false, 'message' => 'public/storage 链接不存在. 请运行 `php artisan storage:link`.'];
            }
        }

        $individual_check_statuses = [
            'php_version' => true,
            'php_extensions' => true,
            'node_version' => true, // Default to true, will be set based on env_type and check result
            'permissions' => true,
            'env_file' => true,
            'app_key' => true,
            'storage_link' => true, // Default to true, will be set based on env_type and check result
        ];
        $overall_status = true;
        ?>

        <section>
            <h2 class="section-title">服务器环境检查</h2>

            <?php
            // PHP 版本检查
            $php_version = PHP_VERSION;
            $php_version_ok = version_compare($php_version, MIN_PHP_VERSION, '>=');
            if (!$php_version_ok) $overall_status = false;
            $individual_check_statuses['php_version'] = $php_version_ok;
            render_check_row("PHP 版本", $php_version_ok, $php_version, "要求: >= " . MIN_PHP_VERSION);
            ?>
        </table>

        <h2 class="section-title">PHP 扩展检查</h2>
        <table class="info-table">
            <?php
            $php_extensions_ok = render_php_extensions($required_php_extensions);
            if (!$php_extensions_ok) $overall_status = false;
            $individual_check_statuses['php_extensions'] = $php_extensions_ok;
            ?>
        </table>

        <?php if ($env_type === 'standard'): ?>
        <h2 class="section-title">其他环境检查 (Node.js)</h2>
        <table class="info-table">
            <?php
            // Node.js 版本检查 (仅在标准环境下)
            $node_version = get_node_version();
            $node_version_ok = false;
            $node_details = "无法检测到 Node.js 或无法获取版本。请确保 Node.js 已安装并配置在系统 PATH 中。";
            if ($node_version) {
                $node_version_ok = version_compare($node_version, MIN_NODE_VERSION, '>=');
                $node_details = "当前版本: {$node_version}, 要求: >= " . MIN_NODE_VERSION;
            }
            if ($env_type === 'standard') {
                if (!$node_version) { // Not detected
                    $overall_status = false;
                    $individual_check_statuses['node_version'] = false;
                } else { // Detected, check version
                    $individual_check_statuses['node_version'] = $node_version_ok;
                    if (!$node_version_ok) {
                        $overall_status = false;
                    }
                }
            } else {
                // For non-standard env, node check is skipped, so it's considered 'passing' for this status array's purpose
                $individual_check_statuses['node_version'] = true;
            }
            render_check_row("Node.js 版本", $node_version_ok, $node_version ?: '未检测到', $node_details);
            ?>
        </table>
        <?php endif; // 结束 Node.js 检测的条件判断 ?>

        <h2 class="section-title">文件与目录权限检查</h2>
        <div class="info-box">
            <i class="fas fa-info-circle"></i> BWMS 需要对特定目录拥有写入权限。以下列表仅检查关键目录的基本可读写状态，更详细的权限设置请参考安装文档或 `prod.sh init` 命令。
        </div>
        <table class="info-table">
            <?php
            $paths_to_check = [
                ['path' => $project_root . '/storage', 'type' => 'dir_recursive', 'note' => '存储目录 (递归)'],
                ['path' => $project_root . '/bootstrap/cache', 'type' => 'dir', 'note' => '缓存目录'],
                ['path' => $project_root . '/storage/framework', 'type' => 'dir_recursive', 'note' => '框架存储 (递归)'],
                ['path' => $project_root . '/storage/logs', 'type' => 'dir_recursive', 'note' => '日志目录 (递归)'],
            ];

            foreach ($paths_to_check as $item) {
                $check_result = check_directory_permissions($item['path']);
                if (!$check_result['status']) {
                    $overall_status = false;
                    $individual_check_statuses['permissions'] = false;
                }
                render_check_row($item['note'], $check_result['status'], $check_result['message']);
            }
            ?>
        </table>

        <h2 class="section-title">项目配置检查</h2>
        <table class="info-table">
            <?php
            // .env 文件检查
            $env_check = check_env_file($project_root);
            if (!$env_check['status']) $overall_status = false;
            $individual_check_statuses['env_file'] = $env_check['status'];
            render_check_row(".env 文件", $env_check['status'], $env_check['message']);

            // APP_KEY 检查
            if ($env_check['status']) { // 仅当 .env 文件存在时检查 APP_KEY
                $app_key_check = check_app_key($project_root);
                if (!$app_key_check['status']) $overall_status = false;
                $individual_check_statuses['app_key'] = $app_key_check['status'];
                render_check_row("APP_KEY 配置", $app_key_check['status'], $app_key_check['message']);
            } else {
                $individual_check_statuses['app_key'] = false; // APP_KEY check effectively fails if .env doesn't exist for this purpose
                render_check_row("APP_KEY 配置", false, "无法检查，.env 文件不存在");
            }
            ?>
            <?php if ($env_type === 'standard'):
                // Storage Link 检查 (仅在标准环境下)
                $storage_link_check = check_storage_link($project_root);
                // This inner if ($env_type === 'standard') is redundant due to the outer one, but kept for clarity of original logic block
                // However, the logic for $individual_check_statuses['storage_link'] should be inside this block.
                $individual_check_statuses['storage_link'] = $storage_link_check['status'];
                if (!$storage_link_check['status']) {
                    $overall_status = false;
                }
                render_check_row("Storage 链接 (public/storage)", $storage_link_check['status'], $storage_link_check['message']);
            else: // For non-standard env, storage link check is skipped, so it's 'passing' for status array
                $individual_check_statuses['storage_link'] = true;
                // Optionally, render a row indicating it's skipped or not applicable for this env_type
                // render_check_row("Storage 链接 (public/storage)", true, "N/A for current environment type");
            endif; // 结束 Storage Link 检测的条件判断 ?>

        </table>
        </section>

        <section class="mt-8">
            <?php if ($overall_status): ?>
                <div class="alert-box success">
                    <i class="fas fa-party-popper icon"></i>
                    <h2 class="text-2xl font-bold">恭喜！您的环境满足 BWMS 的基本安装要求。</h2>
                    <p class="mt-2">您可以继续进行安装或部署操作。</p>
                </div>
            <?php else: ?>
                <div class="alert-box error">
                    <i class="fas fa-exclamation-triangle icon"></i>
                    <h2 class="text-2xl font-bold">环境检查未通过！</h2>
                    <p class="mt-2">请根据上述列表中的 <i class="fas fa-times-circle"></i> 标记修复问题后重试。</p>
                </div>
            <?php endif; ?>
        </section>

        <section class="mt-8 content-card">
            <h2 class="section-title">后续步骤与部署建议</h2>
            <?php if ($overall_status): ?>
                <div class="alert-box success">
                    <i class="fas fa-check-circle"></i>
                    <strong>太棒了！您的服务器环境满足所有要求。</strong>
                </div>
                <p class="mt-4">您可以按照以下步骤继续部署或启动您的 BWMS 应用：</p>
                <ul class="pl-4 mt-2 space-y-1 list-disc list-inside">
                    <li>确保您的 <code>.env</code> 文件已根据实际情况配置完毕 (数据库、应用URL、邮件服务等)。</li>
                    <li>如果这是全新安装，运行数据库迁移和填充：<code>php artisan migrate --seed</code></li>
                    <li>如果您的项目包含前端资源，确保已编译：<code>npm install && npm run prod</code> (或 <code>yarn && yarn prod</code>)</li>
                    <li>配置您的 Web 服务器 (Nginx/Apache) 将域名指向项目的 <code>public</code> 目录。</li>
                    <li>确保 <code>storage</code> 目录及其子目录具有正确的写入权限。</li>
                    <li>(可选) 配置 Supervisor 或类似的进程管理器来运行队列工作器：<code>php artisan queue:work</code></li>
                    <li>访问您的应用URL，开始使用 BWMS！</li>
                </ul>
                <p class="mt-4">如果您在部署过程中遇到任何问题，请查阅 <a href="#" class="text-blue-600 hover:underline">官方部署文档</a> (请替换为实际文档链接)。</p>
            <?php else: ?>
                <div class="alert-box error">
                    <i class="fas fa-times-circle"></i>
                    <strong>环境检测未通过。</strong>
                </div>
                <p class="mt-4">请仔细查看上方列表中的 <span class="status-error">红色错误项</span>，并根据提示进行修复。以下是针对检测未通过部分的建议：</p>
                <ul class="pl-4 mt-2 space-y-1 list-disc list-inside">
                    <?php if (!$individual_check_statuses['php_version']): ?>
                        <li><strong>PHP 版本不正确：</strong> 请升级或降级您的 PHP 版本至推荐范围 (当前要求: >= <?php echo MIN_PHP_VERSION; ?>)。您可能需要联系您的主机提供商或使用版本管理工具（如 `phpbrew` 或 `asdf`）。</li>
                    <?php endif; ?>
                    <?php if (!$individual_check_statuses['php_extensions']): ?>
                        <li><strong>缺少 PHP 扩展：</strong> 根据上方列表提示安装并启用缺失的 PHP 扩展。通常可以通过 `pecl install extension_name` 或系统包管理器 (如 `apt install php-extension_name`) 来安装，然后在 `php.ini` 文件中启用。</li>
                    <?php endif; ?>
                    <?php if ($env_type === 'standard' && !$individual_check_statuses['node_version']): ?>
                        <li><strong>Node.js/NPM 问题：</strong> 确保已正确安装 Node.js (当前要求: >= <?php echo MIN_NODE_VERSION; ?>) 和 NPM，并且它们在系统的 PATH 中。可以使用 `nvm` (Node Version Manager) 来管理 Node.js 版本。</li>
                    <?php endif; ?>
                    <?php if (!$individual_check_statuses['permissions']): ?>
                        <li><strong>目录权限问题：</strong> 确保 BWMS 需要写入的目录 (如 <code>storage</code>, <code>bootstrap/cache</code> 等，详见上方列表) 对 Web 服务器用户 (通常是 `www-data` 或 `nginx`) 可写。您可能需要使用 `chmod` 或 `chown` 命令调整权限。</li>
                    <?php endif; ?>
                    <?php if (!$individual_check_statuses['env_file']): ?>
                        <li><strong>.env 文件问题：</strong> 项目根目录下缺少 <code>.env</code> 文件。请从 <code>.env.example</code> 复制创建，并根据您的环境进行配置。</li>
                    <?php endif; ?>
                    <?php if ($individual_check_statuses['env_file'] && !$individual_check_statuses['app_key']): // Only show APP_KEY issue if .env file exists and key is bad ?>
                        <li><strong>APP_KEY 未设置：</strong> 您的 <code>.env</code> 文件中缺少或未正确设置 <code>APP_KEY</code>。请运行 <code>php artisan key:generate</code> 命令生成。</li>
                    <?php endif; ?>
                    <?php if ($env_type === 'standard' && !$individual_check_statuses['storage_link']): ?>
                        <li><strong>Storage Link 问题：</strong> <code>public/storage</code> 链接不存在或指向错误。请尝试运行 <code>php artisan storage:link</code> 命令创建正确的符号链接。</li>
                    <?php endif; ?>
                </ul>
                <p class="mt-4">修复问题后，请刷新此页面重新进行检测。如果问题依然存在，建议查阅 <a href="#" class="text-blue-600 hover:underline">故障排除指南</a> (请替换为实际文档链接) 或寻求社区支持。</p>
            <?php endif; ?>
        </section>

        <?php endif; // <!-- Closing the main if for APP_DEBUG check --> ?>
        </div> <!-- Closing content-card for checks -->

        <footer class="page-footer">
            BWMS 环境检查工具 &copy; <?php echo date('Y'); ?>
            <?php if ($app_debug_enabled): ?>
                | <span class="font-semibold text-yellow-600">调试模式已开启 (APP_DEBUG=true)</span>
            <?php endif; ?>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
