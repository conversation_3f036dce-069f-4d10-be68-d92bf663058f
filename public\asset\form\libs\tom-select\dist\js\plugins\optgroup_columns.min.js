(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.optgroup_columns=factory())})(this,function(){"use strict";const KEY_LEFT=37;const KEY_RIGHT=39;typeof navigator==="undefined"?false:/Mac/.test(navigator.userAgent);const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const parentMatch=(target,selector,wrapper)=>{if(wrapper&&!wrapper.contains(target)){return}while(target&&target.matches){if(target.matches(selector)){return target}target=target.parentNode}};const nodeIndex=(el,amongst)=>{if(!el)return-1;amongst=amongst||el.nodeName;var i=0;while(el=el.previousElementSibling){if(el.matches(amongst)){i++}}return i};function plugin(){var self=this;var orig_keydown=self.onKeyDown;self.hook("instead","onKeyDown",evt=>{var index,option,options,optgroup;if(!self.isOpen||!(evt.keyCode===KEY_LEFT||evt.keyCode===KEY_RIGHT)){return orig_keydown.call(self,evt)}self.ignoreHover=true;optgroup=parentMatch(self.activeOption,"[data-group]");index=nodeIndex(self.activeOption,"[data-selectable]");if(!optgroup){return}if(evt.keyCode===KEY_LEFT){optgroup=optgroup.previousSibling}else{optgroup=optgroup.nextSibling}if(!optgroup){return}options=optgroup.querySelectorAll("[data-selectable]");option=options[Math.min(options.length-1,index)];if(option){self.setActiveOption(option)}})}return plugin});