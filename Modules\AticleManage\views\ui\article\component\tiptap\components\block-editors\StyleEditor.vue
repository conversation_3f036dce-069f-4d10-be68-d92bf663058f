<template>
  <div class="edit-section">
    <el-form label-position="top" :model="localStyles">
      <el-form-item label="块宽度">
        <div class="width-selector">
          <el-tooltip content="全宽" placement="top">
            <div
              class="width-option"
              :class="{ active: localStyles.width === 'col-12' }"
              @click="selectWidth('col-12')"
            >
              <i class="el-icon-full-screen"></i>
              <span>全宽</span>
            </div>
          </el-tooltip>
          
          <el-tooltip content="标准宽度" placement="top">
            <div
              class="width-option"
              :class="{ active: localStyles.width === 'col-12 col-md-10 col-lg-8' }"
              @click="selectWidth('col-12 col-md-10 col-lg-8')"
            >
              <i class="el-icon-crop"></i>
              <span>标准</span>
            </div>
          </el-tooltip>
          
          <el-tooltip content="窄宽度" placement="top">
            <div
              class="width-option"
              :class="{ active: localStyles.width === 'col-12 col-md-8 col-lg-6' }"
              @click="selectWidth('col-12 col-md-8 col-lg-6')"
            >
              <i class="el-icon-menu"></i>
              <span>窄型</span>
            </div>
          </el-tooltip>
        </div>
      </el-form-item>

      <el-form-item label="对齐方式">
        <el-radio-group v-model="localStyles.textAlign" @change="updateStyles">
          <el-radio-button label="left">
            左对齐
          </el-radio-button>
          <el-radio-button label="center">
            居中
          </el-radio-button>
          <el-radio-button label="right">
            右对齐
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="边距 (px)">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="上">
              <el-input-number 
                v-model="localStyles.marginTop" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="右">
              <el-input-number 
                v-model="localStyles.marginRight" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下">
              <el-input-number 
                v-model="localStyles.marginBottom" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="左">
              <el-input-number 
                v-model="localStyles.marginLeft" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="内边距 (px)">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="上">
              <el-input-number 
                v-model="localStyles.paddingTop" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="右">
              <el-input-number 
                v-model="localStyles.paddingRight" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下">
              <el-input-number 
                v-model="localStyles.paddingBottom" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="左">
              <el-input-number 
                v-model="localStyles.paddingLeft" 
                @change="updateStyles" 
                :min="0" 
                :max="100"
                :controls="false"
                size="small"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <!-- 添加应用按钮容器 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits, watch, defineOptions } from 'vue'

// 定义组件名称
defineOptions({
  name: 'StyleEditor'
})

interface StyleProps {
  width: string
  textAlign: string
  marginTop: number
  marginRight: number
  marginBottom: number
  marginLeft: number
  paddingTop: number
  paddingRight: number
  paddingBottom: number
  paddingLeft: number
}

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  styles: {
    type: Object,
    default: () => ({
      width: 'col-12 col-md-10 col-lg-8', // 默认使用标准宽度
      textAlign: 'left',
      marginTop: 0,
      marginRight: 0,
      marginBottom: 0,
      marginLeft: 0,
      paddingTop: 0,
      paddingRight: 0,
      paddingBottom: 0,
      paddingLeft: 0
    })
  }
})

const emit = defineEmits(['update-styles'])

// 创建本地样式副本，避免直接修改props
const localStyles = reactive<StyleProps>({
  width: 'col-12 col-md-10 col-lg-8', // 默认宽度
  textAlign: 'left',
  marginTop: 0,
  marginRight: 0,
  marginBottom: 0,
  marginLeft: 0,
  paddingTop: 0,
  paddingRight: 0,
  paddingBottom: 0,
  paddingLeft: 0
})

// 添加是否有更改的状态
const isChanged = ref(false)

// 监听props变化，更新本地样式
watch(() => props.styles, (newStyles) => {
  if (newStyles) {
    Object.assign(localStyles, newStyles)
    isChanged.value = false  // 重置更改状态
  }
}, { deep: true, immediate: true })

// 选择宽度
const selectWidth = (width: string) => {
  localStyles.width = width
  updateStyles()
}

// 设置对齐方式
const setAlignment = (align: string) => {
  localStyles.textAlign = align
  updateStyles()
}

// 更新样式
const updateStyles = () => {
  emit('update-styles', { ...localStyles })
  isChanged.value = true  // 标记为已更改
}

// 添加应用更改函数
const applyChanges = () => {
  emit('update-styles', { ...localStyles })
  isChanged.value = false  // 重置更改状态
}
</script>

<style lang="scss" scoped>
.edit-section {
  padding: 12px 16px;  // 减小整体内边距
  position: relative;  // 添加相对定位
  
  :deep(.el-form-item) {
    margin-bottom: 16px;  // 减小表单项间距
    
    .el-form-item__label {
      padding-bottom: 6px;
      font-weight: 500;
      color: #606266;
      font-size: 13px;  // 调整标签字体大小
    }
  }
}

.width-selector {
  display: flex;
  justify-content: space-between;
  gap: 8px;  // 减小选项间距
  margin: 0 0 8px;  // 减小底部边距
  padding: 0;
}

.width-option {
  flex: 1;
  min-width: 80px;  // 减小最小宽度
  max-width: 120px; // 限制最大宽度
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 6px;  // 减小内边距
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;  // 减小图标和文字间距
  transition: all 0.2s ease-in-out;
  background-color: #fff;
  
  i {
    font-size: 16px;  // 减小图标大小
    color: #606266;
  }
  
  span {
    font-size: 12px;  // 减小文字大小
    color: #606266;
    white-space: nowrap;
  }
  
  &:hover {
    background-color: #f5f7fa;
    border-color: #c0c4cc;
  }
  
  &.active {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    
    i, span {
      color: var(--el-color-primary);
    }
  }
}

:deep(.el-radio-group) {
  display: flex;
  width: 100%;
  
  .el-radio-button {
    flex: 1;
    
    &__inner {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;  // 减小按钮高度
      border-radius: 0;
      font-size: 12px;  // 减小文字大小
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
    
    &:first-child {
      .el-radio-button__inner {
        border-radius: 4px 0 0 4px;
      }
    }
    
    &:last-child {
      .el-radio-button__inner {
        border-radius: 0 4px 4px 0;
      }
    }
  }
}

:deep(.el-row) {
  .el-col {
    .el-form-item {
      margin-bottom: 0;
      
      .el-form-item__label {
        padding: 0 0 4px;
        line-height: 1.5;
        font-size: 12px;
        color: #909399;
      }
      
      .el-input-number {
        width: 100%;
        
        .el-input__inner {
          text-align: center;
          padding: 0 5px;
          height: 28px;  // 减小输入框高度
          font-size: 12px;  // 减小输入框字体大小
        }
      }
    }
  }
}

// 添加应用按钮容器样式
.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 