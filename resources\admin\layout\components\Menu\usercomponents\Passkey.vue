<template>
  <div class="passkey">
    <h2>Passkey</h2>
    <el-form label-width="120px" class="passkey-form">
      <el-form-item label="Passkey">
        <span class="status">未启用</span>
        <el-button link class="action-link">启用</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const passkeyStatus = ref('未启用')
</script>

<style scoped>
.passkey {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.passkey-form {
  margin-top: 20px;
}

.status {
  margin-left: 10px;
  color: #909399;
}

.action-link {
  margin-left: 10px;
  color: #409eff;
  cursor: pointer;
}
</style>