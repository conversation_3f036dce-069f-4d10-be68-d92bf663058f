<header>
    <div class="header-con">
        <div class="logo">
            <img src="@asset('asset/image/logo.png')" />
        </div>

        <nav>
            <ul>
                @foreach(\Modules\Nav\Domain\NavUtil::listByPositionWithCache('zh_CN',\Modules\Nav\Enums\NavPosition::PC_TOP_NAV->value) as $nav)
                    <li>
                        <a href="{{ $nav['path'] }}">
                            {{ $nav['name'] }}
                            @if(!empty($nav['children']))
                                <i class="iconfont icon-arrow-right"></i>
                            @endif
                        </a>
                        @if(!empty($nav['children']))
                            <div class="sub-menu">
                                <ul>
                                    @foreach($nav['children'] as $child)
                                        <li>
                                            <a href="{{ $child['path'] }}">{{ $child['name'] }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </li>
                @endforeach
            </ul>
        </nav>

        <div class="more">
            <div class="lang-select">
                <span>繁</span>

                <div class="lang-list">
                    <div class="single"></div>
                    <ul>
                        <li>
                            <a href="">繁体中文</a>
                        </li>
                        <li>
                            <a href="">English</a>
                        </li>
                        <li>
                            <a href="">简体中文</a>
                        </li>
                    </ul>
                </div>
            </div>
            <span class="iconfont icon-search"></span>
            <!-- <a href="/user/user.html" class="iconfont icon-user"></a> -->
            @include('module::Member.views.pc.member')
        </div>
    </div>
</header>
