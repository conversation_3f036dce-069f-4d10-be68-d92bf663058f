.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .container {
  flex-grow: 1;
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .container .left-side {
  background-color: #fff;
  width: 200px;
  flex-shrink: 0;
  min-height: 650px;
}
.bwms-page .container .left-side .ul {
  padding: 15px 48px;
}
.bwms-page .container .left-side .ul .li {
  padding: 8px 0;
}
.bwms-page .container .left-side .ul .li.th {
  font-size: 16px;
  color: #222;
}
.bwms-page .container .left-side .ul .li a {
  font-size: 14px;
  color: #666;
  transition: color 0.35s ease-in-out;
}
.bwms-page .container .left-side .ul .li.active a,
.bwms-page .container .left-side .ul .li:hover a {
  color: #ff9600;
}
.bwms-page .container .right-content {
  padding-left: 20px;
  flex-grow: 1;
}
.bwms-page .container .right-content .white-box {
  margin-bottom: 14px;
  background-color: #fff;
}
.bwms-page .container .right-content .userinfo {
  padding: 42px 44px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .container .right-content .userinfo .avatar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-grow: 1;
}
.bwms-page .container .right-content .userinfo .avatar .pic {
  border-radius: 50%;
  border: 4px solid #e0e0e0;
  width: 130px;
  height: 130px;
  overflow: hidden;
}
.bwms-page .container .right-content .userinfo .avatar .info-box {
  padding-left: 50px;
  flex-grow: 1;
}
.bwms-page .container .right-content .userinfo .avatar .info-box .username {
  font-weight: bold;
  font-size: 24px;
  color: #616161;
  line-height: 1.3;
}
.bwms-page .container .right-content .userinfo .avatar .info-box .call-text {
  margin-bottom: 10px;
  font-size: 14px;
  color: #B0b0b0;
}
.bwms-page .container .right-content .userinfo .avatar .info-box .modify-userinfo a {
  font-size: 12px;
  color: #ff9600;
}
.bwms-page .container .right-content .userinfo .level-phone-mail {
  padding-right: 100px;
}
.bwms-page .container .right-content .userinfo .level-phone-mail .label-box {
  padding: 6px 0;
  color: #757575;
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .container .right-content .num-info {
  padding: 30px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .container .right-content .num-info .num-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}
.bwms-page .container .right-content .num-info .num-item .num {
  font-size: 28px;
  color: #666;
  line-height: 1.32;
}
.bwms-page .container .right-content .num-info .num-item .label {
  font-size: 18px;
  color: #37474f;
  line-height: 1.33;
}
