(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.change_listener=factory())})(this,function(){"use strict";const addEvent=(target,type,callback,options)=>{target.addEventListener(type,callback,options)};function plugin(){addEvent(this.input,"change",()=>{this.sync()})}return plugin});