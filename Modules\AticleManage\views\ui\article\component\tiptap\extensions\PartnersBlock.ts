import { mergeAttributes, Node } from '@tiptap/core'

export interface PartnersBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    partnersBlock: {
      /**
       * Add a partners block
       */
      setPartnersBlock: (attributes?: { }) => ReturnType
    }
  }
}

export const PartnersBlock = Node.create<PartnersBlockOptions>({
  name: 'partnersBlock',

  group: 'block',

  content: 'block+',

  draggable: true,

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="partners"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-bs-component': 'partners' }), 0]
  },

  addCommands() {
    return {
      setPartnersBlock:
        attributes => ({ commands }) => {
          return commands.setNode(this.name, attributes)
        },
    }
  },
}) 