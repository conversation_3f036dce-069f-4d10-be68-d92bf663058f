<?php

namespace Modules\Common\Provider\Recommend;

use Bingo\Core\Type\BaseType;

class RecommendUserFeedbackType implements BaseType
{
    public const LIKE = 'like';
    public const DISLIKE = 'dislike';
    public const VISIT = 'visit';

    public static function getList(): array
    {
        return [
            self::LIKE => '点赞',
            self::DISLIKE => '点踩',
            self::VISIT => '访问',
        ];
    }
}
