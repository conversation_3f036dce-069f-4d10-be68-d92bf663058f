export const pricingTemplate = `
<div class="py-5 bootstrap-pricing responsive-block" data-bs-component="pricing">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-lg-10">
        <div class="mb-3 text-center row g-3">
          <!-- Starter Plan -->
          <div class="col-12 col-md-6 col-lg-4">
            <div class="shadow-sm h-100 card rounded-3 d-flex flex-column pricing-card">
              <div class="py-3 card-header">
                <h4 class="my-0 fw-normal">Starter</h4>
              </div>
              <div class="card-body d-flex flex-column">
                <h1 class="card-title pricing-card-title">$79<small class="text-body-secondary fw-light">/month</small></h1>
                <p class="mt-3 mb-4">Perfect for small businesses or startups looking to kickstart their digital marketing efforts</p>
                <ul class="mt-3 mb-4 list-unstyled flex-grow-1">
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Social media management</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Email marketing campaigns</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Basic SEO optimization</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Monthly performance reports</li>
                  <li class="opacity-0">Hidden item for height</li>
                </ul>
                <button type="button" class="mt-auto w-100 btn btn-lg btn-outline-primary" data-pricing-plan="starter">Get started</button>
              </div>
            </div>
          </div>
          
          <!-- Pro Plan -->
          <div class="col-12 col-md-6 col-lg-4">
            <div class="shadow-sm h-100 card rounded-3 d-flex flex-column pricing-card popular">
              <div class="py-3 card-header position-relative">
                <span class="top-0 position-absolute start-50 translate-middle badge rounded-pill bg-primary">Popular</span>
                <h4 class="my-0 fw-normal">Pro</h4>
              </div>
              <div class="card-body d-flex flex-column">
                <h1 class="card-title pricing-card-title">$129<small class="text-body-secondary fw-light">/month</small></h1>
                <p class="mt-3 mb-4">Ideal for growing businesses that want to expand their online presence and reach a larger audience</p>
                <ul class="mt-3 mb-4 list-unstyled flex-grow-1">
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Everything in Starter plan</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Advanced social media management</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Email marketing automation</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Basic SEO optimization</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Comprehensive SEO analysis</li>
                </ul>
                <button type="button" class="mt-auto w-100 btn btn-lg btn-primary" data-pricing-plan="pro">Get started</button>
              </div>
            </div>
          </div>
          
          <!-- Enterprise Plan -->
          <div class="col-12 col-md-6 col-lg-4">
            <div class="shadow-sm h-100 card rounded-3 d-flex flex-column pricing-card">
              <div class="py-3 card-header">
                <h4 class="my-0 fw-normal">Enterprise</h4>
              </div>
              <div class="card-body d-flex flex-column">
                <h1 class="card-title pricing-card-title">$599<small class="text-body-secondary fw-light">/month</small></h1>
                <p class="mt-3 mb-4">Tailored for established businesses seeking a holistic digital marketing solution</p>
                <ul class="mt-3 mb-4 list-unstyled flex-grow-1">
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Everything in Pro plan</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Customized social media strategies</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Personalized email marketing</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Advanced SEO implementation</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Bi-weekly performance reports</li>
                </ul>
                <button type="button" class="mt-auto w-100 btn btn-lg btn-primary" data-pricing-plan="enterprise">Contact sales</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .bootstrap-pricing {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background-color: #fff;
      padding: 2rem 1rem;
    }

    .bootstrap-pricing .pricing-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid rgba(0,0,0,0.1);
      height: 100%;
    }

    .bootstrap-pricing .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }

    .bootstrap-pricing .pricing-card.popular {
      border: 2px solid #4e73df;
      transform: scale(1.02);
    }

    .bootstrap-pricing .pricing-card.popular:hover {
      transform: scale(1.02) translateY(-5px);
    }

    .bootstrap-pricing .card-header {
      background-color: rgba(0,0,0,0.02);
      border-bottom: 1px solid rgba(0,0,0,0.1);
    }

    .bootstrap-pricing .pricing-card-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #333;
    }

    .bootstrap-pricing .pricing-card-title small {
      font-size: 1rem;
      color: #6c757d;
    }

    .bootstrap-pricing .card-body {
      padding: 1.5rem;
    }

    .bootstrap-pricing .list-unstyled li {
      padding: 0.5rem 0;
      color: #666;
    }

    .bootstrap-pricing .btn-outline-primary {
      border-color: #4e73df;
      color: #4e73df;
    }

    .bootstrap-pricing .btn-outline-primary:hover {
      background-color: #4e73df;
      color: white;
    }

    .bootstrap-pricing .btn-primary {
      background-color: #4e73df;
      border-color: #4e73df;
    }

    .bootstrap-pricing .btn-primary:hover {
      background-color: #2e59d9;
      border-color: #2e59d9;
    }

    .bootstrap-pricing .badge {
      font-size: 0.75rem;
      padding: 0.5rem 1rem;
      margin-top: -1rem;
      background-color: #4e73df;
    }

    /* 移动端预览模式样式 */
    .mobile-preview .bootstrap-pricing {
      padding: 1rem;
    }

    .mobile-preview .bootstrap-pricing [class*="col-"] {
      width: 100%;
      max-width: 100%;
      flex: 0 0 100%;
    }

    .mobile-preview .bootstrap-pricing .pricing-card {
      margin-bottom: 1.5rem;
      max-width: 100%;
    }

    .mobile-preview .bootstrap-pricing .pricing-card.popular {
      transform: none;
      margin: 2rem 0;
    }

    .mobile-preview .bootstrap-pricing .card-body {
      padding: 1.25rem;
    }

    .mobile-preview .bootstrap-pricing .pricing-card-title {
      font-size: 2rem;
    }

    /* 移动端样式 */
    @media (max-width: 767.98px) {
      .bootstrap-pricing {
        padding: 1rem;
      }

      .bootstrap-pricing [class*="col-"] {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
      }

      .bootstrap-pricing .pricing-card {
        margin-bottom: 1.5rem;
        max-width: 100%;
      }

      .bootstrap-pricing .pricing-card.popular {
        transform: none;
        margin: 2rem 0;
      }

      .bootstrap-pricing .card-body {
        padding: 1.25rem;
      }

      .bootstrap-pricing .pricing-card-title {
        font-size: 2rem;
      }
    }

    /* 平板端样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .bootstrap-pricing .pricing-card {
        margin-bottom: 1rem;
      }

      .bootstrap-pricing .card-body {
        padding: 1.25rem;
      }

      .bootstrap-pricing .pricing-card-title {
        font-size: 2.25rem;
      }

      .bootstrap-pricing .pricing-card.popular {
        transform: scale(1.01);
      }

      .bootstrap-pricing [class*="col-md-6"] {
        width: 50%;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .bootstrap-pricing {
        padding: 2rem;
      }

      .bootstrap-pricing .pricing-card {
        margin: 0;
      }

      .bootstrap-pricing .card-body {
        padding: 1.5rem;
      }

      .bootstrap-pricing .pricing-card-title {
        font-size: 2.5rem;
      }

      .bootstrap-pricing .pricing-card.popular {
        transform: scale(1.02);
      }

      .bootstrap-pricing [class*="col-lg-4"] {
        width: 33.333333%;
      }
    }

    /* 桌面预览模式样式 */
    .desktop-preview .bootstrap-pricing {
      padding: 2rem;
    }

    .desktop-preview .bootstrap-pricing .pricing-card {
      margin: 0;
    }

    .desktop-preview .bootstrap-pricing .card-body {
      padding: 1.5rem;
    }

    .desktop-preview .bootstrap-pricing .pricing-card-title {
      font-size: 2.5rem;
    }

    .desktop-preview .bootstrap-pricing .pricing-card.popular {
      transform: scale(1.02);
    }
  </style>

  <script>
    (function() {
      // 找到价格组件
      const pricingComponents = document.querySelectorAll('[data-bs-component="pricing"]');
      
      pricingComponents.forEach(component => {
        // 获取所有价格计划按钮
        const pricingButtons = component.querySelectorAll('[data-pricing-plan]');
        
        // 为每个按钮添加点击事件
        pricingButtons.forEach(button => {
          button.addEventListener('click', function() {
            // 获取价格计划类型
            const planType = this.getAttribute('data-pricing-plan');
            
            // 根据不同计划类型执行不同操作
            switch(planType) {
              case 'starter':
                // 跳转到入门版订阅页面
                window.location.href = '/pricing/starter';
                break;
              case 'pro':
                // 跳转到专业版订阅页面
                window.location.href = '/pricing/pro';
                break;
              case 'enterprise':
                // 跳转到企业版联系页面
                window.location.href = '/contact/sales';
                break;
              default:
                // 默认跳转到价格总览页面
                window.location.href = '/pricing';
            }
            
            // 可以在这里添加点击事件跟踪代码
            console.log('用户点击了' + planType + '价格计划');
            
            // 如果需要，可以在这里触发自定义事件
            const clickEvent = new CustomEvent('pricing-plan-selected', { 
              detail: { plan: planType } 
            });
            document.dispatchEvent(clickEvent);
          });
        });
      });
    })();
  </script>
</div>
` 