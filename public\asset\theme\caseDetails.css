.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .white-box {
  margin-bottom: 30px;
  padding: 30px 50px;
  background-color: #fff;
}
.bwms-page .white-box h1 {
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: bold;
  line-height: 1.32;
}
.bwms-page .white-box .case-info {
  margin-bottom: 20px;
  border-bottom: 1px solid #F7F7F7;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .white-box .case-info span {
  margin-right: 10px;
  display: block;
  font-size: 14px;
  color: #888;
}
.bwms-page .white-box .big {
  position: relative;
}
.bwms-page .white-box .big .swiper-slide {
  max-height: 520px;
  text-align: center;
}
.bwms-page .white-box .big .swiper-slide .img {
  max-width: 100%;
  max-height: 100%;
}
.bwms-page .white-box .big .iconfont {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 50%;
  width: 46px;
  height: 46px;
  background-color: rgba(0, 0, 0, 0.35);
  color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .white-box .big .iconfont.icon-arrow-right {
  right: 0;
}
.bwms-page .white-box .big .iconfont.icon-arrow-left {
  left: 0;
}
.bwms-page .white-box .small-pic {
  margin: 20px 0 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .white-box .small-pic .img {
  padding: 0 5px;
  width: 12.5%;
  cursor: pointer;
  position: relative;
}
.bwms-page .white-box .small-pic .img img {
  width: 100%;
}
.bwms-page .white-box .small-pic .img::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.3);
  transition: background-color 0.35s ease-in-out;
  z-index: 1;
}
.bwms-page .white-box .small-pic .img:hover::after,
.bwms-page .white-box .small-pic .img.active::after {
  background-color: transparent;
}
.bwms-page .white-box p {
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 1.75;
}
.bwms-page .btn-list {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-bottom: 50px;
  border: 1px solid #eee;
  background-color: #fff;
}
.bwms-page .btn-list .btn-box {
  padding: 0 50px;
  border-right: 1px solid #eee;
  font-size: 16px;
  line-height: 4.375;
  color: #888;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .btn-list .btn-box .iconfont {
  display: block;
  margin-right: 10px;
}
.bwms-page .btn-list .btn-box:last-child {
  border-right: none;
}
.bwms-page .btn-list .btn-box.prev,
.bwms-page .btn-list .btn-box.next {
  flex-grow: 1;
}
.bwms-page .btn-list .btn-box[href] {
  color: #383838;
}
