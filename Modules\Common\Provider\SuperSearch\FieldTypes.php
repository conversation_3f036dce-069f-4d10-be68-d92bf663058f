<?php

namespace Modules\Common\Provider\SuperSearch;

use Bingo\Core\Type\BaseType;

class FieldTypes implements BaseType
{
    public const F_TEXT = 'text';
    public const F_KEYWORD = 'keyword';
    public const F_LONG = 'long';
    public const F_LONG_ARRAY = 'longArray';
    public const F_TEXT_ARRAY = 'textArray';
    public const F_DATETIME = 'datetime';
    public const F_DATE = 'date';

    public static function getList(): array
    {
        return [
            self::F_TEXT => '全文搜索字段',
            self::F_KEYWORD => '字符串',
            self::F_LONG => '整型',
            self::F_LONG_ARRAY => '整形数组',
            self::F_TEXT_ARRAY => '字符数组',
            self::F_DATETIME => '日期时间',
            self::F_DATE => '日期',
        ];
    }

}
