<?php

use Illuminate\Support\Facades\Route;
use Modules\Faq\Api\Controllers\FaqController;
use Modules\Faq\Api\Controllers\FaqCategoryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('admin')->group(function () {
    Route::prefix('faq')->group(function () {
        Route::get('/', [FaqController::class, 'index'])->name('faq.index');
        Route::post('/', [FaqController::class, 'store'])->name('faq.store');
        Route::get('/{id}', [FaqController::class, 'show'])->name('faq.show');
        Route::put('/{id}', [FaqController::class, 'update'])->name('faq.update');
        Route::prefix('batch')->group(function () {
            Route::delete('/', [FaqController::class, 'batchDelete'])->name('faq.batch_delete');
            Route::put('/unshelf', [FaqController::class, 'batchUnshelf'])->name('faq.batch_unshelf');
        });
        Route::delete('/{id}', [FaqController::class, 'destroy'])->name('faq.destroy');


        Route::prefix('category')->group(function () {
            Route::get('/', [FaqCategoryController::class, 'index'])->name('faq.category.index');
            Route::post('/', [FaqCategoryController::class, 'store'])->name('faq.category.store');
            Route::get('/{id}', [FaqCategoryController::class, 'show'])->name('faq.category.show');
            Route::put('/{id}', [FaqCategoryController::class, 'update'])->name('faq.category.update');
            Route::delete('/{id}', [FaqCategoryController::class, 'destroy'])->name('faq.category.destroy');
        });
    });
});
