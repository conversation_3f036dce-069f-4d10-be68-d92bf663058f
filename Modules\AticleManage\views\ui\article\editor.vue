<template>
  <div class="bwms-module table-page">
    <div class="module-header">
      <el-button class="button-no-border" @click="generateSummary">
        <el-icon size="16" ><Position/></el-icon>
        <span>一鍵生成簡體</span>
      </el-button>
      <el-button class="button-no-border" @click="handleSave">
        <el-icon size="16" ><Check/></el-icon>
        <span>保存</span>
      </el-button>
      <el-button class="button-no-border" @click="handlePreview">
        <el-icon size="16" ><View/></el-icon>
        <span>预览</span>
      </el-button>
      <el-button type="primary" @click="handlePublish">
        <el-icon size="16"><CircleCheck /></el-icon>
        <span>发布</span>
      </el-button>
      
    </div>
    <div class="module-con">
     <div class="editor-main-layout box scroll-bar-custom">
      <!-- 左侧主内容区 -->
      <div class="editor-left">
          <!-- 语言切换 -->
          <div class="lang-switch">
            <el-tabs class="demo-tabs" v-model="langTab">
              <el-tab-pane label="繁體中文" name="zh_tw" />
              <el-tab-pane label="簡體中文" name="zh_cn" />
            </el-tabs>
          </div>
          <!-- 文章标题 -->
          <el-form-item label="文章標題" class="article-title-item">
            <el-input type="textarea" :rows="2" v-model="form.title" placeholder="請輸入文章標題" maxlength="1100" show-word-limit />
          </el-form-item>
          <!-- AI摘要 -->
          <div class="ai-summary-block">
            <div class="ai-summary-header">
              <span>AI 摘要</span>
              <el-switch v-model="aiSummaryEnabled" />
            </div>
            <div class="ai-summary-list" v-if="aiSummaryEnabled">
              <el-row :gutter="12">
                <el-col :span="8" v-for="(item, idx) in aiSummaries" :key="idx">
                  <el-card class="ai-summary-card">
                    <div class="ai-summary-title flex justify-between">
                      <span>摘要 {{ idx + 1 }}</span>
                      <el-button-group class="ai-summary-actions" style="display:flex; gap: 12px;">
                        <el-button type="primary" link>
                          <el-icon size="16" ><Refresh /></el-icon>
                        </el-button>
                        <el-button type="primary" link>
                          <el-icon size="16" @click="editSummary(idx)"><Edit/></el-icon>
                        </el-button>
                      </el-button-group>
                    </div>
                    <div class="ai-summary-content scroll-bar-custom">
                      <ul>
                        <li v-for="(point, pidx) in item" :key="pidx">{{ point }}</li>
                      </ul>
                    </div>
                    
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- Tab切换：文章内容/编辑记录 -->
          <el-tabs v-model="mainTab" class="main-content-tabs">
            <el-tab-pane label="文章內容" name="content">
              <TiptapEditor />
            </el-tab-pane>
            <el-tab-pane label="編輯記錄" name="record">
              <div class="edit-record-placeholder">
                <el-empty description="暫無編輯記錄" image-size="100px" />
              </div>
            </el-tab-pane>
          </el-tabs>
      </div>
      <!-- 右侧基础设置区 -->
       <div v-if="rightCollapsed" class="collapsed">  
        <div class="toggle-panel-btn" @click="rightCollapsed = !rightCollapsed">
          <el-icon size="16" color="#fff">
            <Setting />
          </el-icon>
        </div>
       </div>
      <transition name="slide-right-panel">
        <div v-show="!rightCollapsed" class="editor-right">
          <div class="toggle-panel-btn" @click="rightCollapsed = !rightCollapsed">
            <el-icon size="16" color="#fff">
              <ArrowRight />
            </el-icon>
          </div>
          <el-tabs v-model="rightTab" class="settings-tabs">
            <el-tab-pane label="基本设定" name="basic">
              <el-form label-position="top" class="right-form">
                <el-form-item>
                  <div class="avatar-upload-card">
                    <el-upload class="avatar-uploader" action="#" :show-file-list="false">
                      <template #default>
                        <div class="avatar-upload-center">
                          <img v-if="form.cover" :src="form.cover" class="avatar-img" />
                          <div v-else class="avatar-placeholder">
                            <el-icon size="48"><User /></el-icon>
                          </div>
                        </div>
                      </template>
                    </el-upload>
                    <div class="avatar-desc">预览主图片</div>
                  </div>
                </el-form-item>
                <el-form-item label="发布日期">
                  <el-date-picker v-model="form.date" type="date" placeholder="选择日期" style="width:100%" />
                </el-form-item>
                <el-form-item label="主频道">
                  <el-select v-model="form.channel" placeholder="选择主频道" style="width:100%">
                    <el-option v-for="item in channels" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="其他频道">
                  <el-select v-model="form.otherChannels" multiple placeholder="选择其他频道" style="width:100%">
                    <el-option v-for="item in channels" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>关键标签</span>
                    <el-button type="primary" @click="addTagDialogVisible = true" style="height: 32px; min-width: 84px;">
                      <el-icon><Plus /></el-icon>
                      <span>新增</span>
                    </el-button>
                  </div>
                  <div class="tags-container" v-if="form.tags?.length > 0">
                    <el-tag
                      v-for="(tag, index) in form.tags"
                      :key="tag"
                      closable
                      @close="removeTag(index)"
                      class="tag-item"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </el-form-item>
                <el-form-item label="笔名">
                  <el-select v-model="form.penName" placeholder="选择笔名" style="width:100%">
                    <el-option v-for="item in penNames" :key="item" :label="item" :value="item" />
                  </el-select>
                </el-form-item>
                <el-form-item label="文章状态">
                  <el-select v-model="form.status" placeholder="选择状态" style="width:100%">
                    <el-option label="草稿" value="draft" />
                    <el-option label="已發布" value="published" />
                    <el-option label="定時發布" value="timing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>影片置顶</span>
                    <el-switch v-model="form.videoTop" />
                  </div>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>精选</span>
                    <el-switch v-model="form.featured" />
                  </div>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>特约内容</span>
                    <el-switch v-model="form.specialContent" />
                  </div>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>啟用廣告</span>
                    <el-switch v-model="form.enableAdvert" />
                  </div>
                </el-form-item>
                <el-form-item label="广告规则">
                  <el-select v-model="form.adRule" placeholder="选择广告规则" style="width:100%">
                    <el-option v-for="item in adRules" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>地理限制</span>
                    <el-switch v-model="form.geoLimit" />
                  </div>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>底部投票開關</span>
                    <el-switch v-model="form.footerDecorate" />
                  </div>
                </el-form-item>
                <el-form-item label="" label-position="left">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>底部FAQ開關</span>
                    <el-switch v-model="form.footerFaq" />
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="SEO设定" name="seo">
              <el-form label-position="top" class="right-form">
                <el-form-item label="SEO标题">
                  <el-input v-model="seoForm.seoTitle" placeholder="输入SEO标题" />
                </el-form-item>
                <el-form-item label="SEO描述">
                  <el-input v-model="seoForm.seoDescription" type="textarea" :rows="3" placeholder="输入SEO描述" />
                </el-form-item>
                <el-form-item label="关键字">
                  <el-input v-model="seoForm.keywords" placeholder="输入关键字，用逗号分隔" />
                </el-form-item>
                <el-form-item label="Open Graph标题">
                  <el-input v-model="seoForm.ogTitle" placeholder="输入OG标题" />
                </el-form-item>
                <el-form-item label="Open Graph描述">
                  <el-input v-model="seoForm.ogDescription" type="textarea" :rows="3" placeholder="输入OG描述" />
                </el-form-item>
                <el-form-item label="规范URL">
                  <el-input v-model="seoForm.canonicalUrl" placeholder="输入规范URL" />
                </el-form-item>
                <el-form-item label="索引設定">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>允許索引</span>
                    <el-switch v-model="seoForm.allowIndexing" />
                  </div>
                </el-form-item>
                <el-form-item label="跟隨連結">
                  <div class="el-form-item__label form-item-label-cls">
                    <span>允許跟隨</span>
                    <el-switch v-model="seoForm.followLinks" />
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </transition>
    </div>
    </div>
    
    <el-dialog class="el-dialog-common-cls" v-model="editDialogVisible" title="编辑摘要" width="400px" align-center :close-on-click-modal="false">
      <div v-for="(point, idx) in editSummaryPoints" :key="idx" style="margin-bottom: 12px;">
        <el-input v-model="editSummaryPoints[idx]" :placeholder="`分点${idx+1}`" type="textarea" :rows="1" />
      </div>
      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEditSummary">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog class="el-dialog-common-cls" v-model="addTagDialogVisible" title="新增標籤" width="420px" align-center :close-on-click-modal="false">
      <el-form label-position="top">
        <el-form-item label="標籤名稱">
          <el-input v-model="newTagName" placeholder="輸入標籤名稱" />
        </el-form-item>
      </el-form>
      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="addTagDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveNewTag" :disabled="!newTagName.trim()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Plus, Upload, UploadFilled, User, ArrowLeft, ArrowRight, Setting } from '@element-plus/icons-vue'
import TiptapEditor from './component/tiptap/index.vue'
// 语言Tab
const langTab = ref('zh_tw')
// 主Tab
const mainTab = ref('content')
// AI摘要
const aiSummaryEnabled = ref(false)
const aiSummaries = ref([
  [
    '消息人士指出，今日恒生指数收跌235点，跌幅1.2%，科技、金融板块领跌，市场避险情绪升温。',
  ],
  [
    '三大指数集体下挫，恒指跌破重要支撑位。',
    '资金流向防御性板块，短线波动或加剧。',
    '建议投资者保持观望，关注后续政策信号。'
  ],
  [
    '最新数据显示，港股市场承压，成交量放大。',
    '投资者信心不足，部分蓝筹股跌幅明显。',
    '建议分散投资，关注长期基本面优质标的。'
  ]
])
const generateSummary = () => {
  // TODO: 生成摘要逻辑
}
// 右侧收缩
const rightCollapsed = ref(false)
// 右侧Tab
const rightTab = ref('basic')
// 表单数据
const form = reactive({
  title: '',
  cover: '',
  date: '',
  channel: '',
  otherChannels: [],
  tags: [] as string[],
  penName: '',
  status: 'draft',
  videoTop: false,
  featured: false,
  specialContent: false,
  enableAdvert: false,
  adRule: '',
  geoLimit: false,
  footerDecorate: false,
  footerFaq: false
})
const seoForm = reactive({
  seoTitle: '',
  seoDescription: '',
  keywords: '',
  ogTitle: '',
  ogDescription: '',
  canonicalUrl: '',
  allowIndexing: true,
  followLinks: true
})
const channels = [
  { label: '財經新聞', value: 'finance' },
  { label: '財經頻道', value: 'stock' },
  { label: '體育頻道', value: 'analysis' },
]
const tags = ref<string[]>(['財經新聞', '金融股市', '股市', '分析', '新聞導讀'])
const penNames = ['預設筆名', '編輯A', '編輯B']
const adRules = [
  { label: '规则A', value: 'A' },
  { label: '规则B', value: 'B' }
]
const addTagDialogVisible = ref(false)
const newTagName = ref('')
function saveNewTag() {
  const tag = newTagName.value.trim()
  if (tag && !tags.value.includes(tag)) {
    tags.value.push(tag)
  }
  if (tag && !form.tags.includes(tag)) {
    form.tags.push(tag)
  }
  newTagName.value = ''
  addTagDialogVisible.value = false
}
function removeTag(index: number) {
  form.tags.splice(index, 1)
}
// 按钮功能
const handleSave = () => {
  ElMessage.success('保存成功（示例）')
}
const handlePreview = () => {
  ElMessage.info('预览功能开发中')
}
const handlePublish = () => {
  ElMessage.success('发布成功（示例）')
}
const editDialogVisible = ref(false)
const editSummaryIdx = ref(-1)
const editSummaryPoints = ref<string[]>([])

function editSummary(idx: number) {
  editSummaryIdx.value = idx
  // 深拷贝，避免直接修改原数据
  editSummaryPoints.value = [...aiSummaries.value[idx]]
  editDialogVisible.value = true
}
function saveEditSummary() {
  if (editSummaryIdx.value >= 0) {
    aiSummaries.value[editSummaryIdx.value] = editSummaryPoints.value.map(s => s.trim()).filter(Boolean)
    ElMessage.success('摘要已更新')
  }
  editDialogVisible.value = false
}
Object.assign(form, {
  videoTop: false,
  featured: false,
  specialContent: false,
  enableAdvert: false,
  adRule: '',
  geoLimit: false,
  footerDecorate: false,
  footerFaq: false
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      background: transparent;
      padding: 0;
      gap: 26px;
    }
  }
}
.editor-main-layout {
  display: flex;
  align-items: flex-start;
  position: relative;
  .editor-left {
    flex: 1;
    min-width: 0;
    padding-right: 26px;
    background: #fff;
    border-radius: 10px;
    padding:0 20px 15px 20px;
   
    .ai-summary-block {
      .ai-summary-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
      }
      .ai-summary-list {
        .ai-summary-card {
          display: flex;
          flex-direction: column;
          gap: 12px;
          border-radius: 4px;
          color: #000;
          &:hover {
            transform: translateY(-2px);
          }
          ::v-deep(.el-card__body) {
            padding: 12px;
          }
          .ai-summary-title {
            display: flex;
            align-items: center;
          }
          .ai-summary-content {
            margin-top: 8px;
            height: 100px;
            ul {
              padding: 0;
            }
          }
        }
      }
    }
    .main-content-tabs {
      margin-top: 16px;
    }
    .edit-record-placeholder {
      color: #aaa;
      text-align: center;
      padding: 32px 0;
    }
  }
  .editor-right {
    width: 320px;
    background: #fff;
    border-radius: 0 10px 10px 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 0 20px 15px 20px;
    position: sticky;
    top: 24px;
    transition: opacity 0.2s, transform 0.2s;
    z-index: 10;
   
    .toggle-panel-btn {
      position: absolute;
      left: -26px;
      top: 15px;
      transform: translateY(-50%);
      width: 26px;
      height: 30px;
      background: #007ee5;
      border-radius: 4px 0 0 4px;
      box-shadow: 0 2px 8px rgba(25,118,255,0.18);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 20;
      transition: left 0.2s;
      &:hover {
        background: #1565c0;
      }
    }
    .right-header {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 8px;
    }
    .avatar-uploader {
      .avatar {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        display: block;
      }
      .avatar-uploader-icon {
        font-size: 32px;
        color: #bbb;
      }
    }
    .settings-tabs {
      .el-tabs__content {
        padding: 0;
      }
      .el-tab-pane {
        padding: 0;
      }
    }
  }
}
.collapsed {
  width: 26px;
  height: max-content;
  overflow: visible;
  background: transparent;
  box-shadow: none;
  padding: 0;
  position: absolute;
  right: 0;
  .toggle-panel-btn {
    width: 26px;
    height: 32px;
    display: flex;
    flex-flow: wrap;
    background: #007ee5;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 8px rgba(25,118,255,0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 20;
    transition: left 0.2s;
    font-size: 12px;
    color: #fff;
    &:hover {
      background: #1565c0;
    }
  }
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}
.form-item-label-cls {
  padding:0;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.avatar-upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  .avatar-upload-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 96px;
    height: 96px;
    margin-bottom: 8px;
  }
  .avatar-img {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    object-fit: cover;
    background: #f2f3f5;
    border: 1px solid #eee;
  }
  .avatar-placeholder {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    background: #f2f3f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #bbb;
    font-size: 48px;
  }
  .avatar-desc {
    color: #000;
    font-size: 16px;
    text-align: center;
    margin-top: 4px;
    letter-spacing: 1px;
  }
}
.slide-right-panel-enter-active,
.slide-right-panel-leave-active {
  transition: transform 0.3s cubic-bezier(0.4,0,0.2,1), opacity 0.3s cubic-bezier(0.4,0,0.2,1);
}
.slide-right-panel-enter-from,
.slide-right-panel-leave-to {
  transform: translateX(120%);
  opacity: 0;
}
.slide-right-panel-enter-to,
.slide-right-panel-leave-from {
  transform: translateX(0);
  opacity: 1;
}
</style> 
