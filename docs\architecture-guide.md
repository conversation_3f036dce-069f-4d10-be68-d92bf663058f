# 架构指南

## 目录
- [系统架构](#系统架构)
- [模块化设计](#模块化设计)
- [接口设计](#接口设计)
- [安全规范](#安全规范)
- [部署架构](#部署架构)

## 系统架构

### 1. 整体架构

```
系统架构
├── 前端层
│   ├── Web 前台（Vue3 + TypeScript）
│   └── 管理后台（Vue3 + Element Plus）
├── 接口层
│   ├── Web API（Web 路由）
│   ├── Admin API（管理后台路由）
│   ├── OpenAPI（开放接口）
│   └── API（内部接口）
├── 应用层
│   ├── Services（应用服务）
│   └── Events（事件系统）
├── 领域层
│   ├── Entities（实体）
│   ├── Repositories（仓储）
│   └── Domain Services（领域服务）
└── 基础设施层
    ├── Database（数据库）
    ├── Cache（缓存）
    └── Queue（队列）
```

### 2. 技术栈

- 前端：Vue3、TypeScript、Element Plus
- 后端：PHP 8.2、Laravel 11
- 数据库：MySQL 8.0
- 缓存：Redis
- 搜索：Elasticsearch
- 队列：Redis Queue

## 模块化设计

### 1. 模块结构

```
Modules/
└── Course/                    # 课程模块
    ├── Api/                   # API 接口
    │   └── Controller/       # API 控制器
    ├── Web/                  # Web 接口
    │   └── Controllers/     # Web 控制器
    ├── Admin/                # 管理后台
    │   └── Controllers/     # 管理控制器
    ├── OpenApi/              # 开放接口
    │   └── Controllers/     # 开放控制器
    ├── Domain/               # 领域层
    │   ├── Entities/       # 实体
    │   ├── Repositories/   # 仓储
    │   └── Services/       # 领域服务
    └── Services/             # 应用服务
```

### 2. 模块通信

```php
// 事件通信
class CourseCreatedEvent
{
    public function __construct(
        public readonly Course $course
    ) {}
}

// 事件监听
class CourseEventSubscriber
{
    public function handleCourseCreated(CourseCreatedEvent $event)
    {
        // 处理课程创建事件
    }
}
```

## 接口设计

### 1. RESTful API

```php
// 路由定义
Route::prefix('api/v1')->group(function () {
    Route::get('/courses', [CourseController::class, 'index']);
    Route::post('/courses', [CourseController::class, 'store']);
    Route::get('/courses/{id}', [CourseController::class, 'show']);
    Route::put('/courses/{id}', [CourseController::class, 'update']);
    Route::delete('/courses/{id}', [CourseController::class, 'destroy']);
});
```

### 2. 接口规范

```php
// 统一响应格式
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "errors": {
        "field": ["错误信息"]
    }
}
```

### 3. 接口版本控制

```php
// API 版本控制
Route::prefix('api/v1')->middleware(['api.version:v1'])->group(function () {
    // V1 版本路由
});

Route::prefix('api/v2')->middleware(['api.version:v2'])->group(function () {
    // V2 版本路由
});
```

## 安全规范

### 1. 认证授权

```php
// JWT 认证
class JWTGuard implements Guard
{
    public function user()
    {
        return $this->auth->user();
    }
}

// 权限控制
class CoursePolicy
{
    public function view(User $user, Course $course)
    {
        return $user->can('view_course');
    }
}
```

### 2. 数据验证

```php
// 请求验证
class CreateCourseRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1'
        ];
    }
}
```

### 3. 安全中间件

```php
// CORS 中间件
class CorsMiddleware
{
    public function handle($request, Closure $next)
    {
        return $next($request)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    }
}
```

## 部署架构

### 1. 开发环境

```yaml
# docker-compose.yml
version: '3'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: course

  redis:
    image: redis:alpine
```

### 2. 生产环境

```
生产架构
├── 负载均衡
│   └── Nginx
├── Web 服务器
│   ├── PHP-FPM
│   └── Nginx
├── 数据库
│   ├── Master
│   └── Slave
├── 缓存服务器
│   └── Redis Cluster
└── 队列服务器
    └── Horizon
```

### 3. CI/CD 流程

```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      - name: Install Dependencies
        run: composer install
      - name: Run Tests
        run: php artisan test
      - name: Deploy
        run: |
          # 部署脚本
```

## 监控和日志

### 1. 日志配置

```php
// 日志配置
'channels' => [
    'daily' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => 'debug',
        'days' => 14,
    ],
    'slack' => [
        'driver' => 'slack',
        'url' => env('LOG_SLACK_WEBHOOK_URL'),
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
    ],
]
```

### 2. 性能监控

```php
// Telescope 配置
Telescope::filter(function (IncomingEntry $entry) {
    return true;
});

// 性能日志
Log::channel('performance')->info('查询执行时间', [
    'query' => $sql,
    'time' => $executionTime
]);
```

## 扩展性设计

### 1. 插件系统

```php
// 插件注册
class CourseServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(
            CourseRepositoryInterface::class,
            CourseRepository::class
        );
    }
}
```

### 2. 事件系统

```php
// 事件分发
class CourseService
{
    public function create(array $data): Course
    {
        $course = $this->repository->create($data);
        event(new CourseCreated($course));
        return $course;
    }
}
```

### 3. 钩子系统

```php
// 钩子定义
class CourseHooks
{
    public static function register()
    {
        add_action('course.created', function ($course) {
            // 处理课程创建后的操作
        });
    }
}
``` 
