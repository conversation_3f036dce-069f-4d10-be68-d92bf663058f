$(document).ready(function(){$.when($("head").append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css" type="text/css" />'),$.getScript("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput-jquery.min.js"),$.getScript("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.min.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("input[type=tel]").each(function(){var that=this;var altID=that.id+"_alt";$(that).after($(that).clone().attr("id",altID).attr("name",altID)).hide();$("#"+altID).change(function(){$(that).val($(this).intlTelInput("getNumber"))}).intlTelInput({initialCountry:$(that).attr("data-initial-country")||"auto",geoIpLookup:function(callback){if(this.initialCountry==="auto"){$.get("https://ipinfo.io",function(){},"jsonp").always(function(resp){var countryCode=resp&&resp.country?resp.country:"";callback(countryCode)})}}})});$("body").css("min-height","150px");$(".iti").css("width","100%")})});