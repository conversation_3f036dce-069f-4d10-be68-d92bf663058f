<?php
// ========================================
// 权限管理控制器
// ========================================

namespace Modules\Users\Api\Controller;


use Illuminate\Http\Request;
use Modules\Users\Models\Menu;
use Modules\Users\Models\Role;
use Illuminate\Support\Facades\DB;
use Modules\Users\Models\Permission;
use App\Http\Controllers\Controller;
use Modules\Users\Services\RBACService;
use Illuminate\Support\Facades\Validator;

/**
 * 角色管理控制器
 */
class RoleController extends Controller
{
    protected $rbacService;

    public function __construct(RBACService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * 角色列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Role::query();

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('role_name', 'like', "%{$search}%")
                  ->orWhere('role_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        $roles = $query->withCount(['admins', 'permissions'])
                      ->orderBy('sort_order')
                      ->paginate($request->input('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $roles
        ]);
    }

    /**
     * 创建角色
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role_name' => 'required|string|max:50|unique:roles',
            'role_code' => 'required|string|max:50|unique:roles',
            'description' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $role = Role::create([
                'role_name' => $request->input('role_name'),
                'role_code' => $request->input('role_code'),
                'description' => $request->input('description'),
                'sort_order' => $request->input('sort_order', 0),
                'status' => 1
            ]);

            return response()->json([
                'success' => true,
                'message' => '角色创建成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '角色创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, int $id)
    {
        $role = Role::findOrFail($id);

        // 系统角色不允许修改名称和代码
        if ($role->is_system) {
            $validator = Validator::make($request->all(), [
                'description' => 'nullable|string|max:255',
                'sort_order' => 'nullable|integer|min:0',
                'status' => 'nullable|in:0,1'
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'role_name' => 'required|string|max:50|unique:roles,role_name,' . $id,
                'role_code' => 'required|string|max:50|unique:roles,role_code,' . $id,
                'description' => 'nullable|string|max:255',
                'sort_order' => 'nullable|integer|min:0',
                'status' => 'nullable|in:0,1'
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updateData = $request->only(['description', 'sort_order', 'status']);

            if (!$role->is_system) {
                $updateData['role_name'] = $request->input('role_name');
                $updateData['role_code'] = $request->input('role_code');
            }

            $role->update($updateData);

            return response()->json([
                'success' => true,
                'message' => '角色更新成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '角色更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除角色
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(int $id)
    {
        $role = Role::findOrFail($id);

        // 系统角色不允许删除
        if ($role->is_system) {
            return response()->json([
                'success' => false,
                'message' => '系统角色不允许删除'
            ], 403);
        }

        // 检查是否有用户使用此角色
        if ($role->admins()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该角色下还有用户，无法删除'
            ], 400);
        }

        try {
            $role->delete();

            return response()->json([
                'success' => true,
                'message' => '角色删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '角色删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取角色权限
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function permissions(int $id)
    {
        $role = Role::findOrFail($id);
        $permissions = $role->permissions()->pluck('permission_id')->toArray();

        return response()->json([
            'success' => true,
            'data' => $permissions
        ]);
    }

    /**
     * 分配角色权限
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignPermissions(Request $request, int $id)
    {
        $validator = Validator::make($request->all(), [
            'permission_ids' => 'required|array',
            'permission_ids.*' => 'integer|exists:permissions,permission_id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $role = Role::findOrFail($id);
        $permissionIds = $request->input('permission_ids');

        try {
            $this->rbacService->assignPermissions($id, $permissionIds, auth()->id());

            return response()->json([
                'success' => true,
                'message' => '权限分配成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '权限分配失败：' . $e->getMessage()
            ], 500);
        }
    }
}

/**
 * 权限管理控制器
 */
class PermissionController extends Controller
{
    /**
     * 权限列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Permission::query();

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('permission_name', 'like', "%{$search}%")
                  ->orWhere('permission_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 模块筛选
        if ($request->filled('module')) {
            $query->where('module', $request->input('module'));
        }

        // 资源类型筛选
        if ($request->filled('resource')) {
            $query->where('resource', $request->input('resource'));
        }

        $permissions = $query->orderBy('sort_order')
                            ->paginate($request->input('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $permissions
        ]);
    }

    /**
     * 获取权限模块列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function modules()
    {
        $modules = Permission::distinct()
                            ->pluck('module')
                            ->filter()
                            ->values();

        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }

    /**
     * 获取权限资源列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function resources()
    {
        $resources = Permission::distinct()
                              ->pluck('resource')
                              ->filter()
                              ->values();

        return response()->json([
            'success' => true,
            'data' => $resources
        ]);
    }
}

/**
 * 菜单管理控制器
 */
class MenuController extends Controller
{
    protected $rbacService;

    public function __construct(RBACService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * 菜单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Menu::query();

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('menu_name', 'like', "%{$search}%")
                  ->orWhere('menu_code', 'like', "%{$search}%");
            });
        }

        // 菜单类型筛选
        if ($request->filled('menu_type')) {
            $query->where('menu_type', $request->input('menu_type'));
        }

        $menus = $query->orderBy('sort_order')->get();

        // 构建菜单树
        $menuTree = $this->buildMenuTree($menus);

        return response()->json([
            'success' => true,
            'data' => $menuTree
        ]);
    }

    /**
     * 创建菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'parent_id' => 'nullable|integer|exists:menus,menu_id',
            'menu_name' => 'required|string|max:100',
            'menu_code' => 'required|string|max:100|unique:menus',
            'menu_type' => 'required|in:1,2,3',
            'route_path' => 'nullable|string|max:255',
            'component_path' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_visible' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $menu = Menu::create([
                'parent_id' => $request->input('parent_id', 0),
                'menu_name' => $request->input('menu_name'),
                'menu_code' => $request->input('menu_code'),
                'menu_type' => $request->input('menu_type'),
                'route_path' => $request->input('route_path'),
                'component_path' => $request->input('component_path'),
                'icon' => $request->input('icon'),
                'sort_order' => $request->input('sort_order', 0),
                'is_visible' => $request->input('is_visible', true),
                'status' => 1
            ]);

            return response()->json([
                'success' => true,
                'message' => '菜单创建成功',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '菜单创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新菜单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, int $id)
    {
        $menu = Menu::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'parent_id' => 'nullable|integer|exists:menus,menu_id',
            'menu_name' => 'required|string|max:100',
            'menu_code' => 'required|string|max:100|unique:menus,menu_code,' . $id,
            'menu_type' => 'required|in:1,2,3',
            'route_path' => 'nullable|string|max:255',
            'component_path' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_visible' => 'boolean',
            'status' => 'in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $menu->update($request->all());

            return response()->json([
                'success' => true,
                'message' => '菜单更新成功',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '菜单更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除菜单
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(int $id)
    {
        $menu = Menu::findOrFail($id);

        // 系统菜单不允许删除
        if ($menu->is_system) {
            return response()->json([
                'success' => false,
                'message' => '系统菜单不允许删除'
            ], 403);
        }

        // 检查是否有子菜单
        if ($menu->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该菜单下还有子菜单，无法删除'
            ], 400);
        }

        try {
            $menu->delete();

            return response()->json([
                'success' => true,
                'message' => '菜单删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '菜单删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新菜单排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'menus' => 'required|array',
            'menus.*.menu_id' => 'required|integer|exists:menus,menu_id',
            'menus.*.sort_order' => 'required|integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            foreach ($request->input('menus') as $menuData) {
                Menu::where('menu_id', $menuData['menu_id'])
                    ->update(['sort_order' => $menuData['sort_order']]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '菜单排序更新成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '菜单排序更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 构建菜单树
     *
     * @param \Illuminate\Support\Collection $menus
     * @param int $parentId
     * @return array
     */
    private function buildMenuTree($menus, int $parentId = 0): array
    {
        $tree = [];

        foreach ($menus as $menu) {
            if ($menu->parent_id == $parentId) {
                $children = $this->buildMenuTree($menus, $menu->menu_id);
                if (!empty($children)) {
                    $menu->children = $children;
                }
                $tree[] = $menu;
            }
        }

        return $tree;
    }
}
