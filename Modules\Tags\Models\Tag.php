<?php

namespace Modules\Tags\Models;

use Modules\Common\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 标签模型
 * 对应数据库表: tvb_tags
 */
class Tag extends BaseModel
{
    // 使用软删除功能
    use SoftDeletes;

    // 标签状态常量
    const STATUS_DISABLED = 0;  // 禁用
    const STATUS_ENABLED = 1;   // 启用

    // 标签分类常量
    const CATEGORY_NEWS = 1;      // 新闻标签
    const CATEGORY_ARTICLE = 2;   // 文章标签
    const CATEGORY_VIDEO = 3;     // 视频标签
    const CATEGORY_PHOTO = 4;     // 图片标签
    const CATEGORY_TOPIC = 5;     // 专题标签

    public static $categoryMap = [
        self::CATEGORY_NEWS => '新闻标签',
        self::CATEGORY_ARTICLE => '文章标签',
        self::CATEGORY_VIDEO => '视频标签',
        self::CATEGORY_PHOTO => '图片标签',
        self::CATEGORY_TOPIC => '专题标签',
    ];

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'tags';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'name',            // 标签名称
        'reference_name',  // 参考名称
        'category_id',     // 分类ID
        'status',          // 状态: 0-禁用, 1-启用
        'sort_order',      // 排序顺序
        'created_by',      // 创建人ID
        'updated_by',      // 更新人ID
        'created_at',      // 创建时间
        'updated_at',      // 更新时间
        'deleted_at',      // 软删除时间
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * 检查指定名称的标签是否存在（未被软删除且is_deleted=0）
     * @param string $name
     * @return bool
     */
    public static function nameExists($name)
    {
        return self::where('name', $name)
            ->where('is_deleted', 0)
            ->whereNull('deleted_at')
            ->exists();
    }
   
}
