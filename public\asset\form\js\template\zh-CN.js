/*! Select2 4.0.13 | https://github.com/select2/select2/blob/master/LICENSE.md */

!function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd)var n=jQuery.fn.select2.amd;n.define("select2/i18n/zh-CN",[],function(){return{errorLoading:function(){return"æ— æ³•è½½å…¥ç»“æžœã€‚"},inputTooLong:function(n){return"è¯·åˆ é™¤"+(n.input.length-n.maximum)+"ä¸ªå­—ç¬¦"},inputTooShort:function(n){return"è¯·å†è¾“å…¥è‡³å°‘"+(n.minimum-n.input.length)+"ä¸ªå­—ç¬¦"},loadingMore:function(){return"è½½å…¥æ›´å¤šç»“æžœâ€¦"},maximumSelected:function(n){return"æœ€å¤šåªèƒ½é€‰æ‹©"+n.maximum+"ä¸ªé¡¹ç›®"},noResults:function(){return"æœªæ‰¾åˆ°ç»“æžœ"},searching:function(){return"æœç´¢ä¸­â€¦"},removeAllItems:function(){return"åˆ é™¤æ‰€æœ‰é¡¹ç›®"}}}),n.define,n.require}();
