{"phrases": {"form.name": "Form Name", "form.description": "Used to identify the form on administration pages.", "form.layout": "Form Layout", "form.disabled": "Disable form elements", "form.sourceCode": "Source Code preview", "form.copy": "Copy", "form.copied": "Copied!", "form.copyToClipboard": "Copy to clipboard", "formSteps.title": "Form Steps", "formSteps.id": "ID / Name", "formSteps.steps": "Steps", "formSteps.progressBar": "Progress Bar", "formSteps.noTitles": "No Titles", "formSteps.noStages": "No Stages", "formSteps.noSteps": "No Form Steps", "popover.save": "Save", "popover.delete": "Delete", "popover.cancel": "Cancel", "popover.more": "More", "tab.fields": "Fields", "tab.settings": "Settings", "tab.code": "Code", "alert.warning": "Warning!", "alert.errorSavingData": "There was a problem saving data. Please retry later", "alert.unsavedChanges": "YOU HAVE UNSAVED CHANGES! ALL CHANGES IN THE FORM WILL BE LOST!", "widget.button": "Submit", "widget.checkbox": "Checkboxes", "widget.date": "Date Field", "widget.email": "Email Field", "widget.file": "File Upload", "widget.heading": "Heading", "widget.hidden": "Hidden Field", "widget.number": "Number Field", "widget.pageBreak": "Page Break", "widget.paragraph": "Paragraph", "widget.radio": "Radio Buttons", "widget.recaptcha": "reCaptcha", "widget.selectList": "Select List", "widget.snippet": "Snippet", "widget.text": "Text Field", "widget.textArea": "Text Area", "heading.title": "Heading", "paragraph.title": "Paragraph", "text.title": "Text", "number.title": "Number", "date.title": "Date", "email.title": "Email", "textarea.title": "Text Area", "checkbox.title": "Checkbox", "radio.title": "Radio", "selectlist.title": "Select List", "hidden.title": "Hidden", "file.title": "File", "snippet.title": "Snippet", "recaptcha.title": "reCAPTCHA", "pagebreak.title": "Page Break", "button.title": "<PERSON><PERSON>", "component.id": "ID / Name", "component.text": "Text", "component.inputType": "Input Type", "component.type": "Type", "component.size": "Size", "component.label": "Label", "component.placeholder": "Placeholder", "component.required": "Required", "component.predefinedValue": "Predefined Value", "component.helpText": "Help Text", "component.fieldSize": "Field Size", "component.groupName": "Group Name", "component.checkboxes": "Checkboxes", "component.radios": "Radios", "component.options": "Options", "component.value": "Value", "component.accept": "Accept", "component.pattern": "Pattern", "component.integerPattern": "<PERSON><PERSON><PERSON>", "component.numberPattern": "Number Pattern", "component.prev": "Text of Previous <PERSON><PERSON>", "component.next": "Text of Next Button", "component.buttonText": "Button Text", "component.src": "Image Source", "component.inline": "Inline", "component.unique": "Unique", "component.readOnly": "Read Only", "component.integerOnly": "Integer Only", "component.minNumber": "Min number", "component.maxNumber": "Max number", "component.stepNumber": "Step number", "component.minDate": "Min date", "component.maxDate": "Max date", "component.minSize": "<PERSON>", "component.maxSize": "<PERSON>", "component.htmlCode": "HTML Code", "component.theme": "Theme", "component.checkDNS": "Check DNS", "component.multiple": "Multiple", "component.disabled": "Disabled", "component.cssClass": "CSS Class", "component.labelClass": "Label CSS Class", "component.containerClass": "Container CSS Class"}}