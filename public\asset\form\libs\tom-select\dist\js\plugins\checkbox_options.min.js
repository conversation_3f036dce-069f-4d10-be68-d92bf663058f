(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.checkbox_options=factory())})(this,function(){"use strict";const hash_key=value=>{if(typeof value==="undefined"||value===null)return null;return get_hash(value)};const get_hash=value=>{if(typeof value==="boolean")return value?"1":"0";return value+""};const preventDefault=(evt,stop=false)=>{if(evt){evt.preventDefault();if(stop){evt.stopPropagation()}}};const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const getDom=query=>{if(query.jquery){return query[0]}if(query instanceof HTMLElement){return query}if(isHtmlString(query)){var tpl=document.createElement("template");tpl.innerHTML=query.trim();return tpl.content.firstChild}return document.querySelector(query)};const isHtmlString=arg=>{if(typeof arg==="string"&&arg.indexOf("<")>-1){return true}return false};function plugin(){var self=this;var orig_onOptionSelect=self.onOptionSelect;self.settings.hideSelected=false;var UpdateCheckbox=function UpdateCheckbox(option){setTimeout(()=>{var checkbox=option.querySelector("input");if(checkbox instanceof HTMLInputElement){if(option.classList.contains("selected")){checkbox.checked=true}else{checkbox.checked=false}}},1)};self.hook("after","setupTemplates",()=>{var orig_render_option=self.settings.render.option;self.settings.render.option=(data,escape_html)=>{var rendered=getDom(orig_render_option.call(self,data,escape_html));var checkbox=document.createElement("input");checkbox.addEventListener("click",function(evt){preventDefault(evt)});checkbox.type="checkbox";const hashed=hash_key(data[self.settings.valueField]);if(hashed&&self.items.indexOf(hashed)>-1){checkbox.checked=true}rendered.prepend(checkbox);return rendered}});self.on("item_remove",value=>{var option=self.getOption(value);if(option){option.classList.remove("selected");UpdateCheckbox(option)}});self.on("item_add",value=>{var option=self.getOption(value);if(option){UpdateCheckbox(option)}});self.hook("instead","onOptionSelect",(evt,option)=>{if(option.classList.contains("selected")){option.classList.remove("selected");self.removeItem(option.dataset.value);self.refreshOptions();preventDefault(evt,true);return}orig_onOptionSelect.call(self,evt,option);UpdateCheckbox(option)})}return plugin});