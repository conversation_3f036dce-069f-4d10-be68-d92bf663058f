// 模拟数据
const SAMPLE_FILES = [
    { id: 'file1', name: 'document1.txt', content: '这是测试文档1，包含一些AI相关内容。AI技术正在快速发展，AI将改变我们的生活方式。' },
    { id: 'file2', name: 'document2.txt', content: '这是测试文档2，也有一些AI内容。AI可以用于图像识别和自然语言处理。' },
    { id: 'file3', name: 'document3.txt', content: '第三个测试文档，讨论AI和机器学习。AI和机器学习是相关的技术。' }
];

// 全局变量
let currentState = 'welcome';
let uploadedFiles = [];
let processedFiles = [];
let replaceRules = [];
let selectedFunction = '';
let chatHistory = [];
let isProcessingAction = false;

// 用户和AI头像
const AI_AVATAR = 'https://randomuser.me/api/portraits/women/44.jpg';
const USER_AVATAR = 'https://randomuser.me/api/portraits/men/32.jpg';

// 状态定义
const states = {
    welcome: {
        content: '您好，请问有什么可以帮到您？',
        buttons: [
            { text: '替换功能', action: 'replace' },
            { text: '批量修改功能', action: 'batch' },
            { text: '批量價格修改', action: 'price' }
        ]
    },
    replace_intro: {
        content: '好的，请问您要替换什么',
        next: 'replace_upload'
    },
    replace_upload: {
        content: '请上传需要替换内容的文件：'
    },
    replace_files_uploaded: {
        content: '已上传以下文件：',
        next: 'replace_rules_form'
    },
    replace_rules_form: {
        content: '请设置替换规则：'
    },
    replace_processing: {
        content: '正在执行替换操作，请稍候...'
    },
    replace_summary: {
        content: '即将替换的总词条数量：'
    },
    replace_preview: {
        content: '预览替换结果：'
    },
    replace_complete: {
        content: '修改执行中，请稍候...',
        next: 'replace_done'
    },
    replace_done: {
        content: '修改已完成！',
        buttons: [
            { text: '退出操作，不保存任何修改', action: 'exit' }
        ]
    },
    batch_intro: {
        content: '好的，请上传需要批量修改的文件'
    },
    price_intro: {
        content: '好的，请上传需要价格修改的文件'
    }
};

// 初始化聊天
function initChat() {
    renderState('welcome');
    
    // 设置事件监听
    document.getElementById('sendButton').addEventListener('click', handleUserInput);
    document.getElementById('userInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleUserInput();
        }
    });
}

// 渲染状态
function renderState(stateName) {
    console.log(`开始渲染状态: ${stateName}`);
    
    // 1. 状态合法性检查
    if (!states[stateName]) {
        console.error('未知状态:', stateName);
        return;
    }
    
    // 2. 更新当前状态
    currentState = stateName;
    const state = states[stateName];
    
    // 3. 禁用所有按钮，防止状态切换时的干扰
    disableAllButtons();
    
    try {
        // 4. 根据不同状态执行对应的渲染逻辑
        switch(stateName) {
            case 'replace_summary':
                renderSummaryState();
                break;
            case 'replace_preview':
                renderPreviewState();
                break;
            case 'replace_upload':
                renderUploadState();
                break;
            case 'replace_rules_form':
                renderRulesFormState();
                break;
            default:
                renderDefaultState(state);
        }
        
        // 5. 处理自动状态转换
        if (state.next && !['replace_summary', 'replace_preview'].includes(stateName)) {
            console.log(`计划自动转换到状态: ${state.next}`);
            setTimeout(() => {
                console.log(`执行自动转换到状态: ${state.next}`);
                renderState(state.next);
            }, 1000);
        }
    } catch (error) {
        console.error('状态渲染错误:', error);
        addMessage('ai', '抱歉，处理过程中出现错误，请重试');
        enableAllButtons();
    }
}

// 渲染预览状态
function renderPreviewState() {
    console.log('渲染预览状态');
    
    // 1. 添加基础消息
    addMessage('ai', states['replace_preview'].content);
    
    // 2. 创建预览容器
    const previewContainer = createPreviewContainer();
    
    try {
        // 3. 生成预览内容
        const previewContent = generatePreviewContent();
        previewContainer.appendChild(previewContent);
        
        // 4. 添加到聊天区域
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.appendChild(previewContainer);
        
        // 5. 添加操作按钮
        addButtons([
            { text: '取消，返回上一步', action: 'cancel' },
            { text: '确认进行替换', action: 'confirm', special: true }
        ]);
        
        // 6. 启用按钮
        enableAllButtons();
        
    } catch (error) {
        console.error('预览生成错误:', error);
        addMessage('ai', `预览生成失败: ${error.message}`);
        
        // 添加重试按钮
        addButtons([
            { text: '返回上一步', action: 'cancel' }
        ]);
        enableAllButtons();
    }
}

// 创建预览容器
function createPreviewContainer() {
    const container = document.createElement('div');
    container.className = 'preview-container';
    Object.assign(container.style, {
        margin: '10px 0 10px 50px',
        backgroundColor: '#f9f9f9',
        padding: '15px',
        borderRadius: '8px',
        maxWidth: '70%',
        maxHeight: '300px',
        overflowY: 'auto',
        whiteSpace: 'pre-wrap'
    });
    return container;
}

// 生成预览内容
function generatePreviewContent() {
    console.log('生成预览内容');
    
    if (!processedFiles.length) {
        throw new Error('没有可预览的文件');
    }
    
    const contentDiv = document.createElement('div');
    const firstFile = processedFiles[0];
    
    // 1. 添加文件标题
    const fileHeader = document.createElement('h4');
    fileHeader.textContent = firstFile.file.name;
    fileHeader.style.margin = '0 0 10px 0';
    contentDiv.appendChild(fileHeader);
    
    // 2. 创建对比容器
    const diffContainer = document.createElement('div');
    
    // 3. 处理内容对比
    const { originalHtml, modifiedHtml } = generateDiffContent(firstFile);
    
    // 4. 设置对比视图
    diffContainer.innerHTML = `
        <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 5px;">原始内容：</div>
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px;">${originalHtml}</div>
        </div>
        <div>
            <div style="font-weight: bold; margin-bottom: 5px;">修改后内容：</div>
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px;">${modifiedHtml}</div>
        </div>
    `;
    
    contentDiv.appendChild(diffContainer);
    return contentDiv;
}

// 生成差异内容
function generateDiffContent(fileData) {
    // 1. 安全处理原始内容
    let originalContent = escapeHtml(fileData.file.content);
    let modifiedContent = escapeHtml(fileData.modifiedContent);
    
    // 2. 初始化HTML内容
    let originalHtml = originalContent;
    let modifiedHtml = modifiedContent;
    
    // 3. 应用高亮规则
    replaceRules.forEach((rule, index) => {
        if (!rule.find) return;
        
        try {
            // 转义并创建安全的正则表达式
            const escapedFind = escapeRegExp(rule.find);
            const regex = new RegExp(escapedFind, 'g');
            
            // 高亮原始文本中的匹配项
            originalHtml = originalHtml.replace(regex, 
                `<span style="background-color: #ffcccc;">${escapeHtml(rule.find)}</span>`);
            
            // 高亮修改后文本中的替换项
            if (rule.replace) {
                const escapedReplace = escapeRegExp(rule.replace);
                const replaceRegex = new RegExp(escapedReplace, 'g');
                modifiedHtml = modifiedHtml.replace(replaceRegex, 
                    `<span style="background-color: #ccffcc;">${escapeHtml(rule.replace)}</span>`);
            }
        } catch (error) {
            console.error(`规则 ${index + 1} 处理错误:`, error);
        }
    });
    
    return { originalHtml, modifiedHtml };
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 正则表达式转义函数
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 渲染摘要状态
function renderSummaryState() {
    console.log('渲染摘要状态');
    
    // 1. 生成摘要内容
    const summaryContent = generateSummaryContent();
    addMessage('ai', states['replace_summary'].content + summaryContent);
    
    // 2. 添加操作按钮
    addButtons([
        { text: '取消，返回上一步', action: 'cancel' },
        { text: '生成预览', action: 'preview', special: true }
    ]);
    
    // 3. 启用按钮
    enableAllButtons();
}

// 生成摘要内容
function generateSummaryContent() {
    const totalReplacements = processedFiles.reduce((sum, file) => sum + file.replacementCount, 0);
    
    let content = `\n即将替换的总词条数量: ${totalReplacements}\n`;
    
    processedFiles.forEach((processed, index) => {
        content += `\n分類${index + 1}  共计${processed.file.name.length}个文檔，${processed.replacementCount}處文本需替換\n`;
    });
    
    content += '\n请下载 摘要报告 或 在线预览，並確認是否進行下一步';
    
    return content;
}

// 渲染默认状态
function renderDefaultState(state) {
    // 1. 添加消息
    addMessage('ai', state.content);
    
    // 2. 添加按钮（如果有）
    if (state.buttons) {
        addButtons(state.buttons);
        enableAllButtons();
    }
}

// 添加消息
function addMessage(sender, text) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    // 添加头像
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'avatar';
    const avatarImg = document.createElement('img');
    avatarImg.src = sender === 'ai' ? AI_AVATAR : USER_AVATAR;
    avatarImg.alt = sender === 'ai' ? '阿博' : '用户';
    avatarDiv.appendChild(avatarImg);
    
    // 添加消息内容
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = text;
    
    // 组合消息
    if (sender === 'ai') {
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);
    } else {
        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(avatarDiv);
    }
    
    chatMessages.appendChild(messageDiv);
    
    // 记录聊天历史
    chatHistory.push({
        sender: sender,
        text: text,
        timestamp: new Date().toISOString()
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 添加按钮
function addButtons(buttons) {
    const chatMessages = document.getElementById('chatMessages');
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'buttons-container';
    buttonsContainer.id = 'buttons-' + Date.now(); // 添加唯一ID
    
    console.log('创建按钮容器:', buttonsContainer.id);
    
    buttons.forEach(button => {
        const btn = document.createElement('button');
        btn.className = 'action-button';
        if (button.special) {
            btn.classList.add('special-button');
        }
        btn.textContent = button.text;
        btn.dataset.action = button.action; // 使用data属性存储action
        btn.id = `btn-${button.action}-${Date.now()}`;
        console.log('创建按钮:', btn.id, button.text);
        
        btn.addEventListener('click', function(e) {
            console.log('点击按钮:', btn.id, button.text);
            // 阻止重复点击
            e.preventDefault();
            if (!isProcessingAction) {
                // 立即禁用所有按钮，防止重复点击
                disableAllButtons();
                // 处理按钮点击
                handleButtonClick(button);
            }
        });
        buttonsContainer.appendChild(btn);
    });
    
    chatMessages.appendChild(buttonsContainer);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 禁用所有功能按钮
function disableAllButtons() {
    isProcessingAction = true;
    const buttons = document.querySelectorAll('.action-button');
    buttons.forEach(button => {
        button.style.opacity = '0.5';
        button.style.pointerEvents = 'none';
        button.disabled = true; // 添加disabled属性
    });
}

// 启用所有功能按钮
function enableAllButtons() {
    isProcessingAction = false;
    const buttons = document.querySelectorAll('.action-button');
    buttons.forEach(button => {
        button.style.opacity = '1';
        button.style.pointerEvents = 'auto';
        button.disabled = false; // 移除disabled属性
    });
}

// 处理按钮点击
function handleButtonClick(button) {
    // 记录用户操作
    let userAction = '';
    
    if (button.action === 'replace') {
        selectedFunction = 'replace';
        userAction = '替换功能';
        addMessage('user', userAction);
        renderState('replace_intro');
    } else if (button.action === 'batch') {
        selectedFunction = 'batch';
        userAction = '批量修改功能';
        addMessage('user', userAction);
        renderState('batch_intro');
    } else if (button.action === 'price') {
        selectedFunction = 'price';
        userAction = '批量价格修改';
        addMessage('user', userAction);
        renderState('price_intro');
    } else if (button.action === 'cancel') {
        userAction = '取消，返回上一步';
        addMessage('user', userAction);
        renderState('replace_rules_form');
    } else if (button.action === 'exit') {
        userAction = '退出操作，不保存任何修改';
        addMessage('user', userAction);
        renderState('welcome');
    } else if (button.action === 'preview') {
        userAction = '生成预览';
        addMessage('user', userAction);
        renderState('replace_preview');
    } else if (button.action === 'confirm') {
        userAction = '确认进行替换';
        addMessage('user', userAction);
        renderState('replace_complete');
    }
    
    console.log(`用户点击按钮: ${userAction} (${button.action})`);
    
    // 只有在按钮点击完成后才启用，不再使用计时器
    // 新的状态渲染完成后会重新启用按钮
}

// 处理用户输入
function handleUserInput() {
    const userInput = document.getElementById('userInput');
    const text = userInput.value.trim();
    
    if (text === '') return;
    
    addMessage('user', text);
    userInput.value = '';
    
    // 简单的响应逻辑
    if (text.includes('替换') || text.includes('修改')) {
        selectedFunction = 'replace';
        renderState('replace_intro');
    } else if (text.includes('批量')) {
        selectedFunction = 'batch';
        renderState('batch_intro');
    } else if (text.includes('价格')) {
        selectedFunction = 'price';
        renderState('price_intro');
    } else {
        // 默认响应
        setTimeout(() => {
            addMessage('ai', '我是一个AI文本处理助手，可以帮您进行文本替换、批量修改和价格调整等操作。请选择您需要的功能。');
            addButtons([
                { text: '替换功能', action: 'replace' },
                { text: '批量修改功能', action: 'batch' },
                { text: '批量價格修改', action: 'price' }
            ]);
        }, 500);
    }
}

// 渲染上传区域
function renderUploadState() {
    const chatMessages = document.getElementById('chatMessages');
    
    const uploadArea = document.createElement('div');
    uploadArea.className = 'upload-area';
    uploadArea.id = 'uploadArea';
    uploadArea.innerHTML = `
        <p>点击或拖放文件到此处上传</p>
        <input type="file" id="fileInput" multiple style="display: none;">
    `;
    
    chatMessages.appendChild(uploadArea);
    
    // 设置点击事件
    uploadArea.addEventListener('click', () => {
        simulateFileUpload();
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 模拟文件上传
function simulateFileUpload() {
    // 清空之前的文件
    uploadedFiles = [];
    
    // 从示例文件中选择随机数量的文件
    const numFiles = Math.min(Math.floor(Math.random() * 3) + 1, SAMPLE_FILES.length);
    
    for (let i = 0; i < numFiles; i++) {
        uploadedFiles.push(SAMPLE_FILES[i]);
    }
    
    // 显示上传中消息
    addMessage('user', `正在上传 ${numFiles} 个文件...`);
    
    // 模拟上传延迟
    setTimeout(() => {
        renderState('replace_files_uploaded');
    }, 1000);
}

// 获取文件列表HTML
function getFileListHTML() {
    if (uploadedFiles.length === 0) {
        return '\n暂无文件';
    }
    
    let fileList = '\n';
    uploadedFiles.forEach(file => {
        fileList += `- ${file.name}\n`;
    });
    
    return fileList;
}

// 渲染替换规则表单
function renderRulesFormState() {
    console.log('渲染替换规则表单');
    const chatMessages = document.getElementById('chatMessages');
    
    const formContainer = document.createElement('div');
    formContainer.className = 'replace-form';
    
    let formHTML = `
        <div id="replace-rules-container">
            <div class="replace-row">
                <input type="text" class="find-input" placeholder="查找内容" required>
                <input type="text" class="replace-input" placeholder="替换为">
                <div class="row-buttons">
                    <button class="remove-rule-btn">-</button>
                </div>
            </div>
        </div>
        <button class="add-rule-btn">+ 添加规则</button>
        <div class="form-buttons">
            <button class="action-button" id="start-replace-btn">开始替换</button>
            <button class="action-button" id="cancel-btn">取消</button>
        </div>
    `;
    
    formContainer.innerHTML = formHTML;
    chatMessages.appendChild(formContainer);
    
    // 设置按钮事件
    const addRuleBtn = formContainer.querySelector('.add-rule-btn');
    const removeRuleBtn = formContainer.querySelector('.remove-rule-btn');
    const startReplaceBtn = formContainer.querySelector('#start-replace-btn');
    const cancelBtn = formContainer.querySelector('#cancel-btn');
    
    // 添加规则按钮
    addRuleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!isProcessingAction) {
            addReplaceRule();
        }
    });
    
    // 删除规则按钮
    removeRuleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!isProcessingAction) {
            const rows = formContainer.querySelectorAll('.replace-row');
            if (rows.length > 1) {
                this.closest('.replace-row').remove();
            }
        }
    });
    
    // 开始替换按钮
    startReplaceBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!isProcessingAction) {
            console.log('点击开始替换按钮');
            disableAllButtons();
            collectReplaceRules();
            addMessage('user', '提交替换规则');
            renderState('replace_summary');
        }
    });
    
    // 取消按钮
    cancelBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!isProcessingAction) {
            console.log('点击取消按钮');
            disableAllButtons();
            addMessage('user', '取消');
            renderState('welcome');
        }
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // 启用按钮
    enableAllButtons();
}

// 添加替换规则
function addReplaceRule() {
    console.log('添加替换规则');
    const container = document.getElementById('replace-rules-container');
    const newRow = document.createElement('div');
    newRow.className = 'replace-row';
    newRow.innerHTML = `
        <input type="text" class="find-input" placeholder="查找内容" required>
        <input type="text" class="replace-input" placeholder="替换为">
        <div class="row-buttons">
            <button class="remove-rule-btn">-</button>
        </div>
    `;
    
    container.appendChild(newRow);
    
    // 设置删除按钮事件
    const removeBtn = newRow.querySelector('.remove-rule-btn');
    removeBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!isProcessingAction) {
            this.closest('.replace-row').remove();
        }
    });
}

// 收集替换规则
function collectReplaceRules() {
    replaceRules = [];
    const rows = document.querySelectorAll('.replace-row');
    
    rows.forEach(row => {
        const findText = row.querySelector('.find-input').value.trim();
        const replaceText = row.querySelector('.replace-input').value.trim();
        
        if (findText) {
            replaceRules.push({
                find: findText,
                replace: replaceText
            });
        }
    });
    
    // 如果没有规则，至少添加一个模拟规则
    if (replaceRules.length === 0) {
        replaceRules.push({
            find: 'AI',
            replace: '人工智能'
        });
    }
    
    // 模拟处理文件
    processFiles();
}

// 处理文件
function processFiles() {
    processedFiles = [];
    
    uploadedFiles.forEach(file => {
        let modifiedContent = file.content;
        let replacementCount = 0;
        
        replaceRules.forEach(rule => {
            const regex = new RegExp(rule.find, 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                replacementCount += matches.length;
            }
            modifiedContent = modifiedContent.replace(regex, rule.replace);
        });
        
        processedFiles.push({
            file: file,
            modifiedContent: modifiedContent,
            replacementCount: replacementCount
        });
    });
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initChat();
    
    // 设置打印JSON按钮
    document.getElementById('printJsonButton').addEventListener('click', function() {
        const chatData = {
            history: chatHistory,
            currentState: currentState,
            uploadedFiles: uploadedFiles.map(f => ({ id: f.id, name: f.name })),
            processedFiles: processedFiles.map(p => ({
                fileName: p.file.name,
                replacementCount: p.replacementCount
            })),
            replaceRules: replaceRules
        };
        
        console.log('聊天记录JSON:');
        console.log(JSON.stringify(chatData, null, 2));
        
        // 在页面上显示提示
        alert('聊天记录已打印到控制台，请按F12查看');
    });
});