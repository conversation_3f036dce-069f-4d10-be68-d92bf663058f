import { Node, mergeAttributes } from '@tiptap/core'
import { getButtonTemplate } from '../templates/button.template'

export interface ButtonBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    buttonBlock: {
      /**
       * 设置按钮块
       */
      setButtonBlock: (attributes?: { 
        text?: string
        type?: string
        size?: string
        style?: string
        href?: string
      }) => ReturnType
    }
  }
}

export const ButtonBlock = Node.create<ButtonBlockOptions>({
  name: 'bootstrap-button',

  group: 'block',

  content: 'inline*',

  draggable: true,

  addAttributes() {
    return {
      text: {
        default: '按钮'
      },
      type: {
        default: 'primary'
      },
      size: {
        default: ''
      },
      style: {
        default: 'solid'
      },
      href: {
        default: ''
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'button[data-bs-component="button"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }

          const classList = Array.from(element.classList)
          const type = classList.find(cls => cls.startsWith('btn-'))?.replace('btn-', '') || 'primary'
          const size = classList.find(cls => cls.startsWith('btn-') && ['sm', 'lg'].includes(cls.replace('btn-', '')))?.replace('btn-', '') || ''
          const style = classList.some(cls => cls.startsWith('btn-outline-')) ? 'outline' : 'solid'
          
          return {
            text: element.textContent,
            type,
            size,
            style
          }
        }
      },
      {
        tag: 'a[data-bs-component="button"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }

          const classList = Array.from(element.classList)
          const type = classList.find(cls => cls.startsWith('btn-'))?.replace('btn-', '') || 'primary'
          const size = classList.find(cls => cls.startsWith('btn-') && ['sm', 'lg'].includes(cls.replace('btn-', '')))?.replace('btn-', '') || ''
          const style = classList.some(cls => cls.startsWith('btn-outline-')) ? 'outline' : 'solid'
          
          return {
            text: element.textContent,
            type,
            size,
            style,
            href: element.getAttribute('href') || ''
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const {
      text = '按钮',
      type = 'primary',
      size = '',
      style = 'solid',
      href = ''
    } = HTMLAttributes

    const classes = [
      'btn',
      style === 'outline' ? `btn-outline-${type}` : `btn-${type}`,
      size ? `btn-${size}` : ''
    ].filter(Boolean).join(' ')

    if (href) {
      return ['a', mergeAttributes(
        {
          class: classes,
          href,
          role: 'button',
          'data-bs-component': 'button'
        },
        this.options.HTMLAttributes
      ), text]
    }

    return ['button', mergeAttributes(
      {
        class: classes,
        type: 'button',
        'data-bs-component': 'button'
      },
      this.options.HTMLAttributes
    ), text]
  },

  addCommands() {
    return {
      setButtonBlock:
        attributes =>
        ({ commands }) => {
          return commands.setNode(this.name, attributes)
        }
    }
  }
}) 