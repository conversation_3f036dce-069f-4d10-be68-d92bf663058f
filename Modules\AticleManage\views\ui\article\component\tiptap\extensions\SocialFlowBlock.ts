import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { socialFlowTemplate } from '../templates/socialFlow.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    socialFlowBlock: {
      insertSocialFlowBlock: () => ReturnType
    }
  }
}

export const SocialFlowBlock = Node.create({
  name: 'socialFlowBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div.social-flow-container',
      },
      {
        tag: 'div[data-bs-component="social-flow"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // 添加Font Awesome样式
    const fontAwesomeStyle = document.querySelector('link[href*="font-awesome"]');
    if (!fontAwesomeStyle) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
      document.head.appendChild(link);
    }
    
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'social-flow',
      'class': 'social-flow-block'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertSocialFlowBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(socialFlowTemplate)
        },
    }
  },
}) 