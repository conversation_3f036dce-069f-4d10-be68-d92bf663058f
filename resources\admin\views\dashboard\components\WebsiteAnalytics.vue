<template>
  <div class="website-analytics">
    <!-- 顶部区域 -->
    <el-row class="analytics-header" :gutter="20">
      <!-- 左侧标签页 -->
      <el-col :span="9">
        <div class="tab-btn-box">
          <div 
            class="tab-btn" 
            v-for="tab in tabs" 
            :key="tab.key" 
            :class="{ active: activeTab === tab.key }" 
            @click="activeTab = tab.key"
          >
            {{ t(`dashboard.websiteAnalytics.tabs.${tab.key}`) }}
          </div>
        </div>
      </el-col>

      <!-- 右侧筛选区域 -->
      <el-col style="flex: 1;">
        <div class="filter-group">
          <div class="filter-item">
            <label for="time-range">{{ t('dashboard.websiteAnalytics.filters.timeRange.label') }}</label>
            <el-select v-model="timeRange" placeholder="時間範圍" name="time-range">
              <el-option 
                value="30" 
                :label="t('dashboard.websiteAnalytics.filters.timeRange.options.last30')" 
              />
              <el-option 
                value="60" 
                :label="t('dashboard.websiteAnalytics.filters.timeRange.options.last60')" 
              />
              <el-option 
                value="90" 
                :label="t('dashboard.websiteAnalytics.filters.timeRange.options.last90')" 
              />
            </el-select>
          </div>
          <div class="filter-item">
            <label for="sort-way">{{ t('dashboard.websiteAnalytics.filters.sortBy.label') }}</label>
            <el-select v-model="sortBy" name="sort-way">
              <el-option 
                value="content" 
                :label="t('dashboard.websiteAnalytics.filters.sortBy.options.content')" 
              />
              <el-option 
                value="time" 
                :label="t('dashboard.websiteAnalytics.filters.sortBy.options.time')" 
              />
            </el-select>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 网站分析卡片列表 -->
    <div class="analytics-list">
      <template v-if="loading">
        <div v-for="i in 2" :key="'skeleton-' + i" class="analytics-card">
          <div class="card-header">
            <div class="site-info">
              <el-skeleton-item variant="circle" style="width: 58px; height: 58px;" />
              <div class="site-meta" style="padding-left: 14px;">
                <el-skeleton-item variant="text" style="width: 120px; height: 20px; margin-bottom: 8px;" />
                <el-skeleton-item variant="text" style="width: 180px; height: 16px;" />
              </div>
            </div>
          </div>
          <div class="card-content">
            <!-- 左侧内容更新数量骨架屏 -->
            <div class="content-stats">
              <div class="stat-header">
                <el-skeleton-item variant="text" style="width: 100px; height: 16px;" />
                <el-skeleton-item variant="text" style="width: 60px; height: 24px;" />
              </div>
              <div class="stat-list">
                <el-skeleton-item variant="text" style="width: 80px; height: 16px; margin-bottom: 12px;" />
                <el-skeleton-item variant="text" style="width: 140px; height: 16px; margin-bottom: 8px;" />
                <el-skeleton-item variant="text" style="width: 140px; height: 16px; margin-bottom: 8px;" />
                <el-skeleton-item variant="text" style="width: 140px; height: 16px;" />
              </div>
            </div>
            <!-- 图表区域骨架屏 -->
            <div class="chart-section first-chart">
              <el-skeleton-item variant="text" style="width: 80px; height: 16px; margin-bottom: 12px;" />
              <el-skeleton-item variant="rect" style="width: 100%; height: 140px;" />
            </div>
            <div class="chart-section">
              <el-skeleton-item variant="text" style="width: 100px; height: 16px; margin-bottom: 12px;" />
              <el-skeleton-item variant="rect" style="width: 100%; height: 160px;" />
            </div>
            <div class="chart-section">
              <el-skeleton-item variant="text" style="width: 120px; height: 16px; margin-bottom: 12px;" />
              <el-skeleton-item variant="rect" style="width: 100%; height: 160px;" />
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <div v-for="site in websites" :key="site.id" class="analytics-card">
          <!-- 添加卡片顶部区域 -->
          <div class="card-header">
            <div class="site-info">
              <div class="site-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="site-meta">
                <h3>{{ site.name }}</h3>
                <p>{{ site.url }}</p>
              </div>
            </div>
            <div class="site-actions">
              <div class="action-icons">
                <el-icon><Share /></el-icon>
                <el-icon><Star /></el-icon>
                <el-icon><More /></el-icon>
              </div>
            </div>
          </div>

          <div class="card-content">
            <!-- 左侧内容更新数量 -->
            <div class="content-stats">
              <div class="stat-header">
                <div class="stat-label">{{ t('dashboard.websiteAnalytics.content.updateCount') }}</div>
                <div class="stat-number">{{ site.updateCount }}</div>
              </div>
              <div class="stat-list">
                <div class="stat-tit">{{ t('dashboard.websiteAnalytics.content.updateTypes') }}</div>
                <div class="stat-item">
                  <div class="stat-label">
                    {{ t('dashboard.websiteAnalytics.content.articles', { count: site.articles }) }}
                  </div>
                  <span class="change" :class="site.articleChange > 0 ? 'up' : 'down'">
                    {{ site.articleChange > 0 ? '+' : '' }}{{ site.articleChange }}
                  </span>
                </div>
                <div class="stat-item">
                  <div class="stat-label">
                    {{ t('dashboard.websiteAnalytics.content.products', { count: site.products }) }}
                  </div>
                  <span class="change" :class="site.productChange > 0 ? 'up' : 'down'">
                    {{ site.productChange > 0 ? '+' : '' }}{{ site.productChange }}
                  </span>
                </div>
                <div class="stat-item">
                  <div class="stat-label">
                    {{ t('dashboard.websiteAnalytics.content.images', { count: site.images }) }}
                  </div>
                  <span class="change" :class="site.imageChange > 0 ? 'up' : 'down'">
                    {{ site.imageChange > 0 ? '+' : '' }}{{ site.imageChange }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 图表区域 -->
            <div class="chart-section first-chart">
              <div class="chart-title">{{ t('dashboard.websiteAnalytics.content.charts.trend') }}</div>
              <div :id="'trend-chart-' + site.id" class="chart trend-chart"></div>
            </div>
            <div class="chart-section">
              <div class="chart-title">{{ t('dashboard.websiteAnalytics.content.charts.distribution') }}</div>
              <div :id="'distribution-chart-' + site.id" class="chart pie-chart"></div>
            </div>
            <div class="chart-section">
              <div class="chart-title">{{ t('dashboard.websiteAnalytics.content.charts.comparison') }}</div>
              <div :id="'quality-chart-' + site.id" class="chart radar-chart"></div>
            </div>
          </div>
        </div>
      </template>

      <!-- 添加竞争对手按钮 -->
      <div class="add-competitor">
        <el-button class="add-btn" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          <span>{{ t('dashboard.websiteAnalytics.competitor.add') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 添加竞争对手弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="t('dashboard.websiteAnalytics.competitor.dialog.title')" 
      width="600px" 
      :close-on-click-modal="false"
    >
      <el-form :model="formData" label-position="top">
        <el-form-item :label="t('dashboard.websiteAnalytics.competitor.dialog.name')" required>
          <el-input 
            v-model="formData.name" 
            :placeholder="t('dashboard.websiteAnalytics.competitor.dialog.namePlaceholder')" 
          />
        </el-form-item>
        <el-form-item :label="t('dashboard.websiteAnalytics.competitor.dialog.url')" required>
          <el-input 
            v-model="formData.url" 
            :placeholder="t('dashboard.websiteAnalytics.competitor.dialog.urlPlaceholder')" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleAddCompetitor">
            {{ t('dashboard.websiteAnalytics.competitor.dialog.confirm') }}
          </el-button>
          <el-button @click="dialogVisible = false">
            {{ t('dashboard.websiteAnalytics.competitor.dialog.cancel') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { Share, Star, More, Plus, Monitor } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import http from '/admin/support/http'

const { t } = useI18n()

// 标签页配置
const tabs = [
  { key: 'overview', label: t('dashboard.websiteAnalytics.tabs.overview') },
  { key: 'content', label: t('dashboard.websiteAnalytics.tabs.content') },
  { key: 'hot', label: t('dashboard.websiteAnalytics.tabs.hot') },
]
const activeTab = ref('overview')

// 在 setup 函数中添加配置接收
const props = defineProps({
  settings: {
    type: Object,
    default: () => ({
      timeRange: '30',
      sortBy: 'content',
      trackCompetitors: true
    })
  }
})

// 修改筛选条件
const timeRange = ref(props.settings.timeRange)
const sortBy = ref(props.settings.sortBy)

// 图表引用
const websites = ref<any>([])

// 弹窗相关
const dialogVisible = ref(false)
const formData = ref({
  name: '',
  url: '',
})

const loading = ref(true)

const charts = ref<echarts.ECharts[]>([])

const initCharts = () => {
  // 清除旧的图表实例
  charts.value.forEach(chart => chart.dispose())
  charts.value = []

  // 为每个网站初始化图表
  websites.value.forEach((site: any) => {
    try {
      // 趋势图
      const trendChart = document.getElementById('trend-chart-' + site.id)
      if (trendChart) {
        const chart = echarts.init(trendChart)
        // 使用 JSON 转换去除 Proxy
        const trendOption = JSON.parse(JSON.stringify(site.trendChart))
        chart.setOption(trendOption)
        charts.value.push(chart)
      }

      // 分布图
      const distributionChart = document.getElementById('distribution-chart-' + site.id)
      if (distributionChart) {
        const chart = echarts.init(distributionChart)
        const distributionOption = JSON.parse(JSON.stringify(site.distributionChart))
        chart.setOption(distributionOption)
        charts.value.push(chart)
      }

      // 质量对比图
      const qualityChart = document.getElementById('quality-chart-' + site.id)
      if (qualityChart) {
        const chart = echarts.init(qualityChart)
        const qualityOption = JSON.parse(JSON.stringify(site.qualityChart))
        chart.setOption(qualityOption)
        charts.value.push(chart)
      }
    } catch (error) {
      console.error(`初始化图表失败 (站点 ID: ${site.id}):`, error)
    }
  })
}

// 监听窗口大小变化，重新调整图表大小
const handleResize = () => {
  charts.value.forEach(chart => chart.resize())
}

// 显示添加弹窗
const showAddDialog = () => {
  dialogVisible.value = true
  formData.value = {
    name: '',
    url: '',
  }
}

// 处理添加竞争对手
const handleAddCompetitor = async () => {
  if (!formData.value.name || !formData.value.url) {
    ElMessage.warning(t('dashboard.websiteAnalytics.competitor.messages.fillRequired'))
    return
  }

  try {
    await http.post('/dashboard/websiteAnalytics/competitor', formData.value)
    await getWebsiteAnalytics()
    dialogVisible.value = false
    ElMessage.success(t('dashboard.websiteAnalytics.competitor.messages.addSuccess'))
  } catch (error) {
    console.error('添加竞争对手失败:', error)
    ElMessage.error(t('dashboard.websiteAnalytics.competitor.messages.addFailed'))
  }
}

const getWebsiteAnalytics = async () => {
  loading.value = true
  try {
    const res = await http.get('/dashboard/websiteAnalytics/data')
    websites.value = res.data.data
    
    // 确保 DOM 更新完成后再初始化图表
    nextTick(() => {
      setTimeout(() => {
        initCharts()
        // 初始化完成后触发一次 resize 以确保图表正确渲染
        handleResize()
      }, 100)
    })
  } catch (error) {
    console.error('获取网站分析数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getWebsiteAnalytics()
  window.addEventListener('resize', handleResize)
})

// 在组件卸载时销毁图表实例和事件监听
onUnmounted(() => {
  charts.value.forEach(chart => chart.dispose())
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.website-analytics {
  .analytics-header {
    margin-bottom: 12px;

    @media screen and (max-width: 1200px) {
      flex-wrap: wrap;
    }

    .tab-btn-box {
      border-radius: 5px;
      border: 1px solid #032f6e;
      background-color: #fff;
      transition: background-color 0.35s ease-in-out;
      overflow: hidden;
      display: flex;

      @media screen and (max-width: 1600px) {
        margin-bottom: 14px;
      }

      .tab-btn {
        border-right: 1px solid #032f6e;
        padding: 10px 20px;
        font-size: 14px;
        line-height: 1.35;
        color: #0d0d0d;
        cursor: pointer;
        flex: 1;
        text-align: center;

        @media screen and (max-width: 1600px) {
          padding: 8px 10px;
        }

        &:last-child {
          border-right: 0;
        }

        &.active {
          background-color: #032f6e;
          color: #fff;
        }
      }
    }

    .filter-group {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      @media screen and (max-width: 1145px) {
        justify-content: flex-start;
      }

      .filter-item {
        display: flex;
        align-items: center;
        margin-left: 20px;

        @media screen and (max-width: 1600px) {
          margin-left: 10px;
        }

        &:first-child {
          margin-left: 0;
        }

        label {
          margin-right: 12px;
          color: #000;
          font-size: 16px;
          white-space: nowrap;

        }

        :deep(.el-select) {
          width: 154px;
          --el-input-text-color: #18191a;
          --el-input-icon-color: #18191a;
          --el-select-input-color: #18191a;
          --el-border-color: transparent;

          @media screen and (max-width: 1600px) {
            width: 134px;
          }

          .el-select__wrapper {
            border-radius: 5px;
            padding: 8px 12px;
            font-size: 16px;
            line-height: 1.3125;
            min-height: 38px;

            @media screen and (max-width: 1600px) {
              padding: 6px 10px;
              font-size: 14px;
              min-height: 34px;
            }
          }
        }
      }
    }
  }

  .analytics-list {
    .analytics-card {
      margin-bottom: 20px;
      border-radius: 20px;
      box-shadow: 0px 3px 6px #00000029;
      padding: 20px 22px;
      background-color: #fff;

      .card-header {
        border-bottom: 1px solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .site-info {
          padding-bottom: 18px;
          display: flex;
          align-items: center;

          .site-icon {
            border-radius: 10px;
            background-color: #e8e8e8;
            width: 58px;
            height: 58px;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-icon {
              font-size: 34px;
            }
          }

          .site-meta {
            padding-left: 14px;

            h3 {
              font-size: 16px;
              font-weight: bold;
              line-height: 1.3125;
              color: #0d0d0d;
            }

            p {
              font-size: 16px;
              color: #0d0d0d;
              line-height: 1.35;
            }
          }
        }

        .site-actions {
          .action-icons {
            display: flex;
            align-items: center;

            .el-icon {
              margin-left: 35px;
              font-size: 20px;
              color: #707070;
              cursor: pointer;
            }
          }
        }
      }

      .card-content {
        padding: 20px 0;
        display: flex;

        @media screen and (max-width: 1600px) {
          flex-wrap: wrap;
        }

        .content-stats {
          border-right: 1px solid #e6e6e6;
          padding-right: 18px;
          width: 20%;

          @media screen and (max-width: 1600px) {
            width: 50%;
          }

          .stat-header {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .stat-label {
              font-size: 14px;
              color: #000;
            }

            .stat-number {
              border-radius: 5px;
              padding: 4px 20px;
              background-color: #66bac3;
              color: #fff;
              font-size: 14px;
              font-weight: bold;
              line-height: 1.35;
            }
          }

          .stat-list {
            .stat-tit {
              margin-bottom: 5px;
              font-size: 14px;
              font-weight: bold;
              color: #000;
              line-height: 1.35;
            }

            .stat-item {
              margin-top: 6px;
              display: flex;
              align-items: center;

              &::before {
                margin-right: 6px;
                content: '';
                border-radius: 50%;
                background-color: #007ee5;
                display: block;
                width: 8px;
                height: 8px;
              }

              .stat-label {
                margin-right: 12px;
                font-size: 14px;
                color: #000;
                line-height: 1.35;
              }

              .change {
                color: #f32020;
                font-size: 14px;
                line-height: 1.35;
              }
            }
          }
        }

        .chart-section {
          border-right: 1px solid #e6e6e6;
          padding: 0 20px;
          width: 23%;
          display: flex;
          flex-direction: column;

          &:last-child {
            border-right: none;
            width: 33.33%;

            @media screen and (max-width: 1600px) {
              width: 50%;
            }
          }

          @media screen and (max-width: 1600px) {
            border-top: 1px solid #e6e6e6;
            padding-top: 20px;
            width: 50%;

            &.first-chart {
              padding-bottom: 20px;
              margin-top: 0;
              border-top: none;
            }
          }

          .chart-title {
            margin-bottom: 15px;
            font-size: 14px;
            color: #000;
            line-height: 1.35;
            flex-shrink: 0;
          }

          .chart {
            flex-grow: 1;
            width: 100%;

            &.trend-chart {
              height: 140px;
            }

            &.pie-chart {
              height: 160px;
              width: 200px;
            }

            &.radar-chart {
              height: 160px;
            }
          }
        }
      }
    }

    .add-competitor {
      border-radius: 100px;
      box-shadow: 0px 3px 6px #00000029;
      padding: 12px;
      width: 100%;
      background-color: #fff;

      .add-btn {
        display: flex;
        align-items: center;

        border-radius: 0;
        padding: 0;
        width: 100%;
        background-color: transparent;
        --el-button-border-color: transparent;
        --el-button-hover-border-color: transparent;
        --el-button-active-border-color: transparent;
        border: none;

        .el-icon {
          margin-right: 10px;
          border-radius: 50%;
          background-color: #f2f2f2;
          width: 22px;
          height: 22px;
          font-size: 10px;
          color: #66bac3;
        }

        span {
          color: #19496a;
          font-size: 14px;
        }
      }
    }
  }
}

.analytics-card {
  :deep(.el-skeleton__text) {
    background: rgba(0, 0, 0, 0.06);
  }
  
  :deep(.el-skeleton__circle) {
    background: rgba(0, 0, 0, 0.06);
  }
  
  :deep(.el-skeleton__rect) {
    background: rgba(0, 0, 0, 0.06);
  }
}
</style>
