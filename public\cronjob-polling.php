<?php

// 设置执行时间
set_time_limit(0);
ignore_user_abort(true);

// 加载 Laravel 框架
require __DIR__.'/../vendor/autoload.php';
$app = require_once __DIR__.'/../bootstrap/app.php';

$lockFile = storage_path('cronjob.lock');
$logFile = storage_path('logs/cronjob-polling.log');

// 检查是否已在运行
if (file_exists($lockFile)) {
    $lockTime = filectime($lockFile);
    if (time() - $lockTime < 3600) { // 1小时锁
        exit('Already running');
    }
}

// 创建锁文件
file_put_contents($lockFile, time());

try {
    // 执行任务
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->call('cronjob:run', [
        '--daemon' => true,
        '--interval' => 1
    ]);
} catch (Exception $e) {
    file_put_contents(
        $logFile,
        date('Y-m-d H:i:s ') . $e->getMessage() . "\n",
        FILE_APPEND
    );
} finally {
    // 清理锁文件
    @unlink($lockFile);
}
