import { NavigationGuardNext, RouteLocationNormalized, Router, RouteRecordRaw } from 'vue-router'
import { useUserStore } from '/admin/stores/modules/user'
import { getAuthToken, removeAuthToken, setPageTitle } from '/admin/support/helper'
import progress from '/admin/support/progress'
import { WhiteListPage } from '/admin/enum/app'
import { usePermissionsStore } from '/admin/stores/modules/user/permissions'
import { toRaw } from 'vue'
import { useAppStore } from '/admin/stores/modules/app'
import { Menu } from '/admin/types/Menu'
import { Permission } from '/admin/types/Permission'
import i18n from '/admin/i18n'
import { preprocessRouteTitle } from '/admin/composables/useRouterTitle'

function ensurePermissionRecord(permissions: Permission[] | Record<string, Permission>): Record<string, Permission> {
  if (Array.isArray(permissions)) {
    return permissions.reduce((acc, permission) => {
      acc[permission.permission_mark] = permission
      return acc
    }, {} as Record<string, Permission>)
  }
  return permissions
}

// 处理多语言标题
function getPageTitle(item: RouteLocationNormalized): string {
  if (!item.meta.title) return ''
  
  // 优先使用预翻译的标题
  if (item.meta._translatedTitle) {
    return item.meta._translatedTitle as string
  }
  
  // 检查是否需要翻译
  if (item.meta.i18n) {
    return i18n.global.t(item.meta.title as string)
  }
  
  // 处理包含点号的多语言key
  const title = item.meta.title as string
  if (title.includes('.')) {
    return i18n.global.t(title)
  }
  
  return title
}

const guard = (router: Router) => {
  router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    // 🔥 预处理路由标题翻译 - 确保在页面渲染前翻译完成
    preprocessRouteTitle(to)
    
    // 支持多语言标题
    setPageTitle(getPageTitle(to))
    progress.start()

    // 确保 app_code 总是存在于 query 中，但只在必要时添加
    const ensureAppCode = (route: RouteLocationNormalized): RouteLocationNormalized => {
      if (!route.query.app_code) {
        return {
          ...route,
          query: { ...route.query, app_code: 'admin' },
        }
      }
      return route
    }

    const proceedTo = (destination: RouteLocationNormalized | string) => {
      if (typeof destination === 'string') {
        // @ts-ignore
        next(ensureAppCode({ path: destination, query: { ...to.query, app_code: 'admin' } }))
      } else {
        const finalDestination = ensureAppCode(destination)
        // 比较路径和除 app_code 外的查询参数
        const currentQuery = { ...to.query }
        const newQuery = { ...finalDestination.query }
        delete currentQuery.app_code
        delete newQuery.app_code

        if (finalDestination.path !== to.path || JSON.stringify(currentQuery) !== JSON.stringify(newQuery)) {
          next(finalDestination)
        } else {
          next()
        }
      }
    }

    // 简化路由逻辑：去掉登录验证，直接进入dashboard
    // 如果访问登录页面，直接重定向到dashboard
    if (to.path === WhiteListPage.LOGIN_PATH) {
      proceedTo('/dashboard')
      progress.done()
      return
    }

    // 如果访问根路径，重定向到dashboard
    if (to.path === '/') {
      proceedTo('/dashboard')
      progress.done()
      return
    }

    // 所有其他路由直接放行，不再需要登录验证
    next()
    progress.done()
  })

  router.afterEach((to: RouteLocationNormalized) => {
    useAppStore().setActiveMenu(to.path)
    progress.done()
  })
}

export default guard
