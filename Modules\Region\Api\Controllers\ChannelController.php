<?php

namespace Modules\Region\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Modules\Region\Models\Channel;
use Modules\Region\Services\ChannelService;
use Modules\Region\Enums\ChannelErrorCode;

/**
 * 频道管理控制器
 */
class ChannelController extends Controller
{
    protected $channelService;

    public function __construct(ChannelService $channelService)
    {
        $this->channelService = $channelService;
    }

    /**
     * 获取频道列表
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        try {
            // 获取分页参数，默认每页20条
            $page = (int) $request->input('page', 1);
            $limit = (int) $request->input('limit', 20);
            $name = $request->input('name', '');
            $status = $request->input('status', '');
            $orderBy = $request->input('order_by', 'sort'); // 默认按排序字段
            $orderDir = $request->input('order_dir', 'asc'); // 默认升序

            // 构建查询参数
            $params = [
                'name' => $name,
                'status' => $status,
                'sort' => $orderBy,
                'order' => $orderDir,
                'per_page' => $limit,
                'page' => $page,
            ];

            // 获取频道列表
            $channels = $this->channelService->getChannelList($params);

            // 返回统一格式
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.success'),
                'data' => [
                    'total' => $channels->total(),
                    'page' => $channels->currentPage(),
                    'limit' => $channels->perPage(),
                    'items' => $channels->getCollection()->map(function ($channel) {
                        $data = $channel->toArray();
                        // 只返回关联区域的数量
                        $data['region_num'] = $channel->regions->count();
                        // 移除regions数组，只保留数量
                        unset($data['regions']);
                        return $data;
                    })->toArray(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_LIST_FAILED->value,
                'message' => Lang::get('Region::region.channel_list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 创建频道
     * @param Request $request
     * @return array
     */
    public function store(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'required|string|max:100|unique:channel,name,NULL,id,deleted_at,NULL',
                'description' => 'nullable|string',
                'status' => 'integer|in:0,1',
                'sort' => 'nullable|integer|min:0|max:9999',
                'url' => 'nullable|string|max:500',
                'cover_img' => 'nullable|string|max:500',
                'region_ids' => 'nullable|array',
                'region_ids.*' => 'integer|exists:regions,id'
            ], [
                'name.required' => Lang::get('Region::region.name_required'),
                'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
                'name.unique' => Lang::get('Region::region.channel_already_exists'),
                'status.in' => Lang::get('Region::region.status_invalid'),
                'sort.integer' => Lang::get('Region::region.sort_invalid'),
                'url.max' => Lang::get('Region::region.url_invalid'),
                'cover_img.max' => Lang::get('Region::region.cover_img_invalid'),
                'region_ids.*.integer' => Lang::get('Region::region.region_ids_invalid'),
                'region_ids.*.exists' => Lang::get('Region::region.region_not_found'),
            ]);

            $validated['created_by'] = 1;
            $validated['updated_by'] = 1;

            // 记录请求日志
            \Log::info('创建频道请求', [
                'request_data' => $request->all(),
                'validated_data' => $validated,
                'user_id' => 1
            ]);

            // 创建频道
            $channel = $this->channelService->createChannel($validated);

            // 记录成功日志
            \Log::info('频道创建成功', [
                'channel_id' => $channel->id,
                'channel_name' => $channel->name
            ]);

            // 返回创建的数据
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.create_success'),
                'data' => $channel->toArray()
            ];

        } catch (\Illuminate\Validation\ValidationException $e) {
            // 记录验证错误
            \Log::error('频道创建验证失败', [
                'errors' => $e->errors(),
                'request_data' => $request->all(),
                'timestamp' => now()
            ]);
            
            // 返回详细的验证错误信息
            return [
                'code' => ChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
            
        } catch (\Exception $e) {
            // 记录其他错误
            \Log::error('频道创建失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'timestamp' => now()
            ]);
            
            return [
                'code' => ChannelErrorCode::CHANNEL_CREATE_FAILED->value,
                'message' => Lang::get('Region::region.create_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取单个频道
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        try {
            // 查询频道
            $channel = $this->channelService->getChannelById($id);

            if (!$channel) {
                return [
                    'code' => ChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.channel_not_found'),
                    'data' => null
                ];
            }

            // 返回详情
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.success'),
                'data' => $channel->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_QUERY_FAILED->value,
                'message' => Lang::get('Region::region.channel_query_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新频道
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'required|string|max:100|unique:channel,name,' . $id . ',id,deleted_at,NULL',
                'description' => 'nullable|string',
                'status' => 'integer|in:0,1',
                'sort' => 'nullable|integer|min:0|max:9999',
                'url' => 'nullable|string|max:500',
                'cover_img' => 'nullable|string|max:500',
                'region_ids' => 'nullable|array',
                'region_ids.*' => 'integer|exists:regions,id'
            ], [
                'name.required' => Lang::get('Region::region.name_required'),
                'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
                'name.unique' => Lang::get('Region::region.channel_already_exists'),
                'status.in' => Lang::get('Region::region.status_invalid'),
                'sort.integer' => Lang::get('Region::region.sort_invalid'),
                'url.max' => Lang::get('Region::region.url_invalid'),
                'cover_img.max' => Lang::get('Region::region.cover_img_invalid'),
                'region_ids.*.integer' => Lang::get('Region::region.region_ids_invalid'),
                'region_ids.*.exists' => Lang::get('Region::region.region_not_found'),
            ]);

            $validated['updated_by'] = 1;

            // 更新频道
            $channel = $this->channelService->updateChannel($id, $validated);

            if (!$channel) {
                return [
                    'code' => ChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.channel_not_found'),
                    'data' => null
                ];
            }

            // 返回更新后的数据
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.update_success'),
                'data' => $channel->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'errors' => $e->errors()
            ];
        } catch (\Exception $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_UPDATE_FAILED->value,
                'message' => Lang::get('Region::region.update_failed'),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 删除频道（软删除）
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        try {
            // 查询频道
            $channel = Channel::find($id);

            if (!$channel) {
                return [
                    'code' => ChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.channel_not_found'),
                    'data' => null
                ];
            }

            // 软删除
            $result = $this->channelService->deleteChannel($id);

            if (!$result) {
                return [
                    'code' => ChannelErrorCode::CHANNEL_DELETE_FAILED->value,
                    'message' => Lang::get('Region::region.delete_failed'),
                    'data' => null
                ];
            }

            // 返回删除结果
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.delete_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('Region::region.delete_success'),
                    'affected_count' => 1
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_DELETE_FAILED->value,
                'message' => Lang::get('Region::region.delete_failed'),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新频道状态
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function updateStatus(Request $request, int $id): array
    {
        // 查询频道
        $channel = Channel::find($id);

        if (!$channel) {
            return [];
        }

        $validated = $request->validate([
            'status' => 'required|integer|in:0,1'
        ]);

        $validated['updated_by'] = 1;

        // 更新状态
        $success = $this->channelService->updateChannelStatus($id, $validated['status'], $validated['updated_by']);

        if (!$success) {
            return [];
        }

        return $channel->fresh()->toArray();
    }

    /**
     * 删除频道与区域的关联关系
     * @param int $channelId
     * @param int $regionId
     * @return array
     */
    public function deleteRegionAssociation(int $channelId, int $regionId): array
    {
        $success = $this->channelService->deleteRegionAssociation($channelId, $regionId);

        return [
            'success' => $success,
            'message' => $success ? '删除关联成功' : '删除关联失败'
        ];
    }

    /**
     * 获取频道关联的区域列表
     * @param int $id
     * @return array
     */
    public function getChannelRegions(int $id): array
    {
        $regions = $this->channelService->getChannelRegions($id);

        return [
            'channel_id' => $id,
            'regions' => $regions
        ];
    }

    /**
     * 批量操作
     * @param Request $request
     * @return array
     */
    public function batchAction(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'action' => 'required|string|in:delete,enable,disable',
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:channel,id'
            ], [
                'action.required' => Lang::get('Region::region.action_required'),
                'action.in' => Lang::get('Region::region.action_invalid'),
                'ids.required' => Lang::get('Region::region.ids_required'),
                'ids.array' => Lang::get('Region::region.ids_invalid'),
                'ids.*.integer' => Lang::get('Region::region.id_invalid'),
                'ids.*.exists' => Lang::get('Region::region.channel_not_found'),
            ]);

            $action = $validated['action'];
            $ids = $validated['ids'];
            $operatorId = 1; // 固定操作人ID

            // 查询要操作的频道
            $channels = Channel::whereIn('id', $ids)->get();
            
            if ($channels->isEmpty()) {
                return [
                    'code' => ChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.channel_not_found'),
                    'data' => [
                        'success' => false,
                        'message' => Lang::get('Region::region.channel_not_found'),
                        'affected_count' => 0
                    ]
                ];
            }

            // 执行批量操作
            $result = $this->channelService->batchAction($action, $ids, $operatorId);

            // 根据操作类型返回相应的消息
            $message = '';
            switch ($action) {
                case 'delete':
                    $message = Lang::get('Region::region.batch_delete_success', ['count' => $result['success_count'] ?? 0]);
                    break;
                case 'enable':
                    $message = Lang::get('Region::region.batch_enable_success', ['count' => $result['success_count'] ?? 0]);
                    break;
                case 'disable':
                    $message = Lang::get('Region::region.batch_disable_success', ['count' => $result['success_count'] ?? 0]);
                    break;
                default:
                    $message = Lang::get('Region::region.batch_success');
            }

            // 返回批量操作结果
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.batch_success'),
                'data' => [
                    'success' => true,
                    'message' => $message,
                    'affected_count' => $result['success_count'] ?? 0,
                    'action' => $action
                ]
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'errors' => $e->errors()
            ];
        } catch (\Exception $e) {
            return [
                'code' => ChannelErrorCode::CHANNEL_BATCH_ACTION_FAILED->value,
                'message' => Lang::get('Region::region.batch_failed'),
                'error' => $e->getMessage()
            ];
        }
    }
} 