import { Node, mergeAttributes, nodeInputRule } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

export interface HeadingBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    headingBlock: {
      /**
       * 添加标题块
       */
      setHeadingBlock: (options?: { level?: 1 | 2 | 3 | 4 | 5 | 6, text?: string }) => ReturnType
    }
  }
}

export const HeadingBlock = Node.create<HeadingBlockOptions>({
  name: 'headingBlock',
  group: 'block',
  content: 'inline*',
  defining: true,
  isolating: true,
  draggable: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'bootstrap-heading'
      }
    }
  },

  addAttributes() {
    return {
      level: {
        default: 1,
        parseHTML: element => {
          const headingTag = element.querySelector('h1, h2, h3, h4, h5, h6')
          if (headingTag) {
            const tag = headingTag.tagName.toLowerCase()
            const level = parseInt(tag.substring(1), 10)
            return level || 1
          }
          
          const level = element.getAttribute('data-heading-level')
          return level ? parseInt(level, 10) : 1
        },
        renderHTML: attributes => {
          return {
            'data-heading-level': attributes.level
          }
        }
      },
      align: {
        default: 'left',
        parseHTML: element => {
          const colEl = element.querySelector('.row > [class*="col-"]')
          if (colEl) {
            if (colEl.classList.contains('text-center')) return 'center'
            if (colEl.classList.contains('text-right')) return 'right'
          }
          return element.getAttribute('data-align') || 'left'
        },
        renderHTML: attributes => {
          return {
            'data-align': attributes.align
          }
        }
      },
      class: {
        default: 'bootstrap-heading',
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return { class: 'bootstrap-heading' }
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="heading"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }

          return {
            class: element.getAttribute('class') || 'bootstrap-heading'
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const level = HTMLAttributes['data-heading-level'] || 1
    const align = HTMLAttributes['data-align'] || 'left'
    const alignClass = align !== 'left' ? `text-${align}` : ''

    // 合并属性，确保data-bs-component="heading"属性始终存在
    const finalAttributes = mergeAttributes(
      { 
        'data-bs-component': 'heading',
        'class': HTMLAttributes.class || 'bootstrap-heading'
      },
      this.options.HTMLAttributes,
      HTMLAttributes
    )
    
    // 确保data-bs-component不被其他属性覆盖
    finalAttributes['data-bs-component'] = 'heading'

    return [
      'div',
      finalAttributes,
      [
        'div',
        { class: 'container-fluid p-0' },
        [
          'div',
          { class: 'row justify-content-center' },
          [
            'div',
            { class: `col-12 col-md-10 col-lg-8 ${alignClass}` },
            [`h${level}`, { class: 'heading-title' }, 0]
          ]
        ]
      ]
    ]
  },

  addCommands() {
    return {
      setHeadingBlock: (options = {}) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            level: options.level || 1
          },
          content: options.text ? [{ type: 'text', text: options.text }] : []
        })
      }
    }
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: /^#+\s(.+)$/,
        type: this.type,
        getAttributes: match => {
          const level = match[0].match(/^#+/)?.[0].length || 1
          return { 
            level: Math.min(level, 6),
            content: match[1]
          }
        }
      })
    ]
  }
}) 