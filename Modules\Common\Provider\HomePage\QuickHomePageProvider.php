<?php

namespace Modules\Common\Provider\HomePage;

class QuickHomePageProvider extends AbstractHomePageProvider
{
    protected $type;
    protected $title;
    protected $action;

    public static function make($title, $action, $type = [self::TYPE_PC, self::TYPE_MOBILE]): static
    {
        $o = new static();
        $o->type = $type;
        $o->title = $title;
        $o->action = $action;
        return $o;
    }

    public function type(): string
    {
        return $this->type;
    }


    public function title()
    {
        return $this->title;
    }

    public function action()
    {
        return $this->action;
    }


}
