<?php

namespace Modules\Common\Provider\Notifier;

use Illuminate\Support\Facades\Log;
use Bingo\Core\Util\SerializeUtil;

class DefaultNotifierProvider extends AbstractNotifierProvider
{
    public function name(): null
    {
        return 'Default';
    }

    public function title(): null
    {
        return '默认日志';
    }

    public function notify($biz, $title, $content, $param = []): void
    {
        Log::info(sprintf('Common.DefaultNotifierProvider - %s - %s - %s', $biz, $title, SerializeUtil::jsonEncode($content, JSON_UNESCAPED_UNICODE)));
    }
}
