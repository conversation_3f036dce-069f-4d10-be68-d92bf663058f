/**
 * Stagewise 开发工具配置
 * 仅在开发环境下启用
 */

// Stagewise 配置
export const stagewiseConfig = {
  plugins: []
}

/**
 * 初始化 Stagewise 工具栏
 * 仅在开发环境下运行
 */
export async function initStagewise() {
  // 临时禁用 stagewise 工具栏以避免连接错误
  console.log('⚠️ Stagewise 工具栏已被禁用以避免连接错误')
  return
  
  // 仅在开发环境下启用
  if (import.meta.env.MODE !== 'development') {
    return
  }

  try {
    // 动态导入 stagewise 工具栏
    const { StagewiseToolbar } = await import('@stagewise/toolbar-vue')
    
    // 创建工具栏容器
    const toolbarContainer = document.createElement('div')
    toolbarContainer.id = 'stagewise-toolbar-container'
    document.body.appendChild(toolbarContainer)

    // 创建 Vue 应用实例用于工具栏
    const { createApp } = await import('vue')
    const toolbarApp = createApp(StagewiseToolbar, {
      config: stagewiseConfig
    })

    // 挂载工具栏
    toolbarApp.mount(toolbarContainer)

    console.log('🚀 Stagewise 开发工具已启用')
  } catch (error) {
    console.warn('⚠️ Stagewise 工具栏初始化失败:', error)
  }
} 