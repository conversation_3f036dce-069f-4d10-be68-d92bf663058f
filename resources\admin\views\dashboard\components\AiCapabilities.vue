<template>
  <div class="ai-capabilities">
    <div class="capabilities-content">
      <div class="robot-image">
        <img :src="$asset('Dashboard/Asset/chatbot.png')" alt="AI Robot" />
      </div>
      <!-- 关闭按钮 -->
      <el-button type="text" link class="close-btn" @click="handleClose">
        <el-icon><Close /></el-icon>
      </el-button>
      <div class="text-content">
        <template v-if="loading">
          <div class="skeleton-content">
            <el-skeleton animated>
              <template #template>
                <div class="skeleton-item title-skeleton">
                  <el-skeleton-item variant="text" style="width: 60%; height: 32px;" />
                </div>
                <div class="skeleton-item desc-skeleton">
                  <el-skeleton-item variant="text" style="width: 90%; height: 20px; margin: 8px 0;" />
                  <el-skeleton-item variant="text" style="width: 80%; height: 20px;" />
                </div>
              </template>
            </el-skeleton>
          </div>
        </template>
        <template v-else>
          <div class="title">{{ modules.title }}</div>
          <div class="description">
            <div v-html="formattedDescription"></div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { Close } from '@element-plus/icons-vue'
import http from '/admin/support/http'

// 定义 emit 事件
const emit = defineEmits<{
  (e: 'close'): void
}>()

const modules = ref({
  title: '',
  description: ''
})
const loading = ref(true)

// 格式化description，在第一个句号后换行
const formattedDescription = computed(() => {
  const description = modules.value.description
  if (!description) return ''
  
  // 找到第一个句号的位置
  const firstPeriodIndex = description.indexOf('。')
  if (firstPeriodIndex === -1) {
    return description
  }
  
  // 在第一个句号后插入换行符
  const beforePeriod = description.substring(0, firstPeriodIndex + 1)
  const afterPeriod = description.substring(firstPeriodIndex + 1)
  
  return `${beforePeriod}<br/>${afterPeriod}`
})

// 处理关闭事件
const handleClose = () => {
  emit('close')
}

const getModules = async () => {
  try {
    const res = await http.get('/dashboard/aiCapabilities/data')
    modules.value = res.data.data
  } catch (error) {
    console.error('获取AI能力数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getModules()
})
</script>

<style lang="scss" scoped>
.ai-capabilities {
  width: 100%;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 10px;
    top: 35px;
    border-radius: 20px;
    // background: linear-gradient(to bottom, #007EE5, #18559A);
    background: transparent linear-gradient(180deg, #007EE5 0%, #18559A 100%) 0% 0% no-repeat padding-box;
  }

  .capabilities-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    padding: 0 94px 0 40px;

    .close-btn {
      position: absolute;
      top: 48px;
      right: 18px;
      color: rgba(255, 255, 255, 1);
      font-size: 24px;
      font-weight: bold;
      z-index: 2;

      &:hover {
        color: rgba(255, 255, 255, 1);
      }
    }

    .robot-image {
      flex-shrink: 0;
      width: 155px;

      img {
        width: 100%;
      }
    }

    .text-content {
      padding-left: 30px;
      flex-grow: 1;
      color: white;

      @media screen and (max-width: 1300px) {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .skeleton-content {
        .skeleton-item {
          :deep(.el-skeleton__text) {
            background: rgba(255, 255, 255, 0.2);
          }
        }
        
        .title-skeleton {
          margin-bottom: 24px;
        }
        
        .desc-skeleton {
          margin-bottom: 16px;
        }
      }

      .title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 24px;
        line-height: 1.2;
      }

      .description {
        font-size: 16px;
        line-height: 1.34375;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .capabilities-content {
      flex-direction: column;
      text-align: center;
      gap: 16px;

      .robot-image {
        width: 80px;
        height: 80px;
      }

      .text-content {
        .title {
          font-size: 20px;
        }

        .description {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
