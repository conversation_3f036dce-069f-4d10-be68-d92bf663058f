<template>
  <div class="bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box">
        <div class="multi-factor-auth">
          <template v-if="!showOtpSetup">
            <div class="auth-list">
              <div v-for="(item, index) in authMethods" :key="index" class="auth-item">
                <div class="auth-icon" :style="{ backgroundColor: item.color }">
                  <el-icon><component :is="item.icon" /></el-icon>
                </div>
                <div class="auth-content">
                  <div class="auth-title">{{ t(`dashboard.mfa.methods.${item.type}.title`) }}</div>
                  <div class="auth-description">{{ t(`dashboard.mfa.methods.${item.type}.description`) }}</div>
                </div>
                <div class="auth-action">
                  <el-button 
                    link 
                    :type="item.status ? 'danger' : 'primary'"
                    @click="item.action"
                  >
                    {{ item.status ? t('dashboard.mfa.button.unbind') : t('dashboard.mfa.button.bind') }}
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 短信验证码弹窗 -->
            <el-dialog 
              v-model="smsDialogVisible" 
              :title="t('dashboard.mfa.dialog.sms.title')" 
              width="400px" 
              custom-class="sms-dialog"
            >
              <el-form :model="smsForm" label-position="top">
                <el-form-item :label="t('dashboard.mfa.dialog.sms.phone')">
                  <el-input v-model="smsForm.phone" :placeholder="t('dashboard.mfa.dialog.sms.phonePlaceholder')">
                    <template #prepend>
                      <el-select v-model="smsForm.countryCode" placeholder="+86" style="width: 100px">
                        <el-option label="+86" value="+86"></el-option>
                        <el-option label="+852" value="+852"></el-option>
                      </el-select>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :label="t('dashboard.mfa.dialog.sms.code')">
                  <div class="verification-code-input">
                    <el-input 
                      v-model="smsForm.code" 
                      :placeholder="t('dashboard.mfa.dialog.sms.codePlaceholder')"
                    >
                      <template #append>
                        <el-button 
                          :disabled="isCountingDown" 
                          @click="sendVerificationCode" 
                          :loading="isCountingDown"
                        >
                          {{ isCountingDown ? 
                            t('dashboard.mfa.button.resendCode', { seconds: countdown }) : 
                            t('dashboard.mfa.button.sendCode') 
                          }}
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </el-form-item>
              </el-form>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="smsDialogVisible = false">
                    {{ t('dashboard.mfa.button.cancel') }}
                  </el-button>
                  <el-button type="primary" @click="bindPhone">
                    {{ t('dashboard.mfa.button.bind') }}
                  </el-button>
                </span>
              </template>
            </el-dialog>

            <!-- 邮箱验证弹窗 -->
            <el-dialog 
              v-model="emailDialogVisible" 
              :title="t('dashboard.mfa.dialog.email.title')" 
              width="400px" 
              custom-class="email-dialog"
            >
              <el-form :model="emailForm" label-position="top">
                <el-form-item label="输入邮箱账号">
                  <el-input v-model="emailForm.email" placeholder="请输入邮箱账号" />
                </el-form-item>
                <el-form-item label="邮箱验证码">
                  <div class="verification-code-input">
                    <el-input v-model="emailForm.code" placeholder="请输入6位验证码">
                      <template #append>
                        <el-button :disabled="isEmailCountingDown" @click="sendEmailVerificationCode" :loading="isEmailCountingDown">
                          {{ emailCountdownText }}
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </el-form-item>
              </el-form>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="emailDialogVisible = false">取消</el-button>
                  <el-button type="primary" @click="bindEmail">绑定</el-button>
                </span>
              </template>
            </el-dialog>

            <!-- Passkey 验证弹窗 -->
            <el-dialog 
              v-model="passkeyDialogVisible" 
              :title="t('dashboard.mfa.dialog.passkey.title')" 
              width="600px" 
              custom-class="passkey-dialog"
            >
              <div class="passkey-content">
                <img src="https://files.authing.co/authing-user-portal/passkey/passkey_empty_one.png" alt="Passkey" class="passkey-image" />
                <h3>{{ t('dashboard.mfa.dialog.passkey.noData') }}</h3>
                <p>{{ t('dashboard.mfa.dialog.passkey.description') }}</p>
                <el-button type="primary" @click="createPasskey">
                  {{ t('dashboard.mfa.dialog.passkey.create') }}
                </el-button>
              </div>
            </el-dialog>
          </template>

          <OtpAuth v-else @finish-setup="handleOtpFinish" @go-back="handleOtpGoBack" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, markRaw, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import OtpAuth from './OtpAuth.vue'
import { ChatDotRound, Message, Key, Lock } from '@element-plus/icons-vue'
import { UserCenterService } from '../application/UserCenterService'
import { ElMessage } from 'element-plus'
import { useUserStore } from '/admin/stores/modules/user/userStore'
import http from '/admin/support/http'

const { t } = useI18n()
const userStore = useUserStore()
const userCenterService = new UserCenterService()

interface MfaConfig {
  enableMfaPersonal: boolean
  enablePhoneEmailBindMfa: boolean
  mfaPersonalDetailList: Array<{
    authenticatorType: string
    enable: boolean
    enableConfig: boolean
  }>
}

const mfaConfig = ref<MfaConfig>({
  enableMfaPersonal: false,
  enablePhoneEmailBindMfa: false,
  mfaPersonalDetailList: [],
})

const authMethods = ref<any>([
  {
    icon: markRaw(ChatDotRound),
    color: '#409EFF',
    title: '短信验证码',
    description: '使用短信形式接收验证码认证登录',
    status: false,
    action: handleSmsAuth,
    type: 'sms',
  },
  {
    icon: markRaw(Message),
    color: '#67C23A',
    title: '电子邮箱验证',
    description: '使用邮件形式接收验证码认证登录',
    status: false,
    action: handleEmailAuth,
    type: 'email',
  },
  {
    icon: markRaw(Key),
    color: '#E6A23C',
    title: 'OTP 口令验证',
    description: '使用 OTP 一次性口令认证登录',
    status: false,
    action: handleOtpAuth,
    type: 'otp',
  },
])

const showOtpSetup = ref(false)
const smsDialogVisible = ref(false)
const emailDialogVisible = ref(false)
const passkeyDialogVisible = ref(false)

const app_code = ref<any>(localStorage.getItem('app_code'))

const smsForm = reactive({
  countryCode: '+86',
  phone: '',
  code: '',
})
const isCountingDown = ref(false)
const countdown = ref(60)
const countdownText = ref('发送验证码')

const emailForm = reactive({
  email: '',
  code: '',
})
const isEmailCountingDown = ref(false)
const emailCountdown = ref(60)
const emailCountdownText = ref('发送验证码')

const getMFA = async () => {
  try {
    const res = await http.get('/iam/mfa/config')
    mfaConfig.value = res.data.data
    updateAuthMethods()
  } catch (error) {
    console.error('获取MFA配置失败', error)
  }
}

const updateAuthMethods = () => {
  authMethods.value = mfaConfig.value.mfaPersonalDetailList
    .map(item => {
      let icon, color, title, description, action
      switch (item.authenticatorType) {
        case 'SMS':
          icon = markRaw(ChatDotRound)
          color = '#409EFF'
          title = '短信验证码'
          description = '使用短信形式接收验证码认证登录'
          action = item.enable ? handleSmsUnbind : handleSmsAuth
          break
        case 'EMAIL':
          icon = markRaw(Message)
          color = '#67C23A'
          title = '电子邮箱验证'
          description = '使用邮件形式接收验证码认证登录'
          action = item.enable ? handleEmailUnbind : handleEmailAuth
          break
        case 'TOTP':
          icon = markRaw(Key)
          color = '#E6A23C'
          title = 'OTP 口令验证'
          description = '使用 OTP 一次性口令认证登录'
          action = handleOtpAuth
          break
        default:
          return null
      }
      return {
        icon,
        color,
        title,
        description,
        status: item.enable,
        action,
      }
    })
    .filter(Boolean)
}

function handleSmsAuth() {
  smsDialogVisible.value = true
}

function handleEmailAuth() {
  emailDialogVisible.value = true
}

function handleOtpAuth() {
  showOtpSetup.value = true
}

function handlePasskeyAuth() {
  passkeyDialogVisible.value = true
}

const sendVerificationCode = async () => {
  if (isCountingDown.value) return

  if (!smsForm.phone) {
    ElMessage.warning(t('dashboard.mfa.message.phoneRequired'))
    return
  }

  try {
    isCountingDown.value = true
    await userCenterService.sendSmsCode(smsForm.phone, 'CHANNEL_BIND_PHONE', smsForm.countryCode, app_code.value)
    ElMessage.success(t('dashboard.mfa.message.codeSent'))
    countdown.value = 60
    countdownText.value = `${countdown.value}s后重新发送`
    const timer = setInterval(() => {
      countdown.value--
      countdownText.value = `${countdown.value}s后重新发送`
      if (countdown.value <= 0) {
        clearInterval(timer)
        isCountingDown.value = false
        countdownText.value = '发送验证码'
      }
    }, 1000)
  } catch (error) {
    isCountingDown.value = false
  }
}

async function bindPhone() {
  if (!smsForm.phone || !smsForm.code) {
    ElMessage.warning(t('dashboard.mfa.message.codeRequired'))
    return
  }

  const res: any = await userCenterService.bindPhone(smsForm.phone, smsForm.code, smsForm.countryCode)
  if (res.data.code === 200) {
    smsDialogVisible.value = false
    smsForm.countryCode = '+86'
    smsForm.phone = ''
    smsForm.code = ''
    getMFA()
    ElMessage.success(t('dashboard.mfa.message.bindSuccess', { type: t('dashboard.mfa.methods.sms.title') }))
  } else {
    ElMessage.error(t('dashboard.mfa.message.bindFailed', { type: t('dashboard.mfa.methods.sms.title') }))
  }
}

const sendEmailVerificationCode = async () => {
  if (isEmailCountingDown.value) return

  if (!emailForm.email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  try {
    isEmailCountingDown.value = true
    await userCenterService.sendEmailCode(emailForm.email, 'CHANNEL_BIND_EMAIL', app_code.value)
    emailCountdown.value = 60
    emailCountdownText.value = `${emailCountdown.value}s后重新发送`
    const timer = setInterval(() => {
      emailCountdown.value--
      emailCountdownText.value = `${emailCountdown.value}s后重新发送`
      if (emailCountdown.value <= 0) {
        clearInterval(timer)
        isEmailCountingDown.value = false
        emailCountdownText.value = '发送验证码'
      }
    }, 1000)
  } catch (error) {
    console.error('发送邮箱验证码失败', error)
    isEmailCountingDown.value = false
  }
}

async function bindEmail() {
  if (!emailForm.email || !emailForm.code) {
    ElMessage.warning('请填写邮箱地址和验证码')
    return
  }

  const res: any = await userCenterService.bindEmail(emailForm.email, emailForm.code)
  if (res.data.code === 200) {
    emailDialogVisible.value = false
    emailForm.email = ''
    emailForm.code = ''
    getMFA() // 重新获取MFA配置
    ElMessage.success('邮箱绑定成功')
  } else {
    ElMessage.error(res.data.message || '邮箱绑定失败')
  }
}

function handleOtpFinish() {
  showOtpSetup.value = false
  getMFA() // 重新获取MFA配置
}

function handleOtpGoBack() {
  showOtpSetup.value = false
}

function createPasskey() {
  passkeyDialogVisible.value = false
  // 这里应该添加创建Passkey的逻辑
  ElMessage.success('Passkey创建成功')
  getMFA() // 重新获取MFA配置
}

// 添加新的解绑函数
async function handleSmsUnbind() {
  try {
    await userCenterService.unbindPhone(smsForm.code)
    ElMessage.success('手机解绑成功')
    getMFA() // 重新获取MFA配置
  } catch (error) {
    ElMessage.error('手机解绑失败')
  }
}

async function handleEmailUnbind() {
  try {
    await userCenterService.unbindEmail(emailForm.code)
    ElMessage.success('邮箱解绑成功')
    getMFA() // 重新获取MFA配置
  } catch (error) {
    ElMessage.error('邮箱解绑失败')
  }
}

// 在组件挂载时获取MFA配置
onMounted(() => {
  getMFA()
})
</script>

<style lang="scss" scoped>
.multi-factor-auth {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: normal;
  }
}

.auth-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.auth-item {
  display: flex;
  align-items: center;
  padding: 24px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.auth-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;

  .el-icon {
    font-size: 24px;
    color: #fff;
  }
}

.auth-content {
  flex: 1;

  .auth-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .auth-description {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.auth-action {
  margin-left: 16px;

  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      margin: 0;
    }
  }
}

// 弹窗样式
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px 25px;
  }

  .el-form-item__label {
    padding-bottom: 8px;
  }
}

.verification-code-input {
  :deep(.el-input-group__append) {
    padding: 0;
    
    button {
      border: none;
      background: none;
      color: var(--el-color-primary);
      padding: 0 15px;
      width: 150px;
      transition: all 0.3s;

      &:disabled {
        color: var(--el-text-color-disabled);
        cursor: not-allowed;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// Passkey 弹窗样式
.passkey-content {
  text-align: center;
  padding: 20px 0;

  .passkey-image {
    width: 60%;
    max-width: 300px;
    height: auto;
    margin: 20px auto;
  }

  h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
  }

  p {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 24px;
    line-height: 1.5;
  }
}
</style>
