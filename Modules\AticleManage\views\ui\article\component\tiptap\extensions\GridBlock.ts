import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import GridBlockComponent from '../components/GridBlockComponent.vue'

export interface GridBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    gridBlock: {
      /**
       * 插入网格布局区块
       */
      insertGridBlock: (options: { layout: string }) => ReturnType
    }
  }
}

export const GridBlock = Node.create<GridBlockOptions>({
  name: 'gridBlock',
  
  group: 'block',
  
  content: 'block+',
  
  draggable: true,
  
  isolating: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'tiptap-grid-block',
      },
    }
  },

  addAttributes() {
    return {
      layout: {
        default: '1', // 默认是单列布局
        parseHTML: element => element.getAttribute('data-layout'),
        renderHTML: attributes => {
          return {
            'data-layout': attributes.layout,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="grid-block"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return {}
          const el = element as HTMLElement
          return {
            layout: el.getAttribute('data-layout') || '1',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
      'data-type': 'grid-block',
    }), 0]
  },

  addCommands() {
    return {
      insertGridBlock: (options) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            layout: options.layout,
          },
          content: Array(options.layout.split(':').length).fill({
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: '添加内容...',
              },
            ],
          }),
        })
      },
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(GridBlockComponent)
  },
}) 