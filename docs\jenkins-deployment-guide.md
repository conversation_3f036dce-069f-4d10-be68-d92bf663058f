# Jenkins 部署配置文档 - 新手完整指南
<!-- 文档编码声明 -->
<meta charset="utf-8">
## 📋 目录
1. [快速开始](#快速开始)
2. [项目概述](#项目概述)
3. [环境准备](#环境准备)
4. [Jenkins 安装与配置](#jenkins-安装与配置)
5. [SSH 密钥配置](#ssh-密钥配置)
6. [创建部署任务](#创建部署任务)
7. [部署服务器配置](#部署服务器配置)
8. [构建流程说明](#构建流程说明)
9. [执行部署](#执行部署)
10. [故障排除](#故障排除)
11. [部署流程时序图](#部署流程时序图)
12. [流水线脚本测试教程](#流水线脚本测试教程)

## ⚡ 快速开始
如果您已经有Jenkins环境，可以按照以下步骤快速配置部署：

### 1. 准备工作（5分钟）
```bash
# 在Jenkins服务器上生成SSH密钥
sudo su - jenkins
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
# 将公钥复制到部署服务器
ssh-copy-id <EMAIL>
```

### 2. 配置Jenkins任务（5分钟）
- 创建新的流水线任务
- 配置Git仓库和SSH凭据
- 在Jenkins界面中直接配置流水线脚本
- 设置构建触发器

### 4. 配置部署服务器（5分钟）
```bash
# 创建部署脚本
sudo nano /home/<USER>/deploy.sh
# 复制本文档中的部署脚本内容
chmod +x /home/<USER>/deploy.sh
```

### 5. 执行首次部署（1分钟）
- 在Jenkins中点击"立即构建"
- 监控构建过程
- 验证部署结果

**总用时：约15分钟** ⏱️
---

## 📖 项目概述

## 🕒 部署流程时序图

```mermaid
sequenceDiagram
    participant 开发者
    participant Git仓库
    participant Jenkins
    participant 部署服务器
    
    开发者->>Git仓库: 推送代码变更
    Git仓库->>Jenkins: 触发Webhook
    Jenkins->>Jenkins: 启动构建任务
    Jenkins->>Git仓库: 拉取最新代码
    Jenkins->>Jenkins: 安装依赖
    Jenkins->>Jenkins: 执行构建
    Jenkins->>部署服务器: 通过SSH传输构建产物
    部署服务器->>部署服务器: 执行部署脚本
    部署服务器-->>Jenkins: 返回部署结果
    Jenkins-->>开发者: 发送构建/部署通知
```

## 🧪 流水线脚本测试教程

### 1. 创建测试流水线

1. 登录Jenkins管理界面
2. 点击"新建任务"
3. 输入任务名称：`bwms-test-pipeline`
4. 选择"流水线"类型
5. 点击"确定"

### 2. 配置基础参数

在任务配置页面设置：

- **描述**：`BWMS测试流水线`
- **丢弃旧的构建**：勾选
  - 保持构建的天数：7
  - 保持构建的最大个数：10

### 3. 添加测试脚本

在流水线部分选择`Pipeline script`，粘贴以下测试脚本：

```groovy
pipeline {
    agent any
    
    stages {
        stage('测试阶段1') {
            steps {
                echo '这是第一个测试阶段'
                sh 'echo 当前目录: $PWD'
            }
        }
        
        stage('测试阶段2') {
            steps {
                echo '这是第二个测试阶段'
                sh 'ls -la'
            }
        }
    }
    
    post {
        always {
            echo '构建完成，清理工作区'
            cleanWs()
        }
    }
}
```

### 4. 保存并执行测试

1. 点击"保存"按钮
2. 点击"立即构建"
3. 观察控制台输出，验证各阶段执行情况

### 5. 逐步扩展测试

1. 添加Git仓库配置
2. 添加依赖安装步骤
3. 添加构建步骤
4. 添加部署步骤
5. 每次添加后执行测试，验证功能

### 6. 调试技巧

- 使用`echo`命令输出变量值
- 检查各阶段执行时间
- 查看控制台输出的错误信息
- 使用`sh 'pwd'`确认工作目录
- 添加`timeout`限制阶段执行时间

BWMS 是一个基于 Laravel + Vue.js 的后台管理系统，包含：
- **后端**：Laravel 11.x + PHP 8.2
- **前端**：Vue 3 + TypeScript + Vite
- **数据库**：MySQL
- **部署方式**：通过 Jenkins 自动化部署到远程服器

## 🛠 环境准备

### Jenkins 服务器环境要求
- **操作系统**：Ubuntu 20.04+ / CentOS 7+ / macOS
- **Java**：OpenJDK 11 或 17
- **Jenkins**：2.400+
- **Node.js**：18+ (用于前端构建)
- **PHP**：8.2+ (可选，用于代码检查)
- **Git**：2.0+
- **内存**：至少 4GB RAM
- **磁盘空间**：至少 20GB

### 目标部署服务器环境要求
- **操作系统**：Ubuntu 20.04+ / CentOS 7+
- **Web服务器**：Nginx 1.18+
- **PHP**：8.2+ 及相关扩展
- **数据库**：MySQL 8.0+ / MariaDB 10.5+
- **内存**：至少 2GB RAM
- **磁盘空间**：至少 10GB

## 🚀 Jenkins 安装与配置

### 第一步：安装 Jenkins

#### Ubuntu/Debian 系统：
```bash
# 更新系统包
sudo apt update

# 安装 Java 11
sudo apt install openjdk-11-jdk -y

# 添加 Jenkins 官方仓库
wget -q -O - https://pkg.jenkins.io/debian-stable/jenkins.io.key | sudo apt-key add -
sudo sh -c 'echo deb https://pkg.jenkins.io/debian-stable binary/ > /etc/apt/sources.list.d/jenkins.list'

# 安装 Jenkins
sudo apt update
sudo apt install jenkins -y

# 启动 Jenkins 服务
sudo systemctl start jenkins
sudo systemctl enable jenkins

# 检查服务状态
sudo systemctl status jenkins
```

#### CentOS/RHEL 系统：
```bash
# 安装 Java 11
sudo yum install java-11-openjdk-devel -y

# 添加 Jenkins 仓库
sudo wget -O /etc/yum.repos.d/jenkins.repo https://pkg.jenkins.io/redhat-stable/jenkins.repo
sudo rpm --import https://pkg.jenkins.io/redhat-stable/jenkins.io.key

# 安装 Jenkins
sudo yum install jenkins -y

# 启动服务
sudo systemctl start jenkins
sudo systemctl enable jenkins
```

### 第二步：初始化 Jenkins

1. **访问 Jenkins Web 界面**
   ```
   https://jenkins.bingo-test.com/
   ```

2. **获取初始管理员密码**
   ```bash
   sudo cat /var/lib/jenkins/secrets/initialAdminPassword
   ```

3. **完成初始设置向导**
   - 输入初始管理员密码
   - 选择 "安装推荐的插件"
   - 创建第一个管理员用户
   - 配置 Jenkins URL

### 第三步：安装必需的插件

进入 **Jenkins 管理 → 插件管理 → 可选插件**，搜索并安装以下插件：

#### 必需插件列表：
```
✅ Git Plugin                    # Git 版本控制支持
✅ Pipeline Plugin               # 流水线支持
✅ NodeJS Plugin                 # Node.js 环境支持
✅ SSH Agent Plugin              # SSH 密钥管理
✅ Publish Over SSH Plugin       # SSH 发布支持
✅ Pipeline Stage View Plugin    # 流水线阶段视图
✅ Blue Ocean                    # 现代化界面（推荐）
✅ Workspace Cleanup Plugin      # 工作空间清理
✅ Build Timeout Plugin          # 构建超时控制
✅ Timestamper Plugin           # 构建日志时间戳
```

**安装步骤：**
1. 勾选需要的插件
2. 点击 "直接安装"
3. 等待安装完成
4. 重启 Jenkins：`sudo systemctl restart jenkins`

### 第四步：配置全局工具

#### 配置 Node.js
1. 进入 **Jenkins 管理 → 全局工具配置**
2. 找到 **NodeJS** 配置部分
3. 点击 **新增 NodeJS**
4. 配置如下：
   - **名称**：`20.18.0`（重要：这个名称须与Pipeline脚本中的一致）
   - **自动安装**：✅ 勾选
   - **版本**：选择 `NodeJS 20.18.0`
5. 点击 **保存**

**注意**：名称必须与Pipeline脚本中`tools`部分的`nodejs`值完全一致。

#### 配置 Git（如果需要）
1. 在 **Git** 配置部分
2. **Path to Git executable**：通常自动检测，如果有问题可以手动指定路径
   - Linux: `/usr/bin/git`
   - macOS: `/usr/local/bin/git`

## 🔐 SSH 密钥配置

### 第一步：生成 SSH 密钥对

在 Jenkins 服务器上生成 SSH 密钥：

```bash
# 切换到 jenkins 用户
sudo su - jenkins

# 生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 按提示操作：
# 1. 文件保存位置：直接回车使用默认位置 ~/.ssh/id_rsa
# 2. 密码：建议留空（直接回车），方便自动化部署
# 3. 确认密码：再次回车

# 查看生成的公钥
cat ~/.ssh/id_rsa.pub
```

### 第二步：配置目标服务器

将 Jenkins 的公钥添加到目标部署服务器：

```bash
# 在目标服务器上创建部署用户（如果还没有）
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG sudo deploy

# 切换到部署用户
sudo su - deploy

# 创建 .ssh 目录
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 将 Jenkins 的公钥添加到 authorized_keys
# 将上一步复制的公钥内容粘贴到这个文件中
nano ~/.ssh/authorized_keys

# 设置正确的权限
chmod 600 ~/.ssh/authorized_keys
```

### 第三步：在 Jenkins 中添加 SSH 凭据

1. 进入 **Jenkins 管理 → 凭据管理**
2. 点击 **全局凭据 (unrestricted)**
3. 点击 **添加凭据**
4. 配置如下：
   - **类型**：SSH Username with private key
   - **范围**：Global
   - **ID**：`deploy-ssh-key`（重要：这个ID会在Jenkinsfile中使用）
   - **描述**：`部署服务器SSH密钥`
   - **用户名**：`deploy`
   - **私钥**：选择 "Enter directly"，然后粘贴私钥内容
     ```bash
     # 在 Jenkins 服务器上获取私钥内容
     sudo cat /var/lib/jenkins/.ssh/id_rsa
     ```
5. 点击 **确定**

### 第四步：测试 SSH 连接

在 Jenkins 服务器上测试连接：

```bash
# 切换到 jenkins 用户
sudo su - jenkins

# 测试 SSH 连接（替换为你的实际服务器地址）
ssh <EMAIL>

# 首次连接会询问是否信任服务器，输入 yes
# 如果能成功登录，说明 SSH 配置正确
```

## 📝 创建部署任务

### 配置 Gitee Webhook 触发

1. **安装 Gitee 插件**
   - 进入 **Jenkins 管理 → 插件管理 → 可选插件**
   - 搜索并安装 `Gitee Plugin`

2. **配置 Webhook 触发器**
   - 在任务配置页面的 **构建触发器** 部分
   - 勾选 **"Gitee webhook 触发构建"**
   - 设置 **Webhook URL** 为 `https://jenkins.bingo-test.com/gitee-project/dev-bwms-deployment`
   - 生成并记录 **Webhook 令牌**

3. **在 Gitee 仓库中配置 Webhook**
   - 进入 Gitee 仓库设置 → Webhooks
   - 添加新的 Webhook
   - URL: 填写上一步生成的 Webhook URL
   - 令牌: 填写 Webhook 令牌
   - 触发事件: 选择 **Push 事件**
   - 点击确认保存

### 创建 Jenkins 流水线任务

#### 1. 创建新的流水线任务

1. **登录 Jenkins 管理界面**
   ```
   https://jenkins.bingo-test.com/
   ```

2. **创建新任务**
   - 点击左侧菜单 **"新建任务"**
   - 输入任务名称：`dev-bwms-deployment`
   - 选择 **"流水线"** 类
   - 点击 **"确定"**

#### 2. 配置任务基本信息

在任务配置页面设置：

**基本配置：**
- **描述**：`BWMS 项目自动化部署流水线`
- **丢弃旧的构建**：✅ 勾选
  - **保持构建的天数**：`7`
  - **保持构建的最大个数**：`10`

**构建触发器：**
- ✅ **GitHub hook trigger for GITScm polling** (如果使用 GitHub)
- ✅ **轮询 SCM**：`H/5 * * * *` (每5分钟检查一次代码变更)

#### 3. 配置流水线

**流水线配置：**
- **定义**：选择 `Pipeline script from SCM`
- **SCM**：选择 `Git`
- **仓库 URL**：`*************:bingo-limited/bwms.git`
- **凭据**：选择之前创建的 Git 凭据（如果是私有仓库）
- **分支**：`*/main` 或 `*/master`
- **脚本路径**：`Jenkinsfile`

#### 4. 添加 Git 凭据（如果需要）

如果是私有仓库，需要添加 Git 凭据：

1. 进入 **Jenkins 管理 → 凭据管理**
2. ��击 **全局凭据 (unrestricted)**
3. 点击 **添加凭据**
4. 配置如下：
   - **类型**：Username with password
   - **范围**：Global
   - **用户名**：你的 Git 用户名
   - **密码**：你的 Git 密码或 Personal Access Token
   - **ID**：`git-credentials`
   - **描述**：`Git 仓库访问凭据`

#### 5. 配置流水线脚本

在任务配置页面的**流水线**部分：



- **定义**：选择 `Pipeline script`
- **脚本**：在文本框中直接粘贴以下流水线脚本：
```groovy
pipeline {
    agent any

    environment {
        // 项目配置
        PROJECT_NAME = 'bwms'

        // 部署服务器配置
        DEPLOY_SERVER = '***************'
        DEPLOY_USER = 'root'
        DEPLOY_PATH = '/www/wwwroot/dev_bwms'

        // Node.js 版本
        NODE_VERSION = '20.18.0'
        BRANCH_NAME = 'dev'

        NEW_APP_URL = 'https://dev-bwms.bingo-test.com'
        DEPLOY_SSHAGENT = '252-deploy-ssh-key'

        GITEE_PR_ID = "${env.GITEE_PR_ID}"
        GITEE_PR_STATE = "${env.GITEE_PR_STATE}"
        GITEE_PR_MERGED = "${env.GITEE_PR_MERGED}"

    }

    tools {
        nodejs "${NODE_VERSION}"
    }

    stages {

        stage('构建开始通知') {
            steps {
                script {
                    // 发送构建开始通知
                    sendFeishuNotification(
                        status: 'start',
                        message: "🚀 BWMS项目开始构建！\n分支: ${BRANCH_NAME}\n构建号: #${BUILD_NUMBER}\n触发时间: ${new Date().format('yyyy-MM-dd HH:mm:ss')}\n部署服务器: ${DEPLOY_SERVER}"
                    )
                }
            }
        }

        stage('代码检出') {
            steps {
                echo '正在检出代码...'
                git branch: 'dev',
                    url: 'https://gitee.com/bingo-limited/bwms.git',
                    credentialsId: 'bwms_gitee_user'

                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                }
            }
        }

        stage('前端依赖安装') {
            steps {
                echo '安装前端依赖...'
                sh '''
                    # 检查Node.js版本
                    echo "检查Node.js版本..."
                    node --version

                    # 检查并安装yarn
                    echo "检查yarn是否已安装..."
                    if ! command -v yarn &> /dev/null; then
                        echo "yarn未安装，正在安装yarn..."
                        npm install -g yarn
                        echo "yarn安装完成"
                    else
                        echo "yarn已安装，版本："
                        yarn --version
                    fi

                    # 验证yarn安装
                    if ! command -v yarn &> /dev/null; then
                        echo "错误：yarn安装失败"
                        exit 1
                    fi

                    # 配置npm镜像
                    npm config set registry https://registry.npmmirror.com
                    yarn config set registry https://registry.npmmirror.com

                    # 显示当前配置
                    echo "当前npm镜像配置："
                    npm config get registry


                    # 生成模块package.json
                    echo "生成模块package.json..."
                    if [ -f "./prod.sh" ]; then
                        chmod +x ./prod.sh
                        ./prod.sh modules
                    fi

                    echo "初始化.env文件"
                    if [ ! -f ".env" ]; then
                        if [ -f ".env.example" ]; then
                            echo "⚠️ 从.env.example初始化.env"
                            cp .env.example .env
                            sed -i.bak "s|^APP_URL=.*|APP_URL=$NEW_APP_URL|" .env
                            sed -i.bak "s|^VITE_URL=.*|VITE_URL=$NEW_APP_URL|" .env
                            sed -i.bak "s|^SUBDIR=.*|SUBDIR=$NEW_APP_URL/|" .env
                        else
                            echo "⚠️ 警告: 缺少.env和.env.example文件"
                            exit 1;
                        fi
                    fi
                    
                    # 配置镜像源
                    echo "\n1.3 配置国内镜像源"
                   

                    yarn config set supportedArchitectures.os "linux"
                    yarn config set supportedArchitectures.cpu "x64"
                    
                    # 安装依赖
                    yarn add @rollup/rollup-linux-x64-gnu --ignore-engines --ignore-platform

                    # 检查关键文件
                    echo "2.1 检查关键配置文件"
                    [ -f "package.json" ] || { echo "❌ package.json不存在"; exit 1; }
                    

                    # ========================
                    # 阶段3: 智能依赖安装
                    # ========================
                    echo "\n=== 阶段3: 智能依赖安装 ==="
                    
                    # 检查是否需要全新安装
                    NEED_INSTALL=false
                    NEED_UPDATE=false
                    
                    echo "3.1 检查node_modules状态"
                    if [ ! -d "node_modules" ]; then
                        echo "❌ node_modules不存在，需要全新安装"
                        NEED_INSTALL=true
                    elif [ ! -f "yarn.lock" ]; then
                        echo "❌ yarn.lock不存在，需要重新安装"
                        NEED_INSTALL=true
                    elif [ "package.json" -nt "yarn.lock" ]; then
                        echo "⚠️ package.json有更新，需要更新依赖"
                        NEED_UPDATE=true
                    fi
                    
                    # 检查关键依赖
                    echo "3.2 验证关键依赖"
                    for dep in "vite" "vue" "@vitejs/plugin-vue" "typescript"; do
                        if [ ! -d "node_modules/$dep" ]; then
                            echo "❌ 关键依赖$dep缺失"
                            NEED_INSTALL=true
                        fi
                    done
                    
                    # 执行安装策略
                    echo "3.3 执行安装策略"
                    if [ "$NEED_INSTALL" = "true" ]; then
                        echo "⚠️ 执行全新安装..."
                        rm -rf node_modules yarn.lock
                        yarn install --network-timeout 100000 || { echo "❌ 依赖安装失败"; exit 1; }
                    elif [ "$NEED_UPDATE" = "true" ]; then
                        echo "⚠️ 更新依赖..."
                        yarn upgrade || { echo "❌ 依赖更新失败"; exit 1; }
                    else
                        echo "✅ 依赖状态正常，跳过安装"
                    fi
                '''
            }
        }

        stage('前端构建') {
            steps {
                echo '构建前端资源...'
                sh '''
                    # 设置构建环境
                    export NODE_OPTIONS="--max-old-space-size=8192"

                    # 检查并处理Vite构建问题
                    echo "检查并处理Vite构建问题..."

                    # 删除可能存在的有问题的软链接
                    echo "清理有问题的软链接..."
                    if [ -L "public/donate" ]; then
                        echo "发现donate软链接，正在删除..."
                        rm -f public/donate
                    fi

                    # 检查其他可能的软链接
                    find public -type l -name "*" 2>/dev/null | while read link; do
                        echo "发现软链接: $link，正在删除..."
                        rm -f "$link"
                    done

                    # 创建必要的目录（如果需要）
                    mkdir -p public/images
                    mkdir -p public/assets
                    mkdir -p public/uploads

                    # 确保public目录存在基本结构
                    if [ ! -d "public/images" ]; then
                        mkdir -p public/images
                        touch public/images/.gitkeep
                    fi

                    echo "��录检查完成，开始构建..."

                    # 根据分支选择构建模式
                    if [ "${BRANCH_NAME}" = "master" ] || [ "${BRANCH_NAME}" = "main" ]; then
                        echo "生产环境构建"
                        yarn build:prod
                    elif [ "${BRANCH_NAME}" = "test" ]; then
                        echo "测试环境构建"
                        yarn build:test
                    else
                        echo "开发环境构建"
                        yarn build
                    fi

                    # 检查构建结果
                    if [ ! -d "public/wms" ]; then
                        echo "端构建失败：找不到构建输出目录"
                        exit 1
                    fi

                    echo "前端构建完成，文件列表："
                    ls -la public/wms/

                    # 构建完成后清理临时修改
                    echo "清理构建过程中的临时修改..."

                    # 恢复原始package.json（如果有ES模块兼容性修复的备份）
                    if [ -f "package.json.esm-backup" ]; then
                        echo "恢复ES模块兼容性修复前的package.json..."
                        mv package.json.esm-backup package.json
                        echo "已恢复原始package.json"
                    fi

                    # 恢复其他可能的备份
                    if [ -f "package.json.build.backup" ]; then
                        echo "恢复构建前的package.json..."
                        mv package.json.build.backup package.json
                        echo "已恢复原始package.json"
                    fi

                    # 清理临时的vite.config.mjs
                    if [ -f "vite.config.mjs" ] && [ -f "vite.config.ts" ]; then
                        echo "清理临时vite.config.mjs..."
                        rm -f vite.config.mjs
                    fi
                '''
            }
        }

        stage('部署') {
            steps {
                echo '开始部署到服务器...'
                script {
                    try {
                        echo "=== 部署阶段开始 ==="
                        echo "目标服务器: ${DEPLOY_SERVER}"
                        echo "部署用户: ${DEPLOY_USER}"
                        echo "部署路径: ${DEPLOY_PATH}"
                        echo "当前时间: ${new Date()}"

                        sshagent(['252-deploy-ssh-key']) {
                            sh '''
                                set -e  # 遇到错误立即退出

                                echo "=== 步骤1: 检查SSH连接 ==="
                                echo "测试SSH连接到 ${DEPLOY_USER}@${DEPLOY_SERVER}..."
                                ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${DEPLOY_USER}@${DEPLOY_SERVER} "echo 'SSH连接成功'"

                                echo "=== 步骤2: 确保目标服务器目录结构存在 ==="
                                ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "
                                    echo '创建必要的目录结构...'
                                    mkdir -p ${DEPLOY_PATH}/public/wms
                                    mkdir -p ${DEPLOY_PATH}/bootstrap/cache
                                    echo '目录结构创建完成'
                                    echo '当前目录结构：'
                                    ls -la ${DEPLOY_PATH}/ || echo '目录不存在或为空'
                                "

                                echo "=== 步骤3: 检查本地构建产物 ==="
                                if [ ! -d "public/wms" ]; then
                                    echo "错误：本地构建产物不存在"
                                    echo "当前目录内容："
                                    ls -la
                                    echo "public目录内容："
                                    ls -la public/ || echo "public目录不存在"
                                    exit 1
                                fi
                                echo "本地构建产物检查完成："
                                echo "构建产物大小："
                                du -sh public/wms/
                                echo "构建产物文件列表（前10个）："
                                ls -la public/wms/ | head -10

                                echo "=== 步骤4: 同步前端构建产物到服务器 ==="
                                echo "开始同步前端文件..."
                                echo "源路径: public/wms/"
                                echo "目标路径: ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}/public/wms/"
                                echo "同步策略: 覆盖已存在文件，删除多余文件"
                                rsync -avz --progress --delete --stats public/wms/ ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}/public/wms/
                                echo "前端文件同步完成"

                                echo "=== 步骤5: 验证前端文件部署 ==="
                                ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "
                                    echo '检查前端文件部署结果...'
                                    echo '前端文件检查：'
                                    if [ -d '${DEPLOY_PATH}/public/wms' ]; then
                                        echo '✅ 前端目录存在'
                                        echo '文件数量：'
                                        find ${DEPLOY_PATH}/public/wms -type f | wc -l
                                        echo '目录大小：'
                                        du -sh ${DEPLOY_PATH}/public/wms
                                        echo '前端文件示例：'
                                        ls -la ${DEPLOY_PATH}/public/wms/ | head -5
                                    else
                                        echo '❌ 错误：前端目录不存在'
                                        exit 1
                                    fi
                                    echo ''
                                    echo '检查项目根目录：'
                                    if [ -d '${DEPLOY_PATH}' ]; then
                                        echo '✅ 项目目录存在'
                                        echo '检查prod.sh脚本：'
                                        if [ -f '${DEPLOY_PATH}/prod.sh' ]; then
                                            echo '✅ prod.sh脚本存在'
                                            chmod +x ${DEPLOY_PATH}/prod.sh
                                            echo 'prod.sh权限已设置'
                                        else
                                            echo '⚠️  警告：prod.sh脚本不存在，将跳过后端更新'
                                        fi
                                    else
                                        echo '❌ 错误：项目目录不存在'
                                        exit 1
                                    fi
                                "

                                echo "=== 步骤6: 在服务器上执行后端更新 ==="
                                ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "
                                    cd ${DEPLOY_PATH}
                                    echo '当前工作目录：'
                                    pwd
                                    echo '执行更新命令...'
                                    if [ -f './prod.sh' ]; then
                                        echo '开始执行 ./prod.sh update'
                                        ./prod.sh update
                                        echo 'prod.sh update 执行完成'
                                    else
                                        echo '错误：prod.sh脚本不存在，跳过更新'
                                        echo '尝试手动执行基本更新操作...'
                                        if command -v composer >/dev/null 2>&1; then
                                            echo '执行 composer install...'
                                            composer install --no-dev --optimize-autoloader --no-interaction || echo 'composer install 失败'
                                        else
                                            echo 'composer 未安装，跳过依赖更新'
                                        fi
                                    fi
                                    echo '切换到www用户和www组...'
                                    chown -R www:www *
                                "

                                echo "=== 部署完成 ==="
                                echo "部署时间: $(date)"
                            '''
                        }

                        echo "=== 部署成功 ==="

                    } catch (Exception e) {
                        echo "=== 部署过程中发生错误 ==="
                        echo "错误信息: ${e.getMessage()}"
                        echo "错误详情: ${e.toString()}"
                        echo "错误发生时间: ${new Date()}"

                        // 尝试收集更多错误信息
                        try {
                            sshagent(['252-deploy-ssh-key']) {
                                sh '''
                                    echo "=== 错误诊断信息 ==="
                                    echo "检查SSH连接..."
                                    ssh -o ConnectTimeout=5 ${DEPLOY_USER}@${DEPLOY_SERVER} "echo 'SSH连接正常'" || echo "SSH连接失败"

                                    echo "检查目标目录..."
                                    ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "ls -la ${DEPLOY_PATH}/" || echo "无法访问目标目录"

                                    echo "检查磁盘空间..."
                                    ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "df -h" || echo "无法检查磁盘空间"
                                '''
                            }
                        } catch (Exception diagError) {
                            echo "无法收集诊断信息: ${diagError.getMessage()}"
                        }

                        throw e
                    }
                }
            }
        }
    }

    post {
        always {
            echo '清理工作空间...'
            sh '''
                # 清理构建文件
                rm -f *.tar.gz
            '''
        }

        success {
            echo '部署成功！'
            script {
                // 发送飞书成功通知
                sendFeishuNotification(
                    status: 'success',
                    message: "🎉 BWMS项目部署成功！\n分支: ${BRANCH_NAME}\n构建号: #${BUILD_NUMBER}\n提交: ${env.GIT_COMMIT_SHORT}\n部署服务器: ${DEPLOY_SERVER}"
                )
            }
        }

        failure {
            echo '部署失败！'
            script {
                // 发送飞书失败通知
                sendFeishuNotification(
                    status: 'failure',
                    message: "❌ BWMS项目部署失败！\n分支: ${BRANCH_NAME}\n构建号: #${BUILD_NUMBER}\n失败原因: 请查看构建日志\n构建链接: ${BUILD_URL}"
                )
            }
        }
    }
}


// 飞书通知函数
def sendFeishuNotification(Map params) {
    def webhook = env.FEISHU_WEBHOOK_URL
    if (!webhook) {
        echo "警告：未配置飞书Webhook URL，跳过通知"
        return
    }

    def color = 'blue'
    def title = '🚀 开始构建'

    if (params.status == 'success') {
        color = 'green'
        title = '✅ 部署成功'
    } else if (params.status == 'failure') {
        color = 'red'
        title = '❌ 部署失败'
    } else if (params.status == 'start') {
        color = 'blue'
        title = '🚀 开始构建'
    }

    def payload = [
        msg_type: "interactive",
        card: [
            config: [
                wide_screen_mode: true
            ],
            header: [
                title: [
                    tag: "text",
                    content: title
                ],
                template: color
            ],
            elements: [
                [
                    tag: "div",
                    text: [
                        tag: "lark_md",
                        content: params.message
                    ]
                ],
                [
                    tag: "action",
                    actions: [
                        [
                            tag: "button",
                            text: [
                                tag: "plain_text",
                                content: "查看构建详情"
                            ],
                            type: "primary",
                            url: "${BUILD_URL}"
                        ]
                    ]
                ]
            ]
        ]
    ]

    try {
        def response = httpRequest(
            httpMode: 'POST',
            url: webhook,
            contentType: 'APPLICATION_JSON',
            requestBody: groovy.json.JsonOutput.toJson(payload)
        )
        echo "飞书通知发送成功: ${response.status}"
    } catch (Exception e) {
        echo "飞书通知发送失败: ${e.message}"
    }
}
```

#### 3. 修改配置参数

在上面的脚本中，请修改以下参数为您的实际配置：

- **Git仓库地址**：`*************:bingo-limited/bwms.git`
- **Git SSH凭据ID**：`bwm-gitee-ssh-key`
- **部署服务器IP**：`***************`
- **部署用户**：`root`
- **部署路径**：`/www/wwwroot/dev_bwms`
- **Node.js版本**：`20.18.0`（确保Jenkins服务器已安装）
- **包管理器**：`yarn`（Pipeline会自动安装）
- **飞书Webhook URL**：需要配置环境变量 `FEISHU_WEBHOOK_URL`
- **NEW_APP_URL配置**：需要修改Pipeline中的NEW_APP_URL为您的实际域名
  - 当前设置：`https://new-bwms.bingo-test.com`

#### 4. 保存配置

点击页面底部的 **"保存"** 按钮。

### 配置飞书通知

#### 1. 获取飞书Webhook URL

1. **创建飞书群聊机器人**
   - 在飞书中创建一个群聊
   - 点击群设置 → 群机器人 → 添加机器人
   - 选择"自定义机器人"
   - 设置机器人名称：`Jenkins部署通知`
   - 复制生成的Webhook URL

2. **在Jenkins中配置环境变量**
   - 进入 **Jenkins 管理 → 系统配置**
   - 找到 **全局属性** 部分
   - 勾选 **环境变量**
   - 添加环境变量：
     - **名称**：`FEISHU_WEBHOOK_URL`
     - **值**：[您的飞书Webhook URL](https://open.feishu.cn/open-apis/bot/v2/hook/d919bc06-ba41-4dbc-8533-625037d03790)
   - 点击 **保存**

#### 2. 安装HTTP Request插件

1. **进入插件管理**
   ```
   Jenkins 管�� → 插��管理 → 可选插件
   ```

2. **搜索并安装插件**
   - 搜索：`HTTP Request Plugin`
   - 勾选并点击 **"直接安装"**
   - 重启Jenkins

#### 3. 飞书通知功能说明

**构建开始通知内容：**
- 🚀 开始构建标题
- 项目分支信息
- 构建号
- 触发时间
- 部署服务器
- 查看构建详情按钮

**成功通知内容：**
- 🎉 部署成功标题
- 项目分支信息
- 构建号
- Git提交信息
- 部署服务器
- 查看构建详情按钮

**失败通知内容：**
- ❌ 部署失败标题
- 项目分支信息
- 构建号
- 失败原因提示
- 构建链接按钮

#### 4. 测试飞书通知

配置完成后，触发一次构建来测试飞书通知是否正常工作。

## 构建方案说明

### � 直接构建方案

本部署方案采用**直接构建**方式，在Jenkins服务器上直接进行前端构建：

#### **1. 环境要求**
- ✅ **Node.js版本**：确保Jenkins服务器安装Node.js 20.18.0
- ✅ **包管理器**：Pipeline自动安装和配置yarn
- ✅ **镜像加速**：自动配置国内镜像源提升下载速度

#### **2. 构建流程**
- ✅ **智能依赖管理**：检查依赖变化，避免重复安装
- ✅ **环境配置**：自动配置.env文件和APP_URL
- ✅ **分支构建**：根分支自动选择构建模式

#### **3. 优化特性**
- ✅ **内存管理**：设置`NODE_OPTIONS="--max-old-space-size=8192"`
- ✅ **平台兼容**：使用`--ignore-platform --ignore-optional`参数
- ✅ **缓存优化**：智能检测依赖变化，复用已安装的依赖

## 部署服务器配置

### 1. 服务器环境准备

```bash
# 安装必需软件
sudo apt update
sudo apt install -y nginx php8.2-fpm php8.2-mysql php8.2-xml php8.2-mbstring \
    php8.2-curl php8.2-zip php8.2-gd php8.2-intl php8.2-bcmath \
    mysql-server composer git unzip

# 创建部署目录
sudo mkdir -p /var/www/bwms
sudo chown deploy:deploy /var/www/bwms

# 配置 SSH 密钥
mkdir -p ~/.ssh
# 将 Jenkins 的公钥添加到 ~/.ssh/authorized_keys
```

### 2. Nginx 配置

创建 `/etc/nginx/sites-available/bwms`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/bwms/current/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/bwms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. 部署脚本示例

在部署服务器上创建 `/home/<USER>/deploy.sh` 脚本：

```bash
#!/bin/bash

# 部署脚本 - 支持前后端分离部署
PROJECT_PATH="/var/www/bwms"
BACKUP_PATH="/var/www/backups"
BUILD_NUMBER=$1

if [ -z "$BUILD_NUMBER" ]; then
    echo "错误：请提供构建号"
    exit 1
fi

echo "开始部署构建 #${BUILD_NUMBER}..."

# 创建备份目录
mkdir -p $BACKUP_PATH

# 备份当前版本
if [ -d "$PROJECT_PATH/current" ]; then
    echo "备份当前版本..."
    mv $PROJECT_PATH/current $BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S)
fi

# 创建新版本目录
mkdir -p $PROJECT_PATH/current
cd /tmp

# 解压部署包
echo "解压部署包..."
tar -xzf bwms-${BUILD_NUMBER}.tar.gz

# 部署后端文件
echo "部署后端文件..."
cp -r deploy-package/backend/* $PROJECT_PATH/current/

# 部署前端构建产物
echo "部署前端构建产物..."
mkdir -p $PROJECT_PATH/current/public/build
cp -r deploy-package/frontend/* $PROJECT_PATH/current/public/

# 切换到项目目录
cd $PROJECT_PATH/current

# 复制环境配置
if [ -f "$PROJECT_PATH/.env" ]; then
    cp $PROJECT_PATH/.env .env
else
    cp .env.example .env
    echo "警告：请配置 .env 文件"
fi

# 安装后端依赖
echo "安装 Composer 依赖..."
composer install --no-dev --optimize-autoloader --no-interaction

# 设置权限
echo "设置文件权限..."
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache

# 设置前端文件权限
sudo chown -R www-data:www-data public/build
sudo chmod -R 755 public/build

# 创建存储链接
if [ ! -L public/storage ]; then
    php artisan storage:link
fi

# 运行数据库迁移 (谨慎使用)
# php artisan migrate --force

# 清理和缓存
echo "清理缓存..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 重启服务
echo "重启服务..."
sudo systemctl reload nginx
sudo systemctl restart php8.2-fpm

# 清理临时文件
echo "清理临时文件..."
rm -rf /tmp/deploy-package
rm -f /tmp/bwms-${BUILD_NUMBER}.tar.gz

echo "部署完成！"
echo "前端构建产物已部署到: $PROJECT_PATH/current/public/build"
echo "后端文件已部署到: $PROJECT_PATH/current"
```

设置脚本权限：
```bash
chmod +x /home/<USER>/deploy.sh
```

## 📦 构建流程说明

### 前后端分离构建策略

本部署方案采用**在Jenkins服务器上构建，SSH传输部署**的策略：

#### 构建阶段（Jenkins服务器）：
1. **代码检出**：从Gitee仓库拉取最新代码
2. **智能依赖管理**：
   - 检查`node_modules`和`yarn.lock`是否存在
   - 比较`package.json`修改时间
   - 使用yarn进行依赖管理，只在必要时重新安装
   - 自动检测和修复Rollup原生依赖问题
3. **模块配置**：执行`./prod.sh modules`生成模块package.json
4. **前端构建**：使用Vite和yarn构建Vue.js应用，输出到`public/wms`目录
5. **文件同步**：通过rsync直接同步到目标服务器

#### 部署阶段（目标服务器）：
1. **前端同步**：将构建好的前端文件同步到`/www/wwwroot/dev_bwms/public/wms/`
2. **后端更新**：在服务器上执行`./prod.sh update`命令拉取后端代码
   - 从Git仓库拉取最新后端代码
   - 更新Composer依赖
   - 清理缓存
   - 重启服务
   - 重新加载服务

#### 优势：
- ✅ **构建环境统一**：所有构建在Jenkins服务器上完成
- ✅ **部署速度快**：目标服务器只需要解压和配置
- ✅ **资源占用少**：目标服务器不需要安装Node.js等构建工具
- ✅ **版本一致性**：确保所有环境使用相同的构建产物
- ✅ **回滚简单**：可以快速回滚到之前的版本

#### 文件结构：
```
部署服务器目录结构：
/www/wwwroot/dev_bwms/
├── app/              # Laravel应用目录
├── config/           # 置文件
├── database/         # 数据库文件
├── routes/           # 路由文件
├── Modules/          # 模块目录
├── public/           # 公共资源目录
│   ├── wms/          # 前端构建产物（Vite输出）
│   │   ├── assets/   # 静态资源
│   │   ├── wms.html  # 入口HTML文件
│   │   └── ...
│   └── ...
├── vendor/           # Composer依赖
├── prod.sh           # 部署脚本
└── ...
```

#### 同步策略：
- **前端文件**：`public/wms/` → `/www/wwwroot/dev_bwms/public/wms/`
- **后端文件**：项目根目录 → `/www/wwwroot/dev_bwms/`（排除前端源码和环境配置）
- **排除文件**：`.env`、`.env.*`、`node_modules`、`.git`、前端码等
- **执行命令**：`./prod.sh update`��更新依赖和缓��）

## � 执行部署

### 第一步：首次部署准备

#### 1. 验证环境配置

在开始部署前，确保所有环境都已正确配置：

**Jenkins 服务器检查清单：**
- ✅ Jenkins 服务正常运行
- ✅ 必需插件已安装
- ✅ Node.js 工具已配置
- ✅ SSH 密钥已配置
- ✅ 能够访问 Git 仓库

**目标服务器检查清单：**
- ✅ Nginx 和 PHP-FPM 服务正常运行
- ✅ MySQL 数据库服务正常运行
- ✅ 部署用户和目录已创建
- ✅ SSH 密钥认证正常
- ✅ 部署脚本已创建并有执行权限

#### 2. 配置环境变量

在目标服务器上创建 `.env` 文件：

```bash
# 在目标服务器上
sudo su - deploy
cd /var/www/bwms

# 创建环境配置文件
cat > .env << 'EOF'
APP_NAME=BWMS
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bwms_production
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
EOF

# 设置正确的权限
chmod 600 .env
```

### 第二步：执行首次部署

#### 1. 手动触发构建

1. **登录 Jenkins 界面**
   ```
   https://jenkins.bingo-test.com/
   ```

2. **找到部署任务**
   - 在首页找到 `bwms-deployment` 任务
   - 点击任务名称进入任务详情页

3. **开始构建**
   - 点击左侧菜单 **"立即构建"**
   - 构建会立即开始，可以看到构建队列中的任务

#### 2. 监控构建过程

**查看构建日志：**
1. 点击构建号（如 `#1`）
2. 点击 **"控制台输出"** 查看实时日志
3. 观察各个阶段的执行情况：
   - 代码检出
   - 环境准备
   - 代码质量检查
   - 构建
   - 测试
   - 部署

**使用 Blue Ocean 界面（推荐）：**
1. 点击左侧菜单 **"Open Blue Ocean"**
2. 选择 `bwms-deployment` 流水线
3. 查看可视化的流水线执行过程

#### 3. 处理构建问题

如果构建失败，常见问题和解决方案：

**Node.js 相关问题：**
```bash
# 如果提示 Node.js 版本问题
# 在 Jenkins 全局工具配置中检查 Node.js 配置
# 确保 Jenkinsfile 中的 NODE_VERSION 与配置一致
```

**权限问题：**
```bash
# 在目标服务器上检查权限
sudo chown -R deploy:deploy /var/www/bwms
sudo chmod -R 755 /var/www/bwms
```

**SSH 连接问题：**
```bash
# 在 Jenkins 服务器上测试 SSH 连接
sudo su - jenkins
ssh <EMAIL>
```

### 第三步：验证部署结果

#### 1. 检查应用状态

```bash
# 在目标服务器上检查
cd /var/www/bwms/current

# 检查文件是否正确部署
ls -la

# 检查 Laravel 应用状态
php artisan --version

# 检查数据库连接
php artisan tinker
# 在 tinker 中执行：DB::connection()->getPdo();
```

#### 2. 检查 Web 服务

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查 PHP-FPM 状态
sudo systemctl status php8.2-fpm

# 测试网站访问
curl -I http://your-domain.com
```

#### 3. 查看应用日志

```bash
# 查看 Laravel 日志
tail -f /var/www/bwms/current/storage/logs/laravel.log

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log

# 查看 PHP-FPM 日志
sudo tail -f /var/log/php8.2-fpm.log
```

### 第四步：设置自动部署

#### 1. 配置 Git Webhook（推荐）

如果使用 GitHub：

1. **在 GitHub 仓库中设置 Webhook**
   - 进入仓库 → Settings → Webhooks
   - 点击 "Add webhook"
   - **Payload URL**: `https://jenkins.bingo-test.com//github-webhook/`
   - **Content type**: `application/json`
   - **Events**: 选择 "Just the push event"

2. **测试自动触发**
   - 推送代码到 main/master 分支
   - 观察 Jenkins 是否自动开始构建

#### 2. 配置定时构建（备选方案）

如果无法使用 Webhook，可以配置定时检查：

1. 在 Jenkins 任务配置中
2. **构建触发器** → **轮询 SCM**
3. 设置 cron 表达式：`H/5 * * * *`（每5分钟检查一次）

### 第五步：日常运维

#### 1. 监控构建状态

- 定期检查 Jenkins 构建历史
- 关注失败的构建并及时处理
- 监控部署服务器的资源使用情况

#### 2. 备份策略

```bash
# 设置定期备份脚本
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/www/backups"
DATE=$(date +%Y%m%d-%H%M%S)

# 备份代码
tar -czf $BACKUP_DIR/code-backup-$DATE.tar.gz -C /var/www/bwms current

# 备份数据库
mysqldump -u your_db_user -p your_db_password bwms_production > $BACKUP_DIR/db-backup-$DATE.sql

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup.sh

# 添加到 crontab（每天凌晨2点备份）
echo "0 2 * * * /home/<USER>/backup.sh" | crontab -
```

#### 3. 回滚操作

如果部署出现问题，可以快速回滚：

```bash
# 方法1：使用备份回滚
ssh <EMAIL>
cd /var/www/bwms
rm -rf current
mv backup-YYYYMMDD-HHMMSS current
sudo systemctl reload nginx

# 方法2：重新部署上一个成功的版本
# 在 Jenkins 中找到上一个成功的构建
# 点击 "重新构建" 或 "Replay"
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. Jenkins 相关问题

**问题：Jenkins 无法启动**
```bash
# 检查 Java 版本
java -version

# 检查 Jenkins 服务状态
sudo systemctl status jenkins

# 查看 Jenkins 日志
sudo journalctl -u jenkins -f

# 重启 Jenkins 服务
sudo systemctl restart jenkins
```

**问题：HTTP ERROR 403 - No valid crumb was included in the request**

这是Jenkins的CSRF保护机制导致的，有以下几种解决方案：

**方案1：刷新页面并重新登录**
```bash
# 1. 清除浏览器缓存和Cookie
# 2. 重新登录Jenkins
# 3. 如果问题持续，尝试使用无痕/隐私模式
```

**��案2：禁用CSRF保护（不推荐用于生产环境）**
```bash
# 编辑Jenkins启动参数
sudo nano /etc/default/jenkins

# 在JENKINS_ARGS中添加：
JENKINS_ARGS="--argumentsRealm.passwd.admin=admin --argumentsRealm.roles.admin=admin -Dhudson.security.csrf.GlobalCrumbIssuerConfiguration.DISABLE_CSRF_PROTECTION=true"

# 启Jenkins
sudo systemctl restart jenkins
```

**方案3：配置反向代理（推荐）**
```nginx
# 如果使用Nginx反向代理，在nginx配置中添加：
location /jenkins/ {
    proxy_pass http://localhost:8080/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # 重要：添加这些头部来处理CSRF
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Server $host;
}
```

**方案4：通过Jenkins CLI操作（临时解决）**
```bash
# 下载Jenkins CLI
wget https://jenkins.bingo-test.com//jnlpJars/jenkins-cli.jar

# 使用CLI配置工具（需要API Token）
java -jar jenkins-cli.jar -s https://jenkins.bingo-test.com// -auth username:api_token help
```

**问��：插件安装失败**
```bash
# 检查网络连接
curl -I https://updates.jenkins.io/

# 手动下载插件
# 1. 访问 https://plugins.jenkins.io/
# 2. 下载 .hpi 文件
# 3. 在 Jenkins 管理 → 插件管理 → 高级 → 上传插件
```

**问题：Node.js 版本不匹配**
```
ERROR: No jenkins.plugins.nodejs.tools.NodeJSInstallation named 20.18.0 found
```

**解决方案：**
1. **检查Jenkins全局工具配置**
   ```
   Jenkins 管理 → 全局工具配置 → NodeJS
   ```

2. **确保Node.js配置正确**
   - **名称**：必须是 `20.18.0`（与Pipeline脚本中完全一致）
   - **自动安装**：✅ 勾选
   - **版本**：选择 `NodeJS 20.18.0`

3. **如果没有配置Node.js工具**
   ```
   1. 点击 "新增 NodeJS"
   2. 名称填写：20.18.0
   3. 勾选"自动安装"
   4. 选择对应版本
   5. 点击"保存"
   ```

4. **或者修改Pipeline脚本中的版本名称**
   ```groovy
   tools {
       nodejs "20.18.0"  // 确保这个名称与全局工具配置中的名称一致
   }
   ```

#### 2. 构建过程问题

**问题：yarn未安装或版本不兼容**
```
yarn: command not found
/var/lib/jenkins/workspace/dev-bwms-deployment@tmp/durable-342bee65/script.sh.copy: line 3: yarn: command not found
```

**解决方案：**

1. **自动安装yarn（已集成到Pipeline）**
   ```bash
   # Pipeline会自动检查并安装yarn
   if ! command -v yarn &> /dev/null; then
       echo "yarn未安装，正在安装yarn..."
       npm install -g yarn
   fi
   ```

2. **手动在Jenkins服务器上安装yarn**
   ```bash
   # 方法1：通过npm全局安装
   sudo npm install -g yarn

   # 方法2：通过官方脚本安装
   curl -o- -L https://yarnpkg.com/install.sh | bash
   source ~/.bashrc

   # 方法3：通过包管理器安装（Ubuntu/Debian）
   curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
   echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
   sudo apt update && sudo apt install yarn
   ```

3. **验证yarn安装**
   ```bash
   # 检查yarn版本
   yarn --version

   # 检查yarn全局安装路径
   which yarn

   # 如果yarn安装在用户目录，需要添加到PATH
   echo 'export PATH="$HOME/.yarn/bin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```

4. **Jenkins用户权限问题**
   ```bash
   # 切换到jenkins用户
   sudo su - jenkins

   # 检查jenkins用户是否能访问yarn
   yarn --version

   # 如果不能访问，为jenkins用户安装yarn
   npm install -g yarn
   ```

5. **使用特定版本的yarn**
   ```bash
   # 安装特定版本的yarn
   npm install -g yarn@1.22.19

   # 或者使用yarn自身升级
   yarn set version stable
   ```

**问题：依赖下载速度慢**
```
npm ERR! network timeout at: https://registry.npmjs.org/package-name
yarn install 下载速度过慢或超时
```

**解决方案：**

1. **使用国内镜像加速（已集成到Pipeline）**
   ```bash
   # Pipeline会自动配置以下镜像
   npm config set registry https://registry.npmmirror.com
   yarn config set registry https://registry.npmmirror.com
   ```

2. **手动配置国内镜像**
   ```bash
   # 配置npm淘宝镜像
   npm config set registry https://registry.npmmirror.com
   npm config set target_platform linux
   npm config set target_arch x64

   # 配置yarn淘宝镜像
   yarn config set registry https://registry.npmmirror.com

   # 配置其他常用二进制文件镜像
   npm config set electron_mirror https://npmmirror.com/mirrors/electron/
   npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
   npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
   npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver/
   ```

3. **使用.npmrc文件配置**
   ```bash
   # 在项目根目录或用户主目录创建.npmrc文件
   cat > ~/.npmrc << 'EOF'
   registry=https://registry.npmmirror.com
   target_platform=linux
   target_arch=x64
   electron_mirror=https://npmmirror.com/mirrors/electron/
   sass_binary_site=https://npmmirror.com/mirrors/node-sass/
   phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
   chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/
   operadriver_cdnurl=https://npmmirror.com/mirrors/operadriver/
   fse_binary_host_mirror=https://npmmirror.com/mirrors/fsevents/
   EOF
   ```

4. **使用.yarnrc文件配置**
   ```bash
   # 在项目根目录创建.yarnrc文件
   cat > .yarnrc << 'EOF'
   registry "https://registry.npmmirror.com"
   target_platform "linux"
   target_arch "x64"
   electron_mirror "https://npmmirror.com/mirrors/electron/"
   sass_binary_site "https://npmmirror.com/mirrors/node-sass/"
   phantomjs_cdnurl "https://npmmirror.com/mirrors/phantomjs/"
   chromedriver_cdnurl "https://npmmirror.com/mirrors/chromedriver/"
   EOF
   ```

5. **验证镜像配置**
   ```bash
   # 检查npm配置
   npm config get registry
   npm config list

   # 检查yarn配置
   yarn config get registry
   yarn config list

   # 测试下载速度
   time npm install lodash --dry-run
   time yarn add lodash --dry-run
   ```

6. **其他镜像源选择**
   ```bash
   # 华为云镜像
   npm config set registry https://repo.huaweicloud.com/repository/npm/

   # 腾讯云镜像
   npm config set registry https://mirrors.cloud.tencent.com/npm/

   # 阿里云镜像
   npm config set registry https://registry.npm.taobao.org/

   # 恢复官方镜像
   npm config set registry https://registry.npmjs.org/
   ```

**问题：Vite构建时软链接目录错误**
```
error during build:
ENOENT: no such file or directory, stat '/var/lib/jenkins/workspace/dev-bwms-deployment/public/donate'
    at Object.statSync (node:fs:1666:25)
    at copyDir (file:///var/lib/jenkins/workspace/dev-bwms-deployment/node_modules/vite/dist/node/chunks/dep-C6uTJdX2.js:17094:30)
```

**解决方案：**

1. **自动清理软链接（已集成到Pipeline）**
   ```bash
   # 删除有问题的软链接
   if [ -L "public/donate" ]; then
       echo "发现donate软链接，正在删除..."
       rm -f public/donate
   fi

   # 清理所有软链接
   find public -type l -name "*" 2>/dev/null | while read link; do
       echo "发现软链接: $link，正在删除..."
       rm -f "$link"
   done
   ```

2. **手动��理软链接**
   ```bash
   # 检查public目录下的软链接
   find public -type l -ls

   # 删除特定的软链接
   rm -f public/donate

   # 批量删除所有软链接
   find public -type l -delete
   ```

3. **检查软链接目标**
   ```bash
   # 查看软链接指向
   ls -la public/donate

   # 如果需要保留，创建实际目录
   rm -f public/donate
   mkdir -p public/donate
   ```

4. **修改vite.config.ts排除软链接**
   ```typescript
   // 在vite.config.ts中排除有问题的目录
   export default defineConfig({
     build: {
       rollupOptions: {
         external: ['public/donate'] // 排除软链接目录
       }
     }
   })
   ```

**问题：@rollup/rollup-linux-x64-gnu模块缺失错误**
```
Error: Cannot find module @rollup/rollup-linux-x64-gnu. npm has a bug related to optional dependencies
Module not found: Error: Can't resolve '@rollup/rollup-linux-x64-gnu'
```

**解决方案：**

1. **手动安装架构特定的rollup依赖（已集成到Pipeline）**
   ```bash
   # 预先安装x64架构的rollup依赖
   yarn add @rollup/rollup-linux-x64-gnu --optional --ignore-engines --ignore-platform

   # ��后安装其他依赖
   yarn install --ignore-platform --ignore-optional
   ```

2. **删除package.json中的问题依赖**
   ```bash
   # 检查package.json中是否有错误的rollup依赖
   grep -i "rollup.*linux" package.json

   # 删除错误的依赖（如arm64版本）
   npm uninstall @rollup/rollup-linux-arm64
   npm uninstall @esbuild/linux-arm64

   # 重新安装正确的依赖
   yarn add @rollup/rollup-linux-x64-gnu --optional
   yarn install --ignore-platform
   ```

3. **使用resolutions强制版本锁定**
   ```json
   // 在package.json中添加
   {
     "resolutions": {
       "@rollup/rollup-linux-x64-gnu": "^4.0.0",
       "@rollup/rollup-linux-arm64": "npm:@rollup/rollup-linux-x64-gnu@^4.0.0"
     }
   }
   ```

4. **清理并重新安装**
   ```bash
   # 完全清理
   rm -rf node_modules
   rm -f yarn.lock package-lock.json
   yarn cache clean

   # 重新安装
   yarn add @rollup/rollup-linux-x64-gnu --optional --ignore-platform
   yarn install --ignore-platform --ignore-optional
   ```

**问题：SSH密钥认证失败**
```
Error loading key "/var/lib/jenkins/workspace/dev-bwms-deployment@tmp/private_key_xxx.key": error in libcrypto
Failed to run ssh-add
[ssh-agent] Could not find specified credentials: 252-deploy-ssh-key
```

**解决方案：**

1. **检查SSH密钥格式**
   ```bash
   # 检查密钥文件格式
   file ~/.ssh/id_rsa

   # 应该显示类似：
   # ~/.ssh/id_rsa: OpenSSH private key

   # 如果显示其他格式，需要转换
   ssh-keygen -p -m PEM -f ~/.ssh/id_rsa
   ```

2. **重新生成SSH密钥（如果格式有问题）**
   ```bash
   # 生成新的SSH密钥对
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/jenkins_deploy_key

   # 将公钥添加到目标服务器
   ssh-copy-id -i ~/.ssh/jenkins_deploy_key.pub root@***************
   ```

3. **在Jenkins中重新配置SSH凭据**
   - 进入 **Jenkins 管理 → 凭据管理**
   - 找到 `deploy-ssh-key` 凭据并删除
   - 点击 **添加凭据**
   - 选择类型：**SSH Username with private key**
   - ID：`deploy-ssh-key`
   - 用户名：`root`
   - 私钥：选择 **直接输入**，粘贴私钥内容
   - 密码短语：如果有的话

4. **验证SSH连接**
   ```bash
   # 在Jenkins服务器上测试SSH连接
   ssh -i ~/.ssh/jenkins_deploy_key root@*************** "echo 'SSH连接成功'"
   ```

5. **检查目标服务器SSH配置**
   ```bash
   # 在目标服务器上检查SSH配置
   sudo nano /etc/ssh/sshd_config

   # 确保以下配置正确：
   PubkeyAuthentication yes
   AuthorizedKeysFile .ssh/authorized_keys
   PermitRootLogin yes  # 如果使用root用户

   # 重启SSH服务
   sudo systemctl restart sshd
   ```

6. **检查文件权限**
   ```bash
   # 在目标服务器上检查权限
   chmod 700 ~/.ssh
   chmod 600 ~/.ssh/authorized_keys
   chown -R root:root ~/.ssh  # 如果使用root用户
   ```

**问题：rsync文件同步和覆盖策略**
```
需要了解rsync的文件覆盖行为
如何确保文件正确同步和覆盖
同步过程中的文件冲突处理
```

**rsync覆盖行为说明：**

1. **默认覆盖行为**
   ```bash
   # rsync -avz 默认会覆盖已存在的文件
   rsync -avz source/ destination/

   # 具体行为：
   # - 如果目标文件不存在：直接复制
   # - 如果目标文件存在且内容不同：覆盖目标文件
   # - 如果目标文件存在且内容相同：跳过传输（基于时间戳和大小）
   ```

2. **Pipeline中使用的参数**
   ```bash
   rsync -avz --progress --delete --stats public/wms/ ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}/public/wms/

   # 参数说明：
   # -a: 归档模式，保持文件属性
   # -v: 详细输出
   # -z: 压缩传输
   # --progress: 显示传输进度
   # --delete: 删除目标目录中源目录没有的文件
   # --stats: 显示传输统计信息
   ```

3. **强制覆盖选项**
   ```bash
   # 如果需要强制覆盖所有文件（忽略时间戳）
   rsync -avz --progress --delete --force public/wms/ destination/

   # 如果需要基于校验和而不是时间戳比较
   rsync -avz --progress --delete --checksum public/wms/ destination/
   ```

4. **备份现有文件**
   ```bash
   # 在覆盖前备份现有文件
   rsync -avz --progress --delete --backup --backup-dir=/backup/$(date +%Y%m%d_%H%M%S) public/wms/ destination/
   ```

5. **排除特定文件**
   ```bash
   # 排除特定文件不被覆盖
   rsync -avz --progress --delete --exclude='.env' --exclude='storage/logs/*' source/ destination/
   ```

**验证同步结果：**
```bash
# 检查同步后的文件
ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "
    echo '同步后的文件统计：'
    find ${DEPLOY_PATH}/public/wms -type f | wc -l
    echo '最新文件：'
    find ${DEPLOY_PATH}/public/wms -type f -newer ${DEPLOY_PATH}/public/wms -ls | head -5
    echo '目录大小：'
    du -sh ${DEPLOY_PATH}/public/wms
"
```

**问题：.env文件APP_URL配置错误**
```
Laravel应用无法正确访问
前端API请求失败，跨域问题
APP_URL配置不匹配当前环境
```

**解决方案：**

1. **自动APP_URL配置（已集成到Pipeline）**
   ```bash
   # Pipeline会根据分支自动配置APP_URL
   # master/main分支 → 生产环境URL
   # test分支 → 测试环境URL
   # 其他分支 → 开发环境URL
   ```

2. **手动配置.env文件**
   ```bash
   # 检查当前APP_URL配置
   grep "^APP_URL=" .env

   # 手动修改APP_URL
   sed -i.bak "s|^APP_URL=.*|APP_URL=https://your-domain.com|" .env

   # 或者直接编辑.env文件
   nano .env
   ```

3. **根据环境设置不同的APP_URL**
   ```bash
   # 生产环境
   APP_URL=https://your-production-domain.com

   # 测试环境
   APP_URL=https://test.your-domain.com

   # 开发环境
   APP_URL=https://dev.your-domain.com

   # 本地开发
   APP_URL=http://localhost:8000
   ```

4. **验证APP_URL配置**
   ```bash
   # 检查.env文件中的APP_URL
   cat .env | grep APP_URL

   # 检查Laravel配置
   php artisan config:show app.url

   # 清理配置缓存
   php artisan config:clear
   php artisan config:cache
   ```

5. **从.env.example初始化.env文件（已集成到Pipeline）**
   ```bash
   # Pipeline会自动检查并从.env.example初始化.env文件
   # 如果.env不存在但.env.example存在，会自动复制并配置APP_URL
   ```

6. **手动从.env.example初始化**
   ```bash
   # 检查是否存在.env.example文件
   if [ -f ".env.example" ]; then
       echo "从.env.example初始化.env文件..."
       cp .env.example .env

       # 设置APP_URL
       sed -i.bak "s|^APP_URL=.*|APP_URL=https://your-domain.com|" .env

       # 生成应用密钥（Laravel项目）
       php artisan key:generate

       echo ".env文件初始化完成"
   else
       echo "错误：未找到.env.example文件"
   fi
   ```

7. **创建环境特定的.env文件**
   ```bash
   # 创建不同环境的.env模板
   cp .env .env.production
   cp .env .env.testing
   cp .env .env.development

   # 根据分支选择对应的.env文件
   if [ "${BRANCH_NAME}" = "master" ]; then
       cp .env.production .env
   elif [ "${BRANCH_NAME}" = "test" ]; then
       cp .env.testing .env
   else
       cp .env.development .env
   fi
   ```

**问题：ES模块兼容性错误**
```
failed to load config from /var/lib/jenkins/workspace/dev-bwms-deployment/vite.config.ts
error during build:
Error [ERR_REQUIRE_ESM]: require() of ES Module /var/lib/jenkins/workspace/dev-bwms-deployment/node_modules/package-manager-detector/dist/detect.mjs not supported.
Instead change the require of /var/lib/jenkins/workspace/dev-bwms-deployment/node_modules/package-manager-detector/dist/detect.mjs to a dynamic import() which is available in all CommonJS modules.
```

**解决方案：**

1. **版本锁定修复（已集成到Pipeline - 推荐方案）**
   ```bash
   # 自动检测并降级有问题的ES模块依赖
   node fix-esm-compatibility.js

   # 主要修复的依赖包版本：
   # - package-manager-detector: 0.2.11 (降级到支持CommonJS)
   # - @antfu/install-pkg: 0.1.1 (降级到兼容版本)
   # - @iconify/utils: 2.1.7 (降级到兼容版本)
   # - execa: 5.1.1 (降级到v5，v6+是纯ES模块)
   # - globby: 11.1.0 (降级到v11，v12+是纯ES模块)
   # - chalk: 4.1.2 (降级到v4，v5+是纯ES模块)
   # - ora: 5.4.1 (降级到v5，v6+是纯ES模块)
   # - inquirer: 8.2.6 (降级到v8，v9+是纯ES模块)
   # - node-fetch: 2.7.0 (降级到v2，v3+是纯ES模块)
   # - nanoid: 3.3.7 (降级到v3，v4+是纯ES模块)
   # - picocolors: 1.0.0 (锁定到兼容版本)
   ```

2. **手动修复package.json（如果自动修复失败）**
   ```bash
   # 备份原始package.json
   cp package.json package.json.backup

   # 手动编辑package.json，添加resolutions��段
   cat >> package.json << 'EOF'
   {
     "resolutions": {
       "package-manager-detector": "0.2.11",
       "@antfu/install-pkg": "0.1.1",
       "@iconify/utils": "2.1.7",
       "execa": "5.1.1",
       "globby": "11.1.0",
       "chalk": "4.1.2",
       "ora": "5.4.1",
       "inquirer": "8.2.6",
       "node-fetch": "2.7.0",
       "nanoid": "3.3.7",
       "picocolors": "1.0.0"
     }
   }
   EOF

   # 重新安装依赖
   yarn install --force
   ```

3. **确保不使用ES模块类型**
   ```bash
   # 检查package.json中是否有type: "module"
   grep -q '"type".*"module"' package.json && echo "发现ES模块配置，需要移除"

   # 移除ES模块配置
   sed -i.bak '/"type".*"module"/d' package.json
   ```

**问题：平台架构依赖错误**
```
wanted {"os":"linux","cpu":"arm64"} (current: {"os":"linux","cpu":"x64"})
npm ERR! notsup Unsupported platform for @esbuild/linux-arm64
npm ERR! notsup Valid OS: linux
npm ERR! notsup Valid Arch: arm64
npm ERR! notsup Actual OS: linux
npm ERR! notsup Actual Arch: x64
```

**解决方案：**

1. **预先安装正确架构的依赖（已集成到Pipeline）**
   ```bash
   # 预先安装x64架构的rollup依赖
   yarn add @rollup/rollup-linux-x64-gnu --optional --ignore-engines --ignore-platform

   # 然后安装其他依赖
   yarn install --ignore-platform --ignore-optional
   ```

2. **在Pipeline中使用推荐的yarn参数（已集成）**
   ```bash
   # 跳过平台检查和可选依赖
   yarn install --frozen-lockfile --ignore-platform --ignore-optional
   ```

3. **设置yarn配置跳过平台检查**
   ```bash
   # 在Jenkins Pipeline中添加
   yarn config set target_platform linux
   yarn config set target_arch x64
   yarn config set ignore-optional true
   yarn config set ignore-platform true
   ```

3. **使用.yarnrc文件配置**
   ```bash
   # 在项目根目录创建.yarnrc文件
   echo "ignore-optional true" >> .yarnrc
   echo "ignore-platform true" >> .yarnrc
   echo "target_platform linux" >> .yarnrc
   echo "target_arch x64" >> .yarnrc
   ```

4. **清理并重新安装（如果上述方法无效）**
   ```bash
   # 完全清理并重新安装
   rm -rf node_modules yarn.lock
   yarn cache clean
   yarn install --ignore-platform --ignore-optional
   ```

5. **排除特定平台依赖包**
   ```bash
   # 如果特定包导致问题，可以排除
   yarn install --ignore-scripts --ignore-optional
   ```

**问题：package.json中存在问题依赖**
```
Error: Cannot find module @rollup/rollup-linux-x64-gnu
package.json中包含平台特定的依赖导致构建失败
```

**解决方案：**

1. **自动删除问题依赖（已集成到Pipeline）**
   ```bash
   # Pipeline会自动检查并删除@rollup/rollup-linux-x64-gnu依赖
   # 包括dependencies、devDependencies、optionalDependencies中的依赖
   # 同时删除yarn.lock和package-lock.json以重新解析依赖
   ```

2. **手动删除问题依赖**
   ```bash
   # 方法1：使用yarn移除
   yarn remove @rollup/rollup-linux-x64-gnu

   # 方法2：使用npm移除
   npm uninstall @rollup/rollup-linux-x64-gnu

   # 方法3：手动编辑package.json
   # 删除package.json中所有@rollup/rollup-linux-x64-gnu相关行
   ```

3. **使用Node.js脚本删除**
   ```bash
   node -e "
   const fs = require('fs');
   const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));

   // 删除所有依赖类型中的问题依赖
   ['dependencies', 'devDependencies', 'optionalDependencies'].forEach(depType => {
       if (pkg[depType] && pkg[depType]['@rollup/rollup-linux-x64-gnu']) {
           delete pkg[depType]['@rollup/rollup-linux-x64-gnu'];
           console.log('已从' + depType + '中删除@rollup/rollup-linux-x64-gnu');
       }
   });

   fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
   console.log('package.json已更新');
   "

   # 或者使用sed命令（在Jenkins Pipeline中需要转义反斜杠）
   sed -i.bak '/"@rollup\\/rollup-linux-x64-gnu"/d' package.json
   sed -i.bak2 's/,\\s*,/,/g' package.json
   sed -i.bak3 's/,\\s*}/}/g' package.json
   ```

4. **清理锁文件并重新安装**
   ```bash
   # 删除锁文件
   rm -f yarn.lock package-lock.json

   # 清理缓存
   yarn cache clean
   npm cache clean --force

   # 重新安装
   yarn install --ignore-platform --ignore-optional
   ```

**问题：Docker构建中git二进制文件缺失**
```
error Couldn't find the binary git
info Visit https://yarnpkg.com/en/docs/cli/install for documentation about this command.
ERROR: failed to solve: process "/bin/sh -c yarn install --frozen-lockfile --ignore-platform --ignore-optional" did not complete successfully: exit code 1
```

**解决方案：**

1. **安装必要的系统依赖（已集成到Pipeline）**
   ```dockerfile
   # 配置阿里云镜像源
   RUN echo 'https://mirrors.aliyun.com/alpine/v3.18/main' > /etc/apk/repositories && \
       echo 'https://mirrors.aliyun.com/alpine/v3.18/community' >> /etc/apk/repositories && \
       apk update

   # 安装必要的系统依赖（基于实际需求）
   RUN apk add --no-cache \
       git \
       build-base \
       cairo-dev \
       pango-dev \
       jpeg-dev \
       giflib-dev \
       librsvg-dev \
       python3 \
       make \
       g++
   ```

**问题：Docker构建中Git配置问题**
```
fatal: detected dubious ownership in repository
fatal: unsafe repository
```

**解决方案：**

1. **配置Git全局设置（已集成到Pipeline）**
   ```dockerfile
   # 配置Git全局设置（解决Git相关问题）
   RUN git config --global url.'https://'.insteadOf git:// && \
       git config --global url.'https://'.insteadOf ssh:// && \
       git config --global --add safe.directory /app && \
       git config --global --add safe.directory '*' && \
       git config --global advice.detachedHead false
   ```

**问题：Docker构建中依赖安装超时或失败**
```
yarn install v1.22.22
info No lockfile found.
[1/4] Resolving packages...
error An unexpected error occurred: "Request timeout"
```

**解决方案：**

1. **优化依赖安装配置（已集成到Pipeline）**
   ```dockerfile
   # 配置国内镜像加速
   RUN npm config set registry https://registry.npmmirror.com && \
       yarn config set registry https://registry.npmmirror.com && \
       yarn config set strict-ssl false

   # 创建.npmrc文件以禁用Git相关检查
   RUN echo "git-checks=false" > .npmrc && \
       echo "unsafe-perm=true" >> .npmrc && \
       echo "network-timeout=1000000" >> .npmrc

   # 清理yarn缓存
   RUN yarn cache clean

   # 安装依赖（使用优化的参数）
   RUN yarn install --network-timeout 1000000 --ignore-engines --ignore-scripts --no-git-tag-version --ignore-platform --ignore-optional
   ```

**问题：Docker构建中yarn安装错误**
```
npm error code EEXIST
npm error path /usr/local/bin/yarn
npm error EEXIST: file already exists
npm error File exists: /usr/local/bin/yarn
ERROR: failed to solve: process "/bin/sh -c npm install -g yarn" did not complete successfully: exit code 1
```

**解决方案：**

1. **使用智能yarn检查（已集成到Pipeline）**
   ```dockerfile
   # 检查并确保yarn可用（node:20.18.0-alpine已预装yarn）
   RUN yarn --version || npm install -g yarn --force
   ```

   这个命令会：
   - 首先检查yarn是否已安装并可用
   - 如果yarn不存在或有问题，才使用`--force`参数强制安装

**问题：Docker构建权限错误**
```
permission denied while trying to connect to the Docker daemon socket
Got permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock
```

**解决方案：**

1. **配置Jenkins用户Docker权限（推荐）**
   ```bash
   # 将jenkins用户添加到docker组
   sudo usermod -aG docker jenkins

   # 重启Jenkins服务
   sudo systemctl restart jenkins

   # 验证权限
   sudo -u jenkins docker ps
   ```

2. **检查Docker服务状态**
   ```bash
   # 检查Docker是否运行
   sudo systemctl status docker

   # 启动Docker服务
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

**问题：Rollup原生模块错误**
```
Error: Cannot find module @rollup/rollup-linux-x64-gnu. yarn has a bug related to optional dependencies
```

**解决方案：**

1. **Docker构建方案（推荐，已集成）**
   ```bash
   # Docker容器环境完全避免原生模块兼容性问题
   # 使用 --ignore-platform --ignore-optional 参数
   # 在隔离的容器环境中构建，避免主机环境影响
   ```

2. **传统修复方法（仅在直接构建时使用）**
   ```bash
   # 清理所有依赖和缓存
   rm -rf node_modules yarn.lock
   yarn cache clean

   # 重新安装
   yarn install --ignore-platform --ignore-optional
   ```

2. **手动安装Rollup原生依赖**
   ```bash
   # 手动安装缺失的原生模块
   yarn add @rollup/rollup-linux-x64-gnu --optional

   # 或者安装所有Rollup原生依赖
   yarn add @rollup/rollup-linux-x64-gnu @rollup/rollup-linux-arm64-gnu --optional
   ```

3. **使用yarn install替代yarn add**
   ```bash
   # 如果有yarn.lock，使用install命令
   yarn install --frozen-lockfile --ignore-optional
   ```

4. **降级Rollup版本**
   ```bash
   # 如果问题持续，可以尝试降级Rollup
   yarn add rollup@3.29.4 --dev
   ```

**问题：Vite ESM模块兼容性错误**
```
The CJS build of Vite's Node API is deprecated
Error [ERR_REQUIRE_ESM]: require() of ES Module not supported
failed to load config from vite.config.ts
```

**解决方案：**

1. **自动ESM兼容性修复（已集成到Pipeline）**
   ```bash
   # Pipeline会自动检查并修复ESM兼容性问题
   # 如果package.json中没有type字段，会自动添加
   ```

2. **手动修复package.json**
   ```bash
   # 在项目根目录的package.json中添加
   {
     "type": "module",
     "scripts": {
       "build": "vite build",
       "dev": "vite"
     }
   }
   ```

3. **使用Vite的ESM API**
   ```bash
   # 确保使用最新版本的Vite
   yarn add vite@latest --dev

   # 清理缓存并重新安装
   rm -rf node_modules yarn.lock
   yarn cache clean
   yarn install
   ```

4. **修复vite.config.ts配置**
   ```typescript
   // 确保vite.config.ts使用ESM语法
   import { defineConfig } from 'vite'
   import vue from '@vitejs/plugin-vue'

   export default defineConfig({
     plugins: [vue()],
     // 其他配置...
   })
   ```

5. **降级到兼容版本**
   ```bash
   # 如果ESM问题持续，可以降级到稳定版本
   yarn add vite@4.5.0 --dev
   yarn add @vitejs/plugin-vue@4.5.0 --dev
   ```

6. **使用Node.js兼容模式**
   ```bash
   # 设置Node.js环境变量
   export NODE_OPTIONS="--loader ts-node/esm"

   # 或者在构建时指定
   NODE_OPTIONS="--loader ts-node/esm" yarn build
   ```

**问题：前端构建内存不足**
```bash
# 在 Jenkinsfile 中增加内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 或者在 Jenkins 服务器上增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

**问题：Composer 依赖安装失败**
```bash
# 检查 PHP 版本
php --version

# 清理 Composer 缓存
composer clear-cache

# 手动安装 Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

**问题：Git 克隆错误**
```
ERROR: Error fetching remote repo 'origin'
ERROR: Error cloning remote repo 'origin'
```

**解决方案：**

1. **检查Git分支是否存在**
   ```bash
   # 在Jenkins服务器上测试分支是否存在
   git ls-remote https://gitee.com/bingo-limited/bwms.git

   # 查看所有分支
   git ls-remote --heads https://gitee.com/bingo-limited/bwms.git
   ```

2. **检查Git仓库地址和协议**
   ```
   SSH格式：*************:bingo-limited/bwms.git
   HTTPS格式：https://gitee.com/bingo-limited/bwms.git

   注意：SSH格式需要SSH密钥，HTTPS格式需要用户名密码
   ```

3. **配置SSH密钥凭据（推荐）**

   **步骤1：生成SSH密钥**
   ```bash
   # 在Jenkins服务器上生成SSH密钥
   sudo su - jenkins
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

   # 查看公钥
   cat ~/.ssh/id_rsa.pub
   ```

   **步骤2：在Gitee中添加SSH公钥**
   ```
   1. 登录Gitee → 设置 → SSH公钥
   2. 点击"添加公钥"
   3. 将上面的公钥内容粘贴进去
   4. 设置标题：Jenkins部署密钥
   ```

   **步骤3：在Jenkins中添加SSH私钥**
   ```
   Jenkins 管理 → 凭据管理 → 全局凭据 → 添加凭据

   配置：
   - 类型：SSH Username with private key
   - 范围：Global
   - ID：bwm-gitee-ssh-key
   - 描述：Gitee SSH密钥
   - 用户名：git
   - 私钥：选择"Enter directly"，粘贴私钥内容
   ```

   **步骤4：获取私钥内容**
   ```bash
   # 在Jenkins服务器上
   sudo cat /var/lib/jenkins/.ssh/id_rsa
   ```

4. **测试SSH连接**
   ```bash
   # 在Jenkins服务器上测试
   sudo su - jenkins
   ssh -T *************

   # 应该看到类似输出：
   # Hi bingo-limited! You've successfully authenticated...
   ```

5. **修正Pipeline脚本**
   ```groovy
   git branch: 'dev',  // 或者 'main', 'master'
       url: '*************:bingo-limited/bwms.git',
       credentialsId: 'bwm-gitee-ssh-key'
   ```

6. **备选方案：使用HTTPS格式**
   ```
   Jenkins 管理 → 凭��管理 → 全局凭据 → 添加凭据

   配置：
   - 类型：Username with password
   - 用户名：您的Gitee用户名
   - 密码：您的Gitee密码或Personal Access Token
   - ID：gitee-credentials
   - 描述：Gitee仓库访问凭据
   ```

3. **获取Gitee Personal Access Token（推荐）**
   ```
   1. 登录Gitee → 设置 → 私人令牌
   2. 点击"生成新令牌"
   3. 选择权限：projects (读取仓库)
   4. 复制生成的token
   5. 在Jenkins凭据中使用token作为密码
   ```

4. **验证仓库访问权限**
   ```bash
   # 在Jenkins服务器上测试
   git ls-remote https://gitee.com/bingo-limited/bwms.git
   ```

5. **检查网络连接**
   ```bash
   # 测试是否能访问Gitee
   curl -I https://gitee.com

   # 检查DNS解析
   nslookup gitee.com
   ```

6. **如果是私有仓库，确保账号有访问权限**
   ```
   联系仓库管理员确认您的账号有读取权限
   ```

#### 3. SSH 连接问题

**问题：SSH 密钥认证失败**
```bash
# 在 Jenkins 服务器上测试 SSH 连接
sudo su - jenkins
ssh -v <EMAIL>

# 检查私钥权限
chmod 600 ~/.ssh/id_rsa

# 检查目标服务器的 authorized_keys
# 在目标服务器上：
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

**问题：SSH 连��超时**
```bash
# 检查防火墙设置
sudo ufw status

# 检查 SSH 服务状态
sudo systemctl status ssh

# 测试网络连接
telnet your-target-server.com 22
```

#### 4. 部署服务器问题

**问题：文件权限错误**
```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /var/www/bwms/current/storage
sudo chown -R www-data:www-data /var/www/bwms/current/bootstrap/cache
sudo chmod -R 755 /var/www/bwms/current/storage
sudo chmod -R 755 /var/www/bwms/current/bootstrap/cache
```

**问题：Nginx 配置错误**
```bash
# 测试 Nginx 配置
sudo nginx -t

# 重新加载 Nginx 配置
sudo systemctl reload nginx

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

**问题：PHP-FPM 错误**
```bash
# 检查 PHP-FPM 状态
sudo systemctl status php8.2-fpm

# 查看 PHP-FPM 错误日志
sudo tail -f /var/log/php8.2-fpm.log

# 重启 PHP-FPM
sudo systemctl restart php8.2-fpm
```

**问题：数据库连接失败**
```bash
# 检查数据库服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u your_db_user -p -h 127.0.0.1

# 检查 .env 文件配置
cat /var/www/bwms/.env | grep DB_
```

#### 5. 应用相关问题

**问题：Laravel 应用错误**
```bash
# 检查 Laravel 日志
tail -f /var/www/bwms/current/storage/logs/laravel.log

# 清理应用缓存
cd /var/www/bwms/current
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# 重新生成缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

**问题：存储链接丢失**
```bash
# 重新创建存储链接
cd /var/www/bwms/current
php artisan storage:link
```

### 调试技巧

#### 1. 启用详细日志

在 Jenkinsfile 中添加调试信息：
```groovy
sh '''
    set -x  # 启用命令跟踪
    # 你的命令
    set +x  # 关闭命令跟踪
'''
```

#### 2. 分步测试

将复杂的部署步骤分解为多个小步骤，逐一测试：
```bash
# 测试 SSH 连接
ssh <EMAIL> "echo 'SSH connection successful'"

# 测试文件上传
scp test.txt <EMAIL>:/tmp/

# 测试命令执行
ssh <EMAIL> "ls -la /var/www/"
```

#### 3. 使用 Jenkins 调试功能

- 使用 **"重新构建"** 功能重新执行失败的构建
- 使用 **"Replay"** 功能修改 Jenkinsfile 并重新执行
- 查看 **"工作空间"** 了解构建过程中的文件状态

### 性能优化

#### 1. 构建优化

```groovy
// 在 Jenkinsfile 中启用并行构建
parallel {
    stage('前端构建') {
        // 前端构建步骤
    }
    stage('后端处理') {
        // 后端处理步骤
    }
}
```

#### 2. 缓存优化

```bash
# 用 npm 缓存
npm ci --cache /tmp/npm-cache

# 使用 Composer 缓存
composer install --no-dev --optimize-autoloader --apcu-autoloader
```

#### 3. 部署优化

```bash
# 使用增量部署
rsync -avz --delete /source/ deploy@server:/destination/

# 使用压缩传输
tar -czf - . | ssh deploy@server "cd /var/www/bwms && tar -xzf -"
```

## 📊 监控和维护

### 1. 监控指标

- **构建成功率**：监控最近构建的成功率
- **构建时间**：跟踪构建时间趋势
- **部署频率**：统计部署频率
- **回滚次数**：记录回滚操作

### 2. 定期维护

```bash
# 每周执行的维护任务
#!/bin/bash

# 清理 Jenkins 工作空间
find /var/lib/jenkins/workspace -name "node_modules" -type d -mtime +7 -exec rm -rf {} +

# 清理旧的构建文件
find /tmp -name "bwms-*.tar.gz" -mtime +3 -delete

# 检查磁盘空间
df -h

# 检查服务状态
systemctl status jenkins nginx mysql php8.2-fpm
```

### 3. 安全建议

1. **使用 HTTPS 部署**
   ```nginx
   server {
       listen 443 ssl;
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       # 其他配置...
   }
   ```

2. **定期更新依赖包**
   ```bash
   # 定期更新系统包
   sudo apt update && sudo apt upgrade

   # 更新 Node.js 依赖
   npm audit fix

   # 更新 Composer 依赖
   composer update
   ```

3. **配置防火墙规则**
   ```bash
   # 只允许必要的端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw allow 8080  # Jenkins (仅限内网)
   sudo ufw enable
   ```

4. **使用专用的部署用户**
   - 不要使用 root 用户进行部署
   - 为部署用户设置最小权限
   - 定期轮换 SSH 密钥

5. **定期备份数据库和代码**
   - 设置自动备份脚本
   - 测试备份恢复流程
   - 将备份存储在不同位置

## 📝 总结

通过本指南，您应该能够：

1. ✅ **安装和配置 Jenkins**
2. ✅ **设置 SSH 密钥认证**
3. ✅ **创建自动化部署流水线**
4. ✅ **配置目标服务器环境**
5. ✅ **执行首次部署**
6. ✅ **设置自动化触发**
7. ✅ **处理常见问题**
8. ✅ **进行日常维护**

### 下一步建议

1. **测试环境**：为测试分支设置单独的部署流��线
2. **监控告警**：集成监控系统，设置构建失败告警
3. **代码质量**：集成代码质量检查工具（如 SonarQube）
4. **容器化**：考虑使用 Docker 容器化部署
5. **多环境管理**：设置开发、测试、生产多环境部署

---

**重要提醒**：
- 请根据实际环境调整配置参数
- 在生产环境部署前，务必在测试环境充分验证
- 定期备份重要数据
- 保持系统和依赖包的更新

如果在部署过程中遇到问题，请参考故障排除部分，或者查看相关日志文件进行诊断。