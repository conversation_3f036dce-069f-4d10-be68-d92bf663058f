<?php

declare(strict_types=1);

namespace Modules\LiveChannel\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class LiveChannel extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 表名（不包含前缀，系统会自动添加）
     */
    protected $table = 'channels';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'channel_num',
        'name',
        'name_hk',
        'description',
        'description_hk',
        'cover_image_url',
        'stream_url',
        'stream_key',
        'start_time',
        'end_time',
        'is_audio_only',
        'is_breaking_news',
        'is_hk_only',
        'live_status',
        'status',
        'sort',
        'created_by',
        'updated_by',
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'start_time' => 'datetime:Y-m-d H:i:s',
        'end_time' => 'datetime:Y-m-d H:i:s',
        'is_audio_only' => 'boolean',
        'is_breaking_news' => 'boolean',
        'is_hk_only' => 'boolean',
        'live_status' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'stream_key',
    ];

    /**
     * 追加的属性
     */
    protected $appends = [
        'live_status_text',
        'status_text',
        'is_audio_only_text',
        'is_breaking_news_text',
        'is_hk_only_text',
    ];

    /**
     * 直播状态文本
     */
    public function getLiveStatusTextAttribute(): string
    {
        return match($this->live_status) {
            0 => '关闭',
            1 => '直播中',
            2 => '暂停',
            default => '未知',
        };
    }

    /**
     * 状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? '启用' : '禁用';
    }

    /**
     * 是否仅音频文本
     */
    public function getIsAudioOnlyTextAttribute(): string
    {
        return $this->is_audio_only ? '是' : '否';
    }

    /**
     * 是否突发直播文本
     */
    public function getIsBreakingNewsTextAttribute(): string
    {
        return $this->is_breaking_news ? '是' : '否';
    }

    /**
     * 是否仅限香港地区文本
     */
    public function getIsHkOnlyTextAttribute(): string
    {
        return $this->is_hk_only ? '是' : '否';
    }

    /**
     * 创建人关联
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * 更新人关联
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'updated_by');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：直播中
     */
    public function scopeLive(Builder $query): Builder
    {
        return $query->where('live_status', 1);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort', 'asc')->orderBy('id', 'desc');
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch(Builder $query, string $keyword): Builder
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('name_hk', 'like', "%{$keyword}%")
              ->orWhere('channel_num', 'like', "%{$keyword}%");
        });
    }

    /**
     * 作用域：按时间范围
     */
    public function scopeTimeRange(Builder $query, string $startDate = null, string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('start_time', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('end_time', '<=', $endDate);
        }
        
        return $query;
    }

    /**
     * 检查频道名称是否已存在
     */
    public static function isNameExists(string $name, int $excludeId = null): bool
    {
        $query = static::where('name', $name);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 检查频道编号是否已存在
     */
    public static function isChannelNumExists(string $channelNum, int $excludeId = null): bool
    {
        $query = static::where('channel_num', $channelNum);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 生成频道编号
     */
    public static function generateChannelNum(): string
    {
        $lastChannel = static::orderBy('id', 'desc')->first();
        
        if (!$lastChannel || !$lastChannel->channel_num) {
            return 'CH001';
        }
        
        // 确保channel_num是字符串格式
        $channelNum = (string) $lastChannel->channel_num;
        $number = (int) substr($channelNum, 2);
        return 'CH' . str_pad((string)($number + 1), 3, '0', STR_PAD_LEFT);
    }


} 