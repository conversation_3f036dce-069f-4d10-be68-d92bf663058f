import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import fs from 'fs'
import fixHtmlPaths from './vite-plugin-fix-html-paths'

// @ts-ignore
export default defineConfig(({ mode }) => {
  // 获取项目根目录（resources 的父级目录）
  const projectRoot = path.resolve(__dirname, '../..')
  const env = loadEnv(mode, projectRoot, ['VITE_', 'FRONTEND_'])
  return {
    base: '/console/',
    root: __dirname,
    plugins: [vue(), fixHtmlPaths()],
    resolve: {
      alias: {
        '/admin': path.resolve(__dirname, '../admin'),
        '@/module': path.resolve(projectRoot, 'Modules'),
        '@/public': path.resolve(__dirname, '../../public'),
        '@/frontend': path.resolve(__dirname, './'),
        '@admin': path.resolve(__dirname, '../admin'),
        '/resources': path.resolve(__dirname, '../../resources'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {},
      },
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    publicDir: path.resolve(__dirname, 'public'),
    preprocessorOptions: {
      scss: {},
    },
    server: {
      host: '127.0.0.1',
      port: 8001,
      open: true, // 自动打开浏览器
      cors: true, // 允许跨域
      strictPort: false, // 端口占用直接退出
      hmr: true,
      fs: {
        allow: ['./'],
      },
      watch: {
        usePolling: true,
      },
      // 配置静态文件服务
      proxy: {
        '/Vendor': {
          target: 'http://127.0.0.1:8000',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/Vendor/, '/Vendor'),
        },
      },
      mimeTypes: {
        'application/javascript': ['js', 'mjs'],
      },
    },

    build: {
      chunkSizeWarningLimit: 20000,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          pure_funcs: ['console.log', 'console.info'],
          drop_debugger: true,
        },
      },
      outDir: path.resolve(__dirname, '../../public/frontend'),
      emptyOutDir: true,
      rollupOptions: {
        input: path.resolve(__dirname, 'index.html'),
        output: {
          chunkFileNames: 'dist/js/[name]-[hash].js',
          entryFileNames: 'dist/js/[name]-[hash].js',
          assetFileNames: 'dist/[ext]/[name]-[hash].[ext]',
        },
      },
    },
    // 添加环境变量前缀
    define: {
      'import.meta.env': JSON.stringify({
        ...env,
        MODE: mode,
        DEV: mode === 'development',
        PROD: mode === 'production',
        SSR: false,
      }),
      'import.meta.env.VITE_API_BASE_URL': JSON.stringify(env.VITE_API_BASE_URL),
      'import.meta.env.VITE_FRONTEND_APP_NAME': JSON.stringify(env.VITE_FRONTEND_APP_NAME),
      'import.meta.env.VITE_URL': JSON.stringify(env.VITE_URL),
      // 添加其他需要的环境变量
    },
  }
})
