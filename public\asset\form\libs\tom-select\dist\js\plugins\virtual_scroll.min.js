(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.virtual_scroll=factory())})(this,function(){"use strict";const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const iterate=(object,callback)=>{if(Array.isArray(object)){object.forEach(callback)}else{for(var key in object){if(object.hasOwnProperty(key)){callback(object[key],key)}}}};const addClasses=(elmts,...classes)=>{var norm_classes=classesArray(classes);elmts=castAsArray(elmts);elmts.map(el=>{norm_classes.map(cls=>{el.classList.add(cls)})})};const classesArray=args=>{var classes=[];iterate(args,_classes=>{if(typeof _classes==="string"){_classes=_classes.trim().split(/[\11\12\14\15\40]/)}if(Array.isArray(_classes)){classes=classes.concat(_classes)}});return classes.filter(Boolean)};const castAsArray=arg=>{if(!Array.isArray(arg)){arg=[arg]}return arg};function plugin(){const self=this;const orig_canLoad=self.canLoad;const orig_clearActiveOption=self.clearActiveOption;const orig_loadCallback=self.loadCallback;var pagination={};var dropdown_content;var loading_more=false;var load_more_opt;var default_values=[];if(!self.settings.shouldLoadMore){self.settings.shouldLoadMore=()=>{const scroll_percent=dropdown_content.clientHeight/(dropdown_content.scrollHeight-dropdown_content.scrollTop);if(scroll_percent>.9){return true}if(self.activeOption){var selectable=self.selectable();var index=Array.from(selectable).indexOf(self.activeOption);if(index>=selectable.length-2){return true}}return false}}if(!self.settings.firstUrl){throw"virtual_scroll plugin requires a firstUrl() method"}self.settings.sortField=[{field:"$order"},{field:"$score"}];const canLoadMore=query=>{if(typeof self.settings.maxOptions==="number"&&dropdown_content.children.length>=self.settings.maxOptions){return false}if(query in pagination&&pagination[query]){return true}return false};const clearFilter=(option,value)=>{if(self.items.indexOf(value)>=0||default_values.indexOf(value)>=0){return true}return false};self.setNextUrl=(value,next_url)=>{pagination[value]=next_url};self.getUrl=query=>{if(query in pagination){const next_url=pagination[query];pagination[query]=false;return next_url}pagination={};return self.settings.firstUrl.call(self,query)};self.hook("instead","clearActiveOption",()=>{if(loading_more){return}return orig_clearActiveOption.call(self)});self.hook("instead","canLoad",query=>{if(!(query in pagination)){return orig_canLoad.call(self,query)}return canLoadMore(query)});self.hook("instead","loadCallback",(options,optgroups)=>{if(!loading_more){self.clearOptions(clearFilter)}else if(load_more_opt){const first_option=options[0];if(first_option!==undefined){load_more_opt.dataset.value=first_option[self.settings.valueField]}}orig_loadCallback.call(self,options,optgroups);loading_more=false});self.hook("after","refreshOptions",()=>{const query=self.lastValue;var option;if(canLoadMore(query)){option=self.render("loading_more",{query:query});if(option){option.setAttribute("data-selectable","");load_more_opt=option}}else if(query in pagination&&!dropdown_content.querySelector(".no-results")){option=self.render("no_more_results",{query:query})}if(option){addClasses(option,self.settings.optionClass);dropdown_content.append(option)}});self.on("initialize",()=>{default_values=Object.keys(self.options);dropdown_content=self.dropdown_content;self.settings.render=Object.assign({},{loading_more:()=>{return`<div class="loading-more-results">Loading more results ... </div>`},no_more_results:()=>{return`<div class="no-more-results">No more results</div>`}},self.settings.render);dropdown_content.addEventListener("scroll",()=>{if(!self.settings.shouldLoadMore.call(self)){return}if(!canLoadMore(self.lastValue)){return}if(loading_more)return;loading_more=true;self.load.call(self,self.lastValue)})})}return plugin});