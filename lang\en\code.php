<?php

use Bingo\Enums\Code;

return [
    Code::class => array(
        Code::SUCCESS->name => 'success',
        Code::LOST_LOGIN->name => 'lost login name',
        Code::VALIDATE_FAILED->name => 'validate failed',
        Code::PERMISSION_FORBIDDEN->name => 'permission forbidden',
        Code::LOGIN_FAILED->name => 'login failed',
        Code::FAILED->name => 'failed',
        Code::LOGIN_EXPIRED->name => 'login expired',
        Code::LOGIN_BLACKLIST->name => 'login blacklist',
        Code::USER_FORBIDDEN->name => 'user forbidden',
    )
];
