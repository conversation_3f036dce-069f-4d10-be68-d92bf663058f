/*!
 * @package   yii2-widget-activeform
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2015 - 2023
 * @version   1.6.4
 *
 * Active Field Hints Display Module
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2015, Kart<PERSON>, Krajee.com
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */
var kvBs4InitForm=function(){};!function(t){"use strict";var e,i;e={NAMESPACE:".kvActiveField",isEmpty:function(e,i){return void 0===e||e===[]||null===e||""===e||i&&""===t.trim(e)}},(i=function(e,i){var n=this;n.$element=t(e),t.each(i,function(t,e){n[t]=e}),n.init()}).prototype={constructor:i,init:function(){var i,n=this,s=n.$element,a=s.find(".kv-hint-block"),o=a.html(),l=s.find(".kv-hintable");a.hide(),e.isEmpty(o)||(e.isEmpty(n.contentCssClass)||(i=t(document.createElement("span")).addClass(n.contentCssClass).append(o),i=t(document.createElement("span")).append(i),o=i.html(),i.remove()),l.each(function(){var e=t(this);e.hasClass("kv-type-label")?e.removeClass(n.labelCssClass).addClass(n.labelCssClass):e.removeClass("hide "+n.iconCssClass).addClass(n.iconCssClass),e.hasClass("kv-hint-click")&&n.listen("click",e,o),e.hasClass("kv-hint-hover")&&n.listen("hover",e,o)}),n.hideOnEscape&&t(document).on("keyup",function(e){l.each(function(){var i=t(this);27===e.which&&i.popover("hide")})}),n.hideOnClickOut&&t("body").on("click",function(e){l.each(function(){var i=t(this);i.is(e.target)||0!==i.has(e.target).length||0!==t(".popover").has(e.target).length||i.popover("hide")})}))},listen:function(t,i,n){var s={html:!0,trigger:"manual",content:n,title:this.title,placement:this.placement,container:this.container||!1,animation:!!this.animation,delay:this.delay,selector:this.selector};e.isEmpty(this.template)||(s.template=this.template),e.isEmpty(this.viewport)||(s.viewport=this.viewport),i.popover(s),"click"!==t?(this.raise(i,"mouseenter",function(){i.popover("show")}),this.raise(i,"mouseleave",function(){i.popover("hide")})):this.raise(i,"click",function(t){t.preventDefault(),i.popover("toggle")})},raise:function(t,i,n){i+=e.NAMESPACE,t.off(i).on(i,n)}},t.fn.activeFieldHint=function(e){var n=Array.apply(null,arguments);return n.shift(),this.each(function(){var s=t(this),a=s.data("activeFieldHint"),o="object"==typeof e&&e;a||s.data("activeFieldHint",a=new i(this,t.extend({},t.fn.activeFieldHint.defaults,o,t(this).data()))),"string"==typeof e&&a[e].apply(a,n)})},t.fn.activeFieldHint.defaults={labelCssClass:"kv-hint-label",iconCssClass:"kv-hint-icon",contentCssClass:"kv-hint-content",hideOnEscape:!1,hideOnClickOut:!1,title:"",placement:"right",container:"body",delay:0,animation:!0,selector:!1,template:"",viewport:""},kvBs4InitForm=function(){var e=[".form-control",".custom-control-input",".custom-select",".custom-range",".custom-file-input"],i=e.join(","),n=".has-error "+e.join(",.has-error "),s=".has-success "+e.join(",.has-success "),a=function(t){t.find(i).removeClass("is-valid is-invalid")};t("form").on("afterValidateAttribute",function(){var e=t(this);a(e),(e.find(".has-error").length||e.find(".has-success").length)&&(e.find(n).addClass("is-invalid"),e.find(s).addClass("is-valid"))}).on("reset",function(){var e=t(this);setTimeout(function(){a(e)},100)})}}(window.jQuery);
