<template>
  <div class="piece-box news-box">
    <div class="piece-tit flex justify-between items-center">
      <div class="tit">BINGO Channel</div>
      <div class="tab right-box flex items-center">
        <div class="tab-list flex">
          <div class="tab-item mx-4" v-for="item in newsBoxTab" :key="item.id" @click="tabChange(item)" :class="{ active: checkedTab.tabVal === item.tabVal }">
            <span class="text">{{ item.text }}</span>
            <div class="line"></div>
          </div>
        </div>
        <el-button color="#FFD100" round>more</el-button>
      </div>
    </div>
    <div class="tab-pane">
      <template v-for="item in newsBoxTab" :key="item.id">
        <div class="pane-item" v-if="item.tabVal === checkedTab.tabVal">
          <div class="news-list flex">
            <div class="news-item px-2.5 w-4/12" v-for="newsInfo in item.list" :key="newsInfo.id">
              <div class="news-box p-3 relative h-full">
                <div class="news-tit mb-2.5 text-sm">{{ newsInfo.tit }}</div>
                <div class="pic" v-if="newsInfo.imgUrl">
                  <img :src="newsInfo.imgUrl" class="img" />
                </div>
                <div class="news-desc text-sm" v-if="newsInfo.desc">{{ newsInfo.desc }}</div>
                <div class="news-time">{{ newsInfo.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'

interface TabItem {
  id: number
  text: string
  tabVal: string
  list: newsItem[]
}
interface newsItem {
  id: number
  isHot: boolean
  tit: string
  desc?: string
  imgUrl?: string
  time: string
}
let newsBoxTab = reactive([
  {
    id: 0,
    text: 'All',
    tabVal: 'All',
    list: [
      {
        id: 0,
        isHot: true,
        tit: 'Understanding Fundamentals of SEO 1',
        imgUrl: 'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
        time: '20/02/2024',
      },
      {
        id: 1,
        isHot: true,
        tit: 'Techniques for Captivating Product Photogragpy 2',
        desc: 'A detailed tutorial on employing lighting, angles, and post-processing to achieve professional-quality product photographs that resonate with customers.',
        time: '11/02/2024',
      },
      {
        id: 2,
        isHot: true,
        tit: 'Effective Management for Website Administrators 3',
        desc: 'An in-depth look at the tools, tasks, and best practices that every website administrator should be familiar with to ensure smooth backend operations.',
        time: '10/01/2024',
      },
    ],
  },
  {
    id: 1,
    text: 'BWMS Updates',
    tabVal: 'BWMS Updates',
    list: [
      {
        id: 0,
        isHot: true,
        tit: 'Understanding Fundamentals of SEO 4',
        imgUrl: 'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
        time: '20/02/2024',
      },
      {
        id: 1,
        isHot: true,
        tit: 'Techniques for Captivating Product Photogragpy 5',
        desc: 'A detailed tutorial on employing lighting, angles, and post-processing to achieve professional-quality product photographs that resonate with customers.',
        time: '11/02/2024',
      },
      {
        id: 2,
        isHot: true,
        tit: 'Effective Management for Website Administrators 6',
        desc: 'An in-depth look at the tools, tasks, and best practices that every website administrator should be familiar with to ensure smooth backend operations.',
        time: '10/01/2024',
      },
    ],
  },
  {
    id: 2,
    text: "BINGO' News",
    tabVal: "BINGO' News",
    list: [
      {
        id: 0,
        isHot: true,
        tit: 'Understanding Fundamentals of SEO 7',
        imgUrl: 'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
        time: '20/02/2024',
      },
      {
        id: 1,
        isHot: true,
        tit: 'Techniques for Captivating Product Photogragpy 8',
        desc: 'A detailed tutorial on employing lighting, angles, and post-processing to achieve professional-quality product photographs that resonate with customers.',
        time: '11/02/2024',
      },
      {
        id: 2,
        isHot: true,
        tit: 'Effective Management for Website Administrators 9',
        desc: 'An in-depth look at the tools, tasks, and best practices that every website administrator should be familiar with to ensure smooth backend operations.',
        time: '10/01/2024',
      },
    ],
  },
  {
    id: 3,
    text: 'Learning Resources',
    tabVal: 'Learning Resources',
    list: [
      {
        id: 0,
        isHot: true,
        tit: 'Understanding Fundamentals of SEO 10',
        imgUrl: 'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
        time: '20/02/2024',
      },
      {
        id: 1,
        isHot: true,
        tit: 'Techniques for Captivating Product Photogragpy 11',
        desc: 'A detailed tutorial on employing lighting, angles, and post-processing to achieve professional-quality product photographs that resonate with customers.',
        time: '11/02/2024',
      },
      {
        id: 2,
        isHot: true,
        tit: 'Effective Management for Website Administrators 12',
        desc: 'An in-depth look at the tools, tasks, and best practices that every website administrator should be familiar with to ensure smooth backend operations.',
        time: '10/01/2024',
      },
    ],
  },
])
let checkedTab = ref({
  id: 0,
  text: 'All',
  tabVal: 'All',
  list: [],
} as TabItem)
const tabChange = (item: TabItem) => {
  checkedTab.value = item
}
</script>

<style lang="scss" scoped>
.news-box {
  .tab {
    .tab-list {
      .tab-item {
        position: relative;
        cursor: pointer;

        &.active,
        &:hover {
          .text {
            color: #333;
            -webkit-text-stroke: 0.5px #333;
          }

          .line {
            width: 100%;
          }
        }

        .text {
          -webkit-text-stroke: 0.5px transparent;
          transition: color 0.35s ease-in-out;
        }

        .line {
          width: 0;
          height: 2px;
          background-color: #ffd100;
          transition: width 0.35s ease-in-out;

          position: absolute;
          left: 50%;
          top: 100%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .tab-pane {
    .pane-item {
      .news-list {
        margin-left: -0.625rem;
        margin-right: -0.625rem;

        .news-item {
          .news-box {
            padding-bottom: calc(0.625rem + 35px);
            border-radius: 10px;
            background-color: #fff;

            .news-tit {
              color: #333333;
              font-weight: bold;
            }

            .pic {
              border-radius: 10px;
              overflow: hidden;
            }

            .news-desc {
              color: #bfbfbf;
            }

            .news-time {
              position: absolute;
              left: 0.75rem;
              bottom: 0.75rem;
              font-size: 0.625rem;
              color: #333333;
            }
          }
        }
      }
    }
  }
}
</style>
