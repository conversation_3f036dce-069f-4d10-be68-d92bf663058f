# OpenApi 路由模板

## 概述

OpenApi 路由用于定义模块的开放 API 接口路由。本文档提供了 OpenApi 路由的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\YourModule\OpenApi\Controllers\YourController;

/*
|--------------------------------------------------------------------------
| OpenApi Routes
|--------------------------------------------------------------------------
|
| 模块的开放 API 路由定义
|
*/

Route::prefix('openapi/v1/your-module')->middleware(['openapi', 'auth:openapi'])->group(function () {
    // 基础接口路由
    Route::get('/', [YourController::class, 'index'])->name('openapi.your-module.index');
    Route::get('/{id}', [YourController::class, 'show'])->name('openapi.your-module.show');
    Route::post('/', [YourController::class, 'store'])->name('openapi.your-module.store');
    Route::put('/{id}', [YourController::class, 'update'])->name('openapi.your-module.update');
    Route::delete('/{id}', [YourController::class, 'destroy'])->name('openapi.your-module.destroy');
    
    // 批量操作路由
    Route::prefix('batch')->group(function () {
        Route::post('create', [YourController::class, 'batchCreate'])->name('openapi.your-module.batch.create');
        Route::post('update', [YourController::class, 'batchUpdate'])->name('openapi.your-module.batch.update');
        Route::post('delete', [YourController::class, 'batchDelete'])->name('openapi.your-module.batch.delete');
    });
    
    // 查询接口路由
    Route::prefix('query')->group(function () {
        Route::get('list', [YourController::class, 'queryList'])->name('openapi.your-module.query.list');
        Route::get('count', [YourController::class, 'queryCount'])->name('openapi.your-module.query.count');
        Route::get('exists', [YourController::class, 'queryExists'])->name('openapi.your-module.query.exists');
    });
    
    // 同步接口路由
    Route::prefix('sync')->group(function () {
        Route::post('push', [YourController::class, 'syncPush'])->name('openapi.your-module.sync.push');
        Route::get('pull', [YourController::class, 'syncPull'])->name('openapi.your-module.sync.pull');
        Route::get('status', [YourController::class, 'syncStatus'])->name('openapi.your-module.sync.status');
    });
});
```

## 规范要求

1. 路由分组
   - 使用版本前缀
   - 使用 OpenApi 中间件
   - 按功��分组路由
   - 使用标准 RESTful

2. 路由命名
   - 使用 openapi 前缀
   - 使用版本号
   - 使用连字符分隔
   - 保持命名一致性

3. 中间件使用
   - OpenApi 中间件
   - API 认证
   - 访问控制
   - 限流控制

4. 接口版本
   - 使用 URL 版本
   - 版本号规范
   - 版本兼容
   - 版本升级

## 最佳实践

1. 接口版本控制
```php
// v1 版本路由
Route::prefix('openapi/v1')->group(function () {
    Route::get('users', [UserController::class, 'index']);
});

// v2 版本路由
Route::prefix('openapi/v2')->group(function () {
    Route::get('users', [UserControllerV2::class, 'index']);
});
```

2. 访问控制
```php
// 使用 API Key 认证
Route::middleware(['auth.apikey'])->group(function () {
    Route::get('data', [DataController::class, 'index']);
});

// 使用 OAuth2 认证
Route::middleware(['auth.oauth'])->group(function () {
    Route::get('users', [UserController::class, 'index']);
});
```

## 常见问题

1. 接口限流
```php
// 好的实践 - 使用限流中间件
Route::middleware(['throttle:60,1'])->group(function () {
    Route::get('data', [DataController::class, 'index']);
});

// 不好的实践 - 没有限流控制
Route::get('data', [DataController::class, 'index']);
```

2. 错误处理
```php
// 好的实践 - 统一的错误响应
Route::get('users', function () {
    try {
        // 业务逻辑
    } catch (ApiException $e) {
        return response()->json([
            'code' => $e->getCode(),
            'message' => $e->getMessage()
        ], 400);
    }
});

// 不好的实践 - 不一致的错误处理
Route::get('users', function () {
    if (!$user) {
        return ['error' => 'User not found'];
    }
});
```

## 注意事项

1. 严格的访问控制
2. 合理的限流设置
3. 统一的错误处理
4. 完善的接口文档
5. 版本兼容性维护
6. 安全性考虑
