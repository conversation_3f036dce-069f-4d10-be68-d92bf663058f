"use strict";(function($){$.widget("ui.editable",{version:"1.0.0",widgetEventPrefix:"edit",options:{content:"self",saveDelay:false,multiline:false,exitKeys:["escape"],className:"ui-editable",editingClass:"ui-editable-editing",invalidClass:"ui-editable-invalid",autoselect:false,preventUndo:false,cancel:"a",start:null,end:null,input:null,save:null,validate:null},keyCode:{down:40,end:35,enter:13,escape:27,left:37,right:39,space:32,tab:9,up:38},_create:function _create(){this.ready=false;this.mode="normal";this.element.addClass(this.options.className);this.content=this.options.content=="self"||!this.options.content?this.element:this.element.find(this.options.content);this._bindEvents()},_bindEvents:function _bindEvents(){this._on({mousedown:this._prepare,mouseup:this._capture,dragstart:this._cancel});this._bindContentEvents()},_bindContentEvents:function _bindContentEvents(){this.content.on("keydown"+this.eventNamespace,this._keydown.bind(this)).on("input"+this.eventNamespace,this._input.bind(this)).on("blur"+this.eventNamespace,this.finish.bind(this))},_prepare:function _prepare(e){if($(e.target).closest(this.options.cancel).length)return;if(e.which===1)this.ready=true},_cancel:function _cancel(){this.ready=false},_capture:function _capture(){if(!this.ready)return;this.start();this.ready=false},getText:function getText(){return this.content[0].innerText||this.content[0].textContent},start:function start(){if(this.mode==="edit")return;if(this._trigger("start")===false)return false;this._mode("edit");this.content.focus();this.isValid=true;this.validContent=this.getText();if(this.options.autoselect)this.select()},finish:function finish(e){if(this.mode==="normal")return;this._save();if(!this.isValid)this.content.text(this.validContent);this._mode("normal");if(!e||e.type!="blur")this.content.blur();this._trigger("end")},validate:function validate(){var result=this._trigger("validate",null,{content:this.getText()});if(result===false)this.element.addClass(this.options.invalidClass);else this.element.removeClass(this.options.invalidClass);return this.isValid=result!==false},select:function select(){var range=document.createRange();var sel=window.getSelection();range.selectNodeContents(this.content[0]);sel.removeAllRanges();sel.addRange(range)},_mode:function _mode(m){switch(m){case"edit":this.element.addClass(this.options.editingClass);if(this.element.data("ui-draggable"))this.element.draggable("disable");this.content.prop("contenteditable",true);break;case"normal":this.element.removeClass([this.options.editingClass,this.options.invalidClass].join(" "));if(this.element.data("ui-draggable"))this.element.draggable("enable");this.content.prop("contenteditable",false);break}this.mode=m},_input:function _input(e){if(this.mode!="edit")return;var ui={content:this.getText()};if(this._trigger("input",e,ui)===false){return false}if(this.options.saveDelay===0)this._save();else if(this.options.saveDelay>0)this._debouncedSave(this.options.saveDelay)},_save:function _save(){var text=this.getText();if(text==this.validContent)return;if(!this.validate())return;this.validContent=text;this._trigger("save",null,{content:this.validContent})},_debouncedSave:function _debouncedSave(timeout){var _this=this;if(this.__save_timeout)clearTimeout(this.__save_timeout);this.__save_timeout=setTimeout(function(){_this._save();_this.__save_timeout=null},timeout)},_keydown:function _keydown(e){var _this2=this;if(this.mode==="normal")return;if(this.options.preventUndo&&e.ctrlKey&&(e.keyCode==="Z".charCodeAt(0)||e.keyCode==="Y".charCodeAt(0)||e.keyCode==="Z".charCodeAt(0)&&e.shiftKey))return false;if(this.options.exitKeys){var exitKeyPressed=this.options.exitKeys.some(function(keyName){return e.keyCode===_this2.keyCode[keyName.toLowerCase()]});if(exitKeyPressed){this.finish();return}}if(!this.options.multiline&&e.keyCode===this.keyCode.enter)return false},_destroy:function _destroy(){this.element.removeClass([this.options.className,this.options.editingClass,this.options.invalidClass].join(" "));this.content.off(this.eventNamespace)}})})(jQuery);