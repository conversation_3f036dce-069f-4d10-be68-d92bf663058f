<template>
  <div class="flex flex-col h-full bg-white menu-box">
    <div class="flex justify-between items-center logo-pic shrink-0">
      <div class="logo" >
        <img :src="$asset('Dashboard/Asset/logos.png')" alt="Bingo" />
      </div>

      <div class="contract-box" @click="router.push(`/config/settings`)">
        <el-button type="text" >
          <el-icon color="#B4B4B4" size="16"><Back /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="menu-container" >
      <el-menu :default-active="activeMenuKey" :default-openeds="activeMenu" class="menu-list" unique-opened>
        <template v-for="item in list" :key="item.id">
          <el-sub-menu v-if="item.children && item.children?.length" :index="item.id.toString()">
            <template #title>
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.name }}</span>
            </template>
            <template v-for="_item in item.children" :key="_item.id">
              <MenuItems :item="_item"></MenuItems>
            </template>
          </el-sub-menu>
          <MenuItems v-else :item="item"></MenuItems>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '/admin/stores/modules/app'
import { Back } from '@element-plus/icons-vue'
import MenuItems from './menuItem.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
interface MenuItem {
  id: number
  path: string
  name: string
  icon: string
  children?: MenuItem[]
}
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

const activeMenu = ref([1, 4])
const activeMenuKey = ref('')
const baseList = ref<MenuItem[]>([
  {
    id: 0,
    path: '/Iam/iamcenter',
    name: t("iam.menu.application"),
    icon: 'Menu',
  },
  {
    id: 1,
    path: '',
    name: t("iam.menu.identitySource.title"),
    icon: 'Share',
    children: [
      {
        id: 2,
        path: '/Iam/identity/social',
        name: t("iam.menu.identitySource.social"),
        icon: 'Avatar',
      },
      {
        id: 3,
        path: '/Iam/identity/enterprise',
        name: t("iam.menu.identitySource.enterprise"),
        icon: 'Briefcase',
      },
    ],
  },
  {
    id: 4,
    path: '',
    name: t("iam.menu.userManagement.title"),
    icon: 'UserFilled',
    children: [
      {
        id: 5,
        path: '/Iam/userManager/list/index',
        name: t("iam.menu.userManagement.userList"),
        icon: 'Fold',
      },
      {
        id: 6,
        path: '/Iam/userManager/group/index',
        name: t("iam.menu.userManagement.userGroup"),
        icon: 'List',
      },
      {
        id: 7,
        path: '/Iam/userManager/organize',
        name: t("iam.menu.userManagement.organization"),
        icon: 'Flag',
      },
      {
        id: 8,
        path: '/Iam/userManager/postManagement',
        name: t("iam.menu.userManagement.position"),
        icon: 'Management',
      },
    ],
  },
  {
    id: 9,
    path: '',
    name: t("iam.menu.permission.title"),
    icon: 'Lock',
    children: [
      {
        id: 10,
        path: '/Iam/permission/role',
        name: t("iam.menu.permission.role"),
        icon: 'Lock',
      },
    ],
  },
  {
    id: 10,
    path: '',
    name: t("iam.menu.security.title"),
    icon: 'Lock',
    children: [
      {
        id: 11,
        path: '/Iam/security/mfa',
        name: t("iam.menu.security.mfa"),
        icon: 'Guide',
      },
    ],
  },
  {
    id: 12,
    path: '',
    name: t("iam.menu.branding.title"),
    icon: 'CreditCard',
    children: [
      {
        id: 13,
        path: '/Iam/branding/index',
        name: t("iam.menu.branding.message"),
        icon: 'Message',
      },
    ],
  },
])

// 根据路由计算显示的菜单列表
const list = computed(() => {
  if (route.path === '/Iam/iamcenter') {
    // 当路由是 iamcenter 时，过滤掉用户管理相关菜单 (id 为 4 的菜单)
    return baseList.value.filter(item => item.id !== 4)
  }
  return baseList.value
})

// 根据路由设置默认的 appCode
const appCode = ref(route.path === '/Iam/iamcenter' ? 'frontend' : route.path === '/Iam/admin' ? 'admin' : localStorage.getItem('app_code') || 'admin')

const handleCommand = (command: string) => {
  appCode.value = command
  localStorage.setItem('app_code', command)
  // 根据选择跳转到对应路由
  if (command === 'frontend') {
    router.push('/Iam/iamcenter')
  } else {
    router.push('/Iam/admin')
  }
}

onMounted(() => {
  let menu = findMenu(list.value) || list.value[1]
  if (menu) {
    activeMenuKey.value = menu.id.toString()
    appStore.setPageName(menu.name)
  }
  localStorage.setItem('app_code', appCode.value)
})

function findMenu(item: MenuItem[]): MenuItem | undefined {
  let findItem
  for (let i = 0; i < item.length; i++) {
    const ele = item[i]

    if (ele.path && route.path.includes(ele.path)) {
      findItem = ele
      break
    }

    if (ele.children && ele.children.length) {
      findItem = findMenu(ele.children)
      if (findItem) break
    }
  }
  return findItem
}

// 监听路由变化，更新激活菜单
watch(() => route.path, () => {
  const menu = findMenu(list.value)
  if (menu) {
    activeMenuKey.value = menu.id.toString()
    appStore.setPageName(menu.name)
  }
})
</script>

<style scoped lang="scss">
.menu-box {
  border-radius: 30px;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.35s ease-in-out;

  .logo-pic {
    padding: 0 16px 20px;
    flex-shrink: 0;

    .logo {
      width: 78px;
      transition: width 0.35s ease-in-out;
    }

    .contract-box {
      border-radius: 50%;
      background-color: #f6f6f6;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.35s ease-in-out;

      .el-icon {
        transition: color 0.35s ease-in-out;
      }

      &:hover {
        background-color: rgba(230,242,252,0.467);

        .el-icon {
          color: #000;
        }
      }
    }
  }

  
  .menu-container {
    padding-bottom: 40px;

    :deep(.el-menu) {
      border-right: none;

            // 添加激活状态样式
      .el-menu-item.is-active {
     
        color: #409eff !important;
        
       
      }

      .el-sub-menu .el-menu-item.is-active {
     
        color: #409eff  !important;
        
   
      }

      // 直接菜单项样式
      .el-menu-item {
        border-radius: 50px;
        padding: 0 12px;
        line-height: 42px;
        height: 42px;
        font-size: 16px;
        font-weight: 500;

        .el-icon {
          text-align: left;
          justify-content: flex-start;
          font-size: 16px;
          width: 16px;
          color: #232323;
        }

        // 悬停状态
        &:hover:not(.is-active) {
          background-color: rgba(230,242,252,0.467);
        }
      }

      // 子菜单样式
      .el-sub-menu {
        margin-bottom: 5px;

        .el-sub-menu__title {
          border-radius: 50px;
          padding: 0 12px;
          line-height: 42px;
          height: 42px;
          font-size: 16px;
          font-weight: 500;

          .el-icon {
            text-align: left;
            justify-content: flex-start;
            font-size: 16px;
            width: 16px;
            color: #232323;

            &.el-sub-menu__icon-arrow {
              font-size: 12px;
              width: 12px;
            }
          }
        }

        .el-menu {
          padding: 0 12px;

          .el-menu-item {
            font-size: 14px;
            padding: 0 12px;
            line-height: 40px;
            height: 40px;
            font-weight: 500;

            .el-icon {
              font-size: 14px;
              width: 14px;
              color: #232323;
            }

            // 子菜单悬停状态
            &:hover:not(.is-active) {
              background-color: rgba(230,242,252,0.467);
            }
          }
        }
      }
    }
  }
}

.contract-box {
  .el-button {
    padding: 0;
    border: none;
    background: transparent;
  }
}

:deep(.el-dropdown-menu__item.is-active) {
  color: var(--el-color-primary);
}
</style>
