<?php
use Bingo\Foundation\Application;
use Illuminate\Contracts\Http\Kernel as HttpKernel;
use Illuminate\Contracts\Console\Kernel as ConsoleKernel;
use Illuminate\Contracts\Debug\ExceptionHandler;

$app = Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->create();

$app->singleton(HttpKernel::class, Bingo\Foundation\Http\Kernel::class);
$app->singleton(ConsoleKernel::class, Bingo\Foundation\Console\Kernel::class);
$app->singleton(ExceptionHandler::class, Bingo\Exceptions\Handler::class);

return $app;
