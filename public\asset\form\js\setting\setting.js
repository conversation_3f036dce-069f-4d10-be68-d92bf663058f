jQuery(function ($) {
    var $el=jQuery("#form-settings .kv-hint-special");if($el.length){$el.each(function(){$(this).activeFieldHint()});}kvBs4InitForm();
    jQuery("#w1").kvFormBuilder({});
    jQuery("#w2").kvFormBuilder({});
    jQuery&&jQuery.pjax&&(jQuery.pjax.defaults.maxCacheLength=0);
    if (jQuery('#form-status').data('bootstrapSwitch')) { jQuery('#form-status').bootstrapSwitch('destroy'); }
    jQuery("#form-status").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    jQuery("#w3").kvFormBuilder({});
    if (jQuery('#form-is_private').data('bootstrapSwitch')) { jQuery('#form-is_private').bootstrapSwitch('destroy'); }
    jQuery("#form-is_private").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    new TomSelect('#form-created_by', {"openOnFocus":false,"create":false,"multiple":false,"selectOnTab":true,"onType":
            function(str) {
                if (str.length < 3) {
                    $(this.dropdown).hide()
                } else {
                    $(this.dropdown).show()
                }
            },"valueField":"id","labelField":"username","searchField":"username","load":
            function(query, callback) {
                var url = options.hasPrettyUrls
                    ? options.userListUrl + '?q=' + encodeURIComponent(query)
                    : options.userListUrl + '&q=' + encodeURIComponent(query);
                fetch(url)
                    .then(function (response) {
                        return response.json();
                    })
                    .then(function (json) {
                        callback(json.items);
                    })
                    .catch(()=>{
                        callback();
                    });
            }})
    jQuery("#w4").kvFormBuilder({});
    jQuery("#w5").kvFormBuilder({});
    jQuery("#w6").kvFormBuilder({});
    
    jQuery("#w7").kvFormBuilder({});
    jQuery("#w8").kvFormBuilder({});
    if (jQuery('#form-save').data('bootstrapSwitch')) { jQuery('#form-save').bootstrapSwitch('destroy'); }
    jQuery("#form-save").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-submission_scope').data('bootstrapSwitch')) { jQuery('#form-submission_scope').bootstrapSwitch('destroy'); }
    jQuery("#form-submission_scope").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-protected_files').data('bootstrapSwitch')) { jQuery('#form-protected_files').bootstrapSwitch('destroy'); }
    jQuery("#form-protected_files").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    jQuery("#w9").kvFormBuilder({});
    jQuery("#w10").kvFormBuilder({});
    jQuery("#w11").kvFormBuilder({});
    jQuery("#w12").kvFormBuilder({});
    jQuery("#w13").kvFormBuilder({});
    jQuery("#w14").kvFormBuilder({});
    jQuery("#w15").kvFormBuilder({});
    jQuery("#w16").kvFormBuilder({});
    if (jQuery('#form-schedule_start_date').data('datecontrol')) { jQuery('#form-schedule_start_date').datecontrol('destroy'); }
    jQuery('#form-schedule_start_date-disp').datecontrol(datecontrol_79a83c16);

    if (jQuery('#form-schedule_start_date-disp').data('datetimepicker')) { jQuery('#form-schedule_start_date-disp').datetimepicker('destroy'); }
    jQuery('#form-schedule_start_date-disp-datetime').datetimepicker(datetimepicker_1091ee42);

    if (jQuery('#form-schedule_end_date').data('datecontrol')) { jQuery('#form-schedule_end_date').datecontrol('destroy'); }
    jQuery('#form-schedule_end_date-disp').datecontrol(datecontrol_f3beab64);

    if (jQuery('#form-schedule_end_date-disp').data('datetimepicker')) { jQuery('#form-schedule_end_date-disp').datetimepicker('destroy'); }
    jQuery('#form-schedule_end_date-disp-datetime').datetimepicker(datetimepicker_1091ee42);

    jQuery("#w17").kvFormBuilder({});
    if (jQuery('#form-use_password').data('bootstrapSwitch')) { jQuery('#form-use_password').bootstrapSwitch('destroy'); }
    jQuery("#form-use_password").bootstrapSwitch(bootstrapSwitch_29bf9f81);
    jQuery("#form-use_password").on('switchChange.bootstrapSwitch', function(event, state) {
        if (state) {
            $('.field-form-password').show()
        } else {
            $('.field-form-password').hide()
        }
    });

    if (jQuery('#form-honeypot').data('bootstrapSwitch')) { jQuery('#form-honeypot').bootstrapSwitch('destroy'); }
    jQuery("#form-honeypot").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-authorized_urls').data('bootstrapSwitch')) { jQuery('#form-authorized_urls').bootstrapSwitch('destroy'); }
    jQuery("#form-authorized_urls").bootstrapSwitch(bootstrapSwitch_29bf9f81);
    jQuery("#form-authorized_urls").on('switchChange.bootstrapSwitch', function(event, state) {
        if (state) {
            $('.field-form-urls').show()
        } else {
            $('.field-form-urls').hide()
        }
    });

    if (jQuery('#form-novalidate').data('bootstrapSwitch')) { jQuery('#form-novalidate').bootstrapSwitch('destroy'); }
    jQuery("#form-novalidate").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    jQuery("#w18").kvFormBuilder({});
    jQuery("#w19").kvFormBuilder({});
    if (jQuery('#form-ip_tracking').data('bootstrapSwitch')) { jQuery('#form-ip_tracking').bootstrapSwitch('destroy'); }
    jQuery("#form-ip_tracking").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-analytics').data('bootstrapSwitch')) { jQuery('#form-analytics').bootstrapSwitch('destroy'); }
    jQuery("#form-analytics").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-autocomplete').data('bootstrapSwitch')) { jQuery('#form-autocomplete').bootstrapSwitch('destroy'); }
    jQuery("#form-autocomplete").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    if (jQuery('#form-resume').data('bootstrapSwitch')) { jQuery('#form-resume').bootstrapSwitch('destroy'); }
    jQuery("#form-resume").bootstrapSwitch(bootstrapSwitch_29bf9f81);

    jQuery("#w21").kvFormBuilder({});
    jQuery("#w22").kvFormBuilder({});
    jQuery("#w23").kvFormBuilder({});
    jQuery("#w24").kvFormBuilder({});
    jQuery("#w25").kvFormBuilder({});
    jQuery("#w27").kvFormBuilder({});
    jQuery("#w28").kvFormBuilder({});
    jQuery("#w29").kvFormBuilder({});
    if (jQuery('#formconfirmation-mail_to').data('select2')) { jQuery('#formconfirmation-mail_to').select2('destroy'); }
    jQuery.when(jQuery('#formconfirmation-mail_to').select2(select2_e8e9960c)).done(initS2Loading('formconfirmation-mail_to','s2options_ba3c7bc6'));

    jQuery("#w30").kvFormBuilder({});
    jQuery("#w31").kvFormBuilder({});
    jQuery("#w32").kvFormBuilder({});
    jQuery("#w33").kvFormBuilder({});
    jQuery("#w35").kvFormBuilder({});
    jQuery("#w36").kvFormBuilder({});
    jQuery("#w37").kvFormBuilder({});
    jQuery("#w38").kvFormBuilder({});
    jQuery("#w40").kvFormBuilder({});
    new TomSelect('#formemail-event', {"create":false,"sortField":"text"})
    jQuery("#w41").kvFormBuilder({});
    jQuery("#w42").kvFormBuilder({});
    new TomSelect('#formemail-from_name', {"create":true,"sortField":"text"})
    new TomSelect('#formemail-from', {"create":true,"sortField":"text"})
    jQuery("#w43").kvFormBuilder({});
    new TomSelect('#formemail-field_to', {"create":false,"sortField":"text"})
    new TomSelect('#formemail-cc', {"persist":false,"create":true})
    new TomSelect('#formemail-bcc', {"persist":false,"create":true})
    jQuery("#w44").kvFormBuilder({});
    jQuery("#w45").kvFormBuilder({});
    jQuery("#w46").kvFormBuilder({});
    jQuery("#w47").kvFormBuilder({});
    jQuery("#w48").kvFormBuilder({});
    jQuery("#w50").kvFormBuilder({});
    new TomSelect('#formui-js_file', {"create":
            function (input) {
                return {
                    value: input,
                    text: input,
                };
            }})
    jQuery("#w51").kvFormBuilder({});
    new TomSelect('#formui-theme_id', {"openOnFocus":true,"create":false,"multiple":false,"selectOnTab":true,"onItemAdd":
            function (value, item) {
                previewSelected(value)
            },"onItemRemove":
            function (value, item) {
                previewUnselected()
            }})
    jQuery('#form-settings').yiiActiveForm([{"id":"form-submission_editable_conditions","name":"submission_editable_conditions","container":".field-form-submission_editable_conditions","input":"#form-submission_editable_conditions","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"条件必须是一条字符串。","skipOnEmpty":1});}},{"id":"form-name","name":"name","container":".field-form-name","input":"#form-name","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.required(value, messages, {"message":"表单名称不能为空。"});yii.validation.string(value, messages, {"message":"表单名称必须是一条字符串。","max":255,"tooLong":"表单名称只能包含至多255个字符。","skipOnEmpty":1});}},{"id":"form-slug","name":"slug","container":".field-form-slug","input":"#form-slug","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Slug必须是一条字符串。","max":2555,"tooLong":"Slug只能包含至多2,555个字符。","skipOnEmpty":1});}},{"id":"form-language","name":"language","container":".field-form-language","input":"#form-language","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"语言必须是一条字符串。","max":5,"tooLong":"语言只能包含至多5个字符。","skipOnEmpty":1});}},{"id":"form-text_direction","name":"text_direction","container":".field-form-text_direction","input":"#form-text_direction","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Text Direction必须是一条字符串。","max":3,"tooLong":"Text Direction只能包含至多3个字符。","skipOnEmpty":1});}},{"id":"form-status","name":"status","container":".field-form-status","input":"#form-status","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"状态必须是整数。","skipOnEmpty":1});}},{"id":"form-is_private","name":"is_private","container":".field-form-is_private","input":"#form-is_private","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Private必须是整数。","skipOnEmpty":1});}},{"id":"form-created_by","name":"created_by","container":".field-form-created_by","input":"#form-created_by","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"创建必须是整数。","skipOnEmpty":1});}},{"id":"form-message","name":"message","container":".field-form-message","input":"#form-message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"讯息必须是一条字符串。","skipOnEmpty":1});}},{"id":"form-shared","name":"shared","container":".field-form-shared","input":"#form-shared","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Shared With必须是整数。","skipOnEmpty":1});}},{"id":"form-submission_number","name":"submission_number","container":".field-form-submission_number","input":"#form-submission_number","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Generate Submission Number必须是整数。","min":0,"tooSmall":"Generate Submission Number的值必须不小于0。","skipOnEmpty":1});}},{"id":"form-submission_number_width","name":"submission_number_width","container":".field-form-submission_number_width","input":"#form-submission_number_width","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Number Width必须是整数。","min":0,"tooSmall":"Number Width的值必须不小于0。","max":45,"tooBig":"Number Width的值必须不大于45。","skipOnEmpty":1});}},{"id":"form-submission_number_prefix","name":"submission_number_prefix","container":".field-form-submission_number_prefix","input":"#form-submission_number_prefix","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Number Prefix必须是一条字符串。","max":100,"tooLong":"Number Prefix只能包含至多100个字符。","skipOnEmpty":1});}},{"id":"form-submission_number_suffix","name":"submission_number_suffix","container":".field-form-submission_number_suffix","input":"#form-submission_number_suffix","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Number Suffix必须是一条字符串。","max":100,"tooLong":"Number Suffix只能包含至多100个字符。","skipOnEmpty":1});}},{"id":"form-save","name":"save","container":".field-form-save","input":"#form-save","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"保存到数据库必须是整数。","skipOnEmpty":1});}},{"id":"form-submission_scope","name":"submission_scope","container":".field-form-submission_scope","input":"#form-submission_scope","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Owner Scope必须是整数。","skipOnEmpty":1});}},{"id":"form-protected_files","name":"protected_files","container":".field-form-protected_files","input":"#form-protected_files","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Protected Files必须是整数。","skipOnEmpty":1});}},{"id":"form-submission_timezone","name":"submission_timezone","container":".field-form-submission_timezone","input":"#form-submission_timezone","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Time Zone For Submissions必须是一条字符串。","max":45,"tooLong":"Time Zone For Submissions只能包含至多45个字符。","skipOnEmpty":1});}},{"id":"form-submission_dateformat","name":"submission_dateformat","container":".field-form-submission_dateformat","input":"#form-submission_dateformat","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Date Format必须是一条字符串。","max":45,"tooLong":"Date Format只能包含至多45个字符。","skipOnEmpty":1});}},{"id":"form-submission_editable","name":"submission_editable","container":".field-form-submission_editable","input":"#form-submission_editable","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Editable必须是整数。","skipOnEmpty":1});}},{"id":"form-submission_editable_time_length","name":"submission_editable_time_length","container":".field-form-submission_editable_time_length","input":"#form-submission_editable_time_length","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"During必须是整数。","skipOnEmpty":1});}},{"id":"form-submission_editable_time_unit","name":"submission_editable_time_unit","container":".field-form-submission_editable_time_unit","input":"#form-submission_editable_time_unit","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Unit of Time必须是一条字符串。","max":1,"tooLong":"Unit of Time只能包含至多1个字符。","skipOnEmpty":1});}},{"id":"form-total_limit","name":"total_limit","container":".field-form-total_limit","input":"#form-total_limit","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Limit Total Number of Submissions必须是整数。","skipOnEmpty":1});}},{"id":"form-total_limit_action","name":"total_limit_action","container":".field-form-total_limit_action","input":"#form-total_limit_action","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Action必须是整数。","skipOnEmpty":1});}},{"id":"form-user_limit","name":"user_limit","container":".field-form-user_limit","input":"#form-user_limit","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Limit Submissions per User必须是整数。","skipOnEmpty":1});}},{"id":"form-user_limit_type","name":"user_limit_type","container":".field-form-user_limit_type","input":"#form-user_limit_type","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[ip_tracking]']:checked").val() != '1'
                && $("input[name='Form[user_limit]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.compare(value, messages, {"operator":"==","type":"number","compareValue":3,"skipOnEmpty":1,"message":"Enable IP Tracking to use this option."}, $form); }}},{"id":"form-total_limit_number","name":"total_limit_number","container":".field-form-total_limit_number","input":"#form-total_limit_number","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[total_limit]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"总数不能为空。"}); }yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"总数必须是整数。","skipOnEmpty":1});}},{"id":"form-total_limit_time_unit","name":"total_limit_time_unit","container":".field-form-total_limit_time_unit","input":"#form-total_limit_time_unit","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[total_limit]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"每个时间段不能为空。"}); }yii.validation.string(value, messages, {"message":"每个时间段必须是一条字符串。","max":1,"tooLong":"每个时间段只能包含至多1个字符。","skipOnEmpty":1});}},{"id":"form-user_limit_number","name":"user_limit_number","container":".field-form-user_limit_number","input":"#form-user_limit_number","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[user_limit]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"最大数字不能为空。"}); }yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"最大数字必须是整数。","skipOnEmpty":1});}},{"id":"form-user_limit_time_unit","name":"user_limit_time_unit","container":".field-form-user_limit_time_unit","input":"#form-user_limit_time_unit","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[user_limit]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"每个时间段不能为空。"}); }yii.validation.string(value, messages, {"message":"每个时间段必须是一条字符串。","max":1,"tooLong":"每个时间段只能包含至多1个字符。","skipOnEmpty":1});}},{"id":"form-schedule","name":"schedule","container":".field-form-schedule","input":"#form-schedule","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"附表形式的活动必须是整数。","skipOnEmpty":1});}},{"id":"form-schedule_start_date","name":"schedule_start_date","container":".field-form-schedule_start_date","input":"#form-schedule_start_date","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[schedule]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"开始日期不能为空。"}); }yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"开始日期必须是整数。","skipOnEmpty":1});}},{"id":"form-schedule_end_date","name":"schedule_end_date","container":".field-form-schedule_end_date","input":"#form-schedule_end_date","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[schedule]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"结束日期不能为空。"}); }yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"结束日期必须是整数。","skipOnEmpty":1});}},{"id":"form-use_password","name":"use_password","container":".field-form-use_password","input":"#form-use_password","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"使用密码必须是整数。","skipOnEmpty":1});}},{"id":"form-honeypot","name":"honeypot","container":".field-form-honeypot","input":"#form-honeypot","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"垃圾邮件过滤器必须是整数。","skipOnEmpty":1});}},{"id":"form-authorized_urls","name":"authorized_urls","container":".field-form-authorized_urls","input":"#form-authorized_urls","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"授权网址必须是整数。","skipOnEmpty":1});}},{"id":"form-novalidate","name":"novalidate","container":".field-form-novalidate","input":"#form-novalidate","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"无验证必须是整数。","skipOnEmpty":1});}},{"id":"form-password","name":"password","container":".field-form-password","input":"#form-password","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[use_password]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"密码不能为空。"}); }yii.validation.string(value, messages, {"message":"密码必须是一条字符串。","max":255,"tooLong":"密码只能包含至多255个字符。","skipOnEmpty":1});yii.validation.string(value, messages, {"message":"密码必须是一条字符串。","min":3,"tooShort":"密码应该包含至少3个字符。","skipOnEmpty":1});value = yii.validation.trim($form, attribute, {"skipOnEmpty":1}, value);}},{"id":"form-urls","name":"urls","container":".field-form-urls","input":"#form-urls","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input[name='Form[authorized_urls]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"的URL不能为空。"}); }yii.validation.string(value, messages, {"message":"的URL必须是一条字符串。","max":2555,"tooLong":"的URL只能包含至多2,555个字符。","skipOnEmpty":1});}},{"id":"form-ip_tracking","name":"ip_tracking","container":".field-form-ip_tracking","input":"#form-ip_tracking","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"IP Tracking必须是整数。","skipOnEmpty":1});}},{"id":"form-analytics","name":"analytics","container":".field-form-analytics","input":"#form-analytics","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"分析必须是整数。","skipOnEmpty":1});}},{"id":"form-autocomplete","name":"autocomplete","container":".field-form-autocomplete","input":"#form-autocomplete","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"自动完成必须是整数。","skipOnEmpty":1});}},{"id":"form-resume","name":"resume","container":".field-form-resume","input":"#form-resume","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"保存并在稍后继续必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-type","name":"type","container":".field-formconfirmation-type","input":"#formconfirmation-type","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"确认提交是成功的必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-message","name":"message","container":".field-formconfirmation-message","input":"#formconfirmation-message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"你的信息必须是一条字符串。","skipOnEmpty":1});}},{"id":"formconfirmation-url","name":"url","container":".field-formconfirmation-url","input":"#formconfirmation-url","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input:radio[name='FormConfirmation[type]']:checked").val() == '2';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"页面网址不能为空。"}); }yii.validation.string(value, messages, {"message":"页面网址必须是一条字符串。","max":2555,"tooLong":"页面网址只能包含至多2,555个字符。","skipOnEmpty":1});yii.validation.url(value, messages, {"pattern":/^(http|https):\/\/(([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+)(?::\d{1,5})?(?:$|[?\/#])/i,"message":"页面网址不是一条有效的URL。","enableIDN":false,"skipOnEmpty":1,"defaultScheme":"http"});}},{"id":"formconfirmation-seconds","name":"seconds","container":".field-formconfirmation-seconds","input":"#formconfirmation-seconds","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Show Message and Redirect After必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-append","name":"append","container":".field-formconfirmation-append","input":"#formconfirmation-append","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Append Submission Data to URL必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-alias","name":"alias","container":".field-formconfirmation-alias","input":"#formconfirmation-alias","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Replace Field Name with Field Alias when it\u0027s available必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmationrule-action","name":"action","container":".field-formconfirmationrule-action","input":"#formconfirmationrule-action","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"确认提交是成功的必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmationrule-message","name":"message","container":".field-formconfirmationrule-message","input":"#formconfirmationrule-message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"讯息必须是一条字符串。","skipOnEmpty":1});if ((function (attribute, value) {
            return false;
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"讯息不能为空。"}); }}},{"id":"formconfirmationrule-url","name":"url","container":".field-formconfirmationrule-url","input":"#formconfirmationrule-url","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"页面网址必须是一条字符串。","max":2555,"tooLong":"页面网址只能包含至多2,555个字符。","skipOnEmpty":1});yii.validation.url(value, messages, {"pattern":/^(http|https):\/\/(([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+)(?::\d{1,5})?(?:$|[?\/#])/i,"message":"页面网址不是一条有效的URL。","enableIDN":false,"skipOnEmpty":1,"defaultScheme":"http"});if ((function (attribute, value) {
            return false;
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"页面网址不能为空。"}); }}},{"id":"formconfirmationrule-seconds","name":"seconds","container":".field-formconfirmationrule-seconds","input":"#formconfirmationrule-seconds","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Show Message and Redirect After必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmationrule-append","name":"append","container":".field-formconfirmationrule-append","input":"#formconfirmationrule-append","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Append Submission Data to URL必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmationrule-alias","name":"alias","container":".field-formconfirmationrule-alias","input":"#formconfirmationrule-alias","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Replace Field Name with Field Alias when it\u0027s available必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmationrule-conditions","name":"conditions","container":".field-formconfirmationrule-conditions","input":"#formconfirmationrule-conditions","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"条件必须是一条字符串。","skipOnEmpty":1});}},{"id":"formconfirmation-send_email","name":"send_email","container":".field-formconfirmation-send_email","input":"#formconfirmation-send_email","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"发送确认电子邮件？必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-mail_from_name","name":"mail_from_name","container":".field-formconfirmation-mail_from_name","input":"#formconfirmation-mail_from_name","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"From Name必须是一条字符串。","max":2555,"tooLong":"From Name只能包含至多2,555个字符。","skipOnEmpty":1});}},{"id":"formconfirmation-mail_from","name":"mail_from","container":".field-formconfirmation-mail_from","input":"#formconfirmation-mail_from","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"回复必须是一条字符串。","max":2555,"tooLong":"回复只能包含至多2,555个字符。","skipOnEmpty":1});value = yii.validation.trim($form, attribute, {"skipOnArray":true,"skipOnEmpty":false,"chars":false}, value);yii.validation.email(value, messages, {"pattern":/^[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$/,"fullPattern":/^[^@]*<[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+\/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?>$/,"allowName":false,"message":"回复不是有效的邮箱地址。","enableIDN":false,"skipOnEmpty":1});}},{"id":"formconfirmation-mail_to","name":"mail_to","container":".field-formconfirmation-mail_to","input":"#formconfirmation-mail_to","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {if ((function (attribute, value) {
            return $("input:radio[name='FormConfirmation[send_email]']:checked").val() == '1';
        })(attribute, value)) { yii.validation.required(value, messages, {"message":"发给不能为空。"}); }}},{"id":"formconfirmation-mail_cc","name":"mail_cc","container":".field-formconfirmation-mail_cc","input":"#formconfirmation-mail_cc","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"副本 (cc)必须是一条字符串。","max":2555,"tooLong":"副本 (cc)只能包含至多2,555个字符。","skipOnEmpty":1});value = yii.validation.trim($form, attribute, {"skipOnArray":true,"skipOnEmpty":false,"chars":false}, value);if (value.length > 0) {
            var mail_cc_emails = value.split(",");
            var mail_cc_regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (mail_cc_emails.length > 1) {
                for (var i = 0; i < mail_cc_emails.length; i++) {
                    if(mail_cc_emails[i] == "" || !mail_cc_regex.test(mail_cc_emails[i].replace(/\s/g, ""))){
                        messages.push("该栏位有无效的电子邮件地址。");
                    }
                }
            }
        }}},{"id":"formconfirmation-mail_bcc","name":"mail_bcc","container":".field-formconfirmation-mail_bcc","input":"#formconfirmation-mail_bcc","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"密件副本 (bcc)必须是一条字符串。","max":2555,"tooLong":"密件副本 (bcc)只能包含至多2,555个字符。","skipOnEmpty":1});value = yii.validation.trim($form, attribute, {"skipOnArray":true,"skipOnEmpty":false,"chars":false}, value);if (value.length > 0) {
            var mail_bcc_emails = value.split(",");
            var mail_bcc_regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (mail_bcc_emails.length > 1) {
                for (var i = 0; i < mail_bcc_emails.length; i++) {
                    if(mail_bcc_emails[i] == "" || !mail_bcc_regex.test(mail_bcc_emails[i].replace(/\s/g, ""))){
                        messages.push("该栏位有无效的电子邮件地址。");
                    }
                }
            }
        }}},{"id":"formconfirmation-mail_subject","name":"mail_subject","container":".field-formconfirmation-mail_subject","input":"#formconfirmation-mail_subject","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"主题必须是一条字符串。","max":2555,"tooLong":"主题只能包含至多2,555个字符。","skipOnEmpty":1});}},{"id":"formconfirmation-mail_message","name":"mail_message","container":".field-formconfirmation-mail_message","input":"#formconfirmation-mail_message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"电子邮件信息必须是一条字符串。","skipOnEmpty":1});}},{"id":"formconfirmation-mail_receipt_copy","name":"mail_receipt_copy","container":".field-formconfirmation-mail_receipt_copy","input":"#formconfirmation-mail_receipt_copy","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"包括提交副本必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-mail_attach","name":"mail_attach","container":".field-formconfirmation-mail_attach","input":"#formconfirmation-mail_attach","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Attach Uploaded Files必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-opt_in","name":"opt_in","container":".field-formconfirmation-opt_in","input":"#formconfirmation-opt_in","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Double Opt-In必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-opt_in_type","name":"opt_in_type","container":".field-formconfirmation-opt_in_type","input":"#formconfirmation-opt_in_type","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Configure the Thank You Page with必须是整数。","skipOnEmpty":1});}},{"id":"formconfirmation-opt_in_message","name":"opt_in_message","container":".field-formconfirmation-opt_in_message","input":"#formconfirmation-opt_in_message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"Thank You Message必须是一条字符串。","skipOnEmpty":1});}},{"id":"formconfirmation-opt_in_url","name":"opt_in_url","container":".field-formconfirmation-opt_in_url","input":"#formconfirmation-opt_in_url","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"页面网址必须是一条字符串。","max":2555,"tooLong":"页面网址只能包含至多2,555个字符。","skipOnEmpty":1});yii.validation.url(value, messages, {"pattern":/^(http|https):\/\/(([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+)(?::\d{1,5})?(?:$|[?\/#])/i,"message":"页面网址不是一条有效的URL。","enableIDN":false,"skipOnEmpty":1,"defaultScheme":"http"});}},{"id":"formemail-subject","name":"subject","container":".field-formemail-subject","input":"#formemail-subject","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"主题必须是一条字符串。","max":255,"tooLong":"主题只能包含至多255个字符。","skipOnEmpty":1});}},{"id":"formemail-from_name","name":"from_name","container":".field-formemail-from_name","input":"#formemail-from_name","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"From Name必须是一条字符串。","max":2555,"tooLong":"From Name只能包含至多2,555个字符。","skipOnEmpty":1});}},{"id":"formemail-from","name":"from","container":".field-formemail-from","input":"#formemail-from","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"回复必须是一条字符串。","max":255,"tooLong":"回复只能包含至多255个字符。","skipOnEmpty":1});}},{"id":"formemail-to","name":"to","container":".field-formemail-to","input":"#formemail-to","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {value = yii.validation.trim($form, attribute, {"skipOnArray":true,"skipOnEmpty":false,"chars":false}, value);if (value.length > 0) {
            var to_emails = value.split(",");
            var to_regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (to_emails.length > 1) {
                for (var i = 0; i < to_emails.length; i++) {
                    if(to_emails[i] == "" || !to_regex.test(to_emails[i].replace(/\s/g, ""))){
                        messages.push("该栏位有无效的电子邮件地址。");
                    }
                }
            }
        }yii.validation.string(value, messages, {"message":"Recipient (Email address)必须是一条字符串。","max":255,"tooLong":"Recipient (Email address)只能包含至多255个字符。","skipOnEmpty":1});}},{"id":"formemail-type","name":"type","container":".field-formemail-type","input":"#formemail-type","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"讯息必须是整数。","skipOnEmpty":1});}},{"id":"formemail-message","name":"message","container":".field-formemail-message","input":"#formemail-message","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.string(value, messages, {"message":"自定义消息必须是一条字符串。","skipOnEmpty":1});}},{"id":"formemail-receipt_copy","name":"receipt_copy","container":".field-formemail-receipt_copy","input":"#formemail-receipt_copy","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"包括提交副本必须是整数。","skipOnEmpty":1});}},{"id":"formemail-attach","name":"attach","container":".field-formemail-attach","input":"#formemail-attach","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"Attach Uploaded Files必须是整数。","skipOnEmpty":1});}},{"id":"formemail-plain_text","name":"plain_text","container":".field-formemail-plain_text","input":"#formemail-plain_text","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"仅纯文本必须是整数。","skipOnEmpty":1});}},{"id":"formui-theme_id","name":"theme_id","container":".field-formui-theme_id","input":"#formui-theme_id","error":".invalid-feedback","validate":function (attribute, value, messages, deferred, $form) {yii.validation.number(value, messages, {"pattern":/^[+-]?\d+$/,"message":"主题 ID必须是整数。","skipOnEmpty":1});}}], []);
});
$('#actions').find('.saveForm').click(function( e ){
    // Do not perform default action when button is clicked
    e.preventDefault();
    $('#actions button').attr("disabled", true); // Disable submit buttons

    // Prepare FormBuilder to POST as JSON
    var data = {};
    $.each($('#w0').serializeArray(), function() {
        data[this.name]= this.value;
    });
    // Send Form Data
    $.ajax({
        method: "POST",
        url: options.endPoint, // From external file configuration
        dataType: 'json',
        data: data
    }).done(function(data) {
        data = data.data;
        $('#actions button').removeAttr("disabled"); // Enable submit buttons

        if (data.success && data.id > 0) {
            // Redirect to another page
            if (options.afterSave === 'redirect' ) {
                window.parent.location.href = options.url;
            }
        } else {

            // Show error message
            $(document).trigger("add-alerts", [
                {
                    'message': "<strong>alert.warning</strong> " + data.message,
                    'priority': 'warning'
                }
            ]);

        }
    }).fail(function(msg){

        // Show error message
        $(document).trigger("add-alerts", [
            {
                'message': "<strong>" + polyglot.t('alert.warning') + "</strong> " + polyglot.t('alert.errorSavingData'),
                'priority': 'warning'
            }
        ]);

    }).always(function(){
    });

});
