import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import HeadlineBlockComponent from '../components/HeadlineBlockComponent.vue'

export interface HeadlineBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    headlineBlock: {
      /**
       * 插入标题区块
       */
      insertHeadlineBlock: (options: { 
        title: string, 
        content: string,
        align?: 'left' | 'center' | 'right'
      }) => ReturnType
    }
  }
}

export const HeadlineBlock = Node.create<HeadlineBlockOptions>({
  name: 'headlineBlock',
  
  group: 'block',
  
  content: '',
  
  draggable: true,
  
  isolating: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'tiptap-headline-block',
      },
    }
  },

  addAttributes() {
    return {
      title: {
        default: '引人注目的标题',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          return {
            'data-title': attributes.title,
          }
        },
      },
      content: {
        default: '详细说明文字，可以添加更多信息描述你的产品或服务。',
        parseHTML: element => element.getAttribute('data-content'),
        renderHTML: attributes => {
          return {
            'data-content': attributes.content,
          }
        },
      },
      align: {
        default: 'center',
        parseHTML: element => element.getAttribute('data-align'),
        renderHTML: attributes => {
          return {
            'data-align': attributes.align,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="headline-block"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return {}
          const el = element as HTMLElement
          return {
            title: el.getAttribute('data-title') || '引人注目的标题',
            content: el.getAttribute('data-content') || '详细说明文字，可以添加更多信息描述你的产品或服务。',
            align: el.getAttribute('data-align') || 'center',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
      'data-type': 'headline-block',
    })]
  },

  addCommands() {
    return {
      insertHeadlineBlock: (options) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            title: options.title,
            content: options.content,
            align: options.align || 'center',
          },
        })
      },
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(HeadlineBlockComponent)
  },
}) 