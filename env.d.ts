export {}
declare global {
    interface Window {
        BwmsAdmin: BwmsAdmin;
        amisRequire: any;
    }

    interface ResType<T> {
        code: number;
        data: T;
        message?: string;
        action?: 'jump' | 'toast' | 'renderPage';
        actionType?: 'url' | 'route';
        url?: string;
        showMenu?: boolean;
        showHeader?: boolean;
    }

    interface BwmsAdmin {
        name: string;
        title: string;
        apiBase: string;
        prefix: string;
        loginLogo: string;
        loginDesc: string;
        copyright: string;
        menu?: {
            backgroundColor?: string;
            textColor?: string;
            activeTextColor?: string;
        },
        footerLinks: {
            href: string;
            title: string;
        }[];
    }
}
