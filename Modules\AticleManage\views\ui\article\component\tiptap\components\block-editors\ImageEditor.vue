<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.imageEditor.contentTab')" name="content">
        <el-form label-position="top">
          <!-- 图片展示区域 -->
          <div class="image-preview-container">
            <div v-if="imageUrl" class="image-preview">
              <img :src="imageUrl" class="preview-img" />
            </div>
            <div v-else class="image-placeholder">
              <el-icon class="placeholder-icon"><Picture /></el-icon>
              <div class="placeholder-text">{{ $t('Editor.imageEditor.noImage') }}</div>
            </div>
          </div>

          <!-- 图片操作按钮 -->
          <div class="button-group">
            <el-button type="primary" class="button-no-border" @click="openFileManager">
              <el-icon class="icon"><Upload /></el-icon>
              <span>{{ $t('Editor.imageEditor.selectImage') }}</span>
            </el-button>
            <el-button v-if="imageUrl" class="delete-btn" @click="confirmDeleteImage">
              <el-icon class="icon"><Delete /></el-icon>
              <span>{{ $t('Editor.imageEditor.deleteImage') }}</span>
            </el-button>
          </div>

          <!-- 图片设置 -->
          <el-form-item :label="$t('Editor.imageEditor.altText')">
            <el-input v-model="imageAlt" @input="markAsChanged" :placeholder="$t('Editor.imageEditor.altTextPlaceholder')" />
          </el-form-item>
          
          <el-form-item :label="$t('Editor.imageEditor.selectMode')">
            <el-radio-group v-model="isMultiSelect" @change="markAsChanged">
              <el-radio :label="false">{{ $t('Editor.imageEditor.singleImage') }}</el-radio>
              <el-radio :label="true">{{ $t('Editor.imageEditor.multiImage') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane :label="$t('Editor.imageEditor.styleTab')" name="style">
        <el-form label-position="top" class="editor-form">
          <!-- 图片圆角 -->
          <el-form-item :label="$t('Editor.imageEditor.cornerType')">
            <el-radio-group v-model="cornerType" @change="handleCornerTypeChange" class="mb-2">
              <el-radio v-for="(label, value) in cornerTypeOptions" :key="value" :label="value">{{ $t('Editor.imageEditor.cornerTypeOptions.' + value) }}</el-radio>
            </el-radio-group>
            
            <div v-if="cornerType === 'preset'" class="mt-2">
              <el-select v-model="imageRounded" @change="markAsChanged" style="width: 100%; min-width: 120px">
                <el-option v-for="(label, value) in presetCornerOptions" :key="value" :label="$t('Editor.imageEditor.presetCornerOptions.' + value)" :value="value" />
              </el-select>
            </div>
            
            <div v-if="cornerType === 'custom'" class="mt-2 custom-value-container">
              <el-slider
                v-model="cornerValue"
                :min="0"
                :max="50"
                @change="markAsChanged"
                class="flex-grow-1 custom-slider"
              />
              <div class="custom-value-input">
                <div class="input-container">
                  <el-input-number 
                    v-model="cornerValue" 
                    :min="0" 
                    :max="100" 
                    @change="markAsChanged" 
                    size="small"
                    controls-position="right"
                  />
                </div>
                <div class="select-container">
                  <el-select v-model="cornerUnit" @change="markAsChanged" size="small">
                    <el-option v-for="(label, value) in unitOptions" :key="value" :label="$t('Editor.imageEditor.unitOptions.' + value)" :value="value" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- 图片效果 -->
          <el-form-item :label="$t('imageEditor.imageEffect')">
            <el-select v-model="imageEffect" @change="markAsChanged" style="width: 100%; min-width: 120px">
              <el-option v-for="(label, value) in imageEffectOptions" :key="value" :label="$t('Editor.imageEditor.imageEffectOptions.' + value)" :value="value" />
            </el-select>
          </el-form-item>

          <!-- 对齐方式 -->
          <el-form-item :label="$t('imageEditor.alignment')">
            <el-radio-group v-model="alignment" @change="markAsChanged" class="alignment-radio-group">
              <el-radio-button v-for="(label, value) in alignmentOptions" :key="value" :label="value">
                <span>{{ $t('Editor.imageEditor.alignmentOptions.' + value) }}</span>
              </el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 宽度设置 -->
          <el-form-item :label="$t('imageEditor.width')">
            <el-radio-group v-model="widthType" @change="handleWidthTypeChange" class="mb-2">
              <el-radio v-for="(label, value) in widthTypeOptions" :key="value" :label="value">{{ $t('Editor.imageEditor.widthTypeOptions.' + value) }}</el-radio>
            </el-radio-group>
            
            <div v-if="widthType === 'preset'" class="mt-2">
              <el-select v-model="imageWidth" @change="markAsChanged" style="width: 100%; min-width: 120px">
                <el-option v-for="(label, value) in presetWidthOptions" :key="value" :label="$t('Editor.imageEditor.presetWidthOptions.' + value)" :value="value" />
              </el-select>
            </div>
            
            <div v-if="widthType === 'custom'" class="mt-2 custom-value-container">
              <el-slider
                v-model="customWidth"
                :min="1"
                :max="widthUnit === '%' ? 100 : 2000"
                :step="widthUnit === '%' ? 5 : 10"
                @change="markAsChanged"
                class="flex-grow-1 custom-slider"
              />
              <div class="custom-value-input">
                <div class="input-container">
                  <el-input-number 
                    v-model="customWidth" 
                    :min="1" 
                    :max="widthUnit === '%' ? 100 : 2000"
                    @change="markAsChanged" 
                    size="small"
                    controls-position="right"
                  />
                </div>
                <div class="select-container">
                  <el-select v-model="widthUnit" @change="handleWidthUnitChange" size="small">
                    <el-option v-for="(label, value) in unitOptions" :key="value" :label="$t('Editor.imageEditor.unitOptions.' + value)" :value="value" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-form-item>
          
          <!-- 高度设置 -->
          <el-form-item :label="$t('imageEditor.height')">
            <el-radio-group v-model="heightType" @change="handleHeightTypeChange" class="mb-2">
              <el-radio v-for="(label, value) in heightTypeOptions" :key="value" :label="value">{{ $t('Editor.imageEditor.heightTypeOptions.' + value) }}</el-radio>
            </el-radio-group>
            
            <div v-if="heightType === 'custom'" class="mt-2 custom-value-container">
              <el-slider
                v-model="customHeight"
                :min="1"
                :max="heightUnit === '%' ? 100 : 2000"
                :step="heightUnit === '%' ? 5 : 10"
                @change="markAsChanged"
                class="flex-grow-1 custom-slider"
              />
              <div class="custom-value-input">
                <div class="input-container">
                  <el-input-number 
                    v-model="customHeight" 
                    :min="1" 
                    :max="heightUnit === '%' ? 100 : 2000"
                    @change="markAsChanged" 
                    size="small"
                    controls-position="right"
                  />
                </div>
                <div class="select-container">
                  <el-select v-model="heightUnit" @change="handleHeightUnitChange" size="small">
                    <el-option v-for="(label, value) in unitOptions" :key="value" :label="$t('Editor.imageEditor.unitOptions.' + value)" :value="value" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{ $t('Editor.imageEditor.applyChanges') }}</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="isMultiSelect" 
      :locale="localeLang"
      @confirmSelection="confirmSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed } from 'vue'
import { Picture, Upload, Delete, Location, Position } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const cornerTypeOptions = {
  none: t('Editor.imageEditor.cornerTypeOptions.none'),
  preset: t('Editor.imageEditor.cornerTypeOptions.preset'),
  custom: t('Editor.imageEditor.cornerTypeOptions.custom'),
  circle: t('Editor.imageEditor.cornerTypeOptions.circle')
}
const presetCornerOptions = {
  rounded: t('Editor.imageEditor.presetCornerOptions.rounded'),
  'rounded-3': t('Editor.imageEditor.presetCornerOptions.rounded3'),
  'rounded-4': t('Editor.imageEditor.presetCornerOptions.rounded4')
}
const unitOptions = {
  px: t('Editor.imageEditor.unitOptions.px'),
  '%': t('Editor.imageEditor.unitOptions.percent')
}
const imageEffectOptions = {
  '': t('Editor.imageEditor.imageEffectOptions.none'),
  shadow: t('Editor.imageEditor.imageEffectOptions.shadow'),
  border: t('Editor.imageEditor.imageEffectOptions.border'),
  'shadow border': t('Editor.imageEditor.imageEffectOptions.shadowBorder')
}
const alignmentOptions = {
  left: t('Editor.imageEditor.alignmentOptions.left'),
  center: t('Editor.imageEditor.alignmentOptions.center'),
  right: t('Editor.imageEditor.alignmentOptions.right')
}
const widthTypeOptions = {
  auto: t('Editor.imageEditor.widthTypeOptions.auto'),
  preset: t('Editor.imageEditor.widthTypeOptions.preset'),
  custom: t('Editor.imageEditor.widthTypeOptions.custom')
}
const presetWidthOptions = {
  'w-100': t('Editor.imageEditor.presetWidthOptions.w100'),
  'w-75': t('Editor.imageEditor.presetWidthOptions.w75'),
  'w-50': t('Editor.imageEditor.presetWidthOptions.w50'),
  'w-25': t('Editor.imageEditor.presetWidthOptions.w25')
}
const heightTypeOptions = {
  auto: t('Editor.imageEditor.heightTypeOptions.auto'),
  custom: t('Editor.imageEditor.heightTypeOptions.custom')
}

// 定义组件名称
defineOptions({
  name: 'ImageEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})
console.info('props', props.blockElement)
const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 原始HTML
const originalHtml = ref('')

// 图片属性
const imageUrl = ref('')
const imageAlt = ref('示例图片')
const imageRounded = ref('rounded')
const imageEffect = ref('')
const alignment = ref('center')
const imageWidth = ref('w-100')
const isMultiSelect = ref(false)

// 圆角设置
const cornerType = ref('preset')
const cornerValue = ref(8)
const cornerUnit = ref('px')

// 宽度设置
const widthType = ref('auto')
const customWidth = ref(100)
const widthUnit = ref('%')

// 高度设置
const heightType = ref('auto')
const customHeight = ref(300)
const heightUnit = ref('px')

// 图片列表 (用于多选模式)
const imageList = ref<{url: string, alt: string}[]>([])

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')

// 处理圆角类型变化
const handleCornerTypeChange = () => {
  markAsChanged()
  if (cornerType.value === 'circle') {
    imageRounded.value = 'rounded-circle'
  } else if (cornerType.value === 'preset') {
    imageRounded.value = 'rounded'
  } else if (cornerType.value === 'none') {
    imageRounded.value = ''
  }
  // 自定义类型不改变imageRounded，使用cornerValue和cornerUnit
}

// 处理宽度类型变化
const handleWidthTypeChange = () => {
  markAsChanged()
  if (widthType.value === 'auto') {
    imageWidth.value = 'w-100'
  } else if (widthType.value === 'preset') {
    imageWidth.value = 'w-100'
  }
  // 自定义类型使用customWidth和widthUnit
}

// 处理高度类型变化
const handleHeightTypeChange = () => {
  markAsChanged()
  // 高度类型变化不影响其他值
}

// 宽度增减函数
const incrementWidth = () => {
  customWidth.value += widthUnit.value === '%' ? 5 : 10
  if (customWidth.value > 2000) customWidth.value = 2000
  markAsChanged()
}

const decrementWidth = () => {
  customWidth.value -= widthUnit.value === '%' ? 5 : 10
  if (customWidth.value < 1) customWidth.value = 1
  markAsChanged()
}

// 高度增减函数
const incrementHeight = () => {
  customHeight.value += heightUnit.value === '%' ? 5 : 10
  if (customHeight.value > 2000) customHeight.value = 2000
  markAsChanged()
}

const decrementHeight = () => {
  customHeight.value -= heightUnit.value === '%' ? 5 : 10
  if (customHeight.value < 1) customHeight.value = 1
  markAsChanged()
}

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

/**
 * 从元素提取对齐方式
 */
const extractAlignment = (element: Element) => {
  if (element.classList.contains('text-center') || 
      element.classList.contains('justify-content-center') ||
      (element as HTMLElement).style?.textAlign === 'center') {
    alignment.value = 'center'
  } else if (element.classList.contains('text-right') || 
             element.classList.contains('justify-content-end') ||
             (element as HTMLElement).style?.textAlign === 'right') {
    alignment.value = 'right'
  } else {
    alignment.value = 'left'
  }
}

/**
 * 从样式中提取值和单位
 */
const extractValueAndUnit = (value: string): {value: number, unit: string} => {
  const match = value.match(/^(\d+)(px|%|rem|em|vh|vw)?$/)
  if (match) {
    return {
      value: parseInt(match[1]),
      unit: match[2] || 'px'
    }
  }
  return { value: 0, unit: 'px' }
}

/**
 * 提取图片及其属性
 */
const extractImageElements = () => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 查找图片元素 - 优先查找带有data-bs-component属性的图片
    let imgElement = props.blockElement.querySelector('img[data-bs-component="bootstrap-image"]')
    
    // 如果没找到特定属性的图片，则查找任意img标签
    if (!imgElement) {
      imgElement = props.blockElement.querySelector('img')
    }
    
    // 如果是img元素本身
    if (!imgElement && props.blockElement.tagName === 'IMG') {
      imgElement = props.blockElement
    }
    
    if (imgElement) {
      // 提取图片URL
      const imgSrc = imgElement.getAttribute('src')
      if (imgSrc) {
        imageUrl.value = imgSrc
      } else {
        // 使用更可靠的默认图片URL
        imageUrl.value = 'https://static-cse.canva.cn/blob/1234/default-image.jpg'
      }
      
      // 提取alt属性
      imageAlt.value = imgElement.getAttribute('alt') || '示例图片'
      
      // 提取圆角样式
      if (imgElement.classList.contains('rounded-circle')) {
        imageRounded.value = 'rounded-circle'
        cornerType.value = 'circle'
      } else if (imgElement.classList.contains('rounded-3')) {
        imageRounded.value = 'rounded-3'
        cornerType.value = 'preset'
      } else if (imgElement.classList.contains('rounded')) {
        imageRounded.value = 'rounded'
        cornerType.value = 'preset'
      } else {
        // 检查是否有自定义圆角
        const style = imgElement.getAttribute('style') || ''
        const borderRadiusMatch = style.match(/border-radius:\s*([^;]+)/)
        if (borderRadiusMatch) {
          cornerType.value = 'custom'
          const { value, unit } = extractValueAndUnit(borderRadiusMatch[1])
          cornerValue.value = value
          cornerUnit.value = unit
        } else {
          imageRounded.value = ''
          cornerType.value = 'none'
        }
      }
      
      // 提取效果样式
      const hasShadow = imgElement.classList.contains('shadow')
      const hasBorder = imgElement.classList.contains('border')
      
      if (hasShadow && hasBorder) {
        imageEffect.value = 'shadow border'
      } else if (hasShadow) {
        imageEffect.value = 'shadow'
      } else if (hasBorder) {
        imageEffect.value = 'border'
      } else {
        imageEffect.value = ''
      }
      
      // 提取宽度
      if (imgElement.classList.contains('w-75')) {
        imageWidth.value = 'w-75'
        widthType.value = 'preset'
      } else if (imgElement.classList.contains('w-50')) {
        imageWidth.value = 'w-50'
        widthType.value = 'preset'
      } else if (imgElement.classList.contains('w-25')) {
        imageWidth.value = 'w-25'
        widthType.value = 'preset'
      } else if (imgElement.classList.contains('w-100')) {
        imageWidth.value = 'w-100'
        widthType.value = 'auto'
      } else {
        // 检查是否有自定义宽度
        const style = imgElement.getAttribute('style') || ''
        const widthMatch = style.match(/width:\s*([^;]+)/)
        if (widthMatch) {
          widthType.value = 'custom'
          const { value, unit } = extractValueAndUnit(widthMatch[1])
          customWidth.value = value
          widthUnit.value = unit
        } else {
          imageWidth.value = 'w-100'
          widthType.value = 'auto'
        }
      }
      
      // 提取高度
      const style = imgElement.getAttribute('style') || ''
      const heightMatch = style.match(/height:\s*([^;]+)/)
      if (heightMatch && heightMatch[1] !== 'auto') {
        heightType.value = 'custom'
        const { value, unit } = extractValueAndUnit(heightMatch[1])
        customHeight.value = value
        heightUnit.value = unit
      } else {
        heightType.value = 'auto'
      }
      
      // 检查是否为多图模式
      const parentElement = props.blockElement.querySelector('.row')
      if (parentElement && parentElement.querySelectorAll('img').length > 1) {
        isMultiSelect.value = true
        
        // 提取所有图片
        const allImages = parentElement.querySelectorAll('img')
        imageList.value = Array.from(allImages).map(img => ({
          url: img.getAttribute('src') || '',
          alt: img.getAttribute('alt') || ''
        }))
      }
      
      // 提取对齐方式
      const colElement = props.blockElement.querySelector('[class*="col-"]')
      if (colElement) {
        extractAlignment(colElement)
      }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取图片元素时出错:', error)
    return false
  }
}

// 获取token
const getToken = () => {
  // 从localStorage或cookie获取token
  const authToken = localStorage.getItem('auth_token') || ''
  token.value = authToken
}

// 组件挂载时，提取图片数据
onMounted(() => {
  getToken()
  const extracted = extractImageElements()
  
  if (!extracted) {
    // 如果无法提取或没有找到图片，使用默认值
    imageUrl.value = 'https://via.placeholder.com/800x450'
    imageAlt.value = '示例图片'
  }
  
  // 初始化时标记为未更改
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 打开文件管理器
const openFileManager = () => {
  visibleDialog.value = true
}

// 确认删除图片
const confirmDeleteImage = () => {
  ElMessageBox.confirm(
    '确定要删除当前图片吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    imageUrl.value = ''
    markAsChanged()
    ElMessage.success('图片已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// 文件选择确认
const confirmSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0) return
  
  if (isMultiSelect.value) {
    // 多选模式
    imageList.value = selectedFiles.map(file => ({
      url: file.path || file.url,
      alt: imageAlt.value
    }))
    // 设置第一张图片为预览图
    imageUrl.value = imageList.value[0].url
  } else {
    // 单选模式
    const file = selectedFiles[0]
    imageUrl.value = file.path || file.url
  }
  
  markAsChanged()
}

/**
 * 生成图片样式字符串
 */
const generateImageStyle = () => {
  const styles = []
  
  // 添加自定义圆角
  if (cornerType.value === 'custom') {
    styles.push(`border-radius: ${cornerValue.value}${cornerUnit.value}`)
  }
  
  // 添加自定义宽度
  if (widthType.value === 'custom') {
    styles.push(`width: ${customWidth.value}${widthUnit.value}`)
  }
  
  // 添加自定义高度
  if (heightType.value === 'custom') {
    styles.push(`height: ${customHeight.value}${heightUnit.value}`)
  }
  
  return styles.length > 0 ? `style="${styles.join('; ')}"` : ''
}

/**
 * 生成图片类名
 */
const generateImageClasses = () => {
  const classes = ['img-fluid']
  
  // 添加圆角类
  if (cornerType.value === 'preset' || cornerType.value === 'circle') {
    classes.push(imageRounded.value)
  }
  
  // 添加效果类
  if (imageEffect.value.includes('shadow')) {
    classes.push('shadow')
  }
  if (imageEffect.value.includes('border')) {
    classes.push('border')
  }
  
  // 添加宽度类
  if (widthType.value === 'auto' || widthType.value === 'preset') {
    classes.push(imageWidth.value)
  }
  
  return classes.join(' ')
}

/**
 * 生成单个图片HTML
 */
const generateSingleImageHtml = (): string => {
  // 生成对齐类
  const alignClass = alignment.value === 'center' ? 'justify-content-center' : 
                    alignment.value === 'right' ? 'justify-content-end' : 
                    'justify-content-start'
  
  // 生成单图HTML
  return `
<div class="bootstrap-image" data-bs-component="image">
  <div class="p-0 container-fluid">
    <div class="row ${alignClass}">
      <div class="col-12 col-md-10 col-lg-8">
        <img src="${imageUrl.value}" class="${generateImageClasses()}" alt="${imageAlt.value}" ${generateImageStyle()}>
      </div>
    </div>
  </div>
</div>
  `.trim()
}

/**
 * 生成多图HTML
 */
const generateMultiImageHtml = (): string => {
  // 如果没有图片，返回单图模板
  if (imageList.value.length === 0) {
    return generateSingleImageHtml()
  }
  
  // 生成对齐类
  const alignClass = alignment.value === 'center' ? 'justify-content-center' : 
                    alignment.value === 'right' ? 'justify-content-end' : 
                    'justify-content-start'
  
  // 确定列数和列宽
  const colCount = imageList.value.length > 3 ? 4 : imageList.value.length
  const colWidth = 12 / Math.min(colCount, 4)
  
  // 生成图片HTML
  const imagesHtml = imageList.value.map(img => `
    <div class="col-12 col-md-${colWidth}">
      <img src="${img.url}" class="${generateImageClasses()}" alt="${img.alt || imageAlt.value}" ${generateImageStyle()}>
    </div>
  `).join('\n')
  
  // 生成多图HTML
  return `
<div class="bootstrap-image-group" data-bs-component="image-group">
  <div class="p-0 container-fluid">
    <div class="row ${alignClass}">
      <div class="col-12 col-md-10 col-lg-12">
        <div class="row g-4">
          ${imagesHtml}
        </div>
      </div>
    </div>
  </div>
</div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    let html = ''
    
    // 根据是否多选生成不同的HTML
    if (isMultiSelect.value && imageList.value.length > 1) {
      html = generateMultiImageHtml()
    } else {
      html = generateSingleImageHtml()
    }
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用图片更改时出错:', error)
  }
}

// 处理宽度单位变化
const handleWidthUnitChange = () => {
  markAsChanged()
  // 当单位从px切换到%时，确保值不超过100%
  if (widthUnit.value === '%' && customWidth.value > 100) {
    customWidth.value = 100
  }
}

// 处理高度单位变化
const handleHeightUnitChange = () => {
  markAsChanged()
  // 当单位从px切换到%时，确保值不超过100%
  if (heightUnit.value === '%' && customHeight.value > 100) {
    customHeight.value = 100
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.image-preview-container {
  width: 100%;
  height: 200px;
  background-color: #F6F6F6;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 4px;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .placeholder-icon {
    font-size: 40px;
    color: #9E9E9E;
    margin-bottom: 10px;
  }
  
  .placeholder-text {
    color: #9E9E9E;
    font-size: 16px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  .delete-btn {
    color: #707070;
    background: #FFFFFF;
    border-radius: 5px;
  }
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

.editor-form {
  max-width: 100%;
  margin: 0 auto;
  
  .el-form-item {
    margin-bottom: 24px;
  }
}

.alignment-radio-group {
  width: 100%;
  display: flex;
  
  .el-radio-button {
    flex: 1;
  }
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-value-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100% ;
}

.custom-value-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  
  .input-container {
    flex: 0 0 60%;
    
    .el-input-number {
      width: 100%;
    }
  }
  
  .select-container {
    flex: 0 0 35%;
    
    .el-select {
      width: 100%;
    }
  }
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.flex-grow-1 {
  flex-grow: 1;
}

:deep(.el-select) {
  &.el-select--small {
    width: 100%;
  }
}

// 恢复原生控制按钮
:deep(.el-input-number) {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

.custom-slider {
  :deep(.el-slider__button) {
    width: 16px;
    height: 16px;
    border: 2px solid #409eff;
  }
  
  :deep(.el-slider__bar) {
    background-color: #409eff;
    height: 6px;
  }
  
  :deep(.el-slider__runway) {
    height: 6px;
  }
}
</style> 