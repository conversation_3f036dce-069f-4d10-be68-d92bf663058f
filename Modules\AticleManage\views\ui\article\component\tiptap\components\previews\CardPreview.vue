<template>
  <div class="preview-content">
    <div class="card" style="width: 18rem;">
      <div class="card-img-placeholder"></div>
      <div class="card-body">
        <h5 class="card-title">卡片标题</h5>
        <p class="card-text">卡片内容示例文本</p>
        <a href="#" class="btn btn-primary">按钮</a>
      </div>
    </div>
  </div>
</template>

<style scoped>
.preview-content {
  width: 200px;
}
.card-img-placeholder {
  height: 100px;
  background-color: #e0e0e0;
}
</style> 