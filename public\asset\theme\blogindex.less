@import "../variable.less";

.bwms-page {
  .left-content {
    .banner {
      margin-top: 10px;
      padding: 0;
      position: relative;
      overflow: hidden;

      .swiper-slide {
        max-height: 316px;
        .df(center, center);

        .pic {
          .img {
            object-fit: cover;
          }
        }

        .text-box {
          margin: 30px 0;
          padding: 0 40px;
          width: 100%;
          color: #fff;

          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          .df(flex-start, flex-start, column);

          .title {
            font-size: 40px;
            font-weight: bold;
            line-height: 1;
          }

          .slogan {
            padding: 10px 0;
            margin-top: 20px;

            .line {
              font-size: 20px;
              line-height: 1.3;
            }
          }

          .link {
            .btn-radius(50px, 15px, 25px, #fff, transparent, rgba(255, 255, 255, .3));

            margin-top: 20px;
            border: 1px solid #fff;
            font-size: 13px;
          }
        }
      }

      &:hover {
        .navigation-box {
          .iconfont {
            opacity: .5;
          }
        }
      }

      .navigation-box {
        .iconfont {
          border-radius: 50%;
          opacity: 0;
          background: #fff;
          color: #34495e;
          font-size: 10px;
          width: 50px;
          height: 50px;
          transition: opacity .3s ease-in-out;
          font-size: 22px;

          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;

          .df(center, center);

          &.icon-backios {
            left: 10px;
          }

          &.icon-right {
            right: 10px;
          }
        }
      }

      .swiper-pagination {
        bottom: 10px;
        left: 0;
        width: 100%;
        .df(center, center);

        .swiper-pagination-bullet {
          margin: 0 4px;
          box-shadow: #666 0 0 .25rem;
          width: 16px;
          height: 4px;
          border-radius: 4px;
          cursor: pointer;
        }
      }
    }

    .tag-filter-box {
      p {
        .df(center);
        padding: 12px;
        color: #34495e;
        font-size: 13px;

        i.iconfont {
          margin-right: 4px;
          display: block;
          color: #34495e;
        }

        span {
          padding: 0 4px;
          color: #3555cc;
          font-weight: bold;
        }

        a.iconfont {
          margin-left: 4px;
          color: #c4cfdb;
          display: block;
        }
      }
    }

    .classify-filter-box {
      .filter-list {
        padding: 12px;
        .df(center);

        .item {
          margin-right: 10px;
          .btn-radius(50px, 9px, 16px, #666, transparent, #3555CC, #fff);
          font-size: 13px;
          border: 1px solid #e5e9ee;

          &:hover {
            border-color: #3555CC;
          }
        }
      }
    }

    .blog-list {
      padding: 12px;

      .blog-item {
        margin-bottom: 24px;
        padding-bottom: 24px;
        border-bottom: 1px solid #e5e9ee;

        .blog-tit {
          margin-bottom: 8px;
          .df(center);

          .tag {
            margin-right: 4px;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 16px;
            line-height: 1.5;

            &.blue {
              color: rgb(59, 130, 246);
              background-color: rgba(59, 130, 246, .3);
            }

            &.red {
              color: rgb(239, 68, 68);
              background-color: rgba(239, 68, 68, .3);
            }

            &.yellow {
              color: rgb(245, 158, 11);
              background-color: rgba(245, 158, 11, .3);
            }
          }

          h4 {
            color: #34495e;
            font-size: 20px;
            font-weight: normal;
            .vertical(1);

            a {
              color: #34495e;
              font-size: 20px;
              font-weight: normal;
              .vertical(1);
            }
          }
        }

        .blog-content {
          .df(center);

          .time-desc-tag {
            flex-grow: 1;

            .time {
              padding-top: 8px;
              .df(center);

              .label-text {
                margin-right: 4px;
                color: #9ca3af;
                font-size: 13px;

                .df(center);

                .label {
                  margin-right: 3px;

                  .iconfont {
                    font-size: 14px;
                  }
                }
              }
            }

            .desc {
              margin-top: 8px;
              color: #9ca3af;
              font-size: 13px;
              line-height: 1.84;
              .vertical(2);
            }

            .tag-list {
              padding-top: 8px;
              .df(center);

              .tag-item {
                margin-right: 4px;
                border-radius: 14px;
                border: 1px solid #e5e9ee;
                padding: 8px 10px;
                color: #666666;
                cursor: pointer;
                font-size: 10px;
              }
            }
          }

          .pic {
            margin-left: 16px;
            border-radius: 4px;
            width: 160px;
            flex-shrink: 0;
            overflow: hidden;
            overflow: hidden;

            .img {
              transform: rotate(0) scaleX(1) scaleY(1);
              transition: transform .35s ease-in-out;
            }

            &:hover {
              .img {
                transform: rotate(3deg) scaleX(1.1) scaleY(1.1);
              }
            }
          }
        }
      }
    }

    .pagination-box {
      .df(center, center);

      a {
        margin: 5px;
        border-radius: 5px;
        color: #666;
        font-size: 13px;

        min-width: 30px;
        min-height: 30px;
        .df(center, center);

        &.active {
          background-color: #3555CC;
          color: #fff;
        }

        &:hover {
          color: #3555CC;
        }
      }

      .more {
        color: #666;
        font-size: 13px;
      }
    }
  }

  .right-side {
    .user-info {
      padding: 54.5px 24px;
      .df(center, center, column);
      text-align: center;

      .avatar {
        border-radius: 50%;
        width: 112px;
        height: 112px;
        overflow: hidden;
      }

      .nickname {
        margin-top: 20px;
        color: #34495e;
        font-size: 20px;
        line-height: 1.3;
      }

      .sign-word {
        margin-top: 16px;
        color: #9ca3af;
        font-size: 13px;
      }
    }

    .classify {
      padding: 12px;

      .col-6 {
        padding-left: 5px;
        padding-right: 5px;
      }

      .classify-item {
        margin-bottom: 12px;
        border-radius: 50px;
        border: 1px solid #e5e9ee;
        padding: 6px 14px;
        font-size: 13px;
        line-height: 1.6;
        color: #666;
        .df(center, center);
        transition: all .35s ease-in-out;

        .iconfont {
          margin-right: 4px;
          font-size: 12px;
          transition: color .35s ease-in-out;
        }

        &:hover {
          border-color: #3555CC;
          color: #3555CC;

          .iconfont {
            color: #3555CC;
          }
        }
      }
    }

    .tag-list {
      padding: 12px;

      .list {
        .df(center);
        flex-wrap: wrap;

        .tag-item {
          margin-right: 10px;
          margin-bottom: 10px;
          .btn-radius(50px, 8px, 14px, #666, #f7f7f7, #eaeaea, #666);
          font-size: 13px;
          .df(center);

          &:hover {
            box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
          }

          .num {
            margin-left: 3px;
            border-radius: 50%;
            background-color: #9ca3af;
            font-size: 13px;
            width: 15px;
            height: 15px;
            color: #fff;
            .df(center, center);
          }
        }
      }
    }

    .popular-blogs {
      padding: 12px;

      .list {
        .blog-item {
          margin-bottom: 8px;
          .df(center);

          &:last-child {
            margin-bottom: 0;
          }

          .iconfont {
            font-size: 13px;
            color: #c4cfdb;
          }

          a {
            color: #34495e;
            font-size: 13px;
            line-height: 1.2;
          }
        }
      }
    }

    .friendship-link {
      padding: 12px 12px 0;

      .row {
        margin: 0 -5px;
      }

      .col-box {
        margin-bottom: 12px;
        padding-left: 5px;
        padding-right: 5px;

        a {
          border-radius: 4px;
          background-color: #f7f7f7;
          color: #666666;
          font-size: 13px;
          display: block;
          text-align: center;
          line-height: 40px;
        }
      }
    }
  }
}