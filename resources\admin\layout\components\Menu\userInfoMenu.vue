<template>
  <div class="menu-box">
    <div class="header-container">
      <div class="logo">
        <img :src="$asset('Dashboard/Asset/logos.png')" alt="Bingo" />
      </div>
      <div class="back-btn" @click="router.push(`/dashboard`)" :title="t('dashboard.userMenu.backToDashboard')">
        <el-icon><Back /></el-icon>
      </div>
    </div>

    <div class="menu-container">
      <el-menu :default-active="activeTab" :router="true" class="menu-list" @select="handleSelect">
        <el-menu-item-group>
          <template #title>
            <span class="group-title">{{ t('dashboard.userMenu.title') }}</span>
          </template>
          <el-menu-item index="/userSettings">
            <el-icon><User /></el-icon>
            <span>{{ t('dashboard.userMenu.items.personalInfo') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/account">
            <el-icon><Link /></el-icon>
            <span>{{ t('dashboard.userMenu.items.accountBinding') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/security">
            <el-icon><Lock /></el-icon>
            <span>{{ t('dashboard.userMenu.items.mfa') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/socialIdentity">
            <el-icon><Connection /></el-icon>
            <span>{{ t('dashboard.userMenu.items.socialIdentity') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/enterpriseIdentity">
            <el-icon><OfficeBuilding /></el-icon>
            <span>{{ t('dashboard.userMenu.items.enterpriseIdentity') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/safety">
            <el-icon><HelpFilled /></el-icon>
            <span>{{ t('dashboard.userMenu.items.accountSecurity') }}</span>
          </el-menu-item>
          <el-menu-item index="/userSettings/log">
            <el-icon><Document /></el-icon>
            <span>{{ t('dashboard.userMenu.items.accessLog') }}</span>
          </el-menu-item>
        </el-menu-item-group>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '/admin/stores/modules/app'
import { 
  User, 
  Link, 
  Lock, 
  Document, 
  HelpFilled, 
  Connection, 
  OfficeBuilding, 
  Operation,
  Back
} from '@element-plus/icons-vue'

import LogoImg from '@/public/asset/image/logo.png'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const { t } = useI18n()

const activeTab = ref('/userSettings')
const appCode = ref(localStorage.getItem('app_code') || 'admin')

const handleCommand = (command: string) => {
  appCode.value = command
  localStorage.setItem('app_code', command)
  window.location.reload()
}

const handleSelect = (key: string) => {
  router.push(key)
}

onMounted(() => {
  appStore.setPageName(t('dashboard.userMenu.title'))
  activeTab.value = route.path
})

watch(() => route.path, (newPath) => {
  activeTab.value = newPath
})
</script>

<style scoped lang="scss">
.menu-box {
  padding: 20px 20px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;

    .logo {
      width: 78px;
      height: auto;
      
      img {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    .back-btn {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f6f6f6;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .el-icon {
        font-size: 16px;
        color: #666;
        transition: all 0.3s ease;
      }

      &:hover {
        background-color: #eff7fd;
        
        .el-icon {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .menu-container {
    padding-bottom: 40px;

    :deep(.el-menu) {
      border-right: none;

      .el-menu-item-group {
        margin-top: 10px;

        .el-menu-item-group__title {
          padding-left: 0;
          color: #7f7f7f;
          font-size: 16px;
          line-height: 1.3125;
          margin-bottom: 12px;
        }

        ul {
          .el-menu-item {
            border-radius: 50px;
            padding: 14px 12px;
            margin: 8px 0;
            line-height: 1.3125;
            height: auto;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            white-space: normal;
            min-height: 50px;
            padding: 12px 16px;
            line-height: 1.4;
            word-break: break-word;

            .el-icon {
              text-align: left;
              justify-content: flex-start;
              font-size: 16px;
              width: 16px;
              margin-right: 12px;
              flex-shrink: 0;
            }

            span {
              flex: 1;
              overflow-wrap: break-word;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            &:hover {
              background-color: #eff7fd;
            }

            &.is-active {
              background-color: #eff7fd;
              color: var(--el-color-primary);
              font-weight: 600;

              .el-icon {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
}

.nav-box {
  &::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    width: 4px;
    background-color: #ccc;
    display: none;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      display: block;
    }
  }
}
</style>
