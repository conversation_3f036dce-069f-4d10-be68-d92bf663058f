<template>
  <div class="table-page bwms-module">
    <div class="module-header">
        <FilterPopover v-model="filterDialog">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
              <el-icon size="16">
                <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="" />
              </el-icon>
              <span>篩選</span>
            </el-button>
          </template>
          <el-form :model="search" label-position="top">
            <el-form-item label="頻道ID">
              <el-input v-model="search.channelId" placeholder="請輸入頻道ID" size="large" />
            </el-form-item>
            <el-form-item label="頻道標題">
              <el-input v-model="search.title" placeholder="請輸入頻道標題" size="large" />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="refreshChannel">
                <el-icon size="16">
                  <Refresh />
                </el-icon>
                <span>重置</span>
              </el-button>
              <el-button class="button-no-border" @click="searchChannel" type="primary">
                <el-icon size="16">
                  <Filter />
                </el-icon>
                <span>篩選</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button @click="openApiSettings" type="primary">
          <el-icon><Setting /></el-icon>
          <span>API設置</span>
        </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table ref="tableRefs" :data="channelList" style="width: 100%; height: 100%" v-loading="loading">
          <template #empty>
            <el-empty description="暫無數據" image-size="100px" />
          </template>
          <el-table-column prop="channelId" label="頻道 ID" width="200" />
          <el-table-column prop="title" label="頻道標題" min-width="120">
            <template #default="scope">
              <div class="channel-title">
                <div class="title-main">{{ scope.row.title }}</div>
                <!-- <div class="title-sub">{{ scope.row.titleEn }}</div> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="狀態" width="150">
            <template #default="scope">
              <el-tag :type="getChannelStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastUpdate" label="最後更新" width="220" />
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button 
                  type="text"
                >
                  <el-switch  @change="toggleChannelStatus(scope.row)"/>
                </el-button>
                <el-button 
                  type="text" 
                  @click="editChannel(scope.row)"
                >
                  <el-icon>
                    <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每頁顯示</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  暫無數據
                </div>
              </template>
            </el-select>
            <span class="total-text">共 {{ total }} 條記錄</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <EditDialog v-model="editDialogVisible" :channel-id="editChannelId" @update:model-value="onEditDialogClose" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, nextTick, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '/admin/support/http'
import { Filter, Plus, Loading, Refresh, Setting, Edit } from '@element-plus/icons-vue'
const EditDialog = defineAsyncComponent(() => import('./components/edit.vue'))

// 直播频道接口定义
interface Channel {
  id: number
  channelId: string
  title: string
  titleEn: string
  status: string
  lastUpdate: string
}

const router = useRouter()
const route = useRoute()

// 直播频道数据
const tableRefs = ref<any>(null)
const channelList = ref<Channel[]>([
  {
    id: 1,
    channelId: 'CH001',
    title: '翡翠台',
    titleEn: 'Jade Channel',
    status: '正在直播',
    lastUpdate: '2024-01-15 14:30'
  },
  {
    id: 2,
    channelId: 'CH002',
    title: '明珠台',
    titleEn: 'Pearl Channel',
    status: '已關閉',
    lastUpdate: '2024-01-15 12:00'
  },
  {
    id: 3,
    channelId: 'CH003',
    title: 'J2台',
    titleEn: 'J2 Channel',
    status: '正在直播',
    lastUpdate: '2024-01-15 13:15'
  }
])
const loading = ref(false)

// 编辑对话框相关
const editDialogVisible = ref(false)
const editChannelId = ref<number|null>(null)

// 搜索相关
const filterDialog = ref(false)
const search = reactive({
  channelId: '',
  title: ''
})

// 获取频道列表的方法
async function getChannelList() {
  loading.value = true

  try {
    // 构建请求参数
    const params: any = {
      page: page.value,
      limit: limit.value
    }

    // 添加搜索条件
    if (search.channelId) {
      params.channelId = search.channelId
    }

    if (search.title) {
      params.title = search.title
    }

    // 调用获取频道列表接口
    const response = await http.get('/admin/live-channel', params)

    if (response.data && response.data.code === 200) {
      channelList.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      // 错误处理
      ElMessage.error(response.data?.message || '獲取數據失敗')
      // 如果请求失败但状态码不是200，使用空数据
      channelList.value = []
      total.value = 0
    }
  } catch (error) {
    channelList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页器
let page = ref(1)
let limit = ref(20)
let total = ref(0)
const changePage = () => {
  getChannelList()
}

const searchChannel = async () => {
  page.value = 1 // 搜索时重置到第一页
  await getChannelList()
  filterDialog.value = false
}

const refreshChannel = () => {
  // 重置搜索条件
  search.channelId = ''
  search.title = ''

  // 重置分页
  page.value = 1
  limit.value = 20

  // 重新获取数据
  getChannelList()
  filterDialog.value = false
}

// 页面初始化
const pageInit = async () => {
  await getChannelList()
}

// 切换频道状态
const toggleChannelStatus = async (channel: Channel) => {
  try {
    const newStatus = channel.status === '正在直播' ? '已關閉' : '正在直播'
    const response = await http.put(`/admin/live-channel/${channel.id}/status`, {
      status: newStatus
    })
    
    if (response.data && response.data.code === 200) {
      channel.status = newStatus
      channel.lastUpdate = new Date().toLocaleString('zh-CN')
      ElMessage.success(`${newStatus === '正在直播' ? '開啟' : '關閉'}成功`)
    } else {
      ElMessage.error(response.data?.message || '操作失敗')
    }
  } catch (error) {
    ElMessage.error('操作失敗')
  }
}

// 编辑频道
const editChannel = (channel: Channel) => {
  editChannelId.value = channel.id
  editDialogVisible.value = true
}

// 编辑对话框关闭回调
const onEditDialogClose = () => {
  editChannelId.value = null
  editDialogVisible.value = false
  // getChannelList() // 刷新列表
}

// API设置
const openApiSettings = () => {
  router.push('/live-channel/api-settings')
}

// 获取频道状态类型
const getChannelStatusType = (status: string) => {
  switch (status) {
    case '正在直播':
      return 'success'
    case '已關閉':
      return 'info'
    default:
      return 'info'
  }
}

// 刷新页面
const refreshPage = () => {
  // 重置所有状态
  search.channelId = ''
  search.title = ''
  page.value = 1
  limit.value = 20

  // 重新获取数据
  getChannelList()
  ElMessage.success('頁面已刷新')
}

onMounted(() => {
  // pageInit()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.channel-title {
  .title-main {
    font-weight: 500;
    color: #000;
    // margin-bottom: 4px;
  }

  // .title-sub {
  //   font-size: 12px;
  //   color: #909399;
  // }
}

.flex {
  display: flex;
}

.w-1\/2 {
  width: 50%;
}

.w-full {
  width: 100%;
}

.gap-4 {
  gap: 1rem;
}
</style>
