<?php

namespace Modules\Common\Util;

use Illuminate\Support\Facades\Session;

class SessionUtil
{
    public static function atomicProduce($name, $value, $expire = 3600): void
    {
        AtomicUtil::produce("$name:".Session::getId(), $value, $expire);
    }

    public static function atomicConsume($name): bool
    {
        return AtomicUtil::consume("$name:".Session::getId());
    }

    public static function atomicRemove($name): void
    {
        AtomicUtil::remove("$name:".Session::getId());
    }

    public static function startForWebFullUrl($redirect): string
    {
        return bingostart_web_full_url('session', [
            'api_token' => Session::getId(),
            'redirect' => $redirect,
        ]);
    }
}
