(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.no_backspace_delete=factory())})(this,function(){"use strict";function plugin(){var self=this;var orig_deleteSelection=self.deleteSelection;this.hook("instead","deleteSelection",evt=>{if(self.activeItems.length){return orig_deleteSelection.call(self,evt)}return false})}return plugin});