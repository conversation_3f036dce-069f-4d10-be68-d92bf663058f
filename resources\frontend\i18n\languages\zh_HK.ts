const zh_HK = {
    system: {
        name: 'BWMS 管理系統',
        chinese: '中文',
        english: '英文',
        confirm: '確定',
        cancel: '取消',
        warning: '警告',
        next: '下一頁',
        prev: '上一頁',
        yes: '是',
        no: '否',
        add: '新增',
        edit: '編輯',
        finish: '完成',
        back: '返回',
        update: '更新',
        export: '匯出',
        search: '搜尋',
        refresh: '重設',
        detail: '詳情',
        delete: '刪除',
        prompt: '提示',
        more: '更多',
        logout: '登出',
        fail: '失敗',
        success: '成功',
        close: '關閉',
        download: '下載',
        lang: {
            en: '英文',
            zh_HK: '繁體中文',
            zh_CN: '簡體中文',
        },
    },

    login: {
        email: '電郵',
        password: '密碼',
        sign_in: '登入',
        welcome: '👏歡迎回來',
        lost_password: '忘記密碼?',
        remember: '記住我',
        verify: {
            email: {
                required: '請先輸入電郵',
                invalid: '電郵地址無效',
            },

            password: {
                required: '請先輸入密碼',
            },
        },
    },

    register: {
        sign_up: '註冊',
    },
    generate: {
        schema: {
            title: '建立資料表',
            name: '表名稱',
            name_verify: '請輸入表名稱',
            engine: {
                name: '表引擎',
                verify: '請選擇表引擎',
                placeholder: '選擇表引擎',
            },
            default_field: {
                name: '預設欄位',
                created_at: '建立時間',
                updated_at: '更新時間',
                creator: '建立人',
                delete_at: '軟刪除',
            },
            comment: {
                name: '表註釋',
                verify: '請填寫表格註記/說明',
            },

            structure: {
                title: '建立資料結構',
                field_name: {
                    name: '欄位名稱',
                    verify: '請填入欄位名稱',
                },
                length: '長度',
                type: {
                    name: '類型',
                    placeholder: '選擇欄位類型',
                    verify: '請先選擇欄位類型',
                },
                form_label: '表單 Label',
                form_component: '表單元件',
                list: '列表',
                form: '表單',
                unique: '唯一',
                search: '查詢',
                search_op: {
                    name: '搜尋運算子',
                    placeholder: '選擇搜尋運算子',
                },
                nullable: 'nullable',
                default: '預設值',
                rules: {
                    name: '驗證規則',
                    placeholder: '選擇驗證規則',
                },
                operate: '操作',
                comment: '欄位註解',
            },
        },
        code: {
            title: '生成程式碼',
            module: {
                name: '模組',
                placeholder: '請選擇模組',
                verify: '請選擇模組',
            },
            controller: {
                name: '控制器',
                placeholder: '請輸入控制器名稱',
                verify: '請輸入控制器名稱',
            },
            model: {
                name: '模型',
                placeholder: '請輸入模型名稱',
                verify: '請輸入模型名稱',
            },
            paginate: '分頁',
            menu: {
                name: '選單名稱',
                placeholder: '請輸入選單名稱',
                verify: '請輸入選單名稱',
            },
        },
    },

    module: {
        create: '建立模組',
        update: '更新模組',
        form: {
            name: {
                title: '模組名稱',
                required: '請輸入模組名稱',
            },

            path: {
                title: '模組目錄',
                required: '請輸入模組目錄',
            },

            desc: {
                title: '模組描述',
            },

            keywords: {
                title: '模組關鍵字',
            },

            dirs: {
                title: '預設目錄',
                Controller: 'Controller 目錄',
                Model: 'Model 目錄',
                Database: 'Database 目錄',
                Request: 'Request 目錄',
            },
        },
    },
    widgets: {
        widget_configuration: '小工具配置',
        report_widget: '配置報告小工具',
        width: '寬度（1-12）',
        newLine: '強制新行',
        title: '小工具標題',
        manage: '管理小工具',
        add: '新增小工具',
        widgets: '小工具',
        close: '關閉',
        sure: '確定',
        loading: '載入中...',
        title_required: '請輸入小工具標題',
        x: 'x軸',
        y: 'y軸',
        h: '高度',
    },

    header: {
        settings: {
            tit1: '帳戶',
            li1: '個人資料和偏好設定',
            li2: '重設密碼',
            tit2: '系統語言',
            li3: '語言切換',
            langswitch: '語言切換',
        },
        contact: {
            title: '您的售後服務專員',
            label1: '服務時間',
            label2: '電子郵件',
            btn_text: '發送郵件',
        },
    },
    home: {
        title: '控制面板',
        btn_text1: '新增小工具',
        dialog_tit1: '確認刪除該模塊？',
        activityLog: {
            th1: '帳戶',
            th2: '瀏覽器',
            th3: '平台',
            th4: 'IP',
            th5: '狀態',
            th6: '登入時間',
        },
        analytics: {
            placeholder1: '開始時間',
            placeholder2: '結束時間',
            reportTypes: {
                basicUser: '基本用戶報告',
                trafficSource: '流量來源報告',
                pagePerformance: '頁面性能報告',
                ecommerce: '電子商務報告',
                userBehavior: '用戶行為報告',
                mobileApp: '移動應用報告',
                adPerformance: '廣告效果報告',
                content: '內容分析報告',
                geographic: '地理位置報告',
                technical: '技術分析報告',
                event: '事件分析報告',
                conversionFunnel: '轉換漏斗報告',
            },
        },
        news: {
            title: '新聞',
        },
        system: {
            title1: '網站儲存容量',
            title2: '離續訂還有幾天',
            con1: '天',
            btn_text1: '查看儲存',
            btn_text2: '立即續訂',
            btn_text3: '增加容量',
        },
    },
    404: {
        tips: '抱歉，您訪問的頁面不存在',
        btn_text: '回到首頁',
    },
    analytics: {
        dimensions: {
            date: '日期',
            country: '國家',
            deviceCategory: '設備類別',
            source: '來源',
            medium: '媒介',
            campaign: '活動',
            pagePath: '頁面路徑',
            itemName: '商品名稱',
            itemCategory: '商品類別',
            sessionSourceMedium: '會話來源/媒介',
            landingPage: '著陸頁',
            appVersion: '應用版本',
            operatingSystem: '作業系統',
            adGroup: '廣告組',
            adContent: '廣告內容',
            pageTitle: '頁面標題',
            region: '地區',
            city: '城市',
            browser: '瀏覽器',
            eventName: '事件名稱',
            eventCategory: '事件類別',
        },
        metrics: {
            totalUsers: '總用戶數',
            newUsers: '新用戶數',
            sessions: '會話數',
            bounceRate: '跳出率',
            screenPageViews: '頁面瀏覽量',
            averageSessionDuration: '平均會話時長',
            conversions: '轉換次數',
            averagePageLoadTime: '平均頁面載入時間',
            exitRate: '退出率',
            itemViews: '商品瀏覽次數',
            itemsAddedToCart: '加入購物車次數',
            purchases: '購買次數',
            itemRevenue: '商品收入',
            engagementRate: '參與率',
            conversionsPerSession: '每次會話轉換次數',
            crashFreeUsersRate: '無當機用戶率',
            userEngagementDuration: '用戶參與時長',
            adClicks: '廣告點擊次數',
            adImpressions: '廣告展示次數',
            adCost: '廣告成本',
            adConversions: '廣告轉換次數',
            averageTimeOnPage: '平均頁面停留時間',
            entrances: '入口次數',
            eventCount: '事件次數',
            eventValue: '事件價值',
            addToCarts: '加入購物車次數',
            checkouts: '結帳次數',
        },
    },
}

export default zh_HK
