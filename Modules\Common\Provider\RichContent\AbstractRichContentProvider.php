<?php

namespace Modules\Common\Provider\RichContent;

use Modules\Common\Html\HtmlConvertUtil;

abstract class AbstractRichContentProvider
{
    abstract public function name();

    abstract public function title();

    abstract public function render($name, $value, $param = []);

    public function toHtml($value, $htmlInterceptors = null)
    {
        return HtmlConvertUtil::callInterceptors($htmlInterceptors, $value);
    }

}
