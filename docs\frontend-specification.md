# 前端开发规格指南

## 目录
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [开发规范](#开发规范)
- [路由管理](#路由管理)
- [状态管理](#状态管理)
- [组件开发](#组件开发)

## 技术栈

- Vue 3.x
- TypeScript
- Vite
- Vue Router
- Pinia
- Element Plus
- SCSS
- Monaco Editor

## 项目结构

```bash
resources/
├── admin/                 # 管理后台
│   ├── components/       # 公共组件
│   ├── layout/          # 布局组件
│   ├── router/          # 路由配置
│   ├── store/           # 状态管理
│   ├── styles/          # 样式文件
│   └── views/           # 页面文件
├── frontend/             # 前台应用
└── assets/               # 静态资源

Modules/                  # 模块目录
└── YourModule/
    ├── Asset/           # 模块静态资源
    │   └── js/         # 模块 JS 文件
    ├── Resources/       # 模块资源
    │   └── views/      # 模块视图
    └── views/           # Vue 组件和路由
        ├── router.ts    # 模块路由配置
        └── ui/          # UI 组件
```

## 开发规范

### 1. 文件命名

- 组件文件：PascalCase（例如：`CourseDetail.vue`）
- 路由文件：kebab-case（例如：`course-routes.ts`）
- 工具文件：camelCase（例如：`courseUtil.ts`）
- 样式文件：kebab-case（例如：`course-style.scss`）

### 2. 组件规范

```vue
<template>
  <div class="course-detail">
    <!-- 模板内容 -->
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'CourseDetail',
  props: {
    // props 定义
  },
  setup(props) {
    // 组件逻辑
    return {
      // 返回数据
    }
  }
})
</script>

<style lang="scss" scoped>
.course-detail {
  // 样式定义
}
</style>
```

### 3. 路由配置

```typescript
import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/course',
    component: () => import('/admin/layout/index.vue'),
    meta: { title: 'Course 管理', icon: 'Tickets' },
    children: [
      {
        path: 'detail',
        name: 'courseDetail',
        component: () => import('./ui/content/detail.vue'),
        meta: { title: '课程详情' }
      }
    ]
  }
]
```

### 4. API 调用

```typescript
import { http } from '@/utils/http'

export const courseApi = {
  getList: (params: any) => http.get('/api/courses', { params }),
  getDetail: (id: number) => http.get(`/api/courses/${id}`),
  create: (data: any) => http.post('/api/courses', data),
  update: (id: number, data: any) => http.put(`/api/courses/${id}`, data),
  delete: (id: number) => http.delete(`/api/courses/${id}`)
}
```

## 路由管理

1. 模块路由配置
   - 每个模块独立的路由配置文件
   - 统一的路由元信息
   - 懒加载组件

2. 路由守卫
   - 权限控制
   - 页面标题
   - 登录状态检查

## 状态管理

1. Pinia Store 结构
```typescript
import { defineStore } from 'pinia'

export const useCourseStore = defineStore('course', {
  state: () => ({
    courseList: [],
    currentCourse: null
  }),
  actions: {
    async fetchCourses() {
      // 获取课程列表
    },
    async getCourseDetail(id: number) {
      // 获取课程详情
    }
  }
})
```

2. 状态管理原则
   - 模块化管理状态
   - 统一的数据获取方式
   - 响应式数据处理

## 组件开发

1. 组件设计原则
   - 单一职责
   - 可复用性
   - Props 验证
   - 事件规范

2. 公共组件
   - 表单组件
   - 表格组件
   - 弹窗组件
   - 上传组件

3. 业务组件
   - 课程卡片
   - ��程列表
   - 课程详情
   - 课程编辑器

## 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia']
    }),
    Components({
      dirs: ['resources/admin/components/']
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'resources'),
      '@admin': resolve(__dirname, 'resources/admin')
    }
  },
  build: {
    outDir: 'public/admin',
    chunkSizeWarningLimit: 2000
  }
})
```

## 开发工具

1. VS Code 推荐插件
   - Volar
   - ESLint
   - Prettier
   - TypeScript Vue Plugin
   - SCSS IntelliSense

2. 开发环境配置
   - Node.js >= 16
   - npm 或 yarn
   - Git

## 性能优化

1. 代码分割
   - 路由懒加载
   - 组件异步加载
   - 第三方库按需导入

2. 缓存策略
   - 路由缓存
   - 数据缓存
   - 静态资源缓存

3. 打包优化
   - Tree Shaking
   - 代码压缩
   - 图片优化 
