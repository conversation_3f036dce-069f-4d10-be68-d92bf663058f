<?php

namespace Modules\Common\Provider\Captcha;

use Bingo\Support\Captcha\CaptchaFacade;
use Illuminate\Support\Facades\View;
use Bingo\Core\Input\InputPackage;
use Bingo\Core\Input\Response;

class DefaultCaptchaProvider extends AbstractCaptchaProvider
{
    public function name()
    {
        return 'default';
    }

    public function title()
    {
        return '图片验证码';
    }


    public function render(): string
    {
        return View::make('module::Common.View.widget.captcha.default')->render();
    }

    public function validate(): array
    {
        $input = InputPackage::buildFromInput();
        $captcha = $input->getTrimString('captcha');
        if (! CaptchaFacade::check($captcha)) {
            return Response::generate(-1, '图片验证码错误', null, '[js]$(\'[data-captcha]\').click();');
        }
        return Response::generateSuccess();
    }
}
