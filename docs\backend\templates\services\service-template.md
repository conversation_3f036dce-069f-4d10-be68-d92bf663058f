# 服务模板

## 概述

服务层用于封装复杂的业务逻辑，实现业务规则和数据处理。本文档提供了服务的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\YourModule\Models\YourModel;
use Modules\YourModule\Repositories\YourRepository;
use Modules\YourModule\Events\YourEvent;
use Modules\YourModule\Exceptions\YourException;
use Modules\YourModule\DTOs\YourDTO;
use Modules\YourModule\Exceptions\BizException;
use Modules\YourModule\Enums\YourModuleErrorCode;

class YourService
{
    private YourRepository $repository;

    /**
     * 构造函数
     *
     * @param YourRepository $repository
     */
    public function __construct(YourRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 创建记录
     *
     * @param array $data
     * @return YourModel
     * @throws YourException
     */
    public function create(array $data): YourModel
    {
        try {
            DB::beginTransaction();

            // 数据验证和处理
            $dto = new YourDTO($data);
            
            // 业务逻辑处理
            $model = $this->repository->create($dto->toArray());
            
            // 触发事件
            event(new YourEvent($model));
            
            DB::commit();
            return $model;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create failed', [
                'message' => $e->getMessage(),
                'data' => $data
            ]);
            BizException::throws(YourModuleErrorCode::CREATE_FAILED, '创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新记录
     *
     * @param int $id
     * @param array $data
     * @return YourModel
     * @throws YourException
     */
    public function update(int $id, array $data): YourModel
    {
        try {
            DB::beginTransaction();

            // 检查记录是否存在
            $model = $this->repository->findOrFail($id);
            
            // 数据验证和处理
            $dto = new YourDTO($data);
            
            // 业务逻辑处理
            $model = $this->repository->update($id, $dto->toArray());
            
            // 触发事件
            event(new YourEvent($model));
            
            DB::commit();
            return $model;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update failed', [
                'id' => $id,
                'message' => $e->getMessage(),
                'data' => $data
            ]);
            BizException::throws(YourModuleErrorCode::UPDATE_FAILED, '更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除记录
     *
     * @param int $id
     * @return bool
     * @throws YourException
     */
    public function delete(int $id): bool
    {
        try {
            DB::beginTransaction();

            // 检查记录是否存在
            $model = $this->repository->findOrFail($id);
            
            // 检查是否可以删除
            if (!$this->canDelete($model)) {
                throw new YourException('该记录不能删除');
            }
            
            // 执行删除
            $result = $this->repository->delete($id);
            
            // 触发事件
            event(new YourEvent($model));
            
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Delete failed', [
                'id' => $id,
                'message' => $e->getMessage()
            ]);
            BizException::throws(YourModuleErrorCode::DELETE_FAILED, '删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量创建记录
     *
     * @param array $dataList
     * @return Collection
     * @throws YourException
     */
    public function batchCreate(array $dataList): Collection
    {
        try {
            DB::beginTransaction();

            $models = collect();
            foreach ($dataList as $data) {
                // 数据验证和处理
                $dto = new YourDTO($data);
                
                // 业务逻辑处理
                $model = $this->repository->create($dto->toArray());
                $models->push($model);
                
                // 触发事件
                event(new YourEvent($model));
            }
            
            DB::commit();
            return $models;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Batch create failed', [
                'message' => $e->getMessage(),
                'data' => $dataList
            ]);
            BizException::throws(YourModuleErrorCode::BATCH_CREATE_FAILED, '批量创建失败：' . $e->getMessage());
        }
    }

    /**
     * 批量更新记录
     *
     * @param array $dataList
     * @return Collection
     * @throws YourException
     */
    public function batchUpdate(array $dataList): Collection
    {
        try {
            DB::beginTransaction();

            $models = collect();
            foreach ($dataList as $data) {
                // 检查记录是否存在
                $model = $this->repository->findOrFail($data['id']);
                
                // 数据验证和处理
                $dto = new YourDTO($data);
                
                // 业务逻辑处理
                $model = $this->repository->update($data['id'], $dto->toArray());
                $models->push($model);
                
                // 触发事件
                event(new YourEvent($model));
            }
            
            DB::commit();
            return $models;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Batch update failed', [
                'message' => $e->getMessage(),
                'data' => $dataList
            ]);
            BizException::throws(YourModuleErrorCode::BATCH_UPDATE_FAILED, '批量更新失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除记录
     *
     * @param array $ids
     * @return bool
     * @throws YourException
     */
    public function batchDelete(array $ids): bool
    {
        try {
            DB::beginTransaction();

            foreach ($ids as $id) {
                // 检查记录是否存在
                $model = $this->repository->findOrFail($id);
                
                // 检查是否可以删除
                if (!$this->canDelete($model)) {
                    throw new YourException("ID为{$id}的记录不能删除");
                }
                
                // 执行删除
                $this->repository->delete($id);
                
                // 触发事件
                event(new YourEvent($model));
            }
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Batch delete failed', [
                'message' => $e->getMessage(),
                'ids' => $ids
            ]);
            BizException::throws(YourModuleErrorCode::BATCH_DELETE_FAILED, '批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 检查记录是否可以删除
     *
     * @param YourModel $model
     * @return bool
     */
    private function canDelete(YourModel $model): bool
    {
        // 实现删除检查逻辑
        return true;
    }
}
```

## 规范要求

1. 服务结构
   - 使用依赖注入
   - 遵循单一职责原则
   - 使用事务管理
   - 统一的异常处理

2. 方法定义
   - 清晰的方法名
   - 完整的参数类型
   - 明确的返回类型
   - 详细的注释说明

3. 业务逻辑
   - 数据验证
   - 业务规则检查
   - 事件触发
   - 日志记录

4. 错误处理
   - 使用自定义异常
   - 事务回滚
   - 错误日志
   - 友好的错误消息

## 最佳实践

1. 依赖注入
```php
class YourService
{
    private YourRepository $repository;
    private EventDispatcher $dispatcher;

    public function __construct(
        YourRepository $repository,
        EventDispatcher $dispatcher
    ) {
        $this->repository = $repository;
        $this->dispatcher = $dispatcher;
    }
}
```

2. 事务处理
```php
public function process(array $data)
{
    try {
        DB::beginTransaction();
        // 业务逻辑
        DB::commit();
    } catch (\Exception $e) {
        DB::rollBack();
        throw $e;
    }
}
```

3. 事件处理
```php
public function create(array $data)
{
    $model = $this->repository->create($data);
    event(new ModelCreated($model));
    return $model;
}
```

## 常见问题

1. 事务处理
```php
// 好的实践 - 完整的事务处理
try {
    DB::beginTransaction();
    // 业务逻辑
    DB::commit();
} catch (\Exception $e) {
    DB::rollBack();
    throw $e;
}

// 不好的实践 - 没有事务处理
$model = $this->repository->create($data);
event(new ModelCreated($model));
```

2. 错误处理
```php
// 好的实践 - 统一的错误处理
try {
    // 业务逻辑
} catch (\Exception $e) {
    Log::error('Operation failed', [
        'message' => $e->getMessage(),
        'data' => $data
    ]);
    throw new BusinessException('操作失败：' . $e->getMessage());
}

// 不好的实践 - 直接抛出异常
if (!$model) {
    throw new \Exception('Model not found');
}
```

## 注意事项

1. 保持服务职责单一
2. 合理使用事务
3. 统一错误处理
4. 完善日志记录
5. 注意代码复用
6. 考虑性能优化
