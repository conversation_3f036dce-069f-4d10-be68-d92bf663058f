<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover v-model="filterDialog">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="FilterIcon" />
            </el-icon>
            <span>篩選</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="分類名稱">
            <el-input v-model="search.name" placeholder="請輸入分類名稱" size="large" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="resetSearch">
              <el-icon size="16"><Refresh /></el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="doSearch" type="primary">
              <el-icon size="16"><Filter /></el-icon>
              <span>篩選</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" class="add-btn" @click="openAddDialog">
        <el-icon><Plus /></el-icon>
        新增分類
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table :data="filteredCategories" style="width: 100%">
          <el-table-column prop="name" label="分類名稱" min-width="120">
            <template #default="scope">
              <span class="cat-name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="desc" label="分類描述" min-width="180" />
          <el-table-column prop="parent" label="父級分類" min-width="100">
            <template #default="scope">
              <span>{{ scope.row.parent || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="order" label="顯示順序" width="100" />
          <el-table-column prop="status" label="狀態" width="100">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.status === '啟用'">啟用</el-tag>
              <el-tag type="info" v-else>停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editCategory(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn del-btn" @click="deleteCategory(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog class="el-dialog-common-cls" v-model="addDialogVisible" :title="dialogTitle" width="500">
      <el-form label-position="top" :model="addForm">
        <el-form-item label="分類名稱">
          <el-input v-model="addForm.name" placeholder="請輸入分類名稱" maxlength="20" show-word-limit />
          <div class="input-tip">最多輸入20個字元</div>
        </el-form-item>
        <el-form-item label="分類描述">
          <el-input v-model="addForm.desc" placeholder="請輸入分類描述" maxlength="20" show-word-limit />
          <div class="input-tip">最多輸入20個字元</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd">確定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Plus, Edit, Delete, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '/admin/support/http'

const filterDialog = ref(false)
const search = reactive({ name: '' })
const addDialogVisible = ref(false)
const addForm = reactive({ name: '', desc: '', status: 1 })
const dialogTitle = ref('新增FAQ分類')
const editIndex = ref(-1)

const categories = ref([
  { id: 1, name: '財經類', desc: '財經相關的所有問題', parent: '', order: 1, status: '啟用' },
  { id: 2, name: '軍事類', desc: '軍事相關的所有問題', parent: '', order: 2, status: '啟用' },
  { id: 3, name: '科技類', desc: '科技相關的所有問題', parent: '', order: 3, status: '啟用' }
])

const filteredCategories = computed(() => {
  let arr = categories.value
  if (search.name) arr = arr.filter(cat => cat.name.includes(search.name))
  return arr
})

function openAddDialog() {
  addForm.name = ''
  addForm.desc = ''
  dialogTitle.value = '新增FAQ分類'
  editIndex.value = -1
  addDialogVisible.value = true
}
function submitAdd() {
  if (!addForm.name.trim()) {
    ElMessage.warning('請輸入分類名稱')
    return
  }
  if (editIndex.value === -1) {
    // 新增
    http.post('/admin/faq/category', {
      name: addForm.name,
      desc: addForm.desc,
      status: addForm.status
    }).then(res => {
      if (res.data && res.data.code === 200) {
        ElMessage.success('新增成功')
        getCategoryList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(res.data?.message || '新增失敗')
      }
    }).catch(() => {
    })
  } else {
    // 编辑
    const row = categories.value[editIndex.value]
    http.put(`/admin/faq/category/${row.id}`, {
      name: addForm.name,
      desc: addForm.desc,
      status: addForm.status
    }).then(res => {
      if (res.data && res.data.code === 200) {
        ElMessage.success('編輯成功')
        getCategoryList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(res.data?.message || '編輯失敗')
      }
    }).catch(() => {
    })
  }
}
function editCategory(row: any) {
  addForm.name = row.name
  addForm.desc = row.desc
  dialogTitle.value = '編輯FAQ分類'
  editIndex.value = categories.value.findIndex(cat => cat === row)
  addDialogVisible.value = true
}
function deleteCategory(row: any) {
  ElMessageBox.confirm(
    `確定要刪除分類「${row.name}」嗎？此操作不可恢復！`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    http.delete(`/admin/faq/category/${row.id}`).then(res => {
      if (res.data && res.data.code === 200) {
        ElMessage.success('刪除成功')
        getCategoryList()
      } else {
        ElMessage.error(res.data?.message || '刪除失敗')
      }
    }).catch(() => {
    })
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}
function resetSearch() {
  search.name = ''
}
function doSearch() {
  filterDialog.value = false
}

// 获取分类列表（需对接接口，暂用本地数据）
function getCategoryList() {
  // TODO: 实际开发时用http.get('/admin/faq/category')拉取
  // 这里只是刷新本地数据演示
}

// onMounted(() => {
//   // pageInit()
// })
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}
.search-bar {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  .el-input {
    font-size: 18px;
    border-radius: 8px;
    height: 48px;
    .el-input__wrapper {
      border-radius: 8px;
    }
  }
}
.cat-name {
  font-weight: bold;
  color: #222;
}
.input-tip {
  color: #bfbfbf;
  font-size: 13px;
  margin-top: 2px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
</style>
