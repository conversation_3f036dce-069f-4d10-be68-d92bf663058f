export const getButtonTemplate = (attrs: any = {}) => {
  const {
    text = '按钮',
    type = 'primary',
    size = '',
    style = 'solid',
    href = ''
  } = attrs

  const classes = [
    'btn',
    style === 'outline' ? `btn-outline-${type}` : `btn-${type}`,
    size ? `btn-${size}` : ''
  ].filter(Boolean).join(' ')

  let buttonContent;

  if (href) {
    buttonContent = `
      <a href="${href}" class="${classes}" role="button">
        ${text}
      </a>
    `.trim()
  } else {
    buttonContent = `
      <button type="button" class="${classes}">
        ${text}
      </button>
    `.trim()
  }

  return `
    <div data-bs-component="button" class="button-block responsive-block">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="text-center col-12 col-md-10 col-lg-8">
            ${buttonContent}
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

export default getButtonTemplate 