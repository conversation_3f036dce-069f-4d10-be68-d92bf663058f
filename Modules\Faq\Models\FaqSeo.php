<?php

namespace Modules\Faq\Models;

use Modules\Common\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 * 常见问题SEO关联模型
 * 对应数据库表: tvb_faq_seo
 */
class FaqSeo extends BaseModel
{
    use SoftDeletes;
    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'faq_seo';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'faq_id',                    // 常见问题ID
        'title',                     // SEO 标题
        'description',               // SEO 描述
        'keywords',                  // SEO 关键词
        'regular_url',               // 正则URL
        'allow_index',               // 允许索引
        'allow_follow',              // 允许跟随
        'open_graph_title',          // Open Graph 标题
        'open_graph_description',    // Open Graph 描述
        'is_deleted',                // 是否删除: 0-未删除, 1-已删除
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $casts = [
        'allow_index' => 'boolean',
        'allow_follow' => 'boolean',
        'is_deleted' => 'integer',
        'faq_id' => 'integer',
    ];

    /**
     * 关联到常见问题
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function faq()
    {
        return $this->belongsTo(Faq::class, 'faq_id', 'id');
    }

    /**
     * 获取SEO标题
     * @return string|null
     */
    public function getSeoTitleAttribute()
    {
        return $this->title;
    }

    /**
     * 获取SEO描述
     * @return string|null
     */
    public function getSeoDescriptionAttribute()
    {
        return $this->description;
    }

    /**
     * 获取SEO关键词
     * @return string|null
     */
    public function getSeoKeywordsAttribute()
    {
        return $this->keywords;
    }

    /**
     * 检查是否允许索引
     * @return bool
     */
    public function isIndexAllowed()
    {
        return $this->allow_index;
    }

    /**
     * 检查是否允许跟随
     * @return bool
     */
    public function isFollowAllowed()
    {
        return $this->allow_follow;
    }

    /**
     * 获取Open Graph标题
     * @return string|null
     */
    public function getOpenGraphTitleAttribute()
    {
        return $this->open_graph_title;
    }

    /**
     * 获取Open Graph描述
     * @return string|null
     */
    public function getOpenGraphDescriptionAttribute()
    {
        return $this->open_graph_description;
    }

    /**
     * 查询未删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('is_deleted', 0);
    }

    /**
     * 查询已删除的记录作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDeleted($query)
    {
        return $query->where('is_deleted', 1);
    }
} 