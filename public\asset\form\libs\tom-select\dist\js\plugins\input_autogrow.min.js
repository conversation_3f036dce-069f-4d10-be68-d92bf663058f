(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.input_autogrow=factory())})(this,function(){"use strict";const addEvent=(target,type,callback,options)=>{target.addEventListener(type,callback,options)};function plugin(){var self=this;self.on("initialize",()=>{var test_input=document.createElement("span");var control=self.control_input;test_input.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ";self.wrapper.appendChild(test_input);var transfer_styles=["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"];for(const style_name of transfer_styles){test_input.style[style_name]=control.style[style_name]}var resize=()=>{test_input.textContent=control.value;control.style.width=test_input.clientWidth+"px"};resize();self.on("update item_add item_remove",resize);addEvent(control,"input",resize);addEvent(control,"keyup",resize);addEvent(control,"blur",resize);addEvent(control,"update",resize)})}return plugin});