!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)}(function(V,h){function S(){return new Date(Date.UTC.apply(Date,arguments))}"indexOf"in Array.prototype||(Array.prototype.indexOf=function(t,e){e===h&&(e=0),e<0&&(e+=this.length),e<0&&(e=0);for(var i=this.length;e<i;e++)if(e in this&&this[e]===t)return e;return-1});function w(t,n){var e=this;this.options=n,this.element=V(t),this.container=n.container||"body",this.language=n.language||this.element.data("date-language")||"en",this.language=this.language in x?this.language:this.language.split("-")[0],this.language=this.language in x?this.language:"en",this.isRTL=x[this.language].rtl||!1,this.formatType=n.formatType||this.element.data("format-type")||"standard",this.format=H.parseFormat(n.format||this.element.data("date-format")||x[this.language].format||H.getDefaultFormat(this.formatType,"input"),this.formatType),this.isInline=!1,this.isVisible=!1,this.isInput=this.element.is("input"),this.fontAwesome=n.fontAwesome||this.element.data("font-awesome")||!1,this.bootcssVer=n.bootcssVer||(this.isInput?this.element.is(".form-control")?3:2:this.bootcssVer=this.element.is(".input-group")?3:2),this.component=!!this.element.is(".date")&&(3===this.bootcssVer?this.element.find(".kv-datetime-picker"):this.element.find(".add-on .icon-th, .add-on .icon-time, .add-on .icon-calendar, .add-on .fa-calendar, .add-on .fa-clock-o").parent()),this.componentReset=!!this.element.is(".date")&&(3===this.bootcssVer?this.element.find(".kv-datetime-remove"):this.element.find(".add-on .icon-remove, .add-on .fa-times").parent()),this.hasInput=this.component&&this.element.find("input").length,this.component&&0===this.component.length&&(this.component=!1),this.linkField=n.linkField||this.element.data("link-field")||!1,this.linkFormat=H.parseFormat(n.linkFormat||this.element.data("link-format")||H.getDefaultFormat(this.formatType,"link"),this.formatType),this.minuteStep=n.minuteStep||this.element.data("minute-step")||5,this.pickerPosition=n.pickerPosition||this.element.data("picker-position")||"bottom-right",this.showMeridian=n.showMeridian||this.element.data("show-meridian")||!1,this.initialDate=n.initialDate||new Date,this.zIndex=n.zIndex||this.element.data("z-index")||h,this.title=void 0!==n.title&&n.title,this.timezone=n.timezone||function(){var t,e,i,s,a;if((i=(null!=(a=(e=(new Date).toString()).split("(")[1])?a.slice(0,-1):0)||e.split(" "))instanceof Array){s=[];for(var n=0,h=i.length;n<h;n++)(t=null!==(a=i[n].match(/\b[A-Z]+\b/)))&&a[0]&&s.push(t);i=s.pop()}return i}(),this.icons=V.extend({leftArrow:this.fontAwesome?"fa-arrow-left":3===this.bootcssVer?"glyphicon-arrow-left":"icon-arrow-left",rightArrow:this.fontAwesome?"fa-arrow-right":3===this.bootcssVer?"glyphicon-arrow-right":"icon-arrow-right"},n.icons||{}),this.icontype=n.icontype||(this.fontAwesome?"fa":"glyphicon"),this._attachEvents(),this.clickedOutside=function(t){0===V(t.target).closest(".datetimepicker").length&&e.hide()},this.formatViewType="datetime","formatViewType"in n?this.formatViewType=n.formatViewType:"formatViewType"in this.element.data()&&(this.formatViewType=this.element.data("formatViewType")),this.minView=0,"minView"in n?this.minView=n.minView:"minView"in this.element.data()&&(this.minView=this.element.data("min-view")),this.minView=H.convertViewMode(this.minView),this.maxView=H.modes.length-1,"maxView"in n?this.maxView=n.maxView:"maxView"in this.element.data()&&(this.maxView=this.element.data("max-view")),this.maxView=H.convertViewMode(this.maxView),this.wheelViewModeNavigation=!1,"wheelViewModeNavigation"in n?this.wheelViewModeNavigation=n.wheelViewModeNavigation:"wheelViewModeNavigation"in this.element.data()&&(this.wheelViewModeNavigation=this.element.data("view-mode-wheel-navigation")),this.wheelViewModeNavigationInverseDirection=!1,"wheelViewModeNavigationInverseDirection"in n?this.wheelViewModeNavigationInverseDirection=n.wheelViewModeNavigationInverseDirection:"wheelViewModeNavigationInverseDirection"in this.element.data()&&(this.wheelViewModeNavigationInverseDirection=this.element.data("view-mode-wheel-navigation-inverse-dir")),this.wheelViewModeNavigationDelay=100,"wheelViewModeNavigationDelay"in n?this.wheelViewModeNavigationDelay=n.wheelViewModeNavigationDelay:"wheelViewModeNavigationDelay"in this.element.data()&&(this.wheelViewModeNavigationDelay=this.element.data("view-mode-wheel-navigation-delay")),this.startViewMode=2,"startView"in n?this.startViewMode=n.startView:"startView"in this.element.data()&&(this.startViewMode=this.element.data("start-view")),this.startViewMode=H.convertViewMode(this.startViewMode),this.viewMode=this.startViewMode,this.viewSelect=this.minView,"viewSelect"in n?this.viewSelect=n.viewSelect:"viewSelect"in this.element.data()&&(this.viewSelect=this.element.data("view-select")),this.viewSelect=H.convertViewMode(this.viewSelect),this.forceParse=!0,"forceParse"in n?this.forceParse=n.forceParse:"dateForceParse"in this.element.data()&&(this.forceParse=this.element.data("date-force-parse"));for(var i=3===this.bootcssVer?H.templateV3:H.template;-1!==i.indexOf("{iconType}");)i=i.replace("{iconType}",this.icontype);for(;-1!==i.indexOf("{leftArrow}");)i=i.replace("{leftArrow}",this.icons.leftArrow);for(;-1!==i.indexOf("{rightArrow}");)i=i.replace("{rightArrow}",this.icons.rightArrow);if(this.picker=V(i).appendTo(this.isInline?this.element:this.container).on({click:V.proxy(this.click,this),mousedown:V.proxy(this.mousedown,this)}),this.wheelViewModeNavigation&&(V.fn.mousewheel?this.picker.on({mousewheel:V.proxy(this.mousewheel,this)}):console.log("Mouse Wheel event is not supported. Please include the jQuery Mouse Wheel plugin before enabling this option")),this.isInline?this.picker.addClass("datetimepicker-inline"):this.picker.addClass("datetimepicker-dropdown-"+this.pickerPosition+" dropdown-menu"),this.isRTL){this.picker.addClass("datetimepicker-rtl");var s=3===this.bootcssVer?".prev span, .next span":".prev i, .next i";this.picker.find(s).toggleClass(this.icons.leftArrow+" "+this.icons.rightArrow)}V(document).on("mousedown touchend",this.clickedOutside),this.autoclose=!1,"autoclose"in n?this.autoclose=n.autoclose:"dateAutoclose"in this.element.data()&&(this.autoclose=this.element.data("date-autoclose")),this.keyboardNavigation=!0,"keyboardNavigation"in n?this.keyboardNavigation=n.keyboardNavigation:"dateKeyboardNavigation"in this.element.data()&&(this.keyboardNavigation=this.element.data("date-keyboard-navigation")),this.todayBtn=n.todayBtn||this.element.data("date-today-btn")||!1,this.clearBtn=n.clearBtn||this.element.data("date-clear-btn")||!1,this.todayHighlight=n.todayHighlight||this.element.data("date-today-highlight")||!1,void(this.weekStart=0)!==n.weekStart?this.weekStart=n.weekStart:void 0!==this.element.data("date-weekstart")?this.weekStart=this.element.data("date-weekstart"):void 0!==x[this.language].weekStart&&(this.weekStart=x[this.language].weekStart),this.weekStart=this.weekStart%7,this.weekEnd=(this.weekStart+6)%7,this.onRenderDay=function(t){var e=(n.onRenderDay||function(){return[]})(t);"string"==typeof e&&(e=[e]);return["day"].concat(e||[])},this.onRenderHour=function(t){var e=(n.onRenderHour||function(){return[]})(t);return"string"==typeof e&&(e=[e]),["hour"].concat(e||[])},this.onRenderMinute=function(t){var e=(n.onRenderMinute||function(){return[]})(t),i=["minute"];return"string"==typeof e&&(e=[e]),t<this.startDate||t>this.endDate?i.push("disabled"):Math.floor(this.date.getUTCMinutes()/this.minuteStep)===Math.floor(t.getUTCMinutes()/this.minuteStep)&&i.push("active"),i.concat(e||[])},this.onRenderYear=function(t){var e=(n.onRenderYear||function(){return[]})(t),i=["year"];"string"==typeof e&&(e=[e]),this.date.getUTCFullYear()===t.getUTCFullYear()&&i.push("active");var s=t.getUTCFullYear(),a=this.endDate.getUTCFullYear();return(t<this.startDate||a<s)&&i.push("disabled"),i.concat(e||[])},this.onRenderMonth=function(t){var e=(n.onRenderMonth||function(){return[]})(t);return"string"==typeof e&&(e=[e]),["month"].concat(e||[])},this.startDate=new Date(-8639968443048e3),this.endDate=new Date(8639968443048e3),this.datesDisabled=[],this.daysOfWeekDisabled=[],this.setStartDate(n.startDate||this.element.data("date-startdate")),this.setEndDate(n.endDate||this.element.data("date-enddate")),this.setDatesDisabled(n.datesDisabled||this.element.data("date-dates-disabled")),this.setDaysOfWeekDisabled(n.daysOfWeekDisabled||this.element.data("date-days-of-week-disabled")),this.setMinutesDisabled(n.minutesDisabled||this.element.data("date-minute-disabled")),this.setHoursDisabled(n.hoursDisabled||this.element.data("date-hour-disabled")),this.fillDow(),this.fillMonths(),this.update(),this.showMode(),this.isInline&&this.show()}w.prototype={constructor:w,_events:[],_attachEvents:function(){this._detachEvents(),this.isInput?this._events=[[this.element,{focus:V.proxy(this.show,this),keyup:V.proxy(this.update,this),keydown:V.proxy(this.keydown,this)}]]:this.component&&this.hasInput?(this._events=[[this.element.find("input"),{focus:V.proxy(this.show,this),keyup:V.proxy(this.update,this),keydown:V.proxy(this.keydown,this)}],[this.component,{click:V.proxy(this.show,this)}]],this.componentReset&&this._events.push([this.componentReset,{click:V.proxy(this.reset,this)}])):this.element.is("div")?this.isInline=!0:this._events=[[this.element,{click:V.proxy(this.show,this)}]];for(var t,e,i=0;i<this._events.length;i++)t=this._events[i][0],e=this._events[i][1],t.on(e)},_detachEvents:function(){for(var t,e,i=0;i<this._events.length;i++)t=this._events[i][0],e=this._events[i][1],t.off(e);this._events=[]},show:function(t){this.picker.show(),this.height=this.component?this.component.outerHeight():this.element.outerHeight(),this.forceParse&&this.update(),this.place(),V(window).on("resize",V.proxy(this.place,this)),t&&(t.stopPropagation(),t.preventDefault()),this.isVisible=!0,this.element.trigger({type:"show",date:this.date})},hide:function(){this.isVisible&&(this.isInline||(this.picker.hide(),V(window).off("resize",this.place),this.viewMode=this.startViewMode,this.showMode(),this.isInput||V(document).off("mousedown",this.hide),this.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())&&this.setValue(),this.isVisible=!1,this.element.trigger({type:"hide",date:this.date})))},remove:function(){this._detachEvents(),V(document).off("mousedown",this.clickedOutside),this.picker.remove(),delete this.picker,delete this.element.data().datetimepicker},getDate:function(){var t=this.getUTCDate();return null===t?null:new Date(t.getTime()+6e4*t.getTimezoneOffset())},getUTCDate:function(){return this.date},getInitialDate:function(){return this.initialDate},setInitialDate:function(t){this.initialDate=t},setDate:function(t){this.setUTCDate(new Date(t.getTime()-6e4*t.getTimezoneOffset()))},setUTCDate:function(t){t>=this.startDate&&t<=this.endDate?(this.date=t,this.setValue(),this.viewDate=this.date,this.fill()):this.element.trigger({type:"outOfRange",date:t,startDate:this.startDate,endDate:this.endDate})},setFormat:function(t){var e;this.format=H.parseFormat(t,this.formatType),this.isInput?e=this.element:this.component&&(e=this.element.find("input")),e&&e.val()&&this.setValue()},setValue:function(){var t=this.getFormattedDate();this.isInput?this.element.val(t):(this.component&&this.element.find("input").val(t),this.element.data("date",t)),this.linkField&&V("#"+this.linkField).val(this.getFormattedDate(this.linkFormat))},getFormattedDate:function(t){return t=t||this.format,H.formatDate(this.date,t,this.language,this.formatType,this.timezone)},setStartDate:function(t){this.startDate=t||this.startDate,8639968443048e3!==this.startDate.valueOf()&&(this.startDate=H.parseDate(this.startDate,this.format,this.language,this.formatType,this.timezone)),this.update(),this.updateNavArrows()},setEndDate:function(t){this.endDate=t||this.endDate,8639968443048e3!==this.endDate.valueOf()&&(this.endDate=H.parseDate(this.endDate,this.format,this.language,this.formatType,this.timezone)),this.update(),this.updateNavArrows()},setDatesDisabled:function(t){this.datesDisabled=t||[],V.isArray(this.datesDisabled)||(this.datesDisabled=this.datesDisabled.split(/,\s*/));var e=this;this.datesDisabled=V.map(this.datesDisabled,function(t){return H.parseDate(t,e.format,e.language,e.formatType,e.timezone).toDateString()}),this.update(),this.updateNavArrows()},setTitle:function(t,e){return this.picker.find(t).find("th:eq(1)").text(!1===this.title?e:this.title)},setDaysOfWeekDisabled:function(t){this.daysOfWeekDisabled=t||[],V.isArray(this.daysOfWeekDisabled)||(this.daysOfWeekDisabled=this.daysOfWeekDisabled.split(/,\s*/)),this.daysOfWeekDisabled=V.map(this.daysOfWeekDisabled,function(t){return parseInt(t,10)}),this.update(),this.updateNavArrows()},setMinutesDisabled:function(t){this.minutesDisabled=t||[],V.isArray(this.minutesDisabled)||(this.minutesDisabled=this.minutesDisabled.split(/,\s*/)),this.minutesDisabled=V.map(this.minutesDisabled,function(t){return parseInt(t,10)}),this.update(),this.updateNavArrows()},setHoursDisabled:function(t){this.hoursDisabled=t||[],V.isArray(this.hoursDisabled)||(this.hoursDisabled=this.hoursDisabled.split(/,\s*/)),this.hoursDisabled=V.map(this.hoursDisabled,function(t){return parseInt(t,10)}),this.update(),this.updateNavArrows()},place:function(){if(!this.isInline){if(!this.zIndex){var e=0;V("div").each(function(){var t=parseInt(V(this).css("zIndex"),10);e<t&&(e=t)}),this.zIndex=e+10}var t,i,s,a;a=this.container instanceof V?this.container.offset():V(this.container).offset(),this.component?(s=(t=this.component.offset()).left,"bottom-left"!==this.pickerPosition&&"top-left"!==this.pickerPosition||(s+=this.component.outerWidth()-this.picker.outerWidth())):(s=(t=this.element.offset()).left,"bottom-left"!==this.pickerPosition&&"top-left"!==this.pickerPosition||(s+=this.element.outerWidth()-this.picker.outerWidth()));var n=document.body.clientWidth||window.innerWidth;n<s+220&&(s=n-220),i="top-left"===this.pickerPosition||"top-right"===this.pickerPosition?t.top-this.picker.outerHeight():t.top+this.height,i-=a.top,s-=a.left,this.picker.css({top:i,left:s,zIndex:this.zIndex})}},hour_minute:"^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]",update:function(){var t,e=!1;arguments&&arguments.length&&("string"==typeof arguments[0]||arguments[0]instanceof Date)?(t=arguments[0],e=!0):"string"==typeof(t=(this.isInput?this.element.val():this.element.find("input").val())||this.element.data("date")||this.initialDate)&&(t=t.replace(/^\s+|\s+$/g,"")),t||(t=new Date,e=!1),"string"==typeof t&&(new RegExp(this.hour_minute).test(t)||new RegExp(this.hour_minute+":[0-5][0-9]").test(t))&&(t=this.getDate()),this.date=H.parseDate(t,this.format,this.language,this.formatType,this.timezone),e&&this.setValue(),this.date<this.startDate?this.viewDate=new Date(this.startDate):this.date>this.endDate?this.viewDate=new Date(this.endDate):this.viewDate=new Date(this.date),this.fill()},fillDow:function(){for(var t=this.weekStart,e="<tr>";t<this.weekStart+7;)e+='<th class="dow">'+x[this.language].daysMin[t++%7]+"</th>";e+="</tr>",this.picker.find(".datetimepicker-days thead").append(e)},fillMonths:function(){for(var t="",e=new Date(this.viewDate),i=0;i<12;i++){e.setUTCMonth(i),t+='<span class="'+this.onRenderMonth(e).join(" ")+'">'+x[this.language].monthsShort[i]+"</span>"}this.picker.find(".datetimepicker-months td").html(t)},fill:function(){if(this.date&&this.viewDate){var t,e=new Date(this.viewDate),i=e.getUTCFullYear(),s=e.getUTCMonth(),a=e.getUTCDate(),n=e.getUTCHours(),h=this.startDate.getUTCFullYear(),r=this.startDate.getUTCMonth(),o=this.endDate.getUTCFullYear(),d=this.endDate.getUTCMonth()+1,l=new S(this.date.getUTCFullYear(),this.date.getUTCMonth(),this.date.getUTCDate()).valueOf(),u=new Date;if(this.setTitle(".datetimepicker-days",x[this.language].months[s]+" "+i),"time"===this.formatViewType){var c=this.getFormattedDate();this.setTitle(".datetimepicker-hours",c),this.setTitle(".datetimepicker-minutes",c)}else this.setTitle(".datetimepicker-hours",a+" "+x[this.language].months[s]+" "+i),this.setTitle(".datetimepicker-minutes",a+" "+x[this.language].months[s]+" "+i);this.picker.find("tfoot th.today").text(x[this.language].today||x.en.today).toggle(!1!==this.todayBtn),this.picker.find("tfoot th.clear").text(x[this.language].clear||x.en.clear).toggle(!1!==this.clearBtn),this.updateNavArrows(),this.fillMonths();var p=S(i,s-1,28,0,0,0,0),m=H.getDaysInMonth(p.getUTCFullYear(),p.getUTCMonth());p.setUTCDate(m),p.setUTCDate(m-(p.getUTCDay()-this.weekStart+7)%7);var f=new Date(p);f.setUTCDate(f.getUTCDate()+42),f=f.valueOf();for(var v,g=[];p.valueOf()<f;)p.getUTCDay()===this.weekStart&&g.push("<tr>"),v=this.onRenderDay(p),p.getUTCFullYear()<i||p.getUTCFullYear()===i&&p.getUTCMonth()<s?v.push("old"):(p.getUTCFullYear()>i||p.getUTCFullYear()===i&&p.getUTCMonth()>s)&&v.push("new"),this.todayHighlight&&p.getUTCFullYear()===u.getFullYear()&&p.getUTCMonth()===u.getMonth()&&p.getUTCDate()===u.getDate()&&v.push("today"),p.valueOf()===l&&v.push("active"),(p.valueOf()+864e5<=this.startDate||p.valueOf()>this.endDate||-1!==V.inArray(p.getUTCDay(),this.daysOfWeekDisabled)||-1!==V.inArray(p.toDateString(),this.datesDisabled))&&v.push("disabled"),g.push('<td class="'+v.join(" ")+'">'+p.getUTCDate()+"</td>"),p.getUTCDay()===this.weekEnd&&g.push("</tr>"),p.setUTCDate(p.getUTCDate()+1);this.picker.find(".datetimepicker-days tbody").empty().append(g.join("")),g=[];var w="",D="",y="",T=this.hoursDisabled||[];for(e=new Date(this.viewDate),t=0;t<24;t++){e.setUTCHours(t),v=this.onRenderHour(e),-1!==T.indexOf(t)&&v.push("disabled");var C=S(i,s,a,t);C.valueOf()+36e5<=this.startDate||C.valueOf()>this.endDate?v.push("disabled"):n===t&&v.push("active"),this.showMeridian&&2===x[this.language].meridiem.length?((D=t<12?x[this.language].meridiem[0]:x[this.language].meridiem[1])!==y&&(""!==y&&g.push("</fieldset>"),g.push('<fieldset class="hour"><legend>'+D.toUpperCase()+"</legend>")),y=D,w=t%12?t%12:12,t<12?v.push("hour_am"):v.push("hour_pm"),g.push('<span class="'+v.join(" ")+'">'+w+"</span>"),23===t&&g.push("</fieldset>")):(w=t+":00",g.push('<span class="'+v.join(" ")+'">'+w+"</span>"))}this.picker.find(".datetimepicker-hours td").html(g.join("")),g=[],y=D=w="";var k=this.minutesDisabled||[];for(e=new Date(this.viewDate),t=0;t<60;t+=this.minuteStep)-1===k.indexOf(t)&&(e.setUTCMinutes(t),e.setUTCSeconds(0),v=this.onRenderMinute(e),this.showMeridian&&2===x[this.language].meridiem.length?((D=n<12?x[this.language].meridiem[0]:x[this.language].meridiem[1])!==y&&(""!==y&&g.push("</fieldset>"),g.push('<fieldset class="minute"><legend>'+D.toUpperCase()+"</legend>")),y=D,w=n%12?n%12:12,g.push('<span class="'+v.join(" ")+'">'+w+":"+(t<10?"0"+t:t)+"</span>"),59===t&&g.push("</fieldset>")):(w=t+":00",g.push('<span class="'+v.join(" ")+'">'+n+":"+(t<10?"0"+t:t)+"</span>")));this.picker.find(".datetimepicker-minutes td").html(g.join(""));var M=this.date.getUTCFullYear(),b=this.setTitle(".datetimepicker-months",i).end().find(".month").removeClass("active");M===i&&b.eq(this.date.getUTCMonth()).addClass("active"),(i<h||o<i)&&b.addClass("disabled"),i===h&&b.slice(0,r).addClass("disabled"),i===o&&b.slice(d).addClass("disabled"),g="",i=10*parseInt(i/10,10);var U=this.setTitle(".datetimepicker-years",i+"-"+(i+9)).end().find("td");for(i-=1,e=new Date(this.viewDate),t=-1;t<11;t++)e.setUTCFullYear(i),v=this.onRenderYear(e),-1!==t&&10!==t||v.push(F),g+='<span class="'+v.join(" ")+'">'+i+"</span>",i+=1;U.html(g),this.place()}},updateNavArrows:function(){var t=new Date(this.viewDate),e=t.getUTCFullYear(),i=t.getUTCMonth(),s=t.getUTCDate(),a=t.getUTCHours();switch(this.viewMode){case 0:e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()&&s<=this.startDate.getUTCDate()&&a<=this.startDate.getUTCHours()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()&&s>=this.endDate.getUTCDate()&&a>=this.endDate.getUTCHours()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 1:e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()&&s<=this.startDate.getUTCDate()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()&&s>=this.endDate.getUTCDate()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 2:e<=this.startDate.getUTCFullYear()&&i<=this.startDate.getUTCMonth()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),e>=this.endDate.getUTCFullYear()&&i>=this.endDate.getUTCMonth()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 3:case 4:e<=this.startDate.getUTCFullYear()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),e>=this.endDate.getUTCFullYear()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"})}},mousewheel:function(t){if(t.preventDefault(),t.stopPropagation(),!this.wheelPause){this.wheelPause=!0;var e=t.originalEvent.wheelDelta,i=0<e?1:0===e?0:-1;this.wheelViewModeNavigationInverseDirection&&(i=-i),this.showMode(i),setTimeout(V.proxy(function(){this.wheelPause=!1},this),this.wheelViewModeNavigationDelay)}},click:function(t){t.stopPropagation(),t.preventDefault();var e=V(t.target).closest("span, td, th, legend");if(e.is("."+this.icontype)&&(e=V(e).parent().closest("span, td, th, legend")),1===e.length){if(e.is(".disabled"))return void this.element.trigger({type:"outOfRange",date:this.viewDate,startDate:this.startDate,endDate:this.endDate});switch(e[0].nodeName.toLowerCase()){case"th":switch(e[0].className){case"switch":this.showMode(1);break;case"prev":case"next":var i=H.modes[this.viewMode].navStep*("prev"===e[0].className?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveHour(this.viewDate,i);break;case 1:this.viewDate=this.moveDate(this.viewDate,i);break;case 2:this.viewDate=this.moveMonth(this.viewDate,i);break;case 3:case 4:this.viewDate=this.moveYear(this.viewDate,i)}this.fill(),this.element.trigger({type:e[0].className+":"+this.convertViewModeText(this.viewMode),date:this.viewDate,startDate:this.startDate,endDate:this.endDate});break;case"clear":this.reset(),this.autoclose&&this.hide();break;case"today":var s=new Date;(s=S(s.getFullYear(),s.getMonth(),s.getDate(),s.getHours(),s.getMinutes(),s.getSeconds(),0))<this.startDate?s=this.startDate:s>this.endDate&&(s=this.endDate),this.viewMode=this.startViewMode,this.showMode(0),this._setDate(s),this.fill(),this.autoclose&&this.hide()}break;case"span":if(!e.is(".disabled")){var a=this.viewDate.getUTCFullYear(),n=this.viewDate.getUTCMonth(),h=this.viewDate.getUTCDate(),r=this.viewDate.getUTCHours(),o=this.viewDate.getUTCMinutes(),d=this.viewDate.getUTCSeconds();if(e.is(".month")?(this.viewDate.setUTCDate(1),n=e.parent().find("span").index(e),h=this.viewDate.getUTCDate(),this.viewDate.setUTCMonth(n),this.element.trigger({type:"changeMonth",date:this.viewDate}),3<=this.viewSelect&&this._setDate(S(a,n,h,r,o,d,0))):e.is(".year")?(this.viewDate.setUTCDate(1),a=parseInt(e.text(),10)||0,this.viewDate.setUTCFullYear(a),this.element.trigger({type:"changeYear",date:this.viewDate}),4<=this.viewSelect&&this._setDate(S(a,n,h,r,o,d,0))):e.is(".hour")?(r=parseInt(e.text(),10)||0,(e.hasClass("hour_am")||e.hasClass("hour_pm"))&&(12===r&&e.hasClass("hour_am")?r=0:12!==r&&e.hasClass("hour_pm")&&(r+=12)),this.viewDate.setUTCHours(r),this.element.trigger({type:"changeHour",date:this.viewDate}),1<=this.viewSelect&&this._setDate(S(a,n,h,r,o,d,0))):e.is(".minute")&&(o=parseInt(e.text().substr(e.text().indexOf(":")+1),10)||0,this.viewDate.setUTCMinutes(o),this.element.trigger({type:"changeMinute",date:this.viewDate}),0<=this.viewSelect&&this._setDate(S(a,n,h,r,o,d,0))),0!==this.viewMode){var l=this.viewMode;this.showMode(-1),this.fill(),l===this.viewMode&&this.autoclose&&this.hide()}else this.fill(),this.autoclose&&this.hide()}break;case"td":if(e.is(".day")&&!e.is(".disabled")){h=parseInt(e.text(),10)||1,a=this.viewDate.getUTCFullYear(),n=this.viewDate.getUTCMonth(),r=this.viewDate.getUTCHours(),o=this.viewDate.getUTCMinutes(),d=this.viewDate.getUTCSeconds();e.is(".old")?0===n?(n=11,a-=1):n-=1:e.is(".new")&&(11===n?(n=0,a+=1):n+=1),this.viewDate.setUTCFullYear(a),this.viewDate.setUTCMonth(n,h),this.element.trigger({type:"changeDay",date:this.viewDate}),2<=this.viewSelect&&this._setDate(S(a,n,h,r,o,d,0))}l=this.viewMode;this.showMode(-1),this.fill(),l===this.viewMode&&this.autoclose&&this.hide()}}},_setDate:function(t,e){var i;e&&"date"!==e||(this.date=t),e&&"view"!==e||(this.viewDate=t),this.fill(),this.setValue(),this.isInput?i=this.element:this.component&&(i=this.element.find("input")),i&&i.change(),this.element.trigger({type:"changeDate",date:this.getDate()}),null===t&&(this.date=this.viewDate)},moveMinute:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCMinutes(i.getUTCMinutes()+e*this.minuteStep),i},moveHour:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCHours(i.getUTCHours()+e),i},moveDate:function(t,e){if(!e)return t;var i=new Date(t.valueOf());return i.setUTCDate(i.getUTCDate()+e),i},moveMonth:function(t,e){if(!e)return t;var i,s,a=new Date(t.valueOf()),n=a.getUTCDate(),h=a.getUTCMonth(),r=Math.abs(e);if(e=0<e?1:-1,1===r)s=-1===e?function(){return a.getUTCMonth()===h}:function(){return a.getUTCMonth()!==i},i=h+e,a.setUTCMonth(i),(i<0||11<i)&&(i=(i+12)%12);else{for(var o=0;o<r;o++)a=this.moveMonth(a,e);i=a.getUTCMonth(),a.setUTCDate(n),s=function(){return i!==a.getUTCMonth()}}for(;s();)a.setUTCDate(--n),a.setUTCMonth(i);return a},moveYear:function(t,e){return this.moveMonth(t,12*e)},dateWithinRange:function(t){return t>=this.startDate&&t<=this.endDate},keydown:function(t){if(this.picker.is(":not(:visible)"))27===t.keyCode&&this.show();else{var e,i,s,a,n=!1;switch(t.keyCode){case 27:this.hide(),t.preventDefault();break;case 37:case 39:if(!this.keyboardNavigation)break;e=37===t.keyCode?-1:1;var h=this.viewMode;t.ctrlKey?h+=2:t.shiftKey&&(h+=1),4===h?(i=this.moveYear(this.date,e),s=this.moveYear(this.viewDate,e)):3===h?(i=this.moveMonth(this.date,e),s=this.moveMonth(this.viewDate,e)):2===h?(i=this.moveDate(this.date,e),s=this.moveDate(this.viewDate,e)):1===h?(i=this.moveHour(this.date,e),s=this.moveHour(this.viewDate,e)):0===h&&(i=this.moveMinute(this.date,e),s=this.moveMinute(this.viewDate,e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=s,this.setValue(),this.update(),t.preventDefault(),n=!0);break;case 38:case 40:if(!this.keyboardNavigation)break;e=38===t.keyCode?-1:1,h=this.viewMode,t.ctrlKey?h+=2:t.shiftKey&&(h+=1),4===h?(i=this.moveYear(this.date,e),s=this.moveYear(this.viewDate,e)):3===h?(i=this.moveMonth(this.date,e),s=this.moveMonth(this.viewDate,e)):2===h?(i=this.moveDate(this.date,7*e),s=this.moveDate(this.viewDate,7*e)):1===h?s=this.showMeridian?(i=this.moveHour(this.date,6*e),this.moveHour(this.viewDate,6*e)):(i=this.moveHour(this.date,4*e),this.moveHour(this.viewDate,4*e)):0===h&&(i=this.moveMinute(this.date,4*e),s=this.moveMinute(this.viewDate,4*e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=s,this.setValue(),this.update(),t.preventDefault(),n=!0);break;case 13:if(0!==this.viewMode){var r=this.viewMode;this.showMode(-1),this.fill(),r===this.viewMode&&this.autoclose&&this.hide()}else this.fill(),this.autoclose&&this.hide();t.preventDefault();break;case 9:this.hide()}if(n)this.isInput?a=this.element:this.component&&(a=this.element.find("input")),a&&a.change(),this.element.trigger({type:"changeDate",date:this.getDate()})}},showMode:function(t){if(t){var e=Math.max(0,Math.min(H.modes.length-1,this.viewMode+t));e>=this.minView&&e<=this.maxView&&(this.element.trigger({type:"changeMode",date:this.viewDate,oldViewMode:this.viewMode,newViewMode:e}),this.viewMode=e)}this.picker.find(">div").hide().filter(".datetimepicker-"+H.modes[this.viewMode].clsName).css("display","block"),this.updateNavArrows()},reset:function(){this._setDate(null,"date")},convertViewModeText:function(t){switch(t){case 4:return"decade";case 3:return"year";case 2:return"month";case 1:return"day";case 0:return"hour"}}};var F=V.fn.datetimepicker;V.fn.datetimepicker=function(s){var a,n=Array.apply(null,arguments);return n.shift(),this.each(function(){var t=V(this),e=t.data("datetimepicker"),i="object"==typeof s&&s;if(e||t.data("datetimepicker",e=new w(this,V.extend({},V.fn.datetimepicker.defaults,i))),"string"==typeof s&&"function"==typeof e[s]&&(a=e[s].apply(e,n))!==h)return!1}),a!==h?a:this},V.fn.datetimepicker.defaults={},V.fn.datetimepicker.Constructor=w;var x=V.fn.datetimepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["am","pm"],suffix:["st","nd","rd","th"],today:"Today",clear:"Clear"}},H={modes:[{clsName:"minutes",navFnc:"Hours",navStep:1},{clsName:"hours",navFnc:"Date",navStep:1},{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(t){return t%4==0&&t%100!=0||t%400==0},getDaysInMonth:function(t,e){return[31,H.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},getDefaultFormat:function(t,e){if("standard"===t)return"input"===e?"yyyy-mm-dd hh:ii":"yyyy-mm-dd hh:ii:ss";if("php"===t)return"input"===e?"Y-m-d H:i":"Y-m-d H:i:s";throw new Error("Invalid format type.")},validParts:function(t){if("standard"===t)return/t|hh?|HH?|p|P|z|Z|ii?|ss?|dd?|DD?|mm?|MM?|yy(?:yy)?/g;if("php"===t)return/[dDjlNwzFmMnStyYaABgGhHis]/g;throw new Error("Invalid format type.")},nonpunctuation:/[^ -\/:-@\[-`{-~\t\n\rTZ]+/g,parseFormat:function(t,e){var i=t.replace(this.validParts(e),"\0").split("\0"),s=t.match(this.validParts(e));if(!i||!i.length||!s||0===s.length)throw new Error("Invalid date format.");return{separators:i,parts:s}},parseDate:function(t,e,i,s,a){if(t instanceof Date){var n=new Date(t.valueOf()-6e4*t.getTimezoneOffset());return n.setMilliseconds(0),n}if(/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(t)&&(e=this.parseFormat("yyyy-mm-dd",s)),/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}$/.test(t)&&(e=this.parseFormat("yyyy-mm-dd hh:ii",s)),/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}\:\d{1,2}[Z]{0,1}$/.test(t)&&(e=this.parseFormat("yyyy-mm-dd hh:ii:ss",s)),/^[-+]\d+[dmwy]([\s,]+[-+]\d+[dmwy])*$/.test(t)){var h,r=/([-+]\d+)([dmwy])/,o=t.match(/([-+]\d+)([dmwy])/g);t=new Date;for(var d=0;d<o.length;d++)switch(c=r.exec(o[d]),h=parseInt(c[1]),c[2]){case"d":t.setUTCDate(t.getUTCDate()+h);break;case"m":t=w.prototype.moveMonth.call(w.prototype,t,h);break;case"w":t.setUTCDate(t.getUTCDate()+7*h);break;case"y":t=w.prototype.moveYear.call(w.prototype,t,h)}return S(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),0)}o=t&&t.toString().match(this.nonpunctuation)||[],t=new Date(0,0,0,0,0,0,0);var l,u,c,p={},m=["hh","h","ii","i","ss","s","yyyy","yy","M","MM","m","mm","D","DD","d","dd","H","HH","p","P","z","Z"],f={hh:function(t,e){return t.setUTCHours(e)},h:function(t,e){return t.setUTCHours(e)},HH:function(t,e){return t.setUTCHours(12===e?0:e)},H:function(t,e){return t.setUTCHours(12===e?0:e)},ii:function(t,e){return t.setUTCMinutes(e)},i:function(t,e){return t.setUTCMinutes(e)},ss:function(t,e){return t.setUTCSeconds(e)},s:function(t,e){return t.setUTCSeconds(e)},yyyy:function(t,e){return t.setUTCFullYear(e)},yy:function(t,e){return t.setUTCFullYear(2e3+e)},m:function(t,e){for(e-=1;e<0;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!==e;){if(isNaN(t.getUTCMonth()))return t;t.setUTCDate(t.getUTCDate()-1)}return t},d:function(t,e){return t.setUTCDate(e)},p:function(t,e){return t.setUTCHours(1===e?t.getUTCHours()+12:t.getUTCHours())},z:function(){return a}};if(f.M=f.MM=f.mm=f.m,f.dd=f.d,f.P=f.p,f.Z=f.z,t=S(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds()),o.length===e.parts.length){d=0;for(var v=e.parts.length;d<v;d++){if(l=parseInt(o[d],10),c=e.parts[d],isNaN(l))switch(c){case"MM":u=V(x[i].months).filter(function(){var t=this.slice(0,o[d].length);return t===o[d].slice(0,t.length)}),l=V.inArray(u[0],x[i].months)+1;break;case"M":u=V(x[i].monthsShort).filter(function(){var t=this.slice(0,o[d].length),e=o[d].slice(0,t.length);return t.toLowerCase()===e.toLowerCase()}),l=V.inArray(u[0],x[i].monthsShort)+1;break;case"p":case"P":l=V.inArray(o[d].toLowerCase(),x[i].meridiem)}p[c]=l}var g;for(d=0;d<m.length;d++)(g=m[d])in p&&!isNaN(p[g])&&f[g](t,p[g])}return t},formatDate:function(t,e,i,s,a){if(null===t)return"";var n;if("standard"===s)n={t:t.getTime(),yy:t.getUTCFullYear().toString().substring(2),yyyy:t.getUTCFullYear(),m:t.getUTCMonth()+1,M:x[i].monthsShort[t.getUTCMonth()],MM:x[i].months[t.getUTCMonth()],d:t.getUTCDate(),D:x[i].daysShort[t.getUTCDay()],DD:x[i].days[t.getUTCDay()],p:2===x[i].meridiem.length?x[i].meridiem[t.getUTCHours()<12?0:1]:"",h:t.getUTCHours(),i:t.getUTCMinutes(),s:t.getUTCSeconds(),z:a},2===x[i].meridiem.length?n.H=n.h%12==0?12:n.h%12:n.H=n.h,n.HH=(n.H<10?"0":"")+n.H,n.P=n.p.toUpperCase(),n.Z=n.z,n.hh=(n.h<10?"0":"")+n.h,n.ii=(n.i<10?"0":"")+n.i,n.ss=(n.s<10?"0":"")+n.s,n.dd=(n.d<10?"0":"")+n.d,n.mm=(n.m<10?"0":"")+n.m;else{if("php"!==s)throw new Error("Invalid format type.");(n={y:t.getUTCFullYear().toString().substring(2),Y:t.getUTCFullYear(),F:x[i].months[t.getUTCMonth()],M:x[i].monthsShort[t.getUTCMonth()],n:t.getUTCMonth()+1,t:H.getDaysInMonth(t.getUTCFullYear(),t.getUTCMonth()),j:t.getUTCDate(),l:x[i].days[t.getUTCDay()],D:x[i].daysShort[t.getUTCDay()],w:t.getUTCDay(),N:0===t.getUTCDay()?7:t.getUTCDay(),S:t.getUTCDate()%10<=x[i].suffix.length?x[i].suffix[t.getUTCDate()%10-1]:"",a:2===x[i].meridiem.length?x[i].meridiem[t.getUTCHours()<12?0:1]:"",g:t.getUTCHours()%12==0?12:t.getUTCHours()%12,G:t.getUTCHours(),i:t.getUTCMinutes(),s:t.getUTCSeconds()}).m=(n.n<10?"0":"")+n.n,n.d=(n.j<10?"0":"")+n.j,n.A=n.a.toString().toUpperCase(),n.h=(n.g<10?"0":"")+n.g,n.H=(n.G<10?"0":"")+n.G,n.i=(n.i<10?"0":"")+n.i,n.s=(n.s<10?"0":"")+n.s}t=[];for(var h=V.extend([],e.separators),r=0,o=e.parts.length;r<o;r++)h.length&&t.push(h.shift()),t.push(n[e.parts[r]]);return h.length&&t.push(h.shift()),t.join("")},convertViewMode:function(t){switch(t){case 4:case"decade":t=4;break;case 3:case"year":t=3;break;case 2:case"month":t=2;break;case 1:case"day":t=1;break;case 0:case"hour":t=0}return t},headTemplate:'<thead><tr><th class="prev"><i class="{iconType} {leftArrow}"/></th><th colspan="5" class="switch"></th><th class="next"><i class="{iconType} {rightArrow}"/></th></tr></thead>',headTemplateV3:'<thead><tr><th class="prev"><span class="{iconType} {leftArrow}"></span> </th><th colspan="5" class="switch"></th><th class="next"><span class="{iconType} {rightArrow}"></span> </th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};H.template='<div class="datetimepicker"><div class="datetimepicker-minutes"><table>'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-hours"><table>'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-days"><table>'+H.headTemplate+"<tbody></tbody>"+H.footTemplate+'</table></div><div class="datetimepicker-months"><table>'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-years"><table>'+H.headTemplate+H.contTemplate+H.footTemplate+"</table></div></div>",H.templateV3='<div class="datetimepicker"><div class="datetimepicker-minutes"><table>'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-hours"><table>'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-days"><table>'+H.headTemplateV3+"<tbody></tbody>"+H.footTemplate+'</table></div><div class="datetimepicker-months"><table>'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-years"><table>'+H.headTemplateV3+H.contTemplate+H.footTemplate+"</table></div></div>",V.fn.datetimepicker.DPGlobal=H,V.fn.datetimepicker.noConflict=function(){return V.fn.datetimepicker=F,this},V(document).on("focus.datetimepicker.data-api click.datetimepicker.data-api",'[data-provide="datetimepicker"]',function(t){var e=V(this);e.data("datetimepicker")||(t.preventDefault(),e.datetimepicker("show"))}),V(function(){V('[data-provide="datetimepicker-inline"]').datetimepicker()})});
