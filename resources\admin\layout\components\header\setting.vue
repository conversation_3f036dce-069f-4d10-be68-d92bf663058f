<template>
  <div class="relative flex items-center justify-center ml-3 bg-white rounded-full cursor-pointer icon-box" @click="toggleDrawer">
    <!-- <div class="icons">
      <font-awesome-icon :icon="['fas', 'gear']" class="fa-lg" :style="{ color: '#000' }" />
    </div> -->
    {{ $t('system.profile') }}
  </div>

  <div class="drawer-box" :class="{ open: drawerVisible }">
    <el-icon size="22" color="#202020" class="close-icon" @click="closeDrawer">
      <Close />
    </el-icon>
    <Profile />
  </div>
</template>

<script lang="ts" setup>
import Profile from './profile.vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
  drawerVisible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:drawerVisible', value: boolean): void
}>()

const router = useRouter()

const toggleDrawer = () => {
  emit('update:drawerVisible', !props.drawerVisible)
}

const closeDrawer = () => {
  emit('update:drawerVisible', false)
}
</script>

<style lang="scss" scoped>
.drawer-box {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 99;
  transform: translateX(100%);

  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  border-radius: 30px 0 0 0;
  padding: 30px;
  background-color: #fff;
  height: calc(100vh - 100px);
  max-width: 375px;
  width: 100%;
  transition: transform 0.35s ease-in-out;

  &.open {
    transform: translateX(0);
  }

  .close-icon {
    position: absolute;
    right: 30px;
    top: 30px;
    cursor: pointer;
    transform: rotate(0);
    transition: transform 0.35s ease-in-out;

    &:hover {
      transform: rotate(180deg);
    }
  }
}
</style>