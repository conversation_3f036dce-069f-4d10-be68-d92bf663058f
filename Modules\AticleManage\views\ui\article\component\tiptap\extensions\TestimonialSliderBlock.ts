import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { testimonialSliderTemplate } from '../templates/testimonialSlider.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    testimonialSliderBlock: {
      insertTestimonialSliderBlock: () => ReturnType
    }
  }
}

export const TestimonialSliderBlock = Node.create({
  name: 'testimonialSliderBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="testimonial-slider"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'testimonial-slider',
      'class': 'testimonial-slider-block'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertTestimonialSliderBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(testimonialSliderTemplate)
        },
    }
  },
}) 