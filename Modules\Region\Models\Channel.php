<?php

namespace Modules\Region\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Region\Models\RegionChannel;

/**
 * 频道管理模型
 * 对应数据库表：tvb_channel
 */
class Channel extends Model
{
    // 使用软删除功能
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'channel';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'name',           // 频道名称
        'description',    // 频道描述
        'status',         // 状态: 0-禁用, 1-启用
        'sort',           // 排序
        'url',            // 频道url
        'cover_img',      // 封面图
        'created_by',     // 创建人ID
        'updated_by',     // 更新人ID
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 序列化时的日期格式
     * @var string
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 数组/JSON序列化时的日期格式
     * @var array
     */
    protected $casts = [
        'status' => 'integer',
        'sort' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 查询已启用的频道作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        // 只查询已启用状态
        return $query->where('status', 1);
    }

    /**
     * 查询已禁用的频道作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDisabled($query)
    {
        // 只查询已禁用状态
        return $query->where('status', 0);
    }

    /**
     * 按名称搜索的作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByName($query, $name)
    {
        // 按名称模糊搜索
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 按排序字段排序的作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $direction
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrderBySort($query, $direction = 'asc')
    {
        // 按排序字段排序，排序值为空的放在最后
        return $query->orderByRaw("CASE WHEN sort IS NULL THEN 1 ELSE 0 END, sort {$direction}");
    }

    /**
     * 关联区域（多对多）
     * @return BelongsToMany
     */
    public function regions(): BelongsToMany
    {
        return $this->belongsToMany(Region::class, 'regions_channel', 'channel_id', 'regions_id')
            ->using(RegionChannel::class)
            ->withPivot(['created_by', 'updated_by', 'created_at', 'updated_at']);
    }

    /**
     * 序列化时间字段为指定格式
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    }
} 