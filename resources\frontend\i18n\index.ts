import Cache from '../../frontend/support/cache'
import { createI18n } from 'vue-i18n'
import type { App } from 'vue'
import zh_HK from './languages/zh_HK'
import en from './languages/en'
import zh_CN from './languages/zh_CN'
// 动态导入模块的语言文件
const modules = import.meta.glob('@/module/*/Lang/*/common.json', { eager: true }) as Record<string, { default: Record<string, any> }>
const loadLocaleMessages = (): Record<string, any> => {
  const messages: Record<string, any> = {
    en: { ...en },
    zh_CN: { ...zh_CN },
    zh_HK: { ...zh_HK },
  }

  for (const path in modules) {
    try {
      const langFile = modules[path]
      const parts = path.split('/')
      const lang = parts[parts.length - 2] // 获取语言代码，如 en, zh_CN, zh_HK

      if (!messages[lang]) {
        messages[lang] = {}
      }

      // 修改获取模块名称的逻辑
      // 在路径中查找 Modules 后的第一个目录名作为模块名
      const moduleNameMatch = path.match(/Modules\/([^/]+)\//)
      const moduleName = moduleNameMatch ? moduleNameMatch[1] : ''

      if (moduleName && !messages[lang][moduleName]) {
        messages[lang][moduleName] = {}
      }

      if (moduleName) {
        messages[lang][moduleName] = {
          ...(messages[lang][moduleName] || {}),
          ...langFile.default,
        }
      }
    } catch (error) {
      console.warn(`Failed to load language file at ${path}:`, error)
    }
  }

  return messages
}

const i18n = createI18n({
  locale: Cache.get('bwms_language') || 'zh_HK',
  globalInjection: true,
  legacy: false,
})

export function bootstrapI18n(app: App): void {
  const messages = loadLocaleMessages()
  for (const [lang, message] of Object.entries(messages)) {
    i18n.global.setLocaleMessage(lang, message)
  }

  app.use(i18n)
}

export default i18n
