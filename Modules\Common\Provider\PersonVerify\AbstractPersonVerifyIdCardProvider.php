<?php

namespace Modules\Common\Provider\PersonVerify;

abstract class AbstractPersonVerifyIdCardProvider
{
    abstract public function name();

    abstract public function title();

    /**
     * @param $name string
     * @param $idCardNumber string
     * @param array $param
     * @return PersonVerifyIdCardResponse
     */
    abstract public function verify(string $name, string $idCardNumber, array $param = []): PersonVerifyIdCardResponse;

}
