import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import ImageTextBlockComponent from '../components/ImageTextBlockComponent.vue'

export interface ImageTextBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    imageTextBlock: {
      /**
       * 插入图文组合区块
       */
      insertImageTextBlock: (options: { 
        title: string, 
        content: string, 
        imageUrl: string,
        imageAlt?: string,
        layout?: 'image-left' | 'image-right' // 图片位置：左侧还是右侧
      }) => ReturnType
    }
  }
}

export const ImageTextBlock = Node.create<ImageTextBlockOptions>({
  name: 'imageTextBlock',
  
  group: 'block',
  
  content: '',
  
  draggable: true,
  
  isolating: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'tiptap-image-text-block',
      },
    }
  },

  addAttributes() {
    return {
      title: {
        default: '图文标题',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          return {
            'data-title': attributes.title,
          }
        },
      },
      content: {
        default: '这里是描述文字，介绍你的产品或服务特点。可以添加更多内容来吸引读者的注意力。',
        parseHTML: element => element.getAttribute('data-content'),
        renderHTML: attributes => {
          return {
            'data-content': attributes.content,
          }
        },
      },
      imageUrl: {
        default: 'https://via.placeholder.com/400x300',
        parseHTML: element => element.getAttribute('data-image-url'),
        renderHTML: attributes => {
          return {
            'data-image-url': attributes.imageUrl,
          }
        },
      },
      imageAlt: {
        default: '示例图片',
        parseHTML: element => element.getAttribute('data-image-alt'),
        renderHTML: attributes => {
          return {
            'data-image-alt': attributes.imageAlt,
          }
        },
      },
      layout: {
        default: 'image-left',
        parseHTML: element => element.getAttribute('data-layout'),
        renderHTML: attributes => {
          return {
            'data-layout': attributes.layout,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="image-text-block"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return {}
          const el = element as HTMLElement
          return {
            title: el.getAttribute('data-title') || '图文标题',
            content: el.getAttribute('data-content') || '这里是描述文字，介绍你的产品或服务特点。可以添加更多内容来吸引读者的注意力。',
            imageUrl: el.getAttribute('data-image-url') || 'https://via.placeholder.com/400x300',
            imageAlt: el.getAttribute('data-image-alt') || '示例图片',
            layout: el.getAttribute('data-layout') || 'image-left',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
      'data-type': 'image-text-block',
    })]
  },

  addCommands() {
    return {
      insertImageTextBlock: (options) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            title: options.title,
            content: options.content,
            imageUrl: options.imageUrl,
            imageAlt: options.imageAlt || '示例图片',
            layout: options.layout || 'image-left',
          },
        })
      },
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(ImageTextBlockComponent)
  },
}) 