import { marketingLandingPageTemplate } from './marketingLandingPage.template';
import { shiliPageTemplate } from './shili.template';
import { aboutPageTemplate } from './about.template';
import { thanksPageTemplate } from './thankspage.template';
import { landingPageTemplate } from './landing.template';
import { chickthroughPageTemplate } from './chickthrough.template';
import { downloadResourceTemplate } from './downloadResource.template';
import { productsTemplate } from './products.tamplate';

export {
  marketingLandingPageTemplate,
  shiliPageTemplate,
  aboutPageTemplate,
  thanksPageTemplate,
  landingPageTemplate,
  chickthroughPageTemplate,
  downloadResourceTemplate,
  productsTemplate
}

// 页面模板数据，用于显示在侧边栏
export const pageTemplates = [
  {
    id: 'marketing-landing-page',
    name: '營銷落地頁',
    description: '適合產品推廣的完整落地頁，包含定價表、數據展示、FAQ等多個部分',
    thumbnail: 'https://via.placeholder.com/200x150/4e73df/ffffff?text=營銷落地頁', // 更好的缩略图
    template: marketingLandingPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-s-marketing' // 添加图标
  },
  {
    id: 'products-page',
    name: '產品營銷頁',
    description: '展示產品功能、數據統計、客戶評價的現代化產品營銷頁面',
    thumbnail: 'https://via.placeholder.com/200x150/6c5ce7/ffffff?text=產品營銷頁',
    template: productsTemplate,
    category: 'marketing',
    iconClass: 'el-icon-goods'
  },
  {
    id: 'demo-landing-page',
    name: '預約演示頁',
    description: '適合SaaS產品的演示預約頁面，包含特性展示、客戶評價、FAQ等部分',
    thumbnail: 'https://via.placeholder.com/200x150/1abc9c/ffffff?text=預約演示頁',
    template: landingPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-video-camera'
  },
  {
    id: 'download-resource-page',
    name: '資源下載頁',
    description: '用於收集用戶信息並提供數字資源下載的頁面，包含表單、用戶評價和常見問題',
    thumbnail: 'https://via.placeholder.com/200x150/6610f2/ffffff?text=資源下載頁',
    template: downloadResourceTemplate,
    category: 'marketing',
    iconClass: 'el-icon-download'
  },
  {
    id: 'shili-page',
    name: '營銷型示例頁',
    description: '包含品牌展示、多媒體內容、客戶評價和社交媒體鏈接的完整營銷網站',
    thumbnail: 'https://via.placeholder.com/200x150/6f42c1/ffffff?text=營銷型示例頁',
    template: shiliPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-picture-outline'
  },
  {
    id: 'about-page',
    name: '團隊介紹頁',
    description: '展示團隊成員、公司理念、核心價值觀和服務內容的團隊介紹頁面',
    thumbnail: 'https://via.placeholder.com/200x150/20c997/ffffff?text=團隊介紹頁',
    template: aboutPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-user'
  },
  {
    id: 'thanks-page',
    name: '資源感謝頁',
    description: '展示資源下載後的感謝信息、下載按鈕和後續步驟引導的頁面',
    thumbnail: 'https://via.placeholder.com/200x150/fd7e14/ffffff?text=資源感謝頁',
    template: thanksPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-download'
  },
  {
    id: 'chickthrough-page',
    name: '現代營銷頁',
    description: '現代化營銷頁面，包含儀表盤展示、自動化工作流、數據統計和客戶推薦',
    thumbnail: 'https://via.placeholder.com/200x150/3498db/ffffff?text=現代營銷頁',
    template: chickthroughPageTemplate,
    category: 'marketing',
    iconClass: 'el-icon-data-analysis'
  }
]; 