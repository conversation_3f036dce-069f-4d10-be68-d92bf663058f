<?php

declare(strict_types=1);

namespace Modules\Region\Enums;

use Modules\Core\Enums\BaseEnum;

/**
 * 区域模块错误码枚举
 */
enum RegionErrorCode: int
{
    /**
     * 通用错误 (15000-15099)
     */
    case REGION_NOT_FOUND = 15000;
    case REGION_ALREADY_EXISTS = 15001;
    case REGION_DISABLED = 15002;

    /**
     * 创建相关错误 (15100-15199)
     */
    case REGION_CREATE_FAILED = 15100;
    case REGION_IMPORT_FAILED = 15101;
    case REGION_BATCH_CREATE_FAILED = 15102;

    /**
     * 更新相关错误 (15200-15299)
     */
    case REGION_UPDATE_FAILED = 15200;
    case REGION_STATUS_UPDATE_FAILED = 15201;
    case REGION_SORT_UPDATE_FAILED = 15202;

    /**
     * 删除相关错误 (15300-15399)
     */
    case REGION_DELETE_FAILED = 15300;
    case REGION_BATCH_DELETE_FAILED = 15301;
    case REGION_SOFT_DELETE_FAILED = 15302;

    /**
     * 查询相关错误 (15400-15499)
     */
    case REGION_QUERY_FAILED = 15400;
    case REGION_SEARCH_FAILED = 15401;
    case REGION_LIST_FAILED = 15402;

    /**
     * 状态相关错误 (15500-15599)
     */
    case REGION_STATUS_INVALID = 15500;
    case REGION_STATE_TRANSITION_FAILED = 15501;
    case REGION_STATUS_CHANGE_FAILED = 15502;

    /**
     * 验证相关错误 (15600-15699)
     */
    case REGION_VALIDATION_FAILED = 15600;
    case REGION_NAME_VALIDATION_FAILED = 15601;
    case REGION_SORT_VALIDATION_FAILED = 15602;

    /**
     * 处理相关错误 (15700-15799)
     */
    case REGION_PROCESS_FAILED = 15700;
    case REGION_SYNC_FAILED = 15701;
    case REGION_IMPORT_PROCESS_FAILED = 15702;

    /**
     * 业务相关错误 (15800-15899)
     */
    case REGION_BUSINESS_RULE_VIOLATED = 15800;
    case REGION_PERMISSION_DENIED = 15801;
    case REGION_QUOTA_EXCEEDED = 15802;

    /**
     * 系统相关错误 (15900-15999)
     */
    case REGION_SYSTEM_ERROR = 15900;
    case REGION_DATABASE_ERROR = 15901;
    case REGION_NETWORK_ERROR = 15902;

    /**
     * 获取错误信息
     *
     * @return string
     */
    public function message(): string
    {
        return match ($this) {
            // 通用错误
            self::REGION_NOT_FOUND => '区域不存在',
            self::REGION_ALREADY_EXISTS => '区域已存在',
            self::REGION_DISABLED => '区域已禁用',

            // 创建相关错误
            self::REGION_CREATE_FAILED => '创建区域失败',
            self::REGION_IMPORT_FAILED => '区域导入失败',
            self::REGION_BATCH_CREATE_FAILED => '批量创建区域失败',

            // 更新相关错误
            self::REGION_UPDATE_FAILED => '更新区域失败',
            self::REGION_STATUS_UPDATE_FAILED => '更新区域状态失败',
            self::REGION_SORT_UPDATE_FAILED => '更新区域排序失败',

            // 删除相关错误
            self::REGION_DELETE_FAILED => '删除区域失败',
            self::REGION_BATCH_DELETE_FAILED => '批量删除区域失败',
            self::REGION_SOFT_DELETE_FAILED => '软删除区域失败',

            // 查询相关错误
            self::REGION_QUERY_FAILED => '查询区域失败',
            self::REGION_SEARCH_FAILED => '搜索区域失败',
            self::REGION_LIST_FAILED => '获取区域列表失败',

            // 状态相关错误
            self::REGION_STATUS_INVALID => '区域状态无效',
            self::REGION_STATE_TRANSITION_FAILED => '区域状态转换失败',
            self::REGION_STATUS_CHANGE_FAILED => '更新区域状态失败',

            // 验证相关错误
            self::REGION_VALIDATION_FAILED => '区域验证失败',
            self::REGION_NAME_VALIDATION_FAILED => '区域名称验证失败',
            self::REGION_SORT_VALIDATION_FAILED => '区域排序验证失败',

            // 处理相关错误
            self::REGION_PROCESS_FAILED => '处理区域数据失败',
            self::REGION_SYNC_FAILED => '同步区域数据失败',
            self::REGION_IMPORT_PROCESS_FAILED => '处理区域导入数据失败',

            // 业务相关错误
            self::REGION_BUSINESS_RULE_VIOLATED => '违反区域业务规则',
            self::REGION_PERMISSION_DENIED => '区域权限不足',
            self::REGION_QUOTA_EXCEEDED => '超出区域配额限制',

            // 系统相关错误
            self::REGION_SYSTEM_ERROR => '区域系统错误',
            self::REGION_DATABASE_ERROR => '区域数据库错误',
            self::REGION_NETWORK_ERROR => '区域网络错误',
        };
    }

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::REGION_VALIDATION_FAILED, self::REGION_NAME_VALIDATION_FAILED, 
            self::REGION_SORT_VALIDATION_FAILED => 400,
            self::REGION_PERMISSION_DENIED => 403,
            self::REGION_NOT_FOUND, self::REGION_QUERY_FAILED, 
            self::REGION_SEARCH_FAILED, self::REGION_LIST_FAILED => 404,
            self::REGION_SYSTEM_ERROR, self::REGION_DATABASE_ERROR, 
            self::REGION_NETWORK_ERROR => 503,
            default => 400,
        };
    }
} 