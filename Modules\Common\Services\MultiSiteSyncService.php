<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use Illuminate\Support\Facades\Log;
use Modules\Config\Models\Site;
use Modules\Config\Services\SiteContext;
use Modules\Common\Models\MultiSiteSyncRecord;
use Modules\Common\Events\MultiSiteSyncRequested;

class MultiSiteSyncService
{
    /**
     * 同步数据到指定站点
     *
     * @param string $module 模块名称 (如: cms, blog, form)
     * @param string $dataType 数据类型 (如: content, category, form)
     * @param int $dataId 数据ID
     * @param array $data 数据内容
     * @param array $targetSiteIds 目标站点ID数组
     * @param callable $saveCallback 保存回调函数
     * @param array $options 额外选项
     * @return array 同步结果
     */
    public function syncToSites(
        string $module,
        string $dataType,
        int $dataId,
        array $data,
        array $targetSiteIds,
        callable $saveCallback,
        array $options = []
    ): array {
        $currentSiteId = SiteContext::instance()->getCurrentSiteId();
        if (!$currentSiteId) {
            return ['error' => '无法获取当前站点信息'];
        }

        $results = [];
        foreach ($targetSiteIds as $targetSiteId) {
            try {
                $result = $this->syncToSingleSite(
                    $module,
                    $dataType,
                    $dataId,
                    $data,
                    $currentSiteId,
                    $targetSiteId,
                    $saveCallback,
                    $options
                );
                $results[$targetSiteId] = $result;
            } catch (\Exception $e) {
                Log::error('多站点同步失败', [
                    'module' => $module,
                    'data_type' => $dataType,
                    'data_id' => $dataId,
                    'target_site_id' => $targetSiteId,
                    'error' => $e->getMessage()
                ]);
                $results[$targetSiteId] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 异步同步到指定站点
     */
    public function asyncSyncToSites(
        string $module,
        string $dataType,
        int $dataId,
        array $data,
        array $targetSiteIds,
        string $callbackClass,
        string $callbackMethod,
        array $options = []
    ): void {
        $currentSiteId = SiteContext::instance()->getCurrentSiteId();
        if (!$currentSiteId) {
            Log::warning('无法获取当前站点信息，跳过异步同步');
            return;
        }
        event(new MultiSiteSyncRequested(
            $module,
            $dataType,
            $dataId,
            $data,
            $currentSiteId,
            $targetSiteIds,
            $callbackClass,
            $callbackMethod,
            $options
        ));
    }

    /**
     * 同步到单个站点
     */
    protected function syncToSingleSite(
        string $module,
        string $dataType,
        int $dataId,
        array $data,
        int $sourceSiteId,
        int $targetSiteId,
        callable $saveCallback,
        array $options = []
    ): array {
        // 1. 获取目标站点信息
        $targetSite = Site::find($targetSiteId);
        if (!$targetSite || $targetSite->status !== 1) {
            throw new \Exception("目标站点不存在或未激活: {$targetSiteId}");
        }

        // 2. 创建同步记录
        $syncRecord = $this->createSyncRecord(
            $module,
            $dataType,
            $dataId,
            $sourceSiteId,
            $targetSiteId
        );

        // 3. 标记为同步中
        $syncRecord->markAsSyncing();

        try {
            // 4. 调用适配器处理同步，传递完整的同步信息
            $result = $saveCallback([
                'data' => $data,
                'target_site' => $targetSite,
                'source_site_id' => $sourceSiteId,
                'sync_record' => $syncRecord,
                'options' => $options
            ]);

            // 5. 处理适配器返回结果
            if (is_array($result)) {
                // 适配器返回详细结果
                $targetDataId = $result['target_data_id'];
                $syncLanguages = $result['sync_languages'] ?? [];
                $skipReason = $result['skip_reason'] ?? null;

                if ($skipReason) {
                    $syncRecord->markAsSkipped($skipReason);
                    return ['success' => false, 'reason' => $skipReason];
                }
            } else {
                // 适配器只返回ID
                $targetDataId = $result;
                $syncLanguages = [];
            }

            // 6. 标记为成功
            $syncRecord->markAsSuccess($targetDataId, $syncLanguages);

            return [
                'success' => true,
                'target_data_id' => $targetDataId,
                'sync_languages' => $syncLanguages
            ];

        } catch (\Exception $e) {
            $syncRecord->markAsFailed($e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建同步记录
     */
    protected function createSyncRecord(
        string $module,
        string $dataType,
        int $dataId,
        int $sourceSiteId,
        int $targetSiteId
    ): MultiSiteSyncRecord {
        return MultiSiteSyncRecord::firstOrCreate([
            'module' => $module,
            'data_type' => $dataType,
            'source_site_id' => $sourceSiteId,
            'target_site_id' => $targetSiteId,
            'source_data_id' => $dataId,
        ], [
            'sync_status' => 'pending',
            'created_at' => time(),
            'updated_at' => time(),
        ]);
    }




}
