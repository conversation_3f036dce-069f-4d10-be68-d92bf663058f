<?php

declare(strict_types=1);

namespace Modules\Common\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Common\Events\MultiSiteSyncRequested;
use Modules\Common\Listeners\MultiSiteSyncListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * 事件监听器映射
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        MultiSiteSyncRequested::class => [
            MultiSiteSyncListener::class,
        ],
    ];

    /**
     * 注册任何事件
     */
    public function boot(): void
    {
        //
    }

    /**
     * 确定是否应自动发现事件和监听器
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
} 