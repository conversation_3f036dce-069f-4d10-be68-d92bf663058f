-- 配置中心
DROP TABLE IF EXISTS `tvb_config`;
CREATE TABLE `tvb_config` (
    id bigint unsigned auto_increment comment 'ID  自增' primary key,
    `key` varchar(100) not null comment '配置key',
    value text null comment '配置value',
    type varchar(10) null comment '配置类型',
    `desc` varchar(50) null comment '描述',
    lang varchar(50) null comment '语言标识',
    creator_id int unsigned default 0 not null comment 'creator id',
    created_at int unsigned default 0 not null comment 'created time',
    updated_at int unsigned default 0 not null comment 'updated time',
    deleted_at int unsigned default 0 not null comment 'delete time',
    constraint bingo_config_id_unique unique (id),
    constraint bingo_config_key_unique unique (`key`, lang)
) comment '配置中心';

-- ========================================
-- RBAC权限系统核心表结构
-- ========================================

-- 管理员表
DROP TABLE IF EXISTS `tvb_admins`;
CREATE TABLE `tvb_admins` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    `email` VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址',
    `first_name` VARCHAR(50) NULL COMMENT '名字',
    `last_name` VARCHAR(50) NULL COMMENT '姓氏',
    `real_name` VARCHAR(100) NULL COMMENT '真实姓名',
    `pen_name` VARCHAR(100) NULL COMMENT '笔名，用于前台显示',
    `phone` VARCHAR(20) NULL COMMENT '手机号码',
    `avatar_url` VARCHAR(255) NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用，2-待激活',
    `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) NULL COMMENT '最后登录IP',
    `login_count` INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    `language` VARCHAR(10) NOT NULL DEFAULT 'zh_HK' COMMENT '语言',
    `created_by` BIGINT UNSIGNED NULL COMMENT '创建者ID',
    `updated_by` BIGINT UNSIGNED NULL COMMENT '更新者ID',
    `password_changed_at` TIMESTAMP NULL COMMENT '密码最后修改时间',
    `created_at` TIMESTAMP NULL COMMENT '创建时间',
    `updated_at` TIMESTAMP NULL COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_username` (`username`,`is_deleted`),
    UNIQUE KEY `uk_email` (`email`,`is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '管理员表';

-- 笔名管理表
DROP TABLE IF EXISTS `tvb_pen_names`;
CREATE TABLE `tvb_pen_names` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '笔名ID',
    `pen_name` VARCHAR(100) UNIQUE NOT NULL COMMENT '笔名',
    `pen_name_type` TINYINT NOT NULL DEFAULT 1 COMMENT '笔名类型: 1-真名, 2-假名，3-自家来源，4-其他',
    `description` VARCHAR(255) COMMENT '笔名描述',
    `created_by` INT NOT NULL COMMENT '创建者ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `last_used_at` TIMESTAMP NULL COMMENT '最后使用时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_pen_name` (`pen_name`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '笔名表';

-- 管理员笔名关联表
DROP TABLE IF EXISTS `tvb_admin_pen_names`;
CREATE TABLE `tvb_admin_pen_names` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    `admin_id` INT NOT NULL COMMENT '管理员ID',
    `pen_name_id` INT NOT NULL COMMENT '笔名ID',
    `is_default` BOOLEAN DEFAULT FALSE COMMENT '是否为默认笔名',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_admin_pen_name` (`admin_id`, `pen_name_id`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '管理员笔名关联表';

-- 角色表
DROP TABLE IF EXISTS `tvb_roles`;
CREATE TABLE `tvb_roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    `role_name` VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) UNIQUE NOT NULL COMMENT '角色代码',
    `description` VARCHAR(255) COMMENT '角色描述',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设角色',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '角色表';

-- 权限表
DROP TABLE IF EXISTS `tvb_permissions`;

CREATE TABLE `tvb_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(100) UNIQUE NOT NULL COMMENT '权限代码',
    `module` VARCHAR(50) NOT NULL COMMENT '所属模块',
    `resource` VARCHAR(50) NOT NULL COMMENT '资源类型',
    `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `description` VARCHAR(255) COMMENT '权限描述',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否为系统预设权限',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '权限表';

-- 菜单表
DROP TABLE IF EXISTS `tvb_menus`;

CREATE TABLE `tvb_menus` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '菜单ID',
    `parent_id` INT DEFAULT 0 COMMENT '父级菜单ID',
    `menu_name` VARCHAR(100) NOT NULL COMMENT '菜单名称',
    `menu_code` VARCHAR(100) UNIQUE NOT NULL COMMENT '菜单代码',
    `menu_type` TINYINT NOT NULL DEFAULT 1 COMMENT '菜单类型: 1-主菜单, 2-子菜单, 3-按钮',
    `route_path` VARCHAR(255) COMMENT '路由路径',
    `component_path` VARCHAR(255) COMMENT '组件路径',
    `icon` VARCHAR(100) COMMENT '菜单图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `is_visible` BOOLEAN DEFAULT TRUE COMMENT '是否可见',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否为系统菜单',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '菜单表';

-- 角色权限关联表
DROP TABLE IF EXISTS `tvb_role_permissions`;

CREATE TABLE `tvb_role_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    `role_id` INT NOT NULL COMMENT '角色ID',
    `permission_id` INT NOT NULL COMMENT '权限ID',
    `granted_by` INT NOT NULL COMMENT '授权人ID',
    `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    `expires_at` TIMESTAMP NULL COMMENT '权限过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '角色权限关联表';

-- 角色菜单关联表
DROP TABLE IF EXISTS `tvb_role_menus`;

CREATE TABLE `tvb_role_menus` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    `role_id` INT NOT NULL COMMENT '角色ID',
    `menu_id` INT NOT NULL COMMENT '菜单ID',
    `granted_by` INT NOT NULL COMMENT '授权人ID',
    `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_role_menu` (`role_id`, `menu_id`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '角色菜单关联表';

-- 数据权限表
DROP TABLE IF EXISTS `tvb_data_permissions`;

CREATE TABLE `tvb_data_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '数据权限ID',
    `role_id` INT NOT NULL COMMENT '角色ID',
    `resource_type` VARCHAR(50) NOT NULL COMMENT '资源类型',
    `permission_type` TINYINT NOT NULL COMMENT '权限类型: 1-全部, 2-部门, 3-个人, 4-自定义',
    `scope_value` JSON COMMENT '权限范围值',
    `created_by` INT NOT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '数据权限表';

-- 操作日志表
DROP TABLE IF EXISTS `tvb_operation_logs`;

CREATE TABLE `tvb_operation_logs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    `admin_id` INT NOT NULL COMMENT '操作人ID',
    `module` VARCHAR(50) NOT NULL COMMENT '操作模块',
    `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `resource_type` VARCHAR(50) COMMENT '资源类型',
    `resource_id` VARCHAR(100) COMMENT '资源ID',
    `description` VARCHAR(500) COMMENT '操作描述',
    `request_data` JSON COMMENT '请求数据',
    `response_data` JSON COMMENT '响应数据',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '操作状态: 0-失败, 1-成功',
    `error_message` TEXT COMMENT '错误信息',
    `execution_time` INT COMMENT '执行时间(毫秒)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '操作日志表';

-- 添加用户角色关联表
DROP TABLE IF EXISTS `tvb_admin_roles`;

CREATE TABLE `tvb_admin_roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    `admin_id` INT NOT NULL COMMENT '管理员ID',
    `role_id` INT NOT NULL COMMENT '角色ID',
    `is_primary` BOOLEAN DEFAULT FALSE COMMENT '是否为主角色',
    `assigned_by` INT NOT NULL COMMENT '分配人ID',
    `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    `expires_at` TIMESTAMP NULL COMMENT '角色过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_admin_role` (`admin_id`, `role_id`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '管理员角色关联表';



##################### Faq #####################
-- 常见问题表
DROP TABLE IF EXISTS `tvb_faq`;
CREATE TABLE `tvb_faq` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '常见问题ID',
    `code` VARCHAR(255) NOT NULL COMMENT '问题代码',
    `category_id` INT NOT NULL COMMENT '分类ID',
    `title` VARCHAR(255) NOT NULL COMMENT '问题标题',
    `content` TEXT NOT NULL COMMENT '问题内容',
    `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态: 0-草稿, 1-已发布, 2-已下架',
    `lang`   VARCHAR(10) NOT NULL DEFAULT 'zh_HK' COMMENT '语言',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `created_by` INT NOT NULL COMMENT '创建人ID',
    `updated_by` INT NOT NULL COMMENT '更新人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '常见问题表';

-- faq与SEO关联表
DROP TABLE IF EXISTS `tvb_faq_seo`;
CREATE TABLE `tvb_faq_seo` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'SEO ID',
    `faq_id` INT NOT NULL COMMENT '常见问题ID',
    `title` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 标题',
    `description` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 描述',
    `keywords` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 关键词',
    `regular_url`   VARCHAR(255) DEFAULT NULL COMMENT '正则URL',
    `allow_index` BOOLEAN DEFAULT FALSE COMMENT '允许索引',
    `allow_follow` BOOLEAN DEFAULT FALSE COMMENT '允许跟随',
    `open_graph_title` VARCHAR(200) DEFAULT NULL COMMENT 'Open Graph 标题',
    `open_graph_description` VARCHAR(200) DEFAULT NULL COMMENT 'Open Graph 描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '常见问题SEO表';

-- faq标签关联表
DROP TABLE IF EXISTS `tvb_faq_tags`;
CREATE TABLE `tvb_faq_tags` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    `faq_id` INT NOT NULL COMMENT '常见问题ID',
    `tag_id` INT NOT NULL COMMENT '标签ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_faq_tag` (`faq_id`, `tag_id`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '常见问题标签关联表';

-- 常见问题分类表
DROP TABLE IF EXISTS `tvb_faq_category`;
CREATE TABLE `tvb_faq_category` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    `name` VARCHAR(255) NOT NULL COMMENT '分类名称',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `parent_id` INT NOT NULL COMMENT '父级分类ID',
    `root_id` INT NOT NULL COMMENT '根分类ID',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_name` (`name`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '常见问题分类表';


##################### SEO #####################

-- SEO表
DROP TABLE IF EXISTS `tvb_seo`;
CREATE TABLE `tvb_seo` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT 'SEO ID',
    `title` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 标题',
    `description` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 描述',
    `keywords` VARCHAR(200) DEFAULT NULL COMMENT 'SEO 关键词',
    `regular_url`   VARCHAR(255) DEFAULT NULL COMMENT '正则URL',
    `allow_index` BOOLEAN DEFAULT FALSE COMMENT '允许索引',
    `allow_follow` BOOLEAN DEFAULT FALSE COMMENT '允许跟随',
    `open_graph_title` VARCHAR(200) DEFAULT NULL COMMENT 'Open Graph 标题',
    `open_graph_description` VARCHAR(200) DEFAULT NULL COMMENT 'Open Graph 描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'SEO表';



##################### 标签 #####################

-- 标签表
DROP TABLE IF EXISTS `tvb_tags`;
CREATE TABLE `tvb_tags` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    `name` VARCHAR(200) NOT NULL COMMENT '标签名称',
    `reference_name` VARCHAR(200) NOT NULL COMMENT '参考名称',
    `category_id` INT NOT NULL COMMENT '分类ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `created_by` INT NOT NULL COMMENT '创建人ID',
    `updated_by` INT NOT NULL COMMENT '更新人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL COMMENT '软删除时间',
    `is_deleted` TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除: 0-未删除, 1-已删除',
    UNIQUE KEY `uk_name` (`name`, `is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '标签表';








