import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { ctaTemplate } from '../templates/cta.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    ctaBlock: {
      insertCtaBlock: () => ReturnType
    }
  }
}

export const CtaBlock = Node.create({
  name: 'ctaBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  // 允许包含rich-text和button组件
  content: 'block*',

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="cta"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }

          const classList = Array.from(element.classList)
          const bgClass = classList.find(cls => cls.startsWith('bg-')) || 'bg-primary'
          const textClass = classList.find(cls => cls.startsWith('text-')) || 'text-white'
          
          return {
            backgroundColor: bgClass,
            textColor: textClass,
            class: element.getAttribute('class') || 'py-5 text-white bg-primary'
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // 确保Bootstrap样式已加载（如果需要）
    const bootstrapStyle = document.querySelector('link[href*="bootstrap"]');
    if (!bootstrapStyle) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css';
      document.head.appendChild(link);
    }
    
    // 合并属性，确保data-bs-component="cta"属性始终存在
    const finalAttributes = mergeAttributes(
      {
        'data-bs-component': 'cta',
        'class': HTMLAttributes.class || 'py-5 text-white bg-primary'
      },
      HTMLAttributes
    )
    
    // 确保data-bs-component不被其他属性覆盖
    finalAttributes['data-bs-component'] = 'cta'
    
    return ['div', finalAttributes, 0]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: 'py-5 text-white bg-primary',
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return { class: 'py-5 text-white bg-primary' }
          }
          
          return {
            class: attributes.class
          }
        }
      },
      backgroundColor: {
        default: 'bg-primary',
        parseHTML: element => {
          if (!(element instanceof HTMLElement)) {
            return 'bg-primary'
          }
          
          const classList = element.className.split(' ');
          const bgClass = classList.find(cls => cls.startsWith('bg-'));
          return bgClass || 'bg-primary';
        },
        renderHTML: attributes => {
          // 避免重复添加到class属性
          return {}
        }
      },
      textColor: {
        default: 'text-white',
        parseHTML: element => {
          if (!(element instanceof HTMLElement)) {
            return 'text-white'
          }
          
          const classList = element.className.split(' ');
          const textClass = classList.find(cls => cls.startsWith('text-'));
          return textClass || 'text-white';
        },
        renderHTML: attributes => {
          // 避免重复添加到class属性
          return {}
        }
      }
    }
  },

  addCommands() {
    return {
      insertCtaBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(ctaTemplate)
        },
    }
  },
}) 