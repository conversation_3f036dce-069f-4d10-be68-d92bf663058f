# Web控制器模板

## 概述

本文档提供了Web控制器的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Web\Controllers;

use Bingo\Module\WebBaseController;
use Bingo\Exceptions\BizException;
use Modules\YourModule\Web\Requests\ListRequest;
use Modules\YourModule\Web\Requests\CreateRequest;
use Modules\YourModule\Web\Requests\UpdateRequest;
use Modules\YourModule\Web\Requests\CommentRequest;
use Modules\YourModule\Services\YourService;
use Modules\YourModule\Enums\YourModuleErrorCode;
use Illuminate\View\View;

final class YourController extends WebBaseController
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    /**
     * 列表页面
     */
    public function index(ListRequest $request): View
    {
        $result = $this->service->getList($request->validated());
        
        return view('yourmodule::index', [
            'items' => $result->items(),
            'total' => $result->total(),
            'filters' => $request->validated()
        ]);
    }

    /**
     * 详情页面
     */
    public function show(int $id): View
    {
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 增加浏览次数
        $this->service->incrementViews($id);
        
        // 获取相关推荐
        $relatedItems = $this->service->getRelatedItems($id, limit: 6);
        
        return view('yourmodule::show', [
            'item' => $item,
            'relatedItems' => $relatedItems
        ]);
    }

    /**
     * 创建页面
     */
    public function create(): View
    {
        // 获取分类列表
        $categories = $this->service->getCategories();
        
        // 获取标签列表
        $tags = $this->service->getTags();
        
        return view('yourmodule::create', [
            'categories' => $categories,
            'tags' => $tags
        ]);
    }

    /**
     * 保存记录
     */
    public function store(CreateRequest $request): array
    {
        // 创建记录
        $item = $this->service->create([
            ...$request->validated(),
            'user_id' => $this->getCurrentUserId()
        ]);
        
        return [
            'message' => '创建成功',
            'redirect' => route('yourmodule.show', ['id' => $item->id])
        ];
    }

    /**
     * 编辑页面
     */
    public function edit(int $id): View
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 权限检查
        if ($item->user_id !== $this->getCurrentUserId()) {
            BizException::throws(YourModuleErrorCode::PERMISSION_DENIED);
        }
        
        // 获取分类列表
        $categories = $this->service->getCategories();
        
        // 获取标签列表
        $tags = $this->service->getTags();
        
        return view('yourmodule::edit', [
            'item' => $item,
            'categories' => $categories,
            'tags' => $tags
        ]);
    }

    /**
     * 更新记录
     */
    public function update(int $id, UpdateRequest $request): array
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 权限检查
        if ($item->user_id !== $this->getCurrentUserId()) {
             BizException::throws(YourModuleErrorCode::PERMISSION_DENIED);
        }
        
        // 更新记录
        $this->service->update($id, $request->validated());
        
        return [
            'message' => '更新成功',
            'redirect' => route('yourmodule.show', ['id' => $id])
        ];
    }

    /**
     * 删除记录
     */
    public function destroy(int $id): array
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 权限检查
        if ($item->user_id !== $this->getCurrentUserId()) {
            BizException::throws(YourModuleErrorCode::PERMISSION_DENIED);
        }
        
        // 删除记录
        $this->service->delete($id);
        
        return [
            'message' => '删除成功',
            'redirect' => route('yourmodule.index')
        ];
    }

    /**
     * 点赞功能
     */
    public function like(int $id): array
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 点赞
        $liked = $this->service->toggleLike($id, $this->getCurrentUserId());
        
        return [
            'message' => $liked ? '点赞成功' : '取消点赞',
            'liked' => $liked
        ];
    }

    /**
     * 收藏功能
     */
    public function favorite(int $id): array
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 收藏
        $favorited = $this->service->toggleFavorite($id, $this->getCurrentUserId());
        
        return [
            'message' => $favorited ? '收藏成功' : '取消收藏',
            'favorited' => $favorited
        ];
    }

    /**
     * 评论功能
     */
    public function comment(int $id, CommentRequest $request): array
    {
        // 获取记录
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        // 创建评论
        $comment = $this->service->createComment([
            ...$request->validated(),
            'user_id' => $this->getCurrentUserId(),
            'item_id' => $id
        ]);
        
        return [
            'message' => '评论成功',
            'comment' => $comment
        ];
    }
}
```

## 请求验证类示例

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Web\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:50'],
            'keyword' => ['sometimes', 'string', 'max:100'],
            'category' => ['sometimes', 'string', Rule::exists('categories', 'slug')],
            'tag' => ['sometimes', 'string', Rule::exists('tags', 'slug')],
            'sort' => ['sometimes', 'string', Rule::in(['latest', 'popular', 'recommended'])],
        ];
    }

    public function messages(): array
    {
        return [
            'page.integer' => T('YourModule::validation.page.integer'),
            'page.min' => T('YourModule::validation.page.min'),
            'limit.integer' => T('YourModule::validation.limit.integer'),
            'limit.min' => T('YourModule::validation.limit.min'),
            'limit.max' => T('YourModule::validation.limit.max'),
            'keyword.string' => T('YourModule::validation.keyword.string'),
            'keyword.max' => T('YourModule::validation.keyword.max'),
            'category.string' => T('YourModule::validation.category.string'),
            'category.exists' => T('YourModule::validation.category.exists'),
            'tag.string' => T('YourModule::validation.tag.string'),
            'tag.exists' => T('YourModule::validation.tag.exists'),
            'sort.string' => T('YourModule::validation.sort.string'),
            'sort.in' => T('YourModule::validation.sort.in'),
        ];
    }
}

class CreateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string', 'min:10'],
            'category_id' => ['required', 'integer', Rule::exists('categories', 'id')],
            'tags' => ['nullable', 'array', 'max:5'],
            'tags.*' => ['integer', Rule::exists('tags', 'id')],
            'images' => ['nullable', 'array', 'max:5'],
            'images.*' => [
                'file',
                'image',
                'max:2048', // 2MB
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000',
                'mimes:jpeg,png,jpg',
            ],
            'publish_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => T('YourModule::validation.title.required'),
            'title.string' => T('YourModule::validation.title.string'),
            'title.max' => T('YourModule::validation.title.max'),
            'content.required' => T('YourModule::validation.content.required'),
            'content.string' => T('YourModule::validation.content.string'),
            'content.min' => T('YourModule::validation.content.min'),
            'category_id.required' => T('YourModule::validation.category_id.required'),
            'category_id.integer' => T('YourModule::validation.category_id.integer'),
            'category_id.exists' => T('YourModule::validation.category_id.exists'),
            'tags.array' => T('YourModule::validation.tags.array'),
            'tags.max' => T('YourModule::validation.tags.max'),
            'tags.*.integer' => T('YourModule::validation.tags.*.integer'),
            'tags.*.exists' => T('YourModule::validation.tags.*.exists'),
            'images.array' => T('YourModule::validation.images.array'),
            'images.max' => T('YourModule::validation.images.max'),
            'images.*.file' => T('YourModule::validation.images.*.file'),
            'images.*.image' => T('YourModule::validation.images.*.image'),
            'images.*.max' => T('YourModule::validation.images.*.max'),
            'images.*.dimensions' => T('YourModule::validation.images.*.dimensions'),
            'images.*.mimes' => T('YourModule::validation.images.*.mimes'),
            'publish_at.date' => T('YourModule::validation.publish_at.date'),
            'publish_at.after' => T('YourModule::validation.publish_at.after'),
        ];
    }
}

class CommentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'content' => [
                'required', 
                'string', 
                'min:2',
                'max:1000',
                function (string $attribute, mixed $value, Closure $fail) {
                    // 敏感词检查
                    if ($this->service->containsSensitiveWords($value)) {
                        $fail(T('YourModule::validation.content.contains_sensitive_words'));
                    }
                }
            ],
            'parent_id' => [
                'nullable',
                'integer',
                Rule::exists('comments', 'id')->where(function ($query) {
                    $query->whereNull('parent_id'); // 只允许二级评论
                }),
            ],
            'images' => ['nullable', 'array', 'max:3'],
            'images.*' => [
                'file',
                'image',
                'max:1024', // 1MB
                'dimensions:min_width=100,min_height=100,max_width=1000,max_height=1000',
                'mimes:jpeg,png,jpg',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'content.required' => T('YourModule::validation.content.required'),
            'content.string' => T('YourModule::validation.content.string'),
            'content.min' => T('YourModule::validation.content.min'),
            'content.max' => T('YourModule::validation.content.max'),
            'parent_id.integer' => T('YourModule::validation.parent_id.integer'),
            'parent_id.exists' => T('YourModule::validation.parent_id.exists'),
            'images.array' => T('YourModule::validation.images.array'),
            'images.max' => T('YourModule::validation.images.max'),
            'images.*.file' => T('YourModule::validation.images.*.file'),
            'images.*.image' => T('YourModule::validation.images.*.image'),
            'images.*.max' => T('YourModule::validation.images.*.max'),
            'images.*.dimensions' => T('YourModule::validation.images.*.dimensions'),
            'images.*.mimes' => T('YourModule::validation.images.*.mimes'),
        ];
    }
}
```

## 规范要求

1. 命名规范
   - 类名：使用大驼峰命名法
   - 方法名：使用小驼峰命名法
   - 变量名：使用小驼峰命名法
   - 视图名：使用小写下划线

2. 方法定义
   - 访问修饰符
   - 返回类型声明
   - 参数类型声明
   - 方法注释

3. 错误处理
   - 使用业务异常
   - 统一错误码
   - 友好错误信息
   - 日志记录

4. 视图处理
   - 视图数据传递
   - 布局模板使用
   - 局部视图复用
   - 视图缓存

## 最佳实践

1. 视图数据处理
```php
public function show(int $id): View
{
    // 获取主数据
    $item = $this->service->findById($id);
    if (!$item) {
        BizException::throws(YourModuleErrorCode::NOT_FOUND);
    }
    
    // 获取关联数据
    $comments = $this->service->getComments($id);
    $relatedItems = $this->service->getRelatedItems($id);
    
    // 获取用户状态
    $userId = $this->getCurrentUserId();
    $userStatus = [
        'liked' => $this->service->hasLiked($id, $userId),
        'favorited' => $this->service->hasFavorited($id, $userId),
        'followed' => $this->service->hasFollowed($item->user_id, $userId)
    ];
    
    // 增加统计
    $this->service->incrementViews($id);
    
    // 返回视图
    return view('yourmodule::show', compact(
        'item',
        'comments',
        'relatedItems',
        'userStatus'
    ));
}
```

2. 权限控制
```php
public function edit(int $id): View
{
    // 获取记录
    $item = $this->service->findById($id);
    if (!$item) {
        BizException::throws(YourModuleErrorCode::NOT_FOUND);
    }
    
    // 基础权限
    if (!$this->getCurrentUser()->can('yourmodule.edit')) {
        BizException::throws(YourModuleErrorCode::PERMISSION_DENIED);
    }
    
    // 数据权限
    if ($item->user_id !== $this->getCurrentUserId() && 
        !$this->getCurrentUser()->isAdmin()
    ) {
        BizException::throws(YourModuleErrorCode::PERMISSION_DENIED);
    }
    
    // 获取关联数据
    $categories = $this->service->getCategories();
    $tags = $this->service->getTags();
    
    // 返回视图
    return view('yourmodule::edit', compact(
        'item',
        'categories',
        'tags'
    ));
}
```

3. 文件上传处理
```php
public function store(CreateRequest $request): array
{
    // 处理图片上传
    $images = [];
    if ($request->hasFile('images')) {
        foreach ($request->file('images') as $image) {
            $path = $image->store('public/images');
            $images[] = [
                'path' => $path,
                'name' => $image->getClientOriginalName(),
                'size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
            ];
        }
    }
    
    // 创建记录
    $item = $this->service->create([
        ...$request->validated(),
        'images' => $images,
        'user_id' => $this->getCurrentUserId(),
    ]);
    
    return [
        'message' => '创建成功',
        'redirect' => route('yourmodule.show', ['id' => $item->id])
    ];
}
```

## 注意事项

1. 控制器职责
   - 处理HTTP请求
   - 调用服务层方法
   - 返回视图或JSON
   - 处理用户输入

2. 安全考虑
   - CSRF防护
   - XSS过滤
   - SQL注入防护
   - 文件上传验证

3. 性能优化
   - 视图缓存
   - 数据缓存
   - 延迟加载
   - N+1问题处理

4. 代码质量
   - 单一职责
   - 代码复用
   - 测试覆盖
   - 注释完整
