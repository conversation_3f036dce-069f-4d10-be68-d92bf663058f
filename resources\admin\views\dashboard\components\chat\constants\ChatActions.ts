/**
 * 聊天机器人统一Action定义 (前端版本)
 * 
 * 这个文件与后端 Modules/Ai/Config/ChatActions.php 保持同步
 * 确保前后端action名称完全一致
 */

export const ChatActions = {
  // ==================== 基础操作 ====================
  
  /** 初始化聊天 */
  INIT_CHAT: 'init_chat',
  
  /** 文本消息 */
  TEXT_MESSAGE: 'text_message',
  
  /** 快捷回复 */
  QUICK_REPLY: 'quick_reply',
  
  // ==================== 工具激活操作 ====================
  
  /** 激活文本替换工具 */
  SELECT_TEXT_REPLACER: 'text_replacer',
  
  /** 激活SEO分析工具 */
  SELECT_SEO_ANALYZER: 'seo_analyzer',
  
  /** 激活文章替换工具 */
  SELECT_ARTICLE_REPLACER: 'article_replacer',
  
  // ==================== 文章替换工具专用操作 ====================
  
  /** 快速替换 */
  QUICK_REPLACE: 'QUICK_REPLACE',
  
  /** 选择文章 */
  SELECT_ARTICLE: 'SELECT_ARTICLE',
  
  /** 选择全部文章 */
  SELECT_ALL_ARTICLES: 'SELECT_ALL_ARTICLES',
  
  /** 预览结果 */
  PREVIEW_RESULTS: 'PREVIEW_RESULTS',
  
  /** 确认执行 */
  CONFIRM_EXECUTE: 'CONFIRM_EXECUTE',
  
  /** 执行替换 */
  EXECUTE_REPLACEMENT: 'EXECUTE_REPLACEMENT',
  
  /** 选择模块 */
  SELECT_MODULE: 'SELECT_MODULE',
  
  /** 取消操作 */
  CANCEL_OPERATION: 'CANCEL_OPERATION',
  
  /** 新的替换操作 */
  NEW_REPLACEMENT: 'NEW_REPLACEMENT',
  
  /** 返回主菜单 */
  BACK_TO_MAIN: 'BACK_TO_MAIN',
  
  /** 重试搜索 */
  RETRY_SEARCH: 'RETRY_SEARCH',
  
  /** 查看详情 */
  VIEW_DETAILS: 'VIEW_DETAILS',
  
  /** 批量替换所有文章 */
  BATCH_REPLACE_ALL: 'BATCH_REPLACE_ALL',
  
  /** 选择单独文章进行替换 */
  SELECT_INDIVIDUAL_ARTICLES: 'SELECT_INDIVIDUAL_ARTICLES',
  
  /** 返回搜索步骤 */
  BACK_TO_SEARCH: 'BACK_TO_SEARCH',
  
  /** 显示更多文章 */
  SHOW_MORE_ARTICLES: 'SHOW_MORE_ARTICLES',
  
  /** 搜索特定文章 */
  SEARCH_SPECIFIC_ARTICLE: 'SEARCH_SPECIFIC_ARTICLE',
  
  /** 返回文章列表 */
  BACK_TO_ARTICLE_LIST: 'BACK_TO_ARTICLE_LIST',
  
  /** 执行搜索 */
  EXECUTE_SEARCH: 'EXECUTE_SEARCH',
  
  /** 取消搜索 */
  CANCEL_SEARCH: 'CANCEL_SEARCH',
  
  // ==================== 通用工具操作 ====================
  
  /** 显示帮助 */
  SHOW_HELP: 'SHOW_HELP',
  
  /** 显示示例 */
  SHOW_EXAMPLES: 'SHOW_EXAMPLES',
  
  /** 退出工具 */
  EXIT_TOOL: 'EXIT_TOOL',
  
  /** 重试操作 */
  RETRY: 'RETRY'
} as const

// ==================== 工具映射 ====================

/**
 * action到工具名称的映射
 */
export const TOOL_MAPPING = {
  [ChatActions.SELECT_TEXT_REPLACER]: 'text_replacer',
  [ChatActions.SELECT_SEO_ANALYZER]: 'seo_analysis',
  [ChatActions.SELECT_ARTICLE_REPLACER]: 'article_content_replacer'
} as const

/**
 * 快捷操作action列表
 */
export const QUICK_ACTIONS = [
  ChatActions.SELECT_TEXT_REPLACER,
  ChatActions.SELECT_SEO_ANALYZER,
  ChatActions.SELECT_ARTICLE_REPLACER
] as const

// ==================== 辅助函数 ====================

/**
 * 检查是否是快捷操作
 */
export function isQuickAction(action: string): boolean {
  return QUICK_ACTIONS.includes(action as any)
}

/**
 * 获取action对应的工具名称
 */
export function getToolName(action: string): string | null {
  return TOOL_MAPPING[action as keyof typeof TOOL_MAPPING] || null
}

/**
 * 获取所有工具激活action
 */
export function getToolActivationActions(): string[] {
  return [
    ChatActions.SELECT_TEXT_REPLACER,
    ChatActions.SELECT_SEO_ANALYZER,
    ChatActions.SELECT_ARTICLE_REPLACER
  ]
}

/**
 * 获取文章替换工具的所有action
 */
export function getArticleReplacerActions(): string[] {
  return [
    ChatActions.QUICK_REPLACE,
    ChatActions.SELECT_ARTICLE,
    ChatActions.SELECT_ALL_ARTICLES,
    ChatActions.PREVIEW_RESULTS,
    ChatActions.CONFIRM_EXECUTE,
    ChatActions.EXECUTE_REPLACEMENT,
    ChatActions.SELECT_MODULE,
    ChatActions.CANCEL_OPERATION,
    ChatActions.NEW_REPLACEMENT,
    ChatActions.BACK_TO_MAIN,
    ChatActions.RETRY_SEARCH,
    ChatActions.VIEW_DETAILS,
    ChatActions.SHOW_HELP,
    ChatActions.SHOW_EXAMPLES,
    ChatActions.EXIT_TOOL,
    ChatActions.RETRY
  ]
}

// ==================== 类型定义 ====================

export type ChatActionType = typeof ChatActions[keyof typeof ChatActions]
export type ToolActivationAction = typeof QUICK_ACTIONS[number]
export type ArticleReplacerAction = ReturnType<typeof getArticleReplacerActions>[number] 