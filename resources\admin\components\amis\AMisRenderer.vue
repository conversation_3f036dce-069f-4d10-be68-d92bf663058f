<template>
    <div class="por" ref="el"></div>
</template>

<script setup lang="ts">
import axios from 'axios'
import { env, getAuthToken } from '/admin/support/helper'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const { t } = useI18n()

//@ts-ignore
const amis = window.amisRequire('amis/embed')
const el = ref(null)
const baseURL = env('VITE_BASE_URL')
const props = defineProps<{
    amisJson: object;
}>()

const router = useRouter()
const route = useRoute()

let amisScoped

// 调整 watch 逻辑，防止不必要的重构
watch(
    () => props.amisJson,
    (newJson, oldJson) => {
        if (JSON.stringify(newJson) !== JSON.stringify(oldJson)) {
            build()
        } else {
            console.log('AMisRenderer: amisJson did not change, skipping build.')
        }
    },
    { deep: true },
)

const build = () => {

    amisScoped = amis.embed(
        el.value,
        props.amisJson,
        {},
        {
            fetcher: ({
                          url, // 接口地址
                          method, // 请求方法 get、post、put、delete
                          data, // 请求数据
                          responseType,
                          config, // 其他配置
                          headers, // 请求头
                      }: any) => {

                url = baseURL + url
                config = config || {}
                responseType && (config.responseType = responseType)

                if (config.cancelExecutor) {
                    config.cancelToken = new (axios as any).CancelToken(
                        config.cancelExecutor,
                    )
                }

                config.headers = headers || {}
                const token = getAuthToken() // 假设这是获取 TOKEN 的函数
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`
                }

                //@ts-ignore
                config.headers['X-CSRF-TOKEN'] = document.head
                    .querySelector('meta[name="csrf-token"]')
                    ?.getAttribute('content')

                if (method !== 'post' && method !== 'put' && method !== 'patch') {
                    if (data) {
                        config.params = data
                    }
                    return (axios as any)[method](url, config)
                } else if (data && data instanceof FormData) {
                    config.headers = config.headers || {}
                    config.headers['Content-Type'] = 'multipart/form-data'
                } else if (
                    data &&
                    typeof data !== 'string' &&
                    !(data instanceof Blob) &&
                    !(data instanceof ArrayBuffer)
                ) {
                    data = JSON.stringify(data)
                    config.headers = config.headers || {}
                    config.headers['Content-Type'] = 'application/json'
                }
                return (axios as any)[method](url, data, config)
            },
            jumpTo: (to: string) => {
                if (to == 'back()') {
                    router.back()
                    return
                }
                if (to.startsWith('http://') || to.startsWith('https://')) {
                    window.location.href = to
                    return
                }
                router.push(to)
            },
            updateLocation: (to: string, replace: boolean) => {
                const queryArr = to.split('&').filter((item) => {
                    return item.indexOf('search[') === -1
                })
                let query = queryArr.join('&')
                if (query.indexOf('?') !== 0) {
                    query = '?' + query
                }
                const path = route.path + query
                if (replace) {
                    router.replace(path)
                } else {
                    router.push(path)
                }
            },
            notify: (type: any, msg: string) => {
                let showType: 'message' | 'alert' | 'notice'
                let showContent: string
                try {
                    const msgData = JSON.parse(msg)
                    showType = msgData.type
                    showContent = msgData.content
                } catch (e) {
                    showType = 'message'
                    showContent = msg
                }
                if (showType == 'alert') {
                    ElMessageBox({
                        message: showContent,
                        title: t('system.prompt'),
                        type: type,
                        center: true,
                    })
                } else if (showType == 'message') {
                    ElMessage({
                        message: showContent,
                        type: type,
                    })
                } else {
                    ElNotification({
                        message: showContent,
                        dangerouslyUseHTMLString: true,
                        type: type,
                    })
                }
            },
            alert: (content: string) => {
                ElMessage(content)
            },
            confirm: (content: string) => {
                return ElMessageBox.confirm(content, t('system.prompt'), {
                    cancelButtonText: t('system.cancel'),
                    confirmButtonText: t('system.confirm'),
                    dangerouslyUseHTMLString: true,
                })
                    .then(() => {
                        return true
                    })
                    .catch(() => {
                        return false
                    })
            },
            affixOffsetTop: 48,
        },
    )
}

onMounted(() => {
    build()
})
</script>
