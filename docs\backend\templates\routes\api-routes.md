# API 路由模板

## 概述

API 路由用于定义模块的 API 接口路由。本文档提供了 API 路由的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\YourModule\Api\Controllers\YourController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| 模块的 API 路由定义
|
*/

Route::prefix('api/v1')->middleware(['api', 'auth:api'])->group(function () {
    // 资源路由
    Route::apiResource('resources', YourController::class);
    
    // 自定义路由
    Route::prefix('custom')->group(function () {
        Route::get('list', [YourController::class, 'customList']);
        Route::post('action', [YourController::class, 'customAction']);
    });
    
    // 批量操作路由
    Route::prefix('batch')->group(function () {
        Route::post('create', [YourController::class, 'batchCreate']);
        Route::post('update', [YourController::class, 'batchUpdate']);
        Route::post('delete', [YourController::class, 'batchDelete']);
    });
    
    // 导入导出路由
    Route::prefix('export')->group(function () {
        Route::get('download', [YourController::class, 'export']);
        Route::post('import', [YourController::class, 'import']);
    });
});
```

## 规范要求

1. 路由分组
   - 使用版本前缀
   - 使用适当的中间件
   - 按功能分组路由
   - 使用资源路由

2. 路由命名
   - 使用小写字母
   - 使用连字符分隔
   - 动词在前，名词在后
   - 保持命名一致性

3. 中间件使用
   - 认证中间件
   - 权限中间件
   - 限流中间件
   - 跨域中间件

4. 版本控制
   - 使用 URL 版本控制
   - 维护向后兼容性
   - 版本号使用 v1、v2 等
   - 在路由层处理版本差异

## 最佳实践

1. 资源路由定义
```php
// 完整资源路由
Route::apiResource('posts', PostController::class);

// 部分资源路由
Route::apiResource('comments', CommentController::class)
    ->only(['index', 'store', 'destroy']);

// 嵌套资源路由
Route::apiResource('posts.comments', PostCommentController::class)
    ->shallow();
```

2. 自定义动作
```php
// 在资源路由上添加自定义动作
Route::apiResource('posts', PostController::class);
Route::post('posts/{post}/publish', [PostController::class, 'publish']);
Route::post('posts/{post}/unpublish', [PostController::class, 'unpublish']);
```

3. 路由参数
```php
// 必选参数
Route::get('posts/{post}', [PostController::class, 'show']);

// 可选参数
Route::get('posts/{post?}', [PostController::class, 'show']);

// 正则约束
Route::get('posts/{post}', [PostController::class, 'show'])
    ->where('post', '[0-9]+');
```

## 常见问题

1. 路由分组
```php
// 按模块分组
Route::prefix('user')->group(function () {
    Route::get('profile', [UserController::class, 'profile']);
    Route::put('profile', [UserController::class, 'updateProfile']);
});

// 按权限分组
Route::middleware(['auth:api', 'role:admin'])->group(function () {
    Route::apiResource('users', UserController::class);
});
```

2. 路由缓存
```php
// 生成路由缓存
php artisan route:cache

// 清除路由缓存
php artisan route:clear
```

## 注意事项

1. 避免路由冲突
2. 合理使用路由缓存
3. 注意路由参数安全
4. 保持 API 版本兼容
5. 使用适当的 HTTP 方法
6. 实现必要的路由中间件
