<?php

namespace Modules\Common\Util;

use Bingo\Core\Input\Request;
use Bingo\Core\Util\EnvUtil;
use Bingo\Core\Util\RandomUtil;
use Bingo\Core\Util\SerializeUtil;

class NoneLoginOperateUtil
{
    public static function generateUrl($url, $param = [], $domainUrl = null): string
    {
        if (null === $domainUrl) {
            $domainUrl = Request::domainUrl();
        }
        $urlParam = [];
        $urlParam['timestamp'] = time();
        $urlParam['nonce'] = RandomUtil::string(10);
        $urlParam['param'] = SerializeUtil::jsonEncode($param);
        $urlParam['sign'] = self::sign($url, $urlParam['nonce'], $urlParam['timestamp'], $urlParam['param']);
        return $domainUrl.bingostart_web_url($url, $urlParam);
    }

    public static function sign($url, $nonce, $timestamp, $param)
    {
        $securityKey = EnvUtil::securityKey();
        return md5($url.':'.$securityKey.':'.$nonce.':'.$timestamp.':'.$param);
    }
}
