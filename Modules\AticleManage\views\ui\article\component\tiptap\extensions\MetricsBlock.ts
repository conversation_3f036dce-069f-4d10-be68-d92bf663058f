import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

export interface MetricsOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    metrics: {
      /**
       * 设置Metrics组件
       */
      setMetrics: (options?: { values?: string[], labels?: string[], textColor?: string, headingStyle?: string }) => ReturnType,
      /**
       * 更新Metrics组件的HTML内容
       */
      updateMetricsHTML: (html: string) => ReturnType,
    }
  }
}

export const MetricsBlock = Node.create<MetricsOptions>({
  name: 'metricsBlock',
  
  group: 'block',
  
  content: '',
  
  marks: '',
  
  draggable: true,
  
  selectable: true,
  
  inline: false,
  
  addAttributes() {
    return {
      values: {
        default: ['15k+', '15k+', '15k+'],
        parseHTML: element => {
          const values: string[] = []
          element.querySelectorAll('.metrics-value').forEach(el => {
            values.push(el.textContent || '')
          })
          return values.length ? values : ['15k+', '15k+', '15k+']
        },
      },
      labels: {
        default: ['Customers of Elevate', 'Customers of Elevate', 'Customers of Elevate'],
        parseHTML: element => {
          const labels: string[] = []
          element.querySelectorAll('.metrics-label').forEach(el => {
            labels.push(el.textContent || '')
          })
          return labels.length ? labels : ['Customers of Elevate', 'Customers of Elevate', 'Customers of Elevate']
        },
      },
      textColor: {
        default: 'primary',
        parseHTML: element => {
          const valueEl = element.querySelector('.metrics-value')
          if (valueEl) {
            const classList = Array.from(valueEl.classList)
            const colorClass = classList.find(cls => cls.startsWith('text-'))
            if (colorClass) {
              return colorClass.replace('text-', '')
            }
          }
          return 'primary'
        },
      },
      headingStyle: {
        default: 'display-4',
        parseHTML: element => {
          const valueEl = element.querySelector('.metrics-value')
          if (valueEl) {
            const classList = Array.from(valueEl.classList)
            const styleClass = classList.find(cls => cls.startsWith('display-') || /^h[1-6]$/.test(cls))
            if (styleClass) {
              return styleClass
            }
          }
          return 'display-4'
        },
      },
      // 存储原始HTML
      rawHTML: {
        default: null,
        parseHTML: () => null,
      }
    }
  },
  
  parseHTML() {
    return [{
      tag: 'div[data-bs-component="metrics"]',
    }]
  },
  
  renderHTML({ HTMLAttributes }) {
    // 如果有原始HTML，直接使用它
    if (HTMLAttributes.rawHTML) {
      const container = document.createElement('div')
      container.innerHTML = HTMLAttributes.rawHTML
      return container.firstChild || createDefaultHTML(HTMLAttributes)
    }
    
    return createDefaultHTML(HTMLAttributes)
  },
  
  addCommands() {
    return {
      setMetrics: (options = {}) => ({ commands }) => {
        return commands.setNode(this.name, options)
      },
      updateMetricsHTML: (html) => ({ commands }) => {
        return commands.updateAttributes(this.name, {
          rawHTML: html
        })
      },
    }
  },
  

})

// 辅助函数：创建默认HTML
function createDefaultHTML(attrs: any) {
  const { values, labels, textColor, headingStyle } = attrs
  
  const itemsHtml = values.map((value: string, index: number) => {
    const label = labels[index] || 'Metric'
    return `
      <div class="col-12 col-md-4">
        <div class="metrics-item">
          <h2 class="metrics-value ${headingStyle} fw-bold text-${textColor}">${value}</h2>
          <p class="mb-0 metrics-label">${label}</p>
        </div>
      </div>
    `
  }).join('')
  
  return `
    <div class="container py-5" data-bs-component="metrics">
      <div class="text-center row justify-content-center">
        <div class="col-12">
          <div class="row g-4">
            ${itemsHtml}
          </div>
        </div>
      </div>
    </div>
  `
}

export default MetricsBlock 