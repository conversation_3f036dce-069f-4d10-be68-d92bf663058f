<template>
  <div class="edit-section">
    <el-empty description="选择组件中的元素以编辑其内容" :image-size="100" />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 为了解决TypeScript类型问题，添加默认导出
defineOptions({
  name: 'DefaultEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
  min-height: 150px;
}
</style> 