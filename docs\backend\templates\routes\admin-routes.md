# Admin 路由模板

## 概述

Admin 路由用于定义模块的管理后台路由。本文档提供了 Admin 路由的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\YourModule\Admin\Controllers\YourController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| 模块的管理后台路由定义
|
*/

Route::prefix('admin/your-module')->group(function () {
    // 列表页面路由
    Route::get('/', [YourController::class, 'index'])->name('admin.your-module.index');
    
    // 详情页面路由
    Route::get('/{id}', [YourController::class, 'show'])->name('admin.your-module.show');
    
    // 表单页面路由
    Route::get('/create', [YourController::class, 'create'])->name('admin.your-module.create');
    Route::get('/{id}/edit', [YourController::class, 'edit'])->name('admin.your-module.edit');
    
    // 数据处理路由
    Route::post('/', [YourController::class, 'store'])->name('admin.your-module.store');
    Route::put('/{id}', [YourController::class, 'update'])->name('admin.your-module.update');
    Route::delete('/{id}', [YourController::class, 'destroy'])->name('admin.your-module.destroy');
    
    // 批量操作路由
    Route::prefix('batch')->group(function () {
        Route::post('delete', [YourController::class, 'batchDelete'])->name('admin.your-module.batch.delete');
        Route::post('update', [YourController::class, 'batchUpdate'])->name('admin.your-module.batch.update');
        Route::post('export', [YourController::class, 'batchExport'])->name('admin.your-module.batch.export');
    });
    
    // 导入导出路由
    Route::prefix('data')->group(function () {
        Route::get('export', [YourController::class, 'export'])->name('admin.your-module.export');
        Route::post('import', [YourController::class, 'import'])->name('admin.your-module.import');
        Route::get('template', [YourController::class, 'template'])->name('admin.your-module.template');
    });
});
```

## 规范要求

1. 路由分组
   - 使用 admin 前缀
   - 使用管理员中间件
   - 按功能分组路由
   - 使用资源路由

2. 路由命名
   - 使用 admin 前缀
   - 使用模块名称
   - 使用连字符分隔
   - 保持命名一致性

3. 中间件��用
   - Admin 中间件
   - 管理员认证
   - 权限验证
   - CSRF 保护

4. 功能实现
   - 列表页面
   - 详情页面
   - 表单操作
   - 批量处理

## 最佳实践

1. 资源路由定义
```php
// 完整资源路由
Route::resource('users', UserController::class)
    ->names('admin.users');

// 部分资源路由
Route::resource('roles', RoleController::class)
    ->only(['index', 'store', 'destroy'])
    ->names('admin.roles');

// 嵌套资源路由
Route::resource('users.roles', UserRoleController::class)
    ->shallow()
    ->names('admin.users.roles');
```

2. 权限控制
```php
// 使用权限中间件
Route::middleware(['permission:manage-users'])->group(function () {
    Route::resource('users', UserController::class);
});

// 使用角色中间件
Route::middleware(['role:admin'])->group(function () {
    Route::resource('settings', SettingController::class);
});
```

## 常见问题

1. 路由组织
```php
// 好的实践 - 按模块组织
Route::prefix('admin')->group(function () {
    // 用户管理
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index']);
        Route::resource('roles', RoleController::class);
    });
    
    // 系统设置
    Route::prefix('settings')->group(function () {
        Route::get('/', [SettingController::class, 'index']);
        Route::resource('options', OptionController::class);
    });
});

// 不好的实践 - 路由分散
Route::get('admin/users', [UserController::class, 'index']);
Route::resource('admin/roles', RoleController::class);
Route::get('admin/settings', [SettingController::class, 'index']);
Route::resource('admin/options', OptionController::class);
```

2. 批量操作
```php
// 好的实践 - 使用专门的批量操作路由
Route::prefix('admin/users/batch')->group(function () {
    Route::post('delete', [UserController::class, 'batchDelete']);
    Route::post('update', [UserController::class, 'batchUpdate']);
});

// 不好的实践 - 混合在资源路由中
Route::resource('admin/users', UserController::class);
Route::post('admin/users/delete-many', [UserController::class, 'deleteMany']);
```

## 注意事项

1. 严格的权限控制
2. 合理的路由分组
3. 统一的命名规范
4. 完善的批量操作
5. 适当的路由缓存
6. 必要的参数验证
