import { createApp } from 'vue'
import VueSafeHTML from 'vue-safe-html'
import App from '@/frontend/App.vue'
// @ts-ignore
import router from '@/frontend/router'
// @ts-ignore
import pinia from '@/frontend/store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import '@/frontend/styles/index.scss'
import { bootstrapI18n } from './i18n'

const app = createApp(App)

// 初始化 i18n
bootstrapI18n(app)

// 修改全局属性，使用 import.meta.env 获取环境变量
app.config.globalProperties.$asset = (path: string) => {
  const baseURL = import.meta.env.VITE_URL || ''
  return `${baseURL}/Vendor/${path}`
}

app.use(router)
app.use(ElementPlus)
app.use(VueSafeHTML)
app.use(pinia)
app.mount('#app')
