import { createApp } from 'vue'
import type { App as app } from 'vue'
import App from '/admin/App.vue'
import router, { bootstrapRouter } from '/admin/router'
import ElementPlus from 'element-plus'
import zh from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'

import { bootstrapStore } from '/admin/stores'
import Cache from './cache'
import { bootstrapI18n } from '/admin/i18n'
import guard from '/admin/router/guard'
import { bootstrapDirectives } from '/admin/directives'
import { Language } from 'element-plus/es/locale'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import { library } from '@fortawesome/fontawesome-svg-core'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'
import { fab } from '@fortawesome/free-brands-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

import '/admin/styles/index.scss'
import { initStagewise } from '/admin/utils/stagewise'

import 'amis/sdk/sdk.js'
// import 'https://registry.npmmirror.com/amis/6.7.0/files/sdk/sdk.js' //第三方CDN TODO 更换成Bingo CDN
import 'amis/lib/themes/default.css'
import 'amis/sdk/rest.js'
import 'amis/sdk/tinymce.js'
import 'amis/sdk/color-picker.js'
import 'amis/sdk/exceljs.js'
import 'amis/sdk/rich-text.js'
import 'amis/sdk/iconfont.css'
// import '@fortawesome/fontawesome-free/css/all.css'
import svgIcon from '../components/svgicon/icon'
import FilterPopover from '../components/popover/index.vue'

const amisLib = window.amisRequire('amis')
const baseURL = import.meta.env.VITE_URL

// TODO: 等待接口数据
Object.defineProperty(window, 'OVERRIDE_API_KEY', {
  value: '9Vu7G93y',
  writable: true,
  enumerable: true,
  configurable: true,
})
Object.defineProperty(window, 'VITE_TINYMCE_KEY', {
  value: 'i2yrmenwvyiiyi0h3avugn9fqfk4zlrw0509k8z0j50fn331',
  writable: true,
  enumerable: true,
  configurable: true,
})

/**
 * BingoStart
 */
export default class BingoStart {
  protected app: app

  protected element: string

  /**
   * construct
   *
   * @param ele
   */
  constructor(ele: string = '#app') {
    this.app = createApp(App)
    this.element = ele
  }

  // @ts-ignore
  registerCustomComponents(): BingoStart {
    amisLib.Renderer({
      test: /(^|\/)custom-svg-icon/,
      name: 'custom-svg-icon',
      autoVar: true,
    })(svgIcon)

    // 注册 FilterPopover 组件
    this.app.component('FilterPopover', FilterPopover)

    return this
  }

  /**
   *
   * admin boot
   */
  bootstrap(): void {
    this.useElementPlus().usePinia().useI18n().installDirectives().useRouter().useIconsVue().useFontAwesone().registerCustomComponents().registerGlobalProperties().mount()
    
    // 初始化 Stagewise 开发工具（仅开发环境）
    this.initStagewise()
  }

  /**
   * 挂载节点
   *
   * @returns
   */
  protected mount(): void {
    this.app.mount(this.element)
  }

  /**
   * 加载路由
   *
   * @returns
   */
  protected useRouter(): BingoStart {
    guard(router)

    bootstrapRouter(this.app)

    return this
  }

  protected useIconsVue(): BingoStart {
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      this.app.component(key, component)
    }

    return this
  }

  /**
   * ui
   *
   * @returns
   */
  protected useElementPlus(): BingoStart {
    const languages: Record<string, Language> = {
      zh,
      en,
    }

    const language = Cache.get('language') || 'zh'
    this.app.use(ElementPlus, {
      locale: languages[language],
    })
    return this
  }

  /**
   * font awesone
   *
   * @returns
   */
  protected useFontAwesone(): BingoStart {
    library.add(fas, far, fab)
    this.app.component('font-awesome-icon', FontAwesomeIcon)

    return this
  }

  /**
   * use pinia
   */
  protected usePinia(): BingoStart {
    bootstrapStore(this.app)

    return this
  }

  /**
   * use i18n
   */
  protected useI18n(): BingoStart {
    bootstrapI18n(this.app)

    return this
  }

  /**
   * install directives
   *
   * @protected
   */
  protected installDirectives(): BingoStart {
    bootstrapDirectives(this.app)

    return this
  }

  /**
   * 注册全局属性
   * @protected
   */
  protected registerGlobalProperties(): BingoStart {
    this.app.config.globalProperties.$asset = (path: string) => {
      return `${baseURL}/Vendor/${path}`
    }
    return this
  }

  /**
   * 初始化 Stagewise 开发工具
   * @protected
   */
  protected initStagewise(): void {
    // 延迟初始化，确保 DOM 已准备就绪
    setTimeout(() => {
      initStagewise()
    }, 100)
  }
}
