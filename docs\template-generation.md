# 动态模板生成与项目规范指南

## 目录
- [概述](#概述)
- [模块结构](#模块结构)
- [路由规范](#路由规范)
- [代码模板](#代码模板)
- [权限规则](#权限规则)
- [多语言规则](#多语言规则)
- [特殊处理规则](#特殊处理规则)
- [最佳实践](#最佳实践)

## 概述

本文档旨在规范项目中的代码模板生成和项目特定规则，确保团队成员能够快速创建符合项目规范的代码结构。基于实践经验，我们总结了以下规范和模板。

## 模块结构

```php
modules/
└── YourModule/                    # 模块根目录
    ├── Api/                       # API 接口层
    │   └── Controller/           # API 控制器
    │   └── route.php          # 管理路由定义
    ├── Web/                      # Web 接口层
    │   ├── Controllers/         # Web 控制器
    │   └── route.php           # Web 路由定义
    ├── Admin/                    # 管理后台
    │   ├── Controllers/        # 管理控制器
    │   └── route.php          # 管理路由定义
    ├── OpenApi/                  # 开放 API 接口层
    │   ├── Controllers/        # 开放 API 控制器
    │   └── route.php          # 开放 API 路由定义
    ├── Domain/                   # 领域层
    │   ├── Entities/          # 领域实体
    │   ├── Interfaces/        # 接口定义
    │   ├── Repositories/      # 仓储实现
    │   ├── Services/          # 领域服务
    │   └── Middleware/        # 中间件
    ├── Models/                   # 数据模型
    ├── Services/                 # 应用服务
    ├── Database/                 # 数据库相关
    │   ├── Migrations/        # 数据库迁移
    │   └── Seeders/          # 数据填充
    ├── Resources/               # 资源文件
    │   ├── assets/           # 前端资源
    │   └── views/            # 视图文件
    ├── Tests/                   # 测试用例
    ├── Lang/                    # 多语言文件
    ├── config.json              # 模块配置
    └── Asset/                   # 前端资源
```

### 模块配置规范

```json
{
    "name": "YourModule",
    "title": "模块标题",
    "version": "1.0.0",
    "description": "模块描述",
    "keywords": ["keyword1", "keyword2"],
    "type": "module",
    "require": {
        "Core": ">=1.0"
    },
    "providers": [
        "Modules\\YourModule\\Providers\\YourModuleServiceProvider"
    ]
}
```

## 路由规范

1. 路由前缀规范
```php
Route::prefix('module-name')->group(function () {
    // 模块路由
});
```

2. 路由分组规范
```php
Route::group(['prefix' => 'resource-name'], function () {
    // 资源相关路由
}); 
```

3. 路由命名规范
```php
Route::get('users', [UserController::class, 'index'])->name('module.resource.action');
```

4. RESTful API路由规范
```php
Route::apiResource('resources', ResourceController::class);
```

5. 中间件使用规范
```php
// API认证中间件
Route::middleware('auth:api')->group(function () {
    // 需要认证的路由
});

// Admin认证中间件 
Route::middleware('auth:admin')->group(function () {
    // 需要管理员认证的路由
});
```

## 代码模板

### 1. 控制器模板

#### 1.1 API控制器
```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Api\Controllers;

use Bingo\Module\ApiBaseController;
use Bingo\Core\Input\InputPackage;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

final class YourController extends ApiBaseController 
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    public function index(): array
    {
        $input = InputPackage::buildFromInput();
        // API业务逻辑实现
    }
}
```

#### 1.2 Admin控制器
```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Admin\Controllers;

use Bingo\Module\AdminBaseController;
use Bingo\Core\Input\InputPackage;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

final class YourController extends AdminBaseController
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    public function index(): array
    {
        $input = InputPackage::buildFromInput();
        // 管理后台业务逻辑实现
    }
}
```

#### 1.3 Web控制器
```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Web\Controllers;

use Bingo\Module\WebBaseController;
use Illuminate\View\View;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

final class YourController extends WebBaseController
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    public function index(): View
    {
        // Web页面逻辑实现
        return view('yourmodule::index');
    }
}
```

#### 1.4 OpenApi控制器
```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\OpenApi\Controllers;

use Bingo\Module\OpenApiBaseController;
use Bingo\Core\Input\InputPackage;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

final class YourController extends OpenApiBaseController
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    public function index(): array
    {
        $input = InputPackage::buildFromInput();
        // 开放API业务逻辑实现
    }
}
```

### 2. 服务模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Services;

use Bingo\Exceptions\BizException;
use Modules\YourModule\Domain\Repositories\YourRepositoryInterface;

final class YourService
{
    public function __construct(
        private readonly YourRepositoryInterface $repository
    ) {
    }

    public function getList(array $params): array
    {
        try {
            return $this->repository->getList($params);
        } catch (\Exception $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }
    }
}
```

### 3. 仓储接口模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Interfaces;

interface YourRepositoryInterface
{
    public function getList(array $params): array;
    public function findById(int $id): ?array;
    public function create(array $data): array;
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
}
```

### 4. 仓储实现模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Repositories;

use Modules\YourModule\Domain\Interfaces\YourRepositoryInterface;
use Modules\YourModule\Models\YourModel;

final class YourRepository implements YourRepositoryInterface
{
    public function getList(array $params): array
    {
        $query = YourModel::query();
        
        if (isset($params['keyword'])) {
            $query->where('title', 'like', '%'.$params['keyword'].'%');
        }
        
        return $query->get()->toArray();
    }
}
```

### 5. 模型模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Models;

use Bingo\Base\BingoModel as Model;

class YourModel extends Model
{
    protected $table = 'your_table';

    protected $fillable = [
        'id',
        'title',
        'description',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
}
```

### 6. 错误码模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Enums;

use Bingo\Enums\Traits\EnumEnhance;

enum YourModuleErrorCode: int
{
    use EnumEnhance;

    /**
     * 错误码命名规范:
     * 1. 使用大写字母和下划线
     * 2. 前缀为模块业务名称
     * 3. 后缀为具体错误描述
     * 
     * 错误码数值规范:
     * 1. 6位整数
     * 2. 前2位为模块编号(10-99)
     * 3. 后4位为错误编号(0001-9999)
     * 例如: 140001 表示 Course 模块的第1个错误
     */

    // 通用错误 (0001-0999)
    case NOT_FOUND = 140001;
    case INVALID_PARAMS = 140002;
    case PERMISSION_DENIED = 140003;
    
    // 业务错误 (1000-9999,按业务分段)
    // 课程相关 (1000-1999)
    case COURSE_NOT_FOUND = 141001;
    case COURSE_ALREADY_EXISTS = 141002;
    case COURSE_STATUS_INVALID = 141003;
    
    // 章节相关 (2000-2999) 
    case CHAPTER_NOT_FOUND = 142001;
    case CHAPTER_ORDER_INVALID = 142002;
    
    // 学习记录相关 (3000-3999)
    case STUDY_RECORD_NOT_FOUND = 143001;
    case STUDY_PROGRESS_INVALID = 143002;
}
```

错误码使用规范:

1. 每个模块定义自己的ErrorCode枚举类

2. 错误码必须是6位整数，格式为: MMNNNN
   - MM: 2位模块编号(10-99)
   - NNNN: 4位错误编号(0001-9999)

3. 模块编号(MM)规则:
   - 新模块开发时默认使用18作为临时模块编号
   - 正式模块编号必须在飞书文档《模块编号登记表》进行登记
   - 按登记顺序分配正式模块编号
   - 分配到正式编号后,需要更新模块的错误码

示例:
```php
// 开发阶段使用18临时编号
enum YourModuleErrorCode: int 
{
    case NOT_FOUND = 180001;  // 18是临时模块编号
}

// 在飞书完成登记后,假设分配到编号14
enum YourModuleErrorCode: int
{
    case NOT_FOUND = 140001;  // 14是正式模块编号
}
```

4. 错误编号(NNNN)分段建议:
   - 0001-0999: 通用错误
   - 1000-9999: 业务错误,每类业务预留1000个错误码

5. 在代码中使用:
```php
// 抛出业务异常
BizException::throws(YourModuleErrorCode::NOT_FOUND);
```

6. 错误信息配置在语言文件中:
```php
// lang/zh_CN/error.php
return [
    YourModuleErrorCode::NOT_FOUND->value => '数据不存在'
];
```

注意事项:
1. 新建模块时先使用18作为临时模块编号
2. 模块开发完成前必须在飞书文档登记获取正式模块编号
3. 获取正式编号后统一更新模块内的错误码
4. 错误码一旦发布到生产环境后不能修改值
5. 废弃的错误码要保留并标记为废弃,避免编号重复使用

## 权限规则

### 1. 权限定义

```php
// 权限配置
return [
    'module_name' => [
        'list' => '查看列表',
        'create' => '创建',
        'edit' => '编辑',
        'delete' => '删除',
    ]
];

// 权限检查
class YourPolicy
{
    public function view(User $user, Model $model): bool
    {
        return $user->can('module_name.view');
    }

    public function create(User $user): bool
    {
        return $user->can('module_name.create');
    }
}
```

## 多语言规则

### 1. 语言文件结构

```php
// resources/lang/zh_CN/module.php
return [
    'title' => '标题',
    'list' => [
        'empty' => '暂无数据',
        'total' => '共 :count 条'
    ],
    'form' => [
        'title' => '表单标题',
        'submit' => '提交'
    ],
    'message' => [
        'create_success' => '创建成功',
        'update_success' => '更新成功',
        'delete_success' => '删除成功'
    ]
];
```

## 特殊处理规则

### 1. 错误处理

```php
// 错误码定义
enum ModuleErrorCode: int
{
    use EnumEnhance;
    
    case NOT_FOUND = 140001;
    case CREATE_FAILED = 140002;
    case UPDATE_FAILED = 140003;
    case DELETE_FAILED = 140004;
}

// 异常处理
public function show(int $id): Model
{
    $model = $this->repository->find($id);
    if (!$model) {
        BizException::throws(ModuleErrorCode::NOT_FOUND);
    }
    return $model;
}
```

## 最佳实践

1. 命名规范
   - 模块名：PascalCase（例如：Course）
   - 控制器：PascalCase + Controller（例如：CourseController）
   - 服务：PascalCase + Service（例如：CourseService）
   - 仓储：PascalCase + Repository（例如：CourseRepository）
   - 数据表：模块名小写 + 下划线 + 表名（例如：course_courses）

2. 代码组织
   - 遵循 DDD 分层架构
   - 控制器只负责请求处理和响应
   - 业务逻辑放在 Service 层
   - 数据访问放在 Repository 层
   - 领域逻辑放在 Domain 层

3. 错误处理
   - 统一使用 BizException 处理异常
   - 错误码统一在模块的 ErrorCode 中定义
   - 示例：
   ```php
   BizException::throws(CourseErrorCode::COURSE_NOT_FOUND)
   ```

4. 路由规范
   - 按功能模块组织路由
   - 使用适当的 HTTP 方法
   - 统一的路由前缀配置
   - 合理使用路由中间件

5. 测试规范
   - 每个模块必须包含测试用例
   - 避免使用 RefreshDatabase 特性
   - 使用工厂创建测试数据
   - 测试用例应该独立且可重复执行 
