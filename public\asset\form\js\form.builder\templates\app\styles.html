<div class="accordion" id="accordion-example">
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingGlobal">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGlobal" aria-expanded="true">
                {{= polyglot.t('style.global') }}
            </button>
        </h2>
        <div id="collapseGlobal" class="accordion-collapse collapse show" data-bs-parent="#accordion-example">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="global-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="global-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="global-font-family-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="global-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="global-text-align-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="global-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="global-padding-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingForm">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseForm" aria-expanded="false">
                {{= polyglot.t('style.form') }}
            </button>
        </h2>
        <div id="collapseForm" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.extra') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-align-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingFormGroup">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFormGroup" aria-expanded="false">
                {{= polyglot.t('style.formGroup') }}
            </button>
        </h2>
        <div id="collapseFormGroup" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-group-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-group-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-group-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-group-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-group-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-group-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-group-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-group-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-group-padding-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingFormControl">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFormControl" aria-expanded="false">
                {{= polyglot.t('style.formControl') }}
            </button>
        </h2>
        <div id="collapseFormControl" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-control-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-control-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-control-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-control-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-height-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-control-font-family-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-line-height-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-control-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.extra') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-control-transition-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.focusEffect') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-control-focus-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-focus-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-focus-box-shadow-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-control-focus-transition-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingButton">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseButton" aria-expanded="false">
                {{= polyglot.t('style.button') }}
            </button>
        </h2>
        <div id="collapseButton" class="accordion-collapse collapse" data-bs-parent="#accordion-example">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-primary-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="button-primary-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="button-primary-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="button-primary-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-height-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-primary-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-line-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.extra') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-primary-transition-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.hoverEffect') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-primary-hover-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-hover-background-image-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-hover-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-box-shadow-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-padding-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-primary-hover-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-hover-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-primary-hover-transition-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingLabel">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLabel" aria-expanded="false">
                {{= polyglot.t('style.label') }}
            </button>
        </h2>
        <div id="collapseLabel" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-label-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-line-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-label-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-label-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-label-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-label-height-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.extra') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-label-display-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingPlaceholder">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePlaceholder" aria-expanded="false">
                {{= polyglot.t('style.placeholder') }}
            </button>
        </h2>
        <div id="collapsePlaceholder" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="placeholder-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-line-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="placeholder-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="placeholder-text-decoration-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingTitle">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTitle" aria-expanded="false">
                {{= polyglot.t('style.heading') }}
            </button>
        </h2>
        <div id="collapseTitle" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="heading-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="heading-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-line-height-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-text-decoration-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-letter-spacing-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-text-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="heading-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="heading-background-image-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="heading-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="heading-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="heading-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="heading-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="heading-height-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingParagraph">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseParagraph" aria-expanded="false">
                {{= polyglot.t('style.paragraph') }}
            </button>
        </h2>
        <div id="collapseParagraph" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="paragraph-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-line-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="paragraph-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="paragraph-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="paragraph-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="paragraph-height-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingHelpText">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHelpText" aria-expanded="false">
                {{= polyglot.t('style.helpText') }}
            </button>
        </h2>
        <div id="collapseHelpText" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-text-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-line-height-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-text-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-text-align-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="form-text-background-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-text-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-border-width-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="form-text-border-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.spacing') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-text-margin-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-padding-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.size') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="form-text-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="form-text-height-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingLink">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLink" aria-expanded="false">
                {{= polyglot.t('style.link') }}
            </button>
        </h2>
        <div id="collapseLink" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="link-font-family-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="link-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-text-transform-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-text-decoration-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="link-background-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.hoverEffect') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="link-hover-font-family-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="link-hover-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-hover-font-weight-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="link-hover-text-decoration-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="link-hover-background-color-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingStep">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseStep" aria-expanded="false">
                {{= polyglot.t('style.formSteps') }}
            </button>
        </h2>
        <div id="collapseStep" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.background') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="step-background-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.others') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="step-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="step-border-radius-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-border-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="step-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-font-family-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-font-weight-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.title') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="step-title-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="step-title-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="step-title-border-color-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.currentStep') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="step-current-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-current-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-current-font-weight-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div class="row">
                            <div class="col-12">
                                <div class="sub-group">
                                    <div class="row">
                                        <div class="col-12">
                                            <h6 class="sub-title">{{= polyglot.t('style.title') }}</h6>
                                        </div>
                                        <div class="col-12">
                                            <div id="step-current-title-background-color-wrapper"></div>
                                        </div>
                                        <div class="col-12">
                                            <div id="step-current-title-color-wrapper"></div>
                                        </div>
                                        <div class="col-12">
                                            <div id="step-current-title-border-color-wrapper"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.succeedStep') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="step-success-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-success-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="step-success-font-weight-wrapper"></div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="sub-group">
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="sub-title">{{= polyglot.t('style.title') }}</h6>
                                    </div>
                                    <div class="col-12">
                                        <div id="step-success-title-background-color-wrapper"></div>
                                    </div>
                                    <div class="col-12">
                                        <div id="step-success-title-color-wrapper"></div>
                                    </div>
                                    <div class="col-12">
                                        <div id="step-success-title-border-color-wrapper"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.previousButton') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-prev-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-prev-color-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-prev-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-prev-border-radius-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-prev-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-prev-display-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.previousButtonHoverEffect') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-prev-hover-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-prev-hover-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-prev-hover-border-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.nextButton') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-next-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-float-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-next-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="button-next-border-radius-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-display-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.nextButtonHoverEffect') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="button-next-hover-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-hover-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="button-next-hover-border-color-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingAlert">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAlert" aria-expanded="false">
                {{= polyglot.t('style.formAlerts') }}
            </button>
        </h2>
        <div id="collapseAlert" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.text') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="alert-font-family-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="alert-font-size-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="alert-font-weight-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">
                            {{= polyglot.t('style.border') }}
                        </h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div id="alert-border-style-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="alert-border-width-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="alert-border-radius-wrapper"></div>
                    </div>
                    <div class="col-6">
                        <div id="alert-box-shadow-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.successMessage') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="alert-success-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="alert-success-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="alert-success-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.errorMessage') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="alert-danger-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="alert-danger-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="alert-danger-color-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingValidation">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseValidation" aria-expanded="false">
                {{= polyglot.t('style.fieldValidation') }}
            </button>
        </h2>
        <div id="collapseValidation" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.asteriskSymbol') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="validation-symbol-asterisk-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.textMessage') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="validation-error-text-color-wrapper"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.field') }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div id="validation-error-field-background-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="validation-error-field-border-color-wrapper"></div>
                    </div>
                    <div class="col-12">
                        <div id="validation-error-field-color-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingOther">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOther" aria-expanded="false">
                {{= polyglot.t('style.otherComponents') }}
            </button>
        </h2>
        <div id="collapseOther" class="accordion-collapse collapse" data-bs-parent="#accordion">
            <div class="accordion-body p-0">
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">reCAPTCHA</h6>
                    </div>
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-12">
                                    <div id="recaptcha-margin-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.signaturePad') }}</h6>
                    </div>
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-12">
                                    <div id="signature-canvas-background-color-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.checkbox') }}</h6>
                    </div>
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-6">
                                    <h6 class="sub-title">{{= polyglot.t('style.label') }}</h6>
                                    <div id="checkbox-inline-font-size-wrapper"></div>
                                </div>
                                <div class="col-6">
                                    <h6 class="sub-title">{{= polyglot.t('style.input') }}</h6>
                                    <div id="checkbox-input-margin-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.customControl') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-checkbox-before-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-checkbox-before-border-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.checked') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-checkbox-checked-before-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-checkbox-checked-before-border-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.mark') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-checkbox-checked-after-color-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.radioButton') }}</h6>
                    </div>
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-6">
                                    <h6 class="sub-title">{{= polyglot.t('style.label') }}</h6>
                                    <div id="radio-inline-font-size-wrapper"></div>
                                </div>
                                <div class="col-6">
                                    <h6 class="sub-title">{{= polyglot.t('style.input') }}</h6>
                                    <div id="radio-input-margin-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.customControl') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-radio-before-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-radio-before-border-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.checked') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-radio-checked-before-border-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.mark') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-radio-checked-after-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="custom-control-radio-checked-after-border-color-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="sub-title">{{= polyglot.t('style.progressBar') }}</h6>
                    </div>
                    <div class="col-12">
                        <div class="sub-group">
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.progressBarContainer') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="progress-bar-container-background-color-wrapper"></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="sub-title">{{= polyglot.t('style.progressBar') }}</h6>
                                </div>
                                <div class="col-12">
                                    <div id="progress-bar-background-color-wrapper"></div>
                                </div>
                                <div class="col-12">
                                    <div id="progress-bar-color-wrapper"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>