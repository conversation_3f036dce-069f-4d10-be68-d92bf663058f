$(document).ready(() => {
  $(".bwms-page .nav-x .container .tab-list .tab-item").click(function() {
    const tab = $(this).attr("data-tab");
    if(tab !== '全部') {
      if($(".bwms-page .breadcrumbs .container ul li.last").length) {
        $(".bwms-page .breadcrumbs .container ul li.last").next().text(tab);
      }else {
        const str = `<li class="iconfont icon-arrow-right last"></li> <li>${tab}</li>`;
        $(".bwms-page .breadcrumbs .container ul").append(str);
      }
    }else {
      $(".bwms-page .breadcrumbs .container ul li.last").next().remove();
      $(".bwms-page .breadcrumbs .container ul li.last").remove();
    }
  })
})