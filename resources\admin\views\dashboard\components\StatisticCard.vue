<template>
  <div class="statistic-card">
    <div class="icon-wrapper">
      <el-icon><component :is="icon" /></el-icon>
    </div>
    <div class="content">
      <div class="number">
        <span class="value">{{ value }}</span>
        <span class="unit">{{ unit }}</span>
      </div>
      <div class="title">{{ title }}</div>
      <div class="total">{{ total }} pages in total</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Document, Bell, Monitor, Warning } from '@element-plus/icons-vue'

defineProps<{
  icon: string
  value: number
  unit: string
  title: string
  total: number
}>()
</script>

<style lang="scss" scoped>
.statistic-card {

  background: white;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  min-width: 280px;

  .icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f7ff;
    flex-shrink: 0;
    
    .el-icon {
      font-size: 24px;
      color: #409eff;
    }
  }

  .content {
    flex: 1;
    min-width: 0;

    .number {
      display: flex;
      align-items: baseline;
      gap: 4px;
      margin-bottom: 4px;

      .value {
        font-size: 32px;
        font-weight: 600;
        color: #409eff;
      }

      .unit {
        font-size: 14px;
        color: #606266;
      }
    }

    .title {
      font-size: 16px;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
    }

    .total {
      font-size: 12px;
      color: #909399;
    }
  }

  @media screen and (min-width: 1441px) and (max-width: 1680px) {
    padding: 18px;
    gap: 14px;
    border-radius: 14px;
    min-width: 260px;

    .icon-wrapper {
      width: 44px;
      height: 44px;
      border-radius: 10px;

      .el-icon {
        font-size: 22px;
      }
    }

    .content {
      .number {
        gap: 3px;
        margin-bottom: 3px;

        .value {
          font-size: 28px;
        }

        .unit {
          font-size: 13px;
        }
      }

      .title {
        font-size: 15px;
        margin-bottom: 3px;
      }

      .total {
        font-size: 11px;
      }
    }
  }

  @media screen and (max-width: 1440px) {
    padding: 17px;
    gap: 13px;
    border-radius: 13px;
    min-width: 240px;

    .icon-wrapper {
      width: 41px;
      height: 41px;
      border-radius: 10px;

      .el-icon {
        font-size: 20px;
      }
    }

    .content {
      .number {
        gap: 3px;
        margin-bottom: 3px;

        .value {
          font-size: 27px;
        }

        .unit {
          font-size: 12px;
        }
      }

      .title {
        font-size: 14px;
        margin-bottom: 3px;
      }

      .total {
        font-size: 10px;
      }
    }
  }
}
</style> 