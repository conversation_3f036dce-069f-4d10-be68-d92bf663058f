import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { infoSectionTemplate, infoSectionAlternateTemplate } from '../templates/infoSection.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    infoSectionBlock: {
      insertInfoSectionBlock: () => ReturnType,
      insertInfoSectionAlternateBlock: () => ReturnType
    }
  }
}

export const InfoSectionBlock = Node.create({
  name: 'infoSectionBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div.info-section',
      },
      {
        tag: 'div[data-bs-component="info-section"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'info-section',
      'class': 'info-section-block py-5'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      },
      variant: {
        default: 'default',
        parseHTML: element => element.getAttribute('data-variant'),
        renderHTML: attributes => {
          if (!attributes.variant) {
            return {}
          }
          
          return {
            'data-variant': attributes.variant
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertInfoSectionBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(infoSectionTemplate)
        },
      insertInfoSectionAlternateBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(infoSectionAlternateTemplate)
        },
    }
  },
}) 