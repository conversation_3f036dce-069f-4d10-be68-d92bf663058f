/*!
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2022
 * @version 1.9.9
 *
 * Date control validation plugin
 *
 * Author: <PERSON><PERSON><PERSON>
 *
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */!function(e){"use strict";var t=".datecontrol",a=function(t,a){return null===t||void 0===t||0===t.length||a&&""===e.trim(t)},n=function(t,a){var n=this;n.$element=e(t),n.init(a),n.listen()};n.prototype={constructor:n,init:function(t){var n=this,r=a(t.dateSettings)?{}:{dateSettings:t.dateSettings};if(e.each(t,function(e,t){n[e]=t}),n.$idSave=e("#"+t.idSave),n.dateFormatter=window.DateFormatter?new window.DateFormatter(r):{},a(n.dateFormatter))throw"No DateFormatter plugin found. Ensure you have 'php-date-formatter.js' loaded.";n.isChanged=!1},raise:function(a,n){var r=this,i=e.Event(a+t),s=r.$element;void 0!==n?s.trigger(i,n):s.trigger(i)},validate:function(){var t,n=this,r=n.$element,i=n.$idSave,s=n.url,o=n.type,u=n.dispFormat,l=n.saveFormat,d=n.dispTimezone,c=n.saveTimezone,v=n.asyncRequest,g=n.dateFormatter;if(!n.isChanged)if(n.isChanged=!0,a(r.val()))i.val("").trigger("change"),n.raise("changesuccess",[r.val(),i.val()]),n.isChanged=!1;else if(a(s)){var h=g.parseDate(r.val(),u);h!==!1&&null!==h&&0!==String(h).length||(h=g.guessDate(r.val(),u),r.val(g.formatDate(h,u))),i.val(g.formatDate(h,l)).trigger("change"),n.raise("changesuccess",[r.val(),i.val()]),n.isChanged=!1}else t="en"===n.language.substring(0,2)?[]:n.dateSettings,e.ajax({url:s,type:"post",dataType:"json",async:v,data:{displayDate:r.val(),type:o,dispFormat:u,saveFormat:l,dispTimezone:d,saveTimezone:c,settings:t},beforeSend:function(e){n.raise("beforechange",[r.val(),i.val(),e])},success:function(e,t,a){var s="changeerror";"success"===e.status&&(i.val(e.output).trigger("change"),s="changesuccess"),n.raise(s,[r.val(),i.val(),e,t,a])},complete:function(){n.isChanged=!1,n.raise("changecomplete",[r.val(),i.val()])},error:function(e,t,a){n.isChanged=!1,n.raise("changeajaxerror",[r.val(),i.val(),e,t,a])}})},listen:function(){var e=this,t=e.$element;t.on("change",function(){setTimeout(function(){e.validate()},100)}).on("paste",function(){setTimeout(function(){t.val(t.val()),e.validate(),e.raise("afterpaste")},100)})}},e.fn.datecontrol=function(t){var a=Array.apply(null,arguments);return a.shift(),this.each(function(){var r=e(this),i=r.data("datecontrol"),s="object"==typeof t&&t;i||(i=new n(this,e.extend({},e.fn.datecontrol.defaults,s,e(this).data())),r.data("datecontrol",i)),"string"==typeof t&&i[t].apply(i,a)})},e.fn.datecontrol.defaults={language:"en",dateSettings:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["AM","PM"]},dispTimezone:null,saveTimezone:null,asyncRequest:!0},e.fn.datecontrol.Constructor=n}(window.jQuery);
