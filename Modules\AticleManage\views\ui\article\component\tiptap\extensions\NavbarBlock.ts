import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { navbarTemplate } from '../templates/navbar.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    navbarBlock: {
      insertNavbarBlock: () => ReturnType
    }
  }
}

// 初始化Bootstrap导航栏功能
const initBootstrapNavbar = () => {
  // 处理导航栏折叠按钮
  const navbarTogglers = document.querySelectorAll('.navbar-section .navbar-toggler, [data-bs-component="navbar"] .navbar-toggler');
  navbarTogglers.forEach(toggler => {
    if (!(toggler instanceof HTMLElement)) return;
    
    // 移除所有现有事件监听器，避免重复绑定
    const newToggler = toggler.cloneNode(true) as HTMLElement;
    if (toggler.parentNode) {
      toggler.parentNode.replaceChild(newToggler, toggler);
    }
    
    // 添加新的事件监听器
    newToggler.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      
      const targetId = newToggler.getAttribute('data-bs-target');
      if (!targetId) return;
      
      const targetCollapse = document.querySelector(targetId);
      if (!targetCollapse) return;
      
      if (targetCollapse.classList.contains('show')) {
        targetCollapse.classList.remove('show');
      } else {
        targetCollapse.classList.add('show');
      }
    });
  });
  
  // 处理下拉菜单
  const dropdownToggles = document.querySelectorAll('.navbar-section .dropdown-toggle, [data-bs-component="navbar"] .dropdown-toggle');
  dropdownToggles.forEach(toggle => {
    if (!(toggle instanceof HTMLElement)) return;
    
    // 移除所有现有事件监听器
    const newToggle = toggle.cloneNode(true) as HTMLElement;
    if (toggle.parentNode) {
      toggle.parentNode.replaceChild(newToggle, toggle);
    }
    
    // 添加新的事件监听器
    newToggle.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      
      // 查找对应的下拉菜单
      const parent = newToggle.closest('.dropdown, .nav-item');
      if (!parent) return;
      
      const dropdownMenu = parent.querySelector('.dropdown-menu');
      if (!dropdownMenu) return;
      
      // 切换显示状态
      if (dropdownMenu.classList.contains('show')) {
        dropdownMenu.classList.remove('show');
        parent.classList.remove('show');
      } else {
        // 关闭其他打开的下拉菜单
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
          menu.classList.remove('show');
          const parentItem = menu.closest('.dropdown, .nav-item');
          if (parentItem) parentItem.classList.remove('show');
        });
        
        // 显示当前下拉菜单
        dropdownMenu.classList.add('show');
        parent.classList.add('show');
      }
    });
  });
  
  // 添加点击外部关闭下拉菜单的事件
  document.addEventListener('click', (event) => {
    if (!(event.target instanceof HTMLElement)) return;
    
    // 如果点击的不是下拉菜单或其子元素
    if (!event.target.closest('.dropdown-menu') && !event.target.classList.contains('dropdown-toggle')) {
      // 关闭所有打开的下拉菜单
      document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
        menu.classList.remove('show');
        const parentItem = menu.closest('.dropdown, .nav-item');
        if (parentItem) parentItem.classList.remove('show');
      });
    }
  });
};

export const NavbarBlock = Node.create({
  name: 'navbarBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  atom: true, // 将节点设置为原子节点，防止内部内容被编辑
  
  parseHTML() {
    return [
      {
        tag: 'nav[data-bs-component="navbar"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }
          
          return {
            class: element.getAttribute('class') || 'navbar-section responsive-block',
            contentHTML: element.innerHTML,
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    // 确保Bootstrap样式已加载（如果需要）
    const bootstrapStyle = document.querySelector('link[href*="bootstrap"]');
    if (!bootstrapStyle) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css';
      document.head.appendChild(link);
    }
    
    // 确保Bootstrap脚本已加载
    const bootstrapScript = document.querySelector('script[src*="bootstrap"]');
    if (!bootstrapScript) {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js';
      script.defer = true;
      document.head.appendChild(script);
    }
    
    // 合并属性，确保data-bs-component="navbar"属性始终存在
    const finalAttributes = mergeAttributes(
      {
        'data-bs-component': 'navbar',
        'class': HTMLAttributes.class || 'navbar-section responsive-block'
      },
      HTMLAttributes
    )
    
    // 确保data-bs-component不被其他属性覆盖
    finalAttributes['data-bs-component'] = 'navbar'
    
    // 处理内容HTML
    let contentHTML = '';
    if (HTMLAttributes.contentHTML) {
      contentHTML = HTMLAttributes.contentHTML;
    } else {
      // 如果没有保存的HTML内容，从模板中提取内容
      const tempEl = document.createElement('div');
      tempEl.innerHTML = navbarTemplate;
      contentHTML = tempEl.firstElementChild?.innerHTML || '';
    }
    
    // 创建最终的HTML结构
    const container = document.createElement('nav');
    Object.entries(finalAttributes).forEach(([key, value]) => {
      if (key !== 'contentHTML') {
        container.setAttribute(key, value.toString());
      }
    });
    
    // 处理内容HTML，确保样式正确
    // 检查contentHTML中是否包含style标签
    const hasInternalStyle = /<style[^>]*>[\s\S]*?<\/style>/i.test(contentHTML);
    
    // 设置内容
    container.innerHTML = contentHTML;
    
    // 如果没有内部样式，尝试从页面中查找与该导航栏相关的外部样式
    if (!hasInternalStyle) {
      // 查找页面中所有style标签
      const allStyles = document.querySelectorAll('style');
      let navbarStyleContent = '';
      
      // 遍历所有style标签，查找与navbar相关的样式
      allStyles.forEach(styleTag => {
        const styleContent = styleTag.textContent || '';
        
        // 检查样式是否包含navbar相关的选择器
        if (styleContent.includes('.navbar-section') || 
            styleContent.includes('[data-bs-component="navbar"]') || 
            styleContent.includes('nav[data-bs-component="navbar"]') ||
            styleContent.includes('.navbar') ||
            styleContent.includes('.navbar-expand-lg') ||
            styleContent.includes('.navbar-nav') ||
            styleContent.includes('.dropdown-menu')) {
          
          // 提取所有与导航栏相关的CSS规则
          const cssRules = styleContent.match(/\.navbar[^}]+\}|\.navbar-[^}]+\}|\.nav-[^}]+\}|\.dropdown[^}]+\}|nav\[data-bs-component="navbar"\][^}]+\}|\.navbar-section\s+[^}]+\}|\.navbar-section\.[^}]+\}/g);
          
          if (cssRules && cssRules.length > 0) {
            cssRules.forEach(rule => {
              navbarStyleContent += rule + '\n';
            });
          }
        }
      });
      
      // 如果找到了相关样式，添加到导航栏中
      if (navbarStyleContent) {
        const styleElement = document.createElement('style');
        styleElement.textContent = navbarStyleContent;
        container.appendChild(styleElement);
      }
      
      // 特殊处理：查找整个页面模板的样式（如marketing-landing-page中的导航栏样式）
      allStyles.forEach(styleTag => {
        const styleContent = styleTag.textContent || '';
        
        // 检查是否有包含在页面模板中的导航栏样式
        if (styleContent.includes('.marketing-landing-page .navbar-section') || 
            styleContent.includes('.marketing-landing-page nav[data-bs-component="navbar"]') ||
            styleContent.includes('.marketing-landing-page .navbar')) {
          
          // 提取所有与页面模板中导航栏相关的CSS规则
          const cssRules = styleContent.match(/\.marketing-landing-page\s+\.navbar[^}]+\}|\.marketing-landing-page\s+\.navbar-[^}]+\}|\.marketing-landing-page\s+\.nav-[^}]+\}|\.marketing-landing-page\s+\.dropdown[^}]+\}|\.marketing-landing-page\s+nav\[data-bs-component="navbar"\][^}]+\}|\.marketing-landing-page\s+\.navbar-section[^}]+\}/g);
          
          if (cssRules && cssRules.length > 0) {
            let pageNavbarStyle = '';
            cssRules.forEach(rule => {
              // 移除页面模板前缀，使样式直接应用于导航栏
              const modifiedRule = rule.replace(/\.marketing-landing-page\s+/g, '');
              pageNavbarStyle += modifiedRule + '\n';
            });
            
            if (pageNavbarStyle) {
              const styleElement = document.createElement('style');
              styleElement.textContent = pageNavbarStyle;
              container.appendChild(styleElement);
            }
          }
        }
      });
      
      // 查找并添加导航栏子元素的样式
      allStyles.forEach(styleTag => {
        const styleContent = styleTag.textContent || '';
        
        // 检查是否有导航栏子元素的样式
        const subSelectors = [
          '.navbar-wrapper', '.navbar-brand', '.navbar-links', 
          '.nav-link', '.navbar-section .btn', '.navbar-section .btn-primary',
          '.navbar-section .btn-outline-primary', '.navbar-toggler',
          '.navbar-collapse', '.navbar-nav', '.dropdown-menu',
          '.dropdown-item', '.dropdown-toggle', '.nav-item'
        ];
        
        let subElementStyles = '';
        
        subSelectors.forEach(selector => {
          const regex = new RegExp(`\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}|\\.marketing-landing-page\\s+\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}|${selector.replace('.', '\\.')}[^}]+\\}`, 'g');
          const matches = styleContent.match(regex);
          
          if (matches && matches.length > 0) {
            matches.forEach(match => {
              // 移除页面模板前缀，保留导航栏前缀
              const modifiedRule = match.replace(/\.marketing-landing-page\s+/g, '');
              subElementStyles += modifiedRule + '\n';
            });
          }
        });
        
        if (subElementStyles) {
          const styleElement = document.createElement('style');
          styleElement.textContent = subElementStyles;
          container.appendChild(styleElement);
        }
      });
    }
    
    // 确保导航栏的交互功能在插入后正常工作
    setTimeout(() => {
      // 初始化Bootstrap导航组件
      initBootstrapNavbar();
    }, 100);
    
    // 使用outerHTML将完整的HTML结构传回给编辑器
    return {
      dom: container,
      contentDOM: null, // 使用null表示这是一个原子节点
    }
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: 'navbar-section responsive-block',
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return { class: 'navbar-section responsive-block' }
          }
          
          return {
            class: attributes.class
          }
        }
      },
      contentHTML: {
        default: null,
        parseHTML: element => element.innerHTML,
        renderHTML: attributes => {
          return { contentHTML: attributes.contentHTML }
        }
      }
    }
  },

  addCommands() {
    return {
      insertNavbarBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(navbarTemplate)
        },
    }
  },
  
  // 添加钩子确保每次更新后导航栏交互功能正常
  onUpdate() {
    setTimeout(() => {
      initBootstrapNavbar();
    }, 100);
  }
}) 