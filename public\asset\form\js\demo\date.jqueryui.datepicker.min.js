$(document).ready(function(){$.when($("head").append('<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" type="text/css" />'),$.getScript("//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("body").css("padding-bottom","200px");$("input[type=date]").each(function(){var that=this;$(that).attr("type","text").after($(that).clone().attr("id",that.id+"_alt").attr("name",that.id+"_alt").datepicker({dateFormat:"mm/dd/yy",changeMonth:true,changeYear:true,altField:this,altFormat:"yy-mm-dd"}).on("change",function(){$(that).trigger("change")})).hide()})})});