<!DOCTYPE html>
<!--
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>CKEditor Samples</title>
	<link rel="stylesheet" href="sample.css">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
</head>
<body>
	<h1 class="samples">
		CKEditor Samples
	</h1>
	<div class="warning deprecated">
		These samples are not maintained anymore. Check out the <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/index.html">brand new samples in CKEditor Examples</a>.
	</div>
	<div class="twoColumns">
		<div class="twoColumnsLeft">
			<h2 class="samples">
				Basic Samples
			</h2>
			<dl class="samples">
				<dt><a class="samples" href="replacebyclass.html">Replace textarea elements by class name</a></dt>
				<dd>Automatic replacement of all textarea elements of a given class with a CKEditor instance.</dd>

				<dt><a class="samples" href="replacebycode.html">Replace textarea elements by code</a></dt>
				<dd>Replacement of textarea elements with CKEditor instances by using a JavaScript call.</dd>

				<dt><a class="samples" href="jquery.html">Create editors with jQuery</a></dt>
				<dd>Creating standard and inline CKEditor instances with jQuery adapter.</dd>
			</dl>

			<h2 class="samples">
				Basic Customization
			</h2>
			<dl class="samples">
				<dt><a class="samples" href="uicolor.html">User Interface color</a></dt>
				<dd>Changing CKEditor User Interface color and adding a toolbar button that lets the user set the UI color.</dd>

				<dt><a class="samples" href="uilanguages.html">User Interface languages</a></dt>
				<dd>Changing CKEditor User Interface language and adding a drop-down list that lets the user choose the UI language.</dd>
			</dl>


			<h2 class="samples">Plugins</h2>
<dl class="samples">
<dt><a class="samples" href="magicline/magicline.html">Magicline plugin</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>

<dt><a class="samples" href="wysiwygarea/fullpage.html">Full page support</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>
</dl>
		</div>
		<div class="twoColumnsRight">
			<h2 class="samples">
				Inline Editing
			</h2>
			<dl class="samples">
				<dt><a class="samples" href="inlineall.html">Massive inline editor creation</a></dt>
				<dd>Turn all elements with <code>contentEditable = true</code> attribute into inline editors.</dd>

				<dt><a class="samples" href="inlinebycode.html">Convert element into an inline editor by code</a></dt>
				<dd>Conversion of DOM elements into inline CKEditor instances by using a JavaScript call.</dd>

				<dt><a class="samples" href="inlinetextarea.html">Replace textarea with inline editor</a> <span class="new">New!</span></dt>
				<dd>A form with a textarea that is replaced by an inline editor at runtime.</dd>


			</dl>

			<h2 class="samples">
				Advanced Samples
			</h2>
			<dl class="samples">
				<dt><a class="samples" href="datafiltering.html">Data filtering and features activation</a> <span class="new">New!</span></dt>
				<dd>Data filtering and automatic features activation basing on configuration.</dd>

				<dt><a class="samples" href="divreplace.html">Replace DIV elements on the fly</a></dt>
				<dd>Transforming a <code>div</code> element into an instance of CKEditor with a mouse click.</dd>

				<dt><a class="samples" href="appendto.html">Append editor instances</a></dt>
				<dd>Appending editor instances to existing DOM elements.</dd>

				<dt><a class="samples" href="ajax.html">Create and destroy editor instances for Ajax applications</a></dt>
				<dd>Creating and destroying CKEditor instances on the fly and saving the contents entered into the editor window.</dd>

				<dt><a class="samples" href="api.html">Basic usage of the API</a></dt>
				<dd>Using the CKEditor JavaScript API to interact with the editor at runtime.</dd>

				<dt><a class="samples" href="xhtmlstyle.html">XHTML-compliant style</a></dt>
				<dd>Configuring CKEditor to produce XHTML 1.1 compliant attributes and styles.</dd>

				<dt><a class="samples" href="readonly.html">Read-only mode</a></dt>
				<dd>Using the readOnly API to block introducing changes to the editor contents.</dd>

				<dt><a class="samples" href="tabindex.html">"Tab" key-based navigation</a></dt>
				<dd>Navigating among editor instances with tab key.</dd>



<dt><a class="samples" href="dialog/dialog.html">Using the JavaScript API to customize dialog windows</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>

<dt><a class="samples" href="enterkey/enterkey.html">Using the &quot;Enter&quot; key in CKEditor</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>

<dt><a class="samples" href="htmlwriter/outputhtml.html">Output HTML</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>

<dt><a class="samples" href="toolbar/toolbar.html">Toolbar Configurations</a></dt>
<dd>Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.</dd>

			</dl>
		</div>
	</div>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2023, <a class="samples" href="https://cksource.com/">CKSource</a> Holding sp. z o.o. All rights reserved.
		</p>
	</div>
</body>
</html>
