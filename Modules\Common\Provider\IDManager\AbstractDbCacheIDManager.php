<?php

namespace Modules\Common\Provider\IDManager;

use Illuminate\Support\Facades\Cache;

abstract class AbstractDbCacheIDManager extends AbstractIDManager
{
    abstract public function dbCacheAll();

    private function clearCache(): void
    {
        Cache::forget($this->name().'_All');
    }

    public function all()
    {
        return Cache::rememberForever($this->name().'_All', function () {
            return $this->dbCacheAll();
        });
    }

    public function add($ids): void
    {
        $this->clearCache();
    }

    public function remove($ids): void
    {
        $this->clearCache();
    }

    public function total(): int
    {
        return count($this->all());
    }

    private function idsPaginate(&$ids, $page, $pageSize): array
    {
        $page = max($page, 1);
        $offset = max($page - 1, 0) * $pageSize;
        if (! isset($ids[$offset])) {
            return [];
        }
        $results = [];
        for ($i = $offset; $i < $offset + $pageSize; $i++) {
            if (! isset($ids[$i])) {
                break;
            }
            $results[] = $ids[$i];
        }
        return $results;
    }

    public function paginate($page, $pageSize): array
    {
        $ids = $this->all();
        return $this->idsPaginate($ids, $page, $pageSize);
    }

    public function paginateRandom($page, $pageSize, $cacheKey = 'all', $cacheMinutes = 60): array
    {
        $ids = Cache::remember($this->name().'_Random_'.$cacheKey, $cacheMinutes, function () {
            $ids = $this->all();
            shuffle($ids);
            return $ids;
        });
        return $this->idsPaginate($ids, $page, $pageSize);
    }

    public function forgetRandom($cacheKey = 'all'): void
    {
        Cache::forget($this->name().'_Random_'.$cacheKey);
    }


}
