<?php

namespace Modules\Common\Provider\SiteUrl;

use Modules\Common\Provider\BizTrait;

class SiteUrlBiz
{
    use BizTrait;

    /**
     * @return AbstractSiteUrlBiz[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractSiteUrlBiz
     */
    public static function get($name): AbstractSiteUrlBiz
    {
        return self::getByName($name);
    }
}
