# IAM 权限系统模型说明

## 概述

本模块包含完整的RBAC（基于角色的访问控制）权限系统模型，为TVB新闻CMS项目提供强大的权限管理功能。

## 模型列表

### 1. Admin（管理员模型）
- **文件**: `Admin.php`
- **表名**: `admins`
- **功能**: 管理系统管理员账户，支持多角色分配、笔名管理
- **主要特性**:
  - 继承 `Authenticatable` 支持认证功能
  - 支持多角色分配
  - 支持多笔名管理
  - 完整的权限检查方法
  - 丰富的查询作用域

### 2. Role（角色模型）
- **文件**: `Role.php`
- **表名**: `roles`
- **功能**: 管理系统角色，支持权限和菜单分配
- **主要特性**:
  - 支持系统预设角色和自定义角色
  - 多对多关联权限和菜单
  - 支持角色过期时间
  - 完整的角色状态管理

### 3. Permission（权限模型）
- **文件**: `Permission.php`
- **表名**: `permissions`
- **功能**: 管理系统权限，支持细粒度权限控制
- **主要特性**:
  - 模块化权限设计（模块-资源-操作）
  - 支持系统预设权限和自定义权限
  - 完整的权限代码生成
  - 丰富的查询筛选功能

### 4. Menu（菜单模型）
- **文件**: `Menu.php`
- **表名**: `menus`
- **功能**: 管理系统菜单，支持层级结构和权限控制
- **主要特性**:
  - 支持无限层级菜单结构
  - 支持菜单类型（主菜单、子菜单、按钮）
  - 完整的菜单路径生成
  - 灵活的菜单排序和状态管理

### 5. DataPermission（数据权限模型）
- **文件**: `DataPermission.php`
- **表名**: `data_permissions`
- **功能**: 提供细粒度的数据访问控制
- **主要特性**:
  - 支持多种权限类型（全部、部门、个人、自定义）
  - 灵活的权限范围配置
  - JSON格式存储权限范围值
  - 完整的数据权限检查功能

### 6. OperationLog（操作日志模型）
- **文件**: `OperationLog.php`
- **表名**: `operation_logs`
- **功能**: 记录系统操作审计日志
- **主要特性**:
  - 完整的操作记录（请求、响应、执行时间）
  - 支持操作成功/失败状态
  - 丰富的日志查询功能
  - 支持慢查询监控

### 7. PenName（笔名模型）
- **文件**: `PenName.php`
- **表名**: `pen_names`
- **功能**: 管理新闻作者的笔名
- **主要特性**:
  - 支持多种笔名类型（记者、编辑、特约、其他）
  - 笔名使用统计和追踪
  - 支持笔名状态管理
  - 完整的笔名关联功能

## 数据库关系

```
Admin (管理员)
├── roles (多对多) -> Role (角色)
├── penNames (多对多) -> PenName (笔名)
└── operationLogs (一对多) -> OperationLog (操作日志)

Role (角色)
├── admins (多对多) -> Admin (管理员)
├── permissions (多对多) -> Permission (权限)
├── menus (多对多) -> Menu (菜单)
└── dataPermissions (一对多) -> DataPermission (数据权限)

Permission (权限)
└── roles (多对多) -> Role (角色)

Menu (菜单)
├── parent (自关联) -> Menu (父菜单)
├── children (自关联) -> Menu (子菜单)
└── roles (多对多) -> Role (角色)

DataPermission (数据权限)
├── role (多对一) -> Role (角色)
└── creator (多对一) -> Admin (创建者)

OperationLog (操作日志)
└── admin (多对一) -> Admin (操作者)

PenName (笔名)
├── creator (多对一) -> Admin (创建者)
└── admins (多对多) -> Admin (使用者)
```

## 使用示例

### 1. 检查用户权限
```php
$admin = Admin::find(1);

// 检查单个权限
if ($admin->hasPermission('article:create')) {
    // 有创建文章的权限
}

// 检查多个权限（AND关系）
if ($admin->hasPermission(['article:create', 'article:edit'])) {
    // 同时有创建和编辑文章的权限
}

// 检查角色
if ($admin->hasRole('content_editor')) {
    // 是内容编辑角色
}
```

### 2. 角色权限管理
```php
$role = Role::find(1);

// 分配权限
$role->permissions()->attach([1, 2, 3]);

// 获取角色权限
$permissions = $role->permissions;

// 获取角色下的管理员
$admins = $role->admins;
```

### 3. 菜单管理
```php
$menu = Menu::find(1);

// 获取子菜单
$children = $menu->children;

// 获取父菜单
$parent = $menu->parent;

// 获取完整菜单路径
$path = $menu->menu_path; // "系统管理 > 用户管理 > 用户列表"
```

### 4. 操作日志记录
```php
OperationLog::create([
    'admin_id' => auth()->id(),
    'module' => 'article',
    'action' => 'create',
    'resource_type' => 'article',
    'resource_id' => '123',
    'description' => '创建文章：标题',
    'request_data' => $request->all(),
    'response_data' => ['success' => true],
    'ip_address' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'status' => 1,
    'execution_time' => 150
]);
```

## 注意事项

1. **模型继承**: 所有模型都继承自 `Bingo\Base\BingoModel`，确保与项目基础架构兼容
2. **软删除**: 大部分模型支持软删除功能，数据不会真正删除
3. **缓存机制**: 建议在权限检查时使用缓存提高性能
4. **数据完整性**: 删除角色或权限前请检查是否有用户正在使用
5. **系统角色**: 系统预设的角色和权限不建议删除，可以禁用

## 扩展建议

1. **权限缓存**: 实现Redis缓存机制提高权限检查性能
2. **权限继承**: 支持角色权限继承机制
3. **动态权限**: 支持运行时动态权限分配
4. **权限组**: 支持权限分组管理
5. **审计增强**: 增加更详细的操作审计功能

# 权限系统设计说明

## 一、设计思想与原则

本权限系统采用 RBAC（基于角色的访问控制）模型，结合菜单权限、功能权限、数据权限三位一体，满足企业级后台系统的灵活性、安全性和可维护性。

- **模块化、分层设计**：模型、服务、API、路由、中间件分层，便于扩展和维护。
- **高复用性**：角色、权限、菜单、数据权限均为独立模型，支持灵活组合。
- **高安全性**：所有接口均通过中间件校验权限，防止越权访问。
- **高性能**：用户权限、菜单等采用缓存机制，减少数据库压力。
- **可审计性**：所有操作均有日志记录，便于追踪和审计。

## 二、为什么要这样设计（设计依据）

1. **RBAC模型成熟可靠**，适合大部分企业后台权限需求。
2. **菜单、功能、数据权限分离**，既能控制页面可见性，也能精细到按钮和数据行级别。
3. **多角色支持**，满足一人多岗、临时授权等场景。
4. **权限粒度可控**，支持权限过期、主角色、数据范围等高级特性。
5. **易于与前端解耦**，权限数据结构清晰，便于前端动态渲染。

## 三、后端与前端的衔接方式

### 1. API接口

所有权限相关操作均通过 RESTful API 提供，接口返回统一的 JSON 结构。例如：

```http
GET /api/roles           # 获取角色列表
GET /api/permissions     # 获取权限列表
GET /api/menus           # 获取菜单树
GET /api/users/me        # 获取当前用户信息及权限
```

### 2. 权限数据结构

- **权限码**：`module:resource:action` 规范，如 `user:view`、`article:create`
- **菜单树**：后端返回当前用户可见的菜单树，前端直接渲染
- **权限列表**：后端返回当前用户所有权限码，前端用于按钮/路由控制

### 3. 权限校验流程

- 前端每次登录后，调用 `/api/users/me` 获取用户信息、菜单、权限码
- 前端根据权限码动态生成菜单、路由、按钮
- 前端每次发起敏感操作（如删除、编辑），可在页面层做一次权限判断
- 后端所有接口均有权限中间件二次校验，确保安全

## 四、Nuxt3 前端如何集成和使用权限系统

### 1. 动态菜单与路由

- 登录后获取菜单树，动态生成侧边栏和路由表
- 菜单数据结构建议与后端保持一致，便于递归渲染

```js
// 示例：动态注册路由
const menus = await $fetch('/api/menus')
menus.forEach(menu => {
  router.addRoute({
    path: menu.route_path,
    component: resolveComponent(menu.component_path),
    meta: { title: menu.menu_name, icon: menu.icon }
  })
})
```

### 2. 按钮/操作权限

- 获取权限码列表后，封装全局指令或 composable 进行按钮级权限控制

```js
// v-permission 指令用法
<button v-permission="'user:create'">新建用户</button>

// 指令实现（Nuxt3/Composition API）
export default defineNuxtPlugin(nuxtApp => {
  nuxtApp.vueApp.directive('permission', {
    mounted(el, binding) {
      const permissions = useUserStore().permissions
      if (!permissions.includes(binding.value)) {
        el.style.display = 'none'
      }
    }
  })
})
```

### 3. 路由守卫

- 在 Nuxt3 的中间件中校验路由权限

```js
// middleware/auth.ts
export default defineNuxtRouteMiddleware((to, from) => {
  const user = useUserStore()
  if (to.meta.permission && !user.permissions.includes(to.meta.permission)) {
    return navigateTo('/403')
  }
})
```

### 4. 数据权限

- 后端根据数据权限自动过滤数据，前端无需关心
- 如需前端展示数据范围，可通过 `/api/users/me` 返回的数据权限信息渲染

## 五、总结

本权限系统设计遵循业界最佳实践，兼顾安全性、灵活性和易用性。前后端通过标准接口和统一的数据结构无缝衔接，Nuxt3 前端可通过动态菜单、按钮指令、路由守卫等方式灵活应用权限控制，极大提升了系统的可维护性和扩展性。 