@import "../variable.less";

footer.footer {
  padding-top: 70px;
  background-color: #383838;
  width: 100%;

  .footer-con {
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
    max-width: 1500px;

    .link-contact-qr {
      .df();

      .qiuck-link {
        width: 60%;
        .df();

        .link-list {
          flex-grow: 1;

          .qiuck-tit {
            margin-bottom: 20px;
            color: #fff;
            font-size: 16px;

            a {
              color: #fff;
              font-size: 16px;
            }
          }
    
          ul {
            .df(stretch, flex-start, column);

            li {
              a {
                line-height: 2.28;
                color: #888;
                font-size: 14px;
                transition: color .35s ease-in-out;

                &:hover {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    
      .contact {
        flex-grow: 1;

        .item {
          margin-bottom: 15px;
          color: #888;
          line-height: 1.71;
          .df(center);

          &:last-child {
            margin-bottom: 0;
          }

          .iconfont {
            margin-right: 10px;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            background-color: #ff9600;
            color: #fff;
            .df(center, center);
          }
        }
      }
    
      .qr-code {
        margin-left: 20px;
        border-radius: 4px;
        padding: 10px;
        width: 10%;
        background: #484848;
    
        .text-box {
          color: #888888;
          line-height: 1.85;
          font-size: 14px;
          text-align: center;
        }
      }
    }

    .friendship-link {
      .tit {
        border-bottom: 1px solid #404040;
        .df();
        
        a {
          border-bottom: 1px solid #FFB133;
          padding: 10px 0;
          color: #888;
          line-height: 1.25;
          font-size: 16px;
        }
      }
    
      .link-list {
        padding: 25px 0;
        .df();

        a {
          margin-right: 15px;
          line-height: 1.71;
          font-size: 14px;
          color: #6E6E6E;
          transition: color .35s ease-in-out;

          &:hover {
            color: #fff;
          }
        }
      }
    }

    .copyright-site {
      .df(center, space-between);

      border-top: 1px solid #404040;
      padding: 20px 0 35px;
      line-height: 1.71;

      .copyright {
        margin-left: 5px;
        color: #888;
        font-size: 14px;
      }

      .site {
        a {
          color: #888;
          font-size: 14px;
          display: block;
          transition: color .35s ease-in-out;

          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
}