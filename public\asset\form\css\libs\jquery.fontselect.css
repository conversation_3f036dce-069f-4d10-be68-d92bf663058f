.font-select * {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.font-select {
	font-size: 14px;
	width: 100%;
	position: relative;
	display: inline-block;
}

.font-select .fs-drop {
	position: absolute;
	top: 34px;
	left: 0;
	z-index: 999;
	background: #fff;
	color: #313941;
	width: 100%;
	border: 1px solid #D5D8DC;
	border-top: 0;
	box-shadow: 0 4px 5px rgba(0,0,0,.15);
	border-radius: 0 0 4px 4px;
}

.font-select > span {
	outline: 0;
	border-radius: 0.25rem;
	border: 1px solid #ced4da;
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	height: 34px;
	line-height: 28px;
	padding: 3px 12px;
	color: #313941;
	background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23303030' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.font-select-active > span {
	background-color: #f1f2f3;
	border-bottom-left-radius : 0;
	border-bottom-right-radius: 0;
}

.font-select .fs-results {
	max-height: 190px;
	overflow-x: hidden;
	overflow-y: auto;
	margin: 0;
	padding: 0;
}

.font-select .fs-results li {
	line-height: 80%;
	padding: 8px;
	margin: 0;
	list-style: none;
	font-size: 18px;
	white-space: nowrap;
}

.font-select .fs-results li.active {
	background-color: #3875d7;
	color: #fff;
	cursor: pointer;
}

.font-select .fs-search {
	border-bottom: 1px solid #D5D8DC;
	padding: 4px;
}

.font-select .fs-search input {
	padding: 7px;
	width: 100%;
	border: 1px solid #D5D8DC;
	font: 16px Helvetica, Sans-serif;
	box-shadow: inset 0 1px 3px rgba(0,0,0,.06);
	border-radius: .1875rem;
}


