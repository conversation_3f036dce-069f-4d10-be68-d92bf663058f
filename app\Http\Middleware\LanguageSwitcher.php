<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Modules\Multilingual\Models\Multilingual;
use Symfony\Component\HttpFoundation\Response;

class LanguageSwitcher
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 尝试从 URL 段获取语言代码
        $locale = $request->segment(1);

        // 尝试从查询参数获取语言代码
        if (empty($locale) || ! $this->isLocaleSupported($locale)) {
            $locale = $request->query('locale', '');
        }

        // 如果获取到的语言代码是支持的，就设置应用的语言
        if ($this->isLocaleSupported($locale)) {
            App::setLocale($locale);
        } else {
            // 如果 URL 中没有语言代码，或者语言代码不被支持，使用数据库中的默认语言
            $defaultLocale = $this->getDefaultLocale();
            App::setLocale($defaultLocale);
        }

        return $next($request);
    }
    /**
     * 检查给定的语言代码是否受支持
     *
     * @param  string $locale 语言代码
     * @return bool
     */
    protected function isLocaleSupported($locale)
    {
        $supportedLocales = Multilingual::where('status', 1)->pluck('mark')->toArray(); // 扩展为支持的语言列表
        return in_array($locale, $supportedLocales);
    }

    /**
     * 从数据库获取默认语言设置
     *
     * @return string
     */
    protected function getDefaultLocale(): string
    {
        return Multilingual::where('is_home_default', 1)->value('mark') ?: 'en';
    }
}
