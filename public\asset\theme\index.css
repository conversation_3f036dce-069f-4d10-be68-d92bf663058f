.bwms-page .banner {
  position: relative;
}
.bwms-page .banner .navigation-pagination .iconfont {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  background-color: rgba(0, 0, 0, 0);
  color: #fff;
  opacity: 0;
  transition: opacity 0.35s ease-in-out, background-color 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .banner .navigation-pagination .iconfont.icon-arrow-left {
  left: 20px;
}
.bwms-page .banner .navigation-pagination .iconfont.icon-arrow-right {
  right: 20px;
}
.bwms-page .banner .swiper-pagination {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;
}
.bwms-page .banner:hover .navigation-pagination .iconfont {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.3);
}
.bwms-page .recommend-module {
  background-color: #F6F6F6;
}
.bwms-page .recommend-module .tabs .tab-list {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .recommend-module .tabs .tab-list .tab-item {
  margin: 0 10px;
  border-radius: 30px;
  padding: 0px 20px;
  background-color: #fff;
  color: #6e6e6e;
  font-size: 16px;
  cursor: pointer;
  line-height: 2.625;
  transition: background 0.35s ease-in-out;
}
.bwms-page .recommend-module .tabs .tab-list .tab-item.active,
.bwms-page .recommend-module .tabs .tab-list .tab-item:hover {
  border-bottom: 0px solid #FD3C6B;
  background-image: linear-gradient(-45deg, #FFD100, #ff9600);
  color: #fff;
}
.bwms-page .recommend-module .tabs .pane-list {
  position: relative;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  transition: opacity 0.35s ease-in-out;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane.active {
  position: static;
  opacity: 1;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane .swiper-slide {
  padding: 30px;
  background-color: #fff;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane .swiper-slide .pic {
  width: 100%;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane .swiper-slide .text-box {
  margin-top: 13px;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane .swiper-slide .text-box a {
  color: #383838;
  font-size: 16px;
  line-height: 1.625;
  width: 100%;
  text-align: center;
  display: block;
}
.bwms-page .recommend-module .tabs .pane-list .tab-pane .swiper-pagination {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .about-module {
  background: url(../image/index/about-bg.jpg) no-repeat center center / cover;
}
.bwms-page .about-module .text-img {
  display: flex;
}
.bwms-page .about-module .text-img .left-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 40px 50px;
  background-color: #fff;
  width: 50%;
}
.bwms-page .about-module .text-img .left-text h6 {
  position: relative;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 40px;
  word-break: break-all;
}
.bwms-page .about-module .text-img .left-text h6:before {
  content: '';
  display: block;
  position: absolute;
  background: #FFD100;
  width: 30px;
  height: 3px;
  bottom: -15px;
  left: 0;
}
.bwms-page .about-module .text-img .left-text .desc {
  font-size: 14px;
  color: #6E6E6E;
  line-height: 2;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .about-module .text-img .left-text .btn-box {
  margin-top: 20px;
  background-color: #ff9600;
  border-radius: 50px;
  padding: 8px 14px;
  color: #fff;
  transition: background-color 1s cubic-bezier(0.175, 0.885, 0.32, 1) 0s;
  display: block;
}
.bwms-page .about-module .text-img .left-text .btn-box:hover {
  background-color: #FCB319;
}
.bwms-page .about-module .text-img .right-img {
  width: 50%;
  position: relative;
  overflow: hidden;
}
.bwms-page .about-module .text-img .right-img img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  object-fit: cover;
}
.bwms-page .case-module .list {
  margin-left: -1px;
  margin-right: -1px;
  margin-bottom: calc(-1px * 2);
}
.bwms-page .case-module .list .item {
  padding-left: 1px;
  padding-right: 1px;
  padding-bottom: calc(1px * 2);
}
.bwms-page .case-module .list .item .box {
  position: relative;
}
.bwms-page .case-module .list .item .box .mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 3;
  padding: 25px;
  background: url(../image/index/case-bg.png) no-repeat center center / cover;
  color: #fff;
  opacity: 0;
  transform: scale(1);
  transition: transform 0.35s ease-in-out, opacity 0.35s ease-in-out;
}
.bwms-page .case-module .list .item .box .mask h2 {
  margin-bottom: 10px;
  padding-top: 20px;
  font-size: 20px;
}
.bwms-page .case-module .list .item .box .mask p {
  font-size: 14px;
  line-height: 1.71;
}
.bwms-page .case-module .list .item .box .mask .btn-box {
  padding-bottom: 10px;
  font-size: 14px;
  position: absolute;
  bottom: 25px;
  right: 25px;
}
.bwms-page .case-module .list .item .box .text-box {
  padding: 25px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 65%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  flex-direction: column-reverse;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .case-module .list .item .box .text-box p {
  margin-bottom: 5px;
  color: #fff;
  font-size: 16px;
}
.bwms-page .case-module .list .item .box:hover .mask {
  opacity: 1;
  transform: scale(1.1);
}
.bwms-page .news-module {
  background-color: #F7F7F7;
}
.bwms-page .news-module .img-list {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .news-module .img-list .left-img {
  padding-right: 10px;
  width: 50%;
  position: relative;
}
.bwms-page .news-module .img-list .left-img .img {
  position: absolute;
  top: 0;
  left: 0;
  right: 10px;
  bottom: 0;
  z-index: 1;
}
.bwms-page .news-module .img-list .left-img .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.bwms-page .news-module .img-list .left-img .post-title {
  position: absolute;
  bottom: -30px;
  left: 30px;
  z-index: 2;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.06);
  padding: 20px;
  background: #fff;
  width: 65%;
  color: #333;
  transition: bottom 0.5s ease-in-out;
}
.bwms-page .news-module .img-list .left-img .post-title h3 a {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.55;
  display: block;
}
.bwms-page .news-module .img-list .left-img .post-title p {
  padding-top: 10px;
  color: #888888;
  font-size: 14px;
}
.bwms-page .news-module .img-list .left-img .post-title .btn-box {
  margin-top: 1rem;
  width: 48px;
  height: 48px;
  font-size: 18px;
  background-color: #FFD100;
  color: #fff;
  transition: all 0.5s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .news-module .img-list .left-img .post-title .btn-box .iconfont {
  font-size: 18px;
}
.bwms-page .news-module .img-list .left-img:hover .post-title {
  bottom: 30px;
}
.bwms-page .news-module .img-list .left-img:hover .post-title .btn-box {
  background-color: #ff9600;
}
.bwms-page .news-module .img-list .right-list {
  margin-bottom: -10px;
  padding-left: 10px;
  width: 50%;
}
.bwms-page .news-module .img-list .right-list .item {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-bottom: 10px;
  background-color: #fff;
}
.bwms-page .news-module .img-list .right-list .item .pic {
  width: 25%;
  flex-shrink: 0;
}
.bwms-page .news-module .img-list .right-list .item .time {
  padding: 10px 0;
  width: 16.66666667%;
  flex-shrink: 0;
}
.bwms-page .news-module .img-list .right-list .item .time .date {
  border-right: 2px solid #eee;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: space-between;
}
.bwms-page .news-module .img-list .right-list .item .time .date .day {
  font-size: 42px;
  line-height: 1.14;
  color: #6E6E6E;
  font-weight: bold;
}
.bwms-page .news-module .img-list .right-list .item .time .date .year-month {
  color: #888;
  font-size: 16px;
}
.bwms-page .news-module .img-list .right-list .item .tit-desc {
  padding: 15px 0 15px 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: space-between;
}
.bwms-page .news-module .img-list .right-list .item .tit-desc h2 a {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
  color: #383838;
  font-weight: bold;
  transition: color 0.35s ease-in-out;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .news-module .img-list .right-list .item .tit-desc p {
  color: #888888;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .news-module .img-list .right-list .item:hover .tit-desc h2 a {
  color: #ff9600;
}
