<?php

namespace Modules\Common\Util;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

class ContentUtil
{
    public static function parseEditorPlusContent($content): array
    {
        if (is_string($content)) {
            $content = trim($content);
        }
        if (is_string($content) && starts_with($content, '[')) {
            $content = @json_decode($content, true);
        }
        $filter = [];
        foreach ($content as $one) {
            BizException::throwsIf(empty($one['type']), Code::FAILED, '内容格式错误');
            switch ($one['type']) {
                case 'text':
                    BizException::throwsIf(empty($one['data']['content']), Code::FAILED, '内容格式错误');
                    BizException::throwsIf(! is_string($one['data']['content']), Code::FAILED, '内容格式错误');
                    $filter[] = [
                        'type' => $one['type'],
                        'data' => [
                            'content' => $one['data']['content']
                        ]
                    ];
                    break;
                case 'image':
                    BizException::throwsIf(empty($one['data']['image']), Code::FAILED, '内容格式错误');
                    BizException::throwsIf(! is_string($one['data']['image']), Code::FAILED, '内容格式错误');
                    $filter[] = [
                        'type' => $one['type'],
                        'data' => [
                            'image' => $one['data']['image']
                        ],
                    ];
                    break;
                case 'images':
                    BizException::throwsIf(empty($one['data']['images']), Code::FAILED, '内容格式错误');
                    BizException::throwsIf(! is_array($one['data']['images']), Code::FAILED, '内容格式错误');
                    $images = [];
                    foreach ($one['data']['images'] as $image) {
                        BizException::throwsIf(! is_string($image), Code::FAILED, '内容格式错误');
                        $images[] = $image;
                    }
                    $filter[] = [
                        'type' => $one['type'],
                        'data' => [
                            'images' => $images
                        ],
                    ];
                    break;
            }
        }
        return $filter;
    }
}
