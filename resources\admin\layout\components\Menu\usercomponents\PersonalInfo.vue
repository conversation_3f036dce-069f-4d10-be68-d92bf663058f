<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <h2></h2>
      <div class="btn-list">
        <el-button class="back-btn" @click="router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          <span>{{ t('Cms.detail.back') }}</span>
        </el-button>
        <el-button type="primary" @click="toggleEdit">
          <span>{{ t('dashboard.personalInfo.actions.save') }}</span>
        </el-button>
      </div>
    </div>
    <div class="module-con">
      <div class="box scroll-bar-custom">
        <el-skeleton :loading="loading" animated>
          <template #template>
            <div class="skeleton-content">
              <el-skeleton-item variant="circle" style="width: 100px; height: 100px" />
              <el-skeleton-item variant="text" style="width: 80%" />
              <el-skeleton-item variant="text" style="width: 60%" />
              <el-skeleton-item variant="text" style="width: 70%" />
              <el-skeleton-item variant="text" style="width: 80%" />
              <el-skeleton-item variant="text" style="width: 60%" />
              <el-skeleton-item variant="text" style="width: 70%" />
            </div>
          </template>
          <template #default>
            <div class="user-info-container">
              <!-- 左侧列 -->
              <div class="left-column">
                <el-form :model="userInfo" label-position="top" class="user-form">
                  <!-- 头像区域 -->
                  <div class="avatar-section">
                    <img v-if="userAvatarUrl" :src="userAvatarUrl" class="avatar" />
                    <div v-else class="avatar-placeholder">
                      <el-icon class="avatar-icon"><Plus /></el-icon>
                    </div>
                    <div class="avatar-actions">
                      <el-upload
                        class="avatar-upload"
                        :show-file-list="false" 
                        :accept="acceptedFileTypes"
                        :http-request="uploadAvatar">
                        <el-button type="primary">
                          <el-icon size="16"><Upload /></el-icon>
                          <span>{{ t('Cms.detail.upload') }}</span>
                        </el-button>
                      </el-upload>
                      <el-button class="el-button-default" v-if="userAvatarUrl" @click="handleDeleteAvatar">
                        <el-icon class="icon"><Remove size="16" /></el-icon>
                        <span>{{ t('Cms.detail.delete') }}</span>
                      </el-button>
                    </div>
                  </div>
                  
                  <!-- 左侧表单字段 -->
                  <el-form-item :label="t('dashboard.personalInfo.form.email')">
                    <el-input v-model="userInfo.email" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.email') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.name')">
                    <el-input v-model="userInfo.name" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.name') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.gender.label')">
                    <el-select v-model="userInfo.gender" :placeholder="t('dashboard.personalInfo.select_placeholder', { label: t('dashboard.personalInfo.form.gender.label') })" class="w-full">
                      <el-option :label="t('dashboard.personalInfo.form.gender.male')" value="male" />
                      <el-option :label="t('dashboard.personalInfo.form.gender.female')" value="female" />
                      <el-option :label="t('dashboard.personalInfo.form.gender.other')" value="other" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.company')">
                    <el-input v-model="userInfo.company" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.company') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.registrationTime')">
                    <el-input v-model="userInfo.signed_up" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.registrationTime') })" disabled />
                  </el-form-item>
                </el-form>
              </div>
              
              <!-- 右侧列 -->
              <div class="right-column">
                <el-form :model="userInfo" label-position="top" class="user-form">
                  <el-form-item :label="t('dashboard.personalInfo.form.username')">
                    <el-input v-model="userInfo.username" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.username') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.userId')">
                    <el-input v-model="userInfo.id" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.userId') })" disabled />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.phone')">
                    <el-input v-model="userInfo.phone" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.phone') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.title')">
                    <el-input v-model="userInfo.nickname" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.title') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.birthdate')">
                    <el-date-picker style="width: 100%" v-model="userInfo.birthdate" type="date" :placeholder="t('dashboard.personalInfo.select_placeholder', { label: t('dashboard.personalInfo.form.birthdate') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.idNumber')">
                    <el-input v-model="userInfo.idNumber" :placeholder="t('dashboard.personalInfo.input_placeholder', { label: t('dashboard.personalInfo.form.idNumber') })" />
                  </el-form-item>
                  
                  <el-form-item :label="t('dashboard.personalInfo.form.address')">
                    <el-input v-model="userInfo.address" />
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Remove, ArrowLeft } from '@element-plus/icons-vue'
import { useUserStore } from '/admin/stores/modules/user/userStore'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
import { useRouter } from 'vue-router'

const router = useRouter()

const { t } = useI18n()
const userStore = useUserStore()
const userInfo = reactive<any>({ ...userStore.userInfo })
const originalUserInfo = ref({ ...userStore.userInfo })
const userAvatarUrl = ref(userInfo.photo || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')
const loading = ref(true)
import { UserCenterService } from '../application/UserCenterService'
const acceptedFileTypes = 'image/jpeg,image/png,image/gif,image/bmp,image/webp'
const userCenterService = new UserCenterService()
onMounted(async () => {
  await userStore.fetchUserInfo()
  Object.assign(userInfo, userStore.userInfo)
  originalUserInfo.value = { ...userStore.userInfo }
  loading.value = false
})

const toggleEdit = async () => {
  loading.value = true
  try {
    const response: any = await userCenterService.updateUserInfo(userInfo)
    if (response.code === 200) {
      originalUserInfo.value = { ...userInfo }
      await userStore.updateUserInfo(userInfo)
      ElMessage.success(t('dashboard.personalInfo.message.updateSuccess'))
    } else {
      throw new Error(response.message || t('dashboard.personalInfo.message.updateError'))
    }
  } catch (error) {
    ElMessage.error(t('dashboard.personalInfo.message.updateError'))
  } finally {
    loading.value = false
  }
}

// 上传头像功能
const uploadAvatar = async (options: { file: File }) => {
  const { file } = options
  const isValidFormat = acceptedFileTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidFormat) {
    ElMessage.error(t('dashboard.personalInfo.form.avatar.formatError'))
    return
  }
  if (!isLt10M) {
    ElMessage.error(t('dashboard.personalInfo.form.avatar.sizeError'))
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('dir', '/images/test')
    formData.append('mode', 'OVERWRITE')

    const response = await http.post('/media/upload', formData)

    if (response && response.data && response.data.data && response.data.data.file && response.data.data.file.url) {
      const newAvatarUrl = response.data.data.file.url
      userAvatarUrl.value = newAvatarUrl
      ElMessage.success(t('dashboard.personalInfo.form.avatar.success'))

      userInfo.photo = newAvatarUrl

      await userCenterService.updateUserInfo(userInfo)
      await userStore.updateUserInfo(userInfo)
      originalUserInfo.value = { ...userInfo }
    } else {
      throw new Error('上传失败')
    }
  } catch (error) {
    ElMessage.error(t('dashboard.personalInfo.form.avatar.error'))
  }
}

// 删除头像功能
const handleDeleteAvatar = async () => {
  // 重置头像为默认头像
  userAvatarUrl.value = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
  userInfo.photo = ''
}
</script>

<style scoped lang="scss">
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.user-info-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 26px;
}

.left-column, .right-column {
  flex: 1;
  min-width: 300px;
  max-width: calc(50% - 15px);
}

.left-column {
  .user-form {
    width: 100%;
  }
}

.right-column {
  .user-form {
    width: 100%;
  }
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 18px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8e8e8;
  padding: 2px;
}

.avatar-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px dashed #d9d9d9;
  
  .avatar-icon {
    font-size: 28px;
    color: #8c939d;
  }
}

.avatar-actions {
  display: flex;
  gap: 8px;
}

.avatar-upload {
  display: inline-flex;
}

.w-full {
  width: 100%;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

@media screen and (max-width: 992px) {
  .left-column, .right-column {
    max-width: 100%;
  }
  
  .user-info-container {
    flex-direction: column;
  }
}
</style>


