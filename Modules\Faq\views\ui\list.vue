<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <el-button @click="batchOnline" class="button-no-border" :disabled="checkedFaqs.length === 0">
          <span>批量上線</span>
        </el-button>
        <el-button @click="batchOffline" class="button-no-border" :disabled="checkedFaqs.length === 0">
          <span>批量停用</span>
        </el-button>
        <el-button @click="batchDelete" class="button-no-border" :disabled="checkedFaqs.length === 0">
          <span>批量刪除</span>
        </el-button>
        <el-button @click="refreshPage" class="button-no-border">
          <el-icon><Refresh /></el-icon>
          <span>刷新</span>
        </el-button>
      <FilterPopover
        v-model="filterDialog"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="" />
            </el-icon>
            <span>篩選</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="FAQ 編號">
            <el-input v-model="search.faqNumber" placeholder="請輸入FAQ編號" size="large" />
          </el-form-item>

          <el-form-item label="問題標題">
            <el-input v-model="search.title" placeholder="請輸入問題標題" size="large" />
          </el-form-item>

          <el-form-item label="分類">
            <el-select v-model="search.category" placeholder="請選擇分類" size="large">
              <el-option label="所有分類" value="" />
              <el-option label="財經類" value="finance" />
              <el-option label="軍事類" value="military" />
              <el-option label="科技類" value="technology" />
            </el-select>
          </el-form-item>

          <el-form-item label="狀態">
            <el-select v-model="search.status" placeholder="請選擇狀態" size="large">
              <el-option label="所有狀態" value="" />
              <el-option label="已發佈" value="published" />
              <el-option label="草稿" value="draft" />
              <el-option label="已停用" value="disabled" />
            </el-select>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshFaq">
              <el-icon size="16">
                <Refresh />
              </el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="searchFaq" type="primary">
              <el-icon size="16">
                <Filter />
              </el-icon>
              <span>篩選</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button @click="addFaq" type="primary">
        <el-icon>
          <Plus />
        </el-icon>
        <span>新增</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table ref="tableRefs" :data="faqList" style="width: 100%; height: 100%" @selection-change="checkedFaqHandle" v-loading="loading">
          <template #empty>
            <el-empty description="暫無數據" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column prop="faqNumber" label="FAQ 編號" width="150" />
          <el-table-column prop="title" label="問題標題" min-width="300">
            <template #default="scope">
              <el-tooltip
                append-to="body"
                effect="dark"
                :content="scope.row.title"
                placement="bottom-start"
                :show-after="200"
              >
                <div class="faq-title">{{ scope.row.title }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分類" width="120" />
          <el-table-column prop="status" label="狀態" width="120">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新時間" width="180" />
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editFaqHandle(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="viewFaqHandle(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/ViewIcon.png')" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn del-btn" @click="delFaqHandle(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每頁顯示</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  暫無數據
                </div>
              </template>
            </el-select>
            <span class="total-text">共 {{ total }} 條記錄</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 1. 引入详情组件 -->
    <DetailList v-model="detailDialogVisible" :faq-id="detailFaqId" :key="detailFaqId" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '/admin/support/http'
import { Filter, Plus, Loading, Refresh } from '@element-plus/icons-vue'

// FAQ接口定义
interface Faq {
  id: number
  faqNumber: string
  title: string
  category: string
  status: string
  updateTime: string
  content?: string
}

const api = 'faq'
const router = useRouter()
const route = useRoute()

// FAQ数据
const tableRefs = ref<any>(null)
const faqList = ref<Faq[]>([
  {
    id: 1,
    faqNumber: 'FAQ-001',
    title: '什麼是AI智能？',
    category: '科技類',
    status: '已發佈',
    updateTime: '2024-07-20 10:00:00',
    content: '<p>AI智能是人工智能的簡稱...</p>'
  },
  {
    id: 2,
    faqNumber: 'FAQ-002',
    title: '如何註冊賬號？',
    category: '科技類',
    status: '草稿',
    updateTime: '2024-07-19 09:30:00',
    content: '<p>請點擊右上角註冊按鈕...</p>'
  },
  {
    id: 3,
    faqNumber: 'FAQ-003',
    title: '如何重置密碼？',
    category: '科技類',
    status: '已停用',
    updateTime: '2024-07-18 08:20:00',
    content: '<p>請在登錄頁點擊忘記密碼...</p>'
  }
])
const loading = ref(false)
const checkedFaqs = ref<Faq[]>([])

// 1. 引入详情组件
import DetailList from './components/detailList.vue'

// 2. 添加详情弹窗相关变量
const detailDialogVisible = ref(false)
const detailFaqId = ref<number|null>(null)

// 监听dialog关闭，重置id，确保能再次打开
watch(detailDialogVisible, (val) => {
  if (!val) detailFaqId.value = null
})

// 修改获取FAQ列表的方法，使用正确的类型
async function getFaqList() {
  loading.value = true

  try {
    // 构建请求参数
    const params: any = {
      page: page.value,
      limit: limit.value
    }

    // 添加搜索条件
    if (search.faqNumber) {
      params.faqNumber = search.faqNumber
    }

    if (search.title) {
      params.title = search.title
    }

    if (search.category) {
      params.category_id = search.category
    }

    if (search.status) {
      params.status = search.status
    }

    // 调用获取FAQ列表接口
    const response = await http.get('/admin/faq', params)

    if (response.data && response.data.code === 200) {
      faqList.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      // 错误处理
      ElMessage.error(response.data?.message || '獲取數據失敗')
      // 如果请求失败但状态码不是200，使用空数据
      faqList.value = []
      total.value = 0
    }
  } catch (error) {
    faqList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页器
let page = ref(1)
let limit = ref(20)
let total = ref(0)
const changePage = () => {
  getFaqList()
}


// 搜索
const filterDialog = ref(false)
const search = reactive({
  faqNumber: '',
  title: '',
  category: '',
  status: '',
})
const searchFaq = async () => {
  page.value = 1 // 搜索时重置到第一页
  await getFaqList()
  filterDialog.value = false
}
const refreshFaq = () => {
  // 重置搜索条件
  search.faqNumber = ''
  search.title = ''
  search.category = ''
  search.status = ''

  // 重置分页
  page.value = 1
  limit.value = 20

  // 重新获取数据
  getFaqList()
  filterDialog.value = false
}

// 页面初始化
const pageInit = async () => {
  await getFaqList()
}

const checkedFaqHandle = (val: Faq[]) => {
  checkedFaqs.value = val
}

const editFaqHandle = async (faq: Faq) => {
  // 跳转到编辑页面
  router.push(`/faq/edit/${faq.id}`)
}

// 3. 修改viewFaqHandle
const viewFaqHandle = (faq: Faq) => {
  detailFaqId.value = faq.id
  detailDialogVisible.value = false
  nextTick(() => {
    detailDialogVisible.value = true
  })
}

const delFaqHandle = (faq?: Faq) => {
  if (!faq) return
  ElMessageBox.confirm(
    `確定要刪除FAQ「${faq.title}」嗎？此操作不可恢復！`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 调用删除接口
      await http.delete(`/admin/faq/${faq.id}`)
      ElMessage.success('刪除成功')
    } catch (error) {
      ElMessage.error('刪除失敗')
    }
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}

const addFaq = () => {
  router.push('/faq/create')
}

// 批量操作
const batchOnline = async () => {
  if (checkedFaqs.value.length === 0) {
    ElMessage.warning('請先選擇要上線的FAQ')
    return
  }

  try {
    const ids = checkedFaqs.value.map(item => item.id)

    // 调用批量上線接口
    const response = await http.put('/admin/faq/batch/shelf', { ids })

    if (response.data && response.data.code === 200) {
      ElMessage.success('批量上線成功')
      checkedFaqs.value = []
      getFaqList()
    } else {
      ElMessage.error(response.data?.message || '批量上線失敗')
    }
  } catch (error) {
    console.error('批量上線失败:', error)
    ElMessage.error('批量上線失敗')
  }
}

const batchOffline = async () => {
  if (checkedFaqs.value.length === 0) {
    ElMessage.warning('請先選擇要停用的FAQ')
    return
  }

  try {
    const ids = checkedFaqs.value.map(item => item.id)

    // 调用批量停用接口
    const response = await http.put('/admin/faq/batch/unshelf', { ids })

    if (response.data && response.data.code === 200) {
      ElMessage.success('批量停用成功')
      checkedFaqs.value = []
      getFaqList()
    } else {
      ElMessage.error(response.data?.message || '批量停用失敗')
    }
  } catch (error) {
    console.error('批量停用失败:', error)
    ElMessage.error('批量停用失敗')
  }
}

const batchDelete = async () => {
  if (checkedFaqs.value.length === 0) {
    ElMessage.warning('請先選擇要刪除的FAQ')
    return
  }

  ElMessageBox.confirm(`確定要刪除選中的 ${checkedFaqs.value.length} 個FAQ嗎？此操作不可恢復！`, '警告', {
    confirmButtonText: '確定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(async () => {
    try {
      const ids = checkedFaqs.value.map(item => item.id)

      // 调用批量删除接口
      const response = await http.delete('/admin/faq/batch', { ids })

      if (response.data && response.data.code === 200) {
        ElMessage.success('批量刪除成功')
        checkedFaqs.value = []
        getFaqList()
      } else {
        ElMessage.error(response.data?.message || '批量刪除失敗')
      }
    } catch (error) {
    }
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}

// 刷新页面
const refreshPage = () => {
  // 重置所有状态
  search.faqNumber = ''
  search.title = ''
  search.category = ''
  search.status = ''
  page.value = 1
  limit.value = 20
  checkedFaqs.value = []

  // 重新获取数据
  getFaqList()
  ElMessage.success('頁面已刷新')
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已發佈':
      return 'success'
    case '草稿':
      return 'warning'
    case '已停用':
      return 'danger'
    default:
      return 'info'
  }
}

onMounted(() => {
  // pageInit()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.flex {
  display: flex;
}

.w-1\/2 {
  width: 50%;
}

.w-full {
  width: 100%;
}

.gap-4 {
  gap: 1rem;
}


.faq-title {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}
</style>
