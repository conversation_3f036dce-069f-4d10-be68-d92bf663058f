<?php

namespace Modules\Region\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * 区域频道关联模型
 * 对应数据库表：tvb_regions_channel
 */
class RegionChannel extends Pivot
{
    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'regions_channel';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'regions_id',     // 区域ID
        'channel_id',     // 频道ID
        'created_by',     // 创建人ID
        'updated_by',     // 更新人ID
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * 序列化时的日期格式
     * @var string
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 数组/JSON序列化时的日期格式
     * @var array
     */
    protected $casts = [
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 序列化时间字段为指定格式
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    }
}