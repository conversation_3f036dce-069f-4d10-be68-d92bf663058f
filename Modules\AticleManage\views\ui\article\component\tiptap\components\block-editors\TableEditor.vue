<template>
  <div class="edit-section">
    <el-form label-position="top">
      <div class="table-options">
        <el-form-item label="表格风格">
          <el-select v-model="tableStyle" @change="updateTableContent" style="width: 100%">
            <el-option label="默认" value="default" />
            <el-option label="条纹" value="striped" />
            <el-option label="带边框" value="bordered" />
            <el-option label="悬停效果" value="hover" />
            <el-option label="深色表格" value="dark" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否使用小型表格">
          <el-switch v-model="tableSmall" @change="updateTableContent" />
        </el-form-item>
        
        <el-form-item label="是否采用紧凑样式">
          <el-switch v-model="tableCompact" @change="updateTableContent" />
        </el-form-item>
        
        <el-form-item label="是否为响应式表格">
          <el-switch v-model="tableResponsive" @change="updateTableContent" />
        </el-form-item>
        
        <el-form-item label="表格标题">
          <el-input v-model="tableCaption" @input="updateTableContent" placeholder="表格标题" />
        </el-form-item>
        
        <el-form-item label="列数">
          <el-input-number v-model="tableColumns" :min="1" :max="10" @change="updateTableColumnsCount" />
        </el-form-item>
        
        <el-form-item label="行数">
          <el-input-number v-model="tableRows" :min="1" :max="20" @change="updateTableRowsCount" />
        </el-form-item>
      </div>
      
      <div class="table-headers">
        <h4>表头设置</h4>
        <div class="header-items">
          <div v-for="(header, index) in tableHeaders" :key="`header-${index}`" class="header-item">
            <el-form-item :label="`列 ${index + 1} 标题`">
              <el-input v-model="tableHeaders[index]" @input="updateTableContent" />
            </el-form-item>
          </div>
        </div>
      </div>
      
      <div class="table-cells">
        <h4>表格数据</h4>
        <div class="table-rows">
          <div v-for="(row, rowIndex) in tableData" :key="`row-${rowIndex}`" class="table-row">
            <div class="row-header">
              <span>行 {{ rowIndex + 1 }}</span>
              <el-button 
                class="remove-button" 
                @click="removeTableRow(rowIndex)" 
                v-if="tableData.length > 1" 
                type="danger" 
                size="small" 
                :icon="Delete"
              />
            </div>
            <div class="row-cells">
              <el-form-item 
                v-for="(cell, colIndex) in row" 
                :key="`cell-${rowIndex}-${colIndex}`" 
                :label="`列 ${colIndex + 1}`" 
                class="cell-item"
              >
                <el-input v-model="tableData[rowIndex][colIndex]" @input="updateTableContent" />
              </el-form-item>
            </div>
          </div>
          <el-button class="add-button" @click="addTableRow" type="primary" plain>添加行</el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Delete } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'TableEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update-block'])

// 表格数据
const tableStyle = ref('default')
const tableSmall = ref(false)
const tableCompact = ref(false)
const tableResponsive = ref(true)
const tableCaption = ref('')
const tableColumns = ref(3)
const tableRows = ref(3)
const tableHeaders = ref<string[]>(['标题 1', '标题 2', '标题 3'])
const tableData = ref<string[][]>([
  ['数据 1-1', '数据 1-2', '数据 1-3'],
  ['数据 2-1', '数据 2-2', '数据 2-3'],
  ['数据 3-1', '数据 3-2', '数据 3-3']
])

// 组件挂载时，从现有块元素中提取表格数据
onMounted(() => {
  if (props.blockElement) {
    // 获取表格内容
    const tableEl = props.blockElement.querySelector('table')
    
    if (tableEl) {
      // 获取表格样式
      tableStyle.value = 'default'
      if (tableEl.classList.contains('table-striped')) {
        tableStyle.value = 'striped'
      } else if (tableEl.classList.contains('table-bordered')) {
        tableStyle.value = 'bordered'
      } else if (tableEl.classList.contains('table-hover')) {
        tableStyle.value = 'hover'
      } else if (tableEl.classList.contains('table-dark')) {
        tableStyle.value = 'dark'
      }
      
      // 获取表格大小
      tableSmall.value = tableEl.classList.contains('table-sm')
      
      // 获取是否紧凑
      tableCompact.value = false // Bootstrap 5 移除了 table-condensed 类，但保留选项以向后兼容
      
      // 获取是否响应式
      tableResponsive.value = props.blockElement.classList.contains('table-responsive')
      
      // 获取表格标题
      const captionEl = tableEl.querySelector('caption')
      tableCaption.value = captionEl ? captionEl.textContent || '' : ''
      
      // 获取表头
      const headerCells = tableEl.querySelectorAll('thead th')
      if (headerCells.length) {
        tableHeaders.value = Array.from(headerCells).map(cell => (cell as Element).textContent || '')
        tableColumns.value = tableHeaders.value.length
      }
      
      // 获取表格数据
      const rows = tableEl.querySelectorAll('tbody tr')
      if (rows.length) {
        tableData.value = []
        rows.forEach((row: Element) => {
          const cells = row.querySelectorAll('td')
          if (cells.length) {
            tableData.value.push(Array.from(cells).map(cell => (cell as Element).textContent || ''))
          }
        })
        tableRows.value = tableData.value.length
      }
    }
  }
})

const updateTableColumnsCount = () => {
  // 更新表头
  const oldHeadersLength = tableHeaders.value.length
  tableHeaders.value = tableHeaders.value.slice(0, tableColumns.value)
  for (let i = oldHeadersLength; i < tableColumns.value; i++) {
    tableHeaders.value.push(`标题 ${i + 1}`)
  }
  
  // 更新表格数据
  tableData.value.forEach((row, rowIndex) => {
    const oldRowLength = row.length
    tableData.value[rowIndex] = row.slice(0, tableColumns.value)
    for (let i = oldRowLength; i < tableColumns.value; i++) {
      tableData.value[rowIndex].push(`数据 ${rowIndex + 1}-${i + 1}`)
    }
  })
  
  updateTableContent()
}

const updateTableRowsCount = () => {
  // 更新表格数据行数
  const oldRowsLength = tableData.value.length
  tableData.value = tableData.value.slice(0, tableRows.value)
  
  for (let i = oldRowsLength; i < tableRows.value; i++) {
    const newRow: string[] = []
    for (let j = 0; j < tableColumns.value; j++) {
      newRow.push(`数据 ${i + 1}-${j + 1}`)
    }
    tableData.value.push(newRow)
  }
  
  updateTableContent()
}

const addTableRow = () => {
  const newRow: string[] = []
  for (let i = 0; i < tableColumns.value; i++) {
    newRow.push(`数据 ${tableData.value.length + 1}-${i + 1}`)
  }
  tableData.value.push(newRow)
  tableRows.value = tableData.value.length
  
  updateTableContent()
}

const removeTableRow = (index: number) => {
  tableData.value.splice(index, 1)
  tableRows.value = tableData.value.length
  
  updateTableContent()
}

const updateTableContent = () => {
  // 构建表头
  let headersHtml = '<tr>'
  tableHeaders.value.forEach(header => {
    headersHtml += `<th scope="col">${header}</th>`
  })
  headersHtml += '</tr>'
  
  // 构建表格数据
  let rowsHtml = ''
  tableData.value.forEach(row => {
    rowsHtml += '<tr>'
    row.forEach(cell => {
      rowsHtml += `<td>${cell}</td>`
    })
    rowsHtml += '</tr>'
  })
  
  // 构建表格类
  const tableClasses = [
    'table',
    tableStyle.value === 'striped' ? 'table-striped' : '',
    tableStyle.value === 'bordered' ? 'table-bordered' : '',
    tableStyle.value === 'hover' ? 'table-hover' : '',
    tableStyle.value === 'dark' ? 'table-dark' : '',
    tableSmall.value ? 'table-sm' : '',
    tableCompact.value ? 'table-condensed' : ''
  ].filter(Boolean).join(' ')
  
  const captionHtml = tableCaption.value ? `<caption>${tableCaption.value}</caption>` : ''
  
  // 构建完整表格
  const html = `
    <div data-bs-component="table" class="${tableResponsive.value ? 'table-responsive' : ''}">
      <table class="${tableClasses}">
        ${captionHtml}
        <thead>
          ${headersHtml}
        </thead>
        <tbody>
          ${rowsHtml}
        </tbody>
      </table>
    </div>
  `
  
  emit('update-block', { html })
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
}

.table-headers,
.table-cells {
  margin-top: 20px;
}

h4 {
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
  color: #303133;
}

.table-row {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  background-color: #F8F9FA;
  margin-bottom: 15px;
}

.row-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 600;
}

.row-cells {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.add-button {
  margin-top: 10px;
  width: 100%;
}
</style> 