header {
  position: sticky;
  top: 0;
  background-color: #fff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03);
  z-index: 99;
  width: 100%;
}
header .header-con {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
  max-width: 1500px;
}
header .header-con .logo {
  width: 125px;
}
header .header-con nav {
  flex-grow: 1;
}
header .header-con nav > ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
header .header-con nav > ul > li {
  position: relative;
}
header .header-con nav > ul > li > a {
  padding-left: 16px;
  padding-right: 16px;
  color: #3e3e3e;
  font-size: 16px;
  line-height: 5.625;
  display: block;
  transition: color 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
header .header-con nav > ul > li > a .iconfont {
  margin-left: 4px;
  font-size: 12px;
  display: block;
  transform: rotate(0);
  transition: transform 0.35s ease-in-out;
}
header .header-con nav > ul > li.active a,
header .header-con nav > ul > li:hover a {
  color: #ff9600;
}
header .header-con nav > ul > li:hover .sub-menu {
  display: flex;
}
header .header-con nav > ul > li:hover a .iconfont {
  transform: rotate(90deg);
}
header .header-con nav > ul > li .sub-menu {
  position: absolute;
  left: 0;
  top: 100%;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: none;
}
header .header-con nav > ul > li .sub-menu ul {
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0 5px 15px -8px rgba(0, 0, 0, 0.175);
}
header .header-con nav > ul > li .sub-menu ul li a {
  padding: 14px 16px;
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  display: block;
  transition: all 0.35s ease-in-out;
}
header .header-con nav > ul > li .sub-menu ul li a:hover {
  background-color: #ff9600;
  color: #fff;
}
header .header-con .more {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  text-align: center;
}
header .header-con .more .site-select {
  color: #6E6E6E;
  font-size: 16px;
  position: relative;
  line-height: 32px;
  width: 60px;
}
header .header-con .more .site-select .site-list {
  padding-top: 18px;
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: none;
}
header .header-con .more .site-select .site-list .single {
  border: 6px solid transparent;
  border-bottom-color: #eee;
}
header .header-con .more .site-select .site-list ul {
  background-color: #fff;
}
header .header-con .more .site-select .site-list ul li a {
  padding: 10px 12px;
  text-align: center;
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  display: block;
  transition: background-color 0.35s ease-in-out;
}
header .header-con .more .site-select .site-list ul li a:hover {
  background-color: #f5f5f5;
}
header .header-con .more .site-select:hover .site-list {
  display: flex;
}
header .header-con .more .lang-select {
  color: #6E6E6E;
  font-size: 16px;
  position: relative;
  line-height: 32px;
  width: 32px;
}
header .header-con .more .lang-select .lang-list {
  padding-top: 18px;
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: none;
}
header .header-con .more .lang-select .lang-list .single {
  border: 6px solid transparent;
  border-bottom-color: #eee;
}
header .header-con .more .lang-select .lang-list ul {
  background-color: #fff;
}
header .header-con .more .lang-select .lang-list ul li a {
  padding: 10px 12px;
  text-align: center;
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  display: block;
  transition: background-color 0.35s ease-in-out;
}
header .header-con .more .lang-select .lang-list ul li a:hover {
  background-color: #f5f5f5;
}
header .header-con .more .lang-select:hover .lang-list {
  display: flex;
}
header .header-con .more .iconfont {
  font-size: 20px;
  width: 32px;
  height: 32px;
  color: #6E6E6E;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
header .header-con .more .iconfont.icon-user {
  margin-left: 14px;
  border-radius: 50%;
  background-color: #ECEFF5;
}
header .header-con .more .user-avatar {
  margin-left: 14px;
  position: relative;
}
header .header-con .more .user-avatar .pic {
  border-radius: 50%;
  overflow: hidden;
  width: 32px;
  height: 32px;
  display: block;
}
header .header-con .more .user-avatar .drop-box {
  padding-top: 18px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: none;
}
header .header-con .more .user-avatar .drop-box .single {
  border: 6px solid transparent;
  border-bottom-color: #eee;
}
header .header-con .more .user-avatar .drop-box .drop-list {
  padding: 12px 20px;
  background-color: #fff;
  box-shadow: 9px 13px 12px rgba(0, 0, 0, 0.2);
}
header .header-con .more .user-avatar .drop-box .drop-list ul li {
  color: #666;
  font-size: 14px;
  line-height: 2.28;
  transition: color 0.35s ease-in-out;
}
header .header-con .more .user-avatar .drop-box .drop-list ul li a {
  white-space: nowrap;
  color: #666;
  font-size: 14px;
  line-height: 2.28;
  transition: color 0.35s ease-in-out;
}
header .header-con .more .user-avatar .drop-box .drop-list ul li:hover {
  color: #ff9600;
}
header .header-con .more .user-avatar .drop-box .drop-list ul li:hover a {
  color: #ff9600;
}
header .header-con .more .user-avatar:hover .drop-box {
  display: flex;
}
