<?php

namespace Modules\Region\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Modules\Region\Models\Region;
use Modules\Region\Services\RegionService;
use Modules\Region\Enums\RegionErrorCode;

/**
 * 区域管理控制器
 */
class RegionController extends Controller
{
    protected $regionService;

    public function __construct(RegionService $regionService)
    {
        $this->regionService = $regionService;
    }

    /**
     * 获取区域列表
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        try {
            // 获取请求参数
            $params = [
                'name' => $request->input('name', ''),
                'status' => $request->input('status', ''),
                'sort' => $request->input('order_by', 'sort'),
                'order' => $request->input('order_dir', 'asc'),
                'per_page' => (int) $request->input('limit', 20)
            ];

            // 调用Service层获取区域列表（包含频道数量）
            $data = $this->regionService->getRegionListWithChannelCount($params);

        // 返回统一格式
        return [
                'code' => 200,
                'message' => Lang::get('Region::region.success'),
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_LIST_FAILED->value,
                'message' => Lang::get('Region::region.region_list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 创建区域
     * @param Request $request
     * @return array
     */
    public function store(Request $request): array
    {
        try {
        // 验证请求参数
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:regions,name,NULL,id,deleted_at,NULL',
            'description' => 'nullable|string',
            'status' => 'integer|in:0,1',
            'sort' => 'nullable|integer|min:0|max:9999'
            ], [
                'name.required' => Lang::get('Region::region.name_required'),
                'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
                'status.in' => Lang::get('Region::region.status_invalid'),
                'sort.integer' => Lang::get('Region::region.sort_invalid'),
        ]);

        $validated['created_by'] = 1;
        $validated['updated_by'] = 1;

            // 调用Service层创建区域
            $region = $this->regionService->createRegion($validated);

        // 返回创建的数据
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.create_success'),
                'data' => $region->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_CREATE_FAILED->value,
                'message' => Lang::get('Region::region.create_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取单个区域
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        try {
            // 调用Service层获取区域
            $region = $this->regionService->getRegionById($id);

        if (!$region) {
                return [
                    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.region_not_found'),
                    'data' => null
                ];
        }

        // 返回详情
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.success'),
                'data' => $region->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_QUERY_FAILED->value,
                'message' => Lang::get('Region::region.region_query_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新区域
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, int $id): array
    {
        try {
        // 验证请求参数
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:regions,name,' . $id . ',id,deleted_at,NULL',
            'description' => 'nullable|string',
            'status' => 'integer|in:0,1',
            'sort' => 'nullable|integer|min:0|max:9999'
            ], [
                'name.required' => Lang::get('Region::region.name_required'),
                'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
                'status.in' => Lang::get('Region::region.status_invalid'),
                'sort.integer' => Lang::get('Region::region.sort_invalid'),
        ]);

        $validated['updated_by'] = 1;

            // 调用Service层更新区域
            $region = $this->regionService->updateRegion($id, $validated);

            if (!$region) {
                return [
                    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.region_not_found'),
                    'data' => null
                ];
            }

        // 返回更新后的数据
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.update_success'),
                'data' => $region->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_UPDATE_FAILED->value,
                'message' => Lang::get('Region::region.update_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 删除区域
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        try {
            // 调用Service层删除区域
            $result = $this->regionService->deleteRegion($id);

            if (!$result) {
                return [
                    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.region_not_found'),
                    'data' => null
                ];
        }

            // 返回删除结果
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.delete_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('Region::region.delete_success'),
                    'affected_count' => 1
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_DELETE_FAILED->value,
                'message' => Lang::get('Region::region.delete_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新区域状态
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function updateStatus(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'status' => 'required|integer|in:0,1'
            ], [
                'status.required' => Lang::get('Region::region.status_required'),
                'status.in' => Lang::get('Region::region.status_invalid'),
            ]);

            // 调用Service层更新状态
            $region = $this->regionService->updateRegionStatus($id, $validated['status']);

        if (!$region) {
                return [
                    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
                    'message' => Lang::get('Region::region.region_not_found'),
                    'data' => null
                ];
            }

            // 返回更新后的数据
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.status_update_success'),
                'data' => $region->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'errors' => $e->errors()
            ];
        } catch (\Exception $e) {
            return [
                'code' => RegionErrorCode::REGION_STATUS_UPDATE_FAILED->value,
                'message' => Lang::get('Region::region.status_update_failed'),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 批量操作
     * @param Request $request
     * @return array
     */
    public function batchAction(Request $request): array
    {
        try {
        // 验证请求参数
        $validated = $request->validate([
                'action' => 'required|string|in:delete,enable,disable',
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:regions,id'
            ], [
                'action.required' => Lang::get('Region::region.action_required'),
                'action.in' => Lang::get('Region::region.action_invalid'),
                'ids.required' => Lang::get('Region::region.ids_required'),
                'ids.array' => Lang::get('Region::region.ids_invalid'),
            ]);

            // 调用Service层执行批量操作
            $result = $this->regionService->batchAction($validated['action'], $validated['ids']);

            // 返回批量操作结果
            return [
                'code' => 200,
                'message' => Lang::get('Region::region.batch_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('Region::region.batch_success'),
                    'affected_count' => $result['affected_count'] ?? 0,
                    'action' => $validated['action']
                ]
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
                'message' => Lang::get('Region::region.validation_failed'),
                'errors' => $e->errors()
                ];
        } catch (\Exception $e) {
                    return [
                'code' => RegionErrorCode::REGION_BATCH_ACTION_FAILED->value,
                'message' => Lang::get('Region::region.batch_failed'),
                'error' => $e->getMessage()
            ];
        }
    }
} 