<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Common\Models\BaseModel;

/** 
 * 笔名模型
 *
 * @property int $id 笔名ID
 * @property string $pen_name 笔名
 * @property int $pen_name_type 笔名类型: 1-记者, 2-编辑, 3-特约, 4-其他
 * @property string|null $description 笔名描述
 * @property int $created_by 创建者ID
 * @property int $status 状态: 0-禁用, 1-启用
 * @property int $usage_count 使用次数
 * @property \Carbon\Carbon|null $last_used_at 最后使用时间
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class PenName extends BaseModel
{
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'pen_names';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'pen_name',
        'pen_name_type',
        'description',
        'created_by',
        'status',
        'usage_count',
        'last_used_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_deleted',
    ];

    /**
     * 字段类型转换
     * @var array
     */
    protected $casts = [
        'pen_name_type' => 'integer',
        'created_by' => 'integer',
        'status' => 'integer',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 获取创建者
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by', 'id');
    }

    /**
     * 获取使用此笔名的管理员
     * @return BelongsToMany
     */
    public function admins(): BelongsToMany
    {
        return $this->belongsToMany(Admin::class, 'tvb_admin_pen_names', 'pen_name_id', 'admin_id')
            ->withPivot(['is_default'])
            ->withTimestamps();
    }

    /**
     * 检查笔名是否启用
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 获取笔名类型文本
     * @return string
     */
    public function getPenNameTypeTextAttribute(): string
    {
        $types = [
            1 => '真名',
            2 => '假名',
            3 => '自家来源',
            4 => '其他',
        ];
        return $types[$this->pen_name_type] ?? '未知';
    }

    /**
     * 增加使用次数
     * @return void
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * 减少使用次数
     * @return void
     */
    public function decrementUsage(): void
    {
        $this->decrement('usage_count');
    }

    /**
     * 检查是否正在使用
     * @return bool
     */
    public function isInUse(): bool
    {
        return $this->usage_count > 0;
    }

    /**
     * 查询作用域：启用的笔名
     * @param $query
     * @return mixed
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 查询作用域：按类型筛选
     * @param $query
     * @param int $type
     * @return mixed
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('pen_name_type', $type);
    }

    /**
     * 查询作用域：按创建者筛选
     * @param $query
     * @param int $createdBy
     * @return mixed
     */
    public function scopeByCreator($query, int $createdBy)
    {
        return $query->where('created_by', $createdBy);
    }

    /**
     * 查询作用域：按笔名模糊搜索
     * @param $query
     * @param string $name
     * @return mixed
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('pen_name', 'like', "%{$name}%");
    }

    /**
     * 查询作用域：正在使用的笔名
     * @param $query
     * @return mixed
     */
    public function scopeInUse($query)
    {
        return $query->where('usage_count', '>', 0);
    }

    /**
     * 查询作用域：未使用的笔名
     * @param $query
     * @return mixed
     */
    public function scopeNotInUse($query)
    {
        return $query->where('usage_count', 0);
    }

    /**
     * 查询作用域：按使用次数排序
     * @param $query
     * @param string $direction
     * @return mixed
     */
    public function scopeOrderByUsage($query, string $direction = 'desc')
    {
        return $query->orderBy('usage_count', $direction);
    }

    /**
     * 查询作用域：按最后使用时间排序
     * @param $query
     * @param string $direction
     * @return mixed
     */
    public function scopeOrderByLastUsed($query, string $direction = 'desc')
    {
        return $query->orderBy('last_used_at', $direction);
    }
}
