.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .contact-info {
  background-color: #FFFFFF;
}
.bwms-page .contact-info .container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .contact-info .container .left {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  flex-grow: 1;
}
.bwms-page .contact-info .container .left .module-tit {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .contact-info .container .left .module-tit h4 {
  margin-bottom: 20px;
  font-size: 36px;
  color: #333;
  line-height: 1.3;
}
.bwms-page .contact-info .container .left .module-tit .desc {
  font-size: 16px;
  line-height: 1.75;
}
.bwms-page .contact-info .container .left .info-list {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .contact-info .container .left .info-list .item {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .contact-info .container .left .info-list .item .iconfont {
  font-size: 42px;
  color: #121212;
}
.bwms-page .contact-info .container .left .info-list .item .info {
  padding-left: 20px;
}
.bwms-page .contact-info .container .left .info-list .item .info .label {
  margin-bottom: 5px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  line-height: 1.5;
}
.bwms-page .contact-info .container .left .info-list .item .info .details {
  color: #333;
  line-height: 1.5;
  font-size: 16px;
}
.bwms-page .contact-info .container .left .info-list .item .info .details a {
  color: #333;
  line-height: 1.5;
  font-size: 16px;
}
.bwms-page .contact-info .container .right {
  border: 1px solid #eee;
  padding: 20px 30px;
  text-align: center;
}
.bwms-page .contact-info .container .right .label {
  font-size: 16px;
  color: #333;
  line-height: 1.75;
}
.bwms-page .contact-info .container .right .text {
  color: #ff9600;
  font-size: 16px;
  line-height: 1.75;
}
.bwms-page .contact-info .container .right .pic {
  max-width: 233px;
}
.bwms-page .feedback-module {
  background-color: #CACACA;
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .feedback-module .left {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.bwms-page .feedback-module .left .container {
  height: 100%;
}
.bwms-page .feedback-module .left .box {
  padding-right: 80px;
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.bwms-page .feedback-module .left .box .module-tit {
  margin-bottom: 30px;
}
.bwms-page .feedback-module .left .box .module-tit h4 {
  font-size: 24px;
  color: #fff;
}
.bwms-page .feedback-module .left .box .from {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
}
.bwms-page .feedback-module .left .box .from .el-form-item {
  margin-bottom: 20px;
}
.bwms-page .feedback-module .left .box .from .el-form-item .el-form-item__content .el-input .el-input__wrapper {
  border: 0;
  border-radius: 0;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0) inset;
  background-color: #fff;
  height: 50px;
  line-height: 50px;
}
.bwms-page .feedback-module .left .box .from .el-form-item .el-form-item__content .el-input .el-input__wrapper input {
  font-size: 16px;
}
.bwms-page .feedback-module .left .box .from textarea {
  margin-bottom: 20px;
  padding: 12px;
  line-height: 24px;
  width: 100%;
  font-size: 16px;
  background: #fff;
  border: 0;
}
.bwms-page .feedback-module .left .box .from button {
  border-radius: 0;
  padding: 12px 30px;
  color: #fff;
  background-color: #ff9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: none;
  width: 100%;
  height: auto;
}
.bwms-page .feedback-module .left .box .from button .iconfont + span {
  margin-left: 5px;
}
.bwms-page .feedback-module .left .box .from button span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .feedback-module .left .box .from button span {
  display: block;
  line-height: 1.5;
}
.bwms-page .feedback-module .left .box .from button:hover {
  background-color: #FCB319;
  color: #fff;
}
.bwms-page .feedback-module .right {
  width: 50%;
}
