<template>
  <div class="bwms-module">
    <div class="module-header">
    
    </div>
    <div class="module-con">
      <div class="box">
        <div class="account-binding">
         
          <div class="info-item">
            <div class="info-left">
              <el-icon><Message /></el-icon>
              <span>{{ t('dashboard.accountBinding.email.title') }}：</span>
              <span class="account-value">{{ userInfo.email || t('dashboard.accountBinding.email.notBound') }}</span>
            </div>
            <div class="info-right">
              <el-button v-if="userInfo.email" type="primary" link @click="showEmailDialog = true">
                {{ t('dashboard.accountBinding.email.modify') }}
              </el-button>
              <el-button v-if="userInfo.email" type="danger" link @click="showUnbindEmailDialog = true">
                {{ t('dashboard.accountBinding.email.unbind') }}
              </el-button>
              <el-button v-else type="primary" link @click="showEmailDialog = true">
                {{ t('dashboard.accountBinding.email.bind') }}
              </el-button>
            </div>
          </div>
          <div class="info-item">
            <div class="info-left">
              <el-icon><Iphone /></el-icon>
              <span>{{ t('dashboard.accountBinding.phone.title') }}：</span>
              <span class="account-value">{{ userInfo.phone || t('dashboard.accountBinding.phone.notBound') }}</span>
            </div>
            <div class="info-right">
              <el-button v-if="userInfo.phone" type="primary" link @click="showPhoneDialog = true">
                {{ t('dashboard.accountBinding.phone.modify') }}
              </el-button>
              <el-button v-if="userInfo.phone" type="danger" link @click="showUnbindPhoneDialog = true">
                {{ t('dashboard.accountBinding.phone.unbind') }}
              </el-button>
              <el-button v-else type="primary" link @click="showPhoneDialog = true">
                {{ t('dashboard.accountBinding.phone.bind') }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改手机号弹窗 -->
    <el-dialog 
      :title="userInfo.phone ? t('dashboard.accountBinding.phone.modifyTitle') : t('dashboard.accountBinding.phone.bindTitle')" 
      v-model="showPhoneDialog" 
    
      custom-class="phone-dialog"
    >
      <el-form :model="phoneForm" label-position="top">
        <el-form-item 
          v-if="userInfo.phone" 
          :label="t('dashboard.accountBinding.phone.current')"
        >
          <el-input v-model="phoneForm.currentPhone" disabled />
        </el-form-item>
        <el-form-item 
          v-if="userInfo.phone" 
          :label="t('dashboard.accountBinding.phone.currentVerificationCode')"
        >
          <div class="verification-code-input">
            <el-input 
              v-model="phoneForm.oldPhoneCode" 
              :placeholder="t('dashboard.accountBinding.phone.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="oldPhoneCodeSent" 
                  @click="sendOldPhoneCode" 
                  :loading="oldPhoneCodeSent"
                >
                  {{ oldPhoneCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: oldPhoneCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item :label="userInfo.phone ? t('dashboard.accountBinding.phone.new') : t('dashboard.accountBinding.phone.title')">
          <el-input 
            v-model="phoneForm.newPhone" 
            :placeholder="userInfo.phone ? t('dashboard.accountBinding.phone.placeholder') : t('dashboard.accountBinding.phone.placeholder')"
          >
            <template #prepend>
              <el-select v-model="phoneForm.phoneCountryCode" style="width: 100px">
                <el-option label="+86" value="+86"></el-option>
                <el-option label="+852" value="+852"></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item 
          :label="userInfo.phone ? t('dashboard.accountBinding.phone.newVerificationCode') : t('dashboard.accountBinding.phone.verificationCode')"
        >
          <div class="verification-code-input">
            <el-input 
              v-model="phoneForm.newPhoneCode" 
              :placeholder="t('dashboard.accountBinding.phone.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="newPhoneCodeSent" 
                  @click="sendNewPhoneCode" 
                  :loading="newPhoneCodeSent"
                >
                  {{ newPhoneCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: newPhoneCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPhoneDialog = false">
            {{ t('dashboard.accountBinding.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="updatePhone">
            {{ t('dashboard.accountBinding.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 解绑手机号弹窗 -->
    <el-dialog 
      :title="t('dashboard.accountBinding.phone.unbindTitle')" 
      v-model="showUnbindPhoneDialog" 
 
      custom-class="phone-dialog"
    >
      <el-form :model="unbindPhoneForm" label-position="top">
        <el-form-item :label="t('dashboard.accountBinding.phone.current')">
          <el-input v-model="unbindPhoneForm.currentPhone" disabled />
        </el-form-item>
        <el-form-item :label="t('dashboard.accountBinding.phone.verificationCode')">
          <div class="verification-code-input">
            <el-input 
              v-model="unbindPhoneForm.code" 
              :placeholder="t('dashboard.accountBinding.phone.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="unbindPhoneCodeSent" 
                  @click="sendUnbindPhoneCode" 
                  :loading="unbindPhoneCodeSent"
                >
                  {{ unbindPhoneCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: unbindPhoneCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUnbindPhoneDialog = false">
            {{ t('dashboard.accountBinding.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="unbindPhone">
            {{ t('dashboard.accountBinding.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 绑定/修改邮箱弹窗 -->
    <el-dialog 
      :title="userInfo.email ? t('dashboard.accountBinding.email.modifyTitle') : t('dashboard.accountBinding.email.bindTitle')" 
      v-model="showEmailDialog" 
   
      custom-class="email-dialog"
    >
      <el-form :model="emailForm" label-position="top">
        <el-form-item 
          v-if="userInfo.email" 
          :label="t('dashboard.accountBinding.email.current')"
        >
          <el-input v-model="emailForm.currentEmail" disabled />
        </el-form-item>
        <el-form-item 
          v-if="userInfo.email" 
          :label="t('dashboard.accountBinding.email.currentVerificationCode')"
        >
          <div class="verification-code-input">
            <el-input 
              v-model="emailForm.oldEmailCode" 
              :placeholder="t('dashboard.accountBinding.email.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="oldEmailCodeSent" 
                  @click="sendOldEmailCode" 
                  :loading="oldEmailCodeSent"
                >
                  {{ oldEmailCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: oldEmailCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item :label="userInfo.email ? t('dashboard.accountBinding.email.new') : t('dashboard.accountBinding.email.title')">
          <el-input 
            v-model="emailForm.newEmail" 
            :placeholder="userInfo.email ? t('dashboard.accountBinding.email.placeholder') : t('dashboard.accountBinding.email.placeholder')" 
          />
        </el-form-item>
        <el-form-item 
          :label="userInfo.email ? t('dashboard.accountBinding.email.newVerificationCode') : t('dashboard.accountBinding.email.verificationCode')"
        >
          <div class="verification-code-input">
            <el-input 
              v-model="emailForm.newEmailCode" 
              :placeholder="t('dashboard.accountBinding.email.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="newEmailCodeSent" 
                  @click="sendNewEmailCode" 
                  :loading="newEmailCodeSent"
                >
                  {{ newEmailCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: newEmailCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEmailDialog = false">
            {{ t('dashboard.accountBinding.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="updateEmail">
            {{ t('dashboard.accountBinding.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 解绑邮箱弹窗 -->
    <el-dialog 
      :title="t('dashboard.accountBinding.email.unbindTitle')" 
      v-model="showUnbindEmailDialog" 
     
      custom-class="email-dialog"
    >
      <el-form :model="unbindEmailForm" label-position="top">
        <el-form-item :label="t('dashboard.accountBinding.email.current')">
          <el-input v-model="unbindEmailForm.currentEmail" disabled />
        </el-form-item>
        <el-form-item :label="t('dashboard.accountBinding.email.verificationCode')">
          <div class="verification-code-input">
            <el-input 
              v-model="unbindEmailForm.code" 
              :placeholder="t('dashboard.accountBinding.email.codePlaceholder')"
            >
              <template #append>
                <el-button 
                  :disabled="unbindEmailCodeSent" 
                  @click="sendUnbindEmailCode" 
                  :loading="unbindEmailCodeSent"
                >
                  {{ unbindEmailCodeSent ? t('dashboard.accountBinding.common.countdown', { seconds: unbindEmailCountdown }) : t('dashboard.accountBinding.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUnbindEmailDialog = false">
            {{ t('dashboard.accountBinding.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="unbindEmail">
            {{ t('dashboard.accountBinding.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, type Ref } from 'vue'
import { useUserStore } from '/admin/stores/modules/user/userStore'
import { ElMessage } from 'element-plus'
import { Iphone, Message } from '@element-plus/icons-vue'
import { UserCenterService } from '../application/UserCenterService'
import { useI18n } from 'vue-i18n'

const userCenterService = new UserCenterService()
const userStore = useUserStore()
const userInfo = ref<any>(userStore.userInfo)

const showPhoneDialog = ref(false)
const showUnbindPhoneDialog = ref(false)
const showEmailDialog = ref(false)
const showUnbindEmailDialog = ref(false)

const phoneForm = ref({
  currentPhone: '',
  oldPhoneCode: '',
  newPhone: '',
  newPhoneCode: '',
  phoneCountryCode: '+86',
})

const unbindPhoneForm = ref({
  currentPhone: '',
  code: '',
})

const emailForm = ref({
  currentEmail: '',
  oldEmailCode: '',
  newEmail: '',
  newEmailCode: '',
})

const unbindEmailForm = ref({
  currentEmail: '',
  code: '',
})

const oldPhoneCodeSent = ref(false)
const oldPhoneCountdown = ref(60)
const newPhoneCodeSent = ref(false)
const newPhoneCountdown = ref(60)
const unbindPhoneCodeSent = ref(false)
const unbindPhoneCountdown = ref(60)
const oldEmailCodeSent = ref(false)
const oldEmailCountdown = ref(60)
const newEmailCodeSent = ref(false)
const newEmailCountdown = ref(60)
const unbindEmailCodeSent = ref(false)
const unbindEmailCountdown = ref(60)
const appCode = ref(localStorage.getItem('app_codeLogin') || 'admin')
const startCountdown = (countdownRef: Ref<number>, sentRef: Ref<boolean>) => {
  sentRef.value = true
  countdownRef.value = 60
  const timer = setInterval(() => {
    countdownRef.value--
    if (countdownRef.value <= 0) {
      clearInterval(timer)
      sentRef.value = false
    }
  }, 1000)
}

const validatePhoneNumber = (phone: string, countryCode: string): boolean => {
  if (countryCode === '+86') {
    // 中国大陆手机号格式：1开头的11位数字
    return /^1\d{10}$/.test(phone)
  } else if (countryCode === '+852') {
    // 香港手机号格式：5/6/9开头的8位数字
    return /^[569]\d{7}$/.test(phone)
  }
  return false
}

const validateEmail = (email: string): boolean => {
  // 简单的邮箱格式校验
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

const sendCode = async (type: string) => {
  let sentRef: Ref<boolean>,
    countdownRef: Ref<number>,
    target: string,
    channel: string,
    countryCode: string = '+86'

  if (type === 'oldPhone') {
    sentRef = oldPhoneCodeSent
    countdownRef = oldPhoneCountdown
    target = phoneForm.value.currentPhone
    channel = 'CHANNEL_UPDATE_PHONE'
  } else if (type === 'newPhone') {
    sentRef = newPhoneCodeSent
    countdownRef = newPhoneCountdown
    target = phoneForm.value.newPhone
    channel = 'CHANNEL_BIND_PHONE'
    countryCode = phoneForm.value.phoneCountryCode

    // 添加手机号格式校验
    if (!validatePhoneNumber(target, countryCode)) {
      ElMessage.error('请输入正确的手机号格式')
      return
    }
  } else if (type === 'unbindPhone') {
    sentRef = unbindPhoneCodeSent
    countdownRef = unbindPhoneCountdown
    target = unbindPhoneForm.value.currentPhone
    channel = 'CHANNEL_UNBIND_PHONE'
  } else if (type === 'oldEmail') {
    sentRef = oldEmailCodeSent
    countdownRef = oldEmailCountdown
    target = emailForm.value.currentEmail
    channel = 'CHANNEL_UPDATE_EMAIL'
  } else if (type === 'newEmail') {
    sentRef = newEmailCodeSent
    countdownRef = newEmailCountdown
    target = emailForm.value.newEmail
    channel = 'CHANNEL_BIND_EMAIL'

    // 添加邮箱格式校验
    if (!validateEmail(target)) {
      ElMessage.error('请输入正确的邮箱格式')
      return
    }
  } else if (type === 'unbindEmail') {
    sentRef = unbindEmailCodeSent
    countdownRef = unbindEmailCountdown
    target = unbindEmailForm.value.currentEmail
    channel = 'CHANNEL_UNBIND_EMAIL'
  } else {
    return
  }

  try {
    sentRef.value = true // 设置 loading 状态
    if (type.includes('Email')) {
      await userCenterService.sendEmailCode(target, channel, appCode.value)
    } else {
      await userCenterService.sendSmsCode(target, channel, countryCode, appCode.value)
    }
    startCountdown(countdownRef, sentRef)
    ElMessage.success('验证码已发送')
  } catch (error) {
    console.error('发送验证码失败', error)
    ElMessage.error('发送验证码失败，请稍后重试')
    sentRef.value = false // 发送失败时重置 loading 状态
  }
}

const sendOldPhoneCode = () => sendCode('oldPhone')
const sendNewPhoneCode = () => sendCode('newPhone')
const sendUnbindPhoneCode = () => sendCode('unbindPhone')
const sendOldEmailCode = () => sendCode('oldEmail')
const sendNewEmailCode = () => sendCode('newEmail')
const sendUnbindEmailCode = () => sendCode('unbindEmail')

const updatePhone = async () => {
  try {
    if (!phoneForm.value.newPhone || !phoneForm.value.newPhoneCode || (userInfo.value.phone && !phoneForm.value.oldPhoneCode)) {
      ElMessage.warning('请填写所有必要信息')
      return
    }

    // 添加新手机号格式校验
    if (!validatePhoneNumber(phoneForm.value.newPhone, phoneForm.value.phoneCountryCode)) {
      ElMessage.error('请输入正确的手机号格式')
      return
    }

    if (userInfo.value.phone) {
      // 如果已经绑定了手机号，先验证旧手机号
      await userCenterService.sendSmsCode(phoneForm.value.currentPhone, 'CHANNEL_VERIFY_OLD_PHONE', userStore.userInfo.phone, appCode.value)
    }

    // 绑定新手机号
    await userCenterService.bindPhone(phoneForm.value.newPhone, phoneForm.value.newPhoneCode, phoneForm.value.phoneCountryCode)

    ElMessage.success('手机号更新成功')
    showPhoneDialog.value = false
    userStore.updateUserInfo({ phone: phoneForm.value.newPhone })
    // 重置表单
    phoneForm.value = {
      currentPhone: phoneForm.value.newPhone,
      oldPhoneCode: '',
      newPhone: '',
      newPhoneCode: '',
      phoneCountryCode: '+86',
    }
  } catch (error) {
    console.error('更新手机号失败', error)
    ElMessage.error('更新手机号失败，请检查信息是否正确')
  }
}

const unbindPhone = async () => {
  try {
    if (!unbindPhoneForm.value.code) {
      ElMessage.warning('请输入验证码')
      return
    }

    await userCenterService.unbindPhone(unbindPhoneForm.value.code)

    showUnbindPhoneDialog.value = false
    userStore.updateUserInfo({ phone: '' })
    // 重置表单
    unbindPhoneForm.value = {
      currentPhone: '',
      code: '',
    }
  } catch (error) {}
}

const updateEmail = async () => {
  try {
    if (!emailForm.value.newEmail || !emailForm.value.newEmailCode || (userInfo.value.email && !emailForm.value.oldEmailCode)) {
      ElMessage.warning('请填写所有必要信息')
      return
    }

    // 添加新邮箱格式校验
    if (!validateEmail(emailForm.value.newEmail)) {
      ElMessage.error('请输入正确的邮箱格式')
      return
    }

    if (userInfo.value.email) {
      // 如果已经绑定了邮箱，先验证旧邮箱
      await userCenterService.verifyEmail(emailForm.value.currentEmail, emailForm.value.oldEmailCode)
    }

    // 绑定新邮箱
    await userCenterService.bindEmail(emailForm.value.newEmail, emailForm.value.newEmailCode)

    showEmailDialog.value = false
    userStore.updateUserInfo({ email: emailForm.value.newEmail })
    // 重置表单
    emailForm.value = {
      currentEmail: emailForm.value.newEmail,
      oldEmailCode: '',
      newEmail: '',
      newEmailCode: '',
    }
  } catch (error) {
    console.error('更新邮箱失败', error)
  }
}

const unbindEmail = async () => {
  try {
    if (!unbindEmailForm.value.code) {
      ElMessage.warning('请输入验证码')
      return
    }

    await userCenterService.unbindEmail(unbindEmailForm.value.code)

    ElMessage.success('邮箱已解绑')
    showUnbindEmailDialog.value = false
    userStore.updateUserInfo({ email: '' })
    // 重置表单
    unbindEmailForm.value = {
      currentEmail: '',
      code: '',
    }
  } catch (error) {
    console.error('解绑邮箱失败', error)
    ElMessage.error('解绑邮箱失败，请检查验证码是否正确')
  }
}

watch(
  () => userInfo.value,
  (newUserInfo: any) => {
    // 更新手机号相关表单
    phoneForm.value.currentPhone = newUserInfo.phone || ''
    unbindPhoneForm.value.currentPhone = newUserInfo.phone || ''

    // 更新邮箱相关表单
    emailForm.value.currentEmail = newUserInfo.email || ''
    unbindEmailForm.value.currentEmail = newUserInfo.email || ''
  },
  { immediate: true, deep: true },
)

const t = useI18n().t
</script>

<style lang="scss" scoped>
.account-binding {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.info-left {
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 8px;
    font-size: 20px;
    color: #409eff;
  }

  span {
    font-size: 16px;
    color: #606266;
  }

  .account-value {
    margin-left: 8px;
    font-size: 14px;
    color: #303133;
  }
}

.info-right {
  display: flex;
  gap: 10px;
}

// 弹窗样式
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .el-form-item__label {
    padding-bottom: 8px;
  }
}

.verification-code-input {
  :deep(.el-input-group__append) {
    padding: 0;
    
    button {
      border: none;
      background: none;
      color: #409eff;
      padding: 0 15px;
      width: 150px;
      transition: all 0.3s;

      &:disabled {
        color: #909399;
        cursor: not-allowed;
      }
    }
  }
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .el-icon {
    margin: 0;
  }
}
</style>