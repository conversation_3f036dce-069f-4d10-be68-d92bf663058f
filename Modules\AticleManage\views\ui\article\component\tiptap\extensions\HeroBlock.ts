import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { heroTemplate } from '../templates/hero.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    heroBlock: {
      insertHeroBlock: () => ReturnType
    }
  }
}

export const HeroBlock = Node.create({
  name: 'heroBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div.hero-section',
      },
      {
        tag: 'div[data-bs-component="hero"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'hero',
      'class': 'hero-block py-5 hero-section'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertHeroBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(heroTemplate)
        },
    }
  },
}) 