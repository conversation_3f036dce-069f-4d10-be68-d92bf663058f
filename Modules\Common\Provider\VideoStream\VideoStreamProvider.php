<?php

namespace Modules\Common\Provider\VideoStream;

class VideoStreamProvider
{
    /**
     * @var AbstractVideoStreamProvider[]
     */
    private static array $instances = [
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractVideoStreamProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function nameTitleMap(): array
    {
        return array_build(self::all(), function ($k, $provider) {
            return [
                $provider->name(),
                $provider->title(),
            ];
        });
    }

    public static function first()
    {
        foreach (self::all() as $provider) {
            return $provider->name();
        }

    }

    /**
     * @param $name
     * @return AbstractVideoStreamProvider
     */
    public static function get($name)
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }

    }
}
