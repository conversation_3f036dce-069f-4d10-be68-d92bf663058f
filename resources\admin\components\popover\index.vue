<template>
  <el-popover 
    :visible="visible"
    placement="bottom-end" 
    :width="width" 
    trigger="click"
    :popper-class="popperClass"
    :close-on-click-outside="true"
  >
    <template #reference>
        <slot name="reference">
        </slot>
    </template>
    
    <!-- 筛选内容 -->
    <div @click.stop class="filter-content scroll-bar-custom" :style="{ maxHeight: maxHeight+'px', overflowY: 'auto' }">
      <slot></slot>
      
      <!-- 底部按钮区域 -->
      <slot name="footer"></slot>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Close, Refresh, Filter } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 500
  },
  popperClass: {
    type: String,
    default: 'table-page'
  },
  maxHeight: {
    type: Number,
    default: 360
  }
})

const emit = defineEmits(['update:modelValue'])

// 可见性状态
const visible = ref(props.modelValue)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 全局点击事件处理
const handleClickOutside = (event: MouseEvent) => {
  
  const popoverEl = document.querySelector(`.el-popover.${props.popperClass}`)
  const triggerEl = document.querySelector('.filter-trigger')
  
  if (visible.value && 
      popoverEl && 
      !popoverEl.contains(event.target as Node) && 
      triggerEl && 
      !triggerEl.contains(event.target as Node)) {
    visible.value = false
  }
}

// 组件挂载/卸载时添加/移除全局点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>


