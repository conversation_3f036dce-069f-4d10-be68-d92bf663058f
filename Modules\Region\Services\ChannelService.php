<?php

namespace Modules\Region\Services;

use Modules\Region\Models\Channel;
use Modules\Region\Models\RegionChannel;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

/**
 * 频道管理服务类
 */
class ChannelService
{
    /**
     * 获取频道列表
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getChannelList(array $params): LengthAwarePaginator
    {
        $query = Channel::query();

        // 按名称搜索
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        // 按状态筛选
        if (isset($params['status']) && !empty($params['status'])) {
            if ($params['status'] == 1) {
                $query->enabled();   // 实际调用 scopeEnabled() 方法
            } else {
                $query->disabled(); // 实际调用 scopeDisabled() 方法
            }
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        
        return $query->with('regions')->paginate($perPage);
    }

    /**
     * 根据ID获取频道
     * @param int $id
     * @return Channel|null
     */
    public function getChannelById(int $id): ?Channel
    {
        return Channel::with('regions')->find($id);
    }

    /**
     * 创建频道
     * @param array $params
     * @return Channel
     */
    public function createChannel(array $params): Channel
    {
        $regionIds = $params['region_ids'] ?? [];
        unset($params['region_ids']);

        $channel = Channel::create($params);

        // 关联区域
        if (!empty($regionIds)) {
            $this->associateRegions($channel->id, $regionIds, $params['created_by']);
        }

        return $channel->load('regions');
    }

    /**
     * 更新频道
     * @param int $id
     * @param array $params
     * @return Channel|null
     */
    public function updateChannel(int $id, array $params): ?Channel
    {
        $channel = Channel::find($id);
        if (!$channel) {
            return null;
        }

        $regionIds = $params['region_ids'] ?? [];
        unset($params['region_ids']);

        $channel->update($params);

        // 更新区域关联
        $this->updateRegionAssociations($id, $regionIds, $params['updated_by']);

        return $channel->fresh(['regions']);
    }

    /**
     * 删除频道
     * @param int $id
     * @return bool
     */
    public function deleteChannel(int $id): bool
    {
        $channel = Channel::find($id);
        if (!$channel) {
            return false;
        }

        // 删除区域关联
        RegionChannel::where('channel_id', $id)->delete();

        return $channel->delete();
    }

    /**
     * 更新频道状态
     * @param int $id
     * @param int $status
     * @param int $updatedBy
     * @return bool
     */
    public function updateChannelStatus(int $id, int $status, int $updatedBy): bool
    {
        $channel = Channel::find($id);
        if (!$channel) {
            return false;
        }

        return $channel->update([
            'status' => $status,
            'updated_by' => $updatedBy
        ]);
    }

    /**
     * 关联区域到频道
     * @param int $channelId
     * @param array $regionIds
     * @param int $createdBy
     * @return bool
     */
    public function associateRegions(int $channelId, array $regionIds, int $createdBy): bool
    {
        // 先删除现有关联，避免重复
        RegionChannel::where('channel_id', $channelId)->delete();
        
        $data = [];
        foreach ($regionIds as $regionId) {
            $data[] = [
                'regions_id' => $regionId,
                'channel_id' => $channelId,
                'created_by' => $createdBy,
                'updated_by' => $createdBy,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        return RegionChannel::insert($data);
    }

    /**
     * 更新频道的区域关联
     * @param int $channelId
     * @param array $regionIds
     * @param int $updatedBy
     * @return bool
     */
    public function updateRegionAssociations(int $channelId, array $regionIds, int $updatedBy): bool
    {
        // 删除现有关联
        RegionChannel::where('channel_id', $channelId)->delete();

        // 创建新关联
        if (!empty($regionIds)) {
            return $this->associateRegions($channelId, $regionIds, $updatedBy);
        }

        return true;
    }

    /**
     * 删除频道与区域的关联关系
     * @param int $channelId
     * @param int $regionId
     * @return bool
     */
    public function deleteRegionAssociation(int $channelId, int $regionId): bool
    {
        return RegionChannel::where('channel_id', $channelId)
            ->where('regions_id', $regionId)
            ->delete();
    }

    /**
     * 获取频道关联的区域列表
     * @param int $channelId
     * @return array
     */
    public function getChannelRegions(int $channelId): array
    {
        $channel = Channel::with('regions')->find($channelId);
        return $channel ? $channel->regions->toArray() : [];
    }

    /**
     * 批量操作
     * @param string $action
     * @param array $ids
     * @param int $operatorId
     * @return array
     */
    public function batchAction(string $action, array $ids, int $operatorId): array
    {
        $result = [
            'success_count' => 0,
            'failed_count' => 0,
            'failed_ids' => []
        ];

        foreach ($ids as $id) {
            $channel = Channel::find($id);
            if (!$channel) {
                $result['failed_count']++;
                $result['failed_ids'][] = $id;
                continue;
            }

            $success = false;
            switch ($action) {
                case 'delete':
                    // 删除区域关联
                    RegionChannel::where('channel_id', $id)->delete();
                    $success = $channel->delete();
                    break;
                case 'enable':
                    $success = $channel->update([
                        'status' => 1,
                        'updated_by' => $operatorId
                    ]);
                    break;
                case 'disable':
                    $success = $channel->update([
                        'status' => 0,
                        'updated_by' => $operatorId
                    ]);
                    break;
            }

            if ($success) {
                $result['success_count']++;
            } else {
                $result['failed_count']++;
                $result['failed_ids'][] = $id;
            }
        }

        return $result;
    }

    /**
     * 获取所有启用的频道选项
     * @return array
     */
    public function getChannelOptions(): array
    {
        return Channel::enabled()
            ->select(['id', 'name'])
            ->orderBy('name')
            ->get()
            ->toArray();
    }
} 