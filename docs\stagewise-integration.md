# Stagewise 开发工具集成指南

## 概述

Stagewise 是一个浏览器工具栏，它将前端 UI 连接到代码编辑器中的 AI 代理。它允许开发者选择 Web 应用中的元素，留下评论，并让 AI 代理基于该上下文进行更改。

## 集成状态

✅ **已完成集成** - Stagewise 工具栏已成功集成到项目中

## 功能特性

- 🎯 **元素选择** - 在浏览器中直接选择 UI 元素
- 💬 **评论系统** - 为选中的元素添加评论和反馈
- 🤖 **AI 代理集成** - 让 AI 根据上下文自动修改代码
- 🔧 **开发环境专用** - 仅在开发模式下启用，不影响生产构建

## 使用方法

### 1. 启动开发服务器

```bash
npm run dev
```

### 2. 访问测试页面

打开浏览器访问开发服务器，Stagewise 工具栏会自动出现在页面上（仅开发环境）。

### 3. 使用工具栏

1. **选择元素** - 点击工具栏上的选择工具，然后点击页面上的任何元素
2. **添加评论** - 为选中的元素添加评论或修改建议
3. **AI 处理** - AI 代理会根据您的评论自动生成代码修改建议

## 技术实现

### 文件结构

```
resources/admin/
├── utils/
│   └── stagewise.ts          # Stagewise 配置和初始化
├── types/
│   └── stagewise.d.ts        # TypeScript 类型声明
├── support/
│   └── bingoStart.ts         # 主应用启动文件（已修改）
└── views/test/
    └── StagewiseTest.vue     # 测试页面
```

### 核心配置

**stagewise.ts**
```typescript
export const stagewiseConfig = {
  plugins: []
}

export async function initStagewise() {
  if (import.meta.env.MODE !== 'development') {
    return
  }
  // ... 初始化逻辑
}
```

### 集成点

工具栏在 `BingoStart.bootstrap()` 方法中初始化：

```typescript
bootstrap(): void {
  this.useElementPlus()
    .usePinia()
    .useI18n()
    // ... 其他初始化
    .mount()
  
  // 初始化 Stagewise 开发工具（仅开发环境）
  this.initStagewise()
}
```

## 环境配置

### 开发环境检查

工具栏仅在以下条件下启用：
- `import.meta.env.MODE === 'development'`
- 通过 Vite 配置确保正确的环境变量

### 生产构建排除

- Stagewise 包安装为 `devDependency`
- 运行时环境检查确保生产环境不加载
- Vite 构建过程会自动排除开发依赖

## 故障排除

### 工具栏未显示

1. **检查开发环境**
   ```javascript
   console.log('当前环境:', import.meta.env.MODE)
   ```

2. **检查控制台日志**
   - 查找 "🚀 Stagewise 开发工具已启用" 消息
   - 查看是否有错误信息

3. **检查 DOM 元素**
   ```javascript
   document.getElementById('stagewise-toolbar-container')
   ```

### 常见问题

**Q: 工具栏在生产环境中出现了怎么办？**
A: 检查环境变量配置，确保 `import.meta.env.MODE` 在生产环境中不是 'development'

**Q: 如何自定义工具栏配置？**
A: 修改 `resources/admin/utils/stagewise.ts` 中的 `stagewiseConfig` 对象

**Q: 如何添加插件？**
A: 在 `stagewiseConfig.plugins` 数组中添加所需的插件配置

## 更新和维护

### 更新 Stagewise

```bash
npm update @stagewise/toolbar-vue
```

### 添加新功能

1. 修改 `stagewiseConfig` 配置
2. 根据需要添加新的插件
3. 更新类型声明文件

## 相关链接

- [Stagewise 官方文档](https://stagewise.dev)
- [Vue 集成指南](https://stagewise.dev/docs/vue)
- [项目 GitHub](https://github.com/stagewise/stagewise)

---

**注意**: 此工具仅用于开发环境，不会影响生产构建的性能和大小。 