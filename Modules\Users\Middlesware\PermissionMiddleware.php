<?php
// ========================================
// 权限中间件（续）
// ========================================

namespace Modules\Users\Middlesware;

use Closure;
use Illuminate\Http\Request;
use Modules\Users\Services\RBACService;
use Illuminate\Support\Facades\Auth;

/**
 * 权限检查中间件
 */
class PermissionMiddleware
{
    protected $rbacService;

    public function __construct(RBACService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @param string $permission 权限代码
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        // 检查用户是否已登录
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => '用户未登录'
            ], 401);
        }

        $adminId = Auth::id();

        // 构建上下文信息
        $context = [
            'resource_id' => $request->route('id') ?? $request->input('id'),
            'resource_type' => $this->getResourceType($permission),
            'request_method' => $request->method(),
            'request_data' => $request->all()
        ];

        // 检查权限
        if (!$this->rbacService->hasPermission($adminId, $permission, $context)) {
            return response()->json([
                'success' => false,
                'message' => '权限不足，无法执行此操作'
            ], 403);
        }

        return $next($request);
    }

    /**
     * 获取资源类型
     *
     * @param string $permission 权限代码
     * @return string
     */
    private function getResourceType(string $permission): string
    {
        $parts = explode(':', $permission);
        return $parts[0] ?? '';
    }
}

/**
 * 角色检查中间件
 */
class RoleMiddleware
{
    protected $rbacService;

    public function __construct(RBACService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @param string $roles 角色代码（多个用逗号分隔）
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $roles)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => '用户未登录'
            ], 401);
        }

        $adminId = Auth::id();
        $requiredRoles = explode(',', $roles);

        // 获取用户角色
        $userRoles = $this->rbacService->getUserRoles($adminId);

        // 检查是否有任一所需角色
        $hasRole = false;
        foreach ($requiredRoles as $role) {
            if (in_array(trim($role), $userRoles)) {
                $hasRole = true;
                break;
            }
        }

        if (!$hasRole) {
            return response()->json([
                'success' => false,
                'message' => '角色权限不足'
            ], 403);
        }

        return $next($request);
    }
}
