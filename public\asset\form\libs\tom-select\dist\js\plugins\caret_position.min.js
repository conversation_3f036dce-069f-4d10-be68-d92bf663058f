(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.caret_position=factory())})(this,function(){"use strict";const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const iterate=(object,callback)=>{if(Array.isArray(object)){object.forEach(callback)}else{for(var key in object){if(object.hasOwnProperty(key)){callback(object[key],key)}}}};const removeClasses=(elmts,...classes)=>{var norm_classes=classesArray(classes);elmts=castAsArray(elmts);elmts.map(el=>{norm_classes.map(cls=>{el.classList.remove(cls)})})};const classesArray=args=>{var classes=[];iterate(args,_classes=>{if(typeof _classes==="string"){_classes=_classes.trim().split(/[\11\12\14\15\40]/)}if(Array.isArray(_classes)){classes=classes.concat(_classes)}});return classes.filter(Boolean)};const castAsArray=arg=>{if(!Array.isArray(arg)){arg=[arg]}return arg};const nodeIndex=(el,amongst)=>{if(!el)return-1;amongst=amongst||el.nodeName;var i=0;while(el=el.previousElementSibling){if(el.matches(amongst)){i++}}return i};function plugin(){var self=this;self.hook("instead","setCaret",new_pos=>{if(self.settings.mode==="single"||!self.control.contains(self.control_input)){new_pos=self.items.length}else{new_pos=Math.max(0,Math.min(self.items.length,new_pos));if(new_pos!=self.caretPos&&!self.isPending){self.controlChildren().forEach((child,j)=>{if(j<new_pos){self.control_input.insertAdjacentElement("beforebegin",child)}else{self.control.appendChild(child)}})}}self.caretPos=new_pos});self.hook("instead","moveCaret",direction=>{if(!self.isFocused)return;const last_active=self.getLastActive(direction);if(last_active){const idx=nodeIndex(last_active);self.setCaret(direction>0?idx+1:idx);self.setActiveItem();removeClasses(last_active,"last-active")}else{self.setCaret(self.caretPos+direction)}})}return plugin});