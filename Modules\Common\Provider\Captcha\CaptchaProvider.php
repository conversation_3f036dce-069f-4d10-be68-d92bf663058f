<?php

namespace Modules\Common\Provider\Captcha;

/**
 * Class CaptchaProvider
 * @package Modules\Common\Provider\Captcha
 */
class CaptchaProvider
{
    /**
     * @var AbstractCaptchaProvider[]
     */
    private static array $instances = [
        DefaultCaptchaProvider::class,
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractCaptchaProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function nameTitleMap(): array
    {
        return array_build(self::all(), function ($k, $v) {
            /** @var AbstractCaptchaProvider $v */
            return [
                $v->name(),
                $v->title()
            ];
        });
    }

    /**
     * @param $name
     * @return AbstractCaptchaProvider
     */
    public static function get($name)
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }

    }

    /**
     * @return bool
     * @since 1.7.0
     */
    public static function hasProvider(): bool
    {
        $provider = app()->config->get('CaptchaProvider');
        return ! empty($provider);
    }
}
