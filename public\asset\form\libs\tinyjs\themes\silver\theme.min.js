/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.7 (2022-12-06)
 */
!function(){"use strict";function t(o){return function(t){return n=typeof(e=t),(null===e?"null":"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n)===o;var e,n}}function e(e){return function(t){return typeof t===e}}function n(e){return function(t){return e===t}}function g(t){return null==t}function f(t,e){if(c(t)){for(var n=0,o=t.length;n<o;++n)if(!e(t[n]))return;return 1}}function st(){}function r(n,o){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n(o.apply(null,t))}}function rt(t){return function(){return t}}function h(t){return t}function v(t,e){return t===e}var y=t("string"),x=t("object"),c=t("array"),b=n(null),w=e("boolean"),E=n(void 0),k=function(t){return!g(t)},S=e("function"),u=e("number");function C(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=r.concat(t);return o.apply(null,n)}}function O(e){return function(t){return!e(t)}}function _(t){return function(){throw new Error(t)}}var T=rt(!1),D=rt(!0),o=tinymce.util.Tools.resolve("tinymce.ThemeManager"),lt=function(){return(lt=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function A(t,e){var n={};for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]]);return n}function V(t,e,n){if(n||2===arguments.length)for(var o,r=0,i=e.length;r<i;r++)!o&&r in e||((o=o||Array.prototype.slice.call(e,0,r))[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))}function i(){return a}var a={fold:function(t,e){return t()},isSome:T,isNone:D,getOr:h,getOrThunk:s,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:rt(null),getOrUndefined:rt(void 0),or:h,orThunk:s,map:i,each:st,bind:i,exists:T,forall:D,filter:function(){return a},toArray:function(){return[]},toString:rt("none()")};function s(t){return t()}function l(t,e){return yt.call(t,e)}function d(t,e){for(var n=0,o=t.length;n<o;n++)if(e(t[n],n))return!0;return!1}function m(t,e){for(var n=[],o=0;o<t;o++)n.push(e(o));return n}function p(t,e){for(var n=[],o=0;o<t.length;o+=e){var r=bt.call(t,o,o+e);n.push(r)}return n}function B(t,e){for(var n=t.length,o=new Array(n),r=0;r<n;r++){var i=t[r];o[r]=e(i,r)}return o}function M(t,e){for(var n=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(e(u,r)?n:o).push(u)}return{pass:n,fail:o}}function F(t,e){for(var n=[],o=0,r=t.length;o<r;o++){var i=t[o];e(i,o)&&n.push(i)}return n}function I(t,o,r){return function(t){for(var e,n=t.length-1;0<=n;n--)e=t[n],r=o(r,e,n)}(t),r}function R(t,n,o){return St(t,function(t,e){o=n(o,t,e)}),o}function N(t,e){return function(t,e,n){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(e(i,o))return vt.some(i);if(n(i,o))break}return vt.none()}(t,e,T)}function P(t,e){for(var n=0,o=t.length;n<o;n++)if(e(t[n],n))return vt.some(n);return vt.none()}function ft(t){for(var e=[],n=0,o=t.length;n<o;++n){if(!c(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);xt.apply(e,t[n])}return e}function H(t,e){return ft(B(t,e))}function L(t,e){for(var n=0,o=t.length;n<o;++n)if(!0!==e(t[n],n))return!1;return!0}function z(t){var e=bt.call(t,0);return e.reverse(),e}function U(t,e){return F(t,function(t){return!wt(e,t)})}function j(t,e){for(var n={},o=0,r=t.length;o<r;o++){var i=t[o];n[String(i)]=e(i,o)}return n}function W(t){return[t]}function G(t,e){var n=bt.call(t,0);return n.sort(e),n}function X(t,e){return 0<=e&&e<t.length?vt.some(t[e]):vt.none()}function Y(t){return X(t,0)}function q(t){return X(t,t.length-1)}function K(t,e){for(var n=0;n<t.length;n++){var o=e(t[n],n);if(o.isSome())return o}return vt.none()}function J(t,e){for(var n=Ct(t),o=0,r=n.length;o<r;o++){var i=n[o];e(t[i],i)}}function dt(t,n){return _t(t,function(t,e){return{k:e,v:n(t,e)}})}function $(t,n){var o=[];return J(t,function(t,e){o.push(n(t,e))}),o}function Q(t,e){for(var n=Ct(t),o=0,r=n.length;o<r;o++){var i=n[o],u=t[i];if(e(u,i,t))return vt.some(u)}return vt.none()}function Z(t){return $(t,h)}function tt(t,e){return Tt(t,e)?vt.from(t[e]):vt.none()}function et(t,e){return Tt(t,e)&&void 0!==t[e]&&null!==t[e]}function mt(t,e,n){return void 0===n&&(n=v),t.exists(function(t){return n(t,e)})}function nt(t){for(var e=[],n=function(t){e.push(t)},o=0;o<t.length;o++)t[o].each(n);return e}function ot(t,e){return t?vt.some(e):vt.none()}function it(t,e,n){return""===e||t.length>=e.length&&t.substr(n,n+e.length)===e}function ut(t,e){return-1!==t.indexOf(e)}function at(t){return 0<t.length}function ct(t){return void 0!==t.style&&S(t.style.getPropertyValue)}function gt(t){if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}}var pt,ht=function(n){function t(){return r}function e(t){return t(n)}var o=rt(n),r={fold:function(t,e){return e(n)},isSome:D,isNone:T,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:t,orThunk:t,map:function(t){return ht(t(n))},each:function(t){t(n)},bind:e,exists:e,forall:e,filter:function(t){return t(n)?r:a},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return r},vt={some:ht,none:i,from:function(t){return null==t?a:ht(t)}},bt=Array.prototype.slice,yt=Array.prototype.indexOf,xt=Array.prototype.push,wt=function(t,e){return-1<l(t,e)},St=function(t,e){for(var n=0,o=t.length;n<o;n++)e(t[n],n)},kt=S(Array.from)?Array.from:function(t){return bt.call(t)},Ct=Object.keys,Ot=Object.hasOwnProperty,_t=function(t,o){var r={};return J(t,function(t,e){var n=o(t,e);r[n.k]=n.v}),r},Tt=function(t,e){return Ot.call(t,e)},Et=function(t,e,n){return t.isSome()&&e.isSome()?vt.some(n(t.getOrDie(),e.getOrDie())):vt.none()},Dt=function(t,e){return it(t,e,0)},At=function(t,e){return it(t,e,t.length-e.length)},Bt=(pt=/^\s+|\s+$/g,function(t){return t.replace(pt,"")}),Mt={fromHtml:function(t,e){var n=(e||document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return gt(n.childNodes[0])},fromTag:function(t,e){var n=(e||document).createElement(t);return gt(n)},fromText:function(t,e){var n=(e||document).createTextNode(t);return gt(n)},fromDom:gt,fromPoint:function(t,e,n){return vt.from(t.dom.elementFromPoint(e,n)).map(gt)}};function Ft(t){return t.dom.nodeName.toLowerCase()}function It(e){return function(t){return t.dom.nodeType===e}}function Rt(n){var o,r=!1;return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return r||(r=!0,o=n.apply(null,t)),o}}function Nt(t,e){var n=String(e).toLowerCase();return N(t,function(t){return t.search(n)})}function Pt(e){return function(t){return ut(t,e)}}function Vt(t){return window.matchMedia(t).matches}function Ht(t,e){var n=t.dom;if(1!==n.nodeType)return!1;var o=n;if(void 0!==o.matches)return o.matches(e);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(e);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(e);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function Lt(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount}function zt(t,e){return t.dom===e.dom}function Ut(t,e){return ln().browser.isIE()?(n=t.dom,o=e.dom,r=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(n.compareDocumentPosition(o)&r)):(i=t.dom)!==(u=e.dom)&&i.contains(u);var n,o,r,i,u}function jt(t){return Mt.fromDom(t.dom.ownerDocument)}function Wt(t){return Ye(t)?t:jt(t)}function Gt(t){return Mt.fromDom(Wt(t).dom.documentElement)}function Xt(t){return Mt.fromDom(Wt(t).dom.defaultView)}function Yt(t){return vt.from(t.dom.parentNode).map(Mt.fromDom)}function qt(t){return vt.from(t.dom.offsetParent).map(Mt.fromDom)}function Kt(t){return B(t.dom.childNodes,Mt.fromDom)}function Jt(t,e){var n=t.dom.childNodes;return vt.from(n[e]).map(Mt.fromDom)}function $t(t,e){return{element:t,offset:e}}function Qt(t,e){var n=Kt(t);return 0<n.length&&e<n.length?$t(n[e],0):$t(t,e)}function Zt(t){return qe(t)&&k(t.dom.host)}function te(t){return Zt(t)?t:Mt.fromDom(Wt(t).dom.body)}function ee(t){return Mt.fromDom(t.dom.host)}function ne(t,e,n){if(!(y(n)||w(n)||u(n)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",n,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(e,n+"")}function oe(t,e,n){ne(t.dom,e,n)}function re(t,e){var n=t.dom;J(e,function(t,e){ne(n,e,t)})}function ie(t,e){var n=t.dom.getAttribute(e);return null===n?void 0:n}function ue(t,e){return vt.from(ie(t,e))}function ae(t,e){var n=t.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(e)}function ce(t,e){t.dom.removeAttribute(e)}function se(t,e,n){if(!y(n))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",n,":: Element ",t),new Error("CSS value must be a string: "+n);ct(t)&&t.style.setProperty(e,n)}function le(t,e){ct(t)&&t.style.removeProperty(e)}function fe(t,e,n){se(t.dom,e,n)}function de(t,e){var n=t.dom;J(e,function(t,e){se(n,e,t)})}function me(t,e){var n=t.dom;J(e,function(t,e){t.fold(function(){le(n,e)},function(t){se(n,e,t)})})}function ge(t,e){var n=t.dom,o=window.getComputedStyle(n).getPropertyValue(e);return""!==o||vn(t)?o:xn(n,e)}function pe(t,e){var n=t.dom,o=xn(n,e);return vt.from(o).filter(function(t){return 0<t.length})}function he(t,e,n){var o=Mt.fromTag(t);return fe(o,e,n),pe(o,e).isSome()}function ve(t,e){le(t.dom,e),mt(ue(t,"style").map(Bt),"")&&ce(t,"style")}function be(t){return t.dom.offsetWidth}function ye(o,r){function t(t){var e=r(t);if(e<=0||null===e){var n=ge(t,o);return parseFloat(n)||0}return e}function i(r,t){return R(t,function(t,e){var n=ge(r,e),o=void 0===n?0:parseInt(n,10);return isNaN(o)?t:t+o},0)}return{set:function(t,e){if(!u(e)&&!e.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+e);var n=t.dom;ct(n)&&(n.style[o]=e+"px")},get:t,getOuter:t,aggregate:i,max:function(t,e,n){var o=i(t,n);return o<e?e-o:0}}}function xe(t){return wn.get(t)}function we(t){return wn.getOuter(t)}function Se(t,e){return void 0!==t?t:void 0!==e?e:0}function ke(t){var e=t.dom.ownerDocument,n=e.body,o=e.defaultView,r=e.documentElement;if(n===t.dom)return kn(n.offsetLeft,n.offsetTop);var i=Se(null==o?void 0:o.pageYOffset,r.scrollTop),u=Se(null==o?void 0:o.pageXOffset,r.scrollLeft),a=Se(r.clientTop,n.clientTop),c=Se(r.clientLeft,n.clientLeft);return Cn(t).translate(u-c,i-a)}function Ce(t){return On.get(t)}function Oe(t){return On.getOuter(t)}function _e(t){function e(){return t.stopPropagation()}function n(){return t.preventDefault()}var o=r(n,e);return{target:Mt.fromDom(function(t){if(mn()&&k(t.target)){var e=Mt.fromDom(t.target);if(Ge(e)&&hn(e)&&t.composed&&t.composedPath){var n=t.composedPath();if(n)return Y(n)}}return vt.from(t.target)}(t).getOr(t.target)),x:t.clientX,y:t.clientY,stop:e,prevent:n,kill:o,raw:t}}function Te(t,e,n,o,r){var i,u,a=(i=n,u=o,function(t){i(t)&&u(_e(t))});return t.dom.addEventListener(e,a,r),{unbind:C(_n,t,e,a,r)}}function Ee(e,n){Yt(e).each(function(t){t.dom.insertBefore(n.dom,e.dom)})}function De(t,e){vt.from(t.dom.nextSibling).map(Mt.fromDom).fold(function(){Yt(t).each(function(t){Tn(t,e)})},function(t){Ee(t,e)})}function Ae(e,n){Jt(e,0).fold(function(){Tn(e,n)},function(t){e.dom.insertBefore(n.dom,t.dom)})}function Be(e,t){St(t,function(t){Tn(e,t)})}function Me(t){t.dom.textContent="",St(Kt(t),function(t){En(t)})}function Fe(t){var e,n=Kt(t);0<n.length&&(e=t,St(n,function(t){Ee(e,t)})),En(t)}function Ie(t){var e=void 0!==t?t.dom:document,n=e.body.scrollLeft||e.documentElement.scrollLeft,o=e.body.scrollTop||e.documentElement.scrollTop;return kn(n,o)}function Re(t,e,n){var o=(void 0!==n?n.dom:document).defaultView;o&&o.scrollTo(t,e)}function Ne(t,e,n,o){return{x:t,y:e,width:n,height:o,right:t+n,bottom:e+o}}function Pe(t){var o=void 0===t?window:t,e=o.document,r=Ie(Mt.fromDom(e)),n=void 0===o?window:o;return(ln().browser.isFirefox()?vt.none():vt.from(n.visualViewport)).fold(function(){var t=o.document.documentElement,e=t.clientWidth,n=t.clientHeight;return Ne(r.left,r.top,e,n)},function(t){return Ne(Math.max(t.pageLeft,r.left),Math.max(t.pageTop,r.top),t.width,t.height)})}function Ve(o){var t,r=Ie(Mt.fromDom(document)),e=(t=An).owner(o),n=Dn(t,e);return vt.some(n).fold(C(ke,o),function(t){var e=Cn(o),n=I(t,function(t,e){var n=Cn(e);return{left:t.left+n.left,top:t.top+n.top}},{left:0,top:0});return kn(n.left+e.left+r.left,n.top+e.top+r.top)})}function He(t){var e=Ve(t),n=Oe(t),o=we(t);return Bn(e.left,e.top,n,o)}"undefined"!=typeof window||Function("return this;")();function Le(){return Ke(0,0)}function ze(t){function e(t){return function(){return n===t}}var n=t.current,o=t.version;return{current:n,version:o,isEdge:e("Edge"),isChrome:e("Chrome"),isIE:e("IE"),isOpera:e("Opera"),isFirefox:e(Ze),isSafari:e("Safari")}}function Ue(t){function e(t){return function(){return n===t}}var n=t.current,o=t.version;return{current:n,version:o,isWindows:e(nn),isiOS:e("iOS"),isAndroid:e(on),isOSX:e("OSX"),isLinux:e("Linux"),isSolaris:e(rn),isFreeBSD:e(un),isChromeOS:e(an)}}var je,We,Ge=It(1),Xe=It(3),Ye=It(9),qe=It(11),Ke=function(t,e){return{major:t,minor:e}},Je={nu:Ke,detect:function(t,e){var n,o,r=String(e).toLowerCase();return 0===t.length?Le():(o=function(t,e){for(var n=0;n<t.length;n++){var o=t[n];if(o.test(e))return o}}(t,n=r))?Ke(i(1),i(2)):{major:0,minor:0};function i(t){return Number(n.replace(o,"$"+t))}},unknown:Le},$e=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Qe={browsers:rt([{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return ut(t,"edge/")&&ut(t,"chrome")&&ut(t,"safari")&&ut(t,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,$e],search:function(t){return ut(t,"chrome")&&!ut(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return ut(t,"msie")||ut(t,"trident")}},{name:"Opera",versionRegexes:[$e,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Pt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Pt("firefox")},{name:"Safari",versionRegexes:[$e,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(ut(t,"safari")||ut(t,"mobile/"))&&ut(t,"applewebkit")}}]),oses:rt([{name:"Windows",search:Pt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return ut(t,"iphone")||ut(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Pt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Pt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Pt("linux"),versionRegexes:[]},{name:"Solaris",search:Pt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Pt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Pt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}])},Ze="Firefox",tn=function(){return ze({current:void 0,version:Je.unknown()})},en=ze,nn=(rt("Edge"),rt("Chrome"),rt("IE"),rt("Opera"),rt(Ze),rt("Safari"),"Windows"),on="Android",rn="Solaris",un="FreeBSD",an="ChromeOS",cn=function(){return Ue({current:void 0,version:Je.unknown()})},sn=Ue,ln=(rt(nn),rt("iOS"),rt(on),rt("Linux"),rt("OSX"),rt(rn),rt(un),rt(an),Rt(function(){return t=navigator.userAgent,e=vt.from(navigator.userAgentData),n=Vt,p=Qe.browsers(),h=Qe.oses(),v=e.bind(function(t){return o=p,K(t.brands,function(e){var n=e.brand.toLowerCase();return N(o,function(t){var e;return n===(null===(e=t.brand)||void 0===e?void 0:e.toLowerCase())}).map(function(t){return{current:t.name,version:Je.nu(parseInt(e.version,10),0)}})});var o}).orThunk(function(){return Nt(p,n=t).map(function(t){var e=Je.detect(t.versionRegexes,n);return{current:t.name,version:e}});var n}).fold(tn,en),b=Nt(h,o=t).map(function(t){var e=Je.detect(t.versionRegexes,o);return{current:t.name,version:e}}).fold(cn,sn),{browser:v,os:b,deviceType:(i=v,u=t,a=n,c=(r=b).isiOS()&&!0===/ipad/i.test(u),s=r.isiOS()&&!c,f=(l=r.isiOS()||r.isAndroid())||a("(pointer:coarse)"),d=c||!s&&l&&a("(min-device-width:768px)"),m=s||l&&!d,g=i.isSafari()&&r.isiOS()&&!1===/safari/i.test(u),{isiPad:rt(c),isiPhone:rt(s),isTablet:rt(d),isPhone:rt(m),isTouch:rt(f),isAndroid:r.isAndroid,isiOS:r.isiOS,isWebView:rt(g),isDesktop:rt(!m&&!d&&!g)})};var t,e,n,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b})),fn=Yt,dn=S(Element.prototype.attachShadow)&&S(Node.prototype.getRootNode),mn=rt(dn),gn=dn?function(t){return Mt.fromDom(t.dom.getRootNode())}:Wt,pn=function(t){var e=gn(t);return Zt(e)?vt.some(e):vt.none()},hn=function(t){return k(t.dom.shadowRoot)},vn=function(t){var e=Xe(t)?t.dom.parentNode:t.dom;if(null==e||null===e.ownerDocument)return!1;var n,o,r=e.ownerDocument;return pn(Mt.fromDom(e)).fold(function(){return r.body.contains(e)},(n=vn,o=ee,function(t){return n(o(t))}))},bn=function(){return yn(Mt.fromDom(document))},yn=function(t){var e=t.dom.body;if(null==e)throw new Error("Body is not available yet");return Mt.fromDom(e)},xn=function(t,e){return ct(t)?t.style.getPropertyValue(e):""},wn=ye("height",function(t){var e=t.dom;return vn(t)?e.getBoundingClientRect().height:e.offsetHeight}),Sn=function(n,o){return{left:n,top:o,translate:function(t,e){return Sn(n+t,o+e)}}},kn=Sn,Cn=function(t){var e,n=t.dom,o=n.ownerDocument.body;return o===n?kn(o.offsetLeft,o.offsetTop):vn(t)?(e=n.getBoundingClientRect(),kn(e.left,e.top)):kn(0,0)},On=ye("width",function(t){return t.dom.offsetWidth}),_n=function(t,e,n,o){t.dom.removeEventListener(e,n,o)},Tn=function(t,e){t.dom.appendChild(e.dom)},En=function(t){var e=t.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},Dn=function(o,t){return o.view(t).fold(rt([]),function(t){var e=o.owner(t),n=Dn(o,e);return[t].concat(n)})},An=Object.freeze({__proto__:null,view:function(t){var e;return(t.dom===document?vt.none():vt.from(null===(e=t.dom.defaultView)||void 0===e?void 0:e.frameElement)).map(Mt.fromDom)},owner:jt}),Bn=function(t,e,n,o){return{x:t,y:e,width:n,height:o,right:t+n,bottom:e+o}},Mn=function(t){var e=ke(t),n=Oe(t),o=we(t);return Bn(e.left,e.top,n,o)},Fn=function(){return Pe(window)},In=function(n){return{isValue:D,isError:T,getOr:rt(n),getOrThunk:rt(n),getOrDie:rt(n),or:function(t){return In(n)},orThunk:function(t){return In(n)},fold:function(t,e){return e(n)},map:function(t){return In(t(n))},mapError:function(t){return In(n)},each:function(t){t(n)},bind:function(t){return t(n)},exists:function(t){return t(n)},forall:function(t){return t(n)},toOptional:function(){return vt.some(n)}}},Rn=function(n){return{isValue:T,isError:D,getOr:h,getOrThunk:function(t){return t()},getOrDie:function(){return _(String(n))()},or:h,orThunk:function(t){return t()},fold:function(t,e){return t(n)},map:function(t){return Rn(n)},mapError:function(t){return Rn(t(n))},each:st,bind:function(t){return Rn(n)},exists:T,forall:D,toOptional:vt.none}},Nn={value:In,error:Rn,fromOption:function(t,e){return t.fold(function(){return Rn(e)},In)}};function Pn(t,e,n){return t.stype===je.Error?e(t.serror):n(t.svalue)}function Vn(t){return{stype:je.Value,svalue:t}}function Hn(t){return{stype:je.Error,serror:t}}function Ln(t,e,n,o){return{tag:"field",key:t,newKey:e,presence:n,prop:o}}function zn(t,e,n){switch(t.tag){case"field":return e(t.key,t.newKey,t.presence,t.prop);case"custom":return n(t.newKey,t.instantiator)}}function Un(u){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(0===t.length)throw new Error("Can't merge zero objects");for(var n={},o=0;o<t.length;o++){var r,i=t[o];for(r in i)Tt(i,r)&&(n[r]=u(n[r],i[r]))}return n}}function jn(){return{tag:"required",process:{}}}function Wn(t){return{tag:"defaultedThunk",process:t}}function Gn(t){return Wn(rt(t))}function Xn(){return{tag:"option",process:{}}}function Yn(t){return{tag:"mergeWithThunk",process:t}}function qn(t){return x(t)&&100<Ct(t).length?" removed due to size":JSON.stringify(t,null,2)}function Kn(t,e){return zo([{path:t,getErrorInfo:e}])}function Jn(n){return{extract:function(e,t){return jo(n(t),function(t){return Kn(e,rt(t))})},toString:rt("val")}}function $n(t,e,n,o){return o(tt(t,e).getOrThunk(function(){return n(t)}))}function Qn(e,n,o,r,i){function u(t){return i.extract(n.concat([r]),t)}function t(t){return t.fold(function(){return Lo(vt.none())},function(t){var e=i.extract(n.concat([r]),t);return Wo(e,vt.some)})}var a,c,s,l,f,d;switch(e.tag){case"required":return s=n,d=u,tt(l=o,f=r).fold(function(){return t=f,e=l,Kn(s,function(){return'Could not find valid *required* value for "'+t+'" in '+qn(e)});var t,e},d);case"defaultedThunk":return $n(o,r,e.process,u);case"option":return t(tt(o,r));case"defaultedOptionThunk":return c=e.process,t(tt(a=o,r).map(function(t){return!0===t?c(a):t}));case"mergeWithThunk":return $n(o,r,rt({}),function(t){return u(Yo(e.process(o),t))})}}function Zn(n){return{extract:function(t,e){return n().extract(t,e)},toString:function(){return n().toString()}}}function to(t){var s=$o(t),l=I(t,function(n,t){return zn(t,function(t){var e;return Yo(n,((e={})[t]=!0,e))},rt(n))},{});return{extract:function(t,e){var n,o,r,i,u,a,c=F(w(e)?[]:Ct((r=k,i=o={},u=function(t,e){i[e]=t},a=st,J(e,function(t,e){(r(t,e)?u:a)(t,e)}),o)),function(t){return!et(l,t)});return 0===c.length?s.extract(t,e):(n=c,Kn(t,function(){return"There are unsupported fields: ["+n.join(", ")+"] specified"}))},toString:s.toString}}function eo(o){return{extract:function(n,t){var e=B(t,function(t,e){return o.extract(n.concat(["["+e+"]"]),t)});return Ko(e)},toString:function(){return"array("+o.toString()+")"}}}function no(u){return{extract:function(t,e){for(var n=[],o=0,r=u;o<r.length;o++){var i=r[o].extract(t,e);if(i.stype===je.Value)return i;n.push(i)}return Ko(n)},toString:function(){return"oneOf("+B(u,function(t){return t.toString()}).join(", ")+")"}}}function oo(n,o){return Jn(function(t){var e=typeof t;return n(t)?Lo(t):zo("Expected type: "+o+" but got: "+e)})}function ro(e,a){return{extract:function(i,u){return tt(u,e).fold(function(){return t=e,Kn(i,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return n=i,e=u,tt(o=a,r=t).fold(function(){return t=o,e=r,Kn(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+qn(t)});var t,e},function(t){return t.extract(n.concat(["branch: "+r]),e)});var n,e,o,r})},toString:function(){return"chooseOn("+e+"). Possible values: "+Ct(a)}}}function io(e){return Jn(function(t){return e(t).fold(zo,Lo)})}function uo(e,t){return r=function(t){return e(t).fold(Hn,Vn)},i=t,{extract:function(n,o){var t=Ct(o),e=eo(Jn(r)).extract(n,t);return Uo(e,function(t){var e=B(t,function(t){return Ln(t,t,jn(),i)});return $o(e).extract(n,o)})},toString:function(){return"setOf("+i.toString()+")"}};var r,i}function ao(t,e,n){return Ho((r=e.extract([t],o=n),Go(r,function(t){return{input:o,errors:t}})));var o,r}function co(t){return t.fold(function(t){throw new Error(ur(t))},h)}function so(t,e,n){return co(ao(t,e,n))}function lo(t,e){return ro(t,dt(e,$o))}function fo(e){return io(function(t){return wt(e,t)?Nn.value(t):Nn.error('Unsupported value: "'+t+'", choose one of "'+e.join(", ")+'".')})}function mo(t){return ar(t,t,jn(),Zo())}function go(t,e){return ar(t,t,jn(),e)}function po(t){return go(t,er)}function ho(t,e){return ar(t,t,jn(),fo(e))}function vo(t){return go(t,or)}function bo(t,e){return ar(t,t,jn(),$o(e))}function yo(t,e){return ar(t,t,jn(),Qo(e))}function xo(t,e){return ar(t,t,jn(),eo(e))}function wo(t){return ar(t,t,Xn(),Zo())}function So(t,e){return ar(t,t,Xn(),e)}function ko(t){return So(t,tr)}function Co(t){return So(t,er)}function Oo(t){return So(t,or)}function _o(t,e){return So(t,eo(e))}function To(t,e){return So(t,$o(e))}function Eo(t,e){return ar(t,t,Gn(e),Zo())}function Do(t,e,n){return ar(t,t,Gn(e),n)}function Ao(t,e){return Do(t,e,tr)}function Bo(t,e){return Do(t,e,er)}function Mo(t,e,n){return Do(t,e,fo(n))}function Fo(t,e){return Do(t,e,nr)}function Io(t,e){return Do(t,e,or)}function Ro(t,e,n){return Do(t,e,eo(n))}function No(t,e,n){return Do(t,e,$o(n))}function Po(t){var e=t;return{get:function(){return e},set:function(t){e=t}}}(We=je={})[We.Error=0]="Error",We[We.Value=1]="Value";function Vo(u){if(!c(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],n={};return St(u,function(t,o){var e=Ct(t);if(1!==e.length)throw new Error("one and only one name per case");var r=e[0],i=t[r];if(void 0!==n[r])throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!c(i))throw new Error("case arguments must be an array");a.push(r),n[r]=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=n.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+e);return{fold:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(t.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+t.length);return t[o].apply(null,n)},match:function(t){var e=Ct(t);if(a.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+e.join(","));if(!L(a,function(t){return wt(e,t)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,n)},log:function(t){console.log(t,{constructors:a,constructor:r,params:n})}}}}),n}var Ho=function(t){return Pn(t,Nn.error,Nn.value)},Lo=Vn,zo=Hn,Uo=function(t,e){return t.stype===je.Value?e(t.svalue):t},jo=function(t,e){return t.stype===je.Error?e(t.serror):t},Wo=function(t,e){return t.stype===je.Value?{stype:je.Value,svalue:e(t.svalue)}:t},Go=function(t,e){return t.stype===je.Error?{stype:je.Error,serror:e(t.serror)}:t},Xo=Pn,Yo=Un(function(t,e){return x(t)&&x(e)?Yo(t,e):e}),qo=Un(function(t,e){return e}),Ko=function(t){var e,n,o=(e=[],n=[],St(t,function(t){Pn(t,function(t){return n.push(t)},function(t){return e.push(t)})}),{values:e,errors:n});return 0<o.errors.length?r(zo,ft)(o.errors):Lo(o.values)},Jo=Jn(Lo),$o=function(n){return{extract:function(i,u){for(var a={},c=[],t=0,e=n;t<e.length;t++)zn(e[t],function(t,e,n,o){var r=Qn(n,i,u,t,o);Xo(r,function(t){c.push.apply(c,t)},function(t){a[e]=t})},function(t,e){a[t]=e(u)});return 0<c.length?zo(c):Lo(a)},toString:function(){return"obj{\n"+B(n,function(t){return zn(t,function(t,e,n,o){return t+" -> "+o.toString()},function(t,e){return"state("+t+")"})}).join("\n")+"}"}}},Qo=r(eo,$o),Zo=rt(Jo),tr=oo(u,"number"),er=oo(y,"string"),nr=oo(w,"boolean"),or=oo(S,"function"),rr=function(e){if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every(function(t){return rr(e[t])});default:return!1}},ir=Jn(function(t){return rr(t)?Lo(t):zo("Expected value to be acceptable for sending via postMessage")}),ur=function(t){return"Errors: \n"+B(10<(e=t.errors).length?e.slice(0,10).concat([{path:[],getErrorInfo:rt("... (only showing first ten failures)")}]):e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n")+"\n\nInput object: "+qn(t.input);var e},ar=Ln,cr=function(t,e){return{tag:"custom",newKey:t,instantiator:e}};function sr(t,e){return(n={})[t]=e,n;var n}function lr(t){return e={},St(t,function(t){e[t.key]=t.value}),e;var e}function fr(t){return S(t)?t:T}function dr(t,e,n){for(var o=t.dom,r=fr(n);o.parentNode;){var o=o.parentNode,i=Mt.fromDom(o),u=e(i);if(u.isSome())return u;if(r(i))break}return vt.none()}function mr(t,e,n){var o=e(t),r=fr(n);return o.orThunk(function(){return r(t)?vt.none():dr(t,e,r)})}function gr(t,e){return zt(t.element,e.event.target)}function pr(t){if(!et(t,"can")&&!et(t,"abort")&&!et(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return lt(lt({},ai),t)}function hr(t){return rt("alloy."+t)}function vr(t,e){eu(t,t.element,e,{})}function br(t,e,n){eu(t,t.element,e,n)}function yr(t){vr(t,Ii())}function xr(t,e,n){eu(t,e,n,{})}function wr(t,e,n,o){t.getSystem().triggerEvent(n,e,o.event)}function Sr(t,e){return{key:t,value:pr({abort:e})}}function kr(t){return{key:t,value:pr({run:function(t,e){e.event.prevent()}})}}function Cr(t,e){return{key:t,value:pr({run:e})}}function Or(t,n,o){return{key:t,value:pr({run:function(t,e){n.apply(void 0,[t,e].concat(o))}})}}function _r(t){return function(n){return{key:t,value:pr({run:function(t,e){gr(t,e)&&n(t,e)}})}}}function Tr(t,e,n){var o,r=e.partUids[n];return Cr(o=t,function(t,e){t.getSystem().getByUid(r).each(function(t){wr(t,t.element,o,e)})})}function Er(t,r){return Cr(t,function(e,t){var n=t.event,o=e.getSystem().getByDom(n.target).getOrThunk(function(){return mr(n.target,function(t){return e.getSystem().getByDom(t).toOptional()},T).getOr(e)});r(e,o,t)})}function Dr(t){return Cr(t,function(t,e){e.cut()})}function Ar(t,e){return _r(t)(e)}function Br(t){return t.dom.innerHTML}function Mr(t,e){var n,o,r=jt(t).dom,i=Mt.fromDom(r.createDocumentFragment());Be(i,(n=e,(o=(r||document).createElement("div")).innerHTML=n,Kt(Mt.fromDom(o)))),Me(t),Tn(t,i)}function Fr(t){if(Zt(t))return"#shadow-root";var e=Mt.fromDom(t.dom.cloneNode(!1)),n=Mt.fromTag("div"),o=Mt.fromDom(e.dom.cloneNode(!0));return Tn(n,o),Br(n)}function Ir(t){var e=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++su+String(e)}function Rr(t){var e=Ge(t)?t.dom[mu]:null;return vt.from(e)}function Nr(e){function n(t){return"The component must be in a context to execute: "+t+(e?"\n"+Fr(e().element)+" is not in context.":"")}function t(t){return function(){throw new Error(n(t))}}function o(t){return function(){console.warn(n(t))}}return{debugInfo:rt("fake"),triggerEvent:o("triggerEvent"),triggerFocus:o("triggerFocus"),triggerEscape:o("triggerEscape"),broadcast:o("broadcast"),broadcastOn:o("broadcastOn"),broadcastEvent:o("broadcastEvent"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),isConnected:T}}function Pr(t,e){var n=t.toString(),o=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:e,parameters:bu(i)}},t}function Vr(t){return sr(yu,t)}function Hr(o){return t=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return o.apply(void 0,V([t.getApis(),t],e,!1))},n=(e=o.toString()).indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,n-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:bu(i.slice(1))}},t;var t,e,n,r,i}function Lr(t,r){var i={};return J(t,function(t,o){J(t,function(t,e){var n=tt(i,e).getOr([]);i[e]=n.concat([r(o,t)])})}),i}function zr(t){return{classes:E(t.classes)?[]:t.classes,attributes:E(t.attributes)?{}:t.attributes,styles:E(t.styles)?{}:t.styles}}function Ur(t){return t.cHandler}function jr(t,e){return{name:t,handler:e}}function Wr(t,e,n){var o=e[n];return o?function(u,t,a){try{var e=G(t,function(t,e){var n=t.name,o=e.name,r=a.indexOf(n),i=a.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+n+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(a,null,2));return r<i?-1:i<r?1:0});return Nn.value(e)}catch(t){return Nn.error([t])}}("Event: "+n,t,o).map(function(t){var n,e,o,r,i=B(t,function(t){return t.handler});return{can:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return R(e,function(t,e){return t&&e.can.apply(void 0,n)},!0)},abort:(o=e=n=i,r=function(t){return t.abort},function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return R(o,function(t,e){return t||r(e).apply(void 0,n)},!1)}),run:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];St(n,function(t){t.run.apply(void 0,e)})}}}):Nn.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(B(t,function(t){return t.name}),null,2)])}function Gr(t,e){var n=ie(t,e);return void 0===n||""===n?[]:n.split(" ")}function Xr(t){return void 0!==t.dom.classList}function Yr(t,e){var n,o,r;Xr(t)?t.dom.classList.add(e):(o=e,r=Gr(n=t,"class").concat([o]),oe(n,"class",r.join(" ")))}function qr(t,e){var n,o,r,i;Xr(t)?t.dom.classList.remove(e):(r=e,0<(i=F(Gr(o=t,"class"),function(t){return t!==r})).length?oe(o,"class",i.join(" ")):ce(o,"class")),0===(Xr(n=t)?n.dom.classList:Gr(n,"class")).length&&ce(n,"class")}function Kr(t,e){return Xr(t)&&t.dom.classList.contains(e)}function Jr(e,t){St(t,function(t){Yr(e,t)})}function $r(e,t){St(t,function(t){qr(e,t)})}function Qr(t){return t.dom.value}function Zr(t,e){if(void 0===e)throw new Error("Value.set was undefined");t.dom.value=e}function ti(t){var n,e,o,r,i=(n=tt(t,"behaviours").getOr({}),H(Ct(n),function(t){var e=n[t];return k(e)?[e.me]:[]}));return e=t,r=B(o=i,function(t){return To(t.name(),[mo("config"),Eo("state",xu)])}),{list:o,data:dt(ao("component.behaviours",$o(r),e.behaviours).fold(function(t){throw new Error(ur(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},h),function(t){return rt(t.map(function(t){return{config:t.config,state:t.state.init(t.config)}}))})}}function ei(t,e,n){var o,r,i=lt(lt({},(o=t).dom),{uid:o.uid,domChildren:B(o.components,function(t){return t.element})}),u=t.domModification.fold(function(){return zr({})},zr),a=0<e.length?function(e,t,n,o){var r=lt({},t);function i(t){return I(t,function(t,e){return lt(lt({},e.modification),t)},{})}St(n,function(t){r[t.name()]=t.exhibit(e,o)});var u=Lr(r,function(t,e){return{name:t,modification:e}});return zr({classes:I(u.classes,function(t,e){return e.modification.concat(t)},[]),attributes:i(u.attributes),styles:i(u.styles)})}(n,{"alloy.base.modification":u},e,i):u;return lt(lt({},r=i),{attributes:lt(lt({},r.attributes),a.attributes),styles:lt(lt({},r.styles),a.styles),classes:r.classes.concat(a.classes)})}function ni(t,e,n){var o,r,i,u,a,c={"alloy.base.behaviour":t.events},s=t.eventOrder;return r=n,i=e,o=Lr(lt(lt({},c),(u=r,a={},St(i,function(t){a[t.name()]=t.handlers(u)}),a)),jr),Su(o,s).getOrDie()}function oi(t){var n,e,o,r,i,u,a,c,s,l,f=hu(t),d=f.events,m=A(f,["events"]),g=B(tt(m,"components").getOr([]),Tu),p=lt(lt({},m),{events:lt(lt({},cu),d),components:g});return Nn.value((e=Po(vu),o=co(ao("custom.definition",Cu,n=p)),r=ti(n),i=r.list,u=r.data,a=function(t){var e=Mt.fromTag(t.tag);re(e,t.attributes),Jr(e,t.classes),de(e,t.styles),t.innerHtml.each(function(t){return Mr(e,t)});var n=t.domChildren;return Be(e,n),t.value.each(function(t){Zr(e,t)}),t.uid,gu(e,t.uid),e}(ei(o,i,u)),c=ni(o,i,u),s=Po(o.components),l={uid:n.uid,getSystem:e.get,config:function(t){var e=u;return(S(e[t.name()])?e[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(n,null,2))})()},hasConfigured:function(t){return S(u[t.name()])},spec:n,readState:function(t){return u[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return o.apis},connect:function(t){e.set(t)},disconnect:function(){e.set(Nr(h))},element:a,syncComponents:function(){var t=H(Kt(a),function(t){return e.get().getByDom(t).fold(function(){return[]},W)});s.set(t)},components:s.get,events:c}));function h(){return l}}function ri(t){var e=Mt.fromText(t);return Ou({element:e})}Vo([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var ii,ui,ai={can:D,abort:T,run:st},ci=rt,si=ci("touchstart"),li=ci("touchmove"),fi=ci("touchend"),di=ci("touchcancel"),mi=ci("mousedown"),gi=ci("mousemove"),pi=ci("mouseout"),hi=ci("mouseup"),vi=ci("mouseover"),bi=ci("focusin"),yi=ci("focusout"),xi=ci("keydown"),wi=ci("keyup"),Si=ci("input"),ki=ci("change"),Ci=ci("click"),Oi=ci("transitioncancel"),_i=ci("transitionend"),Ti=ci("transitionstart"),Ei=ci("selectstart"),Di={tap:hr("tap")},Ai=hr("focus"),Bi=hr("blur.post"),Mi=hr("paste.post"),Fi=hr("receive"),Ii=hr("execute"),Ri=hr("focus.item"),Ni=Di.tap,Pi=hr("longpress"),Vi=hr("sandbox.close"),Hi=hr("typeahead.cancel"),Li=hr("system.init"),zi=hr("system.touchmove"),Ui=hr("system.touchend"),ji=hr("system.scroll"),Wi=hr("system.resize"),Gi=hr("system.attached"),Xi=hr("system.detached"),Yi=hr("system.dismissRequested"),qi=hr("system.repositionRequested"),Ki=hr("focusmanager.shifted"),Ji=hr("slotcontainer.visibility"),$i=hr("change.tab"),Qi=hr("dismiss.tab"),Zi=hr("highlight"),tu=hr("dehighlight"),eu=function(t,e,n,o){var r=lt({target:e},o);t.getSystem().triggerEvent(n,e,r)},nu=lr,ou=_r(Gi()),ru=_r(Xi()),iu=_r(Li()),uu=(ii=Ii(),function(t){return Cr(ii,t)}),au=nu([{key:Ai(),value:pr({can:function(t,e){var n,o=e.event,r=o.originator,i=o.target;return!(zt(n=r,t.element)&&!zt(n,i)&&(console.warn(Ai()+" did not get interpreted by the desired target. \nOriginator: "+Fr(r)+"\nTarget: "+Fr(i)+"\nCheck the "+Ai()+" event handlers"),1))}})}]),cu=Object.freeze({__proto__:null,events:au}),su=0,lu=rt("alloy-id-"),fu=rt("data-alloy-id"),du=lu(),mu=fu(),gu=function(t,e){Object.defineProperty(t.dom,mu,{value:e,writable:!0})},pu=Ir,hu=h,vu=Nr(),bu=function(t){return B(t,function(t){return At(t,"/*")?t.substring(0,t.length-"/*".length):t})},yu=Ir("alloy-premade"),xu={init:function(){return wu({readState:rt("No State required")})}},wu=function(t){return t},Su=function(t,a){var e,n,o,r,i,u,c=$(t,function(r,u){return(1===r.length?Nn.value(r[0].handler):Wr(r,a,u)).map(function(t){var e,i,n=(i=S(e=t)?{can:D,abort:T,run:e}:e,function(t,e){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];var r=[t,e].concat(n);i.abort.apply(void 0,r)?e.stop():i.can.apply(void 0,r)&&i.run.apply(void 0,r)}),o=1<r.length?F(a[u],function(e){return d(r,function(t){return t.name===e})}).join(" > "):r[0].name;return sr(u,{handler:n,purpose:o})})});return e={},n=[],o=[],St(c,function(t){t.fold(function(t){n.push(t)},function(t){o.push(t)})}),0<(u={errors:n,values:o}).errors.length?Nn.error(ft(u.errors)):(i=e,0===(r=u.values).length?Nn.value(i):Nn.value(Yo(i,qo.apply(void 0,r))))},ku="alloy.base.behaviour",Cu=$o([ar("dom","dom",jn(),$o([mo("tag"),Eo("styles",{}),Eo("classes",[]),Eo("attributes",{}),wo("value"),wo("innerHtml")])),mo("components"),mo("uid"),Eo("events",{}),Eo("apis",{}),ar("eventOrder","eventOrder",((ui={})[Ii()]=["disabling",ku,"toggling","typeaheadevents"],ui[Ai()]=[ku,"focusing","keying"],ui[Li()]=[ku,"disabling","toggling","representing"],ui[Si()]=[ku,"representing","streaming","invalidating"],ui[Xi()]=[ku,"representing","item-events","tooltipping"],ui[mi()]=["focusing",ku,"item-type-events"],ui[si()]=["focusing",ku,"item-type-events"],ui[vi()]=["item-type-events","tooltipping"],ui[Fi()]=["receiving","reflecting","tooltipping"],Yn(rt(ui))),Zo()),wo("domModification")]),Ou=function(t){var e=so("external.component",to([mo("element"),wo("uid")]),t),n=Po(Nr()),o=e.uid.getOrThunk(function(){return pu("external")});gu(e.element,o);var r={uid:o,getSystem:n.get,config:vt.none,hasConfigured:T,connect:function(t){n.set(t)},disconnect:function(){n.set(Nr(function(){return r}))},getApis:function(){return{}},element:e.element,spec:t,readState:rt("No state"),syncComponents:st,components:rt([]),events:{}};return Vr(r)},_u=pu,Tu=function(t){return tt(t,yu).getOrThunk(function(){return oi(Tt(t,"uid")?t:lt({uid:_u("")},t)).getOrDie()})},Eu=Vr;function Du(t,e,n,o,r){return t(n,o)?vt.some(n):S(r)&&r(n)?vt.none():e(n,o,r)}function Au(t,e,n){for(var o=t.dom,r=S(n)?n:T;o.parentNode;){var o=o.parentNode,i=Mt.fromDom(o);if(e(i))return vt.some(i);if(r(i))break}return vt.none()}function Bu(t,e,n){return Du(function(t,e){return e(t)},Au,t,e,n)}function Mu(t,e,n){return Bu(t,e,n).isSome()}function Fu(t,e,n){return Au(t,function(t){return Ht(t,e)},n)}function Iu(t,e){return n=e,Lt(o=void 0===t?document:t.dom)?vt.none():vt.from(o.querySelector(n)).map(Mt.fromDom);var n,o}function Ru(t,e,n){return Du(Ht,Fu,t,e,n)}function Nu(){var e=Ir("aria-owns");return{id:e,link:function(t){oe(t,"aria-owns",e)},unlink:function(t){ce(t,"aria-owns")}}}var Pu,Vu,Hu=function(e,t){return Mu(t,function(t){return zt(t,e.element)},T)||(n=e,Bu(t,function(t){if(!Ge(t))return!1;var e=ie(t,"id");return void 0!==e&&-1<e.indexOf("aria-owns")}).bind(function(t){var e=ie(t,"id");return Iu(gn(t),'[aria-owns="'+e+'"]')}).exists(function(t){return Hu(n,t)}));var n},Lu="unknown";function zu(e,t,n){var o,r,i,u;switch(tt(Uu.get(),e).orThunk(function(){return K(Ct(Uu.get()),function(t){return-1<e.indexOf(t)?vt.some(Uu.get()[t]):vt.none()})}).getOr(Pu.NORMAL)){case Pu.NORMAL:return n(Wu());case Pu.LOGGING:var a=(o=e,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,e,n){i.push({outcome:"cut",target:e,purpose:n})},logEventStopped:function(t,e,n){i.push({outcome:"stopped",target:e,purpose:n})},logNoParent:function(t,e,n){i.push({outcome:"no-parent",target:e,purpose:n})},logEventNoHandlers:function(t,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(t,e,n){i.push({outcome:"response",purpose:n,target:e})},write:function(){var t=(new Date).getTime();wt(["mousemove","mouseover","mouseout",Li()],o)||console.log(o,{event:o,time:t-u,target:r.dom,sequence:B(i,function(t){return wt(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Fr(t.target)+")":t.outcome})})}}),c=n(a);return a.write(),c;case Pu.STOP:return!0}}(Vu=Pu=Pu||{})[Vu.STOP=0]="STOP",Vu[Vu.NORMAL=1]="NORMAL",Vu[Vu.LOGGING=2]="LOGGING";var Uu=Po({}),ju=["alloy/data/Fields","alloy/debugging/Debugging"],Wu=rt({logEventCut:st,logEventStopped:st,logNoParent:st,logEventNoHandlers:st,logEventResponse:st,write:st}),Gu=rt([mo("menu"),mo("selectedMenu")]),Xu=rt([mo("item"),mo("selectedItem")]);function Yu(){return bo("markers",[mo("backgroundMenu")].concat(Gu()).concat(Xu()))}function qu(t){return bo("markers",B(t,mo))}function Ku(t,e,n){return void 0!==(o=new Error).stack&&N(o.stack.split("\n"),function(e){return 0<e.indexOf("alloy")&&!d(ju,function(t){return-1<e.indexOf(t)})}).getOr(Lu),ar(e,e,n,io(function(n){return Nn.value(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n.apply(void 0,t)})}));var o}function Ju(t){return Ku(0,t,Gn(st))}function $u(t){return Ku(0,t,Gn(vt.none))}function Qu(t){return Ku(0,t,jn())}function Zu(t){return Ku(0,t,jn())}function ta(t,e){return cr(t,rt(e))}function ea(t){return cr(t,h)}function na(t,e,n,o,r,i,u,a){return{x:t,y:e,bubble:n,direction:o,placement:r,restriction:i,label:u+"-"+r,alwaysFit:a=void 0!==a&&a}}function oa(t,e,n,o){var r=t+e;return o<r?n:r<n?o:r}function ra(n,e){return j(["left","right","top","bottom"],function(t){return tt(e,t).map(function(e){return function(t){switch(e){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(n)})})}function ia(t,e){return t.x+t.width/2-e.width/2}function ua(t,e){return t.x+t.width-e.width}function aa(t,e){return t.y-e.height}function ca(t){return t.y+t.height}function sa(t,e){return t.y+t.height/2-e.height/2}function la(t,e,n){return na(t.x+t.width,sa(t,e),n.east(),Ua(),"east",ra(t,{left:0}),Ga)}function fa(t,e,n){return na(t.x-e.width,sa(t,e),n.west(),ja(),"west",ra(t,{right:1}),Ga)}function da(){return[Xa,Ya,qa,Ka,$a,Ja,la,fa]}function ma(){return[Ya,Xa,Ka,qa,$a,Ja,la,fa]}function ga(){return[qa,Ka,Xa,Ya,Ja,$a]}function pa(){return[Ka,qa,Ya,Xa,Ja,$a]}function ha(){return[Xa,Ya,qa,Ka,$a,Ja]}function va(){return[Ya,Xa,Ka,qa,$a,Ja]}function ba(n,o,r){return iu(function(t,e){r(t,n,o)})}function ya(t){return{key:t,value:void 0}}function xa(t){var e,n,o,r,i,u,a,c,s=so("Creating behaviour: "+t.name,nc,t);return e=s.fields,n=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,a=to(e),c=To(n,[So("config",to(e))]),tc(a,c,n,o,r,i,u)}function wa(t){var e,n=so("Creating behaviour: "+t.name,oc,t),o=lo(n.branchKey,n.branches),r=n.name,i=n.active,u=n.apis,a=n.extra,c=n.state,s=To(r,[So("config",e=o)]);return tc(e,s,r,i,u,a,c)}function Sa(){return Mt.fromDom(document)}function ka(t){return t.dom.focus()}function Ca(t){var e=gn(t).dom;return t.dom===e.activeElement}function Oa(t){return void 0===t&&(t=Sa()),vt.from(t.dom.activeElement).map(Mt.fromDom)}function _a(e){return Oa(gn(e)).filter(function(t){return e.dom.contains(t.dom)})}function Ta(t,n){var o=gn(n),e=Oa(o).bind(function(e){function t(t){return zt(e,t)}var r,i;return t(n)?vt.some(n):(r=t,(i=function(t){for(var e=0;e<t.childNodes.length;e++){var n=Mt.fromDom(t.childNodes[e]);if(r(n))return vt.some(n);var o=i(t.childNodes[e]);if(o.isSome())return o}return vt.none()})(n.dom))}),r=t(n);return e.each(function(e){Oa(o).filter(function(t){return zt(t,e)}).fold(function(){ka(e)},st)}),r}function Ea(t,e,n,o,r){function i(t){return t+"px"}return{position:t,left:e.map(i),top:n.map(i),right:o.map(i),bottom:r.map(i)}}function Da(t,e){var n;me(t,lt(lt({},n=e),{position:vt.some(n.position)}))}function Aa(t,e,n,o,r,i){var u=e.rect,a=u.x-n,c=u.y-o,s=r-(a+u.width),l=i-(c+u.height),f=vt.some(a),d=vt.some(c),m=vt.some(s),g=vt.some(l),p=vt.none();return e.direction.fold(function(){return Ea(t,f,d,p,p)},function(){return Ea(t,p,d,m,p)},function(){return Ea(t,f,p,p,g)},function(){return Ea(t,p,p,m,g)},function(){return Ea(t,f,d,p,p)},function(){return Ea(t,f,p,p,g)},function(){return Ea(t,f,d,p,p)},function(){return Ea(t,p,d,m,p)})}function Ba(t,r){return t.fold(function(){var t=r.rect;return Ea("absolute",vt.some(t.x),vt.some(t.y),vt.none(),vt.none())},function(t,e,n,o){return Aa("absolute",r,t,e,n,o)},function(t,e,n,o){return Aa("fixed",r,t,e,n,o)})}function Ma(t,e){var n=C(Ve,e),o=t.fold(n,n,function(){var t=Ie();return Ve(e).translate(-t.left,-t.top)}),r=Oe(e),i=we(e);return Bn(o.left,o.top,r,i)}rt($o(Xu().concat(Gu())));var Fa=rt($o(Xu())),Ia=rt(bo("initSize",[mo("numColumns"),mo("numRows")])),Ra=Vo([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Na=Ra.southeast,Pa=Ra.southwest,Va=Ra.northeast,Ha=Ra.northwest,La=Ra.south,za=Ra.north,Ua=Ra.east,ja=Ra.west,Wa=function(t,e,n){return Math.min(Math.max(t,e),n)},Ga="layout",Xa=function(t,e,n){return na(t.x,ca(t),n.southeast(),Na(),"southeast",ra(t,{left:1,top:3}),Ga)},Ya=function(t,e,n){return na(ua(t,e),ca(t),n.southwest(),Pa(),"southwest",ra(t,{right:0,top:3}),Ga)},qa=function(t,e,n){return na(t.x,aa(t,e),n.northeast(),Va(),"northeast",ra(t,{left:1,bottom:2}),Ga)},Ka=function(t,e,n){return na(ua(t,e),aa(t,e),n.northwest(),Ha(),"northwest",ra(t,{right:0,bottom:2}),Ga)},Ja=function(t,e,n){return na(ia(t,e),aa(t,e),n.north(),za(),"north",ra(t,{bottom:2}),Ga)},$a=function(t,e,n){return na(ia(t,e),ca(t),n.south(),La(),"south",ra(t,{top:3}),Ga)},Qa=Object.freeze({__proto__:null,events:function(a){return nu([Cr(Fi(),function(r,t){var e,i=a.channels,n=Ct(i),u=t,o=(e=u).universal?n:F(n,function(t){return wt(e.channels,t)});St(o,function(t){var e=i[t],n=e.schema,o=so("channel["+t+"] data\nReceiver: "+Fr(r.element),n,u.data);e.onReceive(r,o)})})])}}),Za=[go("channels",uo(Nn.value,to([Qu("onReceive"),Eo("schema",Zo())])))],tc=function(n,t,f,e,o,r,i){function u(t){return et(t,f)?t[f]():vt.none()}var a=dt(o,function(t,e){return r=f,n=function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];var o=[n].concat(t);return n.config({name:rt(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var e=Array.prototype.slice.call(o,1);return i.apply(void 0,[n,t.config,t.state].concat(e))})},o=u=e,a=(i=t).toString(),c=a.indexOf(")")+1,s=a.indexOf("("),l=a.substring(s+1,c-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:o,parameters:bu(l.slice(0,1).concat(l.slice(3)))}},n;var r,i,u,n,o,a,c,s,l}),c=dt(r,Pr),s=lt(lt(lt({},c),a),{revoke:C(ya,f),config:function(t){var e=so(f+"-config",n,t);return{key:f,value:{config:e,me:s,configAsRaw:Rt(function(){return so(f+"-config",n,t)}),initialConfig:t,state:i}}},schema:rt(t),exhibit:function(t,n){return Et(u(t),tt(e,"exhibit"),function(t,e){return e(n,t.config,t.state)}).getOrThunk(function(){return zr({})})},name:rt(f),handlers:function(t){return u(t).map(function(t){return tt(e,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return s},ec=lr,nc=to([mo("fields"),mo("name"),Eo("active",{}),Eo("apis",{}),Eo("state",xu),Eo("extra",{})]),oc=to([mo("branchKey"),mo("branches"),mo("name"),Eo("active",{}),Eo("apis",{}),Eo("state",xu),Eo("extra",{})]),rc=rt(void 0),ic=xa({fields:Za,name:"receiving",active:Qa}),uc=Object.freeze({__proto__:null,exhibit:function(t,e){return zr({classes:[],styles:e.useFixed()?{}:{position:"relative"}})}}),ac=Vo([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),cc=function(t,e,n){var o=kn(e,n);return t.fold(rt(o),rt(o),function(){var t=Ie();return o.translate(-t.left,-t.top)})};function sc(t){return ue(t,Dc)}function lc(t,e,n,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,D,A,B,M,F,I,R,N,P,V,H,L,z,U,j=t.bubble,W=j.offset,G=(N=o,P=t.restriction,V=W,H=ot("left",N.x),L=ot("top",N.y),z=ot("right",N.right),U=ot("bottom",N.bottom),Bn(H,L,z-H,U-L)),X=t.x+W.left,Y=t.y+W.top,q=Bn(X,Y,e,n),K=(r=G.x,i=G.y,u=G.right,a=G.bottom,c=q.x,s=q.y,l=q.right,f=q.bottom,d=q.width,m=q.height,{originInBounds:r<=c&&c<=u&&i<=s&&s<=a,sizeInBounds:l<=u&&r<=l&&f<=a&&i<=f,visibleW:Math.min(d,r<=c?u-c:l-r),visibleH:Math.min(m,i<=s?a-s:f-i)}),J=K.visibleW,$=K.visibleH,Q=K.originInBounds&&K.sizeInBounds,Z=Q?q:(g=G.x,p=G.y,h=G.right,v=G.bottom,b=q.x,y=q.y,x=q.width,w=q.height,S=Math.max(g,h-x),k=Math.max(p,v-w),C=Wa(b,g,S),O=Wa(y,p,k),_=Math.min(C+x,h)-C,T=Math.min(O+w,v)-O,Bn(C,O,_,T)),tt=0<Z.width&&0<Z.height,et=(E=t.direction,B=rt((D=Z).bottom-(A=o).y),M=rt(A.bottom-D.y),F=E.fold(M,M,B,B,M,B,M,M),I=rt(D.right-A.x),R=rt(A.right-D.x),{maxWidth:E.fold(R,I,R,I,R,R,R,I),maxHeight:F}),nt={rect:Z,maxHeight:et.maxHeight,maxWidth:et.maxWidth,direction:t.direction,placement:t.placement,classes:{on:j.classesOn,off:j.classesOff},layout:t.label,testY:Y};function ot(r,i){return P[r].map(function(t){var e="top"===r||"bottom"===r,n=e?V.top:V.left,o=("left"===r||"top"===r?Math.max:Math.min)(t,i)+n;return e?Wa(o,N.y,N.bottom):Wa(o,N.x,N.right)}).getOr(i)}return Q||t.alwaysFit?Ac.fit(nt):Ac.nofit(nt,J,$,tt)}function fc(t){function e(){return n.get().each(t)}var n=Po(vt.none());return{clear:function(){e(),n.set(vt.none())},isSet:function(){return n.get().isSome()},get:function(){return n.get()},set:function(t){e(),n.set(vt.some(t))}}}function dc(){return fc(function(t){return t.destroy()})}function mc(){return fc(function(t){return t.unbind()})}function gc(){var e=fc(st);return lt(lt({},e),{on:function(t){return e.get().each(t)}})}function pc(t,e,n){return Te(t,e,Bc,n,!1)}function hc(t,e,n){return Te(t,e,Bc,n,!0)}function vc(o,n){function r(t){var e,n=null!==(e=t.raw.pseudoElement)&&void 0!==e?e:"";return zt(t.target,o)&&!at(n)&&wt(Fc,t.raw.propertyName)}function t(t){var e;(g(t)||r(t))&&(a.clear(),c.clear(),!g(e=null==t?void 0:t.raw.type)&&e!==_i()||(clearTimeout(i),ce(o,Ic),$r(o,n.classes)))}function e(){a.set(pc(o,_i(),t)),c.set(pc(o,Oi(),t))}var i,u,a=mc(),c=mc();"ontransitionstart"in o.dom?u=pc(o,Ti(),function(t){r(t)&&(u.unbind(),e())}):e();var s,l,f=(s=o,l=d("transition-delay"),R(d("transition-duration"),function(t,e,n){var o=m(l[n])+m(e);return Math.max(t,o)},0));function d(t){var e=ge(s,t);return F(y(e)?e.split(/\s*,\s*/):[],at)}function m(t){if(y(t)&&/^[\d.]+/.test(t)){var e=parseFloat(t);return At(t,"ms")?e:1e3*e}return 0}requestAnimationFrame(function(){i=setTimeout(t,f+17),oe(o,Ic,i)})}function bc(t,e,n,o,r,i){var u,a,c,s,l,f,d,m,g,p=(u=o,a=r,i.exists(function(t){var e=u.mode;return"all"===e||t[e]!==a[e]}));function h(t){return parseFloat(t).toFixed(3)}p||(g=t,L(o.classes,function(t){return Kr(g,t)}))?(fe(t,"position",n.position),c=Ma(e,t),s=Ba(e,lt(lt({},r),{rect:c})),l=j(Fc,function(t){return s[t]}),m=n,Q(l,function(t,e){var n,o,r,i=m[e].map(h),u=t.map(h);return!Et(n=i,o=u,r=void 0===r?v:r).getOr(n.isNone()&&o.isNone())}).isSome()&&(me(t,l),p&&(Jr(f=t,(d=o).classes),ue(f,Ic).each(function(t){clearTimeout(parseInt(t,10)),ce(f,Ic)}),vc(f,d)),be(t))):$r(t,o.classes)}function yc(t,e,n,o){ve(e,"max-height"),ve(e,"max-width");var r,s,i,l,f,d,m,g,p,u={width:Oe(r=e),height:we(r)};return s=e,i=o.preference,l=t,f=u,d=n,m=o.bounds,g=f.width,p=f.height,R(i,function(t,e){var n=C(a,e);return t.fold(rt(t),n)},Ac.nofit({rect:l,maxHeight:f.height,maxWidth:f.width,direction:Na(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:l.y},-1,-1,!1)).fold(h,h);function a(t,r,i,u,a){var c=lc(t(l,f,d,s,m),g,p,m);return c.fold(rt(c),function(t,e,n,o){return(a===o?u<n||i<e:!a&&o)?c:Ac.nofit(r,i,u,a)})}}function xc(t,e){var n=t,o=Math.floor(e);fe(n,"max-height",wn.max(n,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"])+"px")}function wc(t,e,n){return void 0===t[e]?n:t[e]}function Sc(t,e,n,o){function r(t){return tt(n,t).getOr([])}function i(t,e,n){var o=U(Vc,n);return{offset:kn(t,e),classesOn:H(n,r),classesOff:H(o,r)}}var u=t*(o=void 0===o?1:o),a=e*o;return{southeast:function(){return i(-t,e,["top","alignLeft"])},southwest:function(){return i(t,e,["top","alignRight"])},south:function(){return i(-t/2,e,["top","alignCentre"])},northeast:function(){return i(-t,-e,["bottom","alignLeft"])},northwest:function(){return i(t,-e,["bottom","alignRight"])},north:function(){return i(-t/2,-e,["bottom","alignCentre"])},east:function(){return i(t,-e/2,["valignCentre","left"])},west:function(){return i(-t,-e/2,["valignCentre","right"])},insetNortheast:function(){return i(u,a,["top","alignLeft","inset"])},insetNorthwest:function(){return i(-u,a,["top","alignRight","inset"])},insetNorth:function(){return i(-u/2,a,["top","alignCentre","inset"])},insetSoutheast:function(){return i(u,-a,["bottom","alignLeft","inset"])},insetSouthwest:function(){return i(-u,-a,["bottom","alignRight","inset"])},insetSouth:function(){return i(-u/2,-a,["bottom","alignCentre","inset"])},insetEast:function(){return i(-u,-a/2,["valignCentre","right","inset"])},insetWest:function(){return i(u,-a/2,["valignCentre","left","inset"])}}}function kc(){return Sc(0,0,{})}function Cc(e,n){return function(t){return"rtl"===Lc(t)?n:e}}ac.none;var Oc,_c,Tc=ac.relative,Ec=ac.fixed,Dc="data-alloy-placement",Ac=Vo([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Bc=D,Mc=_e,Fc=["top","bottom","right","left"],Ic="data-alloy-transition-timer",Rc=rt(function(t,e){xc(t,e),de(t,{"overflow-x":"hidden","overflow-y":"auto"})}),Nc=rt(function(t,e){xc(t,e)}),Pc=function(t,e,n,o){var r,i,u,a,c=yc(t,e,n,o),s=e,l=c,f=Ba((r=o).origin,l);return r.transition.each(function(t){bc(s,r.origin,f,t,l,r.lastPlacement)}),Da(s,f),a=c.placement,oe(e,Dc,a),$r(i=e,(u=c.classes).off),Jr(i,u.on),(0,o.maxHeightFunction)(e,c.maxHeight),(0,o.maxWidthFunction)(e,c.maxWidth),{layout:c.layout,placement:c.placement}},Vc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Hc=h,Lc=function(t){return"rtl"===ge(t,"direction")?"rtl":"ltr"};function zc(t){return Mu(t,function(t){return Ge(t)&&ie(t,"data-alloy-vertical-dir")===Oc.BottomToTop})}function Uc(){return To("layouts",[mo("onLtr"),mo("onRtl"),wo("onBottomLtr"),wo("onBottomRtl")])}function jc(e,t,n,o,r,i,u){var a=u.map(zc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(e)}),s=t.layouts.map(function(t){return t.onRtl(e)});return Cc(a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(e)})}).or(c).getOr(r):c.getOr(n),a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(e)})}).or(s).getOr(i):s.getOr(o))(e)}function Wc(t){return t.fold(h,function(t,e,n){return t.translate(-e,-n)})}function Gc(t){return t.fold(h,h)}function Xc(t){return R(t,function(t,e){return t.translate(e.left,e.top)},kn(0,0))}function Yc(t){return Xc(B(t,Gc))}function qc(t,e,n){var o,r,i=Ie(jt(t.element)),u=(o=t,r=Xt(n.root).dom,vt.from(r.frameElement).map(Mt.fromDom).filter(function(t){return zt(jt(t),jt(o.element))}).map(ke).getOr(i));return as(u,i.left,i.top)}function Kc(t,e,n,o){var r=us(kn(t,e));return vt.some({point:r,width:n,height:o})}function Jc(t,a,c,s,l){return t.map(function(t){var e=[a,t.point],n=s.fold(function(){return Yc(e)},function(){return Yc(e)},function(){return Xc(B(e,Wc))}),o={x:n.left,y:n.top,width:t.width,height:t.height},r=(c.showAbove?ga:ha)(),i=(c.showAbove?pa:va)(),u=jc(l,c,r,i,r,i,vt.none());return Hc({anchorBox:o,bubble:c.bubble.getOr(kc()),overrides:c.overrides,layouts:u,placer:vt.none()})})}function $c(t,e,n){var o,r=t.document.createRange(),i=r;return e.fold(function(t){i.setStartBefore(t.dom)},function(t,e){i.setStart(t.dom,e)},function(t){i.setStartAfter(t.dom)}),o=r,n.fold(function(t){o.setEndBefore(t.dom)},function(t,e){o.setEnd(t.dom,e)},function(t){o.setEndAfter(t.dom)}),r}function Qc(t,e,n,o,r){var i=t.document.createRange();return i.setStart(e.dom,n),i.setEnd(o.dom,r),i}function Zc(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}}function ts(t,e,n){return e(Mt.fromDom(n.startContainer),n.startOffset,Mt.fromDom(n.endContainer),n.endOffset)}function es(i,t){return r=i,o=t.match({domRange:function(t){return{ltr:rt(t),rtl:vt.none}},relative:function(t,e){return{ltr:Rt(function(){return $c(r,t,e)}),rtl:Rt(function(){return vt.some($c(r,e,t))})}},exact:function(t,e,n,o){return{ltr:Rt(function(){return Qc(r,t,e,n,o)}),rtl:Rt(function(){return vt.some(Qc(r,n,o,t,e))})}}}),((n=(e=o).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return gs.rtl(Mt.fromDom(t.endContainer),t.endOffset,Mt.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return ts(0,gs.ltr,n)}):ts(0,gs.ltr,n)).match({ltr:function(t,e,n,o){var r=i.document.createRange();return r.setStart(t.dom,e),r.setEnd(n.dom,o),r},rtl:function(t,e,n,o){var r=i.document.createRange();return r.setStart(n.dom,o),r.setEnd(t.dom,e),r}});var r,e,n,o}(Oc=Oc||{}).TopToBottom="toptobottom",Oc.BottomToTop="bottomtotop";var ns="data-alloy-vertical-dir",os=[mo("hotspot"),wo("bubble"),Eo("overrides",{}),Uc(),ta("placement",function(t,e,n){var o=Ma(n,e.hotspot.element),r=jc(t.element,e,ha(),va(),ga(),pa(),vt.some(e.hotspot.element));return vt.some(Hc({anchorBox:o,bubble:e.bubble.getOr(kc()),overrides:e.overrides,layouts:r,placer:vt.none()}))})],rs=[mo("x"),mo("y"),Eo("height",0),Eo("width",0),Eo("bubble",kc()),Eo("overrides",{}),Uc(),ta("placement",function(t,e,n){var o=cc(n,e.x,e.y),r=Bn(o.left,o.top,e.width,e.height),i=jc(t.element,e,da(),ma(),da(),ma(),vt.none());return vt.some(Hc({anchorBox:r,bubble:e.bubble,overrides:e.overrides,layouts:i,placer:vt.none()}))})],is=Vo([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),us=is.screen,as=is.absolute,cs=[mo("node"),mo("root"),wo("bubble"),Uc(),Eo("overrides",{}),Eo("showAbove",!1),ta("placement",function(r,i,u){var a=qc(r,0,i);return i.node.filter(vn).bind(function(t){var e=t.dom.getBoundingClientRect(),n=Kc(e.left,e.top,e.width,e.height),o=i.node.getOr(r.element);return Jc(n,a,i,u,o)})})],ss=function(t,e,n,o){return{start:t,soffset:e,finish:n,foffset:o}},ls=Vo([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),fs=(ls.before,ls.on,ls.after,function(t){return t.fold(h,h,h)}),ds=Vo([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),ms={domRange:ds.domRange,relative:ds.relative,exact:ds.exact,exactFromRange:function(t){return ds.exact(t.start,t.soffset,t.finish,t.foffset)},getWin:function(t){return Xt(t.match({domRange:function(t){return Mt.fromDom(t.startContainer)},relative:function(t,e){return fs(t)},exact:function(t,e,n,o){return t}}))},range:ss},gs=Vo([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function ps(t){return ef.getOption(t)}function hs(t){return ps(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||wt(nf,Ft(t))}function vs(t,e){return Lt(n=void 0===t?document:t.dom)?[]:B(n.querySelectorAll(e),Mt.fromDom);var n}function bs(t){if(0<t.rangeCount){var e=t.getRangeAt(0),n=t.getRangeAt(t.rangeCount-1);return vt.some(ss(Mt.fromDom(e.startContainer),e.startOffset,Mt.fromDom(n.endContainer),n.endOffset))}return vt.none()}function ys(t){if(null===t.anchorNode||null===t.focusNode)return bs(t);var e,n,o,r,i,u,a,c,s,l,f,d=Mt.fromDom(t.anchorNode),m=Mt.fromDom(t.focusNode);return e=d,n=t.anchorOffset,o=m,r=t.focusOffset,u=n,a=o,c=r,(s=jt(i=e).dom.createRange()).setStart(i.dom,u),s.setEnd(a.dom,c),l=s,f=zt(e,o)&&n===r,l.collapsed&&!f?vt.some(ss(d,t.anchorOffset,m,t.focusOffset)):bs(t)}function xs(t,e){var n,o,r=(n=es(t,e)).getClientRects();return 0<(o=0<r.length?r[0]:n.getBoundingClientRect()).width||0<o.height?vt.some(o).map(Zc):vt.none()}function ws(t,e){return{element:t,offset:e}}function Ss(t,e){return(Xe(t)?ws:function(t,e){var n=Kt(t);if(0===n.length)return ws(t,e);if(e<n.length)return ws(n[e],0);var o=n[n.length-1];return ws(o,(Xe(o)?ef.get(o):Kt(o)).length)})(t,e)}function ks(t,e){return e.getSelection.getOrThunk(function(){return function(){return vt.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(ys)}})().map(function(t){var e=Ss(t.start,t.soffset),n=Ss(t.finish,t.foffset);return ms.range(e.element,e.offset,n.element,n.offset)})}function Cs(t){return t.x+t.width}function Os(t,e){return t.x-e.width}function _s(t,e){return t.y-e.height+t.height}function Ts(t,e,n){return na(Cs(t),t.y,n.southeast(),Na(),"southeast",ra(t,{left:0,top:2}),rf)}function Es(t,e,n){return na(Os(t,e),t.y,n.southwest(),Pa(),"southwest",ra(t,{right:1,top:2}),rf)}function Ds(t,e,n){return na(Cs(t),_s(t,e),n.northeast(),Va(),"northeast",ra(t,{left:0,bottom:3}),rf)}function As(t,e,n){return na(Os(t,e),_s(t,e),n.northwest(),Ha(),"northwest",ra(t,{right:1,bottom:3}),rf)}function Bs(){return[Ts,Es,Ds,As]}function Ms(){return[Es,Ts,As,Ds]}function Fs(t,e,n,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v,b,y,x,w={anchorBox:n.anchorBox,origin:e};return a=w,c=r.element,s=n.bubble,l=n.layouts,f=i,d=o,m=n.overrides,g=u,h=wc(m,"maxHeightFunction",Rc()),v=wc(m,"maxWidthFunction",st),b=a.anchorBox,y=a.origin,x={bounds:(p=y,d.fold(function(){return p.fold(Fn,Fn,Bn)},function(n){return p.fold(n,n,function(){var t=n(),e=cc(p,t.x,t.y);return Bn(e.left,e.top,t.width,t.height)})})),origin:y,preference:l,maxHeightFunction:h,maxWidthFunction:v,lastPlacement:f,transition:g},Pc(b,c,s,x)}function Is(t,e){Tn(t.element,e.element)}function Rs(e,t){var n,o=e.components();St((n=e).components(),function(t){return En(t.element)}),Me(n.element),n.syncComponents();var r=U(o,t);St(r,function(t){df(t),e.getSystem().removeFromWorld(t)}),St(t,function(t){t.getSystem().isConnected()?Is(e,t):(e.getSystem().addToWorld(t),Is(e,t),vn(e.element)&&mf(t)),e.syncComponents()})}function Ns(t,e){gf(t,e,Tn)}function Ps(t){df(t),En(t.element),t.getSystem().removeFromWorld(t)}function Vs(e){var t=Yt(e.element).bind(function(t){return e.getSystem().getByDom(t).toOptional()});Ps(e),t.each(function(t){t.syncComponents()})}function Hs(t){var e=t.components();St(e,Ps),Me(t.element),t.syncComponents()}function Ls(t,e){pf(t,e,Tn)}function zs(e){var t=Kt(e.element);St(t,function(t){e.getByDom(t).each(df)}),En(e.element)}function Us(e,t,n,o){n.get().each(function(t){Hs(e)}),Ns(t.getAttachPoint(e),e);var r=e.getSystem().build(o);return Ns(e,r),n.set(r),r}function js(t,e,n,o){var r=Us(t,e,n,o);return e.onOpen(t,r),r}function Ws(e,n,o){o.get().each(function(t){Hs(e),Vs(e),n.onClose(e,t),o.clear()})}function Gs(t,e,n){return n.isOpen()}function Xs(t){var n=so("Dismissal",Sf,t),e={};return e[yf()]={schema:to([mo("target")]),onReceive:function(e,t){bf.isOpen(e)&&(bf.isPartOf(e,t.target)||n.isExtraPart(e,t.target)||n.fireEventInstead.fold(function(){return bf.close(e)},function(t){return vr(e,t.event)}))}},e}function Ys(t){var n=so("Reposition",kf,t),e={};return e[xf()]={onReceive:function(e){bf.isOpen(e)&&n.fireEventInstead.fold(function(){return n.doReposition(e)},function(t){return vr(e,t.event)})}},e}function qs(t,e,n){e.store.manager.onLoad(t,e,n)}function Ks(t,e,n){e.store.manager.onUnload(t,e,n)}function Js(){var t=Po(null);return wu({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})}function $s(){var i=Po({}),u=Po({});return wu({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return tt(i.get(),t).orThunk(function(){return tt(u.get(),t)})},update:function(t){var e=i.get(),n=u.get(),o={},r={};St(t,function(e){tt(o[e.value]=e,"meta").each(function(t){tt(t,"text").each(function(t){r[t]=e})})}),i.set(lt(lt({},e),o)),u.set(lt(lt({},n),r))},clear:function(){i.set({}),u.set({})}})}function Qs(t,e,n,o){var r=e.store;n.update([o]),r.setValue(t,o),e.onSetValue(t,o)}function Zs(o,t){return No(o,{},B(t,function(t){return e=t.name(),n="Cannot configure "+t.name()+" for "+o,ar(e,e,Xn(),Jn(function(t){return zo("The field: "+e+" is forbidden. "+n)}));var e,n}).concat([cr("dump",h)]))}function tl(t){return t.dump}function el(t,e){return lt(lt({},ec(e)),t.dump)}function nl(t){return Tt(t,"uiType")}function ol(t){return t.fold(vt.some,vt.none,vt.some,vt.some)}function rl(t){function e(t){return t.name}return t.fold(e,e,e,e)}function il(n,o){return function(t){var e=so("Converting part type",o,t);return n(e)}}function ul(t,e,n,o){return Yo(e.defaults(t,n,o),n,{uid:t.partUids[e.name]},e.overrides(t,n,o))}function al(r,t){var e={};return St(t,function(t){ol(t).each(function(n){var o=nd(r,n.pname);e[n.name]=function(t){var e=so("Part: "+n.name+" in "+r,$o(n.schema),t);return lt(lt({},o),{config:t,validated:e})}})}),e}function cl(t,e,n){return{uiType:Pf(),owner:t,name:e,config:n,validated:{}}}function sl(t){return H(t,function(t){return t.fold(vt.none,vt.some,vt.none,vt.none).map(function(t){return bo(t.name,t.schema.concat([ea(td())]))}).toArray()})}function ll(t){return B(t,rl)}function fl(t,e,n){return o=e,r={},i={},St(n,function(t){t.fold(function(o){r[o.pname]=Rf(!0,function(t,e,n){return o.factory.sketch(ul(t,o,e,n))})},function(t){var e=o.parts[t.name];i[t.name]=rt(t.factory.sketch(ul(o,t,e[td()]),e))},function(o){r[o.pname]=Rf(!1,function(t,e,n){return o.factory.sketch(ul(t,o,e,n))})},function(o){r[o.pname]=Nf(!0,function(e,t,n){return B(e[o.name],function(t){return o.factory.sketch(Yo(o.defaults(e,t,n),t,o.overrides(e,t)))})})})}),{internals:rt(r),externals:rt(i)};var o,r,i}function dl(t,e,n){return o=vt.some(t),i=(r=e).components,s=dt(n,function(t,e){return o=t,r=!1,{name:rt(n=e),required:function(){return o.fold(function(t,e){return t},function(t,e){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+n);return r=!0,o}};var n,o,r}),u=o,a=r,c=s,l=H(i,function(t){return If(u,a,t,c)}),J(s,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+o.getOr("none")+"\nComponents: "+JSON.stringify(r.components,null,2))}),l;var o,r,i,u,a,c,s,l}function ml(t,e,n){var o=e.partUids[n];return t.getSystem().getByUid(o).toOptional()}function gl(t,e,n){return ml(t,e,n).getOrDie("Could not find part: "+n)}function pl(t,e,n){var o={},r=e.partUids,i=t.getSystem();return St(n,function(t){o[t]=rt(i.getByUid(r[t]))}),o}function hl(t,e){var n=t.getSystem();return dt(e.partUids,function(t,e){return rt(n.getByUid(t))})}function vl(t){return Ct(t.partUids)}function bl(t,e,n){var o={},r=e.partUids,i=t.getSystem();return St(n,function(t){o[t]=rt(i.getByUid(r[t]).getOrDie())}),o}function yl(e,t){return lr(B(ll(t),function(t){return{key:t,value:e+"-"+t}}))}function xl(e){return ar("partUids","partUids",Yn(function(t){return yl(t.uid,e)}),Zo())}function wl(t,e,n,o,r){var i;return so(t+" [SpecSchema]",to((i=r,(0<o.length?[bo("parts",o)]:[]).concat([mo("uid"),Eo("dom",{}),Eo("components",[]),ea("originalSpec"),Eo("debug.sketcher",{})]).concat(i)).concat(e)),n)}function Sl(t,e,n,o,r){var i=rd(r),u=wl(t,e,i,sl(n),[xl(n)]),a=fl(0,u,n);return o(u,dl(t,u,a.internals()),i,a.externals())}function kl(t){var r=so("Sketcher for "+t.name,id,t),e=dt(r.apis,Hr),n=dt(r.extraApis,Pr);return lt(lt({name:r.name,configFields:r.configFields,sketch:function(t){return e=r.name,n=r.configFields,(0,r.factory)(wl(e,n,o=rd(t),[],[]),o);var e,n,o}},e),n)}function Cl(t){var e=so("Sketcher for "+t.name,ud,t),n=al(e.name,e.partFields),o=dt(e.apis,Hr),r=dt(e.extraApis,Pr);return lt(lt({name:e.name,partFields:e.partFields,configFields:e.configFields,sketch:function(t){return Sl(e.name,e.configFields,e.partFields,e.factory,t)},parts:n},o),r)}function Ol(t){return"input"===Ft(t)&&"radio"!==ie(t,"type")||"textarea"===Ft(t)}function _l(t,e,n){(e.disabled()?ld:fd)(t,e)}function Tl(t,e){return!0===e.useNative&&wt(sd,Ft(t.element))}function El(t,e){return Tl(t,e)?ae(t.element,"disabled"):"true"===ie(t.element,"aria-disabled")}function Dl(n,o,t,r){var e=vs(n.element,"."+o.highlightClass);St(e,function(e){d(r,function(t){return t.element===e})||(qr(e,o.highlightClass),n.getSystem().getByDom(e).each(function(t){o.onDehighlight(n,t),vr(t,tu())}))})}function Al(t,e,n,o){Dl(t,e,0,[o]),Yl(0,e,0,o)||(Yr(o.element,e.highlightClass),e.onHighlight(t,o),vr(o,Zi()))}function Bl(n,e,t,o){var r=vs(n.element,"."+e.itemClass);return P(r,function(t){return Kr(t,e.highlightClass)}).bind(function(t){var e=oa(t,o,0,r.length-1);return n.getSystem().getByDom(r[e]).toOptional()})}function Ml(t,e,n){var o=z(t.slice(0,e)),r=z(t.slice(e+1));return N(o.concat(r),n)}function Fl(t,e,n){return N(z(t.slice(0,e)),n)}function Il(t,e,n){var o=t.slice(0,e);return N(t.slice(e+1).concat(o),n)}function Rl(t,e,n){return N(t.slice(e+1),n)}function Nl(n){return function(t){var e=t.raw;return wt(n,e.which)}}function Pl(t){return function(e){return L(t,function(t){return t(e)})}}function Vl(t){return!0===t.raw.shiftKey}function Hl(t){return!0===t.raw.ctrlKey}function Ll(t,e){return{matches:t,classification:e}}function zl(t,e,n){e.exists(function(e){return n.exists(function(t){return zt(t,e)})})||br(t,Ki(),{prevFocus:e,newFocus:n})}function Ul(){function o(t){return _a(t.element)}return{get:o,set:function(t,e){var n=o(t);t.getSystem().triggerFocus(e,t.element),zl(t,n,o(t))}}}function jl(){function r(t){return hd.getHighlighted(t).map(function(t){return t.element})}return{get:r,set:function(e,t){var n=r(e);e.getSystem().getByDom(t).fold(st,function(t){hd.highlight(e,t)});var o=r(e);zl(e,n,o)}}}gs.ltr,gs.rtl;function Wl(t,e,n,o,r,i){var u=i.map(Mn);return lf(t,e,n,o,r,u)}function Gl(t,e,n){var o,r,i,u=e.getAttachPoint(t);fe(t.element,"position",ff.getMode(u)),i=e.cloakVisibilityAttr,pe((o=t).element,r="visibility").fold(function(){ce(o.element,i)},function(t){oe(o.element,i,t)}),fe(o.element,r,"hidden")}function Xl(t,e,n){var o,r,i,u=t.element;d(["top","left","right","bottom"],function(t){return pe(u,t).isSome()})||ve(t.element,"position"),r="visibility",i=e.cloakVisibilityAttr,ue((o=t).element,i).fold(function(){return ve(o.element,r)},function(t){return fe(o.element,r,t)})}function Yl(t,e,n,o){return Kr(o.element,e.highlightClass)}function ql(e,t,n){return Iu(e.element,"."+t.itemClass).bind(function(t){return e.getSystem().getByDom(t).toOptional()})}function Kl(e,t,n){var o=vs(e.element,"."+t.itemClass);return(0<o.length?vt.some(o[o.length-1]):vt.none()).bind(function(t){return e.getSystem().getByDom(t).toOptional()})}function Jl(e,t,n){return nt(B(vs(e.element,"."+t.itemClass),function(t){return e.getSystem().getByDom(t).toOptional()}))}var $l,Ql,Zl,tf,ef=($l=Xe,{get:function(t){if(!$l(t))throw new Error("Can only get text value of a text node");return Ql(t).getOr("")},getOption:Ql=function(t){return $l(t)?vt.from(t.dom.nodeValue):vt.none()},set:function(t,e){if(!$l(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=e}}),nf=["img","br"],of=[wo("getSelection"),mo("root"),wo("bubble"),Uc(),Eo("overrides",{}),Eo("showAbove",!1),ta("placement",function(t,e,n){var r=Xt(e.root).dom,o=qc(t,0,e);return Jc(ks(r,e).bind(function(n){return t=r,e=ms.exactFromRange(n),(0<(o=es(t,e).getBoundingClientRect()).width||0<o.height?vt.some(o).map(Zc):vt.none()).orThunk(function(){var t=Mt.fromText("\ufeff");Ee(n.start,t);var e=xs(r,ms.exact(t,0,t,1));return En(t),e}).bind(function(t){return Kc(t.left,t.top,t.width,t.height)});var t,e,o}),o,e,n,ks(r,e).bind(function(t){return Ge(t.start)?vt.some(t.start):fn(t.start)}).getOr(t.element))})],rf="link-layout",uf=lo("type",{selection:of,node:cs,hotspot:os,submenu:[mo("item"),Uc(),Eo("overrides",{}),ta("placement",function(t,e,n){var o=Ma(n,e.item.element),r=jc(t.element,e,Bs(),Ms(),Bs(),Ms(),vt.none());return vt.some(Hc({anchorBox:o,bubble:kc(),overrides:e.overrides,layouts:r,placer:vt.none()}))})],makeshift:rs}),af=[xo("classes",er),Mo("mode","all",["all","layout","placement"])],cf=[Eo("useFixed",T),wo("getBounds")],sf=[go("anchor",uf),To("transition",af)],lf=function(c,s,l,f,t,d){var m=so("placement.info",$o(sf),t),g=m.anchor,p=f.element,h=l.get(f.uid);Ta(function(){fe(p,"position","fixed");var t=pe(p,"visibility");fe(p,"visibility","hidden");var e,n,o,r,i=s.useFixed()?(r=document.documentElement,Ec(0,0,r.clientWidth,r.clientHeight)):(n=ke((e=c).element),o=e.element.dom.getBoundingClientRect(),Tc(n.left,n.top,o.width,o.height)),u=g.placement,a=d.map(rt).or(s.getBounds);u(c,g,i).each(function(t){var e=t.placer.getOr(Fs)(c,i,t,a,f,h,m.transition);l.set(f.uid,e)}),t.fold(function(){ve(p,"visibility")},function(t){fe(p,"visibility",t)}),pe(p,"left").isNone()&&pe(p,"top").isNone()&&pe(p,"right").isNone()&&pe(p,"bottom").isNone()&&mt(pe(p,"position"),"fixed")&&ve(p,"position")},p)},ff=xa({fields:cf,name:"positioning",active:uc,apis:Object.freeze({__proto__:null,position:function(t,e,n,o,r){Wl(t,e,n,o,r,vt.none())},positionWithin:Wl,positionWithinBounds:lf,getMode:function(t,e,n){return e.useFixed()?"fixed":"absolute"},reset:function(t,e,n,o){var r=o.element;St(["position","left","right","top","bottom"],function(t){return ve(r,t)}),ce(r,Dc),n.clear(o.uid)}}),state:Object.freeze({__proto__:null,init:function(){var n={};return wu({readState:function(){return n},clear:function(t){k(t)?delete n[t]:n={}},set:function(t,e){n[t]=e},get:function(t){return tt(n,t)}})}})}),df=function(t){vr(t,Xi());var e=t.components();St(e,df)},mf=function(t){var e=t.components();St(e,mf),vr(t,Gi())},gf=function(t,e,n){t.getSystem().addToWorld(e),n(t.element,e.element),vn(t.element)&&mf(e),t.syncComponents()},pf=function(t,e,n){n(t,e.element);var o=Kt(e.element);St(o,function(t){e.getByDom(t).each(mf)})},hf=Object.freeze({__proto__:null,cloak:Gl,decloak:Xl,open:js,openWhileCloaked:function(t,e,n,o,r){Gl(t,e),js(t,e,n,o),r(),Xl(t,e)},close:Ws,isOpen:Gs,isPartOf:function(e,n,t,o){return Gs(0,0,t)&&t.get().exists(function(t){return n.isPartOf(e,t,o)})},getState:function(t,e,n){return n.get()},setContent:function(t,e,n,o){return n.get().map(function(){return Us(t,e,n,o)})}}),vf=Object.freeze({__proto__:null,events:function(n,o){return nu([Cr(Vi(),function(t,e){Ws(t,n,o)})])}}),bf=xa({fields:[Ju("onOpen"),Ju("onClose"),mo("isPartOf"),mo("getAttachPoint"),Eo("cloakVisibilityAttr","data-precloak-visibility")],name:"sandboxing",active:vf,apis:hf,state:Object.freeze({__proto__:null,init:function(){var t=gc(),e=rt("not-implemented");return wu({readState:e,isOpen:t.isSet,clear:t.clear,set:t.set,get:t.get})}})}),yf=rt("dismiss.popups"),xf=rt("reposition.popups"),wf=rt("mouse.released"),Sf=to([Eo("isExtraPart",T),To("fireEventInstead",[Eo("event",Yi())])]),kf=to([To("fireEventInstead",[Eo("event",qi())]),vo("doReposition")]),Cf=Object.freeze({__proto__:null,onLoad:qs,onUnload:Ks,setValue:function(t,e,n,o){e.store.manager.setValue(t,e,n,o)},getValue:function(t,e,n){return e.store.manager.getValue(t,e,n)},getState:function(t,e,n){return n}}),Of=Object.freeze({__proto__:null,events:function(n,o){var t=n.resetOnDom?[ou(function(t,e){qs(t,n,o)}),ru(function(t,e){Ks(t,n,o)})]:[ba(n,o,qs)];return nu(t)}}),_f=Object.freeze({__proto__:null,memory:Js,dataset:$s,manual:function(){return wu({readState:st})},init:function(t){return t.store.manager.state(t)}}),Tf=[wo("initialValue"),mo("getFallbackEntry"),mo("getDataKey"),mo("setValue"),ta("manager",{setValue:Qs,getValue:function(t,e,n){var o=e.store,r=o.getDataKey(t);return n.lookup(r).getOrThunk(function(){return o.getFallbackEntry(r)})},onLoad:function(e,n,o){n.store.initialValue.each(function(t){Qs(e,n,o,t)})},onUnload:function(t,e,n){n.clear()},state:$s})],Ef=[mo("getValue"),Eo("setValue",st),wo("initialValue"),ta("manager",{setValue:function(t,e,n,o){e.store.setValue(t,o),e.onSetValue(t,o)},getValue:function(t,e,n){return e.store.getValue(t)},onLoad:function(e,n,t){n.store.initialValue.each(function(t){n.store.setValue(e,t)})},onUnload:st,state:xu.init})],Df=xa({fields:[Do("store",{mode:"memory"},lo("mode",{memory:[wo("initialValue"),ta("manager",{setValue:function(t,e,n,o){n.set(o),e.onSetValue(t,o)},getValue:function(t,e,n){return n.get()},onLoad:function(t,e,n){e.store.initialValue.each(function(t){n.isNotSet()&&n.set(t)})},onUnload:function(t,e,n){n.clear()},state:Js})],manual:Ef,dataset:Tf})),Ju("onSetValue"),Eo("resetOnDom",!1)],name:"representing",active:Of,apis:Cf,extra:{setValueFrom:function(t,e){var n=Df.getValue(e);Df.setValue(t,n)}},state:_f}),Af=Zs,Bf=el,Mf="placeholder",Ff=Vo([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),If=function(r,i,u,a){return t=r,n=a,(nl(e=u)&&e.uiType===Mf?(c=e,s=n,(o=t).exists(function(t){return t!==c.owner})?Ff.single(!0,rt(c)):tt(s,c.name).fold(function(){throw new Error("Unknown placeholder component: "+c.name+"\nKnown: ["+Ct(s)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(c,null,2))},function(t){return t.replace()})):Ff.single(!1,rt(e))).fold(function(t,e){var n=nl(u)?e(i,u.config,u.validated):e(i),o=H(tt(n,"components").getOr([]),function(t){return If(r,i,t,a)});return[lt(lt({},n),{components:o})]},function(t,e){if(nl(u)){var n=e(i,u.config,u.validated);return u.validated.preprocess.getOr(h)(n)}return e(i)});var t,e,n,o,c,s},Rf=Ff.single,Nf=Ff.multiple,Pf=rt(Mf),Vf=Vo([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Hf=Eo("factory",{sketch:h}),Lf=Eo("schema",[]),zf=mo("name"),Uf=ar("pname","pname",Wn(function(t){return"<alloy."+Ir(t.name)+">"}),Zo()),jf=cr("schema",function(){return[wo("preprocess")]}),Wf=Eo("defaults",rt({})),Gf=Eo("overrides",rt({})),Xf=$o([Hf,Lf,zf,Uf,Wf,Gf]),Yf=$o([Hf,Lf,zf,Wf,Gf]),qf=$o([Hf,Lf,zf,Uf,Wf,Gf]),Kf=$o([Hf,jf,zf,mo("unit"),Uf,Wf,Gf]),Jf=il(Vf.required,Xf),$f=il(Vf.external,Yf),Qf=il(Vf.optional,qf),Zf=il(Vf.group,Kf),td=rt("entirety"),ed=Object.freeze({__proto__:null,required:Jf,external:$f,optional:Qf,group:Zf,asNamedPart:ol,name:rl,asCommon:function(t){return t.fold(h,h,h,h)},original:td}),nd=function(t,e){return{uiType:Pf(),owner:t,name:e}},od=Object.freeze({__proto__:null,generate:al,generateOne:cl,schemas:sl,names:ll,substitutes:fl,components:dl,defaultUids:yl,defaultUidsSchema:xl,getAllParts:hl,getAllPartNames:vl,getPart:ml,getPartOrDie:gl,getParts:pl,getPartsOrDie:bl}),rd=function(t){return Tt(t,"uid")?t:lt(lt({},t),{uid:pu("uid")})},id=to([mo("name"),mo("factory"),mo("configFields"),Eo("apis",{}),Eo("extraApis",{})]),ud=to([mo("name"),mo("factory"),mo("configFields"),mo("partFields"),Eo("apis",{}),Eo("extraApis",{})]),ad=Object.freeze({__proto__:null,getCurrent:function(t,e,n){return e.find(t)}}),cd=xa({fields:[mo("find")],name:"composing",apis:ad}),sd=["input","button","textarea","select"],ld=function(e,t,n){t.disableClass.each(function(t){Yr(e.element,t)}),(Tl(e,t)?function(t){oe(t.element,"disabled","disabled")}:function(t){oe(t.element,"aria-disabled","true")})(e),t.onDisabled(e)},fd=function(e,t,n){t.disableClass.each(function(t){qr(e.element,t)}),(Tl(e,t)?function(t){ce(t.element,"disabled")}:function(t){oe(t.element,"aria-disabled","false")})(e),t.onEnabled(e)},dd=Object.freeze({__proto__:null,enable:fd,disable:ld,isDisabled:El,onLoad:_l,set:function(t,e,n,o){(o?ld:fd)(t,e)}}),md=Object.freeze({__proto__:null,exhibit:function(t,e){return zr({classes:e.disabled()?e.disableClass.toArray():[]})},events:function(n,t){return nu([Sr(Ii(),function(t,e){return El(t,n)}),ba(n,t,_l)])}}),gd=xa({fields:[Io("disabled",T),Eo("useNative",!0),wo("disableClass"),Ju("onDisabled"),Ju("onEnabled")],name:"disabling",active:md,apis:dd}),pd=Object.freeze({__proto__:null,dehighlightAll:function(t,e,n){return Dl(t,e,0,[])},dehighlight:function(t,e,n,o){Yl(0,e,0,o)&&(qr(o.element,e.highlightClass),e.onDehighlight(t,o),vr(o,tu()))},highlight:Al,highlightFirst:function(e,n,t){ql(e,n).each(function(t){Al(e,n,0,t)})},highlightLast:function(e,n,t){Kl(e,n).each(function(t){Al(e,n,0,t)})},highlightAt:function(e,n,t,o){var r,i,u;i=o,u=vs((r=e).element,"."+n.itemClass),vt.from(u[i]).fold(function(){return Nn.error(new Error("No element found with index "+i))},r.getSystem().getByDom).fold(function(t){throw t},function(t){Al(e,n,0,t)})},highlightBy:function(e,n,t,o){N(Jl(e,n),o).each(function(t){Al(e,n,0,t)})},isHighlighted:Yl,getHighlighted:function(e,t,n){return Iu(e.element,"."+t.highlightClass).bind(function(t){return e.getSystem().getByDom(t).toOptional()})},getFirst:ql,getLast:Kl,getPrevious:function(t,e,n){return Bl(t,e,0,-1)},getNext:function(t,e,n){return Bl(t,e,0,1)},getCandidates:Jl}),hd=xa({fields:[mo("highlightClass"),mo("itemClass"),Ju("onHighlight"),Ju("onDehighlight")],name:"highlighting",apis:pd}),vd=[8],bd=[9],yd=[13],xd=[27],wd=[32],Sd=[37],kd=[38],Cd=[39],Od=[40],_d=O(Vl);function Td(t,e,n,o,a){function c(e,n,t,o,r){var i=t(e,n,o,r),u=n.event;return N(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(e,n,o,r)})}var r={schema:function(){return t.concat([Eo("focusManager",Ul()),Do("focusInside","onFocus",io(function(t){return wt(["onFocus","onEnterOrSpace","onApi"],t)?Nn.value(t):Nn.error("Invalid value for focusInside")})),ta("handler",r),ta("state",e),ta("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==Zl.OnFocusMode?vt.none():a(i).map(function(n){return Cr(Ai(),function(t,e){n(t,i,u),e.stop()})}),e=[Cr(xi(),function(o,r){c(o,r,n,i,u).fold(function(){var e=o,n=r,t=Nl(wd.concat(yd))(n.event);i.focusInside===Zl.OnEnterOrSpaceMode&&t&&gr(e,n)&&a(i).each(function(t){t(e,i,u),n.stop()})},function(t){r.stop()})}),Cr(wi(),function(t,e){c(t,e,o,i,u).each(function(t){e.stop()})})];return nu(t.toArray().concat(e))}};return r}function Ed(t){function a(t,e){return 0<xe(t.visibilitySelector.bind(function(t){return Ru(e,t)}).getOr(e))}function e(e,n,t){var o=n,r=F(vs(e.element,o.selector),function(t){return a(o,t)});vt.from(r[o.firstTabstop]).each(function(t){n.focusManager.set(e,t)})}function o(n,t,r,i){var e,u=vs(n.element,r.selector);return(e=r).focusManager.get(n).bind(function(t){return Ru(t,e.selector)}).bind(function(t){return P(u,C(zt,t)).bind(function(t){return e=n,o=r,i(u,t,function(t){return a(e=o,n=t)&&e.useTabstopAt(n);var e,n}).fold(function(){return o.cyclic?vt.some(!0):vt.none()},function(t){return o.focusManager.set(e,t),vt.some(!0)});var e,o})})}var n=[wo("onEscape"),wo("onEnter"),Eo("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Eo("firstTabstop",0),Eo("useTabstopAt",D),wo("visibilitySelector")].concat([t]),r=rt([Ll(Pl([Vl,Nl(bd)]),function(t,e,n){return o(t,0,n,n.cyclic?Ml:Fl)}),Ll(Nl(bd),function(t,e,n){return o(t,0,n,n.cyclic?Il:Rl)}),Ll(Nl(xd),function(e,n,t){return t.onEscape.bind(function(t){return t(e,n)})}),Ll(Pl([_d,Nl(yd)]),function(e,n,t){return t.onEnter.bind(function(t){return t(e,n)})})]),i=rt([]);return Td(n,xu.init,r,i,function(){return vt.some(e)})}function Dd(t,e,n){return Ol(n)&&Nl(wd)(e.event)?vt.none():(xr(t,n,Ii()),vt.some(!0))}function Ad(t,e){return vt.some(!0)}function Bd(t,e,n){return n.execute(t,e,t.element)}function Md(){var n=gc();return wu({readState:function(){return n.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,e){n.set({numRows:t,numColumns:e})},getNumRows:function(){return n.get().map(function(t){return t.numRows})},getNumColumns:function(){return n.get().map(function(t){return t.numColumns})}})}function Fd(i){return function(t,e,n,o){var r=i(t.element);return tg(r,t,e,n,o)}}function Id(t,e){return Fd(Cc(t,e))}function Rd(t,e){return Fd(Cc(e,t))}function Nd(r){return function(t,e,n,o){return tg(r,t,e,n,o)}}function Pd(t){return!((e=t.dom).offsetWidth<=0&&e.offsetHeight<=0);var e}function Vd(t,e,n){var o,r=F(vs(t,n),Pd);return P(o=r,function(t){return zt(t,e)}).map(function(t){return{index:t,candidates:o}})}function Hd(t,e){return P(t,function(t){return zt(e,t)})}function Ld(n,t,o,e){return e(Math.floor(t/o),t%o).bind(function(t){var e=t.row*o+t.column;return 0<=e&&e<n.length?vt.some(n[e]):vt.none()})}function zd(r,t,i,u,a){return Ld(r,t,u,function(t,e){var n=t===i-1?r.length-t*u:u,o=oa(e,a,0,n-1);return vt.some({row:t,column:o})})}function Ud(i,t,u,a,c){return Ld(i,t,a,function(t,e){var n=oa(t,c,0,u-1),o=n===u-1?i.length-n*a:a,r=Wa(e,0,o-1);return vt.some({row:n,column:r})})}function jd(e,n,t){Iu(e.element,n.selector).each(function(t){n.focusManager.set(e,t)})}function Wd(r){return function(t,e,n,o){return Vd(t,e,n.selector).bind(function(t){return r(t.candidates,t.index,o.getNumRows().getOr(n.initSize.numRows),o.getNumColumns().getOr(n.initSize.numColumns))})}}function Gd(t,e,n){return n.captureTab?vt.some(!0):vt.none()}function Xd(t,e,n,i){var u=function(t,e,n){var o,r=oa(e,i,0,n.length-1);return r===t?vt.none():"button"===Ft(o=n[r])&&"disabled"===ie(o,"disabled")?u(t,r,n):vt.from(n[r])};return Vd(t,n,e).bind(function(t){var e=t.index,n=t.candidates;return u(e,e,n)})}function Yd(e,n,o){return(r=o).focusManager.get(e).bind(function(t){return Ru(t,r.selector)}).bind(function(t){return o.execute(e,n,t)});var r}function qd(e,n,t){n.getInitial(e).orThunk(function(){return Iu(e.element,n.selector)}).each(function(t){n.focusManager.set(e,t)})}function Kd(t,e,n){return Xd(t,n.selector,e,-1)}function Jd(t,e,n){return Xd(t,n.selector,e,1)}function $d(r){return function(t,e,n,o){return r(t,e,n,o).bind(function(){return n.executeOnMove?Yd(t,e,n):vt.some(!0)})}}function Qd(t,e,n){return n.onEscape(t,e)}function Zd(t,e,n){return vt.from(t[e]).bind(function(t){return vt.from(t[n]).map(function(t){return{rowIndex:e,columnIndex:n,cell:t}})})}function tm(t,e,n,o){return Zd(t,e,oa(n,o,0,t[e].length-1))}function em(t,e,n,o){var r=oa(n,o,0,t.length-1),i=t[r].length;return Zd(t,r,Wa(e,0,i-1))}function nm(t,e,n,o){var r=t[e].length;return Zd(t,e,Wa(n+o,0,r-1))}function om(t,e,n,o){var r=Wa(n+o,0,t.length-1),i=t[r].length;return Zd(t,r,Wa(e,0,i-1))}function rm(e,n,t){n.previousSelector(e).orThunk(function(){var t=n.selectors;return Iu(e.element,t.cell)}).each(function(t){n.focusManager.set(e,t)})}function im(t,o){return function(e,n,i){var u=i.cycles?t:o;return Ru(n,i.selectors.row).bind(function(t){return Hd(vs(t,i.selectors.cell),n).bind(function(o){var r=vs(e,i.selectors.row);return Hd(r,t).bind(function(t){var e,n=(e=i,B(r,function(t){return vs(t,e.selectors.cell)}));return u(n,t,o).map(function(t){return t.cell})})})})}}function um(e,n,o){return o.focusManager.get(e).bind(function(t){return o.execute(e,n,t)})}function am(e,n,t){Iu(e.element,n.selector).each(function(t){n.focusManager.set(e,t)})}function cm(t,e,n){return Xd(t,n.selector,e,-1)}function sm(t,e,n){return Xd(t,n.selector,e,1)}function lm(t,e,n,o){var r=t.getSystem().build(o);gf(t,r,n)}function fm(t,e,n,o){N(Ig(t),function(t){return zt(o.element,t.element)}).each(Vs)}function dm(e,t,n,r,o){var i=Ig(e);return vt.from(i[r]).map(function(t){return fm(e,0,0,t),o.each(function(t){lm(e,0,function(t,e){var n,o=e;Jt(n=t,r).fold(function(){Tn(n,o)},function(t){Ee(t,o)})},t)}),t})}function mm(t,e){var n,o;return{key:t,value:{config:{},me:(n=t,o=nu(e),xa({fields:[mo("enabled")],name:n,active:{events:rt(o)}})),configAsRaw:rt({}),initialConfig:{},state:xu}}}function gm(t,e){e.ignore||(ka(t.element),e.onFocus(t))}function pm(t,e,n){var o=e.aria;o.update(t,o,n.get())}function hm(e,t,n){t.toggleClass.each(function(t){(n.get()?Yr:qr)(e.element,t)})}function vm(t,e,n){Ym(t,e,n,!n.get())}function bm(t,e,n){n.set(!0),hm(t,e,n),pm(t,e,n)}function ym(t,e,n){n.set(!1),hm(t,e,n),pm(t,e,n)}function xm(t,e,n){Ym(t,e,n,e.selected)}function wm(){function t(t,e){e.stop(),yr(t)}return[Cr(Ci(),t),Cr(Ni(),t),Dr(si()),Dr(mi())]}function Sm(t){return nu(ft([t.map(function(n){return uu(function(t,e){n(t),e.stop()})}).toArray(),wm()]))}function km(t){(_a(t.element).isNone()||Vg.isFocused(t))&&(Vg.isFocused(t)||Vg.focus(t),br(t,Ug,{item:t}))}function Cm(t){br(t,jg,{item:t})}function Om(t,e){return t.x+t.width/2-e.width/2}function _m(t,e){return t.x+t.width-e.width}function Tm(t,e){return t.y+t.height-e.height}function Em(t,e){return t.y+t.height/2-e.height/2}function Dm(t,e,n){return na(_m(t,e),Tm(t,e),n.insetSouthwest(),Ha(),"southwest",ra(t,{right:0,bottom:3}),ap)}function Am(t,e,n){return na(t.x,Tm(t,e),n.insetSoutheast(),Va(),"southeast",ra(t,{left:1,bottom:3}),ap)}function Bm(t,e,n){return na(_m(t,e),t.y,n.insetNorthwest(),Pa(),"northwest",ra(t,{right:0,top:2}),ap)}function Mm(t,e,n){return na(t.x,t.y,n.insetNortheast(),Na(),"northeast",ra(t,{left:1,top:2}),ap)}function Fm(t,e,n){return na(_m(t,e),Em(t,e),n.insetEast(),ja(),"east",ra(t,{right:0}),ap)}function Im(t,e,n){return na(t.x,Em(t,e),n.insetWest(),Ua(),"west",ra(t,{left:1}),ap)}function Rm(t){switch(t){case"north":return cp;case"northeast":return Mm;case"northwest":return Bm;case"south":return sp;case"southeast":return Am;case"southwest":return Dm;case"east":return Fm;case"west":return Im}}function Nm(t,e,n,o,r){return sc(o).map(Rm).getOr(cp)(t,e,n,o,r)}function Pm(t){switch(t){case"north":return sp;case"northeast":return Am;case"northwest":return Dm;case"south":return cp;case"southeast":return Mm;case"southwest":return Bm;case"east":return Im;case"west":return Fm}}function Vm(t,e,n,o,r){return sc(o).map(Pm).getOr(cp)(t,e,n,o,r)}function Hm(t){var e=void 0!==t.uid&&et(t,"uid")?t.uid:pu("memento");return{get:function(t){return t.getSystem().getByUid(e).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(e).toOptional()},asSpec:function(){return lt(lt({},t),{uid:e})}}}function Lm(t){return function(){return tt(t,gp).getOr("!not found!")}}function zm(t,e){var n,o=t.toLowerCase();if(dp.isRtl()){var r=At(n=o,"-rtl")?n:n+"-rtl";return Tt(e,r)?r:o}return o}function Um(t,e){return tt(e,zm(t,e))}function jm(t,e){var n=e();return Um(t,n).getOrThunk(Lm(n))}function Wm(){return mm("add-focusable",[ou(function(t){var e,n,o;e=t.element,n="svg",o=function(t){return Ht(t,n)},N(e.dom.childNodes,function(t){return o(Mt.fromDom(t))}).map(Mt.fromDom).each(function(t){return oe(t,"focusable","false")})})])}function Gm(t,e,n,o){var r,i,u,a=(u=e,dp.isRtl()&&Tt(mp,u)?["tox-icon--flip"]:[]),c=tt(n,zm(e,n)).or(o).getOrThunk(Lm(n));return{dom:{tag:t.tag,attributes:null!==(r=t.attributes)&&void 0!==r?r:{},classes:t.classes.concat(a),innerHtml:c},behaviours:ec(V(V([],null!==(i=t.behaviours)&&void 0!==i?i:[],!0),[Wm()],!1))}}function Xm(t,e,n,o){return void 0===o&&(o=vt.none()),Gm(e,t,n(),o)}(tf=Zl=Zl||{}).OnFocusMode="onFocus",tf.OnEnterOrSpaceMode="onEnterOrSpace",tf.OnApiMode="onApi";function Ym(t,e,n,o){(o?bm:ym)(t,e,n)}function qm(t,e,n){oe(t.element,"aria-expanded",n)}function Km(t){return"prepared"===t.type?vt.some(t.menu):vt.none()}var Jm=Ed(cr("cyclic",T)),$m=Ed(cr("cyclic",D)),Qm=Td([Eo("execute",Dd),Eo("useSpace",!1),Eo("useEnter",!0),Eo("useControlEnter",!1),Eo("useDown",!1)],xu.init,function(t,e,n,o){var r=n.useSpace&&!Ol(t.element)?wd:[],i=n.useEnter?yd:[],u=n.useDown?Od:[];return[Ll(Nl(r.concat(i).concat(u)),Bd)].concat(n.useControlEnter?[Ll(Pl([Hl,Nl(yd)]),Bd)]:[])},function(t,e,n,o){return n.useSpace&&!Ol(t.element)?[Ll(Nl(wd),Ad)]:[]},function(){return vt.none()}),Zm=Object.freeze({__proto__:null,flatgrid:Md,init:function(t){return t.state(t)}}),tg=function(e,n,t,o,r){return o.focusManager.get(n).bind(function(t){return e(n.element,t,o,r)}).map(function(t){return o.focusManager.set(n,t),!0})},eg=Nd,ng=Nd,og=Nd,rg=Wd(function(t,e,n,o){return zd(t,e,n,o,-1)}),ig=Wd(function(t,e,n,o){return zd(t,e,n,o,1)}),ug=Wd(function(t,e,n,o){return Ud(t,e,n,o,-1)}),ag=Wd(function(t,e,n,o){return Ud(t,e,n,o,1)}),cg=Td([mo("selector"),Eo("execute",Dd),$u("onEscape"),Eo("captureTab",!1),Ia()],Md,rt([Ll(Nl(Sd),Id(rg,ig)),Ll(Nl(Cd),Rd(rg,ig)),Ll(Nl(kd),eg(ug)),Ll(Nl(Od),ng(ag)),Ll(Pl([Vl,Nl(bd)]),Gd),Ll(Pl([_d,Nl(bd)]),Gd),Ll(Nl(xd),function(t,e,n){return n.onEscape(t,e)}),Ll(Nl(wd.concat(yd)),function(e,n,o,t){return(r=o).focusManager.get(e).bind(function(t){return Ru(t,r.selector)}).bind(function(t){return o.execute(e,n,t)});var r})]),rt([Ll(Nl(wd),Ad)]),function(){return vt.some(jd)}),sg=[mo("selector"),Eo("getInitial",vt.none),Eo("execute",Dd),$u("onEscape"),Eo("executeOnMove",!1),Eo("allowVertical",!0)],lg=rt([Ll(Nl(wd),Ad)]),fg=Td(sg,xu.init,function(t,e,n,o){var r=Sd.concat(n.allowVertical?kd:[]),i=Cd.concat(n.allowVertical?Od:[]);return[Ll(Nl(r),$d(Id(Kd,Jd))),Ll(Nl(i),$d(Rd(Kd,Jd))),Ll(Nl(yd),Yd),Ll(Nl(wd),Yd),Ll(Nl(xd),Qd)]},lg,function(){return vt.some(qd)}),dg=[bo("selectors",[mo("row"),mo("cell")]),Eo("cycles",!0),Eo("previousSelector",vt.none),Eo("execute",Dd)],mg=im(function(t,e,n){return tm(t,e,n,-1)},function(t,e,n){return nm(t,e,n,-1)}),gg=im(function(t,e,n){return tm(t,e,n,1)},function(t,e,n){return nm(t,e,n,1)}),pg=im(function(t,e,n){return em(t,n,e,-1)},function(t,e,n){return om(t,n,e,-1)}),hg=im(function(t,e,n){return em(t,n,e,1)},function(t,e,n){return om(t,n,e,1)}),vg=rt([Ll(Nl(Sd),Id(mg,gg)),Ll(Nl(Cd),Rd(mg,gg)),Ll(Nl(kd),eg(pg)),Ll(Nl(Od),ng(hg)),Ll(Nl(wd.concat(yd)),function(e,n,o){return _a(e.element).bind(function(t){return o.execute(e,n,t)})})]),bg=rt([Ll(Nl(wd),Ad)]),yg=Td(dg,xu.init,vg,bg,function(){return vt.some(rm)}),xg=[mo("selector"),Eo("execute",Dd),Eo("moveOnTab",!1)],wg=rt([Ll(Nl(kd),og(cm)),Ll(Nl(Od),og(sm)),Ll(Pl([Vl,Nl(bd)]),function(t,e,n,o){return n.moveOnTab?og(cm)(t,e,n,o):vt.none()}),Ll(Pl([_d,Nl(bd)]),function(t,e,n,o){return n.moveOnTab?og(sm)(t,e,n,o):vt.none()}),Ll(Nl(yd),um),Ll(Nl(wd),um)]),Sg=rt([Ll(Nl(wd),Ad)]),kg=Td(xg,xu.init,wg,Sg,function(){return vt.some(am)}),Cg=Td([$u("onSpace"),$u("onEnter"),$u("onShiftEnter"),$u("onLeft"),$u("onRight"),$u("onTab"),$u("onShiftTab"),$u("onUp"),$u("onDown"),$u("onEscape"),Eo("stopSpaceKeyup",!1),wo("focusIn")],xu.init,function(t,e,n){return[Ll(Nl(wd),n.onSpace),Ll(Pl([_d,Nl(yd)]),n.onEnter),Ll(Pl([Vl,Nl(yd)]),n.onShiftEnter),Ll(Pl([Vl,Nl(bd)]),n.onShiftTab),Ll(Pl([_d,Nl(bd)]),n.onTab),Ll(Nl(kd),n.onUp),Ll(Nl(Od),n.onDown),Ll(Nl(Sd),n.onLeft),Ll(Nl(Cd),n.onRight),Ll(Nl(wd),n.onSpace),Ll(Nl(xd),n.onEscape)]},function(t,e,n){return n.stopSpaceKeyup?[Ll(Nl(wd),Ad)]:[]},function(t){return t.focusIn}),Og=Jm.schema(),_g=$m.schema(),Tg=fg.schema(),Eg=cg.schema(),Dg=yg.schema(),Ag=Qm.schema(),Bg=kg.schema(),Mg=Cg.schema(),Fg=wa({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Og,cyclic:_g,flow:Tg,flatgrid:Eg,matrix:Dg,execution:Ag,menu:Bg,special:Mg}),name:"keying",active:{events:function(t,e){return t.handler.toEvents(t,e)}},apis:{focusIn:function(e,n,o){n.sendFocusIn(n).fold(function(){e.getSystem().triggerFocus(e.element,e.element)},function(t){t(e,n,o)})},setGridSize:function(t,e,n,o,r){et(n,"setGridSize")?n.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:Zm}),Ig=function(t,e){return t.components()},Rg=xa({fields:[],name:"replacing",apis:Object.freeze({__proto__:null,append:function(t,e,n,o){lm(t,0,Tn,o)},prepend:function(t,e,n,o){lm(t,0,Ae,o)},remove:fm,replaceAt:dm,replaceBy:function(e,t,n,o,r){return P(Ig(e),o).bind(function(t){return dm(e,0,0,t,r)})},set:function(e,t,n,o){Ta(function(){var t=B(o,e.getSystem().build);Rs(e,t)},e.element)},contents:Ig})}),Ng=Object.freeze({__proto__:null,focus:gm,blur:function(t,e){e.ignore||t.element.dom.blur()},isFocused:function(t){return Ca(t.element)}}),Pg=Object.freeze({__proto__:null,exhibit:function(t,e){return zr(e.ignore?{}:{attributes:{tabindex:"-1"}})},events:function(n){return nu([Cr(Ai(),function(t,e){gm(t,n),e.stop()})].concat(n.stopMousedown?[Cr(mi(),function(t,e){e.event.prevent()})]:[]))}}),Vg=xa({fields:[Ju("onFocus"),Eo("stopMousedown",!1),Eo("ignore",!1)],name:"focusing",active:Pg,apis:Ng}),Hg=Object.freeze({__proto__:null,onLoad:xm,toggle:vm,isOn:function(t,e,n){return n.get()},on:bm,off:ym,set:Ym}),Lg=Object.freeze({__proto__:null,exhibit:function(){return zr({})},events:function(t,e){var n,o,r,i=(n=t,o=e,r=vm,uu(function(t){r(t,n,o)})),u=ba(t,e,xm);return nu(ft([t.toggleOnExecute?[i]:[],[u]]))}}),zg=xa({fields:[Eo("selected",!1),wo("toggleClass"),Eo("toggleOnExecute",!0),Do("aria",{mode:"none"},lo("mode",{pressed:[Eo("syncWithExpanded",!1),ta("update",function(t,e,n){oe(t.element,"aria-pressed",n),e.syncWithExpanded&&qm(t,0,n)})],checked:[ta("update",function(t,e,n){oe(t.element,"aria-checked",n)})],expanded:[ta("update",qm)],selected:[ta("update",function(t,e,n){oe(t.element,"aria-selected",n)})],none:[ta("update",st)]}))],name:"toggling",active:Lg,apis:Hg,state:{init:function(){var e=Po(!1);return{get:function(){return e.get()},set:function(t){return e.set(t)},clear:function(){return e.set(!1)},readState:function(){return e.get()}}}}}),Ug="alloy.item-hover",jg="alloy.item-focus",Wg=rt(Ug),Gg=rt(jg),Xg=[mo("data"),mo("components"),mo("dom"),Eo("hasSubmenu",!1),wo("toggling"),Af("itemBehaviours",[zg,Vg,Fg,Df]),Eo("ignoreFocus",!1),Eo("domModification",{}),ta("builder",function(t){return{dom:t.dom,domModification:lt(lt({},t.domModification),{attributes:lt(lt(lt({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Bf(t.itemBehaviours,[t.toggling.fold(zg.revoke,function(t){return zg.config(lt({aria:{mode:"checked"}},t))}),Vg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Cm(t)}}),Fg.config({mode:"execution"}),Df.config({store:{mode:"memory",initialValue:t.data}}),mm("item-type-events",V(V([],wm(),!0),[Cr(vi(),km),Cr(Ri(),Vg.focus)],!1))]),components:t.components,eventOrder:t.eventOrder}}),Eo("eventOrder",{})],Yg=[mo("dom"),mo("components"),ta("builder",function(t){return{dom:t.dom,components:t.components,events:nu([Cr(Ri(),function(t,e){e.stop()})])}})],qg=rt("item-widget"),Kg=rt([Jf({name:"widget",overrides:function(e){return{behaviours:ec([Df.config({store:{mode:"manual",getValue:function(t){return e.data},setValue:st}})])}}})]),Jg=lo("type",{widget:[mo("uid"),mo("data"),mo("components"),mo("dom"),Eo("autofocus",!1),Eo("ignoreFocus",!1),Af("widgetBehaviours",[Df,Vg,Fg]),Eo("domModification",{}),xl(Kg()),ta("builder",function(n){function o(t){return ml(t,n,"widget").map(function(t){return Fg.focusIn(t),t})}function t(t,e){return Ol(e.event.target)||n.autofocus&&e.setSource(t.element),vt.none()}var e=fl(qg(),n,Kg()),r=dl(qg(),n,e.internals());return{dom:n.dom,components:r,domModification:n.domModification,events:nu([uu(function(t,e){o(t).each(function(t){e.stop()})}),Cr(vi(),km),Cr(Ri(),function(t,e){n.autofocus?o(t):Vg.focus(t)})]),behaviours:Bf(n.widgetBehaviours,[Df.config({store:{mode:"memory",initialValue:n.data}}),Vg.config({ignore:n.ignoreFocus,onFocus:function(t){Cm(t)}}),Fg.config({mode:"special",focusIn:n.autofocus?function(t){o(t)}:rc(),onLeft:t,onRight:t,onEscape:function(t,e){return Vg.isFocused(t)||n.autofocus?(n.autofocus&&e.setSource(t.element),vt.none()):(Vg.focus(t),vt.some(!0))}})])}})],item:Xg,separator:Yg}),$g=rt([Zf({factory:{sketch:function(t){var e=so("menu.spec item",Jg,t);return e.builder(e)}},name:"items",unit:"item",defaults:function(t,e){return Tt(e,"uid")?e:lt(lt({},e),{uid:pu("item")})},overrides:function(t,e){return{type:e.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Qg=rt([mo("value"),mo("items"),mo("dom"),mo("components"),Eo("eventOrder",{}),Zs("menuBehaviours",[hd,Df,cd,Fg]),Do("movement",{mode:"menu",moveOnTab:!0},lo("mode",{grid:[Ia(),ta("config",function(t,e){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:t.focusManager}})],matrix:[ta("config",function(t,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),mo("rowSelector")],menu:[Eo("moveOnTab",!0),ta("config",function(t,e){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:e.moveOnTab,focusManager:t.focusManager}})]})),go("markers",Fa()),Eo("fakeFocus",!1),Eo("focusManager",Ul()),Ju("onHighlight")]),Zg=rt("alloy.menu-focus"),tp=Cl({name:"Menu",configFields:Qg(),partFields:$g(),factory:function(t,e,n,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:el(t.menuBehaviours,[hd.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),Df.config({store:{mode:"memory",initialValue:t.value}}),cd.config({find:vt.some}),Fg.config(t.movement.config(t,t.movement))]),events:nu([Cr(Gg(),function(e,n){var t=n.event;e.getSystem().getByDom(t.target).each(function(t){hd.highlight(e,t),n.stop(),br(e,Zg(),{menu:e,item:t})})}),Cr(Wg(),function(t,e){var n=e.event.item;hd.highlight(t,n)})]),components:e,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),ep=function(n,o,r,t){return tt(r,t).bind(function(t){return tt(n,t).bind(function(t){var e=ep(n,o,r,t);return vt.some([t].concat(e))})}).getOr([])},np=function(){function a(t){return e(t).bind(Km)}function n(t){return tt(c.get(),t)}var c=Po({}),i=Po({}),s=Po({}),l=gc(),u=Po({}),e=function(t){return tt(i.get(),t)};return{setMenuBuilt:function(t,e){var n;i.set(lt(lt({},i.get()),((n={})[t]={type:"prepared",menu:e},n)))},setContents:function(t,e,n,o){l.set(t),c.set(n),i.set(e),u.set(o);var r=function(t,e){var n={};J(t,function(t,e){St(t,function(t){n[t]=e})});var o=e,r=_t(e,function(t,e){return{k:t,v:e}}),i=dt(r,function(t,e){return[e].concat(ep(n,o,r,e))});return dt(n,function(t){return tt(i,t).getOr([t])})}(o,n);s.set(r)},expand:function(n){return tt(c.get(),n).map(function(t){var e=tt(s.get(),n).getOr([]);return[t].concat(e)})},refresh:function(t){return tt(s.get(),t)},collapse:function(t){return tt(s.get(),t).bind(function(t){return 1<t.length?vt.some(t.slice(1)):vt.none()})},lookupMenu:e,lookupItem:n,otherMenus:function(t){var e=u.get();return U(Ct(e),t)},getPrimary:function(){return l.get().bind(a)},getMenus:function(){return i.get()},clear:function(){c.set({}),i.set({}),s.set({}),l.clear()},isClear:function(){return l.get().isNone()},getTriggeringPath:function(t,u){var e=F(n(t).toArray(),function(t){return a(t).isSome()});return tt(s.get(),t).bind(function(t){var n=z(e.concat(t));return function(t){for(var e=[],n=0;n<t.length;n++){var o=t[n];if(!o.isSome())return vt.none();e.push(o.getOrDie())}return vt.some(e)}(H(n,function(t,e){return o=t,r=u,i=n.slice(0,e+1),a(o).bind(function(e){return n=o,Q(c.get(),function(t,e){return t===n}).bind(function(t){return r(t).map(function(t){return{triggeredMenu:e,triggeringItem:t,triggeringPath:i}})});var n}).fold(function(){return mt(l.get(),t)?[]:[vt.none()]},function(t){return[vt.some(t)]});var o,r,i}))})}}},op=Km,rp=rt("collapse-item"),ip=kl({name:"TieredMenu",configFields:[Zu("onExecute"),Zu("onEscape"),Qu("onOpenMenu"),Qu("onOpenSubmenu"),Ju("onRepositionMenu"),Ju("onCollapseMenu"),Eo("highlightImmediately",!0),bo("data",[mo("primary"),mo("menus"),mo("expansions")]),Eo("fakeFocus",!1),Ju("onHighlight"),Ju("onHover"),Yu(),mo("dom"),Eo("navigateOnHover",!0),Eo("stayInDom",!1),Zs("tmenuBehaviours",[Fg,hd,cd,Rg]),Eo("eventOrder",{})],apis:{collapseMenu:function(t,e){t.collapseMenu(e)},highlightPrimary:function(t,e){t.highlightPrimary(e)},repositionMenus:function(t,e){t.repositionMenus(e)}},factory:function(a,t){function n(t){var o,r,e=(o=t,r=a.data.primary,dt(a.data.menus,function(t,e){function n(){return tp.sketch(lt(lt({},t),{value:e,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?jl:Ul)()}))}return e===r?{type:"prepared",menu:o.getSystem().build(n())}:{type:"notbuilt",nbMenu:n}})),n=dt(a.data.menus,function(t,e){return H(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})});return g.setContents(a.data.primary,e,a.data.expansions,n),g.getPrimary()}function c(t){return Df.getValue(t).value}function u(e,t){hd.highlight(e,t),hd.getHighlighted(t).orThunk(function(){return hd.getFirst(t)}).each(function(t){xr(e,t.element,Ri())})}function s(e,t){return nt(B(t,function(t){return e.lookupMenu(t).bind(function(t){return"prepared"===t.type?vt.some(t.menu):vt.none()})}))}function l(e,t,n){var o=s(t,t.otherMenus(n));St(o,function(t){$r(t.element,[a.markers.backgroundMenu]),a.stayInDom||Rg.remove(e,t)})}function f(t,o){var e;J((e=t,r.get().getOrThunk(function(){var n={},t=F(vs(e.element,"."+a.markers.item),function(t){return"true"===ie(t,"aria-haspopup")});return St(t,function(t){e.getSystem().getByDom(t).each(function(t){var e=c(t);n[e]=t})}),r.set(n),n})),function(t,e){var n=wt(o,e);oe(t.element,"aria-expanded",n)})}function d(o,r,i){return vt.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return vt.none();var e=t.menu,n=s(r,i.slice(1));return St(n,function(t){Yr(t.element,a.markers.backgroundMenu)}),vn(e.element)||Rg.append(o,Eu(e)),$r(e.element,[a.markers.backgroundMenu]),u(o,e),l(o,r,i),vt.some(e)})})}var m,e,r=gc(),g=np();function i(r,i,u){if(void 0===u&&(u=m.HighlightSubmenu),i.hasConfigured(gd)&&gd.isDisabled(i))return vt.some(i);var t=c(i);return g.expand(t).bind(function(o){return f(r,o),vt.from(o[0]).bind(function(n){return g.lookupMenu(n).bind(function(t){var e=function(t,e,n){if("notbuilt"!==n.type)return n.menu;var o=t.getSystem().build(n.nbMenu());return g.setMenuBuilt(e,o),o}(r,n,t);return vn(e.element)||Rg.append(r,Eu(e)),a.onOpenSubmenu(r,i,e,z(o)),u===m.HighlightSubmenu?(hd.highlightFirst(e),d(r,g,o)):(hd.dehighlightAll(e),vt.some(i))})})})}function o(e,n){var t=c(n);return g.collapse(t).bind(function(t){return f(e,t),d(e,g,t).map(function(t){return a.onCollapseMenu(e,n,t),t})})}function p(n){return function(e,t){return Ru(t.getSource(),"."+a.markers.item).bind(function(t){return e.getSystem().getByDom(t).toOptional().bind(function(t){return n(e,t).map(D)})})}}function h(t){return hd.getHighlighted(t).bind(hd.getHighlighted)}(e=m={})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";var v=nu([Cr(Zg(),function(n,o){var t=o.event.item;g.lookupItem(c(t)).each(function(){var t=o.event.menu;hd.highlight(n,t);var e=c(o.event.item);g.refresh(e).each(function(t){return l(n,g,t)})})}),uu(function(e,t){var n=t.event.target;e.getSystem().getByDom(n).each(function(t){0===c(t).indexOf("collapse-item")&&o(e,t),i(e,t,m.HighlightSubmenu).fold(function(){a.onExecute(e,t)},st)})}),ou(function(e,t){n(e).each(function(t){Rg.append(e,Eu(t)),a.onOpenMenu(e,t),a.highlightImmediately&&u(e,t)})})].concat(a.navigateOnHover?[Cr(Wg(),function(t,e){var n=e.event.item,o=t,r=c(n);g.refresh(r).bind(function(t){return f(o,t),d(o,g,t)}),i(t,n,m.HighlightParent),a.onHover(t,n)})]:[])),b={collapseMenu:function(e){h(e).each(function(t){o(e,t)})},highlightPrimary:function(e){g.getPrimary().each(function(t){u(e,t)})},repositionMenus:function(o){g.getPrimary().bind(function(e){return h(o).bind(function(t){var e=c(t),n=nt(B(Z(g.getMenus()),op));return g.getTriggeringPath(e,function(t){return e=t,K(n,function(t){return t.getSystem().isConnected()?N(hd.getCandidates(t),function(t){return c(t)===e}):vt.none()});var e})}).map(function(t){return{primary:e,triggeringPath:t}})}).fold(function(){vt.from(o.components()[0]).filter(function(t){return"menu"===ie(t.element,"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var e=t.primary,n=t.triggeringPath;a.onRepositionMenu(o,e,n)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:el(a.tmenuBehaviours,[Fg.config({mode:"special",onRight:p(function(t,e){return Ol(e.element)?vt.none():i(t,e,m.HighlightSubmenu)}),onLeft:p(function(t,e){return Ol(e.element)?vt.none():o(t,e)}),onEscape:p(function(t,e){return o(t,e).orThunk(function(){return a.onEscape(t,e).map(function(){return t})})}),focusIn:function(e,t){g.getPrimary().each(function(t){xr(e,t.element,Ri())})}}),hd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),cd.config({find:function(t){return hd.getHighlighted(t)}}),Rg.config({})]),eventOrder:a.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(t,e,n){return{primary:t,menus:e,expansions:n}},singleData:function(t,e){return{primary:t,menus:sr(t,e),expansions:{}}},collapseItem:function(t){return{value:Ir(rp()),meta:{text:t}}}}}),up=kl({name:"InlineView",configFields:[mo("lazySink"),Ju("onShow"),Ju("onHide"),Oo("onEscape"),Zs("inlineBehaviours",[bf,Df,ic]),To("fireDismissalEventInstead",[Eo("event",Yi())]),To("fireRepositionEventInstead",[Eo("event",qi())]),Eo("getRelated",vt.none),Eo("isExtraPart",T),Eo("eventOrder",vt.none)],factory:function(d,t){function e(n){bf.isOpen(n)&&Df.getValue(n).each(function(t){switch(t.mode){case"menu":bf.getState(n).each(ip.repositionMenus);break;case"position":var e=d.lazySink(n).getOrDie();ff.positionWithinBounds(e,n,t.config,t.getBounds())}})}function o(t,e,n,o){i(t,e,n,function(){return o.map(function(t){return Mn(t)})})}function r(t,e,n,o){var r,i,u,a,c,s=(r=d,i=t,u=e,a=o,c="horizontal"===n.type?{layouts:{onLtr:ha,onRtl:va}}:{},ip.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightImmediately:n.menu.highlightImmediately,onEscape:function(){return bf.close(i),r.onEscape.map(function(t){return t(i)}),vt.some(!0)},onExecute:function(){return vt.some(!0)},onOpenMenu:function(t,e){ff.positionWithinBounds(l().getOrDie(),e,u,a())},onOpenSubmenu:function(t,e,n,o){var r=l().getOrDie();ff.position(r,n,{anchor:lt({type:"submenu",item:e},f(o))})},onRepositionMenu:function(t,e,n){var o=l().getOrDie();ff.positionWithinBounds(o,e,u,a()),St(n,function(t){var e=f(t.triggeringPath);ff.position(o,t.triggeredMenu,{anchor:lt({type:"submenu",item:t.triggeringItem},e)})})}}));function l(){return r.lazySink(i)}function f(t){return 2===t.length?c:{}}bf.open(t,s),Df.setValue(t,vt.some({mode:"menu",menu:s}))}var i=function(t,e,n,o){var r=d.lazySink(t).getOrDie();bf.openWhileCloaked(t,e,function(){return ff.positionWithinBounds(r,t,n,o())}),Df.setValue(t,vt.some({mode:"position",config:n,getBounds:o}))},n={setContent:function(t,e){bf.setContent(t,e)},showAt:function(t,e,n){o(t,e,n,vt.none())},showWithin:o,showWithinBounds:i,showMenuAt:function(t,e,n){r(t,e,n,vt.none)},showMenuWithinBounds:r,hide:function(t){bf.isOpen(t)&&(Df.setValue(t,vt.none()),bf.close(t))},getContent:function(t){return bf.getState(t)},reposition:e,isOpen:bf.isOpen};return{uid:d.uid,dom:d.dom,behaviours:el(d.inlineBehaviours,[bf.config({isPartOf:function(t,e,n){return Hu(e,n)||(o=n,d.getRelated(t).exists(function(t){return Hu(t,o)}));var o},getAttachPoint:function(t){return d.lazySink(t).getOrDie()},onOpen:function(t){d.onShow(t)},onClose:function(t){d.onHide(t)}}),Df.config({store:{mode:"memory",initialValue:vt.none()}}),ic.config({channels:lt(lt({},Xs(lt({isExtraPart:t.isExtraPart},d.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Ys(lt(lt({},d.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:e})))})]),eventOrder:d.eventOrder,apis:n}},apis:{showAt:function(t,e,n,o){t.showAt(e,n,o)},showWithin:function(t,e,n,o,r){t.showWithin(e,n,o,r)},showWithinBounds:function(t,e,n,o,r){t.showWithinBounds(e,n,o,r)},showMenuAt:function(t,e,n,o){t.showMenuAt(e,n,o)},showMenuWithinBounds:function(t,e,n,o,r){t.showMenuWithinBounds(e,n,o,r)},hide:function(t,e){t.hide(e)},isOpen:function(t,e){return t.isOpen(e)},getContent:function(t,e){return t.getContent(e)},setContent:function(t,e,n){t.setContent(e,n)},reposition:function(t,e){t.reposition(e)}}}),ap="layout-inset",cp=function(t,e,n){return na(Om(t,e),t.y,n.insetNorth(),La(),"north",ra(t,{top:2}),ap)},sp=function(t,e,n){return na(Om(t,e),Tm(t,e),n.insetSouth(),za(),"south",ra(t,{bottom:3}),ap)},lp=tinymce.util.Tools.resolve("tinymce.util.Delay"),fp=kl({name:"Button",factory:function(t){function n(e){return tt(t.dom,"attributes").bind(function(t){return tt(t,e)})}var e=Sm(t.action),o=t.dom.tag;return{uid:t.uid,dom:t.dom,components:t.components,events:e,behaviours:Bf(t.buttonBehaviours,[Vg.config({}),Fg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:n("role").getOr("button")};var t=n("type").getOr("button"),e=n("role").map(function(t){return{role:t}}).getOr({});return lt({type:t},e)}()},eventOrder:t.eventOrder}},configFields:[Eo("uid",void 0),mo("dom"),Eo("components",[]),Af("buttonBehaviours",[Vg,Fg]),wo("action"),wo("role"),Eo("eventOrder",{})]}),dp=tinymce.util.Tools.resolve("tinymce.util.I18n"),mp={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},gp="temporary-placeholder",pp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},hp=kl({name:"Notification",factory:function(e){function n(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}}function o(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}}var t,r,i,u,a=Hm({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:ec([Rg.config({})])}),c=Hm({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},o(0)],behaviours:ec([Rg.config({})])}),s={updateProgress:function(t,e){t.getSystem().isConnected()&&c.getOpt(t).each(function(t){Rg.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(e)]},o(e)])})},updateText:function(t,e){var n;t.getSystem().isConnected()&&(n=a.get(t),Rg.set(n,[ri(e)]))}},l=ft([e.icon.toArray(),e.level.toArray(),e.level.bind(function(t){return vt.from(pp[t])}).toArray()]),f=Hm(fp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Xm("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:function(t){e.onAction(t)}})),d=[(t=l,r={tag:"div",classes:["tox-notification__icon"]},i=e.iconProvider,u=i(),Gm(r,N(t,function(t){return Tt(u,zm(t,u))}).getOr(gp),u,vt.none())),{dom:{tag:"div",classes:["tox-notification__body"]},components:[a.asSpec()],behaviours:ec([Rg.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},behaviours:ec([Vg.config({}),mm("notification-events",[Cr(bi(),function(t){f.getOpt(t).each(Vg.focus)})])]),components:d.concat(e.progress?[c.asSpec()]:[]).concat(e.closeButton?[f.asSpec()]:[]),apis:s}},configFields:[wo("level"),mo("progress"),mo("icon"),mo("onAction"),mo("text"),mo("iconProvider"),mo("translationProvider"),Fo("closeButton",!0)],apis:{updateProgress:function(t,e,n){t.updateProgress(e,n)},updateText:function(t,e,n){t.updateText(e,n)}}});function vp(n,o){function r(){b(i)||(clearTimeout(i),i=null)}var i=null;return{cancel:r,throttle:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];r(),i=setTimeout(function(){i=null,n.apply(null,t)},o)}}}function bp(o,t,e,n,r){var i=Ap(o,function(t){return(e=o).isBlock(n=t)||wt(["BR","IMG","HR","INPUT"],n.nodeName)||"false"===e.getContentEditable(n);var e,n});return vt.from(i.backwards(t,e,n,r))}function yp(n,e){return Bp(Mt.fromDom(n.selection.getNode())).getOrThunk(function(){var i,u,t=Mt.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',n.getDoc());return Tn(t,Mt.fromDom(e.extractContents())),e.insertNode(t.dom),Yt(t).each(function(t){return t.dom.normalize()}),i=hs,(u=function(t){for(var e=Kt(t),n=e.length-1;0<=n;n--){var o=e[n];if(i(o))return vt.some(o);var r=u(o);if(r.isSome())return r}return vt.none()})(t).map(function(t){var e;n.selection.setCursorLocation(t.dom,"img"===Ft(e=t)?1:ps(e).fold(function(){return Kt(e).length},function(t){return t.length}))}),t})}function xp(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function wp(t){return""!==t&&-1!==" \xa0\f\n\r\t\v".indexOf(t)}function Sp(t,e){return t.substring(e.length)}function kp(o,t,r,e){return void 0===e&&(e=0),Bp(Mt.fromDom(t.startContainer)).fold(function(){return function(t,o,i,r){if(void 0===r&&(r=0),!o.collapsed||3!==o.startContainer.nodeType)return vt.none();var e=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return bp(t,o.startContainer,o.startOffset,function(t,r,e){return function(t,e){for(var n=r-1;0<=n;n--){var o=t.charAt(n);if(wp(o))return vt.none();if(o===e)break}return vt.some(n)}(e,i).getOr(r)},e).bind(function(t){var e=o.cloneRange();if(e.setStart(t.container,t.offset),e.setEnd(o.endContainer,o.endOffset),e.collapsed)return vt.none();var n=xp(e);return 0!==n.lastIndexOf(i)||Sp(n,i).length<r?vt.none():vt.some({text:Sp(n,i),range:e,triggerChar:i})})}(o,t,r,e)},function(t){var e=o.createRng();e.selectNode(t.dom);var n=xp(e);return vt.some({range:e,text:Sp(n,r),triggerChar:r})})}function Cp(t,e){return{container:t,offset:e}}function Op(t){return ao("toolbarbutton",Hp,t)}function _p(t){return ao("ToggleButton",zp,t)}function Tp(e,t,n,o){void 0===o&&(o={});var r=t(),i=e.selection.getRng().startContainer.nodeValue,u=F(r.lookupByChar(n.triggerChar),function(t){return n.text.length>=t.minChars&&t.matches.getOrThunk(function(){return n=e.dom,function(t){var e=Ip(t.startContainer,t.startOffset);return!bp(n,e.container,e.offset,function(t,e){return 0===e?-1:e},n.getRoot()).filter(function(t){return!wp(t.container.data.charAt(t.offset-1))}).isSome()};var n})(n.range,i,n.text)});if(0===u.length)return vt.none();var a=Mp.all(B(u,function(e){return e.fetch(n.text,e.maxResults,o).then(function(t){return{matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}})}));return vt.some({lookupData:a,context:n})}var Ep,Dp,Ap=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),Bp=function(t){return Ru(t,"[data-mce-autocompleter]")},Mp=tinymce.util.Tools.resolve("tinymce.util.Promise"),Fp=function(t){if(3===t.nodeType)return Cp(t,t.data.length);var e=t.childNodes;return 0<e.length?Fp(e[e.length-1]):Cp(t,e.length)},Ip=function(t,e){var n=t.childNodes;return 0<n.length&&e<n.length?Ip(n[e],0):0<n.length&&1===t.nodeType&&n.length===e?Fp(n[n.length-1]):Cp(t,e)},Rp=$o([po("type"),Co("text")]),Np=$o([Eo("type","autocompleteitem"),Eo("active",!1),Eo("disabled",!1),Eo("meta",{}),po("value"),Co("text"),Co("icon")]),Pp=$o([po("type"),po("ch"),Ao("minChars",1),Eo("columns",1),Ao("maxResults",10),Oo("matches"),vo("fetch"),vo("onAction"),Ro("highlightOn",[],er)]),Vp=[Fo("disabled",!1),Co("tooltip"),Co("icon"),Co("text"),Io("onSetup",function(){return st})],Hp=$o([po("type"),vo("onAction")].concat(Vp)),Lp=[Fo("active",!1)].concat(Vp),zp=$o(Lp.concat([po("type"),vo("onAction")])),Up=[Io("predicate",T),Mo("scope","node",["node","editor"]),Mo("position","selection",["node","selection","line"])],jp=Vp.concat([Eo("type","contextformbutton"),Eo("primary",!1),vo("onAction"),cr("original",h)]),Wp=Lp.concat([Eo("type","contextformbutton"),Eo("primary",!1),vo("onAction"),cr("original",h)]),Gp=Vp.concat([Eo("type","contextformbutton")]),Xp=Lp.concat([Eo("type","contextformtogglebutton")]),Yp=lo("type",{contextformbutton:jp,contextformtogglebutton:Wp}),qp=$o([Eo("type","contextform"),Io("initValue",rt("")),Co("label"),xo("commands",Yp),So("launch",lo("type",{contextformbutton:Gp,contextformtogglebutton:Xp}))].concat(Up)),Kp=$o([Eo("type","contexttoolbar"),po("items")].concat(Up));function Jp(t){return tt(_h,t).getOr(kh)}function $p(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}}function Qp(t){var e=$p(t);return{backgroundMenu:e.backgroundMenu,selectedMenu:e.selectedMenu,menu:e.menu,selectedItem:e.selectedItem,item:Jp(t)}}function Zp(t,e,n){return{dom:{tag:"div",classes:ft([[$p(n).tieredMenu]])},markers:Qp(n)}}function th(e,n){return function(t){return B(p(t,n),function(t){return{dom:e,components:t}})}}function eh(t,n){var o=[],r=[];return St(t,function(t,e){n(t,e)?(0<r.length&&o.push(r),r=[],Tt(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),B(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})}function nh(e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[tp.parts.items({preprocess:function(t){return"auto"!==e&&1<e?th({tag:"div",classes:["tox-collection__group"]},e)(t):eh(t,function(t,e){return"separator"===n[e].type})}})]}}function oh(t){return d(t,function(t){return"icon"in t&&void 0!==t.icon})}function rh(t){return console.error(ur(t)),console.log(t),vt.none()}function ih(t,e,n,o,r){var i,u=(i=n,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[tp.parts.items({preprocess:function(t){return eh(t,function(t,e){return"separator"===i[e].type})}})]});return{value:t,dom:u.dom,components:u.components,items:n}}function uh(t,e,n,o,r){var i,u;return"color"===r?{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[tp.parts.items({preprocess:"auto"!==o?th({tag:"div",classes:["tox-swatches__row"]},o):h})]}]}).dom,components:i.components,items:n}:"normal"===r&&"auto"===o?{value:t,dom:(i=nh(o,n)).dom,components:i.components,items:n}:"normal"===r&&1===o?{value:t,dom:(i=nh(1,n)).dom,components:i.components,items:n}:"normal"===r?{value:t,dom:(i=nh(o,n)).dom,components:i.components,items:n}:"listpreview"!==r||"auto"===o?{value:t,dom:{tag:"div",classes:ft([[(u=$p(r)).menu,"tox-menu-"+o+"-column"],e?[u.hasIcons]:[]])},components:Ih,items:n}:{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[tp.parts.items({preprocess:th({tag:"div",classes:["tox-collection__group"]},o)})]}).dom,components:i.components,items:n}}function ah(t,o,e){var r=vs(t.element,"."+e);if(0<r.length){var n=P(r,function(t){var e=t.dom.getBoundingClientRect().top,n=r[0].dom.getBoundingClientRect().top;return Math.abs(e-n)>o}).getOr(r.length);return vt.some({numColumns:n,numRows:Math.ceil(r.length/n)})}return vt.none()}function ch(t,e,n){t.getSystem().broadcastOn([Jh],{})}function sh(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))}function lh(t){return t.getParam("width",nv.DOM.getStyle(t.getElement(),"width"))}function fh(t){return vt.from(t.getParam("min_width")).filter(u)}function dh(t){return vt.from(t.getParam("min_height")).filter(u)}function mh(t){return vt.from(t.getParam("max_width")).filter(u)}function gh(t){return vt.from(t.getParam("max_height")).filter(u)}function ph(t){return!1!==t.getParam("menubar",!0,"boolean")}function hh(t){var e=t.getParam("toolbar",!0),n=!0===e,o=y(e),r=c(e)&&0<e.length;return!rv(t)&&(r||o||n)}function vh(e){var t=F(m(9,function(t){return e.getParam("toolbar"+(t+1),!1,"string")}),function(t){return"string"==typeof t});return 0<t.length?vt.some(t):vt.none()}(Dp=Ep={})[Dp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Dp[Dp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var bh,yh,xh,wh,Sh=Ep,kh="tox-menu-nav__js",Ch="tox-collection__item",Oh="tox-swatch",_h={normal:kh,color:Oh},Th="tox-collection__item--enabled",Eh="tox-collection__item-icon",Dh="tox-collection__item-label",Ah="tox-collection__item-caret",Bh="tox-collection__item--active",Mh="tox-collection__item-container",Fh="tox-collection__item-container--row",Ih=[tp.parts.items({})],Rh=[po("type"),po("src"),Co("alt"),Ro("classes",[],er)],Nh=$o(Rh),Ph=[po("type"),po("text"),Co("name"),Ro("classes",["tox-collection__item-label"],er)],Vh=$o(Ph),Hh=Zn(function(){return ro("type",{cardimage:Nh,cardtext:Vh,cardcontainer:Lh})}),Lh=$o([po("type"),Bo("direction","horizontal"),Bo("align","left"),Bo("valign","middle"),xo("items",Hh)]),zh=[Fo("disabled",!1),Co("text"),Co("shortcut"),ar("value","value",Wn(function(){return Ir("menuitem-value")}),Zo()),Eo("meta",{})],Uh=$o([po("type"),Co("label"),xo("items",Hh),Io("onSetup",function(){return st}),Io("onAction",st)].concat(zh)),jh=$o([po("type"),Fo("active",!1),Co("icon")].concat(zh)),Wh=[po("type"),po("fancytype"),Io("onAction",st)],Gh=lo("fancytype",{inserttable:[Eo("initData",{})].concat(Wh),colorswatch:[No("initData",{},[Fo("allowCustomColors",!0),_o("colors",Zo())])].concat(Wh)}),Xh=$o([po("type"),Io("onSetup",function(){return st}),Io("onAction",st),Co("icon")].concat(zh)),Yh=$o([po("type"),vo("getSubmenuItems"),Io("onSetup",function(){return st}),Co("icon")].concat(zh)),qh=$o([po("type"),Co("icon"),Fo("active",!1),Io("onSetup",function(){return st}),vo("onAction")].concat(zh)),Kh=function(t){return e=Ir("unnamed-events"),ec([mm(e,t)]);var e},Jh=Ir("tooltip.exclusive"),$h=Ir("tooltip.show"),Qh=Ir("tooltip.hide"),Zh=Object.freeze({__proto__:null,hideAllExclusive:ch,setComponents:function(t,e,n,o){n.getTooltip().each(function(t){t.getSystem().isConnected()&&Rg.set(t,o)})}}),tv=Object.freeze({__proto__:null,events:function(r,i){function n(e){i.getTooltip().each(function(t){Vs(t),r.onHide(e,t),i.clearTooltip()}),i.clearTimer()}return nu(ft([[Cr($h,function(o){i.resetTimer(function(){var t,e,n=o;i.isShowing()||(ch(n),t=r.lazySink(n).getOrDie(),e=n.getSystem().build({dom:r.tooltipDom,components:r.tooltipComponents,events:nu("normal"===r.mode?[Cr(vi(),function(t){vr(n,$h)}),Cr(pi(),function(t){vr(n,Qh)})]:[]),behaviours:ec([Rg.config({})])}),i.setTooltip(e),Ns(t,e),r.onShow(n,e),ff.position(t,e,{anchor:r.anchor(n)}))},r.delay)}),Cr(Qh,function(t){i.resetTimer(function(){n(t)},r.delay)}),Cr(Fi(),function(t,e){e.universal||wt(e.channels,Jh)&&n(t)}),ru(function(t){n(t)})],"normal"===r.mode?[Cr(bi(),function(t){vr(t,$h)}),Cr(Bi(),function(t){vr(t,Qh)}),Cr(vi(),function(t){vr(t,$h)}),Cr(pi(),function(t){vr(t,Qh)})]:[Cr(Zi(),function(t,e){vr(t,$h)}),Cr(tu(),function(t){vr(t,Qh)})]]))}}),ev=xa({fields:[mo("lazySink"),mo("tooltipDom"),Eo("exclusive",!0),Eo("tooltipComponents",[]),Eo("delay",300),Mo("mode","normal",["normal","follow-highlight"]),Eo("anchor",function(t){return{type:"hotspot",hotspot:t,layouts:{onLtr:rt([$a,Ja,Xa,qa,Ya,Ka]),onRtl:rt([$a,Ja,Xa,qa,Ya,Ka])}}}),Ju("onHide"),Ju("onShow")],name:"tooltipping",active:tv,state:Object.freeze({__proto__:null,init:function(){function n(){o.on(clearTimeout)}var o=gc(),t=gc(),e=rt("not-implemented");return wu({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:n,resetTimer:function(t,e){n(),o.set(setTimeout(t,e))},readState:e})}}),apis:Zh}),nv=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ov=tinymce.util.Tools.resolve("tinymce.EditorManager"),rv=function(t){return vh(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},D)};function iv(t){return t.getParam("toolbar_mode","","string")}function uv(t){return t.getParam("toolbar_location",xh.auto,"string")}function av(t){return uv(t)===xh.bottom}function cv(t){if(!t.inline)return vt.none();var e=t.getParam("fixed_toolbar_container","","string");if(0<e.length)return Iu(bn(),e);var n=t.getParam("fixed_toolbar_container_target");return k(n)?vt.some(Mt.fromDom(n)):vt.none()}function sv(t){return t.inline&&cv(t).isSome()}function lv(t){return cv(t).getOrThunk(function(){return te(gn(Mt.fromDom(t.getElement())))})}function fv(t){return t.inline&&!ph(t)&&!hh(t)&&!rv(t)}function dv(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!sv(t)&&!fv(t)}function mv(t,e){var n=t.outerContainer.element;e&&(t.mothership.broadcastOn([yf()],{target:n}),t.uiMothership.broadcastOn([yf()],{target:n})),t.mothership.broadcastOn([Kv],{readonly:e}),t.uiMothership.broadcastOn([Kv],{readonly:e})}function gv(t,e){t.on("init",function(){t.mode.isReadOnly()&&mv(e,!0)}),t.on("SwitchMode",function(){return mv(e,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")}function pv(){var t;return ic.config({channels:((t={})[Kv]={schema:Jv,onReceive:function(t,e){gd.set(t,e.readonly)}},t)})}function hv(t,e){var n=t.getApi(e);return function(t){t(n)}}function vv(n,o){return ou(function(t){hv(n,t)(function(t){var e=n.onSetup(t);S(e)&&o.set(e)})})}function bv(e,n){return ru(function(t){return hv(e,t)(n.get())})}function yv(t,e,n,o){var r,i,u=Po(st);return{type:"item",dom:e.dom,components:tb(e.optComponents),data:t.data,eventOrder:Zv,hasSubmenu:t.triggersSubmenu,itemBehaviours:ec([mm("item-events",[(r=t,i=n,uu(function(t,e){hv(r,t)(r.onAction),r.triggersSubmenu||i!==Sh.CLOSE_ON_EXECUTE||(vr(t,Vi()),e.stop())})),vv(t,u),bv(t,u)]),gd.config({disabled:function(){return t.disabled||o.isDisabled()},disableClass:"tox-collection__item--state-disabled"}),pv(),Rg.config({})].concat(t.itemBehaviours))}}function xv(t){return{value:t.value,meta:lt({text:t.text.getOr("")},t.meta)}}function wv(t,e,n){return Xm(t,{tag:"div",classes:n=void 0===n?[Eh]:n},e)}function Sv(t){return{dom:{tag:"div",classes:[Dh]},components:[ri(dp.translate(t))]}}function kv(t,e){return{dom:{tag:"div",classes:e,innerHtml:t}}}function Cv(t,e){return{dom:{tag:"div",classes:[Dh]},components:[{dom:{tag:t.tag,styles:t.styles},components:[ri(dp.translate(e))]}]}}function Ov(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(n=eb.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},e=B(t.split("+"),function(t){var e=t.toLowerCase().trim();return Tt(n,e)?n[e]:t}),eb.mac?e.join(""):e.join("+"))}};var n,e}function _v(t){return wv("checkmark",t,["tox-collection__item-checkmark"])}function Tv(t){var e=t.map(function(t){return{attributes:{title:dp.translate(t)}}}).getOr({});return lt({tag:"div",classes:[kh,Ch]},e)}function Ev(t,e,n,o){return void 0===o&&(o=vt.none()),"color"===t.presets?(r=e,i=o,c=t.ariaLabel,s=t.value,{dom:(u=t.iconContent.map(function(t){return e=r.icons,n=i,Um(t,o=e()).or(n).getOrThunk(Lm(o));var e,n,o}).getOr(""),a={tag:"div",attributes:c.map(function(t){return{title:r.translate(t)}}).getOr({}),classes:["tox-swatch"]},lt(lt({},a),"custom"===s?{tag:"button",classes:V(V([],a.classes,!0),["tox-swatches__picker-btn"],!1),innerHtml:u}:"remove"===s?{classes:V(V([],a.classes,!0),["tox-swatch--remove"],!1),innerHtml:u}:{attributes:lt(lt({},a.attributes),{"data-mce-color":s}),styles:{"background-color":s}})),optComponents:[]}):(l=t,f=e,d=o,m={tag:"div",classes:[Eh]},g=n?l.iconContent.map(function(t){return Xm(t,m,f.icons,d)}).orThunk(function(){return vt.some({dom:m})}):vt.none(),p=l.checkMark,h=vt.from(l.meta).fold(function(){return Sv},function(t){return Tt(t,"style")?C(Cv,t.style):Sv}),v=l.htmlContent.fold(function(){return l.textContent.map(h)},function(t){return vt.some(kv(t,[Dh]))}),{dom:Tv(l.ariaLabel),optComponents:[g,v,l.shortcutContent.map(Ov),p,l.caret]});var r,i,u,a,c,s,l,f,d,m,g,p,h,v}function Dv(t,e){return tt(t,"tooltipWorker").map(function(n){return[ev.config({lazySink:e.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{type:"submenu",item:t,overrides:{maxHeightFunction:Nc}}},mode:"follow-highlight",onShow:function(e,t){n(function(t){ev.setComponents(e,[Ou({element:Mt.fromDom(t)})])})}})]}).getOr([])}function Av(t,e){var n=dp.translate(t),o=nv.DOM.encode(n);if(0<e.length){var r=new RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return o.replace(r,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return o}function Bv(t){return{value:t}}function Mv(t){return rb.test(t)||ib.test(t)}function Fv(t){var e=t.toString(16);return(1===e.length?"0"+e:e).toUpperCase()}function Iv(t){return Bv(Fv(t.red)+Fv(t.green)+Fv(t.blue))}function Rv(t,e,n,o){return{red:t,green:e,blue:n,alpha:o}}function Nv(t){var e=parseInt(t,10);return e.toString()===t&&0<=e&&e<=255}function Pv(t){var e,n,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100,i=ab(0,ub(i,1)),u=ab(0,ub(u,1));if(0===i)return Rv(e=n=o=cb(255*u),n,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:e=c,n=s,o=0;break;case 1:e=s,n=c,o=0;break;case 2:e=0,n=c,o=s;break;case 3:e=0,n=s,o=c;break;case 4:e=s,n=0,o=c;break;case 5:e=c,n=0,o=s;break;default:e=n=o=0}return Rv(e=cb(255*(e+l)),n=cb(255*(n+l)),o=cb(255*(o+l)),1)}function Vv(t){var e,n,o=(e={value:t.value.replace(rb,function(t,e,n,o){return e+e+n+n+o+o})},null===(n=ib.exec(e.value))?["FFFFFF","FF","FF","FF"]:n);return Rv(parseInt(o[1],16),parseInt(o[2],16),parseInt(o[3],16),1)}function Hv(t,e,n,o){return Rv(parseInt(t,10),parseInt(e,10),parseInt(n,10),parseFloat(o))}function Lv(t){if("transparent"===t)return vt.some(Rv(0,0,0,0));var e=sb.exec(t);if(null!==e)return vt.some(Hv(e[1],e[2],e[3],"1"));var n=lb.exec(t);return null!==n?vt.some(Hv(n[1],n[2],n[3],n[4])):vt.none()}function zv(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"}function Uv(t,e){return t.fire("ResizeContent",e)}function jv(t,e,n){return{hue:t,saturation:e,value:n}}function Wv(t){var e,n,o=0,r=t.red/255,i=t.green/255,u=t.blue/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?jv(0,0,100*(o=a)):(n=60*((r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),e=(c-a)/c,o=c,jv(Math.round(n),Math.round(100*e),Math.round(100*o)))}function Gv(t){return Iv(Pv(t))}function Xv(o){return(Mv(e=o)?vt.some({value:(Dt(t=e,"#")?t.substring("#".length):t).toUpperCase()}):vt.none()).orThunk(function(){return Lv(o).map(Iv)}).getOrThunk(function(){var t=document.createElement("canvas");t.height=1,t.width=1;var e=t.getContext("2d");e.clearRect(0,0,t.width,t.height),e.fillStyle="#FFFFFF",e.fillStyle=o,e.fillRect(0,0,1,1);var n=e.getImageData(0,0,1,1).data;return Iv(Rv(n[0],n[1],n[2],n[3]))});var t,e}(yh=bh=bh||{}).default="wrap",yh.floating="floating",yh.sliding="sliding",yh.scrolling="scrolling",(wh=xh=xh||{}).auto="auto",wh.top="top",wh.bottom="bottom";function Yv(t){return gd.config({disabled:t,disableClass:"tox-tbtn--disabled"})}var qv,Kv="silver.readonly",Jv=$o([go("readonly",nr)]),$v=function(t){return gd.config({disabled:t})},Qv=function(t){return gd.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},Zv=((qv={})[Ii()]=["disabling","alloy.base.behaviour","toggling","item-events"],qv),tb=nt,eb=tinymce.util.Tools.resolve("tinymce.Env"),nb=function(t,a){return B(t,function(t){switch(t.type){case"cardcontainer":return r=nb((o=t).items,a),i="vertical"===o.direction?"tox-collection__item-container--column":Fh,u="left"===o.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right",{dom:{tag:"div",classes:[Mh,i,u,function(){switch(o.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}}()]},components:r};case"cardimage":return n=t.src,{dom:{tag:"img",classes:t.classes,attributes:{src:n,alt:t.alt.getOr("")}}};case"cardtext":var e=t.name.exists(function(t){return wt(a.cardText.highlightOn,t)})?vt.from(a.cardText.matchText).getOr(""):"";return kv(Av(t.text,e),t.classes)}var n,o,r,i,u})},ob=al(qg(),Kg()),rb=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,ib=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ub=Math.min,ab=Math.max,cb=Math.round,sb=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,lb=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,fb=Rv(255,0,0,1),db=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),mb="tinymce-custom-colors";function gb(t){return!1!==t.getParam("custom_colors")}function pb(t){var e=t.getParam("color_map");return void 0!==e?function(t){for(var e=[],n=0;n<t.length;n+=2)e.push({text:t[n+1],value:"#"+Xv(t[n]).value,type:"choiceitem"});return e}(e):Bb}function hb(t){Mb.add(t)}function vb(t){var e,n=(e=pb(t).length,Math.max(5,Math.ceil(Math.sqrt(e))));return t.getParam("color_cols",n,"number")}function bb(t){var e="choiceitem",n={type:e,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return t?[n,{type:e,text:"Custom color",icon:"color-picker",value:"custom"}]:[n]}function yb(e,n,t,o){"custom"===t?Ib(e)(function(t){t.each(function(t){hb(t),e.execCommand("mceApplyTextcolor",n,t),o(t)})},Fb):"remove"===t?(o(""),e.execCommand("mceRemoveTextcolor",n)):(o(t),e.execCommand("mceApplyTextcolor",n,t))}function xb(t,e){return t.concat(B(Mb.state(),function(t){return{type:Ab,text:t,value:t}}).concat(bb(e)))}function wb(e,n){return function(t){t(xb(e,n))}}function Sb(t,e,n){t.setIconFill("forecolor"===e?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",n)}function kb(i,n,u,t,o){i.ui.registry.addSplitButton(n,{tooltip:t,presets:"color",icon:"forecolor"===n?"text-color":"highlight-bg-color",select:function(n){var o,r=u;return i.dom.getParents(i.selection.getStart(),function(t){var e;(e=t.style["forecolor"===r?"color":"background-color"])&&(o=o||e)}),vt.from(o).bind(function(t){return Lv(t).map(function(t){var e=Iv(t).value;return ut(n.toLowerCase(),e)})}).getOr(!1)},columns:vb(i),fetch:wb(pb(i),gb(i)),onAction:function(t){yb(i,u,o.get(),st)},onItemAction:function(t,e){yb(i,u,e,function(t){o.set(t),i.fire("TextColorChange",{name:n,color:t})})},onSetup:function(e){function t(t){t.name===n&&Sb(e,t.name,t.color)}return Sb(e,n,o.get()),i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})}function Cb(e,t,n,o){e.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){yb(e,n,t.value,st)}}]}})}function Ob(t,e,n,o,r,i,u,a){return uh(t,oh(e),Rb(e,n,o,"color"!==r?"normal":"color",i,u,a),o,r)}function _b(t,e){var n=Qp(e);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+n.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===e?"tox-swatches__row":"tox-collection__group")}}function Tb(t,e){return ri(e+"x"+t)}var Eb,Db,Ab="choiceitem",Bb=[{type:Ab,text:"Light Green",value:"#BFEDD2"},{type:Ab,text:"Light Yellow",value:"#FBEEB8"},{type:Ab,text:"Light Red",value:"#F8CAC6"},{type:Ab,text:"Light Purple",value:"#ECCAFA"},{type:Ab,text:"Light Blue",value:"#C2E0F4"},{type:Ab,text:"Green",value:"#2DC26B"},{type:Ab,text:"Yellow",value:"#F1C40F"},{type:Ab,text:"Red",value:"#E03E2D"},{type:Ab,text:"Purple",value:"#B96AD9"},{type:Ab,text:"Blue",value:"#3598DB"},{type:Ab,text:"Dark Turquoise",value:"#169179"},{type:Ab,text:"Orange",value:"#E67E23"},{type:Ab,text:"Dark Red",value:"#BA372A"},{type:Ab,text:"Dark Purple",value:"#843FA1"},{type:Ab,text:"Dark Blue",value:"#236FA1"},{type:Ab,text:"Light Gray",value:"#ECF0F1"},{type:Ab,text:"Medium Gray",value:"#CED4D9"},{type:Ab,text:"Gray",value:"#95A5A6"},{type:Ab,text:"Dark Gray",value:"#7E8C8D"},{type:Ab,text:"Navy Blue",value:"#34495E"},{type:Ab,text:"Black",value:"#000000"},{type:Ab,text:"White",value:"#ffffff"}],Mb=function(n){void 0===n&&(n=10);function o(t){i.splice(t,1)}var t,e=db.getItem(mb),r=y(e)?JSON.parse(e):[],i=n-(t=r).length<0?t.slice(0,n):t;return{add:function(t){var e;(-1===(e=l(i,t))?vt.none():vt.some(e)).each(o),i.unshift(t),i.length>n&&i.pop(),db.setItem(mb,JSON.stringify(i))},state:function(){return i.slice(0)}}}(10),Fb="#000000",Ib=function(r){return function(n,t){var o=!1;r.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:{colorpicker:t},onAction:function(t,e){"hex-valid"===e.name&&(o=e.value)},onSubmit:function(t){var e=t.getData().colorpicker;o?(n(vt.from(e)),t.close()):r.windowManager.alert(r.translate(["Invalid hex color code: {0}",e]))},onClose:st,onCancel:function(){n(vt.none())}})}},Rb=function(n,o,r,i,u,a,c){return nt(B(n,function(e){return"choiceitem"===e.type?ao("choicemenuitem",jh,e).fold(rh,function(t){return vt.some(function(e,t,n,o,r,i,u,a){void 0===a&&(a=!0);var c=Ev({presets:n,textContent:t?e.text:vt.none(),htmlContent:vt.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:vt.none(),checkMark:t?vt.some(_v(u.icons)):vt.none(),caret:vt.none(),value:e.value},u,a);return Yo(yv({data:xv(e),disabled:e.disabled,getApi:function(e){return{setActive:function(t){zg.set(e,t)},isActive:function(){return zg.isOn(e)},isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}},onAction:function(t){return o(e.value)},onSetup:function(t){return t.setActive(r),st},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:Th,toggleOnExecute:!1,selected:e.active}})}(t,1===r,i,o,a(e.value),u,c,oh(n)))}):vt.none()}))},Nb=Ir("cell-over"),Pb=Ir("cell-execute"),Vb={inserttable:function(u){var t=Ir("size-label"),a=function(t){for(var e=[],n=0;n<10;n++){for(var o=[],r=0;r<10;r++)o.push(function(e,n,t){function o(t){return br(t,Pb,{row:e,col:n})}function r(t,e){e.stop(),o(t)}var i;return Tu({dom:{tag:"div",attributes:((i={role:"button"})["aria-labelledby"]=t,i)},behaviours:ec([mm("insert-table-picker-cell",[Cr(vi(),Vg.focus),Cr(Ii(),o),Cr(Ci(),r),Cr(Ni(),r)]),zg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Vg.config({onFocus:function(t){return br(t,Nb,{row:e,col:n})}})])})}(n,r,t));e.push(o)}return e}(t),e=Tb(0,0),c=Hm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[e],behaviours:ec([Rg.config({})])});return{type:"widget",data:{value:Ir("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ob.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:H(a,function(t){return B(t,Eu)}).concat(c.asSpec()),behaviours:ec([mm("insert-table-picker",[ou(function(t){Rg.set(c.get(t),[e])}),Er(Nb,function(t,e,n){var o=n.event,r=o.row,i=o.col;!function(t,e,n){for(var o=0;o<10;o++)for(var r=0;r<10;r++)zg.set(t[o][r],o<=e&&r<=n)}(a,r,i),Rg.set(c.get(t),[Tb(r+1,i+1)])}),Er(Pb,function(t,e,n){var o=n.event,r=o.row,i=o.col;u.onAction({numRows:r+1,numColumns:i+1}),vr(t,Vi())})]),Fg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function(e,t){var n,o,r,i=(o=t,r=(n=e).initData.allowCustomColors&&o.colorinput.hasCustomColors(),n.initData.colors.fold(function(){return xb(o.colorinput.getColors(),r)},function(t){return t.concat(bb(r))})),u=t.colorinput.getColorCols(),a=Ob(Ir("menu-value"),i,function(t){e.onAction({value:t})},u,"color",Sh.CLOSE_ON_EXECUTE,T,t.shared.providers),c=lt(lt({},a),{markers:Qp("color"),movement:_b(u,"color")});return{type:"widget",data:{value:Ir("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ob.widget(tp.sketch(c))]}}},Hb=function(t){var e=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:lt({tag:"div",classes:[Ch,"tox-collection__group-heading"]},e),components:[]}},Lb=function(t,e,n,o){void 0===o&&(o=!0);var r=Ev({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.none(),checkMark:vt.none(),shortcutContent:t.shortcut},n,o);return yv({data:xv(t),getApi:function(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,e,n)},zb=function(t,e,n,o,r){void 0===o&&(o=!0);var i=(r=void 0!==r&&r)?wv("chevron-down",n.icons,[Ah]):wv("chevron-right",n.icons,[Ah]),u=Ev({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.some(i),checkMark:vt.none(),shortcutContent:t.shortcut},n,o);return yv({data:xv(t),getApi:function(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}},disabled:t.disabled,onAction:st,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,e,n)},Ub=function(t,e,n,o){void 0===o&&(o=!0);var r=Ev({iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,checkMark:vt.some(_v(n.icons)),caret:vt.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},n,o);return Yo(yv({data:xv(t),disabled:t.disabled,getApi:function(e){return{setActive:function(t){zg.set(e,t)},isActive:function(){return zg.isOn(e)},isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,e,n),{toggling:{toggleClass:Th,toggleOnExecute:!1,selected:t.active}})},jb=function(e,n){return tt(Vb,e.fancytype).map(function(t){return t(e,n)})};function Wb(t,u,a,e,c,s,l){var n=1===e,o=!n||oh(t);return nt(B(t,function(t){switch(t.type){case"separator":return ao("Autocompleter.Separator",Rp,t).fold(rh,function(t){return vt.some(Hb(t))});case"cardmenuitem":return ao("cardmenuitem",Uh,t).fold(rh,function(e){return vt.some((t=lt(lt({},e),{onAction:function(t){e.onAction(t),a(e.value,e.meta)}}),n=c,o=s,r={itemBehaviours:Dv(e.meta,s),cardText:{matchText:u,highlightOn:l}},i={dom:Tv(t.label),optComponents:[vt.some({dom:{tag:"div",classes:[Mh,Fh]},components:nb(t.items,r)})]},yv({data:xv(lt({text:vt.none()},t)),disabled:t.disabled,getApi:function(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(e){gd.set(n,e),St(vs(n.element,"*"),function(t){n.getSystem().getByDom(t).each(function(t){t.hasConfigured(gd)&&gd.set(t,e)})})}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:vt.from(r.itemBehaviours).getOr([])},i,n,o.providers)));var t,n,o,r,i});default:return ao("Autocompleter.Item",Np,t).fold(rh,function(t){return vt.some(function(e,n,t,o,r,i,u,a){void 0===a&&(a=!0);var c=Ev({presets:o,textContent:vt.none(),htmlContent:t?e.text.map(function(t){return Av(t,n)}):vt.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:vt.none(),checkMark:vt.none(),caret:vt.none(),value:e.value},u.providers,a,e.icon);return yv({data:xv(e),disabled:e.disabled,getApi:rt({}),onAction:function(t){return r(e.value,e.meta)},onSetup:rt(st),triggersSubmenu:!1,itemBehaviours:Dv(e.meta,u)},c,i,u.providers)}(t,u,n,"normal",a,c,s,o))})}}))}function Gb(t,e,n,o,r){var i=oh(e),u=nt(B(e,function(t){function e(t){return function(t,e,n,o,r){function i(t){return r?lt(lt({},t),{shortcut:vt.none(),icon:t.text.isSome()?vt.none():t.icon}):t}var u=n.shared.providers;switch(t.type){case"menuitem":return ao("menuitem",Xh,t).fold(rh,function(t){return vt.some(Lb(i(t),e,u,o))});case"nestedmenuitem":return ao("nestedmenuitem",Yh,t).fold(rh,function(t){return vt.some(zb(i(t),e,u,o,r))});case"togglemenuitem":return ao("togglemenuitem",qh,t).fold(rh,function(t){return vt.some(Ub(i(t),e,u,o))});case"separator":return ao("separatormenuitem",Rp,t).fold(rh,function(t){return vt.some(Hb(t))});case"fancymenuitem":return ao("fancymenuitem",Gh,t).fold(rh,function(t){return jb(i(t),n)});default:return console.error("Unknown item in general menu",t),vt.none()}}(t,n,o,r?!Tt(t,"text"):i,r)}return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?e(lt(lt({},t),{disabled:!0})):e(t)}));return(r?ih:uh)(t,i,u,1,"normal")}function Xb(t){return ip.singleData(t.value,t)}function Yb(t,e,n){return Ru(t,e,n).isSome()}function qb(n,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];r=setTimeout(function(){n.apply(null,t),r=null},o)}}}function Kb(t){var e=t.raw;return void 0===e.touches||1!==e.touches.length?vt.none():vt.some(e.touches[0])}function Jb(){return ln().browser.isFirefox()}function $b(e,t){var n,o,r,i,u,a,c,s,l,f=lt({stopBackspace:!0},t),d=(u=f,a=gc(),c=Po(!1),s=qb(function(t){u.triggerEvent(Pi(),t),c.set(!0)},400),l=lr([{key:si(),value:function(n){return Kb(n).each(function(t){s.cancel();var e={x:t.clientX,y:t.clientY,target:n.target};s.schedule(n),c.set(!1),a.set(e)}),vt.none()}},{key:li(),value:function(t){return s.cancel(),Kb(t).each(function(i){a.on(function(t){var e=i,n=t,o=Math.abs(e.clientX-n.x),r=Math.abs(e.clientY-n.y);(5<o||5<r)&&a.clear()})}),vt.none()}},{key:fi(),value:function(e){return s.cancel(),a.get().filter(function(t){return zt(t.target,e.target)}).map(function(t){return c.get()?(e.prevent(),!1):u.triggerEvent(Ni(),e)})}}]),{fireIfReady:function(e,t){return tt(l,t).bind(function(t){return t(e)})}}),m=B(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return pc(e,t,function(e){d.fireIfReady(e,t).each(function(t){t&&e.kill()}),f.triggerEvent(t,e)&&e.kill()})}),g=gc(),p=pc(e,"paste",function(e){d.fireIfReady(e,"paste").each(function(t){t&&e.kill()}),f.triggerEvent("paste",e)&&e.kill(),g.set(setTimeout(function(){f.triggerEvent(Mi(),e)},0))}),h=pc(e,"keydown",function(t){var e;f.triggerEvent("keydown",t)?t.kill():!f.stopBackspace||(e=t).raw.which!==vd[0]||wt(["input","textarea"],Ft(e.target))||Yb(e.target,'[contenteditable="true"]')||t.prevent()}),v=(n=e,o=function(t){f.triggerEvent("focusin",t)&&t.kill()},Jb()?hc(n,"focus",o):pc(n,"focusin",o)),b=gc(),y=(r=e,i=function(t){f.triggerEvent("focusout",t)&&t.kill(),b.set(setTimeout(function(){f.triggerEvent(Bi(),t)},0))},Jb()?hc(r,"blur",i):pc(r,"focusout",i));return{unbind:function(){St(m,function(t){t.unbind()}),h.unbind(),v.unbind(),y.unbind(),p.unbind(),g.on(clearTimeout),b.on(clearTimeout)}}}function Qb(t,e){return Po(tt(t,"target").getOr(e))}function Zb(t,o,e,n,r,i){var u,a,c=t(o,n),s=(u=Po(!1),a=Po(!1),{stop:function(){u.set(!0)},cut:function(){a.set(!0)},isStopped:u.get,isCut:a.get,event:e,setSource:r.set,getSource:r.get});return c.fold(function(){return i.logEventNoHandlers(o,n),ay.complete()},function(e){var n=e.descHandler;return Ur(n)(s),s.isStopped()?(i.logEventStopped(o,e.element,n.purpose),ay.stopped()):s.isCut()?(i.logEventCut(o,e.element,n.purpose),ay.complete()):Yt(e.element).fold(function(){return i.logNoParent(o,e.element,n.purpose),ay.complete()},function(t){return i.logEventResponse(o,e.element,n.purpose),ay.resume(t)})})}function ty(){function r(t){Rr(t.element).each(function(t){delete a[t],i.unregisterId(t)})}var u,i=(u={},{registerId:function(r,i,t){J(t,function(t,e){var n,o=void 0!==u[e]?u[e]:{};o[i]={cHandler:C.apply(void 0,[(n=t).handler].concat(r)),purpose:n.purpose},u[e]=o})},unregisterId:function(n){J(u,function(t,e){Tt(t,n)&&delete t[n]})},filterByType:function(t){return tt(u,t).map(function(t){return $(t,function(t,e){return{id:e,descHandler:t}})}).getOr([])},find:function(t,e,n){return tt(u,e).bind(function(o){return mr(n,function(t){return e=o,Rr(n=t).bind(function(t){return tt(e,t)}).map(function(t){return{element:n,descHandler:t}});var e,n},t)})}}),a={};return{find:function(t,e,n){return i.find(t,e,n)},filter:function(t){return i.filterByType(t)},register:function(t){var n,o=Rr((n=t).element).getOrThunk(function(){return t=n.element,e=Ir(du+"uid-"),gu(t,e),e;var t,e});et(a,o)&&function(t){var e=a[o];if(e!==t)throw new Error('The tagId "'+o+'" is already used by: '+Fr(e.element)+"\nCannot use it for: "+Fr(t.element)+"\nThe conflicting element is"+(vn(e.element)?" ":" not ")+"already in the DOM");r(t)}(t),i.registerId([t],o,t.events),a[o]=t},unregister:r,getById:function(t){return tt(a,t)}}}function ey(n){function o(e){return Yt(n.element).fold(D,function(t){return zt(e,t)})}function s(t,e){return i.find(o,t,e)}function r(e){var t=i.filter(Fi());St(t,function(t){Ur(t.descHandler)(e)})}var i=ty(),t=$b(n.element,{triggerEvent:function(e,n){return zu(e,n.target,function(t){return sy(s,e,n,n.target,t)})}}),u={debugInfo:rt("real"),triggerEvent:function(e,n,o){zu(e,n,function(t){return sy(s,e,o,n,t)})},triggerFocus:function(a,c){Rr(a).fold(function(){ka(a)},function(t){zu(Ai(),a,function(t){var e,n,o=s,r=Ai(),i=t,u=Qb(e={originator:c,kill:st,prevent:st,target:a},n=a);return Zb(o,r,e,n,u,i),!1})})},triggerEscape:function(t,e){u.triggerEvent("keydown",t.element,e.event)},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:Tu,addToGui:function(t){c(t)},removeFromGui:function(t){l(t)},addToWorld:function(t){e(t)},removeFromWorld:function(t){a(t)},broadcast:function(t){f(t)},broadcastOn:function(t,e){d(t,e)},broadcastEvent:function(t,e){m(t,e)},isConnected:D},e=function(t){t.connect(u),Xe(t.element)||(i.register(t),St(t.components(),e),u.triggerEvent(Li(),t.element,{target:t.element}))},a=function(t){Xe(t.element)||(St(t.components(),a),i.unregister(t)),t.disconnect()},c=function(t){Ns(n,t)},l=function(t){Vs(t)},f=function(t){r({universal:!0,data:t})},d=function(t,e){r({universal:!1,channels:t,data:e})},m=function(t,e){var n,o,r=i.filter(t);return o={stop:function(){n.set(!0)},cut:st,isStopped:(n=Po(!1)).get,isCut:T,event:e,setSource:_("Cannot set source of a broadcasted event"),getSource:_("Cannot get source of a broadcasted event")},St(r,function(t){Ur(t.descHandler)(o)}),o.isStopped()},g=function(t){return i.getById(t).fold(function(){return Nn.error(new Error('Could not find component with uid: "'+t+'" in system.'))},Nn.value)},p=function(t){var e=Rr(t).getOr("not found");return g(e)};return e(n),{root:n,element:n.element,destroy:function(){t.unbind(),En(n.element)},add:c,remove:l,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:a,broadcast:f,broadcastOn:d,broadcastEvent:m}}function ny(t,e,n,o){var r=vy(t,e,n,o);return my.sketch(r)}function oy(t,e){return my.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:e.translate(t)}})}function ry(t){return ec([Vg.config({onFocus:t.selectOnFocus?function(t){var e=t.element,n=Qr(e);e.dom.setSelectionRange(0,n.length)}:st})])}function iy(t){return{tag:t.tag,attributes:lt({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}}(Db=Eb={})[Db.ContentFocus=0]="ContentFocus",Db[Db.UiFocus=1]="UiFocus";function uy(f,c){function n(){return r.get().isSome()}function s(){n()&&up.hide(d)}var o,t,r=gc(),l=Po(!1),d=Tu(up.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:ec([mm("dismissAutocompleter",[Cr(Yi(),function(){return m()})])]),lazySink:c.getSink})),m=function(){var t;n()&&(t=r.get().map(function(t){return t.element}),Bp(t.getOr(Mt.fromDom(f.selection.getNode()))).each(Fe),s(),r.clear(),l.set(!1))},u=Rt(function(){return n=dt(f.ui.registry.getAll().popups,function(t){return ao("Autocompleter",Pp,t).fold(function(t){throw new Error(ur(t))},h)}),t=$(n,function(t){return t.ch}),e={},St(t,function(t){e[t]={}}),o=Ct(e),r=Z(n),{dataset:n,triggerChars:o,lookupByChar:function(e){return F(r,function(t){return t.ch===e})}};var t,e,n,o,r}),g=function(t){var e=t;r.get().map(function(t){return kp(f.dom,f.selection.getRng(),t.triggerChar).bind(function(t){return Tp(f,u,t,e)})}).getOrThunk(function(){return e=f,t=(n=u)(),o=e.selection.getRng(),r=e.dom,i=o,K(t.triggerChars,function(t){return kp(r,i,t)}).bind(function(t){return Tp(e,n,t)});var e,n,t,o,r,i}).fold(m,function(a){var t,e=a.context;n()||(t=yp(f,e.range),r.set({triggerChar:e.triggerChar,element:t,matchLength:e.text.length}),l.set(!1)),a.lookupData.then(function(u){r.get().map(function(t){var e,n,o,r,i=a.context;t.triggerChar===i.triggerChar&&(n=i.triggerChar,r=K(o=u,function(t){return vt.from(t.columns)}).getOr(1),0<(e=H(o,function(i){return Wb(i.items,i.matchText,function(o,r){var t=f.selection.getRng();kp(f.dom,t,n).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var e=t.range,n={hide:function(){m()},reload:function(t){s(),g(t)}};l.set(!0),i.onAction(n,e,o,r),l.set(!1)})},r,Sh.BUBBLE_TO_SANDBOX,c,i.highlightOn)})).length?function(t,e,n,o){t.matchLength=e.text.length;var r,i,u,a,c,s,l=K(n,function(t){return vt.from(t.columns)}).getOr(1);up.showAt(d,tp.sketch((r=uh("autocompleter-value",!0,o,l,"normal"),i=l,a=((u=Eb.ContentFocus)===Eb.ContentFocus?jl:Ul)(),c=_b(i,"normal"),s=Qp("normal"),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:s.selectedItem,item:s.item},movement:c,fakeFocus:u===Eb.ContentFocus,focusManager:a,menuBehaviours:Kh("auto"!==i?[]:[ou(function(o,t){ah(o,4,s.item).each(function(t){var e=t.numColumns,n=t.numRows;Fg.setGridSize(o,n,e)})})])})),{anchor:{type:"node",root:Mt.fromDom(f.getBody()),node:vt.from(t.element)}}),up.getContent(d).each(hd.highlightFirst)}(t,i,u,e):(10<=i.text.length-t.matchLength?m:s)())})})})},e={onKeypress:vp(function(t){27!==t.which&&g()},50),cancelIfNecessary:m,isMenuOpen:function(){return up.isOpen(d)},isActive:n,isProcessingAction:l.get,getView:function(){return up.getContent(d)}};function i(t,e){br(t,xi(),{raw:e})}!1===f.hasPlugin("rtc")&&(o=e,(t=f).on("keypress compositionend",o.onKeypress.throttle),t.on("remove",o.onKeypress.cancel),t.on("keydown",function(e){function t(){return o.getView().bind(hd.getHighlighted)}8===e.which&&o.onKeypress.throttle(e),o.isActive()&&(27===e.which&&o.cancelIfNecessary(),o.isMenuOpen()?13===e.which?(t().each(yr),e.preventDefault()):40===e.which?(t().fold(function(){o.getView().each(hd.highlightFirst)},function(t){i(t,e)}),e.preventDefault(),e.stopImmediatePropagation()):37!==e.which&&38!==e.which&&39!==e.which||t().each(function(t){i(t,e),e.preventDefault(),e.stopImmediatePropagation()}):13!==e.which&&38!==e.which&&40!==e.which||o.cancelIfNecessary())}),t.on("NodeChange",function(t){o.isActive()&&!o.isProcessingAction()&&Bp(Mt.fromDom(t.element)).isNone()&&o.cancelIfNecessary()}))}var ay=Vo([{stopped:[]},{resume:["element"]},{complete:[]}]),cy=function(e,n,o,t,r,i){return Zb(e,n,o,t,r,i).fold(D,function(t){return cy(e,n,o,t,r,i)},T)},sy=function(t,e,n,o,r){var i=Qb(n,o);return cy(t,e,n,o,i,r)},ly=kl({name:"Container",factory:function(t){var e=t.dom,n=e.attributes,o=A(e,["attributes"]);return{uid:t.uid,dom:lt({tag:"div",attributes:lt({role:"presentation"},n)},o),components:t.components,behaviours:tl(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[Eo("components",[]),Zs("containerBehaviours",[]),Eo("events",{}),Eo("domModification",{}),Eo("eventOrder",{})]}),fy=rt([Eo("prefix","form-field"),Zs("fieldBehaviours",[cd,Df])]),dy=rt([Qf({schema:[mo("dom")],name:"label"}),Qf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[mo("text")],name:"aria-descriptor"}),Jf({factory:{sketch:function(t){var n,o,e=(n=["factory"],o={},J(t,function(t,e){wt(n,e)||(o[e]=t)}),o);return t.factory.sketch(e)}},schema:[mo("factory")],name:"field"})]),my=Cl({name:"FormField",configFields:fy(),partFields:dy(),factory:function(r,t,e,n){var o=el(r.fieldBehaviours,[cd.config({find:function(t){return ml(t,r,"field")}}),Df.config({store:{mode:"manual",getValue:function(t){return cd.getCurrent(t).bind(Df.getValue)},setValue:function(t,e){cd.getCurrent(t).each(function(t){Df.setValue(t,e)})}}})]),i=nu([ou(function(t,e){var o=pl(t,r,["label","field","aria-descriptor"]);o.field().each(function(n){var e=Ir(r.prefix);o.label().each(function(t){oe(t.element,"for",e),oe(n.element,"id",e)}),o["aria-descriptor"]().each(function(t){var e=Ir(r.prefix);oe(t.element,"id",e),oe(n.element,"aria-describedby",e)})})})]);return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:{getField:function(t){return ml(t,r,"field")},getLabel:function(t){return ml(t,r,"label")}}}},apis:{getField:function(t,e){return t.getField(e)},getLabel:function(t,e){return t.getLabel(e)}}}),gy=Object.freeze({__proto__:null,exhibit:function(t,e){return zr({attributes:lr([{key:e.tabAttr,value:"true"}])})}}),py=xa({fields:[Eo("tabAttr","data-alloy-tabstop")],name:"tabstopping",active:gy}),hy=tinymce.util.Tools.resolve("tinymce.html.Entities"),vy=function(t,e,n,o){return{dom:by(n),components:t.toArray().concat([e]),fieldBehaviours:ec(o)}},by=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},yy=Ir("form-component-change"),xy=Ir("form-close"),wy=Ir("form-cancel"),Sy=Ir("form-action"),ky=Ir("form-submit"),Cy=Ir("form-block"),Oy=Ir("form-unblock"),_y=Ir("form-tabchange"),Ty=Ir("form-resize"),Ey=rt([wo("data"),Eo("inputAttributes",{}),Eo("inputStyles",{}),Eo("tag","input"),Eo("inputClasses",[]),Ju("onSetValue"),Eo("styles",{}),Eo("eventOrder",{}),Zs("inputBehaviours",[Df,Vg]),Eo("selectOnFocus",!0)]),Dy=kl({name:"Input",configFields:Ey(),factory:function(t,e){return{uid:t.uid,dom:iy(t),components:[],behaviours:lt(lt({},ry(n=t)),el(n.inputBehaviours,[Df.config({store:lt(lt({mode:"manual"},n.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return Qr(t.element)},setValue:function(t,e){Qr(t.element)!==e&&Zr(t.element,e)}}),onSetValue:n.onSetValue})])),eventOrder:t.eventOrder};var n}}),Ay={},By={exports:Ay};function My(t){setTimeout(function(){throw t},0)}function Fy(t){var e=Ft(t);return wt(Xy,e)}function Iy(t,e){qr(e.getRoot(t).getOr(t.element),e.invalidClass),e.notify.each(function(e){Fy(t.element)&&oe(t.element,"aria-invalid",!1),e.getContainer(t).each(function(t){Mr(t,e.validHtml)}),e.onValid(t)})}function Ry(e,t,n,o){Yr(t.getRoot(e).getOr(e.element),t.invalidClass),t.notify.each(function(t){Fy(e.element)&&oe(e.element,"aria-invalid",!0),t.getContainer(e).each(function(t){Mr(t,o)}),t.onInvalid(e,o)})}function Ny(e,t,n){return t.validator.fold(function(){return Gy(Nn.value(!0))},function(t){return t.validate(e)})}function Py(e,n,t){return n.notify.each(function(t){t.onValidate(e)}),Ny(e,n).map(function(t){return e.getSystem().isConnected()?t.fold(function(t){return Ry(e,n,0,t),Nn.error(t)},function(t){return Iy(e,n),Nn.value(t)}):Nn.error("No longer in system")})}!function(){var t=this,e=function(){var t,e,n,o={exports:{}};function r(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(t,this)}function u(n,o){for(;3===n._state;)n=n._value;0!==n._state?(n._handled=!0,i._immediateFn(function(){var t,e=1===n._state?o.onFulfilled:o.onRejected;if(null!==e){try{t=e(n._value)}catch(t){return void c(o.promise,t)}a(o.promise,t)}else(1===n._state?a:c)(o.promise,n._value)})):n._deferreds.push(o)}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof i)return e._state=3,e._value=t,void s(e);if("function"==typeof n)return void f((o=n,r=t,function(){o.apply(r,arguments)}),e)}e._state=1,e._value=t,s(e)}catch(t){c(e,t)}var o,r}function c(t,e){t._state=2,t._value=e,s(t)}function s(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var e=0,n=t._deferreds.length;e<n;e++)u(t,t._deferreds[e]);t._deferreds=null}function l(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function f(t,e){var n=!1;try{t(function(t){n||(n=!0,a(e,t))},function(t){n||(n=!0,c(e,t))})}catch(t){if(n)return;n=!0,c(e,t)}}t=o,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=setTimeout,i.prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,e){var n=new this.constructor(r);return u(this,new l(t,e,n)),n},i.all=function(t){var a=Array.prototype.slice.call(t);return new i(function(r,i){if(0===a.length)return r([]);for(var u=a.length,t=0;t<a.length;t++)!function e(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var o=t.then;if("function"==typeof o)return o.call(t,function(t){e(n,t)},i),0}a[n]=t,0==--u&&r(a)}catch(t){i(t)}}(t,a[t])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(t){t(e)})},i.reject=function(n){return new i(function(t,e){e(n)})},i.race=function(r){return new i(function(t,e){for(var n=0,o=r.length;n<o;n++)r[n].then(t,e)})},i._immediateFn="function"==typeof setImmediate?function(t){setImmediate(t)}:function(t){n(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},t.exports?t.exports=i:e.Promise||(e.Promise=i);var d=o.exports;return{boltExport:("undefined"!=typeof window?window:Function("return this;")()).Promise||d}};"object"==typeof Ay&&void 0!==By?By.exports=e():(t="undefined"!=typeof globalThis?globalThis:t||self).EphoxContactWrapper=e()}();var Vy,Hy,Ly=By.exports.boltExport,zy=function(t){function o(t){r()?i(t):e.push(t)}var n=vt.none(),e=[],r=function(){return n.isSome()},i=function(e){n.each(function(t){setTimeout(function(){e(t)},0)})};return t(function(t){r()||(n=vt.some(t),St(e,i),e=[])}),{get:o,map:function(n){return zy(function(e){o(function(t){e(n(t))})})},isReady:r}},Uy={nu:zy,pure:function(e){return zy(function(t){t(e)})}},jy=function(n){function t(t){n().then(t,My)}return{map:function(t){return jy(function(){return n().then(t)})},bind:function(e){return jy(function(){return n().then(function(t){return e(t).toPromise()})})},anonBind:function(t){return jy(function(){return n().then(function(){return t.toPromise()})})},toLazy:function(){return Uy.nu(t)},toCached:function(){var t=null;return jy(function(){return t=null===t?n():t})},toPromise:n,get:t}},Wy=function(t){return jy(function(){return new Ly(t)})},Gy=function(t){return jy(function(){return Ly.resolve(t)})},Xy=["input","textarea"],Yy=Object.freeze({__proto__:null,markValid:Iy,markInvalid:Ry,query:Ny,run:Py,isInvalid:function(t,e){return Kr(e.getRoot(t).getOr(t.element),e.invalidClass)}}),qy=Object.freeze({__proto__:null,events:function(e,t){return e.validator.map(function(t){return nu([Cr(t.onEvent,function(t){Py(t,e).get(h)})].concat(t.validateOnLoad?[ou(function(t){Py(t,e).get(st)})]:[]))}).getOr({})}}),Ky=xa({fields:[mo("invalidClass"),Eo("getRoot",vt.none),To("notify",[Eo("aria","alert"),Eo("getContainer",vt.none),Eo("validHtml",""),Ju("onValid"),Ju("onInvalid"),Ju("onValidate")]),To("validator",[mo("validate"),Eo("onEvent","input"),Eo("validateOnLoad",!0)])],name:"invalidating",active:qy,apis:Yy,extra:{validation:function(n){return function(t){var e=Df.getValue(t);return Gy(n(e))}}}}),Jy=Object.freeze({__proto__:null,getCoupled:function(t,e,n,o){return n.getOrCreate(t,e,o)}}),$y=xa({fields:[go("others",uo(Nn.value,Zo()))],name:"coupling",apis:Jy,state:Object.freeze({__proto__:null,init:function(){var i={},t=rt({});return wu({readState:t,getOrCreate:function(n,o,r){var t=Ct(o.others);if(t)return tt(i,r).getOrThunk(function(){var t=tt(o.others,r).getOrDie("No information found for coupled component: "+r)(n),e=n.getSystem().build(t);return i[r]=e});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),Qy=rt("sink"),Zy=rt(Qf({name:Qy(),overrides:rt({dom:{tag:"div"},behaviours:ec([ff.config({useFixed:D})]),events:nu([Dr(xi()),Dr(mi()),Dr(Ci())])})}));function tx(t,e){var n=t.getHotspot(e).getOr(e),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{type:"hotspot",hotspot:n,overrides:o}},function(t){return{type:"hotspot",hotspot:n,overrides:o,layouts:t}})}function ex(t,e,n,o,r,i,u){var a,c=tx(t,n),s=n,l=o,f=r,d=u,m=e,g=(0,(a=t).fetch)(s).map(m),p=mw(s,a);return g.map(function(t){return t.bind(function(t){return vt.from(ip.sketch(lt(lt({},f.menu()),{uid:pu(""),data:t,highlightImmediately:d===Vy.HighlightFirst,onOpenMenu:function(t,e){var n=p().getOrDie();ff.position(n,e,{anchor:c}),bf.decloak(l)},onOpenSubmenu:function(t,e,n){var o=p().getOrDie();ff.position(o,n,{anchor:{type:"submenu",item:e}}),bf.decloak(l)},onRepositionMenu:function(t,e,n){var o=p().getOrDie();ff.position(o,e,{anchor:c}),St(n,function(t){ff.position(o,t.triggeredMenu,{anchor:{type:"submenu",item:t.triggeringItem}})})},onEscape:function(){return Vg.focus(s),bf.close(l),vt.some(!0)}})))})}).map(function(t){return t.fold(function(){bf.isOpen(o)&&bf.close(o)},function(t){bf.cloak(o),bf.open(o,t),i(o)}),o})}function nx(t,e,n,o,r,i){var u=$y.getCoupled(n,"sandbox");return(bf.isOpen(u)?function(t,e,n,o,r,i,u){return bf.close(o),Gy(o)}:ex)(t,e,n,u,o,r,i)}function ox(t){bf.getState(t).each(function(t){ip.repositionMenus(t)})}function rx(s,l,f){var d=Nu(),t=mw(l,s);return{dom:{tag:"div",classes:s.sandboxClasses,attributes:{id:d.id,role:"listbox"}},behaviours:Bf(s.sandboxBehaviours,[Df.config({store:{mode:"memory",initialValue:l}}),bf.config({onOpen:function(t,e){var n,o,r,i,u,a,c=tx(s,l);d.link(l.element),s.matchWidth&&(n=c.hotspot,o=e,r=s.useMinWidth,u=cd.getCurrent(o).getOr(o),a=Ce(n.element),r?fe(u.element,"min-width",a+"px"):(i=u.element,On.set(i,a))),s.onOpen(c,t,e),void 0!==f&&void 0!==f.onOpen&&f.onOpen(t,e)},onClose:function(t,e){d.unlink(l.element),void 0!==f&&void 0!==f.onClose&&f.onClose(t,e)},isPartOf:function(t,e,n){return Hu(e,n)||Hu(l,n)},getAttachPoint:function(){return t().getOrDie()}}),cd.config({find:function(t){return bf.getState(t).bind(function(t){return cd.getCurrent(t)})}}),ic.config({channels:lt(lt({},Xs({isExtraPart:T})),Ys({doReposition:ox}))})])}}function ix(t){ox($y.getCoupled(t,"sandbox"))}function ux(){return[Eo("sandboxClasses",[]),Af("sandboxBehaviours",[cd,ic,bf,Df])]}function ax(e){return Qf({name:e+"-edge",overrides:function(t){return t.model.manager.edgeActions[e].fold(function(){return{}},function(o){return{events:nu([Or(si(),function(t,e,n){return o(t,n)},[t]),Or(mi(),function(t,e,n){return o(t,n)},[t]),Or(gi(),function(t,e,n){n.mouseIsDown.get()&&o(t,n)},[t])])}})}})}function cx(t){var e=t.event.raw;return-1===e.type.indexOf("touch")?void 0!==e.clientX?vt.some(e).map(function(t){return kn(t.clientX,t.clientY)}):vt.none():void 0!==e.touches&&1===e.touches.length?vt.some(e.touches[0]).map(function(t){return kn(t.clientX,t.clientY)}):vt.none()}function sx(t){return t.model.minX}function lx(t){return t.model.minY}function fx(t){return t.model.minX-1}function dx(t){return t.model.minY-1}function mx(t){return t.model.maxX}function gx(t){return t.model.maxY}function px(t){return t.model.maxX+1}function hx(t){return t.model.maxY+1}function vx(t,e,n){return e(t)-n(t)}function bx(t){return vx(t,mx,sx)}function yx(t){return vx(t,gx,lx)}function xx(t){return bx(t)/2}function wx(t){return yx(t)/2}function Sx(t){return t.stepSize}function kx(t){return t.snapToGrid}function Cx(t){return t.snapStart}function Ox(t){return t.rounded}function _x(t,e){return void 0!==t[e+"-edge"]}function Tx(t){return _x(t,"left")}function Ex(t){return _x(t,"right")}function Dx(t){return _x(t,"top")}function Ax(t){return _x(t,"bottom")}function Bx(t){return t.model.value.get()}function Mx(t,e){return{x:t,y:e}}function Fx(t,e){br(t,Aw(),{value:e})}function Ix(t,e,n,o){return t<e?t:n<t?n:t===e?e-1:Math.max(e,t-o)}function Rx(t,e,n,o){return n<t?t:t<e?e:t===n?n+1:Math.min(n,t+o)}function Nx(t,e,n){return Math.max(e,Math.min(n,t))}function Px(t){var e=t.min,n=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?e-1:e,p=l?n+1:n;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w=Nx((x=f,Math.min(d,Math.max(r,x))-x)/m*o+e,g,p);return u&&e<=w&&w<=n?(h=w,v=e,b=n,y=i,a.fold(function(){var t=Math.round((h-v)/y)*y;return Nx(v+t,v-1,b+1)},function(t){var e=Math.round((h-t)%y/y),n=Math.floor((h-t)/y),o=Math.floor((b-t)/y),r=Math.min(o,n+e);return Math.max(t,t+r*y)})):c?Math.round(w):w}function Vx(t){var e=t.min,n=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<e?i?0:s:n<r?u?a:l:(r-e)/o*c}function Hx(t){return t.element.dom.getBoundingClientRect()}function Lx(t){return Hx(t)[Bw]}function zx(t){return Hx(t).right}function Ux(t){return Hx(t).top}function jx(t){return Hx(t).bottom}function Wx(t){return Hx(t).width}function Gx(t){return Hx(t).height}function Xx(t,e){var n=Hx(t),o=Hx(e);return(n[Bw]+n.right)/2-o[Bw]}function Yx(t,e){var n=Hx(t),o=Hx(e);return(n.top+n.bottom)/2-o.top}function qx(t,e){br(t,Aw(),{value:e})}function Kx(t,e,n){return Px({min:sx(e),max:mx(e),range:bx(e),value:n,step:Sx(e),snap:kx(e),snapStart:Cx(e),rounded:Ox(e),hasMinEdge:Tx(e),hasMaxEdge:Ex(e),minBound:Lx(t),maxBound:zx(t),screenRange:Wx(t)})}function Jx(r){return function(t,e){return qx(t,{x:o=(0<r?Rx:Ix)(Bx(n=e).x,sx(n),mx(n),Sx(n))}),vt.some(o).map(D);var n,o}}function $x(t,e,n,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=n,s=o,l=r,f=Wx(u=e),d=s.bind(function(t){return vt.some(Xx(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Xx(t,u))}).getOr(f),Vx({min:sx(a),max:mx(a),range:bx(a),value:c,hasMinEdge:Tx(a),hasMaxEdge:Ex(a),minBound:Lx(u),minOffset:0,maxBound:zx(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Lx(e)-Lx(t)+g}function Qx(t,e){br(t,Aw(),{value:e})}function Zx(t,e,n){return Px({min:lx(e),max:gx(e),range:yx(e),value:n,step:Sx(e),snap:kx(e),snapStart:Cx(e),rounded:Ox(e),hasMinEdge:Dx(e),hasMaxEdge:Ax(e),minBound:Ux(t),maxBound:jx(t),screenRange:Gx(t)})}function t0(r){return function(t,e){return Qx(t,{y:o=(0<r?Rx:Ix)(Bx(n=e).y,lx(n),gx(n),Sx(n))}),vt.some(o).map(D);var n,o}}function e0(t,e,n,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=n,s=o,l=r,f=Gx(u=e),d=s.bind(function(t){return vt.some(Yx(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Yx(t,u))}).getOr(f),Vx({min:lx(a),max:gx(a),range:yx(a),value:c,hasMinEdge:Dx(a),hasMaxEdge:Ax(a),minBound:Ux(u),minOffset:0,maxBound:jx(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Ux(e)-Ux(t)+g}function n0(t,e){br(t,Aw(),{value:e})}function o0(t,e){return{x:t,y:e}}function r0(u,a){return function(t,e){return o=e,r=0<u?Rx:Ix,n0(t,o0(i=(n=a)?Bx(o).x:r(Bx(o).x,sx(o),mx(o),Sx(o)),n?r(Bx(o).y,lx(o),gx(o),Sx(o)):Bx(o).y)),vt.some(i).map(D);var n,o,r,i}}function i0(t){return"<alloy.field."+t+">"}function u0(f,d,m,g){function p(t,e,n,o,r){var i,u,a=f(uS+"range"),c=[my.parts.label({dom:{tag:"label",innerHtml:n,attributes:{"aria-label":o}}}),my.parts.field({data:r,factory:Dy,inputAttributes:lt({type:"text"},"hex"===e?{"aria-live":"polite"}:{}),inputClasses:[d("textfield")],inputBehaviours:ec([(i=e,u=t,Ky.config({invalidClass:d("invalid"),notify:{onValidate:function(t){br(t,iS,{type:i})},onValid:function(t){br(t,oS,{type:i,value:Df.getValue(t)})},onInvalid:function(t){br(t,rS,{type:i,value:Df.getValue(t)})}},validator:{validate:function(t){var e=Df.getValue(t),n=u(e)?Nn.value(!0):Nn.error(f("aria.input.invalid"));return Gy(n)},validateOnLoad:!1}})),py.config({})]),onSetValue:function(t){Ky.isInvalid(t)&&Ky.run(t).get(st)}})],s="hex"!==e?[my.parts["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}}function h(t,e){var n=e.red,o=e.green,r=e.blue;Df.setValue(t,{red:n,green:o,blue:r})}function v(t,e){b.getOpt(t).each(function(t){fe(t.element,"background-color","#"+e.value)})}var b=Hm({dom:{tag:"div",classes:[d("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return kl({factory:function(){function a(t){return o[t].get()}function c(t,e){o[t].set(e)}function e(t,e){var n=e.event;"hex"!==n.type?c(n.type,vt.none()):g(t)}function n(t,e){var r,n,o,i,u=e.event;"hex"===u.type?function(t,e){m(t);var n=Bv(e);c("hex",vt.some(e));var o=Vv(n);h(t,o),s(o),br(t,Qw,{hex:n}),v(t,n)}(t,u.value):(r=t,n=u.type,o=u.value,i=parseInt(o,10),c(n,vt.some(i)),a("red").bind(function(n){return a("green").bind(function(e){return a("blue").map(function(t){return Rv(n,e,t,1)})})}).each(function(t){var e,n,o=(e=r,n=Iv(t),nS.getField(e,"hex").each(function(t){Vg.isFocused(t)||Df.setValue(e,{hex:n.value})}),n);br(r,Qw,{hex:o}),v(r,o)}))}function t(t){return{label:f(uS+t+".label"),description:f(uS+t+".description")}}function s(t){var e=t.red,n=t.green,o=t.blue;c("red",vt.some(e)),c("green",vt.some(n)),c("blue",vt.some(o))}var o={red:Po(vt.some(255)),green:Po(vt.some(255)),blue:Po(vt.some(255)),hex:Po(vt.some("ffffff"))},r=t("red"),i=t("green"),u=t("blue"),l=t("hex");return Yo(nS.sketch(function(t){return{dom:{tag:"form",classes:[d("rgb-form")],attributes:{"aria-label":f("aria.color.picker")}},components:[t.field("red",my.sketch(p(Nv,"red",r.label,r.description,255))),t.field("green",my.sketch(p(Nv,"green",i.label,i.description,255))),t.field("blue",my.sketch(p(Nv,"blue",u.label,u.description,255))),t.field("hex",my.sketch(p(Mv,"hex",l.label,l.description,"ffffff"))),b.asSpec()],formBehaviours:ec([Ky.config({invalidClass:d("form-invalid")}),mm("rgb-form-events",[Cr(oS,n),Cr(rS,e),Cr(iS,e)])])}}),{apis:{updateHex:function(t,e){var n;Df.setValue(t,{hex:e.value}),h(t,n=Vv(e)),s(n),v(t,e)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,e,n){t.updateHex(e,n)}},extraApis:{}})}function a0(x,w){return kl({name:"ColourPicker",configFields:[mo("dom"),Eo("onValidHex",st),Eo("onInvalidHex",st)],factory:function(t){function e(t,e,n){v.getOpt(t).each(function(t){g.setHue(t,n)})}function n(t,e){b.getOpt(t).each(function(t){m.updateHex(t,e)})}function r(e,n,o,t){var r=o,i=Vv(n);p.paletteRgba.set(i),p.paletteHue.set(r),St(t,function(t){t(e,n,o)})}var o,i,u,a,c,s,l,f,d,m=u0(x,w,t.onValidHex,t.onInvalidHex),g=(l=w,f=$w.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[l("sv-palette-spectrum")]}}),d=$w.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette-thumb")],innerHtml:"<div class="+l("sv-palette-inner-thumb")+' role="presentation"></div>'}}),kl({factory:function(t){var e=rt({x:0,y:0}),n=ec([cd.config({find:vt.some}),Vg.config({})]);return $w.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette")]},model:{mode:"xy",getInitialValue:e},rounded:!1,components:[f,d],onChange:function(t,e,n){br(t,tS,{value:n})},onInit:function(t,e,n,o){y(n.element.dom,zv(fb))},sliderBehaviours:n})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:function(t,e,n){var o=n;y(e.components()[0].element.dom,zv(Pv(jv(o,100,100))))},setThumb:function(t,e,n){var o=e,r=Wv(Vv(n));$w.setValue(o,{x:r.saturation,y:100-r.value})}},extraApis:{}})),p={paletteRgba:Po(fb),paletteHue:Po(0)},h=Hm((i=$w.parts.spectrum({dom:{tag:"div",classes:[(o=w)("hue-slider-spectrum")],attributes:{role:"presentation"}}}),u=$w.parts.thumb({dom:{tag:"div",classes:[o("hue-slider-thumb")],attributes:{role:"presentation"}}}),$w.sketch({dom:{tag:"div",classes:[o("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:rt({y:0})},components:[i,u],sliderBehaviours:ec([Vg.config({})]),onChange:function(t,e,n){br(t,Zw,{value:n})}}))),v=Hm(g.sketch({})),b=Hm(m.sketch({}));function y(t,e){var n,o,r=t.width,i=t.height,u=t.getContext("2d");null!==u&&(u.fillStyle=e,u.fillRect(0,0,r,i),(n=u.createLinearGradient(0,0,r,0)).addColorStop(0,"rgba(255,255,255,1)"),n.addColorStop(1,"rgba(255,255,255,0)"),u.fillStyle=n,u.fillRect(0,0,r,i),(o=u.createLinearGradient(0,0,0,i)).addColorStop(0,"rgba(0,0,0,0)"),o.addColorStop(1,"rgba(0,0,0,1)"),u.fillStyle=o,u.fillRect(0,0,r,i))}return{uid:t.uid,dom:t.dom,components:[v.asSpec(),h.asSpec(),b.asSpec()],behaviours:ec([mm("colour-picker-events",[Cr(Qw,(s=[e,function(t,e,n){h.getOpt(t).each(function(t){$w.setValue(t,{y:100-n/360*100})})},function(t,e){v.getOpt(t).each(function(t){g.setThumb(t,e)})}],function(t,e){var n=e.event.hex;r(t,n,Wv(Vv(n)).hue,s)})),Cr(tS,(c=[n],function(t,e){var n=e.event.value,o=p.paletteHue.get();r(t,Gv(jv(o,n.x,100-n.y)),o,c)})),Cr(Zw,(a=[e,n],function(t,e){var n=(100-e.event.value.y)/100*360,o=Wv(p.paletteRgba.get());r(t,Gv(jv(n,o.saturation,o.value)),n,a)}))]),cd.config({find:function(t){return b.getOpt(t)}}),Fg.config({mode:"acyclic"})])}}})}function c0(t){return sS[t]}function s0(t,e,n){return Df.config(Yo({store:{mode:"manual",getValue:e,setValue:n}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))}function l0(r,i){function e(t,e){e.stop()}function n(t){return function(e,n){St(t,function(t){t(e,n)})}}function o(t,e){var n;gd.isDisabled(t)||(n=e.event.raw,a(t,n.dataTransfer.files))}function u(t,e){var n=e.event.raw.target;a(t,n.files)}function a(t,e){var n,o;Df.setValue(t,(n=e,o=fS.explode(i.getSetting("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string")),F(kt(n),function(e){return d(o,function(t){return At(e.name.toLowerCase(),"."+t.toLowerCase())})}))),br(t,yy,{name:r.name})}var c=Hm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ec([mm("input-file-events",[Dr(Ci()),Dr(Ni())])])});return ny(r.label.map(function(t){return oy(t,i)}),my.parts.field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ec([pS([]),aw(),gd.config({}),zg.config({toggleClass:"dragenter",toggleOnExecute:!1}),mm("dropzone-events",[Cr("dragenter",n([e,zg.toggle])),Cr("dragleave",n([e,zg.toggle])),Cr("dragover",e),Cr("drop",n([e,o])),Cr(ki(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:i.translate("Drop an image here")}},fp.sketch({dom:{tag:"button",innerHtml:i.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element.dom.click()},buttonBehaviours:ec([py.config({}),$v(i.isDisabled),pv()])})]}]}}}}),["tox-form__group--stretched"],[])}function f0(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:ec([Vg.config({ignore:!0}),py.config({})])}}function d0(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[f0([hS]),t,f0([vS])],behaviours:ec([cS(1)])}}function m0(t,e){br(t,xi(),{raw:{which:9,shiftKey:e}})}function g0(t,e){var n=e.element;Kr(n,hS)?m0(t,!0):Kr(n,vS)&&m0(t,!1)}function p0(t){return Yb(t,["."+hS,"."+vS].join(","),T)}function h0(t,e){return xS(document.createElement("canvas"),t,e)}function v0(t){var e=h0(t.width,t.height);return yS(e).drawImage(t,0,0),e}function b0(t){return t.naturalWidth||t.width}function y0(t){return t.naturalHeight||t.height}function x0(t,o,r){return o=o||"image/png",S(HTMLCanvasElement.prototype.toBlob)?new Ly(function(e,n){t.toBlob(function(t){t?e(t):n()},o,r)}):(g=t.toDataURL(o,r),new Ly(function(t,e){!function(){var t=g.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return vt.none();for(var n=e[1],o=t[1],r=atob(o),i=r.length,u=Math.ceil(i/1024),a=new Array(u),c=0;c<u;++c){for(var s=1024*c,l=Math.min(1024+s,i),f=new Array(l-s),d=s,m=0;d<l;++m,++d)f[m]=r[d].charCodeAt(0);a[c]=new Uint8Array(f)}return vt.some(new Blob(a,{type:n}))}().fold(function(){e("uri is not base64: "+g)},t)}));var g}function w0(t,e,n){function o(e,n){return t.then(function(t){return t.toDataURL(e||"image/png",n)})}return{getType:rt(e.type),toBlob:function(){return Ly.resolve(e)},toDataURL:rt(n),toBase64:function(){return n.split(",")[1]},toAdjustedBlob:function(e,n){return t.then(function(t){return x0(t,e,n)})},toAdjustedDataURL:o,toAdjustedBase64:function(t,e){return o(t,e).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(v0)}}}function S0(e,t){return x0(e,t).then(function(t){return w0(Ly.resolve(e),t,e.toDataURL())})}function k0(e){return n=e,new Ly(function(t){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(n)}).then(function(t){return w0((a=e,new Ly(function(t,e){function n(){r.removeEventListener("load",i),r.removeEventListener("error",u)}var o=URL.createObjectURL(a),r=new Image,i=function(){n(),t(r)},u=function(){n(),e("Unable to load data of type "+a.type+": "+o)};r.addEventListener("load",i),r.addEventListener("error",u),r.src=o,r.complete&&setTimeout(i,0)}).then(function(t){wS(t);var e=h0(b0(t),y0(t));return yS(e).drawImage(t,0,0),e})),e,t);var a});var n}function C0(t,e,n){var o="string"==typeof t?parseFloat(t):t;return n<o?o=n:o<e&&(o=e),o}function O0(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function _0(t,e){for(var n=[],o=new Array(25),r=0;r<5;r++){for(var i=0;i<5;i++)n[i]=e[i+5*r];for(i=0;i<5;i++){for(var u=0,a=0;a<5;a++)u+=t[i+5*a]*n[a];o[i+5*r]=u}}return o}function T0(e,n){return e.toCanvas().then(function(t){return kS(t,e.getType(),n)})}function E0(n){return function(t,e){return T0(t,n(O0(),e))}}function D0(t,e){void 0===e&&(e=2);var n=Math.pow(10,e),o=Math.round(t*n);return Math.ceil(o/n)}function A0(t){return CS(t)}function B0(t){return TS(t)}function M0(t,e){return ES(t,e)}function F0(t,e){return OS(t,e)}function I0(t,e){return _S(t,e)}function R0(t,e){return o=e,(n=t).toCanvas().then(function(t){return MS(t,n.getType(),o)});var n,o}function N0(t,e){return o=e,(n=t).toCanvas().then(function(t){return BS(t,n.getType(),o)});var n,o}function P0(t,e,n){return Xm(t,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:n},e)}function V0(t,e){return P0(t,e,[])}function H0(t,e){return P0(t,e,[Rg.config({})])}function L0(t,e,n){return{dom:{tag:"span",innerHtml:n.translate(t),classes:[e+"__select-label"]},behaviours:ec([Rg.config({})])}}function z0(e,n,o){function t(t,e){var n=Df.getValue(t);return Vg.focus(n),br(n,"keydown",{raw:e.event.raw}),hw.close(n),vt.some(!0)}var r=Po(st),i=e.text.map(function(t){return Hm(L0(t,n,o.providers))}),u=e.icon.map(function(t){return Hm(H0(t,o.providers.icons))}),a=e.role.fold(function(){return{}},function(t){return{role:t}}),c=e.tooltip.fold(function(){return{}},function(t){var e=o.providers.translate(t);return{title:e,"aria-label":e}}),s=Xm("chevron-down",{tag:"div",classes:[n+"__select-chevron"]},o.providers.icons);return Hm(hw.sketch(lt(lt(lt({},e.uid?{uid:e.uid}:{}),a),{dom:{tag:"button",classes:[n,n+"--select"].concat(B(e.classes,function(t){return n+"--"+t})),attributes:lt({},c)},components:tb([u.map(function(t){return t.asSpec()}),i.map(function(t){return t.asSpec()}),vt.some(s)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:ec(V(V([],e.dropdownBehaviours,!0),[$v(function(){return e.disabled||o.providers.isDisabled()}),pv(),vw.config({}),Rg.config({}),mm("dropdown-events",[vv(e,r),bv(e,r)]),mm("menubutton-update-display-text",[Cr(NS,function(e,n){i.bind(function(t){return t.getOpt(e)}).each(function(t){Rg.set(t,[ri(o.providers.translate(n.event.text))])})}),Cr(PS,function(e,n){u.bind(function(t){return t.getOpt(e)}).each(function(t){Rg.set(t,[H0(n.event.icon,o.providers.icons)])})})])],!1)),eventOrder:Yo(RS,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:ec([Fg.config({mode:"special",onLeft:t,onRight:t})]),lazySink:o.getSink,toggleClass:n+"--active",parts:{menu:Zp(0,e.columns,e.presets)},fetch:function(t){return Wy(C(e.fetch,t))}}))).asSpec()}function U0(t){return"separator"===t.type}function j0(t,n,o,e){var r=Ir("primary-menu"),i=HS(t,o.shared.providers.menuItems());if(0===i.items.length)return vt.none();var u=Gb(r,i.items,n,o,e),a=dt(i.menus,function(t,e){return Gb(e,t,n,o,!1)}),c=Yo(a,sr(r,u));return vt.from(ip.tieredData(r,c,i.expansions))}function W0(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)},setActive:function(t){var e=n.element;t?(Yr(e,"tox-tbtn--enabled"),oe(e,"aria-pressed",!0)):(qr(e,"tox-tbtn--enabled"),ce(e,"aria-pressed"))},isActive:function(){return Kr(n.element,"tox-tbtn--enabled")}}}function G0(n,t,o,e){return z0({text:n.text,icon:n.icon,tooltip:n.tooltip,role:e,fetch:function(t,e){n.fetch(function(t){e(j0(t,Sh.CLOSE_ON_EXECUTE,o,!1))})},onSetup:n.onSetup,getApi:W0,columns:1,presets:"normal",classes:[],dropdownBehaviours:[py.config({})]},t,o.shared)}function X0(t,e,n,o,r,i){void 0===n&&(n=[]);var u=e.fold(function(){return{}},function(t){return{action:t}}),a=lt({buttonBehaviours:ec([$v(function(){return t.disabled||i.isDisabled()}),pv(),py.config({}),mm("button press",[kr("click"),kr("mousedown")])].concat(n)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Yo(a,{dom:o});return Yo(c,{components:r})}function Y0(t,e,n,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},i=t.icon.map(function(t){return V0(t,n.icons)});return X0(t,e,o,r,tb([i]),n)}function q0(t,e,n,o){void 0===o&&(o=[]);var r=Y0(t,vt.some(e),n,o);return fp.sketch(r)}function K0(t,e,n,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=n.translate(t.text),u=t.icon?t.icon.map(function(t){return V0(t,n.icons)}):vt.none(),a=u.isSome()?tb([u]):[],c=u.isSome()?{}:{innerHtml:i},s=V(V(V(V([],t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],!0),u.isSome()?["tox-button--icon"]:[],!0),t.borderless?["tox-button--naked"]:[],!0),r,!0);return X0(t,e,o,lt(lt({tag:"button",classes:s},c),{attributes:{title:i}}),a,n)}function J0(t,e,n,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=K0(t,vt.some(e),n,o,r);return fp.sketch(i)}function $0(e,n){return function(t){"custom"===n?br(t,Sy,{name:e,value:{}}):"submit"===n?vr(t,ky):"cancel"===n?vr(t,wy):console.error("Unknown button type: ",n)}}function Q0(e,t,n){if("menu"===t){var o=e,r=Hm(G0(lt(lt({},e),{onSetup:function(t){return t.setDisabled(e.disabled),st},fetch:(i=o.items,u=function(){return r},a=n,function(t){t(B(i,function(t){var e,n,o=t.text.fold(function(){return{}},function(t){return{text:t}});return lt(lt({type:t.type,active:!1},o),{onAction:function(t){var e=!t.isActive();t.setActive(e),n.storage.set(e),a.shared.getSink().each(function(t){u().getOpt(t).each(function(t){ka(t.element),br(t,Sy,{name:n.name,value:n.storage.get()})})})},onSetup:(e=n=t,function(t){t.setActive(e.storage.get())})})}))})}),"tox-tbtn",n,vt.none()));return r.asSpec()}var i,u,a;if("custom"===t||"cancel"===t||"submit"===t){var c=$0(e.name,t);return J0(lt(lt({},e),{borderless:!1}),c,n.shared.providers,[])}console.error("Unknown footer button type: ",t)}function Z0(t,e){return Jf({factory:my,name:t,overrides:function(o){return{fieldBehaviours:ec([mm("coupled-input-behaviour",[Cr(Si(),function(n){ml(n,o,e).bind(cd.getCurrent).each(function(e){ml(n,o,"lock").each(function(t){zg.isOn(t)&&o.onLockedChange(n,e,t)})})})])])}}})}function tw(t){var e=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===e)return Nn.error(t);var n=parseFloat(e[1]),o=e[2];return Nn.value({value:n,unit:o})}function ew(t,e){function n(t){return Tt(o,t)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1};return t.unit===e?vt.some(t.value):n(t.unit)&&n(e)?o[t.unit]===o[e]?vt.some(t.value):vt.some(t.value/o[t.unit]*o[e]):vt.none()}function nw(t){return vt.none()}function ow(o,e){function t(t){return Xm(t,{tag:"span",classes:["tox-icon","tox-lock-icon__"+t]},e.icons)}function n(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}}function r(n){return my.parts.field({factory:Dy,inputClasses:["tox-textfield"],inputBehaviours:ec([gd.config({disabled:function(){return o.disabled||e.isDisabled()}}),pv(),py.config({}),mm("size-input-events",[Cr(bi(),function(t,e){br(t,u,{isField1:n})}),Cr(ki(),function(t,e){br(t,yy,{name:o.name})})])]),selectOnFocus:!1})}function i(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:e.translate(t)}}}var l=nw,u=Ir("ratio-event"),a=US.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:e.translate(o.label.getOr("Constrain proportions"))}},components:[t("lock"),t("unlock")],buttonBehaviours:ec([gd.config({disabled:function(){return o.disabled||e.isDisabled()}}),pv(),py.config({})])}),c=US.parts.field1(n([my.parts.label(i("Width")),r(!0)])),s=US.parts.field2(n([my.parts.label(i("Height")),r(!1)]));return US.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([i("&nbsp;"),a])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,e){tw(Df.getValue(t)).each(function(t){l(t).each(function(t){var e,n,o,r;Df.setValue(i,(r=-1!==(r=(e=t).value.toFixed((n=e.unit)in(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4})?o[n]:1)).indexOf(".")?r.replace(/\.?0*$/,""):r)+e.unit)})})},coupledFieldBehaviours:ec([gd.config({disabled:function(){return o.disabled||e.isDisabled()},onDisabled:function(t){US.getField1(t).bind(my.getField).each(gd.disable),US.getField2(t).bind(my.getField).each(gd.disable),US.getLock(t).each(gd.disable)},onEnabled:function(t){US.getField1(t).bind(my.getField).each(gd.enable),US.getField2(t).bind(my.getField).each(gd.enable),US.getLock(t).each(gd.enable)}}),pv(),mm("size-input-events2",[Cr(u,function(t,e){var n,o,r,i=e.event.isField1,u=i?US.getField1(t):US.getField2(t),a=i?US.getField2(t):US.getField1(t),c=u.map(Df.getValue).getOr(""),s=a.map(Df.getValue).getOr("");n=s,o=tw(c).toOptional(),r=tw(n).toOptional(),l=Et(o,r,function(t,o){return ew(t,o.unit).map(function(t){return o.value/t}).map(function(t){return e=t,n=o.unit,function(t){return ew(t,n).map(function(t){return{value:t*e,unit:n}})};var e,n}).getOr(nw)}).getOr(nw)})])])})}function rw(f,c){function t(t,e,n,o){return Hm(J0({name:t,text:t,disabled:n,primary:o,icon:vt.none(),borderless:!1},e,c))}function e(t,e,n,o){return Hm(q0({name:t,icon:vt.some(t),tooltip:vt.some(e),disabled:o,primary:!1,borderless:!1},n,c))}function d(t,n){t.map(function(t){var e=t.get(n);e.hasConfigured(gd)&&gd.disable(e)})}function m(t,n){t.map(function(t){var e=t.get(n);e.hasConfigured(gd)&&gd.enable(e)})}function r(t,e,n){br(t,e,n)}function i(t){return vr(t,YS.disable()),0}function u(t){return vr(t,YS.enable()),0}function g(t,e){i(t),r(t,jS.transform(),{transform:e}),u(t)}function n(t){return function(){q.getOpt(t).each(function(t){Rg.set(t,[Y])})}}function s(t,e){i(t),r(t,jS.transformApply(),{transform:e,swap:n(t)}),u(t)}function p(){return t("Back",function(t){return r(t,jS.back(),{swap:n(t)})},!1,!1)}function o(){return Hm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:ec([gd.config({})])})}function h(){return t("Apply",function(t){return r(t,jS.apply(),{swap:n(t)})},!0,!0)}function v(e,n){return function(t){return e(t,n)}}function a(t,e){var n,o=e;i(n=t),r(n,jS.tempTransform(),{transform:o}),u(n)}function b(t,e,n,o,r){var i=$w.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=$w.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=$w.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Hm($w.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:n,maxX:r,getInitialValue:rt({x:o})},components:[i,u,a],sliderBehaviours:ec([Vg.config({})]),onChoose:e}))}function l(t,e,n,o,r){var i,u,a,c,s,l,f=(i=t,u=e,a=n,c=o,s=r,[p(),(l=u,b(i,function(t,e,n){g(t,v(l,n.x/100))},a,c,s)),h()]);return ly.sketch({dom:w,components:f.map(function(t){return t.asSpec()}),containerBehaviours:ec([mm("image-tools-filter-panel-buttons-events",[Cr(YS.disable(),function(t,e){d(f,t)}),Cr(YS.enable(),function(t,e){m(f,t)})])])})}function y(t){return b(t,function(l,t,e){var n=H.getOpt(l),o=z.getOpt(l),r=L.getOpt(l);n.each(function(s){o.each(function(c){r.each(function(t){var o,r,i,e=Df.getValue(s).x/100,n=Df.getValue(t).x/100,u=Df.getValue(c).x/100,a=(o=e,r=n,i=u,function(t){return T0(t,(e=r,n=i,_0(O0(),[C0(o,0,2),0,0,0,0,0,C0(e,0,2),0,0,0,0,0,C0(n,0,2),0,0,0,0,0,1,0,0,0,0,0,1])));var e,n});g(l,a)})})})},0,100,200)}function x(e,n,o){return function(t){r(t,jS.swap(),{transform:n,swap:function(){q.getOpt(t).each(function(t){Rg.set(t,[e]),o(t)})}})}}var w={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},S=st,k=[p(),o(),t("Apply",function(t){s(t,function(t){var e,n,o,r,i,u,a,c,s,l=f.getRect();return e=l.x,n=l.y,o=l.w,r=l.h,u=e,a=n,c=o,s=r,(i=t).toCanvas().then(function(t){return FS(t,i.getType(),u,a,c,s)})}),f.hideCrop()},!1,!0)],C=ly.sketch({dom:w,components:k.map(function(t){return t.asSpec()}),containerBehaviours:ec([mm("image-tools-crop-buttons-events",[Cr(YS.disable(),function(t,e){d(k,t)}),Cr(YS.enable(),function(t,e){m(k,t)})])])}),O=Hm(ow({name:"size",label:vt.none(),constrain:!0,disabled:!1},c)),_=[p(),o(),O,o(),t("Apply",function(a){O.getOpt(a).each(function(t){var r,i,e=Df.getValue(t),n=parseInt(e.width,10),o=parseInt(e.height,10),u=(r=n,i=o,function(t){return n=r,o=i,(e=t).toCanvas().then(function(t){return DS(t,n,o).then(function(t){return S0(t,e.getType())})});var e,n,o});s(a,u)})},!1,!0)],T=ly.sketch({dom:w,components:_.map(function(t){return t.asSpec()}),containerBehaviours:ec([mm("image-tools-resize-buttons-events",[Cr(YS.disable(),function(t,e){d(_,t)}),Cr(YS.enable(),function(t,e){m(_,t)})])])}),E=v(R0,"h"),D=v(R0,"v"),A=v(N0,-90),B=v(N0,90),M=[p(),o(),e("flip-horizontally","Flip horizontally",function(t){a(t,E)},!1),e("flip-vertically","Flip vertically",function(t){a(t,D)},!1),e("rotate-left","Rotate counterclockwise",function(t){a(t,A)},!1),e("rotate-right","Rotate clockwise",function(t){a(t,B)},!1),o(),h()],F=ly.sketch({dom:w,components:M.map(function(t){return t.asSpec()}),containerBehaviours:ec([mm("image-tools-fliprotate-buttons-events",[Cr(YS.disable(),function(t,e){d(M,t)}),Cr(YS.enable(),function(t,e){m(M,t)})])])}),I=[p(),o(),h()],R=ly.sketch({dom:w,components:I.map(function(t){return t.asSpec()})}),N=l("Brightness",F0,-100,0,100),P=l("Contrast",I0,-100,0,100),V=l("Gamma",M0,-100,0,100),H=y("R"),L=y("G"),z=y("B"),U=[p(),H,L,z,h()],j=ly.sketch({dom:w,components:U.map(function(t){return t.asSpec()})}),W=vt.some(B0),G=vt.some(A0),X=[e("crop","Crop",x(C,vt.none(),function(t){f.showCrop()}),!1),e("resize","Resize",x(T,vt.none(),function(t){O.getOpt(t).each(function(t){var e=f.getMeasurements(),n=e.width,o=e.height;Df.setValue(t,{width:n,height:o})})}),!1),e("orientation","Orientation",x(F,vt.none(),S),!1),e("brightness","Brightness",x(N,vt.none(),S),!1),e("sharpen","Sharpen",x(R,W,S),!1),e("contrast","Contrast",x(P,vt.none(),S),!1),e("color-levels","Color levels",x(j,vt.none(),S),!1),e("gamma","Gamma",x(V,vt.none(),S),!1),e("invert","Invert",x(R,G,S),!1)],Y=ly.sketch({dom:w,components:X.map(function(t){return t.asSpec()})}),q=Hm(ly.sketch({dom:{tag:"div"},components:[Y],containerBehaviours:ec([Rg.config({})])}));return{memContainer:q,getApplyButton:function(t){return q.getOpt(t).map(function(t){var e=t.components()[0];return e.components()[e.components().length-1]})}}}function iw(t){if(k(t.changedTouches))for(var e="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<e.length;n++)t[e[n]]=t.changedTouches[0][e[n]]}(Hy=Vy=Vy||{})[Hy.HighlightFirst=0]="HighlightFirst",Hy[Hy.HighlightNone=1]="HighlightNone";function uw(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:el(o.formBehaviours,[Df.config({store:{mode:"manual",getValue:function(t){return dt(hl(t,o),function(t,o){return t().bind(function(t){var e=cd.getCurrent(t),n=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+Fr(t.element));return e.fold(function(){return Nn.error(n)},Nn.value)}).map(Df.getValue)})},setValue:function(n,t){J(t,function(e,t){ml(n,o,t).each(function(t){cd.getCurrent(t).each(function(t){Df.setValue(t,e)})})})}}})]),apis:{getField:function(t,e){return ml(t,o,e).bind(cd.getCurrent)}}}}function aw(){return cd.config({find:vt.some})}function cw(t){return e=Br,n=Mr,s0(t,function(t){return e(t.element)},function(t,e){return n(t.element,e)});var e,n}var sw,lw,fw,dw,mw=function(e,t){return e.getSystem().getByUid(t.uid+"-"+Qy()).map(function(t){return function(){return Nn.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return Nn.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(e)}})})},gw=rt([mo("dom"),mo("fetch"),Ju("onOpen"),$u("onExecute"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Uc(),Zs("dropdownBehaviours",[zg,$y,Fg,Vg]),mo("toggleClass"),Eo("eventOrder",{}),wo("lazySink"),Eo("matchWidth",!1),Eo("useMinWidth",!1),wo("role")].concat(ux())),pw=rt([$f({schema:[Yu()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),Zy()]),hw=Cl({name:"Dropdown",configFields:gw(),partFields:pw(),factory:function(e,t,n,o){function r(t){bf.getState(t).each(function(t){ip.highlightPrimary(t)})}function i(t,e){return yr(t),vt.some(!0)}var u,a={expand:function(t){zg.isOn(t)||nx(e,h,t,o,st,Vy.HighlightNone).get(st)},open:function(t){zg.isOn(t)||nx(e,h,t,o,st,Vy.HighlightFirst).get(st)},isOpen:zg.isOn,close:function(t){zg.isOn(t)&&nx(e,h,t,o,st,Vy.HighlightFirst).get(st)},repositionMenus:function(t){zg.isOn(t)&&ix(t)}};return{uid:e.uid,dom:e.dom,components:t,behaviours:el(e.dropdownBehaviours,[zg.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(t){return rx(e,t,{onOpen:function(){return zg.on(t)},onClose:function(){return zg.off(t)}})}}}),Fg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(t,e){return hw.isOpen(t)?r($y.getCoupled(t,"sandbox")):hw.open(t),vt.some(!0)},onEscape:function(t,e){return hw.isOpen(t)?(hw.close(t),vt.some(!0)):vt.none()}}),Vg.config({})]),events:Sm(vt.some(function(t){nx(e,h,t,o,r,Vy.HighlightFirst).get(st)})),eventOrder:lt(lt({},e.eventOrder),((u={})[Ii()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:a,domModification:{attributes:lt(lt({"aria-haspopup":"true"},e.role.fold(function(){return{}},function(t){return{role:t}})),"button"===e.dom.tag?{type:tt(e.dom,"attributes").bind(function(t){return tt(t,"type")}).getOr("button")}:{})}}},apis:{open:function(t,e){return t.open(e)},expand:function(t,e){return t.expand(e)},close:function(t,e){return t.close(e)},isOpen:function(t,e){return t.isOpen(e)},repositionMenus:function(t,e){return t.repositionMenus(e)}}}),vw=xa({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:function(){return nu([Sr(Ei(),D)])},exhibit:function(){return zr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),bw=Ir("color-input-change"),yw=Ir("color-swatch-change"),xw=Ir("color-picker-cancel"),ww=Qf({schema:[mo("dom")],name:"label"}),Sw=ax("top-left"),kw=ax("top"),Cw=ax("top-right"),Ow=ax("right"),_w=ax("bottom-right"),Tw=ax("bottom"),Ew=ax("bottom-left"),Dw=[ww,ax("left"),Ow,kw,Tw,Sw,Cw,Ew,_w,Jf({name:"thumb",defaults:rt({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:nu([Tr(si(),t,"spectrum"),Tr(li(),t,"spectrum"),Tr(fi(),t,"spectrum"),Tr(mi(),t,"spectrum"),Tr(gi(),t,"spectrum"),Tr(hi(),t,"spectrum")])}}}),Jf({schema:[cr("mouseIsDown",function(){return Po(!1)})],name:"spectrum",overrides:function(n){function o(e,t){return r.getValueFromEvent(t).map(function(t){return r.setValueFrom(e,n,t)})}var r=n.model.manager;return{behaviours:ec([Fg.config({mode:"special",onLeft:function(t){return r.onLeft(t,n)},onRight:function(t){return r.onRight(t,n)},onUp:function(t){return r.onUp(t,n)},onDown:function(t){return r.onDown(t,n)}}),Vg.config({})]),events:nu([Cr(si(),o),Cr(li(),o),Cr(mi(),o),Cr(gi(),function(t,e){n.mouseIsDown.get()&&o(t,e)})])}}})],Aw=rt("slider.change.value"),Bw="left",Mw=Jx(-1),Fw=Jx(1),Iw=vt.none,Rw=vt.none,Nw={"top-left":vt.none(),top:vt.none(),"top-right":vt.none(),right:vt.some(function(t,e){Fx(t,{x:px(e)})}),"bottom-right":vt.none(),bottom:vt.none(),"bottom-left":vt.none(),left:vt.some(function(t,e){Fx(t,{x:fx(e)})})},Pw=Object.freeze({__proto__:null,setValueFrom:function(t,e,n){var o=Kx(t,e,n);return qx(t,{x:o}),o},setToMin:function(t,e){qx(t,{x:sx(e)})},setToMax:function(t,e){qx(t,{x:mx(e)})},findValueOfOffset:Kx,getValueFromEvent:function(t){return cx(t).map(function(t){return t.left})},findPositionOfValue:$x,setPositionFromValue:function(t,e,n,o){var r=Bx(n),i=$x(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),n),u=Ce(e.element)/2;fe(e.element,"left",i-u+"px")},onLeft:Mw,onRight:Fw,onUp:Iw,onDown:Rw,edgeActions:Nw}),Vw=vt.none,Hw=vt.none,Lw=t0(-1),zw=t0(1),Uw={"top-left":vt.none(),top:vt.some(function(t,e){Fx(t,{y:dx(e)})}),"top-right":vt.none(),right:vt.none(),"bottom-right":vt.none(),bottom:vt.some(function(t,e){Fx(t,{y:hx(e)})}),"bottom-left":vt.none(),left:vt.none()},jw=Object.freeze({__proto__:null,setValueFrom:function(t,e,n){var o=Zx(t,e,n);return Qx(t,{y:o}),o},setToMin:function(t,e){Qx(t,{y:lx(e)})},setToMax:function(t,e){Qx(t,{y:gx(e)})},findValueOfOffset:Zx,getValueFromEvent:function(t){return cx(t).map(function(t){return t.top})},findPositionOfValue:e0,setPositionFromValue:function(t,e,n,o){var r=Bx(n),i=e0(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),n),u=xe(e.element)/2;fe(e.element,"top",i-u+"px")},onLeft:Vw,onRight:Hw,onUp:Lw,onDown:zw,edgeActions:Uw}),Ww=cx,Gw=r0(-1,!1),Xw=r0(1,!1),Yw=r0(-1,!0),qw=r0(1,!0),Kw={"top-left":vt.some(function(t,e){Fx(t,Mx(fx(e),dx(e)))}),top:vt.some(function(t,e){Fx(t,Mx(xx(e),dx(e)))}),"top-right":vt.some(function(t,e){Fx(t,Mx(px(e),dx(e)))}),right:vt.some(function(t,e){Fx(t,Mx(px(e),wx(e)))}),"bottom-right":vt.some(function(t,e){Fx(t,Mx(px(e),hx(e)))}),bottom:vt.some(function(t,e){Fx(t,Mx(xx(e),hx(e)))}),"bottom-left":vt.some(function(t,e){Fx(t,Mx(fx(e),hx(e)))}),left:vt.some(function(t,e){Fx(t,Mx(fx(e),wx(e)))})},Jw=Object.freeze({__proto__:null,setValueFrom:function(t,e,n){var o=o0(Kx(t,e,n.left),Zx(t,e,n.top));return n0(t,o),o},setToMin:function(t,e){n0(t,o0(sx(e),lx(e)))},setToMax:function(t,e){n0(t,o0(mx(e),gx(e)))},getValueFromEvent:Ww,setPositionFromValue:function(t,e,n,o){var r=Bx(n),i=$x(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),n),u=e0(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),n),a=Ce(e.element)/2,c=xe(e.element)/2;fe(e.element,"left",i-a+"px"),fe(e.element,"top",u-c+"px")},onLeft:Gw,onRight:Xw,onUp:Yw,onDown:qw,edgeActions:Kw}),$w=Cl({name:"Slider",configFields:[Eo("stepSize",1),Eo("onChange",st),Eo("onChoose",st),Eo("onInit",st),Eo("onDragStart",st),Eo("onDragEnd",st),Eo("snapToGrid",!1),Eo("rounded",!0),wo("snapStart"),go("model",lo("mode",{x:[Eo("minX",0),Eo("maxX",100),cr("value",function(t){return Po(t.mode.minX)}),mo("getInitialValue"),ta("manager",Pw)],y:[Eo("minY",0),Eo("maxY",100),cr("value",function(t){return Po(t.mode.minY)}),mo("getInitialValue"),ta("manager",jw)],xy:[Eo("minX",0),Eo("maxX",100),Eo("minY",0),Eo("maxY",100),cr("value",function(t){return Po({x:t.mode.minX,y:t.mode.minY})}),mo("getInitialValue"),ta("manager",Jw)]})),Zs("sliderBehaviours",[Fg,Df]),cr("mouseIsDown",function(){return Po(!1)})],partFields:Dw,factory:function(i,t,e,n){function u(t){return gl(t,i,"thumb")}function a(t){return gl(t,i,"spectrum")}function o(t){return ml(t,i,"left-edge")}function r(t){return ml(t,i,"right-edge")}function c(t){return ml(t,i,"top-edge")}function s(t){return ml(t,i,"bottom-edge")}function l(t,e){v.setPositionFromValue(t,e,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function f(t,e){h.value.set(e),l(t,u(t))}function d(n){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&ml(n,i,"thumb").each(function(t){var e=h.value.get();i.onChoose(n,t,e)})}function m(t,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))}function g(t,e){e.stop(),i.onDragEnd(t,u(t)),d(t)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:t,behaviours:el(i.sliderBehaviours,[Fg.config({mode:"special",focusIn:function(t){return ml(t,i,"spectrum").map(Fg.focusIn).map(D)}}),Df.config({store:{mode:"manual",getValue:function(t){return h.value.get()}}}),ic.config({channels:((p={})[wf()]={onReceive:d},p)})]),events:nu([Cr(Aw(),function(t,e){!function(t,e){f(t,e);var n=u(t);i.onChange(t,n,e),vt.some(!0)}(t,e.event.value)}),ou(function(t,e){var n=h.getInitialValue();h.value.set(n);var o=u(t);l(t,o);var r=a(t);i.onInit(t,o,r,h.value.get())}),Cr(si(),m),Cr(fi(),g),Cr(mi(),m),Cr(hi(),g)]),apis:{resetToMin:function(t){v.setToMin(t,i)},resetToMax:function(t){v.setToMax(t,i)},setValue:f,refresh:l},domModification:{styles:{position:"relative"}}}},apis:{setValue:function(t,e,n){t.setValue(e,n)},resetToMin:function(t,e){t.resetToMin(e)},resetToMax:function(t,e){t.resetToMax(e)},refresh:function(t,e){t.refresh(e)}}}),Qw=Ir("rgb-hex-update"),Zw=Ir("slider-update"),tS=Ir("palette-update"),eS=[Zs("formBehaviours",[Df])],nS={getField:Hr(function(t,e,n){return t.getField(e,n)}),sketch:function(t){var n,e={field:function(t,e){return n.push(t),cl("form",i0(t),e)},record:rt(n=[])},o=t(e),r=B(e.record(),function(t){return Jf({name:t,pname:i0(t)})});return Sl("form",eS,r,uw,o)}},oS=Ir("valid-input"),rS=Ir("invalid-input"),iS=Ir("validating-input"),uS="colorcustom.rgb.",aS=function(t){return cd.config({find:t.getOpt})},cS=function(t){return cd.config({find:function(e){return Jt(e.element,t).bind(function(t){return e.getSystem().getByDom(t).toOptional()})}})},sS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},lS=tinymce.util.Tools.resolve("tinymce.Resource"),fS=tinymce.util.Tools.resolve("tinymce.util.Tools"),dS=$o([Eo("preprocess",h),Eo("postprocess",h)]),mS=function(r,t){var i=so("RepresentingConfigs.memento processors",dS,t);return Df.config({store:{mode:"manual",getValue:function(t){var e=r.get(t),n=Df.getValue(e);return i.postprocess(n)},setValue:function(t,e){var n=i.preprocess(e),o=r.get(t);Df.setValue(o,n)}}})},gS=s0,pS=function(t){return Df.config({store:{mode:"memory",initialValue:t}})},hS=Ir("alloy-fake-before-tabstop"),vS=Ir("alloy-fake-after-tabstop"),bS=!(ln().browser.isIE()||ln().browser.isEdge()),yS=function(t){return t.getContext("2d")},xS=function(t,e,n){return t.width=e,t.height=n,t},wS=function(t){URL.revokeObjectURL(t.src)},SS=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],kS=function(t,e,T){var n=yS(t),o=function(t){for(var e,n,o,r,i=t.data,u=T[0],a=T[1],c=T[2],s=T[3],l=T[4],f=T[5],d=T[6],m=T[7],g=T[8],p=T[9],h=T[10],v=T[11],b=T[12],y=T[13],x=T[14],w=T[15],S=T[16],k=T[17],C=T[18],O=T[19],_=0;_<i.length;_+=4)e=i[_],n=i[_+1],o=i[_+2],r=i[_+3],i[_]=e*u+n*a+o*c+r*s+l,i[_+1]=e*f+n*d+o*m+r*g+p,i[_+2]=e*h+n*v+o*b+r*y+x,i[_+3]=e*w+n*S+o*k+r*C+O;return t}(n.getImageData(0,0,t.width,t.height));return n.putImageData(o,0,0),S0(t,e)},CS=(sw=[-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1],function(t){return T0(t,sw)}),OS=E0(function(t,e){return _0(t,[1,0,0,0,e=C0(255*e,-255,255),0,1,0,0,e,0,0,1,0,e,0,0,0,1,0,0,0,0,0,1])}),_S=E0(function(t,e){var n;return e=C0(e,-1,1),_0(t,[(n=(e*=100)<0?127+e/100*127:127*(0==(n=e%1)?SS[e]:SS[Math.floor(e)]*(1-n)+SS[Math.floor(e)+1]*n)+127)/127,0,0,0,.5*(127-n),0,n/127,0,0,.5*(127-n),0,0,n/127,0,.5*(127-n),0,0,0,1,0,0,0,0,0,1])}),TS=(lw=[0,-1,0,-1,5,-1,0,-1,0],function(t){return a=lw,(u=t).toCanvas().then(function(t){return e=t,n=u.getType(),o=a,r=yS(e),i=function(t,e,n){for(var o=function(t,e,n){return n<t?t=n:t<e&&(t=e),t},r=Math.round(Math.sqrt(n.length)),i=Math.floor(r/2),u=t.data,a=e.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=n[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return e}(r.getImageData(0,0,e.width,e.height),r.getImageData(0,0,e.width,e.height),o),r.putImageData(i,0,0),S0(e,n);var e,n,o,r,i});var u,a}),ES=(fw=function(t,e){return 255*Math.pow(t/255,1-e)},function(e,n){return e.toCanvas().then(function(t){return function(t,e,n){for(var o=yS(t),r=new Array(256),i=0;i<r.length;i++)r[i]=fw(i,n);var u=function(t,e){for(var n=t.data,o=0;o<n.length;o+=4)n[o]=e[n[o]],n[o+1]=e[n[o+1]],n[o+2]=e[n[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),S0(t,e)}(t,e.getType(),n)})}),DS=function(t,e,n){var o=b0(t),r=y0(t),i=e/o,u=n/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=AS(t,i,u);return a?c.then(function(t){return DS(t,e,n)}):c},AS=function(u,a,c){return new Ly(function(t){var e=b0(u),n=y0(u),o=Math.floor(e*a),r=Math.floor(n*c),i=h0(o,r);yS(i).drawImage(u,0,0,e,n,0,0,o,r),t(i)})},BS=function(t,e,n){var o=(n<0?360+n:n)*Math.PI/180,r=t.width,i=t.height,u=Math.sin(o),a=Math.cos(o),c=D0(Math.abs(r*a)+Math.abs(i*u)),s=D0(Math.abs(r*u)+Math.abs(i*a)),l=h0(c,s),f=yS(l);return f.translate(c/2,s/2),f.rotate(o),f.drawImage(t,-r/2,-i/2),S0(l,e)},MS=function(t,e,n){var o=h0(t.width,t.height),r=yS(o);return"v"===n?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),S0(o,e)},FS=function(t,e,n,o,r,i){var u=h0(r,i);return yS(u).drawImage(t,-n,-o),S0(u,e)},IS=Ir("toolbar.button.execute"),RS=((dw={})[Ii()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],dw),NS=Ir("update-menu-text"),PS=Ir("update-menu-icon"),VS={type:"separator"},HS=function(t,l){var e,n,o;return I((e=y(t)?t.split(" "):t,n=l,0<(o=R(e,function(t,e){return y(e)?""===e?t:"|"===e?0<t.length&&!U0(t[t.length-1])?t.concat([VS]):t:Tt(n,e.toLowerCase())?t.concat([n[e.toLowerCase()]]):t:t.concat([e])},[])).length&&U0(o[o.length-1])&&o.pop(),o),function(t,e){var n,o,r,i,u,a,c=function(t){if(U0(t))return t;var e=tt(t,"value").getOrThunk(function(){return Ir("generated-menu-item")});return Yo({value:e},t)}(e),s=(o=l,Tt(n=c,"getSubmenuItems")?(i=o,u=(r=n).getSubmenuItems(),a=HS(u,i),{item:r,menus:Yo(a.menus,sr(r.value,a.items)),expansions:Yo(a.expansions,sr(r.value,r.value))}):{item:n,menus:{},expansions:{}});return{menus:Yo(t.menus,s.menus),items:[s.item].concat(t.items),expansions:Yo(t.expansions,s.expansions)}},{menus:{},expansions:{},items:[]})},LS=rt([Eo("field1Name","field1"),Eo("field2Name","field2"),Qu("onLockedChange"),qu(["lockClass"]),Eo("locked",!1),Af("coupledFieldBehaviours",[cd,Df])]),zS=rt([Z0("field1","field2"),Z0("field2","field1"),Jf({factory:fp,schema:[mo("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:ec([zg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),US=Cl({name:"FormCoupledInputs",configFields:LS(),partFields:zS(),factory:function(o,t,e,n){return{uid:o.uid,dom:o.dom,components:t,behaviours:Bf(o.coupledFieldBehaviours,[cd.config({find:vt.some}),Df.config({store:{mode:"manual",getValue:function(t){var e=bl(t,o,["field1","field2"]),n={};return n[o.field1Name]=Df.getValue(e.field1()),n[o.field2Name]=Df.getValue(e.field2()),n},setValue:function(t,e){var n=bl(t,o,["field1","field2"]);et(e,o.field1Name)&&Df.setValue(n.field1(),e[o.field1Name]),et(e,o.field2Name)&&Df.setValue(n.field2(),e[o.field2Name])}}})]),apis:{getField1:function(t){return ml(t,o,"field1")},getField2:function(t){return ml(t,o,"field2")},getLock:function(t){return ml(t,o,"lock")}}}},apis:{getField1:function(t,e){return t.getField1(e)},getField2:function(t,e){return t.getField2(e)},getLock:function(t,e){return t.getLock(e)}}}),jS={undo:rt(Ir("undo")),redo:rt(Ir("redo")),zoom:rt(Ir("zoom")),back:rt(Ir("back")),apply:rt(Ir("apply")),swap:rt(Ir("swap")),transform:rt(Ir("transform")),tempTransform:rt(Ir("temp-transform")),transformApply:rt(Ir("transform-apply"))},WS=rt("save-state"),GS=rt("disable"),XS=rt("enable"),YS={formActionEvent:Sy,saveState:WS,disable:GS,enable:XS},qS=tinymce.util.Tools.resolve("tinymce.geom.Rect"),KS=tinymce.util.Tools.resolve("tinymce.util.Observable"),JS=tinymce.util.Tools.resolve("tinymce.util.VK");function $S(t,d){function e(t){var e,n,o,r,i,u,a,c,s=t.raw,l=(e=Math.max,n=y.documentElement,o=y.body,r=e(n.scrollWidth,o.scrollWidth),i=e(n.clientWidth,o.clientWidth),u=e(n.offsetWidth,o.offsetWidth),a=e(n.scrollHeight,o.scrollHeight),c=e(n.clientHeight,o.clientHeight),{width:r<u?i:r,height:a<e(n.offsetHeight,o.offsetHeight)?c:a});iw(s),t.prevent(),p=s.button,h=s.screenX,v=s.screenY;var f=ge(w,"cursor");de(g=Mt.fromTag("div",y),{position:"absolute",top:"0",left:"0",width:l.width+"px",height:l.height+"px","z-index":"2147483647",opacity:"0.0001",cursor:f}),Tn(yn(x),g),b.push(pc(x,"mousemove",m),pc(x,"touchmove",m),pc(x,"mouseup",S),pc(x,"touchend",S)),d.start(s)}function m(t){var e=t.raw;if(iw(e),e.button!==p)return S(t);e.deltaX=e.screenX-h,e.deltaY=e.screenY-v,t.prevent(),d.drag(e)}var n,o,r,g,p,h,v,i=[],b=[],y=null!==(n=d.document)&&void 0!==n?n:document,u=null!==(o=d.root)&&void 0!==o?o:y,x=Mt.fromDom(y),w=Mt.fromDom(u.getElementById(null!==(r=d.handle)&&void 0!==r?r:t)),S=function(t){iw(t.raw),St(b,function(t){return t.unbind()}),b=[],En(g),d.stop&&d.stop(t.raw)};return i.push(pc(w,"mousedown",e),pc(w,"touchstart",e)),{destroy:function(){St(b.concat(i),function(t){return t.unbind()}),b=[],i=[],k(g)&&En(g)}}}function QS(t,e,n,o,r){return q0({name:t,icon:vt.some(e),disabled:n,tooltip:vt.some(t),primary:!1,borderless:!1},o,r)}function ZS(t,e){e?gd.enable(t):gd.disable(t)}var tk=0,ek=function(s,e,l,t,n){function f(t,e){return{x:e.x-t.x,y:e.y-t.y,w:e.w,h:e.h}}function u(t,e,n,o){var r=e.x+n*t.deltaX,i=e.y+o*t.deltaY,u=Math.max(20,e.w+n*t.deltaW),a=Math.max(20,e.h+o*t.deltaH),c=(s=qS.clamp({x:r,y:i,w:u,h:a},l,"move"===t.name),f(l,s));b.fire("updateRect",{rect:c}),h(c)}function o(t){r(s=t)}function r(n){function t(t,e){Iu(m,"#"+d+"-"+t).each(function(t){de(t,{left:e.x+"px",top:e.y+"px",width:Math.max(0,e.w)+"px",height:Math.max(0,e.h)+"px"})})}St(g,function(e){Iu(m,"#"+d+"-"+e.name).each(function(t){de(t,{left:n.w*e.xMul+n.x+"px",top:n.h*e.yMul+n.y+"px"})})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}var i,a,c=[],d="tox-crid-"+tk++,m=Mt.fromDom(t),g=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],p=["top","right","bottom","left"],h=function(t){var e;o((e=l,{x:t.x+e.x,y:t.y+e.y,w:t.w,h:t.h}))};function v(t){oe(t.target,"aria-grabbed","focus"===t.raw.type?"true":"false")}re(a=Mt.fromTag("div"),{id:d,class:"tox-croprect-container",role:"grid","aria-dropeffect":"execute"}),Tn(m,a),St(p,function(n){Iu(m,"#"+d).each(function(t){var e=Mt.fromTag("div");re(e,{id:d+"-"+n,class:"tox-croprect-block","data-mce-bogus":"all"}),fe(e,"display","none"),Tn(t,e)})}),St(g,function(n){Iu(m,"#"+d).each(function(t){var e=Mt.fromTag("div");re(e,{id:d+"-"+n.name,"aria-label":n.label,"aria-grabbed":"false","data-mce-bogus":"all",role:"gridcell",tabindex:"-1",title:n.label}),Jr(e,["tox-croprect-handle","tox-croprect-handle-"+n.name]),fe(e,"display","none"),Tn(t,e)})}),i=B(g,function(e){var n;return $S(d,{document:t.ownerDocument,root:gn(m).dom,handle:d+"-"+e.name,start:function(){n=s},drag:function(t){u(e,n,t.deltaX,t.deltaY)}})}),r(s),c.push(pc(m,"focusin",v),pc(m,"focusout",v),pc(m,"keydown",function(e){var i;function t(t,e,n,o,r){t.stopPropagation(),t.preventDefault(),u(i,n,o,r)}switch(St(g,function(t){if(ie(e.target,"id")===d+"-"+t.name)return i=t,!1}),e.raw.keyCode){case JS.LEFT:t(e,0,s,-10,0);break;case JS.RIGHT:t(e,0,s,10,0);break;case JS.UP:t(e,0,s,0,-10);break;case JS.DOWN:t(e,0,s,0,10);break;case JS.ENTER:case JS.SPACEBAR:e.prevent(),n()}}));var b=lt(lt({},KS),{toggleVisibility:function(t){var e=V(V([],B(g,function(t){return"#"+d+"-"+t.name}),!0),B(p,function(t){return"#"+d+"-"+t}),!0).join(","),n=vs(m,e);St(n,t?function(t){return ve(t,"display")}:function(t){return fe(t,"display","none")})},setClampRect:function(t){l=t,r(s)},setRect:o,getInnerRect:function(){return f(l,s)},setInnerRect:h,setViewPortRect:function(t){e=t,r(s)},destroy:function(){St(i,function(t){return t.destroy()}),i=[],St(c,function(t){return t.unbind()}),c=[]}});return b};function nk(t){var n,o,e,r,i=Po(t),u=gc(),a=(o=-1,{data:n=[],add:function(t){var e=n.splice(++o);return n.push(t),{state:t,removed:e}},undo:function(){if(e())return n[--o]},redo:function(){if(r())return n[++o]},canUndo:e=function(){return 0<o},canRedo:r=function(){return-1!==o&&o<n.length-1}});function c(t){i.set(t)}function s(t){URL.revokeObjectURL(t.url)}function l(t){var e=f(t);c(e);var n=a.add(e).removed;return fS.each(n,s),e.url}a.add(t);function f(t){return{blob:t,url:URL.createObjectURL(t)}}function d(){u.on(s),u.clear()}return{getBlobState:function(){return i.get()},setBlobState:c,addBlobState:l,getTempState:function(){return u.get().getOrThunk(i.get)},updateTempState:function(t){var e=f(t);return d(),u.set(e),e.url},addTempState:function(t){var e=f(t);return u.set(e),e.url},applyTempState:function(e){return u.get().fold(st,function(t){l(t.blob),e()})},destroyTempState:d,undo:function(){var t=a.undo();return c(t),t.url},redo:function(){var t=a.redo();return c(t),t.url},getHistoryStates:function(){return{undoEnabled:a.canUndo(),redoEnabled:a.canRedo()}}}}function ok(t,e){function i(t){var e=y.getHistoryStates();k.updateButtonUndoStates(t,e.undoEnabled,e.redoEnabled),br(t,YS.formActionEvent,{name:YS.saveState(),value:e.undoEnabled})}function u(t){return t.toBlob()}function a(t){br(t,YS.formActionEvent,{name:YS.disable(),value:{}})}function c(e,t,n,o,r){a(e),k0(t).then(n).then(u).then(o).then(function(t){return w(e,t)}).then(function(){i(e),r(),x(e)}).catch(function(t){console.log(t),e.getSystem().isConnected()&&x(e)})}function r(t,e,n){c(t,y.getBlobState().blob,e,function(t){return y.updateTempState(t)},n)}function s(t){var e=y.getBlobState().url;return y.destroyTempState(),i(t),e}var n,o,l,f,d,m,g,p,h,v,b,y=nk(t.currentState),x=function(t){C.getApplyButton(t).each(function(t){gd.enable(t)}),br(t,YS.formActionEvent,{name:YS.enable(),value:{}})},w=function(t,e){return a(t),S.updateSrc(t,e)},S=(f=t.currentState.url,m=Hm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),g=Po(1),d=dc(),p=lt(lt({},d),{run:function(t){return d.get().each(t)}}),h=Po({x:0,y:0,w:1,h:1}),v=Po({x:0,y:0,w:1,h:1}),{memContainer:b=Hm(ly.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[m.asSpec(),{dom:{tag:"img",attributes:{src:f}}},{dom:{tag:"div"},behaviours:ec([mm("image-panel-crop-events",[ou(function(t){b.getOpt(t).each(function(t){var e=t.element.dom,n=ek({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},e,st);n.toggleVisibility(!1),n.on("updateRect",function(t){var e=t.rect,n=g.get(),o={x:Math.round(e.x/n),y:Math.round(e.y/n),w:Math.round(e.w/n),h:Math.round(e.h/n)};h.set(o)}),p.set(n)})}),ru(function(){p.clear()})])])}],containerBehaviours:ec([Rg.config({}),mm("image-panel-events",[ou(function(t){_(t,f)})])])})),updateSrc:_,zoom:function(t,e){var n=g.get(),o=0<e?Math.min(2,n+.1):Math.max(.1,n-.1);g.set(o),b.getOpt(t).each(function(t){var e=t.components()[1].element;O(t,e)})},showCrop:function(){p.run(function(t){t.toggleVisibility(!0)})},hideCrop:function(){p.run(function(t){t.toggleVisibility(!1)})},getRect:function(){return h.get()},getMeasurements:function(){var t=v.get();return{width:t.w,height:t.h}}}),k=(o=Hm(QS("Undo","undo",!0,function(t){br(t,jS.undo(),{direction:1})},n=e)),l=Hm(QS("Redo","redo",!0,function(t){br(t,jS.redo(),{direction:1})},n)),{container:ly.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),l.asSpec(),QS("Zoom in","zoom-in",!1,function(t){br(t,jS.zoom(),{direction:1})},n),QS("Zoom out","zoom-out",!1,function(t){br(t,jS.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(t,e,n){o.getOpt(t).each(function(t){ZS(t,e)}),l.getOpt(t).each(function(t){ZS(t,n)})}}),C=rw(S,e);function O(t,s){b.getOpt(t).each(function(t){var n=g.get(),o=Ce(t.element),r=xe(t.element),i=s.dom.naturalWidth*n,u=s.dom.naturalHeight*n,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),e={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};de(s,e),m.getOpt(t).each(function(t){de(t.element,e)}),p.run(function(t){var e=h.get();t.setRect({x:e.x*n+a,y:e.y*n+c,w:e.w*n,h:e.h*n}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})}function _(t,e){var n,i=Mt.fromTag("img");return oe(i,"src",e),n=i.dom,new Mp(function(t){var e=function(){n.removeEventListener("load",e),t(n)};n.complete?t(n):n.addEventListener("load",e)}).then(function(){t.getSystem().isConnected()&&b.getOpt(t).map(function(t){var e=Ou({element:i});Rg.replaceAt(t,1,vt.some(e));var n=v.get(),o={x:0,y:0,w:i.dom.naturalWidth,h:i.dom.naturalHeight};v.set(o);var u,r=qS.inflate(o,-20,-20);h.set(r),n.w===o.w&&n.h===o.h||(u=i,b.getOpt(t).each(function(t){var e=Ce(t.element),n=xe(t.element),o=u.dom.naturalWidth,r=u.dom.naturalHeight,i=Math.min(e/o,n/r);1<=i?g.set(1):g.set(i)})),O(t,i)})})}return{dom:{tag:"div",attributes:{role:"presentation"}},components:[C.memContainer.asSpec(),S.memContainer.asSpec(),k.container],behaviours:ec([Df.config({store:{mode:"manual",getValue:function(){return y.getBlobState()}}}),mm("image-tools-events",[Cr(jS.undo(),function(e,t){var n=y.undo();w(e,n).then(function(t){x(e),i(e)})}),Cr(jS.redo(),function(e,t){var n=y.redo();w(e,n).then(function(t){x(e),i(e)})}),Cr(jS.zoom(),function(t,e){var n=e.event.direction;S.zoom(t,n)}),Cr(jS.back(),function(t,e){var n,o=s(n=t);w(n,o).then(function(t){x(n)}),(0,e.event.swap)(),S.hideCrop()}),Cr(jS.apply(),function(t,e){y.applyTempState(function(){s(t),(0,e.event.swap)()})}),Cr(jS.transform(),function(t,e){return r(t,e.event.transform,st)}),Cr(jS.tempTransform(),function(t,e){var n=e.event.transform;c(t,y.getTempState().blob,n,function(t){return y.addTempState(t)},st)}),Cr(jS.transformApply(),function(t,e){var n=t,o=e.event.transform,r=e.event.swap,i=y.getBlobState().blob;c(n,i,o,function(t){var e=y.addBlobState(t);return s(n),e},r)}),Cr(jS.swap(),function(e,t){k.updateButtonUndoStates(e,!1,!1);var n=t.event.transform,o=t.event.swap;n.fold(function(){o()},function(t){r(e,t,o)})})]),aw()])}}function rk(t){return!Tt(t,"items")}function ik(t,e){function n(t){return{dom:{tag:"td",innerHtml:e.translate(t)}}}return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:B(t.header,function(t){return{dom:{tag:"th",innerHtml:e.translate(t)}}})}]},{dom:{tag:"tbody"},components:B(t.cells,function(t){return{dom:{tag:"tr"},components:B(t,n)}})}],behaviours:ec([py.config({}),Vg.config({})])}}function uk(n,e){var t=n.label.map(function(t){return oy(t,e)}),o=[gd.config({disabled:function(){return n.disabled||e.isDisabled()}}),pv(),Fg.config({mode:"execution",useEnter:!0!==n.multiline,useControlEnter:!0===n.multiline,execute:function(t){return vr(t,ky),vt.some(!0)}}),mm("textfield-change",[Cr(Si(),function(t,e){br(t,yy,{name:n.name})}),Cr(Mi(),function(t,e){br(t,yy,{name:n.name})})]),py.config({})],r=n.validation.map(function(o){return Ky.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-invalid",validator:{validate:function(t){var e=Df.getValue(t),n=o.validator(e);return Gy(!0===n?Nn.value(e):Nn.error(n))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=n.placeholder.fold(rt({}),function(t){return{placeholder:e.translate(t)}}),u=n.inputMode.fold(rt({}),function(t){return{inputmode:t}}),a=lt(lt({},i),u);return ny(t,my.parts.field({tag:!0===n.multiline?"textarea":"input",inputAttributes:a,inputClasses:[n.classname],inputBehaviours:ec(ft([o,r])),selectOnFocus:!1,factory:Dy}),(n.flex?["tox-form__group--stretched"]:[]).concat(n.maximized?["tox-form-group--maximize"]:[]),[gd.config({disabled:function(){return n.disabled||e.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])}function ak(t){var e=Po(null);return wu({readState:function(){return{timer:null!==e.get()?"set":"unset"}},setTimer:function(t){e.set(t)},cancel:function(){var t=e.get();null!==t&&t.cancel()}})}function ck(t,e,n){var o=Df.getValue(n);Df.setValue(e,o),s1(e)}function sk(t,e){var n=t.element,o=Qr(n),r=n.dom;"number"!==ie(n,"type")&&e(r,o)}function lk(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:st}}function fk(t,e){return{type:"menuitem",value:e,text:t,meta:{attach:void 0},onAction:st}}function dk(t,e){return n=t,B(F(e,function(t){return t.type===n}),lk);var n}function mk(t,e){var n=t.toLowerCase();return F(e,function(t){return ut((void 0!==t.meta&&void 0!==t.meta.text?t.meta:t).text.toLowerCase(),n)||ut(t.value.toLowerCase(),n)})}function gk(u,a,c){function r(t){var e=Df.getValue(t);c.addToHistory(e.value,u.filetype)}var t,e,n,o,i=a.shared.providers,s=my.parts.field({factory:m1,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":v1,type:"url"},minChars:0,responseTime:0,fetch:function(t){var n,o,e,r,i=j0((n=u.filetype,o=c,e=Df.getValue(t),r=void 0!==e.meta.text?e.meta.text:e.value,o.getLinkInformation().fold(function(){return[]},function(t){var e=mk(r,B(o.getHistory(n),function(t){return fk(t,t)}));return"file"===n?R([e,mk(r,dk("header",t.targets)),mk(r,ft([vt.from(t.anchorTop).map(function(t){return fk("<top>",t)}).toArray(),dk("anchor",t.targets),vt.from(t.anchorBottom).map(function(t){return fk("<bottom>",t)}).toArray()]))],function(t,e){return 0===t.length||0===e.length?t.concat(e):t.concat(h1,e)},[]):e})),Sh.BUBBLE_TO_SANDBOX,a,!1);return Gy(i)},getHotspot:function(t){return p.getOpt(t)},onSetValue:function(t,e){t.hasConfigured(Ky)&&Ky.run(t).get(st)},typeaheadBehaviours:ec(ft([c.getValidationHandler().map(function(n){return Ky.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,e){f.getOpt(t).each(function(t){oe(t.element,"title",i.translate(e))})}},validator:{validate:function(t){var e=Df.getValue(t);return p1(function(o){n({type:u.filetype,url:e.value},function(t){var e,n;"invalid"===t.status?(e=Nn.error(t.message),o(e)):(n=Nn.value(t.message),o(n))})})},validateOnLoad:!1}})}).toArray(),[gd.config({disabled:function(){return u.disabled||i.isDisabled()}}),py.config({}),mm("urlinput-events",ft(["file"===u.filetype?[Cr(Si(),function(t){br(t,yy,{name:u.name})})]:[],[Cr(ki(),function(t){br(t,yy,{name:u.name}),r(t)}),Cr(Mi(),function(t){br(t,yy,{name:u.name}),r(t)})]]))]])),eventOrder:((t={})[Si()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:a.shared.getSink,parts:{menu:Zp(0,0,"normal")},onExecute:function(t,e,n){br(e,ky,{})},onItemExecute:function(t,e,n,o){r(t),br(t,yy,{name:u.name})}}),l=u.label.map(function(t){return oy(t,i)}),f=Hm((e="invalid",n=vt.some(v1),Xm("warning",{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:lt({title:i.translate(o=void 0===o?e:o),"aria-live":"polite"},n.fold(function(){return{}},function(t){return{id:t}}))},i.icons))),d=Hm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[f.asSpec()]}),m=c.getUrlPicker(u.filetype),g=Ir("browser.url.event"),p=Hm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[s,d.asSpec()],behaviours:ec([gd.config({disabled:function(){return u.disabled||i.isDisabled()}})])}),h=Hm(J0({name:u.name,icon:vt.some("browse"),text:u.label.getOr(""),disabled:u.disabled,primary:!1,borderless:!0},function(t){return vr(t,g)},i,[],["tox-browse-url"]));return my.sketch({dom:by([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:ft([[p.asSpec()],m.map(function(){return h.asSpec()}).toArray()])}]),fieldBehaviours:ec([gd.config({disabled:function(){return u.disabled||i.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable),h.getOpt(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable),h.getOpt(t).each(gd.enable)}}),pv(),mm("url-input-events",[Cr(g,function(o){cd.getCurrent(o).each(function(e){var t=Df.getValue(e),n=lt({fieldname:u.name},t);m.each(function(t){t(n).get(function(t){Df.setValue(e,t),br(o,yy,{name:u.name})})})})})])])})}function pk(r){return function(e,n,o){return tt(n,"name").fold(function(){return r(n,o)},function(t){return e.field(t,r(n,o))})}}function hk(e,t,n){var o=Yo(n,{shared:{interpreter:function(t){return x1(e,t,o)}}});return x1(e,t,o)}function vk(t,e,n){function o(){return Mt.fromDom(t.getContentAreaContainer())}function r(){return p||!n()}var i,u,a,c,s,l,f,d,m,g,p=sv(t);return{inlineDialog:(f=o,d=e,m=r,g={maxHeightFunction:Nc()},function(){return m()?{type:"node",root:te(f()),node:vt.from(f()),bubble:Sc(12,12,w1),layouts:{onRtl:function(){return[Mm]},onLtr:function(){return[Bm]}},overrides:g}:{type:"hotspot",hotspot:d(),bubble:Sc(-12,12,w1),layouts:{onRtl:function(){return[Xa]},onLtr:function(){return[Ya]}},overrides:g}}),banner:(c=o,s=e,l=r,function(){return l()?{type:"node",root:te(c()),node:vt.from(c()),layouts:{onRtl:function(){return[cp]},onLtr:function(){return[cp]}}}:{type:"hotspot",hotspot:s(),layouts:{onRtl:function(){return[$a]},onLtr:function(){return[$a]}}}}),cursor:(u=t,function(){return{type:"selection",root:a(),getSelection:function(){var t=u.selection.getRng();return vt.some(ms.range(Mt.fromDom(t.startContainer),t.startOffset,Mt.fromDom(t.endContainer),t.endOffset))}}}),node:(i=a=function(){return Mt.fromDom(t.getBody())},function(t){return{type:"node",root:i(),node:t}})}}function bk(i){return vt.from(i.getParam("style_formats")).filter(c).map(function(t){var e,n,o=(e=i,n=k1(t),e.formatter?r(n.customFormats):e.on("init",function(){r(n.customFormats)}),n.formats);function r(t){St(t,function(t){e.formatter.has(t.name)||e.formatter.register(t.name,t.format)})}return i.getParam("style_formats_merge",!1,"boolean")?S1.concat(o):o}).getOr(S1)}function yk(t,e,n){var o={type:"formatter",isSelected:e(t.format),getStylePreview:n(t.format)};return Yo(t,o)}function xk(c,t,s,l){var f=function(t){return B(t,function(t){var e,n,o,r,i,u=Ct(t);if(et(t,"items")){var a=f(t.items);return Yo(Yo(t,{type:"submenu"}),{getStyleItems:rt(a)})}return et(t,"format")?yk(t,s,l):1===u.length&&wt(u,"title")?Yo(t,{type:"separator"}):(r={type:"formatter",format:o="custom-"+(n=y((e=t).name)?e.name:Ir(e.title)),isSelected:s(o),getStylePreview:l(o)},i=Yo(e,r),c.formatter.register(n,i),i)})};return f(t)}function wk(n){return function(t){if(k(e=t)&&1===e.nodeType){if(t.contentEditable===n)return!0;if(t.getAttribute("data-mce-contenteditable")===n)return!0}var e;return!1}}function Sk(t,e,n,o,r){return{type:t,title:e,url:n,level:o,attach:r}}function kk(t){return t.innerText||t.textContent}function Ck(t){return t&&"A"===t.nodeName&&void 0!==(t.id||t.name)&&JC(t)}function Ok(t){return t&&/^(H[1-6])$/.test(t.nodeName)}function _k(t){return Ok(t)&&JC(t)}function Tk(t){var e,n=t.id||Ir("h");return Sk("header",kk(t),"#"+n,Ok(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=n})}function Ek(t){var e=t.id||t.name;return Sk("anchor",kk(t)||"#"+e,"#"+e,0,st)}function Dk(t){return 0<C1(t.title).length}function Ak(t){return y(t)&&/^https?/.test(t)}function Bk(t){return x(t)&&Q(t,function(t){return!(c(e=t)&&e.length<=5&&L(e,Ak));var e}).isNone()}function Mk(){var t,e=db.getItem(E1);if(null===e)return{};try{t=JSON.parse(e)}catch(t){if(t instanceof SyntaxError)return console.log("Local storage "+E1+" was not valid JSON",t),{};throw t}return Bk(t)?t:(console.log("Local storage "+E1+" was not valid format",t),{})}function Fk(t){return tt(Mk(),t).getOr([])}function Ik(e,t){var n,o;Ak(e)&&(o=F(tt(n=Mk(),t).getOr([]),function(t){return t!==e}),n[t]=[e].concat(o).slice(0,5),function(t){if(!Bk(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));db.setItem(E1,JSON.stringify(t))}(n))}function Rk(t){return!!t}function Nk(t){return dt(fS.makeMap(t,/[, ]/),Rk)}function Pk(t){return vt.from(t.getParam("file_picker_callback")).filter(S)}function Vk(t){return vt.from(t).filter(y).getOrUndefined()}function Hk(l){return{getHistory:Fk,addToHistory:Ik,getLinkInformation:function(){return!1===(t=l).getParam("typeahead_urls")?vt.none():vt.some({targets:T1(t.getBody()),anchorTop:Vk(t.getParam("anchor_top","#top")),anchorBottom:Vk(t.getParam("anchor_bottom","#bottom"))});var t},getValidationHandler:function(){return vt.from(void 0===(e=(t=l).getParam("file_picker_validator_handler",void 0,"function"))?t.getParam("filepicker_validator_handler",void 0,"function"):e);var t,e},getUrlPicker:function(t){return e=c=l,n=s=t,r=vt.some((o=e).getParam("file_picker_types")).filter(Rk),i=vt.some(o.getParam("file_browser_callback_types")).filter(Rk),u=r.or(i).map(Nk),a=Pk(o).fold(T,function(t){return u.fold(D,function(t){return 0<Ct(t).length&&t})}),(w(a)?a?Pk(e):vt.none():a[n]?Pk(e):vt.none()).map(function(o){return function(e){return Wy(function(n){var t=lt({filetype:s,fieldname:e.fieldname},vt.from(e.meta).getOr({}));o.call(c,function(t,e){if(!y(t))throw new Error("Expected value to be string");if(void 0!==e&&!x(e))throw new Error("Expected meta to be a object");n({value:t,meta:e})},e.value,t)})}});var e,n,o,r,i,u,a,c,s}}}function Lk(t,e,n){var o,r,i,u,a,c,s,l,f,d,m,g,p,h=Po(!1),v={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=Po(av(e)?"bottom":"top")).get,setDockingMode:o.set},b={shared:{providers:{icons:function(){return e.ui.registry.getAll().icons},menuItems:function(){return e.ui.registry.getAll().menuItems},translate:dp.translate,isDisabled:function(){return e.mode.isReadOnly()||e.ui.isDisabled()},getSetting:e.getParam.bind(e)},interpreter:function(t){return x1(y1,t,b)},anchors:vk(e,n,v.isPositionedAtTop),header:v,getSink:function(){return Nn.value(t)}},urlinput:Hk(e),styleselect:(f=Po([]),d=Po([]),m=Po([]),g=Po([]),p=Po(!(l=function(t){var e=t.items;return void 0!==e&&0<e.length?H(e,l):[t.format]})),(s=e).on("PreInit",function(t){var e=bk(s),n=xk(s,e,y,x);f.set(n),d.set(H(n,l))}),s.on("addStyleModifications",function(t){var e=xk(s,t.items,y,x);m.set(e),p.set(t.replace),g.set(H(e,l))}),{getData:function(){var t=p.get()?[]:f.get(),e=m.get();return t.concat(e)},getFlattenedKeys:function(){var t=p.get()?[]:d.get(),e=g.get();return t.concat(e)}}),colorinput:{colorPicker:function(t,e){Ib(c)(t,e)},hasCustomColors:function(){return gb(a)},getColors:function(){return pb(u)},getColorCols:(i=u=a=c=e,function(){return vb(i)})},dialog:{isDraggableModal:(r=e,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return h.get()},setContextMenuState:function(t){return h.set(t)}};function y(t){return function(){return s.formatter.match(t)}}function x(e){return function(){var t=s.formatter.get(e);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:s.dom.parseStyle(s.formatter.getCssText(e))}):vt.none()}}return b}function zk(t){return(mt(pe(t,"position"),"fixed")?vt.none():qt(t)).orThunk(function(){var n=Mt.fromTag("span");return Yt(t).bind(function(t){Tn(t,n);var e=qt(n);return En(n),e})})}function Uk(t){return zk(t).map(ke).getOrThunk(function(){return kn(0,0)})}function jk(t,e){var n=t.element;Yr(n,e.transitionClass),qr(n,e.fadeOutClass),Yr(n,e.fadeInClass),e.onShow(t)}function Wk(t,e){var n=t.element;Yr(n,e.transitionClass),qr(n,e.fadeInClass),Yr(n,e.fadeOutClass),e.onHide(t)}function Gk(t,e,n){return L(t,function(t){switch(t){case"bottom":return e.bottom<=n.bottom;case"top":return e.y>=n.y}})}function Xk(e,t){return t.getInitialPos().map(function(t){return Bn(t.bounds.x,t.bounds.y,Ce(e),xe(e))})}function Yk(n,o,r){return r.getInitialPos().bind(function(t){switch(r.clearInitialPos(),t.position){case"static":return vt.some(H1.static());case"absolute":var e=zk(n).map(Mn).getOrThunk(function(){return Mn(bn())});return vt.some(H1.absolute(Ea("absolute",tt(t.style,"left").map(function(t){return o.x-e.x}),tt(t.style,"top").map(function(t){return o.y-e.y}),tt(t.style,"right").map(function(t){return e.right-o.right}),tt(t.style,"bottom").map(function(t){return e.bottom-o.bottom}))));default:return vt.none()}})}function qk(t,e,n){var o,r,i,u=t.element;return mt(pe(u,"position"),"fixed")?(r=e,Xk(o=u,i=n).filter(function(t){return Gk(i.getModes(),t,r)}).bind(function(t){return Yk(o,t,i)})):function(t,e,n){var r,o,i=Mn(t);if(Gk(n.getModes(),i,e))return vt.none();r=t,o=i,n.setInitialPos({style:function(){var t={},e=r.dom;if(ct(e))for(var n=0;n<e.style.length;n++){var o=e.style.item(n);t[o]=e.style[o]}return t}(),position:ge(r,"position")||"static",bounds:o});var u=Fn(),a=i.x-u.x,c=e.y-u.y,s=u.bottom-e.bottom,l=i.y<=e.y;return vt.some(H1.fixed(Ea("fixed",vt.some(a),l?vt.some(c):vt.none(),vt.none(),l?vt.none():vt.some(s))))}(u,e,n)}function Kk(e,t,n){n.setDocked(!1),St(["left","right","top","bottom","position"],function(t){return ve(e.element,t)}),t.onUndocked(e)}function Jk(t,e,n,o){var r="fixed"===o.position;n.setDocked(r),Da(t.element,o),(r?e.onDocked:e.onUndocked)(t)}function $k(o,t,r,i,u){void 0===u&&(u=!1),t.contextual.each(function(n){n.lazyContext(o).each(function(t){var e=t.y<i.bottom&&t.bottom>i.y;e!==r.isVisible()&&(r.setVisible(e),u&&!e?(Jr(o.element,[n.fadeOutClass]),n.onHide(o)):(e?jk:Wk)(o,n))})})}function Qk(t,e,n){var o,r,i,u,a,c;n.isDocked()&&(r=e,i=n,c=(o=t).element,i.setDocked(!1),Xk(a=o.element,u=i).bind(function(t){return Yk(a,t,u)}).each(function(t){t.fold(function(){return Kk(o,r,i)},function(t){return Jk(o,r,i,t)},st)}),i.setVisible(!0),r.contextual.each(function(t){$r(c,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(o)}),L1(o,r,i))}function Zk(t,e){return wt(j1.getModes(t),e)}function tC(r){var i=r.element;Yt(i).each(function(t){var e,n,o="padding-"+j1.getModes(r)[0];j1.isDocked(r)?(e=Ce(t),fe(i,"width",e+"px"),fe(t,o,we(n=i)+(parseInt(ge(n,"margin-top"),10)||0)+(parseInt(ge(n,"margin-bottom"),10)||0)+"px")):(ve(i,"width"),ve(t,o))})}function eC(t,e){e?(qr(t,G1.fadeOutClass),Jr(t,[G1.transitionClass,G1.fadeInClass])):(qr(t,G1.fadeInClass),Jr(t,[G1.fadeOutClass,G1.transitionClass]))}function nC(t,e){var n=Mt.fromDom(t.getContainer());e?(Yr(n,X1),qr(n,Y1)):(Yr(n,Y1),qr(n,X1))}function oC(u,t){function o(e){r().each(function(t){return e(t.element)})}function e(t){u.inline||tC(t),nC(u,j1.isDocked(t)),t.getSystem().broadcastOn([xf()],{}),r().each(function(t){return t.getSystem().broadcastOn([xf()],{})})}var n,i=gc(),r=t.getSink,a=u.inline?[]:[ic.config({channels:((n={})[W1()]={onReceive:tC},n)})];return V([Vg.config({}),j1.config({contextual:lt({lazyContext:function(t){var e=we(t.element),n=u.inline?u.getContentAreaContainer():u.getContainer(),o=Mn(Mt.fromDom(n)),r=o.height-e,i=o.y+(Zk(t,"top")?0:e);return vt.some(Bn(o.x,i,o.width,r))},onShow:function(){o(function(t){return eC(t,!0)})},onShown:function(r){o(function(t){return $r(t,[G1.transitionClass,G1.fadeInClass])}),i.get().each(function(t){var e,n=r.element,o=jt(e=t);Oa(o).filter(function(t){return!zt(e,t)}).filter(function(t){return zt(t,Mt.fromDom(o.dom.body))||Ut(n,t)}).each(function(){return ka(e)}),i.clear()})},onHide:function(t){var e=t.element,n=r;_a(e).orThunk(function(){return n().toOptional().bind(function(t){return _a(t.element)})}).fold(i.clear,i.set),o(function(t){return eC(t,!1)})},onHidden:function(){o(function(t){return $r(t,[G1.transitionClass])})}},G1),lazyViewport:function(t){var e=Fn(),n=u.getParam("toolbar_sticky_offset",0,"number"),o=e.y+(Zk(t,"top")?n:0),r=e.height-(Zk(t,"bottom")?n:0);return Bn(e.x,o,e.width,r)},modes:[t.header.getDockingMode()],onDocked:e,onUndocked:e})],a,!0)}function rC(t){return ao("menubutton",$1,t)}function iC(e,t){return t.getAnimationRoot.fold(function(){return e.element},function(t){return t(e)})}function uC(t){return t.dimension.property}function aC(t,e){return t.dimension.getDimension(e)}function cC(t,e){$r(iC(t,e),[e.shrinkingClass,e.growingClass])}function sC(t,e){qr(t.element,e.openClass),Yr(t.element,e.closedClass),fe(t.element,uC(e),"0px"),be(t.element)}function lC(t,e){qr(t.element,e.closedClass),Yr(t.element,e.openClass),ve(t.element,uC(e))}function fC(t,e,n,o){n.setCollapsed(),fe(t.element,uC(e),aC(e,t.element)),be(t.element),cC(t,e),sC(t,e),e.onStartShrink(t),e.onShrunk(t)}function dC(t,e,n){var o=aC(e,t.element);("0px"===o?fC:function(t,e,n,o){var r=o.getOrThunk(function(){return aC(e,t.element)});n.setCollapsed(),fe(t.element,uC(e),r),be(t.element);var i=iC(t,e);qr(i,e.growingClass),Yr(i,e.shrinkingClass),sC(t,e),e.onStartShrink(t)})(t,e,n,vt.some(o))}function mC(t,e,n){var o=iC(t,e),r=Kr(o,e.shrinkingClass),i=aC(e,t.element);lC(t,e);var u=aC(e,t.element);(r?function(){fe(t.element,uC(e),i),be(t.element)}:function(){sC(t,e)})(),qr(o,e.shrinkingClass),Yr(o,e.growingClass),lC(t,e),fe(t.element,uC(e),u),n.setExpanded(),e.onStartGrow(t)}function gC(t,e,n){return!0===Kr(iC(t,e),e.growingClass)}function pC(t,e,n){return!0===Kr(iC(t,e),e.shrinkingClass)}function hC(t){return"<alloy.field."+t+">"}function vC(t){return{element:function(){return t.element.dom}}}function bC(t,n){cd.getCurrent(t).each(function(t){return Rg.set(t,[(e=n,uO.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:(n=t,r=B(Ct(o=e),function(t){var e=o[t],n=co(ao("sidebar",aO,e));return{name:t,getApi:vC,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}),B(r,function(t){var e=Po(st);return n.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Kh([vv(t,e),bv(t,e),Cr(Ji(),function(e,t){var n=t.event;N(r,function(t){return t.name===n.name}).each(function(t){(n.visible?t.onShow:t.onHide)(t.getApi(e))})})])})})),slotBehaviours:Kh([ou(function(t){return uO.hideAllSlots(t)})])};var n,o,r}))]);var e})}function yC(t){return cd.getCurrent(t).bind(function(t){return nO.isGrowing(t)||nO.hasGrown(t)?cd.getCurrent(t).bind(function(e){return N(uO.getSlotNames(e),function(t){return uO.isShowing(e,t)})}):vt.none()})}function xC(t){var e=Mt.fromHtml(t),n=Kt(e),o=R(void 0!==e.dom.attributes?e.dom.attributes:[],function(t,e){var n;return"class"===e.name?t:lt(lt({},t),((n={})[e.name]=e.value,n))},{}),r=Array.prototype.slice.call(e.dom.classList,0),i=0===n.length?{}:{innerHtml:Br(e)};return lt({tag:Ft(e),classes:r,attributes:o},i)}function wC(t){return cd.getCurrent(t).each(function(t){return ka(t.element)})}function SC(f,d,m){function e(t){var e;!g.get()||"focusin"===(e=t).type&&(e.composed?Y(e.composedPath()):vt.from(e.target)).map(Mt.fromDom).filter(Ge).exists(function(t){return Kr(t,"mce-pastebin")})||(t.preventDefault(),wC(d()),f.editorManager.setActive(f))}var g=Po(!1),n=gc();function o(t){var e,n,o,r,i,u,a,c,s,l;t!==g.get()&&(g.set(t),e=f,n=d(),o=t,r=m.providers,c=n.element,s=o,l="data-mce-"+(i="tabindex"),vt.from(e.iframeElement).map(Mt.fromDom).each(function(e){s?(ue(e,i).each(function(t){return oe(e,l,t)}),oe(e,i,-1)):(ce(e,i),ue(e,l).each(function(t){oe(e,i,t),ce(e,l)}))}),o?(fO.block(n,(a=r,function(t,e){return{dom:{tag:"div",attributes:{"aria-label":a.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:xC('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})),ve(c,"display"),ce(c,"aria-hidden"),e.hasFocus()&&wC(n)):(u=cd.getCurrent(n).exists(function(t){return Ca(t.element)}),fO.unblock(n),fe(c,"display","none"),oe(c,"aria-hidden","true"),u&&e.focus()),f.fire("AfterProgressState",{state:t}))}f.inline||f.on("PreInit",function(){f.dom.bind(f.getWin(),"focusin",e),f.on("BeforeExecCommand",function(t){"mcefocus"===t.command.toLowerCase()&&!0!==t.value&&e(t)})}),f.on("ProgressState",function(t){var e;n.on(lp.clearTimeout),u(t.time)?(e=lp.setEditorTimeout(f,function(){return o(t.state)},t.time),n.set(e)):(o(t.state),n.clear())})}function kC(t,e,n){return{within:t,extra:e,withinWidth:n}}function CC(t,e,o){var n,r=(n=function(t,e){var n=o(t);return vt.some({element:t,start:e,finish:e+n,width:n})},R(t,function(e,t){return n(t,e.len).fold(rt(e),function(t){return{len:t.finish,list:e.list.concat([t])}})},{len:0,list:[]}).list),i=F(r,function(t){return t.finish<=e}),u=I(i,function(t,e){return t+e.width},0);return{within:i,extra:r.slice(i.length),withinWidth:u}}function OC(t){return B(t,function(t){return t.element})}function _C(t,e){var n=B(e,function(t){return Eu(t)});N1.setGroups(t,n)}function TC(t,e,n){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k=e.builtGroups.get();0!==k.length&&(o=gl(t,e,"primary"),r=$y.getCoupled(t,"overflowGroup"),fe(o.element,"visibility","hidden"),u=K(i=k.concat([r]),function(e){return _a(e.element).bind(function(t){return e.getSystem().getByDom(t).toOptional()})}),n([]),_C(o,i),a=Ce(o.element),0===(s=a,l=e.builtGroups.get(),d=r,y=(0===(m=CC(l,s,f=function(t){return Ce(t.element)})).extra.length?vt.some(m):vt.none()).getOrThunk(function(){return CC(l,s-f(d),f)}),x=y.within,w=y.extra,S=y.withinWidth,(c=1===w.length&&w[0].width<=f(d)?(b=S,kC(OC(x.concat(w)),[],b)):1<=w.length?(p=w,h=d,v=S,kC(OC(x).concat([h]),OC(p),v)):(g=S,kC(OC(x),[],g))).extra.length)?(Rg.remove(o,r),n([])):(_C(o,c.within),n(c.extra)),ve(o.element,"visibility"),be(o.element),u.each(Vg.focus))}function EC(t,e){var n=$y.getCoupled(t,"toolbarSandbox");bf.isOpen(n)?bf.close(n):bf.open(n,e.toolbar())}function DC(t,e,n,o){var r=n.getBounds.map(function(t){return t()}),i=n.lazySink(t).getOrDie();ff.positionWithinBounds(i,e,{anchor:{type:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:pO()}}},r)}function AC(t,e,n,o,r){N1.setGroups(e,r),DC(t,e,n,o),zg.on(t)}function BC(t){return B(t,function(t){return Eu(t)})}function MC(t,n,o){TC(t,o,function(e){o.overflowGroups.set(e),n.getOpt(t).each(function(t){bO.setGroups(t,BC(e))})})}function FC(e,n){ml(e,n,"overflow-button").bind(function(){return ml(e,n,"overflow")}).each(function(t){QC(e,n),nO.toggleGrow(t)})}function IC(t){var e=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:lt({tag:"div",classes:["tox-toolbar__group"]},e),components:[wO.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:ec([py.config({}),Vg.config({})])}}function RC(t){return wO.sketch(IC(t))}function NC(n,t){var e=ou(function(t){var e=B(n.initGroups,RC);N1.setGroups(t,e)});return ec([Qv(n.providers.isDisabled),pv(),Fg.config({mode:t,onEscape:n.onEscape,selector:".tox-toolbar__group"}),mm("toolbar-events",[e])])}function PC(t){var e=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":IC({title:vt.none(),items:[]}),"overflow-button":Y0({name:"more",icon:vt.some("more-drawer"),disabled:!1,tooltip:vt.some("More..."),primary:!1,borderless:!1},vt.none(),t.providers)},splitToolbarBehaviours:NC(t,e)}}function VC(t){var e=t.cyclicKeying?"cyclic":"acyclic";return N1.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===bh.scrolling?["tox-toolbar--scrolling"]:[])},components:[N1.parts.groups({})],toolbarBehaviours:NC(t,e)})}function HC(t){return"string"==typeof t?t.split(" "):t}function LC(i,u){var a=lt(lt({},IO),u.menus),n=0<Ct(u.menus).length,t=F(void 0===u.menubar||!0===u.menubar?HC("file edit view insert format tools table help"):HC(!1===u.menubar?"":u.menubar),function(t){var e=Tt(IO,t);return n?e||tt(u.menus,t).exists(function(t){return Tt(t,"items")}):e});return F(B(t,function(t){var e=a[t],n={title:e.title,items:HC(e.items)},o=u,r=i.getParam("removed_menuitems","").split(/[ ,]/);return{text:n.title,getItems:function(){return H(n.items,function(t){var e=t.toLowerCase();return 0===e.trim().length||d(r,function(t){return t===e})?[]:"separator"===e||"|"===e?[{type:"separator"}]:o.menuItems[e]?[o.menuItems[e]]:[]})}}}),function(t){return 0<t.getItems().length&&d(t.getItems(),function(t){return"separator"!==t.type})})}function zC(t){function e(){t._skinLoaded=!0,t.fire("SkinLoaded")}return function(){t.initialized?e():t.on("init",e)}}function UC(n,o,r){return new Mp(function(t,e){r.load(o,t,e),n.on("remove",function(){return r.unload(o)})})}function jC(t,e){var n,o,r,i,u,a,c,s,l,f=(r=(n=e).getParam("skin"),i=n.getParam("skin_url"),!1!==r&&(o=r||"oxide",i=i?n.documentBaseURI.toAbsolute(i):ov.baseURL+"/skins/ui/"+o),i);f&&e.contentCSS.push(f+(t?"/content.inline":"/content")+".min.css"),!1===e.getParam("skin")==0&&y(f)?Mp.all([UC(e,f+"/skin.min.css",e.ui.styleSheetLoader),(c=e,s=f,l=Mt.fromDom(c.getElement()),pn(l).isSome()?UC(c,s+"/skin.shadowdom.min.css",nv.DOM.styleSheetLoader):Mp.resolve())]).then(zC(e),(u=e,a="Skin could not be loaded",function(){return u.fire("SkinLoadError",{message:a})})):zC(e)()}function WC(o,r){return function(e){function t(){e.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,e.setActive);n.set(t)}var n=mc();return o.initialized?t():o.once("init",t),function(){o.off("init",t),n.clear()}}}function GC(o,r,i){return function(t){function e(){return i(t)}function n(){i(t),o.on(r,e)}return o.initialized?n():o.once("init",n),function(){o.off("init",n),o.off(r,e)}}}function XC(e){return function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("mceToggleFormat",!1,t.format)})}}}function YC(t,e){return function(){return t.execCommand(e)}}function qC(t,e,n){var u,a,c,o=n.dataset,r="basic"===o.type?function(){return B(o.data,function(t){return yk(t,n.isSelectedFor,n.getPreviewFor)})}:o.getData;return{items:(u=e,a=n,c=function(t,e,n){var o="formatter"===t.type&&a.isInvalid(t);return 0===e?o?[]:i(t,e,!1,n).toArray():i(t,e,o,n).toArray()},{validateItems:s,getFetch:function(n,o){return function(t,e){e(j0(s(o()),Sh.CLOSE_ON_EXECUTE,n,!1))}}}),getStyleItems:r};function i(t,e,n,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return vt.some({type:"separator",text:r});if("submenu"!==t.type)return vt.some(lt({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:n,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=H(t.getStyleItems(),function(t){return c(t,e,o)});return 0===e&&i.length<=0?vt.none():vt.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return H(t.getStyleItems(),function(t){return c(t,e,o)})}})}function s(t){var e=a.getCurrentValue(),n=a.shouldHide?0:1;return H(t,function(t){return c(t,n,e)})}}function KC(t,e,n){var o=qC(0,e,n),r=o.items,i=o.getStyleItems,u=GC(t,"NodeChange",function(t){var e=t.getComponent();n.updateText(e)});return z0({text:n.icon.isSome()?vt.none():n.text,icon:n.icon,tooltip:vt.from(n.tooltip),role:vt.none(),fetch:r.getFetch(e,i),onSetup:u,getApi:function(t){return{getComponent:rt(t)}},columns:1,presets:"normal",classes:n.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",e.shared)}function JC(t){return function(t){for(;t=t.parentNode;){var e=t.contentEditable;if(e&&"inherit"!==e)return O1(t)}return!1}(t)&&!_1(t)}function $C(r,t){function e(t){return vl(r)}function n(n,o){return function(t,e){return ml(t,r,e).map(function(t){return n(t,e)}).getOr(o)}}function o(t,e){return"true"!==ie(t.element,"aria-hidden")}var i,u=n(o,!1),a=n(function(t,e){var n;o(t)&&(fe(n=t.element,"display","none"),oe(n,"aria-hidden","true"),br(t,Ji(),{name:e,visible:!1}))}),c=(i=a,function(e,t){St(t,function(t){return i(e,t)})}),s=n(function(t,e){var n;o(t)||(ve(n=t.element,"display"),ce(n,"aria-hidden"),br(t,Ji(),{name:e,visible:!0}))});return{uid:r.uid,dom:r.dom,components:t,behaviours:tl(r.slotBehaviours),apis:{getSlotNames:e,getSlot:function(t,e){return ml(t,r,e)},isShowing:u,hideSlot:a,hideAllSlots:function(t){return c(t,e())},showSlot:s}}}function QC(t,e){ml(t,e,"overflow").each(function(n){TC(t,e,function(t){var e=B(t,function(t){return Eu(t)});N1.setGroups(n,e)}),ml(t,e,"overflow-button").each(function(t){nO.hasGrown(n)&&zg.on(t)}),nO.refresh(n)})}var ZC,t1,e1,n1="data-value",o1=function(e,n,t,o){return B(t,function(t){return rk(t)?{type:"togglemenuitem",text:t.text,value:t.value,active:t.value===o,onAction:function(){Df.setValue(e,t.value),br(e,yy,{name:n}),Vg.focus(e)}}:{type:"nestedmenuitem",text:t.text,getSubmenuItems:function(){return o1(e,n,t.items,o)}}})},r1=function(t,e){return K(t,function(t){return rk(t)?ot(t.value===e,t):r1(t.items,e)})},i1=kl({name:"HtmlSelect",configFields:[mo("options"),Zs("selectBehaviours",[Vg,Df]),Eo("selectClasses",[]),Eo("selectAttributes",{}),wo("data")],factory:function(n,t){var e=B(n.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=n.data.map(function(t){return sr("initialValue",t)}).getOr({});return{uid:n.uid,dom:{tag:"select",classes:n.selectClasses,attributes:n.selectAttributes},components:e,behaviours:el(n.selectBehaviours,[Vg.config({}),Df.config({store:lt({mode:"manual",getValue:function(t){return Qr(t.element)},setValue:function(t,e){N(n.options,function(t){return t.value===e}).isSome()&&Zr(t.element,e)}},o)})])}}}),u1=Object.freeze({__proto__:null,events:function(t,e){var n=t.stream.streams.setup(t,e);return nu([Cr(t.event,n),ru(function(){return e.cancel()})].concat(t.cancelEvent.map(function(t){return[Cr(t,function(){return e.cancel()})]}).getOr([])))}}),a1=Object.freeze({__proto__:null,throttle:ak,init:function(t){return t.stream.streams.state(t)}}),c1=xa({fields:[go("stream",lo("mode",{throttle:[mo("delay"),Eo("stopEvent",!0),ta("streams",{setup:function(t,e){var n=t.stream,o=vp(t.onStream,n.delay);return e.setTimer(o),function(t,e){o.throttle(t,e),n.stopEvent&&e.stop()}},state:ak})]})),Eo("event","input"),wo("cancelEvent"),Qu("onStream")],name:"streaming",active:u1,state:a1}),s1=function(t){sk(t,function(t,e){return t.setSelectionRange(e.length,e.length)})},l1=rt("alloy.typeahead.itemexecute"),f1=rt([wo("lazySink"),mo("fetch"),Eo("minChars",5),Eo("responseTime",1e3),Ju("onOpen"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Eo("layouts",vt.none()),Eo("eventOrder",{}),No("model",{},[Eo("getDisplayText",function(t){return void 0!==t.meta&&void 0!==t.meta.text?t.meta.text:t.value}),Eo("selectsOver",!0),Eo("populateFromBrowse",!0)]),Ju("onSetValue"),$u("onExecute"),Ju("onItemExecute"),Eo("inputClasses",[]),Eo("inputAttributes",{}),Eo("inputStyles",{}),Eo("matchWidth",!0),Eo("useMinWidth",!1),Eo("dismissOnBlur",!0),qu(["openClass"]),wo("initialData"),Zs("typeaheadBehaviours",[Vg,Df,c1,Fg,zg,$y]),cr("previewing",function(){return Po(!0)})].concat(Ey()).concat(ux())),d1=rt([$f({schema:[Yu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(e,n){o.previewing.get()?e.getSystem().getByUid(o.uid).each(function(t){!function(t,e,o){if(t.selectsOver){var n=Df.getValue(e),r=t.getDisplayText(n),i=Df.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?vt.some(function(){var n;ck(0,e,o),n=r.length,sk(e,function(t,e){return t.setSelectionRange(n,e.length)})}):vt.none()}return vt.none()}(o.model,t,n).fold(function(){return hd.dehighlight(e,n)},function(t){return t()})}):e.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&ck(o.model,t,n)}),o.previewing.set(!1)},onExecute:function(t,e){return t.getSystem().getByUid(o.uid).toOptional().map(function(t){return br(t,l1(),{item:e}),!0})},onHover:function(t,e){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&ck(o.model,t,e)})}}}})]),m1=Cl({name:"Typeahead",configFields:f1(),partFields:d1(),factory:function(r,t,e,i){function n(t,e,n){r.previewing.set(!1);var o=$y.getCoupled(t,"sandbox");bf.isOpen(o)?cd.getCurrent(o).each(function(t){hd.getHighlighted(t).fold(function(){n(t)},function(){wr(o,t.element,"keydown",e)})}):ex(r,u(t),t,o,i,function(t){cd.getCurrent(t).each(n)},Vy.HighlightFirst).get(st)}function u(n){return function(t){return t.map(function(t){var e=H(Z(t.menus),function(t){return F(t.items,function(t){return"item"===t.type})});return Df.getState(n).update(B(e,function(t){return t.data})),t})}}var o=ry(r),a=[Vg.config({}),Df.config({onSetValue:r.onSetValue,store:lt({mode:"dataset",getDataKey:function(t){return Qr(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,e){Zr(t.element,r.model.getDisplayText(e))}},r.initialData.map(function(t){return sr("initialValue",t)}).getOr({}))}),c1.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,e){var n,o=$y.getCoupled(t,"sandbox");Vg.isFocused(t)&&Qr(t.element).length>=r.minChars&&(n=cd.getCurrent(o).bind(function(t){return hd.getHighlighted(t).map(Df.getValue)}),r.previewing.set(!0),ex(r,u(t),t,o,i,function(t){cd.getCurrent(o).each(function(t){n.fold(function(){r.model.selectsOver&&hd.highlightFirst(t)},function(e){hd.highlightBy(t,function(t){return Df.getValue(t).value===e.value}),hd.getHighlighted(t).orThunk(function(){return hd.highlightFirst(t),vt.none()})})})},Vy.HighlightFirst).get(st))},cancelEvent:Hi()}),Fg.config({mode:"special",onDown:function(t,e){return n(t,e,hd.highlightFirst),vt.some(!0)},onEscape:function(t){var e=$y.getCoupled(t,"sandbox");return bf.isOpen(e)?(bf.close(e),vt.some(!0)):vt.none()},onUp:function(t,e){return n(t,e,hd.highlightLast),vt.some(!0)},onEnter:function(e){var t=$y.getCoupled(e,"sandbox"),n=bf.isOpen(t);if(n&&!r.previewing.get())return cd.getCurrent(t).bind(function(t){return hd.getHighlighted(t)}).map(function(t){return br(e,l1(),{item:t}),!0});var o=Df.getValue(e);return vr(e,Hi()),r.onExecute(t,e,o),n&&bf.close(t),vt.some(!0)}}),zg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(t){return rx(r,t,{onOpen:function(){return zg.on(t)},onClose:function(){return zg.off(t)}})}}}),mm("typeaheadevents",[uu(function(t){nx(r,u(t),t,i,st,Vy.HighlightFirst).get(st)}),Cr(l1(),function(t,e){var n=$y.getCoupled(t,"sandbox");ck(r.model,t,e.event.item),vr(t,Hi()),r.onItemExecute(t,n,e.event.item,Df.getValue(t)),bf.close(n),s1(t)})].concat(r.dismissOnBlur?[Cr(Bi(),function(t){var e=$y.getCoupled(t,"sandbox");_a(e.element).isNone()&&bf.close(e)})]:[]))];return{uid:r.uid,dom:iy(Yo(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:lt(lt({},o),el(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),g1=function(i){return lt(lt({},i),{toCached:function(){return g1(i.toCached())},bindFuture:function(e){return g1(i.bind(function(t){return t.fold(function(t){return Gy(Nn.error(t))},function(t){return e(t)})}))},bindResult:function(e){return g1(i.map(function(t){return t.bind(e)}))},mapResult:function(e){return g1(i.map(function(t){return t.map(e)}))},mapError:function(e){return g1(i.map(function(t){return t.mapError(e)}))},foldResult:function(e,n){return i.map(function(t){return t.fold(e,n)})},withTimeout:function(t,r){return g1(Wy(function(e){var n=!1,o=setTimeout(function(){n=!0,e(Nn.error(r()))},t);i.get(function(t){n||(clearTimeout(o),e(t))})}))}})},p1=function(t){return g1(Wy(t))},h1={type:"separator"},v1=Ir("aria-invalid"),b1={bar:pk(function(t,e){return n=e.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:B(t.items,n.interpreter)};var n}),collection:pk(function(t,e){return u=t,a=e.shared.providers,c=u.label.map(function(t){return oy(t,a)}),s=n(function(t,e,n,o){e.stop(),a.isDisabled()||br(t,Sy,{name:u.name,value:o})}),l=[Cr(vi(),n(function(t,e,n){ka(n)})),Cr(Ci(),s),Cr(Ni(),s),Cr(bi(),n(function(t,e,n){Iu(t.element,"."+Bh).each(function(t){qr(t,Bh)}),Yr(n,Bh)})),Cr(yi(),n(function(t){Iu(t.element,"."+Bh).each(function(t){qr(t,Bh)})})),uu(n(function(t,e,n,o){br(t,Sy,{name:u.name,value:o})}))],ny(c,my.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:h},behaviours:ec([gd.config({disabled:a.isDisabled,onDisabled:function(t){o(t,function(t){Yr(t,"tox-collection__item--state-disabled"),oe(t,"aria-disabled",!0)})},onEnabled:function(t){o(t,function(t){qr(t,"tox-collection__item--state-disabled"),ce(t,"aria-disabled")})}}),pv(),Rg.config({}),Df.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){var e=o,n=B(t,function(t){var e=dp.translate(t.text),n=1===u.columns?'<div class="tox-collection__item-label">'+e+"</div>":"",o='<div class="tox-collection__item-icon">'+t.icon+"</div>",r={_:" "," - ":" ","-":" "},i=e.replace(/\_| \- |\-/g,function(t){return r[t]});return'<div class="tox-collection__item'+(a.isDisabled()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+hy.encodeAllRaw(t.value)+'" title="'+i+'" aria-label="'+i+'">'+o+n+"</div>"}),r=B("auto"!==u.columns&&1<u.columns?p(n,u.columns):[n],function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});Mr(e.element,r.join("")),"auto"===u.columns&&ah(o,5,"tox-collection__item").each(function(t){var e=t.numRows,n=t.numColumns;Fg.setGridSize(o,e,n)}),vr(o,Ty)}}),py.config({}),Fg.config(1===(i=u.columns)?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===i?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:"."+Ch}}),mm("collection-events",l)]),eventOrder:((r={})[Ii()]=["disabling","alloy.base.behaviour","collection-events"],r)}),["tox-form__group--collection"],[]);function n(o){return function(e,n){Ru(n.event.target,"[data-collection-item-value]").each(function(t){o(e,n,t,ie(t,"data-collection-item-value"))})}}function o(t,e){return B(vs(t.element,".tox-collection__item"),e)}var u,a,r,i,c,s,l}),alertbanner:pk(function(t,e){return o=e.shared.providers,ly.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+(n=t).level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:jm(n.icon,o.icons),attributes:{title:o.translate(n.iconTooltip)}},action:function(t){br(t,Sy,{name:"alert-banner",value:n.url})},buttonBehaviours:ec([Wm()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(n.text)}}]});var n,o}),input:pk(function(t,e){return o=e.shared.providers,uk({name:(n=t).name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:vt.none(),maximized:n.maximized},o);var n,o}),textarea:pk(function(t,e){return o=e.shared.providers,uk({name:(n=t).name,multiline:!0,label:n.label,inputMode:vt.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:vt.none(),maximized:n.maximized},o);var n,o}),label:pk(function(t,e){return r={dom:{tag:"label",innerHtml:(o=e.shared).providers.translate((n=t).label),classes:["tox-label"]}},i=B(n.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:ec([aw(),Rg.config({}),cw(vt.none()),Fg.config({mode:"acyclic"})])};var n,o,r,i}),iframe:(ZC=function(t,e){return n=t,o=e.shared.providers,u=bS&&n.sandboxed,a=lt(lt({},n.label.map(function(t){return{title:t}}).getOr({})),u?{sandbox:"allow-scripts allow-same-origin"}:{}),r=u,i=Po(""),c={getValue:function(t){return i.get()},setValue:function(t,e){var n;r?oe(t.element,"srcdoc",e):(oe(t.element,"src","javascript:''"),(n=t.element.dom.contentWindow.document).open(),n.write(e),n.close()),i.set(e)}},ny(n.label.map(function(t){return oy(t,o)}),my.parts.field({factory:{sketch:function(t){return d0({uid:t.uid,dom:{tag:"iframe",attributes:a},behaviours:ec([py.config({}),Vg.config({}),gS(vt.none(),c.getValue,c.setValue)])})}}}),["tox-form__group--stretched"],[]);var n,o,r,i,u,a,c},function(t,e,n){var o=Yo(e,{source:"dynamic"});return pk(ZC)(t,o,n)}),button:pk(function(t,e){return n=t,o=e.shared.providers,r=$0(n.name,"custom"),ny(vt.none(),my.parts.field(lt({factory:fp},K0(n,vt.some(r),o,[pS(""),aw()]))),[],[]);var n,o,r}),checkbox:pk(function(t,e){return r=t,i=e.shared.providers,u=Df.config({store:{mode:"manual",getValue:function(t){return t.element.dom.checked},setValue:function(t,e){t.element.dom.checked=e}}}),a=my.parts.field({factory:{sketch:h},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ec([aw(),gd.config({disabled:function(){return r.disabled||i.isDisabled()}}),py.config({}),Vg.config({}),u,Fg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),mm("checkbox-events",[Cr(ki(),function(t,e){br(t,yy,{name:r.name})})])])}),c=my.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:i.translate(r.label)},behaviours:ec([vw.config({})])}),s=Hm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]}),my.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[a,s.asSpec(),c],fieldBehaviours:ec([gd.config({disabled:function(){return r.disabled||i.isDisabled()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])});function n(t){return t.element.dom.click(),vt.some(!0)}function o(t){return Xm("checked"===t?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t]},i.icons)}var r,i,u,a,c,s}),colorinput:pk(function(t,e){return n=t,r=e.shared,i=e.colorinput,c=my.parts.field({factory:Dy,inputClasses:["tox-textfield"],onSetValue:function(t){return Ky.run(t).get(st)},inputBehaviours:ec([gd.config({disabled:r.providers.isDisabled}),pv(),py.config({}),Ky.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return Yt(t.element)},notify:{onValid:function(t){var e=Df.getValue(t);br(t,bw,{color:e})}},validator:{validateOnLoad:!1,validate:function(t){var e=Df.getValue(t);if(0===e.length)return Gy(Nn.value(!0));var n=Mt.fromTag("span");fe(n,"background-color",e);var o=pe(n,"background-color").fold(function(){return Nn.error("blah")},function(t){return Nn.value(e)});return Gy(o)}}})]),selectOnFocus:!1}),s=n.label.map(function(t){return oy(t,r.providers)}),l=Hm((u={dom:{tag:"span",attributes:{"aria-label":r.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Ya,Xa,$a]},onLtr:function(){return[Xa,Ya,$a]}},components:[],fetch:wb(i.getColors(),i.hasCustomColors()),columns:i.getColorCols(),presets:"color",onItemAction:function(t,n){l.getOpt(t).each(function(e){"custom"===n?i.colorPicker(function(t){t.fold(function(){return vr(e,xw)},function(t){o(e,t),hb(t)})},"#ffffff"):o(e,"remove"===n?"":n)})}},hw.sketch({dom:u.dom,components:u.components,toggleClass:"mce-active",dropdownBehaviours:ec([$v((a=r).providers.isDisabled),pv(),vw.config({}),py.config({})]),layouts:u.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:a.getSink,fetch:function(e){return Wy(function(t){return u.fetch(t)}).map(function(t){return vt.from(Xb(Yo(Ob(Ir("menu-value"),t,function(t){u.onItemAction(e,t)},u.columns,u.presets,Sh.CLOSE_ON_EXECUTE,T,a.providers),{movement:_b(u.columns,u.presets)})))})},parts:{menu:Zp(0,0,u.presets)}}))),my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:s.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[c,l.asSpec()]}]),fieldBehaviours:ec([mm("form-field-events",[Cr(bw,function(t,e){l.getOpt(t).each(function(t){fe(t.element,"background-color",e.event.color)}),br(t,yy,{name:n.name})}),Cr(yw,function(e,n){my.getField(e).each(function(t){Df.setValue(t,n.event.value),cd.getCurrent(e).each(Vg.focus)})}),Cr(xw,function(e,t){my.getField(e).each(function(t){cd.getCurrent(e).each(Vg.focus)})})])])});function o(t,e){br(t,yw,{value:e})}var n,r,i,u,a,c,s,l}),colorpicker:pk(function(t){var r=Hm(a0(c0,function(t){return"tox-"+t}).sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){br(t,Sy,{name:"hex-valid",value:!0})},onInvalidHex:function(t){br(t,Sy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:ec([Df.config({store:{mode:"manual",getValue:function(t){var e=r.get(t);return cd.getCurrent(e).bind(function(t){return Df.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,e){var n=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(e),o=r.get(t);cd.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){Df.setValue(t,{hex:vt.from(n[1]).getOr("")}),nS.getField(t,"hex").each(function(t){vr(t,Si())})})}}}),aw()])}}),dropzone:pk(function(t,e){return l0(t,e.shared.providers)}),grid:pk(function(t,e){return n=e.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+t.columns+"col"]},components:B(t.items,n.interpreter)};var n}),listbox:pk(function(t,e){return n=t,r=(o=e).shared.providers,i=Y(n.items).filter(rk),u=n.label.map(function(t){return oy(t,r)}),a={dom:{tag:"div",classes:["tox-listboxfield"]},components:[my.parts.field({dom:{},factory:{sketch:function(t){return z0({uid:t.uid,text:i.map(function(t){return t.text}),icon:vt.none(),tooltip:n.label,role:vt.none(),fetch:function(t,e){e(j0(o1(t,n.name,n.items,Df.getValue(t)),Sh.CLOSE_ON_EXECUTE,o,!1))},onSetup:rt(st),getApi:rt({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[py.config({}),Df.config({store:{mode:"manual",initialValue:i.map(function(t){return t.value}).getOr(""),getValue:function(t){return ie(t.element,n1)},setValue:function(e,t){r1(n.items,t).each(function(t){oe(e.element,n1,t.value),br(e,NS,{text:t.text})})}}})]},"tox-listbox",o.shared)}}})]},my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([u.toArray(),[a]]),fieldBehaviours:ec([gd.config({disabled:rt(n.disabled),onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}})])});var n,o,r,i,u,a}),selectbox:pk(function(t,e){return n=t,o=e.shared.providers,r=B(n.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=n.label.map(function(t){return oy(t,o)}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:ft([[my.parts.field({dom:{},selectAttributes:{size:n.size},options:r,factory:i1,selectBehaviours:ec([gd.config({disabled:function(){return n.disabled||o.isDisabled()}}),py.config({}),mm("selectbox-change",[Cr(ki(),function(t,e){br(t,yy,{name:n.name})})])])})],(1<n.size?vt.none():vt.some(Xm("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},o.icons))).toArray()])},my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([i.toArray(),[u]]),fieldBehaviours:ec([gd.config({disabled:function(){return n.disabled||o.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])});var n,o,r,i,u}),sizeinput:pk(function(t,e){return ow(t,e.shared.providers)}),urlinput:pk(function(t,e){return gk(t,e,e.urlinput)}),customeditor:pk(function(n){var o=gc(),e=Hm({dom:{tag:n.tag}}),r=gc();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ec([mm("custom-editor-events",[ou(function(t){e.getOpt(t).each(function(e){(Tt(n,"init")?n.init(e.element.dom):lS.load(n.scriptId,n.scriptUrl).then(function(t){return t(e.element.dom,n.settings)})).then(function(e){r.on(function(t){e.setValue(t)}),r.clear(),o.set(e)})})})]),Df.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,e){o.get().fold(function(){r.set(e)},function(t){return t.setValue(e)})}}}),aw()]),components:[e.asSpec()]}}),htmlpanel:pk(function(t){return"presentation"===t.presets?ly.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):ly.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:ec([py.config({}),Vg.config({})])})}),imagetools:pk(function(t,e){return ok(t,e.shared.providers)}),table:pk(function(t,e){return ik(t,e.shared.providers)}),panel:pk(function(t,e){return{dom:{tag:"div",classes:t.classes},components:B(t.items,e.shared.interpreter)}})},y1={field:function(t,e){return e}},x1=function(e,n,o){return tt(b1,n.type).fold(function(){return console.error('Unknown factory type "'+n.type+'", defaulting to container: ',n),n},function(t){return t(e,n,o)})},w1={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},S1=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],k1=function(t){return R(t,function(t,e){if(Tt(e,"items")){var n=k1(e.items);return{customFormats:t.customFormats.concat(n.customFormats),formats:t.formats.concat([{title:e.title,items:n.formats}])}}if(Tt(e,"inline")||Tt(e,"block")||Tt(e,"selector")){var o="custom-"+(y(e.name)?e.name:e.title.toLowerCase());return{customFormats:t.customFormats.concat([{name:o,format:e}]),formats:t.formats.concat([{title:e.title,format:o,icon:e.icon}])}}return lt(lt({},t),{formats:t.formats.concat(e)})},{customFormats:[],formats:[]})},C1=fS.trim,O1=wk("true"),_1=wk("false"),T1=function(t){var e=B(vs(Mt.fromDom(t),"h1,h2,h3,h4,h5,h6,a:not([href])"),function(t){return t.dom});return F(B(F(e,_k),Tk).concat(B(F(e,Ck),Ek)),Dk)},E1="tinymce-url-history",D1=od,A1=ed,B1=rt([Eo("shell",!1),mo("makeItem"),Eo("setupItem",st),Af("listBehaviours",[Rg])]),M1=rt([Qf({name:"items",overrides:function(){return{behaviours:ec([Rg.config({})])}}})]),F1=Cl({name:rt("CustomList")(),configFields:B1(),partFields:M1(),factory:function(s,t,e,n){var o=s.shell?{behaviours:[Rg.config({})],components:[]}:{behaviours:[],components:t};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:el(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){var t;t=a,(s.shell?vt.some(t):ml(t,s,"items")).fold(function(){throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(e){var t=Rg.contents(e),n=c.length,o=n-t.length,r=0<o?m(o,function(){return s.makeItem()}):[],i=t.slice(n);St(i,function(t){return Rg.remove(e,t)}),St(r,function(t){return Rg.append(e,t)});var u=Rg.contents(e);St(u,function(t,e){s.setupItem(a,t,c[e],e)})})}}}},apis:{setItems:function(t,e,n){t.setItems(e,n)}}}),I1=rt([mo("dom"),Eo("shell",!0),Zs("toolbarBehaviours",[Rg])]),R1=rt([Qf({name:"groups",overrides:function(){return{behaviours:ec([Rg.config({})])}}})]),N1=Cl({name:"Toolbar",configFields:I1(),partFields:R1(),factory:function(o,t,e,n){var r=o.shell?{behaviours:[Rg.config({})],components:[]}:{behaviours:[],components:t};return{uid:o.uid,dom:o.dom,components:r.components,behaviours:el(o.toolbarBehaviours,r.behaviours),apis:{setGroups:function(t,e){var n;n=t,(o.shell?vt.some(n):ml(n,o,"groups")).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Rg.set(t,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,e,n){t.setGroups(e,n)}}}),P1=rt([]),V1=Object.freeze({__proto__:null,setup:st,isDocked:T,getBehaviours:P1}),H1=Vo([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),L1=function(t,e,n){var o,r,i,u;t.getSystem().isConnected()&&(i=n,u=(r=e).lazyViewport(o=t),i.isDocked()&&$k(o,r,i,u),qk(o,u,i).each(function(t){t.fold(function(){return Kk(o,r,i)},function(t){return Jk(o,r,i,t)},function(t){$k(o,r,i,u,!0),Jk(o,r,i,t)})}))},z1=Object.freeze({__proto__:null,refresh:L1,reset:Qk,isDocked:function(t,e,n){return n.isDocked()},getModes:function(t,e,n){return n.getModes()},setModes:function(t,e,n,o){return n.setModes(o)}}),U1=Object.freeze({__proto__:null,events:function(o,r){return nu([Ar(_i(),function(e,n){o.contextual.each(function(t){Kr(e.element,t.transitionClass)&&($r(e.element,[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(e)),n.stop()})}),Cr(ji(),function(t,e){L1(t,o,r)}),Cr(Wi(),function(t,e){Qk(t,o,r)})])}}),j1=xa({fields:[To("contextual",[po("fadeInClass"),po("fadeOutClass"),po("transitionClass"),vo("lazyContext"),Ju("onShow"),Ju("onShown"),Ju("onHide"),Ju("onHidden")]),Io("lazyViewport",Fn),Ro("modes",["top","bottom"],er),Ju("onDocked"),Ju("onUndocked")],name:"docking",active:U1,apis:z1,state:Object.freeze({__proto__:null,init:function(t){var e=Po(!1),n=Po(!0),o=gc(),r=Po(t.modes);return wu({isDocked:e.get,setDocked:e.set,getInitialPos:o.get,setInitialPos:o.set,clearInitialPos:o.clear,isVisible:n.get,setVisible:n.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+e.get()+", visible: "+n.get()+", modes: "+r.get().join(",")}})}})}),W1=rt(Ir("toolbar-height-change")),G1={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},X1="tox-tinymce--toolbar-sticky-on",Y1="tox-tinymce--toolbar-sticky-off",q1=Object.freeze({__proto__:null,setup:function(t,e,n){t.inline||(e.header.isPositionedAtTop()||t.on("ResizeEditor",function(){n().each(j1.reset)}),t.on("ResizeWindow ResizeEditor",function(){n().each(tC)}),t.on("SkinLoaded",function(){n().each(function(t){j1.isDocked(t)?j1.reset(t):j1.refresh(t)})}),t.on("FullscreenStateChanged",function(){n().each(j1.reset)})),t.on("AfterScrollIntoView",function(b){n().each(function(t){j1.refresh(t);var e,n,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=t.element;Pd(v)&&(e=b,r=(o=jt(n=v)).dom.defaultView.innerHeight,i=Ie(o),a=He(u=Mt.fromDom(e.elm)),c=xe(u),l=(s=a.y)+c,f=ke(n),d=xe(n),g=(m=f.top)+d,p=Math.abs(m-i.top)<2,h=Math.abs(g-(i.top+r))<2,p&&s<g?Re(i.left,s-d,o):h&&m<l&&Re(i.left,s-r+c+d,o))})}),t.on("PostRender",function(){nC(t,!1)})},isDocked:function(t){return t().map(j1.isDocked).getOr(!1)},getBehaviours:oC}),K1=$o([po("type"),go("items",no([Qo([po("name"),xo("items",er)]),er]))].concat(Vp)),J1=[Co("text"),Co("tooltip"),Co("icon"),vo("fetch"),Io("onSetup",function(){return st})],$1=$o(V([po("type")],J1,!0)),Q1=$o([po("type"),Co("tooltip"),Co("icon"),Co("text"),Oo("select"),vo("fetch"),Io("onSetup",function(){return st}),Mo("presets","normal",["normal","color","listpreview"]),Eo("columns",1),vo("onAction"),vo("onItemAction")]),Z1=kl({factory:function(e,o){var t={focus:Fg.focusIn,setMenus:function(t,e){var n=B(e,function(e){return G0(rC({type:"menubutton",text:e.text,fetch:function(t){t(e.getItems())}}).mapError(function(t){return ur(t)}).getOrDie(),"tox-mbtn",o.backstage,vt.some("menuitem"))});Rg.set(t,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:ec([Rg.config({}),mm("menubar-events",[ou(function(t){e.onSetup(t)}),Cr(vi(),function(n,t){Iu(n.element,".tox-mbtn--active").each(function(e){Ru(t.event.target,".tox-mbtn").each(function(t){zt(e,t)||n.getSystem().getByDom(e).each(function(e){n.getSystem().getByDom(t).each(function(t){hw.expand(t),hw.close(e),Vg.focus(t)})})})})}),Cr(Ki(),function(n,t){t.event.prevFocus.bind(function(t){return n.getSystem().getByDom(t).toOptional()}).each(function(e){t.event.newFocus.bind(function(t){return n.getSystem().getByDom(t).toOptional()}).each(function(t){hw.isOpen(e)&&(hw.expand(t),hw.close(e))})})})]),Fg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return e.onEscape(t),vt.some(!0)}}),py.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[mo("dom"),mo("uid"),mo("onEscape"),mo("backstage"),Eo("onSetup",st)],apis:{focus:function(t,e){t.focus(e)},setMenus:function(t,e,n){t.setMenus(e,n)}}}),tO=Object.freeze({__proto__:null,refresh:function(t,e,n){var o;n.isExpanded()&&(ve(t.element,uC(e)),o=aC(e,t.element),fe(t.element,uC(e),o))},grow:function(t,e,n){n.isExpanded()||mC(t,e,n)},shrink:function(t,e,n){n.isExpanded()&&dC(t,e,n)},immediateShrink:function(t,e,n){n.isExpanded()&&fC(t,e,n)},hasGrown:function(t,e,n){return n.isExpanded()},hasShrunk:function(t,e,n){return n.isCollapsed()},isGrowing:gC,isShrinking:pC,isTransitioning:function(t,e,n){return gC(t,e)||pC(t,e)},toggleGrow:function(t,e,n){(n.isExpanded()?dC:mC)(t,e,n)},disableTransitions:cC}),eO=Object.freeze({__proto__:null,exhibit:function(t,e,n){return zr(e.expanded?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:sr(e.dimension.property,"0px")})},events:function(n,o){return nu([Ar(_i(),function(t,e){e.event.raw.propertyName===n.dimension.property&&(cC(t,n),o.isExpanded()&&ve(t.element,n.dimension.property),(o.isExpanded()?n.onGrown:n.onShrunk)(t))})])}}),nO=xa({fields:[mo("closedClass"),mo("openClass"),mo("shrinkingClass"),mo("growingClass"),wo("getAnimationRoot"),Ju("onShrunk"),Ju("onStartShrink"),Ju("onGrown"),Ju("onStartGrow"),Eo("expanded",!1),go("dimension",lo("property",{width:[ta("property","width"),ta("getDimension",function(t){return Ce(t)+"px"})],height:[ta("property","height"),ta("getDimension",function(t){return xe(t)+"px"})]}))],name:"sliding",active:eO,apis:tO,state:Object.freeze({__proto__:null,init:function(t){var e=Po(t.expanded);return wu({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:C(e.set,!1),setExpanded:C(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),oO="container",rO=[Zs("slotBehaviours",[])],iO=dt({getSlotNames:function(t,e){return t.getSlotNames(e)},getSlot:function(t,e,n){return t.getSlot(e,n)},isShowing:function(t,e,n){return t.isShowing(e,n)},hideSlot:function(t,e,n){return t.hideSlot(e,n)},hideAllSlots:function(t,e){return t.hideAllSlots(e)},showSlot:function(t,e,n){return t.showSlot(e,n)}},Hr),uO=lt(lt({},iO),{sketch:function(t){var n,e={slot:function(t,e){return n.push(t),cl(oO,hC(t),e)},record:rt(n=[])},o=t(e),r=B(e.record(),function(t){return Jf({name:t,pname:hC(t)})});return Sl(oO,rO,r,$C,o)}}),aO=$o([Co("icon"),Co("tooltip"),Io("onShow",st),Io("onHide",st),Io("onSetup",function(){return st})]),cO=Ir("FixSizeEvent"),sO=Ir("AutoSizeEvent"),lO=Object.freeze({__proto__:null,block:function(t,e,n,o){oe(t.element,"aria-busy",!0);var r=e.getRoot(t).getOr(t),i=ec([Fg.config({mode:"special",onTab:function(){return vt.some(!0)},onShiftTab:function(){return vt.some(!0)}}),Vg.config({})]),u=o(r,i),a=r.getSystem().build(u);Rg.append(r,Eu(a)),a.hasConfigured(Fg)&&e.focus&&Fg.focusIn(a),n.isBlocked()||e.onBlock(t),n.blockWith(function(){return Rg.remove(r,a)})},unblock:function(t,e,n){ce(t.element,"aria-busy"),n.isBlocked()&&e.onUnblock(t),n.clear()}}),fO=xa({fields:[Io("getRoot",vt.none),Fo("focus",!0),Ju("onBlock"),Ju("onUnblock")],name:"blocking",apis:lO,state:Object.freeze({__proto__:null,init:function(){var e=dc();return wu({readState:e.isSet,blockWith:function(t){e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),dO=rt([Zs("splitToolbarBehaviours",[$y]),cr("builtGroups",function(){return Po([])})]),mO=rt([qu(["overflowToggledClass"]),Oo("getOverflowBounds"),mo("lazySink"),cr("overflowGroups",function(){return Po([])})].concat(dO())),gO=rt([Jf({factory:N1,schema:I1(),name:"primary"}),$f({schema:I1(),name:"overflow"}),$f({name:"overflow-button"}),$f({name:"overflow-group"})]),pO=rt(function(t,e){var n=t,o=Math.floor(e);fe(n,"max-width",On.max(n,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"])+"px")}),hO=rt([qu(["toggledClass"]),mo("lazySink"),vo("fetch"),Oo("getBounds"),To("fireDismissalEventInstead",[Eo("event",Yi())]),Uc()]),vO=rt([$f({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ec([zg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),$f({factory:N1,schema:I1(),name:"toolbar",overrides:function(e){return{toolbarBehaviours:ec([Fg.config({mode:"cyclic",onEscape:function(t){return ml(t,e,"button").each(Vg.focus),vt.none()}})])}}})]),bO=Cl({name:"FloatingToolbarButton",factory:function(u,t,a,e){return lt(lt({},fp.sketch(lt(lt({},e.button()),{action:function(t){EC(t,e)},buttonBehaviours:Bf({dump:e.button().buttonBehaviours},[$y.config({others:{toolbarSandbox:function(t){return o=t,n=a,r=u,{dom:{tag:"div",attributes:{id:(i=Nu()).id}},behaviours:ec([Fg.config({mode:"special",onEscape:function(t){return bf.close(t),vt.some(!0)}}),bf.config({onOpen:function(t,e){r.fetch().get(function(t){AC(o,e,r,n.layouts,t),i.link(o.element),Fg.focusIn(e)})},onClose:function(){zg.off(o),Vg.focus(o),i.unlink(o.element)},isPartOf:function(t,e,n){return Hu(e,n)||Hu(o,n)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),ic.config({channels:lt(lt({},Xs(lt({isExtraPart:T},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Ys({doReposition:function(){bf.getState($y.getCoupled(o,"toolbarSandbox")).each(function(t){DC(o,t,r,n.layouts)})}}))})])};var o,n,r,i}}})])}))),{apis:{setGroups:function(e,n){bf.getState($y.getCoupled(e,"toolbarSandbox")).each(function(t){AC(e,t,u,a.layouts,n)})},reposition:function(e){bf.getState($y.getCoupled(e,"toolbarSandbox")).each(function(t){DC(e,t,u,a.layouts)})},toggle:function(t){EC(t,e)},getToolbar:function(t){return bf.getState($y.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return bf.isOpen($y.getCoupled(t,"toolbarSandbox"))}}})},configFields:hO(),partFields:vO(),apis:{setGroups:function(t,e,n){t.setGroups(e,n)},reposition:function(t,e){t.reposition(e)},toggle:function(t,e){t.toggle(e)},getToolbar:function(t,e){return t.getToolbar(e)},isOpen:function(t,e){return t.isOpen(e)}}}),yO=rt([mo("items"),qu(["itemSelector"]),Zs("tgroupBehaviours",[Fg])]),xO=rt([Zf({name:"items",unit:"item"})]),wO=Cl({name:"ToolbarGroup",configFields:yO(),partFields:xO(),factory:function(t,e,n,o){return{uid:t.uid,dom:t.dom,components:e,behaviours:el(t.tgroupBehaviours,[Fg.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),SO=Cl({name:"SplitFloatingToolbar",configFields:mO(),partFields:gO(),factory:function(n,t,e,o){var r=Hm(bO.sketch({fetch:function(){return Wy(function(t){t(BC(n.overflowGroups.get()))})},layouts:{onLtr:function(){return[Ya,Xa]},onRtl:function(){return[Xa,Ya]},onBottomLtr:function(){return[Ka,qa]},onBottomRtl:function(){return[qa,Ka]}},getBounds:e.getOverflowBounds,lazySink:n.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:n.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:n.uid,dom:n.dom,components:t,behaviours:el(n.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(){return wO.sketch(lt(lt({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,e){n.builtGroups.set(B(e,t.getSystem().build)),MC(t,r,n)},refresh:function(t){return MC(t,r,n)},toggle:function(t){r.getOpt(t).each(function(t){bO.toggle(t)})},isOpen:function(t){return r.getOpt(t).map(bO.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each(function(t){bO.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(bO.getToolbar)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,e,n){t.setGroups(e,n)},refresh:function(t,e){t.refresh(e)},reposition:function(t,e){t.reposition(e)},toggle:function(t,e){t.toggle(e)},isOpen:function(t,e){return t.isOpen(e)},getOverflow:function(t,e){return t.getOverflow(e)}}}),kO=rt([qu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ju("onOpened"),Ju("onClosed")].concat(dO())),CO=rt([Jf({factory:N1,schema:I1(),name:"primary"}),Jf({factory:N1,schema:I1(),name:"overflow",overrides:function(e){return{toolbarBehaviours:ec([nO.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:function(t){ml(t,e,"overflow-button").each(function(t){zg.off(t),Vg.focus(t)}),e.onClosed(t)},onGrown:function(t){Fg.focusIn(t),e.onOpened(t)},onStartGrow:function(t){ml(t,e,"overflow-button").each(zg.on)}}),Fg.config({mode:"acyclic",onEscape:function(t){return ml(t,e,"overflow-button").each(Vg.focus),vt.some(!0)}})])}}}),$f({name:"overflow-button",overrides:function(t){return{buttonBehaviours:ec([zg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),$f({name:"overflow-group"})]),OO=Cl({name:"SplitSlidingToolbar",configFields:kO(),partFields:CO(),factory:function(o,t,e,n){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:el(o.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(e){return wO.sketch(lt(lt({},n["overflow-group"]()),{items:[fp.sketch(lt(lt({},n["overflow-button"]()),{action:function(t){vr(e,r)}}))]}))}}}),mm("toolbar-toggle-events",[Cr(r,function(t){FC(t,o)})])]),apis:{setGroups:function(t,e){var n=B(e,t.getSystem().build);o.builtGroups.set(n),QC(t,o)},refresh:function(t){return QC(t,o)},toggle:function(t){return FC(t,o)},isOpen:function(t){return ml(t,o,"overflow").map(nO.hasGrown).getOr(!1)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,e,n){t.setGroups(e,n)},refresh:function(t,e){t.refresh(e)},toggle:function(t,e){t.toggle(e)},isOpen:function(t,e){return t.isOpen(e)}}}),_O=A1.optional({factory:Z1,name:"menubar",schema:[mo("backstage")]}),TO=A1.optional({factory:{sketch:function(t){return F1.sketch({uid:t.uid,dom:t.dom,listBehaviours:ec([Fg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return VC({type:t.type,uid:Ir("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),vt.some(!0)}})},setupItem:function(t,e,n,o){N1.setGroups(e,n)},shell:!0})}},name:"multiple-toolbar",schema:[mo("dom"),mo("onEscape")]}),EO=A1.optional({factory:{sketch:function(t){return(t.type===bh.sliding?function(t){var e=OO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),n=OO.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=PC(t);return OO.sketch(lt(lt({},o),{components:[e,n],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([W1()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([W1()],{type:"closed"})}}))}:t.type===bh.floating?function(i){var t=PC(i),e=SO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return SO.sketch(lt(lt({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element,e=He(t),n=Gt(t),o=He(n),r=Math.max(n.dom.scrollHeight,o.height);return Bn(e.x+4,o.y,e.width-8,r)},parts:lt(lt({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[e],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}:VC)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),vt.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[mo("dom"),mo("onEscape"),mo("getSink")]}),DO=A1.optional({factory:{sketch:function(t){var e=t.editor,n=t.sticky?oC:P1;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:ec(n(e,t.sharedBackstage))}}},name:"header",schema:[mo("dom")]}),AO=A1.optional({name:"socket",schema:[mo("dom")]}),BO=A1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ec([py.config({}),Vg.config({}),nO.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){cd.getCurrent(t).each(uO.hideAllSlots),vr(t,sO)},onGrown:function(t){vr(t,sO)},onStartGrow:function(t){br(t,cO,{width:pe(t.element,"width").getOr("")})},onStartShrink:function(t){br(t,cO,{width:Ce(t.element)+"px"})}}),Rg.config({}),cd.config({find:function(t){return Y(Rg.contents(t))}})])}],behaviours:ec([cS(0),mm("sidebar-sliding-events",[Cr(cO,function(t,e){fe(t.element,"width",e.event.width)}),Cr(sO,function(t,e){ve(t.element,"width")})])])}}},name:"sidebar",schema:[mo("dom")]}),MO=A1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ec([Rg.config({}),fO.config({focus:!1}),cd.config({find:function(t){return Y(t.components())}})]),components:[]}}},name:"throbber",schema:[mo("dom")]}),FO=Cl({name:"OuterContainer",factory:function(n,t,e){return{uid:n.uid,dom:n.dom,components:t,apis:{getSocket:function(t){return D1.getPart(t,n,"socket")},setSidebar:function(t,e){D1.getPart(t,n,"sidebar").each(function(t){return bC(t,e)})},toggleSidebar:function(t,e){D1.getPart(t,n,"sidebar").each(function(t){var n=e;cd.getCurrent(t).each(function(e){cd.getCurrent(e).each(function(t){nO.hasGrown(e)?uO.isShowing(t,n)?nO.shrink(e):(uO.hideAllSlots(t),uO.showSlot(t,n)):(uO.hideAllSlots(t),uO.showSlot(t,n),nO.grow(e))})})})},whichSidebar:function(t){return D1.getPart(t,n,"sidebar").bind(yC).getOrNull()},getHeader:function(t){return D1.getPart(t,n,"header")},getToolbar:function(t){return D1.getPart(t,n,"toolbar")},setToolbar:function(t,e){D1.getPart(t,n,"toolbar").each(function(t){t.getApis().setGroups(t,e)})},setToolbars:function(t,e){D1.getPart(t,n,"multiple-toolbar").each(function(t){F1.setItems(t,e)})},refreshToolbar:function(t){D1.getPart(t,n,"toolbar").each(function(t){return t.getApis().refresh(t)})},toggleToolbarDrawer:function(t){D1.getPart(t,n,"toolbar").each(function(e){var t=e.getApis().toggle;null!=t?vt.some(function(t){return t(e)}(t)):vt.none()})},isToolbarDrawerToggled:function(t){return D1.getPart(t,n,"toolbar").bind(function(e){return vt.from(e.getApis().isOpen).map(function(t){return t(e)})}).getOr(!1)},getThrobber:function(t){return D1.getPart(t,n,"throbber")},focusToolbar:function(t){D1.getPart(t,n,"toolbar").orThunk(function(){return D1.getPart(t,n,"multiple-toolbar")}).each(function(t){Fg.focusIn(t)})},setMenubar:function(t,e){D1.getPart(t,n,"menubar").each(function(t){Z1.setMenus(t,e)})},focusMenubar:function(t){D1.getPart(t,n,"menubar").each(function(t){Z1.focus(t)})}},behaviours:n.behaviours}},configFields:[mo("dom"),mo("behaviours")],partFields:[DO,_O,EO,TO,AO,BO,MO],apis:{getSocket:function(t,e){return t.getSocket(e)},setSidebar:function(t,e,n){t.setSidebar(e,n)},toggleSidebar:function(t,e,n){t.toggleSidebar(e,n)},whichSidebar:function(t,e){return t.whichSidebar(e)},getHeader:function(t,e){return t.getHeader(e)},getToolbar:function(t,e){return t.getToolbar(e)},setToolbar:function(t,e,n){var o=B(n,RC);t.setToolbar(e,o)},setToolbars:function(t,e,n){var o=B(n,function(t){return B(t,RC)});t.setToolbars(e,o)},refreshToolbar:function(t,e){return t.refreshToolbar(e)},toggleToolbarDrawer:function(t,e){t.toggleToolbarDrawer(e)},isToolbarDrawerToggled:function(t,e){return t.isToolbarDrawerToggled(e)},getThrobber:function(t,e){return t.getThrobber(e)},setMenubar:function(t,e,n){t.setMenubar(e,n)},focusMenubar:function(t,e){t.focusMenubar(e)},focusToolbar:function(t,e){t.focusToolbar(e)}}}),IO={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},RO=C(jC,!1),NO=C(jC,!0);function PO(t,e,n,o){var r;return{type:"basic",data:(r=t.getParam(e,n,"string"),B(o===t1.SemiColon?r.replace(/;$/,"").split(";"):r.split(" "),function(t){var e=t,n=t,o=t.split("=");return 1<o.length&&(e=o[0],n=o[1]),{title:e,format:n}}))}}function VO(n){var t={type:"basic",data:v_};return{tooltip:"Align",text:vt.none(),icon:vt.some("align-left"),isSelectedFor:function(t){return function(){return n.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(t){return vt.none},onAction:function(e){return function(){return N(v_,function(t){return t.format===e.format}).each(function(t){return n.execCommand(t.command)})}},updateText:function(t){var e=N(v_,function(t){return n.formatter.match(t.format)}).fold(rt("left"),function(t){return t.title.toLowerCase()});br(t,PS,{icon:"align-"+e})},dataset:t,shouldHide:!1,isInvalid:function(t){return!n.formatter.canApply(t.format)}}}function HO(t){return B(t.split(/\s*,\s*/),function(t){return t.replace(/^['"]+|['"]+$/g,"")})}function LO(r){function i(){function n(t){return t?HO(t)[0]:""}var t=r.queryCommandValue("FontName"),e=a.data,o=t?t.toLowerCase():"";return{matchOpt:N(e,function(t){var e=t.format;return e.toLowerCase()===o||n(e).toLowerCase()===n(o).toLowerCase()}).orThunk(function(){return ot(0===(t=o).indexOf("-apple-system")&&(e=HO(t.toLowerCase()),L(b_,function(t){return-1<e.indexOf(t.toLowerCase())})),{title:u,format:o});var t,e}),font:t}}var u="System Font",a=PO(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",t1.SemiColon);return{tooltip:"Fonts",text:vt.some(u),icon:vt.none(),isSelectedFor:function(e){return function(t){return t.exists(function(t){return t.format===e})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return vt.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},updateText:function(t){var e=i(),n=e.matchOpt,o=e.font,r=n.fold(rt(o),function(t){return t.title});br(t,NS,{text:r})},dataset:a,shouldHide:!1,isInvalid:T}}function zO(n){function i(){var a=vt.none(),c=o.data,s=n.queryCommandValue("FontSize");if(s)for(var t=function(t){var e,n,o,r,i=(n=t,/[0-9.]+px$/.test(e=s)?(o=72*parseInt(e,10)/96,r=Math.pow(10,n||0),Math.round(o*r)/r+"pt"):tt(x_,e).getOr(e)),u=tt(y_,i).getOr("");a=N(c,function(t){return t.format===s||t.format===i||t.format===u})},e=3;a.isNone()&&0<=e;e--)t(e);return{matchOpt:a,size:s}}var t=rt(vt.none),o=PO(n,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",t1.Space);return{tooltip:"Font sizes",text:vt.some("12pt"),icon:vt.none(),isSelectedFor:function(e){return function(t){return t.exists(function(t){return t.format===e})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("FontSize",!1,t.format)})}},updateText:function(t){var e=i(),n=e.matchOpt,o=e.size,r=n.fold(rt(o),function(t){return t.title});br(t,NS,{text:r})},dataset:o,shouldHide:!1,isInvalid:T}}function UO(t,e){var n=e(),o=B(n,function(t){return t.format});return vt.from(t.formatter.closest(o)).bind(function(e){return N(n,function(t){return t.format===e})}).orThunk(function(){return ot(t.formatter.match("p"),{title:"Paragraph",format:"p"})})}function jO(n){var o=PO(n,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",t1.SemiColon);return{tooltip:"Blocks",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return n.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(e){return function(){var t=n.formatter.get(e);return vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:n.dom.parseStyle(n.formatter.getCssText(e))})}},onAction:XC(n),updateText:function(t){var e=UO(n,function(){return o.data}).fold(rt("Paragraph"),function(t){return t.title});br(t,NS,{text:e})},dataset:o,shouldHide:!1,isInvalid:function(t){return!n.formatter.canApply(t.format)}}}function WO(r,t){return{tooltip:"Formats",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return r.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(e){return function(){var t=r.formatter.get(e);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:r.dom.parseStyle(r.formatter.getCssText(e))}):vt.none()}},onAction:XC(r),updateText:function(t){var n=function(t){var e=t.items;return void 0!==e&&0<e.length?H(e,n):[{title:t.title,format:t.format}]},e=H(bk(r),n),o=UO(r,rt(e)).fold(rt("Paragraph"),function(t){return t.title});br(t,NS,{text:o})},shouldHide:r.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!r.formatter.canApply(t.format)},dataset:t}}function GO(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}}function XO(e){return{setActive:function(t){zg.set(e,t)},isActive:function(){return zg.isOn(e)},isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)}}}function YO(t,e){return t.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})}function qO(e,n,t,o,r,i){var u;return{dom:{tag:"button",classes:["tox-tbtn"].concat(n.isSome()?["tox-tbtn--select"]:[]),attributes:YO(t,i)},components:tb([e.map(function(t){return V0(t,i.icons)}),n.map(function(t){return L0(t,"tox-tbtn",i)})]),eventOrder:((u={})[mi()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:ec([Qv(i.isDisabled),pv(),mm("common-button-display-events",[Cr(mi(),function(t,e){e.event.prevent(),vr(t,T_)})])].concat(o.map(function(t){return k_.config({channel:t,initialData:{icon:e,text:n},renderComponents:function(t,e){return tb([t.icon.map(function(t){return V0(t,i.icons)}),t.text.map(function(t){return L0(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function KO(t,e,n){var o,r=Po(st),i=qO(t.icon,t.text,t.tooltip,vt.none(),vt.none(),n);return fp.sketch({dom:i.dom,components:i.components,eventOrder:RS,buttonBehaviours:ec([mm("toolbar-button-events",[(o={onAction:t.onAction,getApi:e.getApi},uu(function(e,t){hv(o,e)(function(t){br(e,IS,{buttonApi:t}),o.onAction(t)})})),vv(e,r),bv(e,r)]),Qv(function(){return t.disabled||n.isDisabled()}),pv()].concat(e.toolbarButtonBehaviours))})}function JO(r,i){return function(t,e,n){var o=r(t).mapError(function(t){return ur(t)}).getOrDie();return i(o,e,n)}}function $O(n,t,o,r){var e,i=t.outerContainer,u=o.toolbar,a=o.buttons;f(u,y)?(e=u.map(function(t){var e={toolbar:t,buttons:a,allowToolbarGroups:o.allowToolbarGroups};return F_(n,e,{backstage:r},vt.none())}),FO.setToolbars(i,e)):FO.setToolbar(i,F_(n,o,{backstage:r},vt.none()))}function QO(t){return/^[0-9\.]+(|px)$/i.test(""+t)?vt.some(parseInt(""+t,10)):vt.none()}function ZO(t){return u(t)?t+"px":t}function t_(e,t,n){var o=t.filter(function(t){return e<t}),r=n.filter(function(t){return t<e});return o.or(r).getOr(e)}function e_(t){var e=lh(t),n=fh(t),o=mh(t);return QO(e).map(function(t){return t_(t,n,o)})}function n_(u,a,t,e,c){function s(){return x.get()&&!u.removed}function l(t){return y?t.fold(rt(0),function(t){return 1<t.components().length?xe(t.components()[1].element):0}):0}function f(){r.broadcastOn([xf()],{})}function o(t){var e,n,o,r,i;void 0===t&&(t=!1),s()&&(m||(e=p.getOrThunk(function(){var t=QO(ge(bn(),"margin-left")).getOr(0);return Ce(bn())-ke(a).left+t}),fe(c.get().element,"max-width",e+"px")),y&&FO.refreshToolbar(d),m||(n=l(FO.getToolbar(d)),o=Mn(a),r=v()?Math.max(o.y-xe(c.get().element)+n,0):o.bottom,de(d.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(o.x)+"px"})),g&&(i=c.get(),t?j1.reset(i):j1.refresh(i)),f())}function n(t){var e,n;void 0===t&&(t=!0),!m&&g&&s()&&(e=h.getDockingMode(),(n=function(t){switch(uv(u)){case xh.auto:var e=l(FO.getToolbar(d)),n=xe(t.element)-e,o=Mn(a);if(o.y>n)return"top";var r=Gt(a),i=Math.max(r.dom.scrollHeight,xe(r));return o.bottom<i-n||Fn().bottom<o.bottom-n?"bottom":"top";case xh.bottom:return"bottom";default:return xh.top,"top"}}(c.get()))!==e&&(function(t){var e=c.get();j1.setModes(e,[t]),h.setDockingMode(t);var n=v()?Oc.TopToBottom:Oc.BottomToTop;oe(e.element,ns,n)}(n),t&&o(!0)))}var r=t.uiMothership,d=t.outerContainer,i=nv.DOM,m=sv(u),g=dv(u),p=mh(u).or(e_(u)),h=e.shared.header,v=h.isPositionedAtTop,b=iv(u),y=b===bh.sliding||b===bh.floating,x=Po(!1);return{isVisible:s,isPositionedAtTop:v,show:function(){x.set(!0),fe(d.element,"display","flex"),i.addClass(u.getBody(),"mce-edit-focus"),ve(r.element,"display"),n(!1),o()},hide:function(){x.set(!1),t.outerContainer&&(fe(d.element,"display","none"),i.removeClass(u.getBody(),"mce-edit-focus")),fe(r.element,"display","none")},update:o,updateMode:n,repositionPopups:f}}function o_(t,e){var n=Mn(t);return{pos:e?n.y:n.bottom,bounds:n}}function r_(i,u){return Cr(IS,function(t,e){var n,o=i.get(t),r=(n=o,{hide:function(){return vr(n,Vi())},getValue:function(){return Df.getValue(n)}});u.onAction(r,e.event.buttonApi)})}function i_(t,e,n){return e.bottom-t.y>=(n=void 0===n?.01:n)&&t.bottom-e.y>=n}function u_(t){var e=function(t){var e=t.getBoundingClientRect();if(e.height<=0&&e.width<=0){var n=Qt(Mt.fromDom(t.startContainer),t.startOffset).element;return(Xe(n)?Yt(n):vt.some(n)).filter(Ge).map(function(t){return t.dom.getBoundingClientRect()}).getOr(e)}return e}(t.selection.getRng());if(t.inline){var n=Ie();return Bn(n.left+e.left,n.top+e.top,e.width,e.height)}var o=He(Mt.fromDom(t.getBody()));return Bn(o.x+e.left,o.y+e.top,e.width,e.height)}function a_(t,e,n,o){void 0===o&&(o=0);var r,i,u,a,c=Pe(window),s=Mn(Mt.fromDom(t.getContentAreaContainer())),l=ph(t)||hh(t)||rv(t),f=(r=s,i=c,u=o,{x:a=Math.max(r.x+u,i.x),width:Math.min(r.right-u,i.right)-a}),d=f.x,m=f.width;if(t.inline&&!l)return Bn(d,c.y,m,c.height);var g=function(t,e,n,o,r,i){var u=Mt.fromDom(t.getContainer()),a=Iu(u,".tox-editor-header").getOr(u),c=Mn(a),s=c.y>=e.bottom,l=o&&!s;if(t.inline&&l)return{y:Math.max(c.bottom+i,n.y),bottom:n.bottom};if(t.inline&&!l)return{y:n.y,bottom:Math.min(c.y-i,n.bottom)};var f="line"===r?Mn(u):e;return l?{y:Math.max(c.bottom+i,n.y),bottom:Math.min(f.bottom-i,n.bottom)}:{y:Math.max(f.y+i,n.y),bottom:Math.min(c.y-i,n.bottom)}}(t,s,c,e.header.isPositionedAtTop(),n,o),p=g.y,h=g.bottom;return Bn(d,p,m,h-p)}function c_(t){return"node"===t}function s_(t,r,e,i,n){var u=u_(t),o=i.lastElement().exists(function(t){return zt(e,t)}),a=e,c=t.selection.getRng(),s=Qt(Mt.fromDom(c.startContainer),c.startOffset);return c.startContainer===c.endContainer&&c.startOffset===c.endOffset-1&&zt(s.element,a)?o?Nm:cp:o?function(e,t){var n=pe(e,"position");fe(e,"position",t);var o=i_(u,Mn(r))&&!i.isReposition()?Vm:Nm;return n.each(function(t){return fe(e,"position",t)}),o}(r,i.getMode()):("fixed"===i.getMode()?n.y+Ie().top:n.y)+(xe(r)+12)<=u.y?cp:sp}function l_(e,t){var n=M(F(t,function(t){return t.predicate(e.dom)}),function(t){return"contexttoolbar"===t.type});return{contextToolbars:n.pass,contextForms:n.fail}}function f_(n,t){function o(t){return zt(t,r)}var e,r=Mt.fromDom(t.getBody()),i=Mt.fromDom(t.selection.getNode());return o(e=i)||Ut(r,e)?function(t,e,n){var o=l_(t,e);if(0<o.contextForms.length)return vt.some({elem:t,toolbars:[o.contextForms[0]]});var r=l_(t,n);if(0<r.contextForms.length)return vt.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;function e(e){return d(t,function(t){return t.position===e})}function n(e){return F(t,function(t){return t.position===e})}var o=e("selection"),r=e("node");if(o||r){if(r&&o){var i=n("node"),u=B(n("selection"),function(t){return lt(lt({},t),{position:"node"})});return i.concat(u)}return n(o?"selection":"node")}return n("line")}(o.contextToolbars.concat(r.contextToolbars));return vt.some({elem:t,toolbars:i})}return vt.none()}(i,n.inNodeScope,n.inEditorScope).orThunk(function(){return a=n,(t=o)(e=i)?vt.none():dr(e,function(t){if(Ge(t)){var e=l_(t,a.inNodeScope),n=e.contextToolbars,o=e.contextForms,r=0<o.length?o:(u=n).length<=1?u:i("selection").orThunk(function(){return i("node")}).orThunk(function(){return i("line")}).map(function(t){return t.position}).fold(function(){return[]},function(e){return F(u,function(t){return t.position===e})});return 0<r.length?vt.some({elem:t,toolbars:r}):vt.none()}function i(e){return N(u,function(t){return t.position===e})}var u;return vt.none()},t);var t,e,a}):vt.none()}function d_(a,c){var s={},l=[],f=[],d={},m={},t=Ct(a);return St(t,function(t){var e,n,o,r,i,u=a[t];"contextform"===u.type?(o=t,i=co(ao("ContextForm",qp,r=u)),(s[o]=i).launch.map(function(t){d["form:"+o]=lt(lt({},r.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){c(i)}})}),("editor"===i.scope?f:l).push(i),m[o]=i):"contexttoolbar"===u.type&&(e=t,ao("ContextToolbar",Kp,n=u).each(function(t){("editor"===n.scope?f:l).push(t),m[e]=t}))}),{forms:s,inNodeScope:l,inEditorScope:f,lookupTable:m,formNavigators:d}}function m_(d,t,m,u){function a(){var t=y.get().getOr("node"),e=c_(t)?1:0;return a_(d,p,t,e)}function c(){return!(d.removed||h()&&g.isContextMenuOpen())}function s(){if(c()){var t=a(),e=mt(y.get(),"node")?(n=d,v.get().filter(vn).map(He).getOrThunk(function(){return u_(n)})):u_(d);return t.height<=0||!i_(e,t)}return 1;var n}function e(){v.clear(),b.clear(),y.clear(),up.hide(x)}function n(){var t;up.isOpen(x)&&(ve(t=x.element,"display"),s()?fe(t,"display","none"):(b.set(0),up.reposition(x)))}function l(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:ec([Fg.config({mode:"acyclic"}),mm("pop-dialog-wrap-events",[ou(function(t){d.shortcuts.add("ctrl+F9","focus statusbar",function(){return Fg.focusIn(t)})}),ru(function(t){d.shortcuts.remove("ctrl+F9")})])])}}function f(t,e){var n,o,r,i,c,s,u,a="node"===t?p.anchors.node(e):p.anchors.cursor(),l=(n=d,o=t,r=h(),i={lastElement:v.get,isReposition:function(){return mt(b.get(),0)},getMode:function(){return ff.getMode(m)}},"line"===o?{bubble:Sc(12,0,L_),layouts:{onLtr:function(){return[la]},onRtl:function(){return[fa]}},overrides:z_}:{bubble:Sc(0,12,L_,1/12),layouts:(c=n,s=i,u=o,r?{onLtr:function(t){return[$a,Xa,Ya,qa,Ka,Ja].concat(f(t))},onRtl:function(t){return[$a,Ya,Xa,Ka,qa,Ja].concat(f(t))}}:{onLtr:function(t){return[Ja,$a,qa,Xa,Ka,Ya].concat(f(t))},onRtl:function(t){return[Ja,$a,Ka,Ya,qa,Xa].concat(f(t))}}),overrides:z_});function f(t){return c_(u)?[(a=t,function(t,e,n,o,r){var i=s_(c,o,a,s,r),u=lt(lt({},t),{y:r.y,height:r.height});return lt(lt({},i(u,e,n,o,r)),{alwaysFit:!0})})]:[];var a}return Yo(a,l)}function o(t,e){var n,o,r,i;k.cancel(),c()&&(n=S(t),r=f(o=t[0].position,e),y.set(o),b.set(1),ve(i=x.element,"display"),mt(Et(e,v.get(),zt),!0)||(qr(i,X_),ff.reset(m,x)),up.showWithinBounds(x,l(n),{anchor:r,transition:{classes:[X_],mode:"placement"}},function(){return vt.some(a())}),e.fold(v.clear,v.set),s()&&fe(i,"display","none"))}var r,i,g=u.backstage,p=g.shared,h=ln().deviceType.isTouch,v=gc(),b=gc(),y=gc(),x=Tu((r={sink:m,onEscape:function(){return d.focus(),vt.some(!0)}},i=Po([]),up.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){i.set([]),up.getContent(t).each(function(t){ve(t.element,"visibility")}),qr(t.element,G_),ve(t.element,"width")},inlineBehaviours:ec([mm("context-toolbar-events",[Ar(_i(),function(t,e){"width"===e.event.raw.propertyName&&(qr(t.element,G_),ve(t.element,"width"))}),Cr(W_,function(t,e){var n=t.element;ve(n,"width");var o=Ce(n);up.setContent(t,e.event.contents),Yr(n,G_);var r=Ce(n);fe(n,"width",o+"px"),up.getContent(t).each(function(t){e.event.focus.bind(function(t){return ka(t),_a(n)}).orThunk(function(){return Fg.focusIn(t),Oa(gn(n))})}),lp.setTimeout(function(){fe(t.element,"width",r+"px")},0)}),Cr(U_,function(e,t){up.getContent(e).each(function(t){i.set(i.get().concat([{bar:t,focus:Oa(gn(e.element))}]))}),br(e,W_,{contents:t.event.forwardContents,focus:vt.none()})}),Cr(j_,function(e,t){q(i.get()).each(function(t){i.set(i.get().slice(0,i.get().length-1)),br(e,W_,{contents:Eu(t.bar),focus:t.focus})})})]),Fg.config({mode:"special",onEscape:function(e){return q(i.get()).fold(function(){return r.onEscape()},function(t){return vr(e,j_),vt.some(!0)})}})]),lazySink:function(){return Nn.value(r.sink)}}))),w=Rt(function(){return d_(t,function(t){var e=S([t]);br(x,U_,{forwardContents:l(e)})})}),S=function(t){var e=d.ui.registry.getAll().buttons,n=w(),o=lt(lt({},e),n.formNavigators),r=iv(d)===bh.scrolling?bh.scrolling:bh.default,i=ft(B(t,function(t){return"contexttoolbar"===t.type?F_(d,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},u,vt.some(["form:"])):(e=p.providers,H_(t,e));var e}));return VC({type:r,uid:Ir("context-toolbar"),initGroups:i,onEscape:vt.none,cyclicKeying:!0,providers:p.providers})},k=vp(function(){d.hasFocus()&&!d.removed&&(Kr(x.element,X_)?k.throttle():f_(w(),d).fold(e,function(t){o(t.toolbars,vt.some(t.elem))}))},17);d.on("init",function(){d.on("remove",e),d.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",n),d.on("click keyup focus SetContent",k.throttle),d.on(V_,e),d.on("contexttoolbar-show",function(e){tt(w().lookupTable,e.toolbarKey).each(function(t){o([t],ot(e.target!==d,e.target)),up.getContent(x).each(Fg.focusIn)})}),d.on("focusout",function(t){lp.setEditorTimeout(d,function(){_a(m.element).isNone()&&_a(x.element).isNone()&&e()},0)}),d.on("SwitchMode",function(){d.mode.isReadOnly()&&e()}),d.on("AfterProgressState",function(t){t.state?e():d.hasFocus()&&k.throttle()}),d.on("NodeChange",function(t){_a(x.element).fold(k.throttle,st)})})}(e1=t1=t1||{})[e1.SemiColon=0]="SemiColon",e1[e1.Space=1]="Space";var g_,p_,h_,v_=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],b_=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],y_={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},x_={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},w_=Object.freeze({__proto__:null,events:function(r,i){function u(n,o){r.updateState.each(function(t){var e=t(n,o);i.set(e)}),r.renderComponents.each(function(t){var e=B(t(o,i.get()),n.getSystem().build);Rs(n,e)})}return nu([Cr(Fi(),function(t,e){var n,o=e;o.universal||(n=r.channel,wt(o.channels,n)&&u(t,o.data))}),ou(function(e,t){r.initialData.each(function(t){u(e,t)})})])}}),S_=Object.freeze({__proto__:null,getState:function(t,e,n){return n}}),k_=xa({fields:[mo("channel"),wo("renderComponents"),wo("updateState"),wo("initialData")],name:"reflecting",active:w_,apis:S_,state:Object.freeze({__proto__:null,init:function(){var t=Po(vt.none());return{readState:function(){return t.get().getOr("none")},get:t.get,set:t.set,clear:function(){return t.set(vt.none())}}}})}),C_=rt([mo("toggleClass"),mo("fetch"),Qu("onExecute"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Uc(),Qu("onItemExecute"),wo("lazySink"),mo("dom"),Ju("onOpen"),Zs("splitDropdownBehaviours",[$y,Fg,Vg]),Eo("matchWidth",!1),Eo("useMinWidth",!1),Eo("eventOrder",{}),wo("role")].concat(ux())),O_=rt([Jf({factory:fp,schema:[mo("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:ec([Vg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(yr)},buttonBehaviours:ec([zg.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])}}}),Jf({factory:fp,schema:[mo("dom")],name:"button",defaults:function(){return{buttonBehaviours:ec([Vg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(e){e.getSystem().getByUid(n.uid).each(function(t){n.onExecute(t,e)})}}}}),Qf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[mo("text")],name:"aria-descriptor"}),$f({schema:[Yu()],name:"menu",defaults:function(o){return{onExecute:function(e,n){e.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,e,n)})}}}}),Zy()]),__=Cl({name:"SplitDropdown",configFields:C_(),partFields:O_(),factory:function(o,t,e,n){function r(t){cd.getCurrent(t).each(function(t){hd.highlightFirst(t),Fg.focusIn(t)})}function i(t){nx(o,h,t,n,r,Vy.HighlightFirst).get(st)}function u(t){return yr(gl(t,o,"button")),vt.some(!0)}var a,c=lt(lt({},nu([ou(function(n,t){ml(n,o,"aria-descriptor").each(function(t){var e=Ir("aria");oe(t.element,"id",e),oe(n.element,"aria-describedby",e)})})])),Sm(vt.some(i))),s={repositionMenus:function(t){zg.isOn(t)&&ix(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:s,eventOrder:lt(lt({},o.eventOrder),((a={})[Ii()]=["disabling","toggling","alloy.base.behaviour"],a)),events:c,behaviours:el(o.splitDropdownBehaviours,[$y.config({others:{sandbox:function(t){var e=gl(t,o,"arrow");return rx(o,t,{onOpen:function(){zg.on(e),zg.on(t)},onClose:function(){zg.off(e),zg.off(t)}})}}}),Fg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),vt.some(!0)}}),Vg.config({}),zg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,e){return t.repositionMenus(e)}}}),T_=Ir("focus-button"),E_=function(t,e,n){return KO(t,{toolbarButtonBehaviours:[].concat(0<n.length?[mm("toolbarButtonWith",n)]:[]),getApi:GO,onSetup:t.onSetup},e)},D_=function(t,e,n){return Yo(KO(t,{toolbarButtonBehaviours:[Rg.config({}),zg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<n.length?[mm("toolbarToggleButtonWith",n)]:[]),getApi:XO,onSetup:t.onSetup},e))},A_=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],B_={button:JO(Op,function(t,e){return n=e.backstage.shared.providers,E_(t,n,[]);var n}),togglebutton:JO(_p,function(t,e){return n=e.backstage.shared.providers,D_(t,n,[]);var n}),menubutton:JO(rC,function(t,e){return G0(t,"tox-tbtn",e.backstage,vt.none())}),splitbutton:JO(function(t){return ao("SplitButton",Q1,t)},function(t,e){return o=t,r=e.backstage.shared,s=Ir("channel-update-split-dropdown-display"),l=Po(st),f={getApi:n,onSetup:o.onSetup},__.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:lt({"aria-pressed":!1},YO(o.tooltip,r.providers))},onExecute:function(t){o.onAction(n(t))},onItemExecute:function(t,e,n){},splitDropdownBehaviours:ec([Yv(r.providers.isDisabled),pv(),mm("split-dropdown-events",[Cr(T_,Vg.focus),vv(f,l),bv(f,l)]),vw.config({})]),eventOrder:((i={})[Gi()]=["alloy.base.behaviour","split-dropdown-events"],i),toggleClass:"tox-tbtn--enabled",lazySink:r.getSink,fetch:(u=n,a=o,c=r.providers,function(e){return Wy(function(t){return a.fetch(t)}).map(function(t){return vt.from(Xb(Yo(Ob(Ir("menu-value"),t,function(t){a.onItemAction(u(e),t)},a.columns,a.presets,Sh.CLOSE_ON_EXECUTE,a.select.getOr(T),c),{movement:_b(a.columns,a.presets),menuBehaviours:Kh("auto"!==a.columns?[]:[ou(function(o,t){ah(o,4,Jp(a.presets)).each(function(t){var e=t.numRows,n=t.numColumns;Fg.setGridSize(o,e,n)})})])})))})}),parts:{menu:Zp(0,o.columns,o.presets)},components:[__.parts.button(qO(o.icon,o.text,vt.none(),vt.some(s),vt.some([zg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),r.providers)),__.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:jm("chevron-down",r.providers.icons)},buttonBehaviours:ec([Yv(r.providers.isDisabled),pv(),Wm()])}),__.parts["aria-descriptor"]({text:r.providers.translate("To open the popup, press Shift+Enter")})]});function n(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)},setIconFill:function(t,e){Iu(n.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){oe(t,"fill",e)})},setIconStroke:function(t,e){Iu(n.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){oe(t,"stroke",e)})},setActive:function(e){oe(n.element,"aria-pressed",e),Iu(n.element,"span").each(function(t){n.getSystem().getByDom(t).each(function(t){return zg.set(t,e)})})},isActive:function(){return Iu(n.element,"span").exists(function(t){return n.getSystem().getByDom(t).exists(zg.isOn)})}}}var o,r,i,u,a,c,s,l,f}),grouptoolbarbutton:JO(function(t){return ao("GroupToolbarButton",K1,t)},function(t,e,n){var o,r,i,u,a,c,s=n.ui.registry.getAll().buttons,l=((o={})[ns]=e.backstage.shared.header.isPositionedAtTop()?Oc.TopToBottom:Oc.BottomToTop,o);if(iv(n)!==bh.floating)throw new Error("Toolbar groups are only supported when using floating toolbar mode");return i=e.backstage,u=function(t){return F_(n,{buttons:s,toolbar:t,allowToolbarGroups:!1},e,vt.none())},a=l,c=i.shared,bO.sketch({lazySink:c.getSink,fetch:function(){return Wy(function(t){t(B(u(r.items),RC))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:qO((r=t).icon,r.text,r.tooltip,vt.none(),vt.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}})}),styleSelectButton:function(t,e){return KC(t,n=e.backstage,WO(t,lt({type:"advanced"},n.styleselect)));var n},fontsizeSelectButton:function(t,e){return KC(t,e.backstage,zO(t))},fontSelectButton:function(t,e){return KC(t,e.backstage,LO(t))},formatButton:function(t,e){return KC(t,e.backstage,jO(t))},alignMenuButton:function(t,e){return KC(t,e.backstage,VO(t))}},M_={styleselect:B_.styleSelectButton,fontsizeselect:B_.fontsizeSelectButton,fontselect:B_.fontSelectButton,formatselect:B_.formatButton,align:B_.alignMenuButton},F_=function(o,c,s,l){var n,t,e,r,i=(e=c.toolbar,r=c.buttons,!1===e?[]:void 0===e||!0===e?(n=r,t=B(A_,function(t){var e=F(t.items,function(t){return Tt(n,t)||Tt(M_,t)});return{name:t.name,items:e}}),F(t,function(t){return 0<t.items.length})):y(e)?B(e.split("|"),function(t){return{items:t.trim().split(" ")}}):f(e,function(t){return Tt(t,"name")&&Tt(t,"items")})?e:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[]));return F(B(i,function(t){var e=H(t.items,function(t){return 0===t.trim().length?[]:(r=o,e=c.buttons,i=t,u=c.allowToolbarGroups,a=s,n=l,tt(e,i.toLowerCase()).orThunk(function(){return n.bind(function(t){return K(t,function(t){return tt(e,t+i.toLowerCase())})})}).fold(function(){return tt(M_,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return vt.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(n=a,o=r,tt(B_,(e=t).type).fold(function(){return console.error("skipping button defined by",e),vt.none()},function(t){return vt.some(t(e,n,o))})):(console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),vt.none());var e,n,o}).toArray());var r,e,i,u,a,n});return{title:vt.from(o.translate(t.name)),items:e}}),function(t){return 0<t.items.length})},I_=ln(),R_=I_.os.isiOS()&&I_.os.version.major<=12,N_=Object.freeze({__proto__:null,render:function(n,e,t,o,r){var i=Po(0),u=e.outerContainer;RO(n);var a=Mt.fromDom(r.targetNode),c=te(gn(a)),s=e.mothership;pf(a,s,De),Ls(c,e.uiMothership),n.on("PostRender",function(){$O(n,e,t,o),i.set(n.getWin().innerWidth),FO.setMenubar(u,LC(n,t)),FO.setSidebar(u,t.sidebar),function(n,t){function e(){var t=c.get();t.left===u.innerWidth&&t.top===u.innerHeight||(c.set(kn(u.innerWidth,u.innerHeight)),Uv(n))}function o(){var t=n.getDoc().documentElement,e=s.get();e.left===t.offsetWidth&&e.top===t.offsetHeight||(s.set(kn(t.offsetWidth,t.offsetHeight)),Uv(n))}function r(t){return n.fire("ScrollContent",t)}var i=n.dom,u=n.getWin(),a=n.getDoc().documentElement,c=Po(kn(u.innerWidth,u.innerHeight)),s=Po(kn(a.offsetWidth,a.offsetHeight));i.bind(u,"resize",e),i.bind(u,"scroll",r);var l=hc(Mt.fromDom(n.getBody()),"load",o),f=t.uiMothership.element;n.on("hide",function(){fe(f,"display","none")}),n.on("show",function(){ve(f,"display")}),n.on("NodeChange",o),n.on("remove",function(){l.unbind(),i.unbind(u,"resize",e),i.unbind(u,"scroll",r),u=null})}(n,e)});var l,f,d,m,g=FO.getSocket(u).getOrDie("Could not find expected socket element");R_&&(de(g.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"}),f=function(){n.fire("ScrollContent")},d=null,m=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];b(d)&&(d=setTimeout(function(){d=null,f.apply(null,t)},20))},l=pc(g.element,"scroll",m),n.on("remove",l.unbind)),gv(n,e),n.addCommand("ToggleSidebar",function(t,e){FO.toggleSidebar(u,e),n.fire("ToggleSidebar")}),n.addQueryValueHandler("ToggleSidebar",function(){return FO.whichSidebar(u)});var p=iv(n);p!==bh.sliding&&p!==bh.floating||n.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=n.getWin().innerWidth;t!==i.get()&&(FO.refreshToolbar(e.outerContainer),i.set(t))});var h={enable:function(){mv(e,!1)},disable:function(){mv(e,!0)},isDisabled:function(){return gd.isDisabled(u)}};return{iframeContainer:g.element.dom,editorContainer:u.element.dom,api:h}}}),P_=Object.freeze({__proto__:null,render:function(e,n,o,r,t){var i=n.mothership,u=n.uiMothership,a=n.outerContainer,c=Po(null),s=Mt.fromDom(t.targetNode),l=n_(e,s,n,r,c),f=e.getParam("toolbar_persist",!1,"boolean");function d(){var t;c.get()?l.show():(c.set(FO.getHeader(a).getOrDie()),Ls(t=lv(e),i),Ls(t,u),$O(e,n,o,r),FO.setMenubar(a,LC(e,o)),l.show(),function(c,s,l,t){function e(t){var e=o_(s,l.isPositionedAtTop()),n=e.pos,o=e.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:n,bounds:o}),a&&Uv(c,t),l.isVisible()&&(i!==n?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))}var f=Po(o_(s,l.isPositionedAtTop()));t||(c.on("activate",l.show),c.on("deactivate",l.hide)),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){lp.requestAnimationFrame(function(){return e(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var n=mc();n.set(hc(Mt.fromDom(c.getBody()),"load",e)),c.on("remove",function(){n.clear()})}(e,s,l,f),e.nodeChanged())}NO(e),e.on("show",d),e.on("hide",l.hide),f||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",function(){(e.hasFocus()||f)&&d()}),gv(e,n);var m={show:function(){l.show()},hide:function(){l.hide()},enable:function(){mv(n,!1)},disable:function(){mv(n,!0)},isDisabled:function(){return gd.isDisabled(a)}};return{editorContainer:a.element.dom,api:m}}}),V_="contexttoolbar-hide",H_=function(t,e){var n,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=Hm(Dy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:ec([Fg.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return yr(t),!0})},onLeft:function(t,e){return e.cut(),vt.none()},onRight:function(t,e){return e.cut(),vt.none()}})])})),c=(n=a,o=t.commands,r=e,i=B(o,function(t){return Hm(("contextformtogglebutton"===t.type?function(t,e,n){var o=e.original;o.primary;var r=A(o,["primary"]),i=co(_p(lt(lt({},r),{type:"togglebutton",onAction:st})));return D_(i,n.backstage.shared.providers,[r_(t,e)])}:function(t,e,n){var o=e.original;o.primary;var r=A(o,["primary"]),i=co(Op(lt(lt({},r),{type:"button",onAction:st})));return E_(i,n.backstage.shared.providers,[r_(t,e)])})(n,t,{backstage:{shared:{providers:r}}}))}),{asSpecs:function(){return B(i,function(t){return t.asSpec()})},findPrimary:function(n){return K(o,function(t,e){return t.primary?vt.from(i[e]).bind(function(t){return t.getOpt(n)}).filter(O(gd.isDisabled)):vt.none()})}});return[{title:vt.none(),items:[a.asSpec()]},{title:vt.none(),items:c.asSpecs()}]},L_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},z_={maxHeightFunction:Nc(),maxWidthFunction:pO()},U_=Ir("forward-slide"),j_=Ir("backward-slide"),W_=Ir("change-slide-event"),G_="tox-pop--resizing",X_="tox-pop--transition",Y_={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},q_=(h_=["Infinity",(g_="[0-9]+")+"\\."+K_(g_)+K_(p_="[eE][+-]?[0-9]+"),"\\."+g_+K_(p_),g_+K_(p_)].join("|"),new RegExp("^([+-]?(?:"+h_+"))(.*)$"));function K_(t){return"(?:"+t+")?"}function J_(u,a){function e(){var t=a.getOptions(u),r=a.getCurrent(u).map(a.hash),i=gc();return B(t,function(o){return{type:"togglemenuitem",text:a.display(o),onSetup:function(e){function t(t){t&&(i.on(function(t){return t.setActive(!1)}),i.set(e)),e.setActive(t)}t(mt(r,a.hash(o)));var n=a.watcher(u,o,t);return function(){i.clear(),n()}},onAction:function(){return a.setCurrent(u,o)}}})}u.ui.registry.addMenuButton(a.name,{tooltip:a.text,icon:a.icon,fetch:function(t){return t(e())},onSetup:a.onToolbarSetup}),u.ui.registry.addNestedMenuItem(a.name,{type:"nestedmenuitem",text:a.text,getSubmenuItems:e,onSetup:a.onMenuSetup})}function $_(t,e){return function(){t.execCommand("mceToggleFormat",!1,e)}}function Q_(t){var e,n;!function(n){fS.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,e){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:WC(n,t.name),onAction:$_(n,t.name)})});for(var t=1;t<=6;t++){var e="h"+t;n.ui.registry.addToggleButton(e,{text:e.toUpperCase(),tooltip:"Heading "+t,onSetup:WC(n,e),onAction:$_(n,e)})}}(t),e=t,fS.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(e,t.action)})}),n=t,fS.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(n,t.action),onSetup:WC(n,t.name)})})}function Z_(e,n){return GC(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(t){t.setDisabled(e.mode.isReadOnly()||!e.undoManager[n]())})}function tT(t){var e;t.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:YC(t,"mceToggleVisualAid")}),t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:GC(e=t,"VisualAid",function(t){t.setActive(e.hasVisual)}),onAction:YC(t,"mceToggleVisualAid")})}function eT(t,e){var n,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T=t;St([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){T.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(T,t.cmd),onSetup:WC(T,t.name)})}),T.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:YC(T,"JustifyNone")}),Q_(O=t),_=O,fS.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){_.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:YC(_,t.action)})}),_.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:$_(_,"code")}),C=qC(0,d=l=e,VO(f=s=t)),f.ui.registry.addNestedMenuItem("align",{text:d.shared.providers.translate("Align"),getSubmenuItems:function(){return C.items.validateItems(C.getStyleItems())}}),p=qC(0,g=l,LO(m=s)),m.ui.registry.addNestedMenuItem("fontformats",{text:g.shared.providers.translate("Fonts"),getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}}),h=s,b=lt({type:"advanced"},(v=l).styleselect),y=qC(0,v,WO(h,b)),h.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return y.items.validateItems(y.getStyleItems())}}),w=qC(0,l,jO(x=s)),x.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return w.items.validateItems(w.getStyleItems())}}),k=qC(0,l,zO(S=s)),S.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return k.items.validateItems(k.getStyleItems())}}),(a=u=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:Z_(a,"hasUndo"),onAction:YC(a,"undo")}),a.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:Z_(a,"hasRedo"),onAction:YC(a,"redo")}),(c=u).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",disabled:!0,onSetup:Z_(c,"hasUndo"),onAction:YC(c,"undo")}),c.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",disabled:!0,onSetup:Z_(c,"hasRedo"),onAction:YC(c,"redo")}),function(t){var i;(i=t).addCommand("mceApplyTextcolor",function(t,e){var n,o=t,r=e;(n=i).undoManager.transact(function(){n.focus(),n.formatter.apply(o,{value:r}),n.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var e,n=t;(e=i).undoManager.transact(function(){e.focus(),e.formatter.remove(n,{value:null},null,!0),e.nodeChanged()})});var e=Po(Fb),n=Po(Fb);kb(t,"forecolor","forecolor","Text color",e),kb(t,"backcolor","hilitecolor","Background color",n),Cb(t,"forecolor","forecolor","Text color"),Cb(t,"backcolor","hilitecolor","Background color")}(t),tT(t),(r=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:GC(i=r,"NodeChange",function(t){t.setDisabled(!i.queryCommandState("outdent"))}),onAction:YC(r,"outdent")}),r.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:YC(r,"indent")}),J_(n=t,LT),o=n,vt.from(o.getParam("content_langs",void 0,"array")).map(function(t){return{name:"language",text:"Language",icon:"language",getOptions:rt(t),hash:function(t){return E(t.customCode)?t.code:t.code+"/"+t.customCode},display:function(t){return t.title},watcher:function(t,e,n){return t.formatter.formatChanged("lang",n,!1,{value:e.code,customValue:e.customCode}).unbind},getCurrent:function(t){return mr(Mt.fromDom(t.selection.getNode()),function(t){return vt.some(t).filter(Ge).bind(function(e){return ue(e,"lang").map(function(t){return{code:t,customCode:ue(e,"data-mce-lang").getOrUndefined(),title:""}})})})},setCurrent:function(t,e){return t.execCommand("Lang",!1,e)},onToolbarSetup:function(t){var e=mc();return t.setActive(o.formatter.match("lang",{},void 0,!0)),e.set(o.formatter.formatChanged("lang",t.setActive,!0)),e.clear}}}).each(function(t){return J_(n,t)})}function nT(t,e){return{type:"makeshift",x:t,y:e}}function oT(t){return"longpress"===t.type||0===t.type.indexOf("touch")}function rT(t,e){return"contextmenu"===e.type||"longpress"===e.type?t.inline?function(t){if(oT(t)){var e=t.touches[0];return nT(e.pageX,e.pageY)}return nT(t.pageX,t.pageY)}(e):(n=t.getContentAreaContainer(),o=function(t){if(oT(t)){var e=t.touches[0];return nT(e.clientX,e.clientY)}return nT(t.clientX,t.clientY)}(e),r=nv.DOM.getPos(n),i=r.x,u=r.y,nT(o.x+i,o.y+u)):zT(t);var n,o,r,i,u}function iT(t,e,n){switch(n){case"node":return{type:"node",node:vt.some(Mt.fromDom((o=t).selection.getNode())),root:Mt.fromDom(o.getBody())};case"point":return rT(t,e);case"selection":return zT(t)}var o}function uT(f,d,m,g,p,h){function t(){var e,n,t,o,r,i,u,a,c,s,l=m();t=l,o=g,r=p,u=!(y||v||b&&x),c=iT(e=f,n=d,a=i=h),s=lt({bubble:Sc(0,"point"===a?12:0,jT),layouts:UT,overrides:{maxWidthFunction:pO(),maxHeightFunction:Nc()}},c),j0(t,Sh.CLOSE_ON_EXECUTE,o,!0).map(function(t){n.preventDefault(),up.showMenuWithinBounds(r,{anchor:s},{menu:{markers:Qp("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return vt.some(a_(e,o.shared,"node"===i?"node":"selection"))}),e.fire(V_)})}var e,n=ln(),v=n.os.isiOS(),b=n.os.isOSX(),y=n.os.isAndroid(),x=n.deviceType.isTouch();(b||v)&&"node"!==h?(e=function(){(function(t){function e(){lp.setEditorTimeout(t,function(){t.selection.setRng(n)},10),i()}var n=t.selection.getRng();function o(t){t.preventDefault(),t.stopImmediatePropagation()}function r(){return i()}t.once("touchend",e),t.on("mousedown",o,!0),t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}})(f),t()},function(t,e){var n=t.selection;if(!(n.isCollapsed()||e.touches.length<1)){var o=e.touches[0],r=n.getRng();return xs(t.getWin(),ms.domRange(r)).exists(function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY})}}(f,d)?e():(f.once("selectionchange",e),f.once("touchend",function(){return f.off("selectionchange",e)}))):t()}function aT(t){return"string"==typeof t?t.split(/[ ,]/):t}function cT(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")}function sT(t){return y(t)?"|"===t:"separator"===t.type}function lT(t,e){if(0===e.length)return t;var n=q(t).filter(function(t){return!sT(t)}).fold(function(){return[]},function(t){return[WT]});return t.concat(n).concat(e).concat([WT])}function fT(t,e){return"longpress"!==e.type&&(2!==e.button||e.target===t.getBody()&&""===e.pointerType)}function dT(t,e){return fT(t,e)?t.selection.getStart(!0):e.target}function mT(s,t,e){function n(t){return up.hide(i)}function o(c){var t;cT(s)&&c.preventDefault(),c.ctrlKey&&!cT(s)||!1===s.getParam("contextmenu")||(t=function(t,e){var n=t.getParam("contextmenu_avoid_overlap","","string"),o=fT(t,e)?"selection":"point";if(at(n)){var r=dT(t,e);return Yb(Mt.fromDom(r),n)?"node":o}return o}(s,c),(r()?uT:function(t,e,n,o,r,i){var u=n(),a=iT(t,e,i);j0(u,Sh.CLOSE_ON_EXECUTE,o,!1).map(function(t){e.preventDefault(),up.showMenuAt(r,{anchor:a},{menu:{markers:Qp("normal")},data:t})})})(s,c,function(){var t,e,n,o=dT(s,c),r=s.ui.registry.getAll(),i=(n=(e=s).ui.registry.getAll().contextMenus,vt.from(e.getParam("contextmenu")).map(aT).getOrThunk(function(){return F(aT("link linkchecker image imagetools table spellchecker configurepermanentpen"),function(t){return Tt(n,t)})})),u=r.contextMenus,a=o;return 0<(t=R(i,function(o,t){return tt(u,t.toLowerCase()).map(function(t){var e=t.update(a);if(y(e))return lT(o,e.split(" "));if(0<e.length){var n=B(e,GT);return lT(o,n)}return o}).getOrThunk(function(){return o.concat([t])})},[])).length&&sT(t[t.length-1])&&t.pop(),t},e,i,t))}var r=ln().deviceType.isTouch,i=Tu(up.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return s.focus()},onShow:function(){return e.setContextMenuState(!0)},onHide:function(){return e.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:ec([mm("dismissContextMenu",[Cr(Yi(),function(t,e){bf.close(t),s.focus()})])])}));s.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");s.on(t,n),s.on("longpress contextmenu",o)})}function gT(e){return function(t){return t.translate(-e.left,-e.top)}}function pT(e){return function(t){return t.translate(e.left,e.top)}}function hT(n){return function(t,e){return R(n,function(t,e){return e(t)},kn(t,e))}}function vT(t,e,n){return t.fold(hT([pT(n),gT(e)]),hT([gT(e)]),hT([]))}function bT(t,e,n){return t.fold(hT([pT(n)]),hT([]),hT([pT(e)]))}function yT(t,e,n){return t.fold(hT([]),hT([gT(n)]),hT([pT(e),gT(n)]))}function xT(t,e,n){var o=t.fold(function(t,e){return{position:vt.some("absolute"),left:vt.some(t+"px"),top:vt.some(e+"px")}},function(t,e){return{position:vt.some("absolute"),left:vt.some(t-n.left+"px"),top:vt.some(e-n.top+"px")}},function(t,e){return{position:vt.some("fixed"),left:vt.some(t+"px"),top:vt.some(e+"px")}});return lt({right:vt.none(),bottom:vt.none()},o)}function wT(t,i,u,a){function e(o,r){return function(t,e){var n=o(i,u,a);return r(t.getOr(n.left),e.getOr(n.top))}}return t.fold(e(yT,YT),e(bT,qT),e(vT,KT))}function ST(t,e){var n=ie(t,e);return E(n)?NaN:parseInt(n,10)}function kT(t,e,n,o,r,i){var u,a,c,s,l,f,d=(u=n,a=o,l=ST(s=t.element,(c=e).leftAttr),f=ST(s,c.topAttr),(isNaN(l)||isNaN(f)?vt.none():vt.some(kn(l,f))).fold(function(){return u},function(t){return KT(t.left+a.left,t.top+a.top)})),m=(e.mustSnap?JT:$T)(t,e,d,r,i),g=vT(d,r,i),p=e,h=g,v=t.element;return oe(v,p.leftAttr,h.left+"px"),oe(v,p.topAttr,h.top+"px"),m.fold(function(){return{coord:KT(g.left,g.top),extra:vt.none()}},function(t){return{coord:t.output,extra:t.extra}})}function CT(t,c,s,l){return K(t,function(t){var e,n,o=t.sensor,r=t.range.left,i=t.range.top,u=bT(c,e=s,n=l),a=bT(o,e,n);return Math.abs(u.left-a.left)<=r&&Math.abs(u.top-a.top)<=i?vt.some({output:wT(t.output,c,s,l),extra:t.extra}):vt.none()})}function OT(t,e){var n;t.getSystem().addToGui(e),Yt((n=e).element).filter(Ge).each(function(e){pe(e,"z-index").each(function(t){oe(e,ZT,t)}),fe(e,"z-index",ge(n.element,"z-index"))})}function _T(t){Yt(t.element).filter(Ge).each(function(e){ue(e,ZT).fold(function(){return ve(e,"z-index")},function(t){return fe(e,"z-index",t)}),ce(e,ZT)}),t.getSystem().removeFromGui(t)}function TT(t,e,n){return t.getSystem().build(ly.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[e]},events:n}))}function ET(t,e){return{bounds:t.getBounds(),height:we(e.element),width:Oe(e.element)}}function DT(A,B,t,e,n){var o=t.update(e,n),M=t.getStartData().getOrThunk(function(){return ET(B,A)});o.each(function(t){var e,n,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_=A,T=M,E=t,D=(e=B).getTarget(_.element);e.repositionTarget&&(n=Ie(jt(_.element)),o=Uk(D),S=pe(w=D,"left"),k=pe(w,"top"),C=pe(w,"position"),O=function(t,e,n){return("fixed"===n?KT:YT)(parseInt(t,10),parseInt(e,10))},r=(S.isSome()&&k.isSome()&&C.isSome()?vt.some(O(S.getOrDie(),k.getOrDie(),C.getOrDie())):vt.none()).getOrThunk(function(){var t=ke(w);return qT(t.left,t.top)}),i=_,u=e.snaps,a=r,c=n,s=o,l=E,f=T,m=u.fold(function(){var n,o,t=vT((n=l.left,o=l.top,a.fold(function(t,e){return YT(t+n,e+o)},function(t,e){return qT(t+n,e+o)},function(t,e){return KT(t+n,e+o)})),c,s);return KT(t.left,t.top)},function(e){var t=kT(i,e,a,l,c,s);return t.extra.each(function(t){e.onSensor(i,t)}),t.coord}),g=c,p=s,h=(d=f).bounds,v=bT(m,g,p),b=Wa(v.left,h.x,h.x+h.width-d.width),y=Wa(v.top,h.y,h.y+h.height-d.height),x=qT(b,y),me(D,xT(m.fold(function(){var t=yT(x,g,p);return YT(t.left,t.top)},rt(x),function(){var t=vT(x,g,p);return KT(t.left,t.top)}),0,o))),e.onDrag(_,D,E)})}function AT(o,t,e,n){t.each(_T),e.snaps.each(function(t){var e,n;e=t,ce(n=o.element,e.leftAttr),ce(n,e.topAttr)});var r=e.getTarget(o.element);n.reset(),e.onDrop(o,r)}function BT(t){return function(e,n){function o(t){n.setStartData(ET(e,t))}return nu(V([Cr(ji(),function(t){n.getStartData().each(function(){return o(t)})})],t(e,n,o),!0))}}function MT(a,c,s){return[Cr(mi(),function(e,t){var n,o,r,i,u;0===t.event.raw.button&&(t.stop(),r={drop:n=function(){return AT(e,vt.some(i),a,c)},delayDrop:(o=qb(n,200)).schedule,forceDrop:n,move:function(t){o.cancel(),DT(e,a,c,nE,t)}},i=TT(e,a.blockerClass,(u=r,nu([Cr(mi(),u.forceDrop),Cr(hi(),u.drop),Cr(gi(),function(t,e){u.move(e.event)}),Cr(pi(),u.delayDrop)]))),s(e),OT(e,i))})]}function FT(a,c,s){function l(t){AT(t,f.get(),a,c),f.clear()}var f=gc();return[Cr(si(),function(e,t){function n(){return l(e)}t.stop();var o,r,i,u=TT(e,a.blockerClass,(r=o=n,i=function(t){DT(e,a,c,rE,t)},nu([Cr(si(),r),Cr(fi(),o),Cr(di(),o),Cr(li(),function(t,e){i(e.event)})])));f.set(u),s(e),OT(e,u)}),Cr(li(),function(t,e){e.stop(),DT(t,a,c,rE,e.event)}),Cr(fi(),function(t,e){e.stop(),l(t)}),Cr(di(),l)]}function IT(t,r,i,u,e,n){return t.fold(function(){return aE.snap({sensor:qT(i-20,u-20),range:kn(e,n),output:qT(vt.some(i),vt.some(u)),extra:{td:r}})},function(t){var e=i-20,n=u-20,o=t.element.dom.getBoundingClientRect();return aE.snap({sensor:qT(e,n),range:kn(40,40),output:qT(vt.some(i-o.width/2),vt.some(u-o.height/2)),extra:{td:r}})})}function RT(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,e){var n=e.td,o=i.get(),r=n;o.exists(function(t){return zt(t,r)})||(i.set(n),u(n))},mustSnap:!0}}function NT(t){return Hm(fp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ec([aE.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),vw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}function PT(a,n){function e(t){var e=He(t);return IT(g.getOpt(n),t,e.x,e.y,e.width,e.height)}function o(t){var e=He(t);return IT(p.getOpt(n),t,e.right,e.bottom,e.width,e.height)}function r(t,i,e,u){var n=e(i);aE.snapTo(t,n),function(t){var e=i.dom.getBoundingClientRect();ve(t.element,"display");var n=Xt(Mt.fromDom(a.getBody())).dom.innerHeight,o=e[u]<0,r=e[u]>n;(o||r)&&fe(t.element,"display","none")}(t)}function i(t){return r(h,t,e,"top")}function u(t){return r(v,t,o,"bottom")}var c=Po([]),s=Po([]),t=Po(!1),l=gc(),f=gc(),d=RT(function(){return B(c.get(),e)},l,function(e){f.get().each(function(t){a.fire("TableSelectorChange",{start:e,finish:t})})}),m=RT(function(){return B(s.get(),o)},f,function(e){l.get().each(function(t){a.fire("TableSelectorChange",{start:t,finish:e})})}),g=NT(d),p=NT(m),h=Tu(g.asSpec()),v=Tu(p.asSpec());ln().deviceType.isTouch()&&(a.on("TableSelectionChange",function(e){t.get()||(Ns(n,h),Ns(n,v),t.set(!0)),l.set(e.start),f.set(e.finish),e.otherCells.each(function(t){c.set(t.upOrLeftCells),s.set(t.downOrRightCells),i(e.start),u(e.finish)})}),a.on("ResizeEditor ResizeWindow ScrollContent",function(){l.get().each(i),f.get().each(u)}),a.on("TableSelectionClear",function(){t.get()&&(Vs(h),Vs(v),t.set(!1)),l.clear(),f.clear()}))}var VT,HT,LT={name:"lineheight",text:"Line height",icon:"line-height",getOptions:function(t){return t.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" ")},hash:function(t){return r=["fixed","relative","empty"],vt.from(q_.exec(t)).bind(function(t){var e=Number(t[1]),n=t[2],o=n;return d(r,function(t){return d(Y_[t],function(t){return o===t})})?vt.some({value:e,unit:n}):vt.none()}).map(function(t){return t.value+t.unit}).getOr(t);var r},display:h,watcher:function(t,e,n){return t.formatter.formatChanged("lineheight",n,!1,{value:e}).unbind},getCurrent:function(t){return vt.from(t.queryCommandValue("LineHeight"))},setCurrent:function(t,e){return t.execCommand("LineHeight",!1,e)}},zT=function(t){return{type:"selection",root:Mt.fromDom(t.selection.getNode())}},UT={onLtr:function(){return[$a,Xa,Ya,qa,Ka,Ja,cp,sp,Mm,Am,Bm,Dm]},onRtl:function(){return[$a,Ya,Xa,Ka,qa,Ja,cp,sp,Bm,Dm,Mm,Am]}},jT={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},WT={type:"separator"},GT=function(e){function t(t){return{text:t.text,icon:t.icon,disabled:t.disabled,shortcut:t.shortcut}}var n;if(y(e))return e;switch(e.type){case"separator":return WT;case"submenu":return lt(lt({type:"nestedmenuitem"},t(e)),{getSubmenuItems:function(){var t=e.getSubmenuItems();return y(t)?t:B(t,GT)}});default:return lt(lt({type:"menuitem"},t(e)),{onAction:(n=e.onAction,function(){return n()})})}},XT=Vo([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),YT=XT.offset,qT=XT.absolute,KT=XT.fixed,JT=function(t,e,l,f,d){var n=e.getSnapPoints(t);return CT(n,l,f,d).orThunk(function(){return R(n,function(e,n){var t,o,r,i,u,a,c,s=(t=n.sensor,n.range.left,n.range.top,i=bT(l,o=f,r=d),u=bT(t,o,r),a=Math.abs(i.left-u.left),c=Math.abs(i.top-u.top),kn(a,c));return e.deltas.fold(function(){return{deltas:vt.some(s),snap:vt.some(n)}},function(t){return(s.left+s.top)/2<=(t.left+t.top)/2?{deltas:vt.some(s),snap:vt.some(n)}:e})},{deltas:vt.none(),snap:vt.none()}).snap.map(function(t){return{output:wT(t.output,l,f,d),extra:t.extra}})})},$T=function(t,e,n,o,r){return CT(e.getSnapPoints(t),n,o,r)},QT=Object.freeze({__proto__:null,snapTo:function(t,e,n,o){var r,i,u,a=e.getTarget(t.element);e.repositionTarget&&(r=Ie(jt(t.element)),i=Uk(a),me(a,xT({coord:wT((u=o).output,u.output,r,i),extra:u.extra}.coord,0,i)))}}),ZT="data-initial-z-index",tE=To("snaps",[mo("getSnapPoints"),Ju("onSensor"),mo("leftAttr"),mo("topAttr"),Eo("lazyViewport",Fn),Eo("mustSnap",!1)]),eE=[Eo("useFixed",T),mo("blockerClass"),Eo("getTarget",h),Eo("onDrag",st),Eo("repositionTarget",!0),Eo("onDrop",st),Io("getBounds",Fn),tE],nE=Object.freeze({__proto__:null,getData:function(t){return vt.from(kn(t.x,t.y))},getDelta:function(t,e){return kn(e.left-t.left,e.top-t.top)}}),oE=V(V([],eE,!0),[ta("dragger",{handlers:BT(MT)})],!1),rE=Object.freeze({__proto__:null,getData:function(t){var e,n=t.raw.touches;return 1===n.length?(e=n[0],vt.some(kn(e.clientX,e.clientY))):vt.none()},getDelta:function(t,e){return kn(e.left-t.left,e.top-t.top)}}),iE=V(V([],eE,!0),[ta("dragger",{handlers:BT(FT)})],!1),uE=V(V([],eE,!0),[ta("dragger",{handlers:BT(function(t,e,n){return V(V([],MT(t,e,n),!0),FT(t,e,n),!0)})})],!1),aE=wa({branchKey:"mode",branches:Object.freeze({__proto__:null,mouse:oE,touch:iE,mouseOrTouch:uE}),name:"dragging",active:{events:function(t,e){return t.dragger.handlers(t,e)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:vt.from(t.extra)}}},state:Object.freeze({__proto__:null,init:function(){var i=vt.none(),e=vt.none(),t=rt({});return wu({readState:t,reset:function(){i=vt.none(),e=vt.none()},update:function(r,t){return r.getData(t).bind(function(t){return e=r,n=t,o=i.map(function(t){return e.getDelta(t,n)}),i=vt.some(n),o;var e,n,o})},getStartData:function(){return e},setStartData:function(t){e=vt.some(t)}})}}),apis:QT});function cE(t,e,n){var o,r,i,u,a,c,s=Mt.fromDom(t.getContainer());J((o=t,r=e,i=n,u=xe(s),a=Ce(s),(c={}).height=t_(u+r.top,dh(o),gh(o)),i===VT.Both&&(c.width=t_(a+r.left,fh(o),mh(o))),c),function(t,e){return fe(s,e,ZO(t)),0}),t.fire("ResizeEditor")}function sE(t,e,n,o){return cE(t,kn(20*n,20*o),e),vt.some(!0)}function lE(t,e){var n,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(v=[],t.getParam("elementpath",!0,"boolean")&&v.push((g=t,h=e,(p={}).delimiter||(p.delimiter="\xbb"),{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ec([Fg.config({mode:"flow",selector:"div[role=button]"}),gd.config({disabled:h.isDisabled}),pv(),py.config({}),Rg.config({}),mm("elementPathEvents",[ou(function(r,t){g.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Fg.focusIn(r)}),g.on("NodeChange",function(t){var e,o,n=function(t){for(var e=[],n=t.length;0<n--;){var o=t[n];if(1===o.nodeType&&!function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return 1;if("bookmark"===t.getAttribute("data-mce-type"))return 1}}(o)){var r=g.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||e.push({name:r.name,element:o}),r.isPropagationStopped())break}}return e}(t.parents);0<n.length?Rg.set(r,(e=B(n||[],function(e,t){return fp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:e.name},action:function(t){g.focus(),g.selection.select(e.element),g.nodeChanged()},buttonBehaviours:ec([$v(h.isDisabled),pv()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+p.delimiter+" "}},R(e.slice(1),function(t,e){var n=t;return n.push(o),n.push(e),n},[e[0]]))):Rg.set(r,[])})})])]),components:[]})),t.hasPlugin("wordcount")&&v.push((f=t,d=e,fp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ec([$v(d.isDisabled),pv(),py.config({}),Rg.config({}),Df.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),mm("wordcount-events",[uu(function(t){var e=Df.getValue(t),n="words"===e.mode?"characters":"words";Df.setValue(t,{mode:n,count:e.count}),b(t,e.count,n)}),ou(function(n){f.on("wordCountUpdate",function(t){var e=Df.getValue(n).mode;Df.setValue(n,{mode:e,count:t.wordCount}),b(n,t.wordCount,e)})})])]),eventOrder:((m={})[Ii()]=["disabling","alloy.base.behaviour","wordcount-events"],m)}))),t.getParam("branding",!0,"boolean")&&v.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(l=dp.translate(["Powered by {0}","Tiny"]))+'">'+l+"</a>"}}),n=0<v.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:v}]:[],i=e,a=!(u=r=t).hasPlugin("autoresize"),o=(s=!1===(c=u.getParam("resize",a))?VT.None:"both"===c?VT.Both:VT.Vertical)===VT.None?vt.none():vt.some(Xm("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:i.translate("Resize")},behaviours:[aE.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,e,n){return cE(r,n,s)},blockerClass:"tox-blocker"}),Fg.config({mode:"special",onLeft:function(){return sE(r,s,-1,0)},onRight:function(){return sE(r,s,1,0)},onUp:function(){return sE(r,s,0,-1)},onDown:function(){return sE(r,s,0,1)}}),py.config({}),Vg.config({})]},i.icons)),n.concat(o.toArray()))};function b(t,e,n){return Rg.set(t,[ri(d.translate(["{0} "+n,e[n]]))])}}function fE(d){function m(){return i.bind(FO.getHeader)}function g(){return Nn.value(y)}function p(){return i.bind(function(t){return FO.getThrobber(t)}).getOrDie("Could not find throbber element")}var t,e,n,o,r=d.inline,h=r?P_:N_,v=dv(d)?q1:V1,i=vt.none(),u=ln(),a=u.browser.isIE()?["tox-platform-ie"]:[],c=u.deviceType.isTouch()?["tox-platform-touch"]:[],s=av(d),l=lv(d),f=dp.isRtl()?{attributes:{dir:"rtl"}}:{},b={attributes:((t={})[ns]=s?Oc.BottomToTop:Oc.TopToBottom,t)},y=Tu((e=zt(bn(),l)&&"grid"===ge(l,"display"),n={dom:lt({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(a).concat(c)},f),behaviours:ec([ff.config({useFixed:function(){return v.isDocked(m)}})])},o={dom:{styles:{width:document.body.clientWidth+"px"}},events:nu([Cr(Wi(),function(){fe(J.element,"width",document.body.clientWidth+"px")})])},Yo(n,e?o:{}))),x=Hm({dom:{tag:"div",classes:["tox-anchorbar"]}}),w=Lk(y,d,function(){return i.bind(function(t){return x.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),S=FO.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:w,onEscape:function(){d.focus()}}),k=iv(d),C=FO.parts.toolbar(lt({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:w.shared.providers,onEscape:function(){d.focus()},type:k,lazyToolbar:function(){return i.bind(function(t){return FO.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return m().getOrDie("Could not find header element")}},b)),O=FO.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:w.shared.providers,onEscape:function(){d.focus()},type:k}),_=FO.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),T=FO.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),E=FO.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:w}),D=d.getParam("statusbar",!0,"boolean")&&!r?vt.some(lE(d,w.shared.providers)):vt.none(),A={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_,T]},B=rv(d),M=hh(d),F=ph(d),I=FO.parts.header({dom:lt({tag:"div",classes:["tox-editor-header"]},b),components:ft([F?[S]:[],B?[O]:M?[C]:[],sv(d)?[]:[x.asSpec()]]),sticky:dv(d),editor:d,sharedBackstage:w.shared}),R=ft([s?[]:[I],r?[]:[A],s?[I]:[]]),N=ft([[{dom:{tag:"div",classes:["tox-editor-container"]},components:R}],r?[]:D.toArray(),[E]]),P=fv(d),V=lt(lt({role:"application"},dp.isRtl()?{dir:"rtl"}:{}),P?{"aria-hidden":"true"}:{}),H=Tu(FO.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(r?["tox-tinymce-inline"]:[]).concat(s?["tox-tinymce--toolbar-bottom"]:[]).concat(c).concat(a),styles:lt({visibility:"hidden"},P?{opacity:"0",border:"0"}:{}),attributes:V},components:N,behaviours:ec([pv(),gd.config({disableClass:"tox-tinymce--disabled"}),Fg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),i=vt.some(H);d.shortcuts.add("alt+F9","focus menubar",function(){FO.focusMenubar(H)}),d.shortcuts.add("alt+F10","focus toolbar",function(){FO.focusToolbar(H)}),d.addCommand("ToggleToolbarDrawer",function(){FO.toggleToolbarDrawer(H)}),d.addQueryStateHandler("ToggleToolbarDrawer",function(){return FO.isToolbarDrawerToggled(H)});var L,z,U,j,W,G,X,Y,q,K=ey(H),J=ey(y);function $(){var t,e,n,o,r,i,u=ZO((o=sh(n=e=d),r=dh(n),i=gh(n),QO(o).map(function(t){return t_(t,r,i)}).getOr(sh(e)))),a=ZO(e_(t=d).getOr(lh(t)));return d.inline||(he("div","width",a)&&fe(H.element,"width",a),he("div","height",u)?fe(H.element,"height",u):fe(H.element,"height","200px")),u}return L=d,z=K,U=J,j=Sa(),W=pc(j,"touchstart",tt),G=pc(j,"touchmove",function(t){return Q(zi(),t)}),X=pc(j,"touchend",function(t){return Q(Ui(),t)}),Y=pc(j,"mousedown",tt),q=pc(j,"mouseup",function(t){0===t.raw.button&&Z(wf(),{target:t.target})}),L.on("PostRender",function(){L.on("click",et),L.on("tap",et),L.on("mouseup",nt),L.on("mousedown",ot),L.on("ScrollWindow",rt),L.on("ResizeWindow",it),L.on("ResizeEditor",ut),L.on("AfterProgressState",at),L.on("DismissPopups",ct)}),L.on("remove",function(){L.off("click",et),L.off("tap",et),L.off("mouseup",nt),L.off("mousedown",ot),L.off("ScrollWindow",rt),L.off("ResizeWindow",it),L.off("ResizeEditor",ut),L.off("AfterProgressState",at),L.off("DismissPopups",ct),Y.unbind(),W.unbind(),G.unbind(),X.unbind(),q.unbind()}),L.on("detach",function(){zs(z),zs(U),z.destroy(),U.destroy()}),{mothership:K,uiMothership:J,backstage:w,renderUI:function(){var o,r;v.setup(d,w.shared,m),eT(d,w),mT(d,g,w),r=(o=d).ui.registry.getAll().sidebars,St(Ct(r),function(e){function n(){return mt(vt.from(o.queryCommandValue("ToggleSidebar")),e)}var t=r[e];o.ui.registry.addToggleButton(e,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,e),t.setActive(n())},onSetup:function(t){function e(){return t.setActive(n())}return o.on("ToggleSidebar",e),function(){o.off("ToggleSidebar",e)}}})}),SC(d,p,w.shared),dt(d.getParam("toolbar_groups",{},"object"),function(t,e){d.ui.registry.addGroupToolbarButton(e,t)});var t,e=d.ui.registry.getAll(),n=e.buttons,i=e.menuItems,u=e.contextToolbars,a=e.sidebars,c=vh(d),s={menuItems:i,menus:(t=d.getParam("menu"))?dt(t,function(t){return lt(lt({},t),{items:t.items})}):{},menubar:d.getParam("menubar"),toolbar:c.getOrThunk(function(){return d.getParam("toolbar",!0)}),allowToolbarGroups:k===bh.floating,buttons:n,sidebar:a};m_(d,u,y,{backstage:w}),PT(d,y);var l=d.getElement(),f=$();return h.render(d,{mothership:K,uiMothership:J,outerContainer:H},s,w,{targetNode:l,height:f})},getUi:function(){return{channels:{broadcastAll:J.broadcast,broadcastOn:J.broadcastOn,register:st}}}};function Q(e,n){St([z,U],function(t){t.broadcastEvent(e,n)})}function Z(e,n){St([z,U],function(t){t.broadcastOn([e],n)})}function tt(t){return Z(yf(),{target:t.target})}function et(t){return Z(yf(),{target:Mt.fromDom(t.target)})}function nt(t){0===t.button&&Z(wf(),{target:Mt.fromDom(t.target)})}function ot(){St(L.editorManager.get(),function(t){L!==t&&t.fire("DismissPopups",{relatedTarget:L})})}function rt(t){return Q(ji(),Mc(t))}function it(t){Z(xf(),{}),Q(Wi(),Mc(t))}function ut(){return Z(xf(),{})}function at(t){t.state&&Z(yf(),{target:Mt.fromDom(L.getContainer())})}function ct(t){Z(yf(),{target:Mt.fromDom(t.relatedTarget.getContainer())})}}function dE(e){return ar("items","items",jn(),eo(io(function(t){return ao("Checking item of "+e,AD,t).fold(function(t){return Nn.error(ur(t))},function(t){return Nn.value(t)})})))}function mE(t){return y(t.type)&&y(t.name)}function gE(t){return{internalDialog:co(ao("dialog",VD,t)),dataValidator:(e=H(F(zD(t),mE),function(e){return vt.from(UD[e.type]).fold(function(){return[]},function(t){return[go(e.name,t)]})}),$o(e)),initialData:t.initialData};var e}function pE(t){var n=[],o={};return J(t,function(t,e){t.fold(function(){n.push(e)},function(t){o[e]=t})}),0<n.length?Nn.error(n):Nn.value(o)}function hE(t,e){fe(t,"height",e+"px"),ln().browser.isIE()?ve(t,"flex-basis"):fe(t,"flex-basis",e+"px")}function vE(t,d,e){Fu(t,'[role="dialog"]').each(function(f){Iu(f,'[role="tablist"]').each(function(l){e.get().map(function(t){return fe(d,"height","0"),fe(d,"flex-basis","0"),Math.min(t,(n=d,o=l,r=Gt(e=f).dom,i="fixed"===ge(Fu(e,".tox-dialog-wrap").getOr(e),"position")?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight),u=xe(n),a=n.dom.offsetLeft>=o.dom.offsetLeft+Ce(o)?Math.max(xe(o),u):u,c=parseInt(ge(e,"margin-top"),10)||0,s=parseInt(ge(e,"margin-bottom"),10)||0,i-(xe(e)+c+s-a)));var e,n,o,r,i,u,a,c,s}).each(function(t){hE(d,t)})})})}function bE(t){return Iu(t,'[role="tabpanel"]')}function yE(t,n){function o(t){var e=pE(Df.getValue(t)).getOr({}),n=i.get(),o=Yo(n,e);i.set(o)}function r(t){var e=i.get();Df.setValue(t,e)}var u,a,i=Po({}),c=Po(null),e=B(t.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:n.shared.providers.translate(t.title)},view:function(){return[nS.sketch(function(e){return{dom:{tag:"div",classes:["tox-form"]},components:B(t.items,function(t){return hk(e,t,n)}),formBehaviours:ec([Fg.config({mode:"acyclic",useTabstopAt:O(p0)}),mm("TabView.form.events",[ou(r),ru(o)]),ic.config({channels:lr([{key:QD,value:{onReceive:o}},{key:ZD,value:{onReceive:r}}])})])}})]}}}),s=(u=e,a=gc(),{extraEvents:[ou(function(t){var o=t.element;bE(o).each(function(e){var n;fe(e,"visibility","hidden"),t.getSystem().getByDom(e).toOptional().each(function(t){var o,r,i;Y(G((r=e,i=t,B(o=u,function(t,e){Rg.set(i,o[e].view());var n=r.dom.getBoundingClientRect();return Rg.set(i,[]),n.height})),function(t,e){return e<t?-1:t<e?1:0})).fold(a.clear,a.set)}),vE(o,e,a),ve(e,"visibility"),n=t,Y(u).each(function(t){return $D.showTab(n,t.value)}),lp.requestAnimationFrame(function(){vE(o,e,a)})})}),Cr(Wi(),function(t){var e=t.element;bE(e).each(function(t){vE(e,t,a)})}),Cr(Ty,function(t,e){var r=t.element;bE(r).each(function(e){var t=Oa(gn(e));fe(e,"visibility","hidden");var n=pe(e,"height").map(function(t){return parseInt(t,10)});ve(e,"height"),ve(e,"flex-basis");var o=e.dom.getBoundingClientRect().height;n.forall(function(t){return t<o})?(a.set(o),vE(r,e,a)):n.each(function(t){hE(e,t)}),ve(e,"visibility"),t.each(ka)})})],selectFirst:!1});return $D.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,e,n){var o=Df.getValue(e);br(t,_y,{name:o,oldName:c.get()}),c.set(o)},tabs:e,components:[$D.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[YD.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ec([py.config({})])}),$D.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:s.selectFirst,tabSectionBehaviours:ec([mm("tabpanel",s.extraEvents),Fg.config({mode:"acyclic"}),cd.config({find:function(t){return Y($D.getViewItems(t))}}),Df.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([QD],{}),i.get()},setValue:function(t,e){i.set(e),t.getSystem().broadcastOn([ZD],{})}}})])})}function xE(t,e,r,n){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:lt(lt({},e.map(function(t){return{id:t}}).getOr({})),n?{"aria-live":"polite"}:{})},components:[],behaviours:ec([cS(0),k_.config({channel:nA,updateState:function(t,e){return vt.some({isTabPanel:function(){return"tabpanel"===e.body.type}})},renderComponents:function(t){return"tabpanel"!==t.body.type?[(n=t.body,o=r,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(e=Hm(nS.sketch(function(e){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:B(n.items,function(t){return hk(e,t,o)})}}))).asSpec()]}],behaviours:ec([Fg.config({mode:"acyclic",useTabstopAt:O(p0)}),aS(e),mS(e,{postprocess:function(t){return pE(t).fold(function(t){return console.error(t),{}},h)}})])})]:[yE(t.body,r)];var n,o,e},initialData:t})])}}(HT=VT=VT||{})[HT.None=0]="None",HT[HT.Both=1]="Both",HT[HT.Vertical=2]="Vertical";var wE,SE=rt([mo("lazySink"),wo("dragBlockClass"),Io("getBounds",Fn),Eo("useTabstopAt",D),Eo("eventOrder",{}),Zs("modalBehaviours",[Fg]),$u("onExecute"),Zu("onEscape")]),kE={sketch:h},CE=rt([Qf({name:"draghandle",overrides:function(t,e){return{behaviours:ec([aE.config({mode:"mouse",getTarget:function(t){return Fu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(e,null,2)).message),getBounds:t.getDragBounds})])}}}),Jf({schema:[mo("dom")],name:"title"}),Jf({factory:kE,schema:[mo("dom")],name:"close"}),Jf({factory:kE,schema:[mo("dom")],name:"body"}),Qf({factory:kE,schema:[mo("dom")],name:"footer"}),$f({factory:{sketch:function(t,e){return lt(lt({},t),{dom:e.dom,components:e.components})}},schema:[Eo("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Eo("components",[])],name:"blocker"})]),OE=Cl({name:"ModalDialog",configFields:SE(),partFields:CE(),factory:function(a,t,e,r){var n,i=gc(),o=Ir("modal-events"),u=lt(lt({},a.eventOrder),((n={})[Gi()]=[o].concat(a.eventOrder["alloy.system.attached"]||[]),n));return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(t){i.set(t);var e=a.lazySink(t).getOrDie(),n=r.blocker(),o=e.getSystem().build(lt(lt({},n),{components:n.components.concat([Eu(t)]),behaviours:ec([Vg.config({}),mm("dialog-blocker-events",[Ar(bi(),function(){Fg.focusIn(t)})])])}));Ns(e,o),Fg.focusIn(t)},hide:function(e){i.clear(),Yt(e.element).each(function(t){e.getSystem().getByDom(t).each(function(t){Vs(t)})})},getBody:function(t){return gl(t,a,"body")},getFooter:function(t){return gl(t,a,"footer")},setIdle:function(t){fO.unblock(t)},setBusy:function(t,e){fO.block(t,e)}},eventOrder:u,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:el(a.modalBehaviours,[Rg.config({}),Fg.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),fO.config({getRoot:i.get}),mm(o,[ou(function(t){var e,n,o,r=t.element,i=gl(t,a,"title").element,u=ue(r,"id").fold(function(){var t=Ir("dialog-label");return oe(i,"id",t),t},h);oe(r,"aria-labelledby",u),e=t.element,n=gl(t,a,"body").element,o=vt.from(ie(e,"id")).fold(function(){var t=Ir("dialog-describe");return oe(n,"id",t),t},h),oe(e,"aria-describedby",o)})])])}},apis:{show:function(t,e){t.show(e)},hide:function(t,e){t.hide(e)},getBody:function(t,e){return t.getBody(e)},getFooter:function(t,e){return t.getFooter(e)},setBusy:function(t,e,n){t.setBusy(e,n)},setIdle:function(t,e){t.setIdle(e)}}}),_E=$o([po("type"),po("name")].concat(zh)),TE=nr,EE=[ar("name","name",Wn(function(){return Ir("button-name")}),er),Co("icon"),Mo("align","end",["start","end"]),Fo("primary",!1),Fo("disabled",!1)],DE=V(V([],EE,!0),[po("text")],!1),AE=V([ho("type",["submit","cancel","custom"])],DE,!0),BE=lo("type",{submit:AE,cancel:AE,custom:AE,menu:V([ho("type",["menu"]),Co("text"),Co("tooltip"),Co("icon"),xo("items",_E)],EE,!0)}),ME=[po("type"),po("text"),ho("level",["info","warn","error","success"]),po("icon"),Eo("url","")],FE=$o(ME),IE=[po("type"),po("text"),Fo("disabled",!1),Fo("primary",!1),ar("name","name",Wn(function(){return Ir("button-name")}),er),Co("icon"),Fo("borderless",!1)],RE=$o(IE),NE=[po("type"),po("name"),po("label"),Fo("disabled",!1)],PE=$o(NE),VE=nr,HE=[po("type"),po("name")],LE=HE.concat([Co("label")]),zE=LE.concat([Eo("columns","auto")]),UE=$o(zE),jE=Qo([po("value"),po("text"),po("icon")]),WE=$o(LE),GE=er,XE=$o(LE),YE=er,qE=HE.concat([Bo("tag","textarea"),po("scriptId"),po("scriptUrl"),Do("settings",void 0,ir)]),KE=HE.concat([Bo("tag","textarea"),vo("init")]),JE=io(function(t){return ao("customeditor.old",to(KE),t).orThunk(function(){return ao("customeditor.new",to(qE),t)})}),$E=er,QE=$o(LE),ZE=eo(Jo),tD=[po("type"),po("html"),Mo("presets","presentation",["presentation","document"])],eD=$o(tD),nD=LE.concat([Fo("sandboxed",!0)]),oD=$o(nD),rD=er,iD=LE.concat([go("currentState",$o([mo("blob"),po("url")]))]),uD=$o(iD),aD=LE.concat([Co("inputMode"),Co("placeholder"),Fo("maximized",!1),Fo("disabled",!1)]),cD=$o(aD),sD=er,lD=[po("text"),po("value")],fD=[po("text"),xo("items",(wE=Rt(function(){return dD}),{extract:function(t,e){return wE().extract(t,e)},toString:function(){return wE().toString()}}))],dD=no([$o(lD),$o(fD)]),mD=LE.concat([xo("items",dD),Fo("disabled",!1)]),gD=$o(mD),pD=er,hD=LE.concat([yo("items",[po("text"),po("value")]),Ao("size",1),Fo("disabled",!1)]),vD=$o(hD),bD=er,yD=LE.concat([Fo("constrain",!0),Fo("disabled",!1)]),xD=$o(yD),wD=$o([po("width"),po("height")]),SD=[po("type"),xo("header",er),xo("cells",eo(er))],kD=$o(SD),CD=LE.concat([Co("placeholder"),Fo("maximized",!1),Fo("disabled",!1)]),OD=$o(CD),_D=er,TD=LE.concat([Mo("filetype","file",["image","media","file"]),Eo("disabled",!1)]),ED=$o(TD),DD=$o([po("value"),Eo("meta",{})]),AD=Zn(function(){return ro("type",{alertbanner:FE,bar:$o((n=dE("bar"),[po("type"),n])),button:RE,checkbox:PE,colorinput:WE,colorpicker:XE,dropzone:QE,grid:$o((t=dE("grid"),[po("type"),go("columns",tr),t])),iframe:oD,input:cD,listbox:gD,selectbox:vD,sizeinput:xD,textarea:OD,urlinput:ED,customeditor:JE,htmlpanel:eD,imagetools:uD,collection:UE,label:$o((e=dE("label"),[po("type"),po("label"),e])),table:kD,panel:MD});var t,e,n}),BD=[po("type"),Eo("classes",[]),xo("items",AD)],MD=$o(BD),FD=[ar("name","name",Wn(function(){return Ir("tab-name")}),er),po("title"),xo("items",AD)],ID=[po("type"),yo("tabs",FD)],RD=$o(ID),ND=DE,PD=BE,VD=$o([po("title"),go("body",ro("type",{panel:MD,tabpanel:RD})),Bo("size","normal"),xo("buttons",PD),Eo("initialData",{}),Io("onAction",st),Io("onChange",st),Io("onSubmit",st),Io("onClose",st),Io("onCancel",st),Eo("onTabChange",st)]),HD=$o(V([ho("type",["cancel","custom"])],ND,!0)),LD=$o([po("title"),po("url"),ko("height"),ko("width"),_o("buttons",HD),Io("onAction",st),Io("onCancel",st),Io("onClose",st),Io("onMessage",st)]),zD=function(t){return x(t)?[t].concat(H(Z(t),zD)):c(t)?H(t,zD):[]},UD={checkbox:VE,colorinput:GE,colorpicker:YE,dropzone:ZE,input:sD,iframe:rD,sizeinput:wD,selectbox:bD,listbox:pD,size:wD,textarea:_D,urlinput:DD,customeditor:$E,collection:jE,togglemenuitem:TE},jD={open:function(t,e){var n=gE(e);return t(n.internalDialog,n.initialData,n.dataValidator)},openUrl:function(t,e){return t(co(ao("dialog",LD,e)))},redial:gE},WD=kl({name:"TabButton",configFields:[Eo("uid",void 0),mo("value"),ar("dom","dom",Yn(function(){return{attributes:{role:"tab",id:Ir("aria"),"aria-selected":"false"}}}),Zo()),wo("action"),Eo("domModification",{}),Zs("tabButtonBehaviours",[Vg,Fg,Df]),mo("view")],factory:function(t,e){return{uid:t.uid,dom:t.dom,components:t.components,events:Sm(t.action),behaviours:el(t.tabButtonBehaviours,[Vg.config({}),Fg.config({mode:"execution",useSpace:!0,useEnter:!0}),Df.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),GD=rt([mo("tabs"),mo("dom"),Eo("clickToDismiss",!1),Zs("tabbarBehaviours",[hd,Fg]),qu(["tabClass","selectedClass"])]),XD=rt([Zf({factory:WD,name:"tabs",unit:"tab",overrides:function(o){return{action:function(t){var e=t.getSystem().getByUid(o.uid).getOrDie(),n=hd.isHighlighted(e,t);(n&&o.clickToDismiss?function(t,e){hd.dehighlight(t,e),br(t,Qi(),{tabbar:t,button:e})}:n?st:function(t,e){hd.highlight(t,e),br(t,$i(),{tabbar:t,button:e})})(e,t)},domModification:{classes:[o.markers.tabClass]}}}})]),YD=Cl({name:"Tabbar",configFields:GD(),partFields:XD(),factory:function(t,e,n,o){return{uid:t.uid,dom:t.dom,components:e,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:el(t.tabbarBehaviours,[hd.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,e){oe(e.element,"aria-selected","true")},onDehighlight:function(t,e){oe(e.element,"aria-selected","false")}}),Fg.config({mode:"flow",getInitial:function(t){return hd.getHighlighted(t).map(function(t){return t.element})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),qD=kl({name:"Tabview",configFields:[Zs("tabviewBehaviours",[Rg])],factory:function(t,e){return{uid:t.uid,dom:t.dom,behaviours:el(t.tabviewBehaviours,[Rg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),KD=rt([Eo("selectFirst",!0),Ju("onChangeTab"),Ju("onDismissTab"),Eo("tabs",[]),Zs("tabSectionBehaviours",[])]),JD=rt([Jf({factory:YD,schema:[mo("dom"),bo("markers",[mo("tabClass"),mo("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),Jf({factory:qD,name:"tabview"})]),$D=Cl({name:"TabSection",configFields:KD(),partFields:JD(),factory:function(i,t,e,n){function o(t,e){ml(t,i,"tabbar").each(function(t){e(t).each(yr)})}return{uid:i.uid,dom:i.dom,components:t,behaviours:tl(i.tabSectionBehaviours),events:nu(ft([i.selectFirst?[ou(function(t,e){o(t,hd.getFirst)})]:[],[Cr($i(),function(t,e){var o=e.event.button,r=Df.getValue(o);ml(o,i,"tabview").each(function(n){N(i.tabs,function(t){return t.value===r}).each(function(t){var e=t.view();ue(o.element,"id").each(function(t){oe(n.element,"aria-labelledby",t)}),Rg.set(n,e),i.onChangeTab(n,o,e)})})}),Cr(Qi(),function(t,e){var n=e.event.button;i.onDismissTab(t,n)})]])),apis:{getViewItems:function(t){return ml(t,i,"tabview").map(function(t){return Rg.contents(t)}).getOr([])},showTab:function(t,n){o(t,function(e){return N(hd.getCandidates(e),function(t){return Df.getValue(t)===n}).filter(function(t){return!hd.isHighlighted(e,t)})})}}}},apis:{getViewItems:function(t,e){return t.getViewItems(e)},showTab:function(t,e,n){t.showTab(e,n)}}}),QD="send-data-to-section",ZD="send-data-to-view",tA=Ir("update-dialog"),eA=Ir("update-title"),nA=Ir("update-body"),oA=Ir("update-footer"),rA=Ir("body-send-message");function iA(t){return(iA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uA(t,e){return(uA=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function aA(t,e,n){return(aA=function(){if("undefined"!=typeof Reflect&&Reflect.construct&&!Reflect.construct.sham){if("function"==typeof Proxy)return 1;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),1}catch(t){return}}}()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);var r=new(Function.bind.apply(t,o));return n&&uA(r,n.prototype),r}).apply(null,arguments)}function cA(t){return function(t){if(Array.isArray(t))return sA(t)}(t)||function(){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}()||function(t){if(t){if("string"==typeof t)return sA(t,void 0);var e=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(e="Object"===e&&t.constructor?t.constructor.name:e)||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?sA(t,void 0):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sA(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var lA,fA=Object.hasOwnProperty,dA=Object.setPrototypeOf,mA=Object.isFrozen,gA=Object.getPrototypeOf,pA=Object.getOwnPropertyDescriptor,hA=Object.freeze,vA=Object.seal,bA=Object.create,yA="undefined"!=typeof Reflect&&Reflect,xA=yA.apply||function(t,e,n){return t.apply(e,n)},hA=hA||function(t){return t},vA=vA||function(t){return t},wA=yA.construct||function(t,e){return aA(t,cA(e))},SA=MA(Array.prototype.forEach),kA=MA(Array.prototype.pop),CA=MA(Array.prototype.push),OA=MA(String.prototype.toLowerCase),_A=MA(String.prototype.match),TA=MA(String.prototype.replace),EA=MA(String.prototype.indexOf),DA=MA(String.prototype.trim),AA=MA(RegExp.prototype.test),BA=(lA=TypeError,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return wA(lA,e)});function MA(r){return function(t){for(var e=arguments.length,n=new Array(1<e?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];return xA(r,t,n)}}function FA(t,e){dA&&dA(t,null);for(var n=e.length;n--;){var o,r=e[n];"string"!=typeof r||(o=OA(r))!==r&&(mA(e)||(e[n]=o),r=o),t[r]=!0}return t}function IA(t){var e,n=bA(null);for(e in t)xA(fA,t,[e])&&(n[e]=t[e]);return n}function RA(t,e){for(;null!==t;){var n=pA(t,e);if(n){if(n.get)return MA(n.get);if("function"==typeof n.value)return MA(n.value)}t=gA(t)}return function(t){return console.warn("fallback value for",t),null}}var NA=hA(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),PA=hA(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),VA=hA(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),HA=hA(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),LA=hA(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),zA=hA(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),UA=hA(["#text"]),jA=hA(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),WA=hA(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),GA=hA(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),XA=hA(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),YA=vA(/\{\{[\w\W]*|[\w\W]*\}\}/gm),qA=vA(/<%[\w\W]*|[\w\W]*%>/gm),KA=vA(/^data-[\-\w.\u00B7-\uFFFF]/),JA=vA(/^aria-[\-\w]+$/),$A=vA(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),QA=vA(/^(?:\w+script|data):/i),ZA=vA(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),tB=vA(/^html$/i);function eB(t,e){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,e]}}function nB(t,e){return OE.parts.close(fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close")}},action:t,buttonBehaviours:ec([py.config({})])}))}function oB(){return OE.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function rB(t,e){return OE.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:xC("<p>"+(n=e.translate(t),_B().sanitize(n))+"</p>")}]}]});var n}function iB(t){return OE.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})}function uB(t,e){return[ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:e})]}function aB(e){var t,n="tox-dialog",o=n+"-wrap",r=o+"__backdrop",i=n+"__disable-scroll";return OE.sketch({lazySink:e.lazySink,onEscape:function(t){return e.onEscape(t),vt.some(!0)},useTabstopAt:function(t){return!p0(t)},dom:{tag:"div",classes:[n].concat(e.extraClasses),styles:lt({position:"relative"},e.extraStyles)},components:V([e.header,e.body],e.footer.toArray(),!0),parts:{blocker:{dom:xC('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:TB?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:ec(V([Vg.config({}),mm("dialog-events",e.dialogEvents.concat([Ar(bi(),function(t,e){Fg.focusIn(t)})])),mm("scroll-lock",[ou(function(){Yr(bn(),i)}),ru(function(){qr(bn(),i)})])],e.extraBehaviours,!0)),eventOrder:lt(((t={})[Ii()]=["dialog-events"],t[Gi()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Xi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),e.eventOrder)})}function cB(t){return fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[Xm("close",{tag:"div",classes:["tox-icon"]},t.icons)],action:function(t){vr(t,wy)}})}function sB(t,e,n){function o(t){return[ri(n.translate(t.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:lt({},e.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:ec([k_.config({channel:eA,renderComponents:o})])}}function lB(){return{dom:xC('<div class="tox-dialog__draghandle"></div>')}}function fB(t,e){return n={title:e.shared.providers.translate(t),draggable:e.dialog.isDraggableModal()},o=e.shared.providers,r=OE.parts.title(sB(n,vt.none(),o)),i=OE.parts.draghandle(lB()),u=OE.parts.close(cB(o)),a=[r].concat(n.draggable?[i]:[]).concat([u]),ly.sketch({dom:xC('<div class="tox-dialog__header"></div>'),components:a});var n,o,r,i,u,a}function dB(t,e,n){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":n.translate(t)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:e,components:[{dom:xC('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}}function mB(t,o,e){return{onClose:function(){return e.closeWindow()},onBlock:function(n){OE.setBusy(t(),function(t,e){return dB(n.message,e,o)})},onUnblock:function(){OE.setIdle(t())}}}function gB(t,e,n,o){var r;return Tu(aB(lt(lt({},t),{lazySink:o.shared.getSink,extraBehaviours:V([k_.config({channel:tA,updateState:function(t,e){return vt.some(e)},initialData:e}),pS({})],t.extraBehaviours,!0),onEscape:function(t){vr(t,wy)},dialogEvents:n,eventOrder:((r={})[Fi()]=[k_.name(),ic.name()],r[Gi()]=["scroll-lock",k_.name(),"messages","dialog-events","alloy.base.behaviour"],r[Xi()]=["alloy.base.behaviour","dialog-events","messages",k_.name(),"scroll-lock"],r)})))}function pB(t){return B(t,function(t){return"menu"===t.type?(n=B((e=t).items,function(t){var e=Po(!1);return lt(lt({},t),{storage:e})}),lt(lt({},e),{items:n})):t;var e,n})}function hB(t){return R(t,function(t,e){return"menu"!==e.type?t:R(e.items,function(t,e){return t[e.name]=e.storage,t},t)},{})}function vB(t,n){return[Er(bi(),g0),t(xy,function(t,e){n.onClose(),e.onClose()}),t(wy,function(t,e,n,o){e.onCancel(t),vr(o,xy)}),Cr(Oy,function(t,e){return n.onUnblock()}),Cr(Cy,function(t,e){return n.onBlock(e.event)})]}function bB(t,e){function n(t,e){return ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:B(e,function(t){return t.memento.asSpec()})})}var o=M(e.map(function(t){return t.footerButtons}).getOr([]),function(t){return"start"===t.align});return[n("start",o.pass),n("end",o.fail)]}function yB(t,n){return{dom:xC('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ec([k_.config({channel:oA,initialData:t,updateState:function(t,e){var r=B(e.buttons,function(t){var e=Hm(Q0(t,t.type,n));return{name:t.name,align:t.align,memento:e}});return vt.some({lookupByName:function(t,e){return n=t,o=e,N(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(n)});var n,o},footerButtons:r})},renderComponents:bB})])}}function xB(t,e){return OE.parts.footer(yB(t,e))}function wB(e,n){if(e.getRoot().getSystem().isConnected()){var o=cd.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return nS.getField(o,n).fold(function(){var t=e.getFooter();return k_.getState(t).get().bind(function(t){return t.lookupByName(o,n)})},function(t){return vt.some(t)})}return vt.none()}function SB(c,o,s){function t(t){var e=c.getRoot();e.getSystem().isConnected()&&t(e)}var l={getData:function(){var t=c.getRoot(),e=t.getSystem().isConnected()?c.getFormWrapper():t,n=Df.getValue(e),o=dt(s,function(t){return t.get()});return lt(lt({},n),o)},setData:function(a){t(function(t){var e,n,o=l.getData(),r=lt(lt({},o),a),i=(e=r,n=c.getRoot(),k_.getState(n).get().map(function(t){return co(ao("data",t.dataValidator,e))}).getOr(e)),u=c.getFormWrapper();Df.setValue(u,i),J(s,function(t,e){Tt(r,e)&&t.set(r[e])})})},disable:function(t){wB(c,t).each(gd.disable)},enable:function(t){wB(c,t).each(gd.enable)},focus:function(t){wB(c,t).each(Vg.focus)},block:function(e){if(!y(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){br(t,Cy,{message:e})})},unblock:function(){t(function(t){vr(t,Oy)})},showTab:function(n){t(function(t){var e=c.getBody();k_.getState(e).get().exists(function(t){return t.isTabPanel()})&&cd.getCurrent(e).each(function(t){$D.showTab(t,n)})})},redial:function(n){t(function(t){var e=o(n);t.getSystem().broadcastOn([tA],e),t.getSystem().broadcastOn([eA],e.internalDialog),t.getSystem().broadcastOn([nA],e.internalDialog),t.getSystem().broadcastOn([oA],e.internalDialog),l.setData(e.initialData)})},close:function(){t(function(t){vr(t,xy)})}};return l}function kB(t){return x(t)&&-1!==MB.indexOf(t.mceAction)}function CB(o,t,r,e){var n,i,u,a=fB(o.title,e),c=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[d0({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:ec([py.config({}),Vg.config({})])})]}],behaviours:ec([Fg.config({mode:"acyclic",useTabstopAt:O(p0)})])},OE.parts.body(i)),s=o.buttons.bind(function(t){return 0===t.length?vt.none():vt.some(xB({buttons:t},e))}),l=EB(function(){return v},mB(function(){return h},e.shared.providers,t)),f=lt(lt({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),d=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],m=new BB(o.url,{base_uri:new BB(window.location.href)}),g=m.protocol+"://"+m.host+(m.port?":"+m.port:""),p=mc(),h=gB({header:a,body:c,footer:s,extraClasses:d,extraBehaviours:[mm("messages",[ou(function(){var t=pc(Mt.fromDom(window),"message",function(t){var e,n;m.isSameOrigin(new BB(t.raw.origin))&&(kB(e=t.raw.data)?function(t,e,n){switch(n.mceAction){case"insertContent":t.insertContent(n.content);break;case"setContent":t.setContent(n.content);break;case"execCommand":var o=!!w(n.ui)&&n.ui;t.execCommand(n.cmd,o,n.value);break;case"close":e.close();break;case"block":e.block(n.message);break;case"unblock":e.unblock()}}(r,v,e):!kB(n=e)&&x(n)&&Tt(n,"mceAction")&&o.onMessage(v,e))});p.set(t)}),ru(p.clear)]),ic.config({channels:((n={})[rA]={onReceive:function(t,e){Iu(t.element,"iframe").each(function(t){t.dom.contentWindow.postMessage(e,g)})}},n)})],extraStyles:f},o,l,e),v={block:function(e){if(!y(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");b(function(t){br(t,Cy,{message:e})})},unblock:function(){b(function(t){vr(t,Oy)})},close:function(){b(function(t){vr(t,xy)})},sendMessage:function(e){b(function(t){t.getSystem().broadcastOn([rA],e)})}};function b(t){u.getSystem().isConnected()&&t(u)}return{dialog:u=h,instanceApi:v}}function OB(t){function o(t,y){return jD.open(function(t,e,n){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=e,b=(r={redial:jD.redial,closeWindow:function(){OE.hide(b.dialog),y(b.instanceApi)}},i=R,c=fB((o={dataValidator:n,initialData:v,internalDialog:t}).internalDialog.title,i),u=i,a=xE({body:o.internalDialog.body},vt.none(),u,!1),s=OE.parts.body(a),l=pB(o.internalDialog.buttons),f=hB(l),d=xB({buttons:l},i),m=DB(function(){return h},mB(function(){return p},i.shared.providers,r),i.shared.getSink),g=function(){switch(o.internalDialog.size){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}}(),p=gB({header:c,body:s,footer:vt.some(d),extraClasses:g,extraBehaviours:[],extraStyles:{}},o,m,i),h=SB({getRoot:rt(p),getBody:function(){return OE.getBody(p)},getFooter:function(){return OE.getFooter(p)},getFormWrapper:function(){var t=OE.getBody(p);return cd.getCurrent(t).getOr(t)}},r.redial,f),{dialog:p,instanceApi:h});return OE.show(b.dialog),b.instanceApi.setData(v),b.instanceApi},t)}function r(t,M,F,I){return jD.open(function(t,e,n){function o(){return E.on(function(t){up.reposition(t),j1.refresh(t)})}var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T=co(ao("data",n,e)),E=gc(),D=R.shared.header.isPositionedAtTop(),A=(i={dataValidator:n,initialData:T,internalDialog:t},u={redial:jD.redial,closeWindow:function(){E.on(up.hide),N.off("ResizeEditor",o),E.clear(),F(A.instanceApi)}},a=R,c=I,v=Ir("dialog-label"),b=Ir("dialog-content"),y=Hm((f={title:i.internalDialog.title,draggable:!0},d=v,m=a.shared.providers,ly.sketch({dom:xC('<div class="tox-dialog__header"></div>'),components:[sB(f,vt.some(d),m),lB(),cB(m)],containerBehaviours:ec([aE.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Ru(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),x=Hm((g={body:i.internalDialog.body},p=a,h=c,xE(g,vt.some(b),p,h))),w=pB(i.internalDialog.buttons),S=hB(w),k=Hm(AB({buttons:w},a)),C=DB(function(){return _},{onBlock:function(n){fO.block(O,function(t,e){return dB(n.message,e,a.shared.providers)})},onUnblock:function(){fO.unblock(O)},onClose:function(){return u.closeWindow()}},a.shared.getSink),O=Tu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((s={role:"dialog"})["aria-labelledby"]=v,s["aria-describedby"]=b,s)},eventOrder:((l={})[Fi()]=[k_.name(),ic.name()],l[Ii()]=["execute-on-form"],l[Gi()]=["reflecting","execute-on-form"],l),behaviours:ec([Fg.config({mode:"cyclic",onEscape:function(t){return vr(t,xy),vt.some(!0)},useTabstopAt:function(t){return!p0(t)&&("button"!==Ft(t)||"disabled"!==ie(t,"disabled"))}}),k_.config({channel:tA,updateState:function(t,e){return vt.some(e)},initialData:i}),Vg.config({}),mm("execute-on-form",C.concat([Ar(bi(),function(t,e){Fg.focusIn(t)})])),fO.config({getRoot:function(){return vt.some(O)}}),Rg.config({}),pS({})]),components:[y.asSpec(),x.asSpec(),k.asSpec()]}),_=SB({getRoot:rt(O),getFooter:function(){return k.get(O)},getBody:function(){return x.get(O)},getFormWrapper:function(){var t=x.get(O);return cd.getCurrent(t).getOr(t)}},u.redial,S),{dialog:O,instanceApi:_}),B=Tu(up.sketch(lt(lt({lazySink:R.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},D?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:ec(V([mm("window-manager-inline-events",[Cr(Yi(),function(t,e){vr(A.dialog,wy)})])],(r=N,P&&D?[]:[j1.config({contextual:{lazyContext:function(){return vt.some(Mn(Mt.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]),!0)),isExtraPart:function(t,e){return Yb(n=e,".tox-alert-dialog")||Yb(n,".tox-confirm-dialog");var n}})));return E.set(B),up.showWithin(B,Eu(A.dialog),{anchor:M},vt.some(bn())),P&&D||(j1.refresh(B),N.on("ResizeEditor",o)),A.instanceApi.setData(T),Fg.focusIn(A.dialog),A.instanceApi},t)}var c,s,l,f,R=t.backstage,N=t.editor,P=dv(N),n=(s=(c=t).backstage.shared,{open:function(t,e){function n(){OE.hide(u),e()}var o=Hm(Q0({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:vt.none()},"cancel",c.backstage)),r=oB(),i=nB(n,s.providers),u=Tu(aB({lazySink:function(){return s.getSink()},header:eB(r,i),body:rB(t,s.providers),footer:vt.some(iB(uB([],[o.asSpec()]))),onEscape:n,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(wy,n)],eventOrder:{}}));OE.show(u);var a=o.get(u);Vg.focus(a)}}),i=(f=(l=t).backstage.shared,{open:function(t,e){function n(t){OE.hide(a),e(t)}var o=Hm(Q0({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:vt.none()},"submit",l.backstage)),r=Q0({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:vt.none()},"cancel",l.backstage),i=oB(),u=nB(function(){return n(!1)},f.providers),a=Tu(aB({lazySink:function(){return f.getSink()},header:eB(i,u),body:rB(t,f.providers),footer:vt.some(iB(uB([],[r,o.asSpec()]))),onEscape:function(){return n(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(wy,function(){return n(!1)}),Cr(ky,function(){return n(!0)})],eventOrder:{}}));OE.show(a);var c=o.get(a);Vg.focus(c)}});return{open:function(t,e,n){return void 0!==e&&"toolbar"===e.inline?r(t,R.shared.anchors.inlineDialog(),n,e.ariaAttrs):void 0!==e&&"cursor"===e.inline?r(t,R.shared.anchors.cursor(),n,e.ariaAttrs):o(t,n)},openUrl:function(t,e){return n=e,jD.openUrl(function(t){var e=CB(t,{closeWindow:function(){OE.hide(e.dialog),n(e.instanceApi)}},N,R);return OE.show(e.dialog),e.instanceApi},t);var n},alert:function(t,e){n.open(t,function(){e()})},close:function(t){t.close()},confirm:function(t,e){i.open(t,function(t){e(t)})}}}var _B=function e(t){function l(t){return e(t)}var f=0<arguments.length&&void 0!==t?t:"undefined"==typeof window?null:window;if(l.version="2.3.8",l.removed=[],!f||!f.document||9!==f.document.nodeType)return l.isSupported=!1,l;var n,d=f.document,u=f.document,m=f.DocumentFragment,o=f.HTMLTemplateElement,g=f.Node,a=f.Element,r=f.NodeFilter,i=f.NamedNodeMap,c=void 0===i?f.NamedNodeMap||f.MozNamedAttrMap:i,s=f.HTMLFormElement,p=f.DOMParser,h=f.trustedTypes,v=a.prototype,b=RA(v,"cloneNode"),y=RA(v,"nextSibling"),x=RA(v,"childNodes"),w=RA(v,"parentNode");"function"!=typeof o||(n=u.createElement("template")).content&&n.content.ownerDocument&&(u=n.content.ownerDocument);var S=function(t,e){if("object"!==iA(t)||"function"!=typeof t.createPolicy)return null;var n=null,o="data-tt-policy-suffix",r="dompurify"+((n=e.currentScript&&e.currentScript.hasAttribute(o)?e.currentScript.getAttribute(o):n)?"#"+n:"");try{return t.createPolicy(r,{createHTML:function(t){return t}})}catch(t){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(h,d),k=S?S.createHTML(""):"",C=u,O=C.implementation,_=C.createNodeIterator,T=C.createDocumentFragment,E=C.getElementsByTagName,D=d.importNode,A={};try{A=IA(u).documentMode?u.documentMode:{}}catch(t){}var B={};function M(t){return t instanceof RegExp||t instanceof Function}function F(t){St&&St===t||(t=IA(t=t&&"object"===iA(t)?t:{}),G="ALLOWED_TAGS"in t?FA({},t.ALLOWED_TAGS):X,Y="ALLOWED_ATTR"in t?FA({},t.ALLOWED_ATTR):q,pt="ADD_URI_SAFE_ATTR"in t?FA(IA(ht),t.ADD_URI_SAFE_ATTR):ht,mt="ADD_DATA_URI_TAGS"in t?FA(IA(gt),t.ADD_DATA_URI_TAGS):gt,ft="FORBID_CONTENTS"in t?FA({},t.FORBID_CONTENTS):dt,J="FORBID_TAGS"in t?FA({},t.FORBID_TAGS):{},$="FORBID_ATTR"in t?FA({},t.FORBID_ATTR):{},I="USE_PROFILES"in t&&t.USE_PROFILES,Q=!1!==t.ALLOW_ARIA_ATTR,Z=!1!==t.ALLOW_DATA_ATTR,tt=t.ALLOW_UNKNOWN_PROTOCOLS||!1,et=t.SAFE_FOR_TEMPLATES||!1,nt=t.WHOLE_DOCUMENT||!1,it=t.RETURN_DOM||!1,ut=t.RETURN_DOM_FRAGMENT||!1,at=t.RETURN_TRUSTED_TYPE||!1,rt=t.FORCE_BODY||!1,ct=!1!==t.SANITIZE_DOM,st=!1!==t.KEEP_CONTENT,lt=t.IN_PLACE||!1,W=t.ALLOWED_URI_REGEXP||W,xt=t.NAMESPACE||yt,t.CUSTOM_ELEMENT_HANDLING&&M(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(K.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&M(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(K.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(K.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),R=-1===wt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,N="application/xhtml+xml"===R?function(t){return t}:OA,et&&(Z=!1),ut&&(it=!0),I&&(G=FA({},cA(UA)),Y=[],!0===I.html&&(FA(G,NA),FA(Y,jA)),!0===I.svg&&(FA(G,PA),FA(Y,WA),FA(Y,XA)),!0===I.svgFilters&&(FA(G,VA),FA(Y,WA),FA(Y,XA)),!0===I.mathMl&&(FA(G,LA),FA(Y,GA),FA(Y,XA))),t.ADD_TAGS&&FA(G=G===X?IA(G):G,t.ADD_TAGS),t.ADD_ATTR&&FA(Y=Y===q?IA(Y):Y,t.ADD_ATTR),t.ADD_URI_SAFE_ATTR&&FA(pt,t.ADD_URI_SAFE_ATTR),t.FORBID_CONTENTS&&FA(ft=ft===dt?IA(ft):ft,t.FORBID_CONTENTS),st&&(G["#text"]=!0),nt&&FA(G,["html","head","body"]),G.table&&(FA(G,["tbody"]),delete J.tbody),hA&&hA(t),St=t)}l.isSupported="function"==typeof w&&O&&void 0!==O.createHTMLDocument&&9!==A;var I,R,N,P,V=YA,H=qA,L=KA,z=JA,U=QA,j=ZA,W=$A,G=null,X=FA({},[].concat(cA(NA),cA(PA),cA(VA),cA(LA),cA(UA))),Y=null,q=FA({},[].concat(cA(jA),cA(WA),cA(GA),cA(XA))),K=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),J=null,$=null,Q=!0,Z=!0,tt=!1,et=!1,nt=!1,ot=!1,rt=!1,it=!1,ut=!1,at=!1,ct=!0,st=!0,lt=!1,ft=null,dt=FA({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),mt=null,gt=FA({},["audio","video","img","source","image","track"]),pt=null,ht=FA({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),vt="http://www.w3.org/1998/Math/MathML",bt="http://www.w3.org/2000/svg",yt="http://www.w3.org/1999/xhtml",xt=yt,wt=["application/xhtml+xml","text/html"],St=null,kt=u.createElement("form"),Ct=FA({},["mi","mo","mn","ms","mtext"]),Ot=FA({},["foreignobject","desc","title","annotation-xml"]),_t=FA({},["title","style","font","a","script"]),Tt=FA({},PA);FA(Tt,VA),FA(Tt,HA);var Et=FA({},LA);function Dt(e){CA(l.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=k}catch(t){e.remove()}}}function At(t,e){try{CA(l.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){CA(l.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!Y[t])if(it||ut)try{Dt(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}}function Bt(t){var e,n,o;rt?t="<remove></remove>"+t:o=(n=_A(t,/^[\r\n\t ]+/))&&n[0],"application/xhtml+xml"===R&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");var r=S?S.createHTML(t):t;if(xt===yt)try{e=(new p).parseFromString(r,R)}catch(t){}if(!e||!e.documentElement){e=O.createDocument(xt,"template",null);try{e.documentElement.innerHTML=P?"":r}catch(t){}}var i=e.body||e.documentElement;return t&&o&&i.insertBefore(u.createTextNode(o),i.childNodes[0]||null),xt===yt?E.call(e,nt?"html":"body")[0]:nt?e.documentElement:i}function Mt(t){return _.call(t.ownerDocument||t,t,r.SHOW_ELEMENT|r.SHOW_COMMENT|r.SHOW_TEXT,null,!1)}function Ft(t){return"object"===iA(g)?t instanceof g:t&&"object"===iA(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName}function It(t,e,n){B[t]&&SA(B[t],function(t){t.call(l,e,n,St)})}function Rt(t){var e;if(It("beforeSanitizeElements",t,null),t instanceof s&&("string"!=typeof t.nodeName||"string"!=typeof t.textContent||"function"!=typeof t.removeChild||!(t.attributes instanceof c)||"function"!=typeof t.removeAttribute||"function"!=typeof t.setAttribute||"string"!=typeof t.namespaceURI||"function"!=typeof t.insertBefore))return Dt(t),1;if(AA(/[\u0080-\uFFFF]/,t.nodeName))return Dt(t),1;var n=N(t.nodeName);if(It("uponSanitizeElement",t,{tagName:n,allowedTags:G}),t.hasChildNodes()&&!Ft(t.firstElementChild)&&(!Ft(t.content)||!Ft(t.content.firstElementChild))&&AA(/<[/\w]/g,t.innerHTML)&&AA(/<[/\w]/g,t.textContent))return Dt(t),1;if("select"===n&&AA(/<template/i,t.innerHTML))return Dt(t),1;if(G[n]&&!J[n])return t instanceof a&&!function(t){var e=w(t);e&&e.tagName||(e={namespaceURI:yt,tagName:"template"});var n=OA(t.tagName),o=OA(e.tagName);return t.namespaceURI===bt?e.namespaceURI===yt?"svg"===n:e.namespaceURI===vt?"svg"===n&&("annotation-xml"===o||Ct[o]):Boolean(Tt[n]):t.namespaceURI===vt?e.namespaceURI===yt?"math"===n:e.namespaceURI===bt?"math"===n&&Ot[o]:Boolean(Et[n]):t.namespaceURI===yt&&(e.namespaceURI!==bt||Ot[o])&&(e.namespaceURI!==vt||Ct[o])&&!Et[n]&&(_t[n]||!Tt[n])}(t)||("noscript"===n||"noembed"===n)&&AA(/<\/no(script|embed)/i,t.innerHTML)?(Dt(t),1):(et&&3===t.nodeType&&(e=t.textContent,e=TA(e,V," "),e=TA(e,H," "),t.textContent!==e&&(CA(l.removed,{element:t.cloneNode()}),t.textContent=e)),It("afterSanitizeElements",t,null),0);if(!J[n]&&Vt(n)){if(K.tagNameCheck instanceof RegExp&&AA(K.tagNameCheck,n))return;if(K.tagNameCheck instanceof Function&&K.tagNameCheck(n))return}if(st&&!ft[n]){var o=w(t)||t.parentNode,r=x(t)||t.childNodes;if(r&&o)for(var i=r.length-1;0<=i;--i)o.insertBefore(b(r[i],!0),y(t))}return Dt(t),1}function Nt(t,e,n){if(ct&&("id"===e||"name"===e)&&(n in u||n in kt))return!1;if((!Z||$[e]||!AA(L,e))&&(!Q||!AA(z,e)))if(!Y[e]||$[e]){if(!(Vt(t)&&(K.tagNameCheck instanceof RegExp&&AA(K.tagNameCheck,t)||K.tagNameCheck instanceof Function&&K.tagNameCheck(t))&&(K.attributeNameCheck instanceof RegExp&&AA(K.attributeNameCheck,e)||K.attributeNameCheck instanceof Function&&K.attributeNameCheck(e))||"is"===e&&K.allowCustomizedBuiltInElements&&(K.tagNameCheck instanceof RegExp&&AA(K.tagNameCheck,n)||K.tagNameCheck instanceof Function&&K.tagNameCheck(n))))return!1}else if(!pt[e]&&!AA(W,TA(n,j,""))&&("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==EA(n,"data:")||!mt[t])&&(!tt||AA(U,TA(n,j,"")))&&n)return!1;return!0}function Pt(t){It("beforeSanitizeAttributes",t,null);var e=t.attributes;if(e){for(var n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Y},o=e.length;o--;){var r,i=(r=e[o]).name,u=r.namespaceURI,a="value"===i?r.value:DA(r.value),c=N(i);if(n.attrName=c,n.attrValue=a,n.keepAttr=!0,n.forceKeepAttr=void 0,It("uponSanitizeAttribute",t,n),a=n.attrValue,!n.forceKeepAttr&&(At(i,t),n.keepAttr))if(AA(/\/>/i,a))At(i,t);else if(et&&(a=TA(a,V," "),a=TA(a,H," ")),Nt(N(t.nodeName),c,a))try{u?t.setAttributeNS(u,i,a):t.setAttribute(i,a),kA(l.removed)}catch(t){}}It("afterSanitizeAttributes",t,null)}}FA(Et,zA);var Vt=function(t){return 0<t.indexOf("-")};return l.sanitize=function(t,e){var n,o,r,i,u;if("string"!=typeof(t=(P=!t)?"\x3c!--\x3e":t)&&!Ft(t)){if("function"!=typeof t.toString)throw BA("toString is not a function");if("string"!=typeof(t=t.toString()))throw BA("dirty is not a string, aborting")}if(!l.isSupported){if("object"===iA(f.toStaticHTML)||"function"==typeof f.toStaticHTML){if("string"==typeof t)return f.toStaticHTML(t);if(Ft(t))return f.toStaticHTML(t.outerHTML)}return t}if(ot||F(e),l.removed=[],lt="string"!=typeof t&&lt){if(t.nodeName){var a=N(t.nodeName);if(!G[a]||J[a])throw BA("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof g)1===(o=(n=Bt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?n=o:n.appendChild(o);else{if(!it&&!et&&!nt&&-1===t.indexOf("<"))return S&&at?S.createHTML(t):t;if(!(n=Bt(t)))return it?null:at?k:""}n&&rt&&Dt(n.firstChild);for(var c=Mt(lt?t:n);r=c.nextNode();)3===r.nodeType&&r===i||Rt(r)||(r.content instanceof m&&function t(e){var n,o=Mt(e);for(It("beforeSanitizeShadowDOM",e,null);n=o.nextNode();)It("uponSanitizeShadowNode",n,null),Rt(n)||(n.content instanceof m&&t(n.content),Pt(n));It("afterSanitizeShadowDOM",e,null)}(r.content),Pt(r),i=r);if(i=null,lt)return t;if(it){if(ut)for(u=T.call(n.ownerDocument);n.firstChild;)u.appendChild(n.firstChild);else u=n;return Y.shadowroot?D.call(d,u,!0):u}var s=nt?n.outerHTML:n.innerHTML;return nt&&G["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&AA(tB,n.ownerDocument.doctype.name)&&(s="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+s),et&&(s=TA(s,V," "),s=TA(s,H," ")),S&&at?S.createHTML(s):s},l.setConfig=function(t){F(t),ot=!0},l.clearConfig=function(){St=null,ot=!1},l.isValidAttribute=function(t,e,n){return St||F({}),Nt(N(t),N(e),n)},l.addHook=function(t,e){"function"==typeof e&&(B[t]=B[t]||[],CA(B[t],e))},l.removeHook=function(t){if(B[t])return kA(B[t])},l.removeHooks=function(t){B[t]&&(B[t]=[])},l.removeAllHooks=function(){B={}},l}(),TB=eb.deviceType.isTouch(),EB=function(u,t){function e(t,i){return Cr(t,function(n,o){var e,r;e=n,r=function(t,e){i(u(),t,o.event,n)},k_.getState(e).get().each(function(t){r(t,e)})})}return V(V([],vB(e,t),!0),[e(Sy,function(t,e,n){e.onAction(t,{name:n.name})})],!1)},DB=function(u,t,c){function e(t,i){return Cr(t,function(n,o){var e,r;e=n,r=function(t,e){i(u(),t,o.event,n)},k_.getState(e).get().each(function(t){r(t.internalDialog,e)})})}return V(V([],vB(e,t),!0),[e(ky,function(t,e){return e.onSubmit(t)}),e(yy,function(t,e,n){e.onChange(t,{name:n.name})}),e(Sy,function(t,e,n,o){function r(){return Fg.focusIn(o)}function i(t){return ae(t,"disabled")||ue(t,"aria-disabled").exists(function(t){return"true"===t})}var u=gn(o.element),a=Oa(u);e.onAction(t,{name:n.name,value:n.value}),Oa(u).fold(r,function(e){i(e)||a.exists(function(t){return Ut(e,t)&&i(t)})?r():c().toOptional().filter(function(t){return!Ut(t.element,e)}).each(r)})}),e(_y,function(t,e,n){e.onTabChange(t,{newTabName:n.name,oldTabName:n.oldName})}),ru(function(t){var e=u();Df.setValue(t,e.getData())})],!1)},AB=yB,BB=tinymce.util.Tools.resolve("tinymce.util.URI"),MB=["insertContent","setContent","execCommand","close","block","unblock"];o.add("silver",function(t){var e=fE(t),n=e.uiMothership,o=e.backstage,r=e.renderUI,i=e.getUi;return uy(t,o.shared),{renderUI:r,getWindowManagerImpl:rt(OB({editor:t,backstage:o})),getNotificationManagerImpl:function(){return u=t,r=n,l={backstage:o}.backstage.shared,{open:function(t,e){function n(){e(),up.hide(c)}var o=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),a=Tu(hp.sketch({text:t.text,level:wt(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:vt.from(t.icon),closeButton:!o,onAction:n,iconProvider:l.providers.icons,translationProvider:l.providers.translate})),c=Tu(up.sketch(lt({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:l.getSink,fireDismissalEventInstead:{}},l.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));function s(){var t=Mn(Mt.fromDom(u.getContentAreaContainer())),e=Fn(),n=Wa(e.x,t.x,t.right),o=Wa(e.y,t.y,t.bottom),r=Math.max(t.right,e.right),i=Math.max(t.bottom,e.bottom);return vt.some(Bn(n,o,r-n,i-o))}return r.add(c),0<t.timeout&&lp.setTimeout(function(){n()},t.timeout),{close:n,moveTo:function(t,e){up.showAt(c,Eu(a),{anchor:{type:"makeshift",x:t,y:e}})},moveRel:function(t,e){var n,o,r,i=Eu(a),u={maxHeightFunction:Nc()};"banner"!==e&&k(t)?(n=function(){switch(e){case"bc-bc":return sp;case"tc-tc":return cp;case"tc-bc":return Ja;default:return $a}}(),o={type:"node",root:bn(),node:vt.some(Mt.fromDom(t)),overrides:u,layouts:{onRtl:function(){return[n]},onLtr:function(){return[n]}}},up.showWithinBounds(c,i,{anchor:o},s)):(r=lt(lt({},l.anchors.banner()),{overrides:u}),up.showWithinBounds(c,i,{anchor:r},s))},text:function(t){hp.updateText(a,t)},settings:t,getEl:function(){return a.element.dom},progressBar:{value:function(t){hp.updateProgress(a,t)}}}},close:function(t){t.close()},reposition:function(n){0<n.length&&St(n,function(t,e){0===e?t.moveRel(null,"banner"):t.moveRel(n[e-1].getEl(),"bc-tc")})},getArgs:function(t){return t.settings}};var u,r,l},ui:i()}})}();