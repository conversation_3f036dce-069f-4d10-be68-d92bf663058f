<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

/**
 * 管理员模型
 *
 * @property int $admin_id 管理员ID
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $real_name 真实姓名
 * @property string|null $pen_name 笔名
 * @property string $email 邮箱
 * @property string|null $avatar_url 头像URL
 * @property \Carbon\Carbon|null $last_login 最后登录时间
 * @property int $status 状态: 0-禁用, 1-启用
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class Admin extends Authenticatable
{
    use SoftDeletes, HasApiTokens, Notifiable;

    protected $table = 'admins';
    protected $primaryKey = 'admin_id';

    protected $fillable = [
        'username',
        'password',
        'real_name',
        'pen_name',
        'email',
        'avatar_url',
        'last_login',
        'status'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'last_login' => 'datetime',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 获取管理员的角色
     *
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'admin_roles', 'admin_id', 'role_id')
                    ->withPivot(['is_primary', 'assigned_by', 'assigned_at', 'expires_at'])
                    ->withTimestamps();
    }

    /**
     * 获取管理员的笔名
     *
     * @return BelongsToMany
     */
    public function penNames(): BelongsToMany
    {
        return $this->belongsToMany(PenName::class, 'admin_pen_names', 'admin_id', 'pen_name_id')
                    ->withPivot(['is_default'])
                    ->withTimestamps();
    }

    /**
     * 获取管理员的操作日志
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function operationLogs()
    {
        return $this->hasMany(OperationLog::class, 'admin_id', 'admin_id');
    }

    /**
     * 检查管理员是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查管理员是否禁用
     *
     * @return bool
     */
    public function isDisabled(): bool
    {
        return $this->status === 0;
    }

    /**
     * 获取主角色
     *
     * @return Role|null
     */
    public function getPrimaryRole()
    {
        return $this->roles()->wherePivot('is_primary', true)->first();
    }

    /**
     * 获取默认笔名
     *
     * @return PenName|null
     */
    public function getDefaultPenName()
    {
        return $this->penNames()->wherePivot('is_default', true)->first();
    }

    /**
     * 获取显示名称
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->real_name ?: $this->username;
    }

    /**
     * 获取完整显示名称（包含笔名）
     *
     * @return string
     */
    public function getFullDisplayNameAttribute(): string
    {
        $name = $this->display_name;
        if ($this->pen_name) {
            $name .= " ({$this->pen_name})";
        }
        return $name;
    }

    /**
     * 检查是否有指定角色
     *
     * @param string|array $roles 角色代码
     * @return bool
     */
    public function hasRole($roles): bool
    {
        $roleCodes = is_array($roles) ? $roles : [$roles];

        return $this->roles()
                    ->whereIn('role_code', $roleCodes)
                    ->where('status', 1)
                    ->exists();
    }

    /**
     * 检查是否有指定权限
     *
     * @param string|array $permissions 权限代码
     * @return bool
     */
    public function hasPermission($permissions): bool
    {
        $permissionCodes = is_array($permissions) ? $permissions : [$permissions];

        return $this->roles()
                    ->whereHas('permissions', function ($query) use ($permissionCodes) {
                        $query->whereIn('permission_code', $permissionCodes)
                              ->where('status', 1);
                    })
                    ->where('status', 1)
                    ->exists();
    }

    /**
     * 获取所有权限代码
     *
     * @return array
     */
    public function getAllPermissions(): array
    {
        return $this->roles()
                    ->where('status', 1)
                    ->with(['permissions' => function ($query) {
                        $query->where('status', 1);
                    }])
                    ->get()
                    ->flatMap(function ($role) {
                        return $role->permissions->pluck('permission_code');
                    })
                    ->unique()
                    ->values()
                    ->toArray();
    }

    /**
     * 获取所有角色代码
     *
     * @return array
     */
    public function getAllRoles(): array
    {
        return $this->roles()
                    ->where('status', 1)
                    ->pluck('role_code')
                    ->toArray();
    }

    /**
     * 更新最后登录时间
     *
     * @return void
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login' => now()]);
    }

    /**
     * 作用域：启用的管理员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：禁用的管理员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDisabled($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：按用户名搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $username
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUsername($query, string $username)
    {
        return $query->where('username', 'like', "%{$username}%");
    }

    /**
     * 作用域：按真实姓名搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $realName
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRealName($query, string $realName)
    {
        return $query->where('real_name', 'like', "%{$realName}%");
    }

    /**
     * 作用域：按邮箱搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $email
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByEmail($query, string $email)
    {
        return $query->where('email', 'like', "%{$email}%");
    }

    /**
     * 作用域：按角色筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $roleCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRole($query, string $roleCode)
    {
        return $query->whereHas('roles', function ($q) use ($roleCode) {
            $q->where('role_code', $roleCode);
        });
    }

    /**
     * 作用域：最近登录的管理员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecentlyActive($query, int $days = 7)
    {
        return $query->where('last_login', '>=', now()->subDays($days));
    }

    /**
     * 作用域：从未登录的管理员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeverLoggedIn($query)
    {
        return $query->whereNull('last_login');
    }
}
