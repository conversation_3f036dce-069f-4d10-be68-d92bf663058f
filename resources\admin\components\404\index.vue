<template>
  <div class="flex flex-col w-full">
    <img :src="$asset('Dashboard/Asset/404.png')" class="m-auto w-full sm:w-3/5" />
    <div class="bottom-0 m-auto mr-auto w-full">
      <div class="w-full text-base text-center text-gray-400">{{ $t('404.tips') }}</div>
      <div @click="push('/')" class="mt-2 w-full text-center">
        <el-button type="primary">{{ $t('404.btn_text') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const { push } = useRouter()
</script>
