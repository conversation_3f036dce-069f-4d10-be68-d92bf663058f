<?php

namespace Modules\Common\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

/**
 * 语言中间件
 * 处理 API 的语言参数
 */
class LanguageMiddleware
{
    /**
     * 支持的语言列表
     */
    protected array $supportedLanguages = [
        'en' => 'en',
        'zh_cn' => 'zh_CN',
        'zh_hk' => 'zh_HK',
    ];

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取语言参数
        $lang = $request->get('lang', 'zh_cn');
        
        // 验证语言是否支持
        if (isset($this->supportedLanguages[$lang])) {
            $locale = $this->supportedLanguages[$lang];
            App::setLocale($locale);
        } else {
            // 默认使用中文简体
            App::setLocale('zh_CN');
        }

        return $next($request);
    }
} 