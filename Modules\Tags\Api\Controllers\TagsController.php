<?php

namespace Modules\Tags\Api\Controllers;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Tags\Models\Tag;

/**
 * 标签控制器
 * 负责处理标签的增删改查等操作
 */
class TagsController extends Controller
{
    /**
     * 获取标签列表
     *
     * @return array 标签列表
     */
    public function index(Request $request): array
    {
        $limit = $request->input('limit', 15);
        $page = $request->input('page', 1);
        $tagIdBegin = $request->input('tag_id_begin', 0);
        $tagIdEnd = $request->input('tag_id_end', 0);
        $keyword = $request->input('keyword', '');
        $createdBy = $request->input('created_by', '');
        $status = $request->input('status', '');
        $created_at_begin = $request->input('created_at_begin', '');
        $created_at_end = $request->input('created_at_end', '');

        $query = Tag::query();

        // 根据ID范围筛选
        if ($tagIdBegin > 0) {
            $query->where('id', '>=', $tagIdBegin);
        }

        if ($tagIdEnd > 0) {
            $query->where('id', '<=', $tagIdEnd);
        }

        // 根据关键字筛选（搜索标签名称）
        if (!empty($keyword)) {
            $query->where('name', 'like', '%' . $keyword . '%');
        }

        // 根据创建人筛选
        if (!empty($createdBy)) {
            $query->where('created_by', $createdBy);
        }

        if ($status !== null && $status !== '') {
            $query->where('status', $status);
        }

        // 根据创建时间范围筛选
        if (!empty($created_at_begin)) {
            $query->where('created_at', '>=', $created_at_begin);
        }

        if (!empty($created_at_end)) {
            $query->where('created_at', '<=', $created_at_end);
        }

        // 分页结果
        $tags = $query->paginate($limit, ['*'], 'page', $page);

        foreach ($tags->items() as $tag) {
            $tag->category_name = Tag::$categoryMap[$tag->category_id] ?? '';
            $tag->status_name = $tag->status == 1 ? '启用' : '禁用';
        }

        return [
            'total' => $tags->total(),
            'page' => $tags->currentPage(),
            'limit' => $tags->perPage(),
            'items' => $tags->items(),
        ];
    }

    /**
     * 创建新标签
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function store(Request $request) : array
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:tags,name,is_deleted,0',
            'reference_name' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer',
            'status' => 'nullable|boolean',
            'sort_order' => 'nullable|integer',
        ]);

        if(!in_array($validatedData['category_id'],array_keys(Tag::$categoryMap))){
            throw new Exception('标签分类不存在');
        }

        $tag =  Tag::create($validatedData);
        $tag->category_name = Tag::$categoryMap[$tag->category_id] ?? '';
        $tag->status_name = $tag->status == 1 ? '启用' : '禁用';
        return $tag->toArray();
    }

    /**
     * 显示指定标签
     *
     * @param int $id
     * @return array
     */
    public function show($id): array
    {
        $tag = Tag::findOrFail($id)->toArray();
        $tag['category_name'] = Tag::$categoryMap[$tag['category_id']] ?? '';
        $tag['status_name'] = $tag['status'] == 1 ? '启用' : '禁用';
        return $tag;
    }

    /**
     * 更新指定标签
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'sometimes|required|string|max:255|unique:tags,name,is_deleted,0',
            'reference_name' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer',
            'status' => 'nullable|boolean',
            'sort_order' => 'nullable|integer',
        ]);

        Tag::where('id', $id)->firstOrFail();
        $tag = new Tag();
        $tag->newQuery()->where('id', $id)
            ->firstOrFail();

        $updateData = [];
        foreach ($validatedData as $key => $value) {
            // 字段不为 null 且与原值不一致才更新
            if (!is_null($value) && $tag[$key] !== $value) {
                $updateData[$key] = $value;
            }
        }
        if (!empty($updateData)) {
            $tag->update($updateData);
        }
        $tag = $tag->refresh();
        $tag->category_name = Tag::$categoryMap[$tag->category_id] ?? '';
        $tag->status_name = $tag->status == 1 ? '启用' : '禁用';
        return $tag->toArray();
    }

    /**
     * 删除指定标签
     *
     * @param int $id
     * @return array
     * @throws Exception
     */
    public function destroy($id)
    {
        $tag = Tag::findOrFail($id);
        $affectedRows = $tag->delete();
        if (empty($affectedRows)) {
            throw new Exception('删除标签失败，可能标签不存在或已被删除');
        }

        return [];
    }

    /**
     * 复制标签
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function copy($id): array
    {
        // 查询原标签
        $originalTag = Tag::where('id', $id)->first();
        if (!$originalTag) {
            throw new Exception('未找到要复制的标签');
        }

        // 复制标签属性，排除主键和时间戳
        $newTagData = $originalTag->toArray();
        unset($newTagData['id'], $newTagData['created_at'], $newTagData['updated_at'], $newTagData['deleted_at']);

        // 名称后缀“_copy”，避免唯一性冲突
        $newTagData['name'] = $originalTag->name . '_copy';
        // 检查名称唯一性
        if (Tag::nameExists($newTagData['name'])) {
            throw new Exception('复制失败：标签名称已存在');
        }

        // 创建新标签
        $newTag = Tag::create($newTagData);

        // 返回新标签信息
        $newTag->category_name = Tag::$categoryMap[$newTag->category_id] ?? '';
        $newTag->status_name = $newTag->status == 1 ? '启用' : '禁用';
        return $newTag->toArray();
    }

    /**
     * 切换标签状态（启用/禁用）
     *
     * @return array
     * @throws Exception
     */
    public function switch($id): array
    {
        // 查询标签
        $tag = Tag::findOrFail($id);

        // 切换状态
        $tag->status = $tag->status == 1 ? 0 : 1;
        $tag->save();

        // 返回最新状态
        $tag->category_name = Tag::$categoryMap[$tag->category_id] ?? '';
        $tag->status_name = $tag->status == 1 ? '启用' : '禁用';
        return $tag->toArray();
    }
}
