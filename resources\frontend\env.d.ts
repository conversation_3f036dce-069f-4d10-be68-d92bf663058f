/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'vue-i18n'

interface ImportMetaEnv {
  VITE_API_BASE_URL: string
  VITE_FRONTEND_API_BASE_URL: string
  FRONTEND_APP_NAME: string
  // 添加其他环境变量
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
