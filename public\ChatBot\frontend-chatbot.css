.chat-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 35px 35px 0 0;
  overflow: hidden;
}
.chat-view .chat-view-header {
  padding: 16px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 10;
  transition: all 0.3s;
  position: relative;
}
.chat-view .chat-view-header.scrolled {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.chat-view .chat-view-header .back-button {
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
}
.chat-view .chat-view-header .back-button .anticon {
  font-size: 20px;
  color: #666;
}
.chat-view .chat-view-header .assistant-info {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  margin-left: 0;
}
.chat-view .chat-view-header .assistant-info .assistant-avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}
.chat-view .chat-view-header .assistant-info .assistant-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-status .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-status .status-dot.online {
  background-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-status .status-dot.offline {
  background-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}
.chat-view .chat-view-header .assistant-info .assistant-details .assistant-status .typing {
  color: #1890ff;
}
.chat-view .chat-view-header:hover .back-button {
  left: 16px;
  opacity: 1;
}
.chat-view .chat-view-header:hover .assistant-info {
  margin-left: 40px;
}
.chat-view .Composer-sendBtn {
  background-color: #0670cc !important;
  background: #0670cc !important;
}
.chat-view .ChatApp {
  flex: 1;
  height: calc(100% - 73px);
  overflow: hidden;
}
.chat-view .MessageContainer {
  height: 100%;
}
.chat-view .MessageList {
  height: calc(100%);
  overflow-y: auto;
}
.chat-view .emoji-panel {
  position: absolute;
  bottom: 100%;
  left: 0;
  width: 250px;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}
.chat-view .emoji-panel .emoji-item {
  padding: 5px;
  cursor: pointer;
  font-size: 18px;
  width: calc(100% / 8);
  text-align: center;
  transition: all 0.2s;
}
.chat-view .emoji-panel .emoji-item:hover {
  background-color: #f0f0f0;
  transform: scale(1.2);
}
.chat-view .chat-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 73px);
}
.chat-view .emoji-toolbar-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e6e6e6;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
.chat-view .emoji-toolbar-panel .emoji-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e6e6e6;
  background: #f5f5f5;
}
.chat-view .emoji-toolbar-panel .emoji-header span {
  font-size: 14px;
  color: #333;
}
.chat-view .emoji-toolbar-panel .emoji-header .close-btn {
  cursor: pointer;
  font-size: 20px;
  color: #999;
  padding: 4px;
}
.chat-view .emoji-toolbar-panel .emoji-header .close-btn:hover {
  color: #666;
}
.chat-view .emoji-toolbar-panel .emoji-content {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}
.chat-view .emoji-toolbar-panel .emoji-content .emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  font-size: 20px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  user-select: none;
}
.chat-view .emoji-toolbar-panel .emoji-content .emoji-item:hover {
  background: #f5f5f5;
  transform: scale(1.1);
}
.chat-view :global .ChatApp {
  height: calc(100% - 73px);
}
.chat-view :global .Composer {
  position: relative;
  background: #fff;
}
.chat-view :global .Composer-actions {
  display: flex;
  align-items: center;
  padding: 0 8px;
}
.chat-view :global .Composer-toolbar {
  display: flex;
  padding: 4px 8px;
  border-top: 1px solid #e6e6e6;
}
.chat-view :global .Composer-sendBtn {
  background-color: #0670cc !important;
  background: #0670cc !important;
}
.chat-view :global .Toolbar {
  display: flex;
  padding: 0;
  background: #fff;
}
.chat-view :global .Toolbar-item {
  padding: 8px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s;
}
.chat-view :global .Toolbar-item:hover {
  color: #1890ff;
}
.chat-view :global .Toolbar-item .Icon {
  font-size: 20px;
}
@keyframes fadeInOut {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}
.Composer-toolbar {
  position: relative;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.ChatApp {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.ChatApp .MessageContainer {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.recording-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 1000;
}
.recording-indicator .recording-wave {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 0, 0, 0.3);
  animation: wave 1.5s infinite;
}
.recording-indicator .recording-icon {
  font-size: 24px;
  margin: 10px 0;
  color: #ff4d4f;
}
.recording-indicator .recording-time {
  font-size: 16px;
  font-family: monospace;
}
@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
.audio-message {
  padding: 4px;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-panel {
  background-color: #f5f5f5;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-play-button {
  background-color: #1890ff;
  border-radius: 50%;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-current-time-display,
.audio-message .audio-preview-container audio::-webkit-media-controls-time-remaining-display {
  color: #666;
}
@keyframes playing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
.playing {
  animation: playing 1s infinite;
}
/* 预览对话框样式 */
.audio-preview-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.audio-preview-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 280px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.audio-duration {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 10px 0;
}
.preview-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}
.preview-buttons button {
  padding: 8px 24px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}
.preview-send {
  background: #1989fa;
  color: white;
}
.preview-send:hover {
  background: #40a9ff;
}
.preview-cancel {
  background: #f5f5f5;
  color: #666;
}
.preview-cancel:hover {
  background: #e8e8e8;
}
/* 自定义音频播放器样式 */
.audio-preview-container audio {
  width: 100%;
  margin-bottom: 10px;
  border-radius: 4px;
}
.audio-preview-container audio::-webkit-media-controls-panel {
  background-color: #f5f5f5;
}
.audio-preview-container audio::-webkit-media-controls-play-button {
  background-color: #1989fa;
  border-radius: 50%;
}
.audio-preview-container audio::-webkit-media-controls-current-time-display,
.audio-preview-container audio::-webkit-media-controls-time-remaining-display {
  color: #666;
}
.audio-preview-container audio::-webkit-media-controls-timeline {
  background-color: #e8e8e8;
}
.audio-preview-container audio::-webkit-media-controls-volume-slider {
  background-color: #e8e8e8;
}
/* 添加音频消息的样式 */
.audio-message .audio-player {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background: #f5f5f5;
  cursor: pointer;
}
.audio-message .audio-player .audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
.audio-message .audio-player .audio-controls .play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e6e6e6;
  transition: all 0.3s;
}
.audio-message .audio-player .audio-controls .play-icon:hover {
  background: #d9d9d9;
}
.audio-message .audio-player .audio-controls .play-icon.playing {
  background: #1890ff;
  color: #fff;
}
.audio-message .audio-player .audio-controls .audio-duration {
  font-size: 14px;
  color: #666;
}
.audio-message .audio-player audio {
  display: none;
}
/* 添加录音指示器的样式 */
.recording-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 24px;
  color: #fff;
  z-index: 1000;
}
.recording-indicator .recording-wave {
  width: 12px;
  height: 12px;
  background: #ff4d4f;
  border-radius: 50%;
  animation: pulse 1s infinite;
}
.recording-indicator .recording-icon {
  font-size: 20px;
}
.recording-indicator .recording-time {
  font-size: 16px;
  font-weight: 500;
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.message-wrapper {
  position: relative;
}
.message-wrapper .message-status {
  position: absolute;
  bottom: -20px;
  font-size: 12px;
}
.message-wrapper .message-status.sending {
  color: #999;
}
.message-wrapper .message-status.failed {
  color: #ff4d4f;
}
.image-container {
  position: relative;
}
.image-container img {
  max-width: 200px;
  border-radius: 4px;
}
.image-container .message-status {
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 12px;
}
.image-container .message-status.sending {
  color: #999;
}
.image-container .message-status.failed {
  color: #ff4d4f;
}
.file-preview-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.file-preview-dialog .file-preview-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 300px;
}
.file-preview-dialog .file-preview-container .file-info {
  margin-bottom: 15px;
}
.file-preview-dialog .file-preview-container .file-info .file-name {
  font-weight: bold;
  margin-bottom: 5px;
}
.file-preview-dialog .file-preview-container .file-info .file-size {
  color: #666;
  font-size: 12px;
}
.file-preview-dialog .file-preview-container .preview-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.file-preview-dialog .file-preview-container .preview-buttons button {
  padding: 5px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.file-preview-dialog .file-preview-container .preview-buttons button.preview-send {
  background: #1890ff;
  color: white;
}
.file-preview-dialog .file-preview-container .preview-buttons button.preview-cancel {
  background: #f5f5f5;
  color: #666;
}
.file-message .file-container {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
}
.file-message .file-container:hover {
  background: #e8e8e8;
}
.file-message .file-container .file-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #1890ff;
}
.file-message .file-container .file-info .file-name {
  font-weight: 500;
  margin-bottom: 2px;
}
.file-message .file-container .file-info .file-size {
  font-size: 12px;
  color: #666;
}
.image-preview-dialog {
  animation: fadeIn 0.3s ease-in-out;
}
.image-preview-container {
  animation: zoomIn 0.3s ease-in-out;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    transform: scale(0.5);
  }
  to {
    transform: scale(1);
  }
}
/* 添加按钮悬停效果 */
.preview-send:hover {
  background: #40a9ff !important;
}
.preview-cancel:hover {
  background: #e8e8e8 !important;
}
.image-message {
  display: inline-block;
  position: relative;
  transition: transform 0.2s ease;
}
.image-message:hover {
  transform: scale(1.02);
}
.image-message img {
  display: block;
  max-width: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.image-preview-dialog {
  animation: fadeIn 0.2s ease-out;
}
.image-preview-dialog .image-preview-container {
  animation: zoomIn 0.3s ease-out;
}
.image-preview-dialog .image-preview-container img {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    transform: scale(0.3);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
.ChatApp {
  background-color: #ffffff !important;
}
.MessageContainer {
  background-color: #ffffff !important;
}
.Message {
  background-color: #ffffff !important;
}
.MessageList::-webkit-scrollbar {
  width: 6px !important;
}
.MessageList::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 3px !important;
}
.MessageList::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5) !important;
  border-radius: 3px !important;
}
.MessageList::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.7) !important;
}
:global .ChatApp,
:global .MessageContainer,
:global .Message,
:global .MessageList {
  background-color: #ffffff !important;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-panel {
  background-color: #fff;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-play-button {
  background-color: #1890ff;
  border-radius: 50%;
}
.audio-message .audio-preview-container audio::-webkit-media-controls-current-time-display,
.audio-message .audio-preview-container audio::-webkit-media-controls-time-remaining-display {
  color: #666;
}
.audio-message .audio-info {
  font-size: 12px;
  color: #666;
}
.audio-message .audio-info .time {
  margin-left: 8px;
}
.article-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}
.article-view .article-header {
  background: #0084ff;
  padding: 16px 20px;
  color: white;
  display: flex;
  align-items: center;
  height: 60px;
}
.article-view .article-header .back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  margin-right: 12px;
}
.article-view .article-header .back-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.article-view .article-header .header-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.article-view .article-header .header-content .title {
  font-size: 16px;
  font-weight: 500;
}
.article-view .article-header .header-content .close-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.article-view .article-header .header-content .close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.article-view .article-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}
.article-view .article-content h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}
.article-view .article-content .article-text {
  font-size: 15px;
  line-height: 1.6;
  color: #4a4a4a;
  margin-bottom: 32px;
}
.article-view .article-content .article-feedback {
  border-top: 1px solid #eee;
  padding-top: 24px;
  margin-bottom: 32px;
}
.article-view .article-content .article-feedback .feedback-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}
.article-view .article-content .article-feedback .feedback-buttons {
  display: flex;
  gap: 12px;
}
.article-view .article-content .article-feedback .feedback-buttons .feedback-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}
.article-view .article-content .article-feedback .feedback-buttons .feedback-btn:hover {
  border-color: #0084ff;
  color: #0084ff;
}
.article-view .article-content .article-feedback .feedback-buttons .feedback-btn.active {
  background: #0084ff;
  color: white;
  border-color: #0084ff;
}
.article-view .article-content .read-more {
  border-top: 1px solid #eee;
  padding-top: 24px;
}
.article-view .article-content .read-more .read-more-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}
.article-view .article-content .read-more .related-articles {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.article-view .article-content .read-more .related-articles .related-article-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}
.article-view .article-content .read-more .related-articles .related-article-item:hover {
  background: #f5f5f5;
  color: #0084ff;
}
.article-view .article-content .read-more .related-articles .related-article-item .anticon {
  color: #666;
}
.categories-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}
.categories-view .categories-header {
  background: #0084ff;
  padding: 16px 20px;
  color: white;
  display: flex;
  align-items: center;
  height: 60px;
}
.categories-view .categories-header .back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  margin-right: 12px;
}
.categories-view .categories-header .back-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.categories-view .categories-header .header-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.categories-view .categories-header .header-content .title {
  font-size: 16px;
  font-weight: 500;
}
.categories-view .categories-header .header-content .close-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.categories-view .categories-header .header-content .close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.categories-view .categories-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}
.categories-view .categories-content .category-item {
  padding: 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.3s;
}
.categories-view .categories-content .category-item:hover {
  background: #f5f5f5;
}
.categories-view .categories-content .category-item .category-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}
.categories-view .categories-content .category-item .category-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
.categories-view .categories-content .category-item:last-child {
  border-bottom: none;
}
:root{--brand-1:#ff5000;--brand-2:#ff9000;--brand-3:#fff9eb;--black:#000;--white:#fff;--gray-1:#111;--gray-2:#666;--gray-3:#999;--gray-4:#ccc;--gray-5:#ddd;--gray-6:#eee;--gray-7:#f5f5f5;--gray-8:#f8f8f8;--light-1:#eee;--light-2:#f5f5f5;--highlight-1:var(--brand-1);--highlight-2:var(--brand-2);--link-color:var(--blue);--blue:#39f;--gray-dark:#333;--green:#62d957;--orange:#f70;--red:#ff3634;--yellow:#ffc233;--yellow-light:#fff9db;--font-size-xs:0.75rem;--font-size-sm:0.875rem;--font-size-md:1rem;--font-size-lg:1.125rem;--radius-sm:0.25rem;--radius-md:0.75rem;--radius-lg:1.25rem;--shadow-1:0 3px 4px 0 rgba(0,0,0,.04);--shadow-2:0 4px 8px 0 rgba(0,0,0,.08);--shadow-3:0 6px 10px 0 rgba(0,0,0,.08);--safe-top:0px;--safe-bottom:0px;--gutter:12px;--btn-primary-border-color:transparent;--btn-primary-bg:linear-gradient(90deg,var(--brand-2) 0%,var(--brand-1) 98%);--btn-primary-color:var(--white)}@supports (top:constant(safe-area-inset-top)){:root{--safe-top:constant(safe-area-inset-top);--safe-bottom:constant(safe-area-inset-bottom)}}@supports (top:env(safe-area-inset-top)){:root{--safe-top:env(safe-area-inset-top);--safe-bottom:env(safe-area-inset-bottom)}}.S--invisible{clip:rect(0,0,0,0);position:absolute}.pb-safe{padding-bottom:var(--safe-bottom)}@-webkit-keyframes slideInRight{0%{opacity:0;-webkit-transform:translateX(6.25rem);transform:translateX(6.25rem)}to{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}@keyframes slideInRight{0%{opacity:0;-webkit-transform:translateX(6.25rem);transform:translateX(6.25rem)}to{opacity:1;-webkit-transform:translateX(0);transform:translateX(0)}}.slide-in-right-item{-webkit-animation:slideInRight .5s ease-in-out both;animation:slideInRight .5s ease-in-out both}.slide-in-right-item:nth-child(2){-webkit-animation-delay:.1s;animation-delay:.1s}.slide-in-right-item:nth-child(3){-webkit-animation-delay:.2s;animation-delay:.2s}.slide-in-right-item:nth-child(4){-webkit-animation-delay:.3s;animation-delay:.3s}.slide-in-right-item:nth-child(5){-webkit-animation-delay:.4s;animation-delay:.4s}.slide-in-right-item:nth-child(6){-webkit-animation-delay:.5s;animation-delay:.5s}.slide-in-right-item:nth-child(7){-webkit-animation-delay:.6s;animation-delay:.6s}.slide-in-right-item:nth-child(8){-webkit-animation-delay:.7s;animation-delay:.7s}.slide-in-right-item:nth-child(9){-webkit-animation-delay:.8s;animation-delay:.8s}.slide-in-right-item:nth-child(10){-webkit-animation-delay:.9s;animation-delay:.9s}.slide-in-right-item:nth-child(11){-webkit-animation-delay:1s;animation-delay:1s}@-webkit-keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.A-fadeIn{-webkit-animation:fadeIn .6s;animation:fadeIn .6s}.Avatar{border-radius:50%;display:inline-block;overflow:hidden}.Avatar img{display:block;height:2.25rem;object-fit:cover;width:2.25rem}.Avatar--sm img{height:1.5rem;width:1.5rem}.Avatar--lg img{height:2.5rem;width:2.5rem}.Avatar--square{border-radius:4px}.Backdrop{background:rgba(0,0,0,.7);bottom:0;height:100vh;left:0;opacity:0;outline:0;position:fixed;right:0;transition:.3s;width:100vw;z-index:100}.Backdrop.active{opacity:1}.Bubble{background:var(--white);border-radius:12px;max-width:42.5rem;min-width:.0625rem}.Bubble.text,.Bubble.typing{word-wrap:break-word;box-sizing:border-box;min-width:2.5rem;overflow-wrap:break-word;padding:.75rem;-webkit-user-select:text;-ms-user-select:text;user-select:text;white-space:pre-wrap}.Bubble.image img{border-radius:inherit;display:block;height:auto;max-height:12.5rem;max-width:12.5rem}.Btn,.Bubble p{margin:0}.Btn{-webkit-tap-highlight-color:transparent;align-items:center;-webkit-appearance:none;background:var(--white);border:1px solid var(--gray-5);border-radius:999px;color:var(--gray-1);display:inline-flex;font-family:inherit;font-size:.875rem;font-weight:400;justify-content:center;line-height:1.5;min-width:5rem;overflow:visible;padding:.3125rem .75rem;text-align:center;text-transform:none;transition:.15s ease-in-out;-webkit-user-select:none;-ms-user-select:none;user-select:none;vertical-align:middle;white-space:nowrap}.Btn:not(:disabled){cursor:pointer}.Btn:focus:not(:focus-visible){outline:0}.Btn:active{background:rgba(0,0,0,.08)}.Btn:disabled{color:var(--gray-4);opacity:.5;pointer-events:none}.Btn--primary:not(.Btn--outline){background:var(--btn-primary-bg);background-origin:border-box;border-color:var(--btn-primary-border-color);color:var(--btn-primary-color)}.Btn--primary:not(.Btn--outline):active{opacity:.8}.Btn--outline.Btn--primary{border-color:var(--brand-1);color:var(--brand-1)}.Btn--sm{font-size:.875rem;padding:.25rem .75rem}.Btn--lg{font-size:1rem;padding:.4375rem .75rem}.Btn--block{display:block;width:100%}.Btn-icon{-webkit-margin-end:.5rem;align-self:center;display:inline-flex;flex-shrink:0;margin-inline-end:.5rem}@media (hover:hover){.Btn:hover{background:rgba(0,0,0,.04)}.Btn--primary:not(.Btn--outline):hover{background:var(--btn-primary-bg);opacity:.9}}.Btn--text{border:0;color:var(--link-color);font-size:inherit;padding:0;vertical-align:initial}.Btn--text,.Btn--text:active,.Btn--text:hover{background:transparent}.BackBottom{bottom:4.25rem;overflow:hidden;position:absolute;right:0;z-index:10}.BackBottom .Btn{background:hsla(0,0%,100%,.85);border-radius:50px 0 0 50px;border-right:0;color:var(--brand-1);font-size:var(--font-size-sm)}.Card{background:var(--white);border-radius:12px;overflow:hidden}.Card--xl{width:18.75rem}.Card--lg{width:10rem}.Card--md{width:7.5rem}.Card--sm{width:6.5rem}.Card--xs{width:5rem}.Card--fluid{max-width:42.5rem;min-width:16.25rem;width:calc(100% - 3rem)}.Card[data-fluid=order]{max-width:22.5rem}.CardMedia{background-position:50%;background-repeat:no-repeat;background-size:cover;position:relative}.CardMedia:after{content:"";display:block;height:0}.CardMedia--wide:after{padding-top:56.25%}.CardMedia--square:after{padding-top:100%}.CardMedia-content{height:100%;left:0;overflow:hidden;position:absolute;top:0;width:100%}.CardTitle{padding:.75rem .75rem .375rem}.CardTitle--center{padding:.25rem .125rem;text-align:center}.CardTitle-title{font-size:1rem;font-weight:500;margin:0}.CardTitle-subtitle{color:var(--gray-3);font-size:.625rem;margin:0}.CardContent{padding:.75rem}.CardTitle+.CardContent{padding-top:0}.CardText{color:var(--gray-dark);padding:.75rem}.CardTitle+.CardText{padding-top:0}.CardText p{margin:0}.CardActions{display:flex;padding:.75rem .75rem 1.125rem}.CardContent+.CardActions,.CardText+.CardActions,.CardTitle+.CardActions{padding-top:0}.CardActions .Btn{flex:1;line-height:1.5}.CardActions .Btn+.Btn{margin-left:.75rem}.CardActions--column{flex-direction:column;padding:0}.CardActions--column .Btn{background:var(--white);border:0;border-radius:0;border-top:1px solid var(--gray-6);color:var(--gray-3);padding:.625rem}.CardActions--column .Btn:last-child{border-radius:0 0 12px 12px}.CardActions--column .Btn:active{background:var(--gray-7)}.CardActions--column .Btn:disabled{color:var(--gray-4)}.CardActions--column .Btn+.Btn{margin:0}.CardActions--column .Btn--primary{color:var(--brand-1)}@media (hover:hover){.CardActions--column .Btn:hover{background:var(--gray-7)}}.Divider{align-items:center;color:var(--gray-3);display:flex;font-size:.75rem;margin:.75rem 0}.Divider:after,.Divider:before{border-top:1px solid var(--gray-5);content:"";display:block;flex:1}.Divider--text-center:before,.Divider--text-left:before,.Divider--text-right:before{margin-right:var(--gutter)}.Divider--text-center:after,.Divider--text-left:after,.Divider--text-right:after{margin-left:var(--gutter)}.Divider--text-left:before,.Divider--text-right:after{max-width:10%}.Empty{padding:1.875rem;text-align:center}.Empty-img{height:7.8125rem}.Empty-tip{color:var(--gray-4);margin:1.25rem 0}.Flex{display:flex}.Flex--inline{display:inline-flex}.Flex--center{align-items:center;justify-content:center}.Flex--d-r{flex-direction:row}.Flex--d-rr{flex-direction:row-reverse}.Flex--d-c{flex-direction:column}.Flex--d-cr{flex-direction:column-reverse}.Flex--w-n{flex-wrap:nowrap}.Flex--w-w{flex-wrap:wrap}.Flex--w-wr{flex-wrap:wrap-reverse}.Flex--jc-fs{justify-content:flex-start}.Flex--jc-fe{justify-content:flex-end}.Flex--jc-c{justify-content:center}.Flex--jc-sb{justify-content:space-between}.Flex--jc-sa{justify-content:space-around}.Flex--ai-fs{align-items:flex-start}.Flex--ai-fe{align-items:flex-end}.Flex--ai-c{align-items:center}.FlexItem{flex:1;min-height:0;min-width:0}.HelpText{color:var(--gray-4);font-size:.75rem}.Icon{stroke-width:0;fill:currentColor;display:inline-block;height:1em;transition:all .3s cubic-bezier(.18,.89,.32,1.28);width:1em}.is-spin{-webkit-animation:spin 1s linear infinite;animation:spin 1s linear infinite}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.IconBtn{background:transparent;border:0;border-radius:3px;color:var(--gray-2);font-size:1.125rem;min-width:0;padding:0}.IconBtn.Btn--primary{color:var(--brand-2)}.IconBtn:disabled{border-color:var(--gray-6);color:var(--gray-6)}.IconBtn.Btn--lg{border-radius:6px;font-size:1.5rem}.IconBtn>.Icon,.IconBtn>img{display:block}.IconBtn>img{height:1em;width:1em}.Image{border-style:none;display:inline-block;overflow:hidden;position:relative}.Image--fluid{height:auto;max-width:100%}.InfiniteScroll{-webkit-overflow-scrolling:touch;overflow-y:scroll}.InputWrapper{position:relative}.InputWrapper.has-counter{padding-bottom:1.25rem}.InputWrapper.has-counter+.HelpText{margin-top:-1.25rem}.Input{-webkit-tap-highlight-color:transparent;-webkit-appearance:none;background:var(--white);border:1px solid var(--gray-6);border-radius:12px;box-sizing:border-box;color:var(--gray-1);display:block;font-family:inherit;font-size:.875rem;line-height:1.5;margin:0;min-height:1.5rem;padding:.3125rem .75rem;resize:none;transition:.2s ease-in-out;width:100%}.Input:focus{border-color:var(--brand-1);outline:none}.Input:focus:not([disabled]):not([readonly])~.Input-line:after,.Input:focus:not([disabled]):not([readonly])~.Input-line:before{width:50%}.Input::-webkit-input-placeholder{color:var(--gray-4)}.Input:-ms-input-placeholder{color:var(--gray-4)}.Input::placeholder{color:var(--gray-4)}.Input--filled{background-color:var(--gray-6);border-color:transparent}.Input--flushed{background:none;border-radius:0;border-width:0 0 1px;padding:.125rem var(--gutter)}.Input--flushed:focus{box-shadow:var(--brand-1) 0 1px 0 0}.Input-counter{color:var(--gray-3);float:right;font-size:.75rem;margin-right:var(--gutter);position:relative;z-index:1}.Label{color:var(--gray-2);display:block;font-size:.75rem}.List{background:var(--white)}.List--bordered{border:1px solid var(--gray-7);border-radius:2px}.ListItem{align-items:center;border:0;box-sizing:border-box;color:var(--gray-1);display:flex;font-size:.9375rem;line-height:1.6;padding:.625rem var(--gutter);text-decoration:none;transition:.3s}.ListItem:focus:not(:focus-visible){outline:0}.ListItem+.ListItem{border-top:1px solid var(--gray-7)}.ListItem .Icon{color:var(--gray-3)}button.ListItem{-webkit-appearance:none;appearance:none;background:transparent;text-align:left;width:100%}a.ListItem:active,button.ListItem:active{background:var(--gray-6)}@media (hover:hover){a.ListItem:hover,button.ListItem:hover{background:rgba(0,0,0,.04);background-clip:padding-box;cursor:pointer}}.ListItem-content{flex:1}.Loading{color:var(--gray-2);padding:.75rem}.Loading .Icon{font-size:1.875rem}.Loading-tip{font-size:.875rem;margin:0 0 0 .375rem}.MediaObject{display:flex}.MediaObject-pic{margin-right:.625rem;width:4.375rem}.MediaObject-pic>img{display:block;height:100%;width:100%}.MediaObject-info{flex:1}.MediaObject-title{font-size:.875rem;font-weight:400;margin:0 0 .375rem}.MediaObject-meta{color:var(--gray-2);font-size:.75rem}.Message{position:relative}.Message+.Message{margin-top:.75rem}.Message.left .Message-main>.Avatar{margin-right:.25rem}.Message.left .Bubble{margin-right:3rem}.Message.right .Message-content,.Message.right .Message-main{flex-direction:row-reverse}.Message.right .Message-main>.Avatar{margin-left:.25rem}.Message.right .Message-author{text-align:right}.Message.right .Bubble{background:var(--brand-3);border-radius:12px;margin-left:2.5rem}.Message.pop{display:none}.Message-meta{display:flex;justify-content:center;margin-bottom:.75rem;text-align:center}.Message-content,.Message-main{align-items:flex-start;display:flex}.Message-inner{flex:1;min-width:0}.Message-author{color:var(--gray-2);font-size:var(--font-size-xs);line-height:1.1;margin-bottom:.375rem}.SystemMessage{color:var(--gray-2);font-size:.75rem;padding:0 .9375rem;text-align:center}.SystemMessage a{margin-left:.3125rem}.SystemMessage-inner{background:var(--gray-8);border-radius:6px;display:inline-block;padding:.375rem .5625rem;text-align:left}.MessageStatus{align-self:center;margin-right:.75rem}.MessageStatus[data-status=loading] .Icon{color:var(--gray-4)}.MessageStatus[data-status=fail] .IconBtn{color:#ff5959}.MessageStatus .Icon,.MessageStatus .IconBtn{display:block}.Message.right .Bubble+.MessageStatus{margin-right:-1.75rem}.Modal,.Popup{align-items:center;bottom:0;display:flex;height:100%;justify-content:center;left:0;outline:0;position:fixed;right:0;top:0;z-index:100}.Modal.active .Modal-dialog{opacity:1;-webkit-transform:none;transform:none}.Modal-dialog{background:var(--white);border-radius:12px;font-size:.9375rem;opacity:0;overflow:hidden;position:relative;-webkit-transform:translateY(-3.125rem);transform:translateY(-3.125rem);transition:opacity .15s linear,-webkit-transform .3s ease-out;transition:transform .3s ease-out,opacity .15s linear;transition:transform .3s ease-out,opacity .15s linear,-webkit-transform .3s ease-out;width:20rem;z-index:100}@media (max-width:320px){.Modal-dialog{width:18.75rem}}.Modal-header{padding:1.125rem 1.25rem .625rem;position:relative}.Modal-body{padding:1.125rem .9375rem}.Modal-header+.Modal-body{padding-top:0}.Modal-footer{display:flex}.Modal-footer .Btn--outline{border-color:var(--gray-6);border-radius:0;border-width:1px 0 0;padding-bottom:.625rem;padding-top:.625rem}.Modal-footer .Btn--outline:not(.Btn--primary){color:var(--gray-2)}.Modal-footer--h[data-variant=round]{padding:0 .9375rem 1.125rem}.Modal-footer--h[data-variant=round] .Btn+.Btn{margin-left:.75rem}.Modal-footer--h[data-variant=outline] .Btn+.Btn{border-left-width:1px}.Modal-footer--h .Btn{flex:1}.Modal-footer--v{flex-direction:column}.Confirm .Modal-body{text-align:center}.Popup{align-items:flex-end}.Popup.active .Popup-dialog{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}.Popup-content{display:flex;flex-direction:column;height:100%}.Popup-dialog{background:var(--white);border-radius:27px 27px 0 0;opacity:0;overflow:hidden;position:relative;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);transition:.3s;width:100%;z-index:100}.Popup-dialog[data-bg-color=gray]{background:var(--gray-7)}.Popup-dialog[data-height="80"]{height:80vh}.Popup-dialog[data-height="60"]{height:60vh}.Popup-dialog[data-height="40"]{height:40vh}.Popup-header{margin-top:.25rem;padding:.9375rem 2.5rem;position:relative}.Modal-title,.Popup-title{color:var(--gray-1);font-size:1.125rem;font-weight:500;margin:0;min-height:1.5625rem;text-align:center}.Modal-close,.Popup-close{color:var(--gray-1);position:absolute;right:.75rem}.Modal-close{top:.75rem}.Popup-close{top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.Popup-body{flex:1;min-height:0}.Popup-body.overflow{-webkit-overflow-scrolling:touch;max-height:70vh;overflow-y:auto}.Popup-footer{background:var(--white);display:flex;flex-direction:column;padding:.5625rem .75rem;padding-bottom:calc(.5625rem + var(--safe-bottom));position:relative}.Popup-footer .Btn+.Btn{margin-top:.5625rem}.S--modalOpen,.S--modalOpen .MessageContainer>.PullToRefresh{overflow:hidden}.S--wide .Popup{align-items:center}.S--wide .Popup-dialog{border-radius:12px;width:30rem}.S--wide .Popup-footer--v{padding-bottom:.75rem}.Navbar{background:var(--gray-6);border-bottom:1px solid var(--gray-5);padding:0 var(--gutter);padding-top:var(--safe-top);position:relative;z-index:10}.Navbar,.Navbar-main{align-items:center;display:flex}.Navbar-main{flex:1;justify-content:center;min-height:2.75rem}.Navbar-left,.Navbar-right{width:3.625rem}.Navbar-right{text-align:right;white-space:nowrap}.Navbar-title{color:var(--gray-1);font-size:1.125rem;font-weight:500;margin:0;text-align:center}.Navbar-logo{height:2.25rem;width:auto}.Navbar .IconBtn{color:var(--gray-1)}.Navbar .IconBtn+.IconBtn{margin-left:.5625rem}.Notice{align-items:center;background:var(--white);border-radius:12px;display:flex;padding:.75rem}.Notice-icon{margin-right:.375rem}.Notice-close{margin-left:.375rem}.Notice .Icon{color:var(--brand-1);font-size:1rem}.Notice-content{color:var(--gray-1);flex:1;font-size:.8125rem;min-width:0}.Popover{font-size:.875rem;left:0;position:absolute;top:0;-webkit-transform:translateY(-.625rem);transform:translateY(-.625rem);z-index:1030}.Popover-body{background:var(--white);border-radius:6px;box-shadow:var(--shadow-3)}.Popover-arrow{fill:var(--white);display:block;height:.3125rem;margin-left:.625rem;width:.5625rem}.Price{display:inline;font-size:.875rem;font-weight:500}.Price--original{color:var(--gray-3);text-decoration:line-through}.Price .Price-fraction{font-size:.857em}.Progress{background-color:var(--gray-5);border-radius:100px;display:flex;height:.125rem;overflow:hidden}.Progress-bar{background-color:var(--blue);overflow:hidden;transition:width .6s ease}.Progress--success .Progress-bar{background-color:var(--green)}.Progress--error .Progress-bar{background-color:var(--red)}.PullToRefresh{-webkit-overflow-scrolling:touch;height:100%;overflow-y:scroll}.PullToRefresh-fallback{padding-top:var(--gutter);text-align:center}.PullToRefresh-loadMore{font-size:.875rem}.PullToRefresh-inner{min-height:100%;overflow:hidden}.PullToRefresh-indicator{color:grey;height:1.875rem;line-height:1.875rem;margin-top:-1.875rem;text-align:center}.PullToRefresh-spinner{color:var(--gray-3);font-size:1.6875rem}.PullToRefresh-transition{transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.QuickReplies{background:var(--gray-7);bottom:100%;left:0;overflow:hidden;padding:.5625rem var(--gutter);position:absolute;right:0;transition:opacity .3s;z-index:110}.QuickReplies[data-visible=false]{opacity:0;visibility:hidden}.QuickReplies:not(.ScrollView--hasControls){padding-left:0;padding-right:0}.QuickReplies:not(.ScrollView--hasControls) .ScrollView-inner{padding:0 var(--gutter)}.QuickReplies:not(.ScrollView--hasControls) .ScrollView-item:last-child{padding-right:var(--gutter)}.QuickReply{background:var(--white);border:1px solid transparent;border-radius:15px;color:var(--gray-1);cursor:pointer;font-size:.875rem;line-height:1.25rem;margin:0;padding:.25rem .75rem;position:relative;transition:.15s ease-in-out}.QuickReply.new:after{background:var(--red);border:1px solid #fff;border-radius:50%;content:"";height:.5rem;overflow:hidden;position:absolute;right:0;top:0;width:.5rem}.QuickReply.highlight{background:#fff;border-color:#ffd0bf;font-weight:500}.QuickReply-inner{align-items:center;display:flex}.QuickReply-img{max-height:1rem}.QuickReply-img,.QuickReply-inner>.Icon{margin-right:.1875rem}.QuickReply .Icon{color:var(--brand-1);font-size:.9375rem}.Checkbox,.Radio{-webkit-tap-highlight-color:transparent;background:var(--white);border:1px solid var(--gray-6);border-radius:9px;color:var(--gray-2);cursor:pointer;display:inline-block;font-size:.875rem;line-height:1.25rem;margin:.5625rem .75rem 0 0;padding:.25rem .75rem;position:relative;text-align:center;transition:.15s ease-in-out}.RadioGroup{margin-top:-.5625rem}.RadioGroup--block .Radio{display:block;margin-right:.5625rem}.CheckboxGroup--block .Checkbox{display:block;margin-right:0}.Checkbox--disabled,.Radio--disabled{cursor:auto;opacity:.5}.Checkbox--checked,.Radio--checked{border-color:var(--brand-1);color:var(--brand-1)}.Checkbox-input,.Radio-input{cursor:inherit;height:100%;left:0;margin:0;opacity:0;padding:0;position:absolute;top:0;width:100%}.RateActions{align-self:flex-end;margin:0 .5rem;position:relative;width:2rem;z-index:10}.RateBtn{background:var(--white);border-radius:50%;font-size:1.25rem;padding:.375rem}.RateBtn+.RateBtn{margin-top:.5625rem}.RateBtn[data-type=up].active,.RateBtn[data-type=up]:hover{color:var(--brand-1)}.RateBtn[data-type=down].active,.RateBtn[data-type=down]:hover{color:var(--blue)}.RichText{word-wrap:break-word;overflow-wrap:break-word}.ScrollView{overflow:hidden}.ScrollView-scroller{-webkit-overflow-scrolling:touch;-ms-overflow-style:none;scroll-behavior:smooth;scrollbar-width:none}.ScrollView-scroller::-webkit-scrollbar{display:none}.ScrollView--fullWidth{margin:0 calc(var(--gutter) * -1)}.ScrollView--fullWidth:not(.ScrollView--hasControls) .ScrollView-inner{padding:0 var(--gutter)}.ScrollView--x .ScrollView-scroller{display:flex;margin-bottom:-1.125rem;overflow-x:scroll;overflow-y:hidden;padding-bottom:1.125rem}.ScrollView--x .ScrollView-inner{display:flex}.ScrollView--x .ScrollView-item{flex:0 0 auto;margin-left:.375rem}.ScrollView--x .ScrollView-item:first-child{margin-left:0}.ScrollView--hasControls{align-items:center;display:flex}.ScrollView--hasControls .ScrollView-scroller{flex:1}.ScrollView-control{color:var(--gray-3);font-size:1rem;padding:.375rem}.ScrollView-control:not(:disabled):hover{color:var(--brand-1)}.Search{align-items:center;background:var(--white);border-radius:50px;display:flex;padding:.1875rem .3125rem .1875rem .9375rem}.Search-clear,.Search-icon{font-size:var(--font-size-lg)}.Search-icon{color:var(--gray-3)}.Search-input{border:0;flex:1;padding:0 .5625rem}.Search-input::-webkit-search-cancel-button{display:none}.Search-input:focus+.Search-clear,.Search-input:focus~.Btn--primary{opacity:1}.Search-clear{color:var(--gray-5);opacity:0}.Search-clear:hover{background:initial;color:var(--gray-3)}.Search .Btn--primary{font-size:var(--font-size-xs);margin-left:.375rem;min-width:3.5rem;opacity:0;padding:.125rem .75rem}.Select{-webkit-appearance:none;appearance:none;background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");background-position:right .75rem center;background-repeat:no-repeat;background-size:1rem .75rem}.Select:disabled{background-color:var(--gray-7)}.Stepper{list-style-type:none;margin:0;padding:0}.Step{padding-bottom:1.125rem;padding-left:1.8125rem;position:relative}.Step:last-child{padding-bottom:0}.Step:last-child .Step-line{display:none}.Step--active .Step-dot{background:var(--brand-1)}.Step--active .Step-title span{color:var(--brand-1);font-weight:500}.Step--active .Step-desc{color:var(--gray-1)}.Step--active[data-status] .Step-line{top:1.625rem}.Step--active[data-status] .Step-icon{color:var(--red)}.Step--active[data-status=success] .Step-icon{color:var(--green)}.Step-icon{align-items:center;display:flex;font-size:1.5rem;height:1.5rem;justify-content:center;left:0;position:absolute;top:0;width:1.5rem}.Step-dot{background:var(--white);border:1px solid var(--brand-1);border-radius:50%;height:.5rem;width:.5rem}.Step-line{background:var(--brand-1);bottom:-.25rem;left:.75rem;opacity:.5;position:absolute;top:1.25rem;width:.0625rem}.Step-title{align-items:center;color:var(--gray-1);display:flex;font-size:var(--font-size-md)}.Step-title small{color:var(--gray-3);font-size:var(--font-size-xs);margin-left:.75rem}.Step-desc{color:var(--gray-3);font-size:var(--font-size-sm);margin-top:.1875rem}.Tabs-nav{display:flex;margin:.4375rem 0;position:relative}.Tabs-nav::-webkit-scrollbar{display:none}.Tabs--scrollable .Tabs-nav{-webkit-overflow-scrolling:touch;margin-bottom:-.6875rem;overflow:hidden;overflow-x:auto;padding-bottom:1.125rem}.Tabs--scrollable .Tabs-navPointer{bottom:1.125rem}.Tabs--scrollable .Tabs-navItem{flex:1 0 auto}.Tabs-navItem{flex:1;text-align:center}.Tabs-navLink{background:transparent;border:0;border-radius:20px;color:var(--gray-2);font-size:.875rem;padding:.25rem .75rem;transition:.3s}.Tabs-navLink:focus:not(:focus-visible){outline:0}.Tabs-navLink:hover{color:var(--gray-1);cursor:pointer}.Tabs-navLink.active{color:var(--gray-1);font-weight:700;position:relative;z-index:1}.Tabs-navPointer{background:var(--btn-primary-bg);border-radius:2px;bottom:0;height:.1875rem;left:0;position:absolute;transition:.3s}.Tabs-pane{display:none}.Tabs-pane.active{display:block}.Tag{border:1px solid var(--brand-1);border-radius:4px;color:var(--brand-1);display:inline-block;font-size:.75rem;line-height:1.25;margin:0 .25rem 0 0;padding:0 .375rem;position:relative;white-space:nowrap}.Tag--primary{border-color:transparent;color:var(--orange)}.Tag--primary:before{background:currentColor;border-radius:inherit;bottom:0;content:"";left:0;margin:-.0625rem;opacity:.14;position:absolute;right:0;top:0}.Tag--success{background:var(--green);border-color:var(--green);color:#fff}.Tag--danger{background:var(--red);border-color:var(--red);color:#fff}.Tag--warning{background:var(--orange);border-color:var(--orange);color:#fff}.Text--truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.Text--break{overflow-wrap:break-word!important;word-break:break-word!important}.Text--ellipsis{-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden;text-overflow:ellipsis}.Time{color:var(--gray-3);font-size:.75rem}.Toast{display:flex;justify-content:center;left:0;opacity:0;position:fixed;right:0;top:30%;-webkit-transform:translateY(-50%);transform:translateY(-50%);transition:all .3s ease 0s;visibility:hidden;z-index:200}.Toast[data-type=success] .Icon{color:var(--green)}.Toast[data-type=error] .Icon{color:var(--red)}.Toast[data-type=loading] .Icon{color:var(--brand-1)}.Toast.show{opacity:1;visibility:visible}.Toast .Icon{font-size:1.5rem;margin-right:.375rem}.Toast-content{background:rgba(0,0,0,.7);border-radius:12px;box-sizing:border-box;display:flex;max-width:18.75rem;padding:1.125rem 1.5rem}.Toast-message{color:var(--white);flex:1;font-size:1rem;margin:0;word-break:break-word}.Toolbar{padding:var(--gutter);padding-top:0}.Toolbar-item{display:inline-block;margin-top:var(--gutter);text-align:center;width:25%}.Toolbar-btn{border:0;color:var(--gray-2);display:inline-block;overflow:hidden;padding:.375rem;position:relative;vertical-align:top;width:4.75rem}.Toolbar-btn,.Toolbar-btn:hover{background:transparent}.Toolbar-btn:active .Toolbar-btnIcon{background:rgba(0,0,0,.04)}@media (hover:hover){.Toolbar-btn:hover .Toolbar-btnIcon{background:rgba(0,0,0,.04)}}.Toolbar-btnIcon{background:var(--white);border-radius:12px;display:inline-block;padding:.75rem;transition:.3s}.Toolbar-btnIcon .Icon{font-size:1.875rem;vertical-align:top}.Toolbar-img{height:1.875rem;vertical-align:top;width:1.875rem}.Toolbar-btnText{display:block;font-size:.875rem;margin-top:.375rem}[data-tooltip]{cursor:pointer;position:relative}[data-tooltip]:after,[data-tooltip]:before{bottom:100%;left:50%;opacity:0;pointer-events:none;position:absolute;-webkit-transform:translate(-50%,.25rem);transform:translate(-50%,.25rem);-webkit-transform-origin:top;transform-origin:top;transition:all .18s ease-out .18s;z-index:200}[data-tooltip]:after{background:var(--gray-1);border-radius:6px;color:var(--white);content:attr(aria-label);font-size:.75rem;margin-bottom:.625rem;padding:.5em 1em;white-space:nowrap}[data-tooltip]:before{border:.3125rem solid transparent;border-top:.3125rem solid var(--gray-1);content:"";height:0;-webkit-transform-origin:top;transform-origin:top;width:0}[data-tooltip]:hover:after,[data-tooltip]:hover:before{opacity:1;-webkit-transform:translate(-50%);transform:translate(-50%)}.Tree{background:var(--white)}.TreeNode-title{align-items:center;border-bottom:1px solid var(--gray-7);display:flex;justify-content:space-between;padding:.625rem .9375rem}.TreeNode-title:hover{background:var(--gray-7);color:var(--brand-1);cursor:pointer}.TreeNode:last-child .TreeNode-title{border:0}.TreeNode-children-title{background:var(--gray-7);border-bottom:1px solid var(--gray-7)}.TreeNode-title-text{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;flex:1;overflow:hidden;text-overflow:ellipsis}.TreeNode-children{display:none}.TreeNode-children-active{display:block}.Typing{align-items:center;display:flex;height:1.40625rem;transition:opacity .1s}.Typing-dot{-webkit-animation:typing-dot .8s linear infinite;animation:typing-dot .8s linear infinite;background:var(--gray-3);border-radius:50%;display:inline-block;height:.3125rem;margin-left:.5625rem;width:.3125rem}.Typing-dot:first-child{margin:0}.Typing-dot:nth-child(2){-webkit-animation-delay:-.2s;animation-delay:-.2s}.Typing-dot:nth-child(3){-webkit-animation-delay:-.4s;animation-delay:-.4s}@-webkit-keyframes typing-dot{0%{-webkit-transform:scale(.57);transform:scale(.57)}25%{-webkit-transform:scale(.78);transform:scale(.78)}50%{-webkit-transform:scale(1);transform:scale(1)}75%{-webkit-transform:scale(.78);transform:scale(.78)}to{-webkit-transform:scale(.57);transform:scale(.57)}}@keyframes typing-dot{0%{-webkit-transform:scale(.57);transform:scale(.57)}25%{-webkit-transform:scale(.78);transform:scale(.78)}50%{-webkit-transform:scale(1);transform:scale(1)}75%{-webkit-transform:scale(.78);transform:scale(.78)}to{-webkit-transform:scale(.57);transform:scale(.57)}}.Video{border-radius:inherit;position:relative}.Video-cover,.Video-video:not([hidden]){border-radius:inherit;display:block;max-height:100%;width:100%}.Video-duration{bottom:.3125rem;color:var(--white);position:absolute;right:.3125rem;z-index:1}.Video-duration:after{content:"＂"}.Video-playBtn{background:transparent;border:0;height:100%;left:0;margin:0;padding:0;position:absolute;top:0;width:100%}.Video-playBtn:hover{cursor:pointer}.Video-playIcon{background:url(//gw.alicdn.com/tfs/TB1p1mkqIbpK1RjSZFyXXX_qFXa-70-70.png) 0 0 no-repeat;background-size:cover;display:inline-block;height:2.1875rem;width:2.1875rem}.Video--playing .Video-playBtn{display:none}.Carousel{overflow:hidden;position:relative}.Carousel--draggable .Carousel-inner{cursor:grab;touch-action:pan-y}.Carousel--draggable .Carousel-inner:active{cursor:grabbing}.Carousel--rtl{direction:rtl}.Carousel--dragging .Carousel-item{pointer-events:none}.Carousel-inner{display:flex;will-change:transform}.Carousel-dots{bottom:.5rem;display:flex;justify-content:center;left:50%;list-style-type:none;margin:0;padding:0;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%);z-index:1}.Carousel-dot{background:var(--gray-4);border:0;border-radius:50%;cursor:pointer;display:block;height:.5rem;margin:0 .25rem;padding:0;transition:.3s;width:.5rem}.Carousel-dot.active{background:var(--brand-1)}.Goods{font-size:.875rem;padding:.75rem}.Goods+.Goods{border-top:1px solid var(--gray-7)}.Goods-img{border-radius:12px;height:4.5rem;margin-right:.5625rem;object-fit:cover;width:4.5rem}.Goods-name{font-size:.875rem;font-weight:400;margin:0 0 .1875rem}.Goods-main .Price{margin-right:.5625rem}.Goods-desc{color:var(--gray-3);margin-bottom:.1875rem;word-break:break-all}.Goods-meta{color:var(--gray-3);font-size:.625rem}.Goods-countUnit{color:var(--gray-3);font-size:.75rem;margin:.1875rem 0}.Goods-unit{font-size:.625rem;margin-left:.1875rem}.Goods-buyBtn{color:#fff;float:right;padding:.125rem}.Goods-buyBtn,.Goods-buyBtn:hover{background:var(--brand-1)}.Goods-detailBtn{border-radius:10px;font-size:.625rem;line-height:1.125rem;min-width:3rem;padding:0 .625rem}.Goods-aside{align-items:flex-end;display:flex;flex-direction:column;margin-left:.5625rem}.Goods-status{color:var(--highlight-2);font-size:.75rem}.FileCard{padding:.5rem}.FileCard-icon{color:var(--gray-2);height:3.75rem;margin-right:.5rem;position:relative}.FileCard-icon[data-type=pdf]{color:var(--red)}.FileCard-icon[data-type*=doc]{color:var(--blue)}.FileCard-icon[data-type*=ppt],.FileCard-icon[data-type=key]{color:var(--orange)}.FileCard-icon[data-type*=xls]{color:var(--green)}.FileCard-icon[data-type=rar],.FileCard-icon[data-type=zip]{color:var(--brand-1)}.FileCard-icon .Icon{font-size:3.75rem}.FileCard-name{height:2.375rem;line-height:1.4;margin-bottom:.25rem}.FileCard-ext{bottom:.9375rem;font-size:1rem;font-weight:700;left:1.25rem;max-width:3.125rem;position:absolute;text-transform:uppercase;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:left bottom;transform-origin:left bottom}.FileCard-meta{color:var(--gray-3);font-size:.75rem}.FileCard-meta>a,.FileCard-meta>span{margin-right:.625rem}.FileCard-meta a{color:var(--link-color);text-decoration:none}.Form{background:var(--white)}.Form.is-light .FormItem{padding:0}.Form.is-light .HelpText,.Form.is-light .Label,.FormItem{padding:0 var(--gutter)}.FormItem{position:relative}.FormItem+.FormItem{margin-top:1.25rem}.FormItem.required .Label:after{color:var(--red);content:"*";display:inline-block;font-family:SimSun,sans-serif;font-size:.875rem;line-height:1;vertical-align:middle}.FormItem.is-invalid .HelpText,.FormItem.is-invalid .Label{color:var(--red)}.FormItem.is-invalid .Input{border-color:var(--red)}.FormItem .CheckboxGroup,.FormItem .RadioGroup{margin-top:.625rem}.FormItem .Label+.Input{margin-top:.3125rem}.FormActions{background:var(--white);display:flex;padding:.625rem var(--gutter)}.FormActions .Btn{flex:1}.FormActions .Btn+.Btn{margin-left:.375rem}.MessageContainer{display:flex;flex:1;flex-direction:column;min-height:0;position:relative}.MessageContainer>.PullToRefresh{flex:1}.MessageContainer>.PullToRefresh>.PullToRefresh-inner{box-sizing:border-box;padding-bottom:3.375rem}.MessageContainer:focus{outline:0}.MessageList{font-size:.9375rem;padding:var(--gutter)}.RecorderToast{background:rgba(51,51,51,.87);border-radius:12px;color:var(--white);height:10rem;left:50%;padding:.625rem;position:fixed;text-align:center;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:10rem;z-index:100}.Recorder--cancel .RecorderToast{color:var(--red)}.Recorder--cancel .Recorder-btn{background:rgba(0,0,0,.04);color:var(--gray-3)}.RecorderToast-icon{font-size:2.3125rem;position:relative}.RecorderToast-waves{height:100%;position:absolute;transition:.3s;width:100%;z-index:-1}.RecorderToast-wave-1,.RecorderToast-wave-2,.RecorderToast-wave-3{-webkit-animation:wave 10s linear infinite;animation:wave 10s linear infinite;color:var(--brand-2);position:absolute;z-index:-1}.RecorderToast-wave-1{font-size:11rem;left:.875rem;opacity:.2;top:-1.5625rem}.RecorderToast-wave-2{font-size:11.625rem;left:-1.3125rem;opacity:.4;top:-.75rem}.RecorderToast-wave-3{font-size:4.4375rem;left:3.4375rem;opacity:.8;top:2.5rem}.Recorder-btn{-webkit-touch-callout:none;background:var(--white);border-radius:20px;color:var(--gray-dark);height:2.25rem;line-height:2.25rem;text-align:center;transition:.3s;-webkit-user-select:none;-ms-user-select:none;user-select:none}@-webkit-keyframes wave{0%{-webkit-transform:translateY(5%) rotate(0);transform:translateY(5%) rotate(0)}50%{-webkit-transform:translateY(-5%) rotate(180deg);transform:translateY(-5%) rotate(180deg)}to{-webkit-transform:translateY(5%) rotate(1turn);transform:translateY(5%) rotate(1turn)}}@keyframes wave{0%{-webkit-transform:translateY(5%) rotate(0);transform:translateY(5%) rotate(0)}50%{-webkit-transform:translateY(-5%) rotate(180deg);transform:translateY(-5%) rotate(180deg)}to{-webkit-transform:translateY(5%) rotate(1turn);transform:translateY(5%) rotate(1turn)}}.Composer{align-items:flex-end;display:flex;padding:.5625rem var(--gutter)}.Composer>div+div{margin-left:.5625rem}.Composer-actions{align-items:center;display:flex;height:2.25rem}.Composer-actions .IconBtn{color:var(--gray-1);font-size:1.875rem}.Composer-toggleBtn{transition:-webkit-transform .2s;transition:transform .2s;transition:transform .2s,-webkit-transform .2s}.Composer-toggleBtn.active .Icon{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.Composer-inputWrap{flex:1;position:relative}.Composer-input{background:var(--white);border:0;border-radius:12px;caret-color:var(--brand-2);font-size:.9375rem;line-height:1.25rem;max-height:8.25rem;overflow:hidden;padding:.5rem .75rem;transition:border-color .15s ease-in-out}.Composer-sendBtn{min-width:0;padding-left:1rem;padding-right:1rem}.SendConfirm .Modal-dialog{margin:1.25rem;width:30rem}.SendConfirm-inner{height:20rem;text-align:center}.SendConfirm-inner img{max-height:100%;max-width:100%}html{height:100vh}html[data-safari]{height:calc(100vh - calc(100vh - 100%))}#root,body{height:100%}body{margin:0}.ChatApp{-webkit-tap-highlight-color:transparent;background:var(--gray-6);color:var(--gray-1);display:flex;flex-direction:column;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;height:100%;line-height:1.5}.S--focusing{--safe-bottom:0}@media (hover:none){.S--focusing .MessageList{margin-top:75vh}}.ChatFooter{background:var(--gray-7);padding-bottom:var(--safe-bottom);position:relative;z-index:10}.frontend-chat-container .bot {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 60px;
  height: 60px;
  cursor: pointer;
  z-index: 1000;
}
.frontend-chat-container .bot .bot-icon {
  width: 100%;
  height: 100%;
  background: #0084ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: transform 0.3s ease;
}
.frontend-chat-container .bot .bot-icon:hover {
  transform: scale(1.1);
}
.frontend-chat-container .bot .bot-icon img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  opacity: 0;
  animation: iconRotateIn 0.3s ease forwards;
}
.frontend-chat-container .bot .bot-icon .notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}
.frontend-chat-container .bot-close {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 60px;
  height: 60px;
  background: #0084ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
}
.frontend-chat-container .bot-close:hover {
  transform: scale(1.1);
}
.frontend-chat-container .bot-close .anticon {
  color: white;
  font-size: 24px;
}
.frontend-chat-container .bot-close .anticon.entering {
  opacity: 0;
  animation: iconRotateIn 0.3s ease forwards;
}
.frontend-chat-container .bot-close .anticon.leaving {
  opacity: 1;
  animation: iconRotateOut 0.3s ease forwards;
}
@keyframes iconRotateIn {
  0% {
    opacity: 0;
    transform: rotate(-180deg);
  }
  100% {
    opacity: 1;
    transform: rotate(0);
  }
}
@keyframes iconRotateOut {
  0% {
    opacity: 1;
    transform: rotate(0);
  }
  100% {
    opacity: 0;
    transform: rotate(180deg);
  }
}
.frontend-chat-container .fade-in {
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}
.frontend-chat-container .fade-out {
  opacity: 1;
  animation: fadeOut 0.3s forwards;
}
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  to {
    opacity: 0;
  }
}
.frontend-chat-container .modal-content {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 470px;
  height: 65vh;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.frontend-chat-container .modal-content .chat-header-new {
  flex-shrink: 0;
  background: #0084ff;
  padding: 20px;
  color: white;
  text-align: center;
}
.frontend-chat-container .modal-content .chat-header-new .header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.frontend-chat-container .modal-content .chat-header-new .header-content .chat-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
}
.frontend-chat-container .modal-content .chat-header-new .header-content .header-text {
  text-align: center;
}
.frontend-chat-container .modal-content .chat-header-new .header-content .header-text .title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}
.frontend-chat-container .modal-content .chat-header-new .header-content .header-text .subtitle {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}
.frontend-chat-container .modal-content .chat-main-container {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  padding: 20px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  margin-bottom: 20px;
  overflow: hidden;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .card-title {
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item {
  padding: 16px 20px;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item:hover {
  background-color: #f8f9fa;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: #e6f3ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-avatar .anticon {
  color: #0084ff;
  font-size: 20px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-content {
  flex: 1;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-content .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-content .message-header .title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-content .message-header .time {
  font-size: 12px;
  color: #999;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .conversation-item .message-content .message-text {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .new-conversation {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  color: #0084ff;
  cursor: pointer;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .new-conversation:hover {
  background-color: #f8f9fa;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .new-conversation .anticon {
  margin-right: 8px;
  font-size: 16px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .conversation-list .new-conversation span {
  font-size: 14px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .search-box {
  padding: 16px 20px;
  position: relative;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .search-box .search-icon {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .search-box input {
  width: 100%;
  height: 40px;
  padding: 8px 16px 8px 40px;
  border: 1px solid #e8e8e8;
  border-radius: 20px;
  font-size: 14px;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .search-box input:focus {
  outline: none;
  border-color: #0084ff;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item:hover {
  background-color: #f8f9fa;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item .anticon {
  margin-right: 12px;
  color: #666;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item span {
  font-size: 14px;
  color: #333;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item .article-preview {
  margin-top: 6px;
  padding-left: 28px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item.all-articles {
  text-align: center;
  color: #0084ff;
}
.frontend-chat-container .modal-content .chat-main-container .content-wrapper .chat-card .article-list .article-item:last-child {
  border-bottom: none;
}
.frontend-chat-container .modal-content .chat-main-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.frontend-chat-container .modal-content .chat-main-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}
.frontend-chat-container .modal-content .chat-main-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.frontend-chat-container .modal-content .chat-main-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
.frontend-chat-container .modal-content.opening {
  opacity: 1;
  transform: scale(1);
  transform-origin: bottom right;
  transition: transform 0.3s ease-in;
}
.frontend-chat-container .modal-content.closing {
  opacity: 0;
  transform: scale(0);
  transform-origin: bottom right;
}
.frontend-chat-container .modal-content.closed {
  display: none;
}
@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes rotateIn {
  0% {
    opacity: 0;
    transform: rotate(-180deg) scale(0.3);
  }
  100% {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}
.frontend-chat-container .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}
.frontend-chat-container .loading-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.frontend-chat-container .loading-container .loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.frontend-chat-container .message-image-container {
  position: relative;
  display: inline-block;
}
.frontend-chat-container .message-image-container .image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
}
.frontend-chat-container .message-image-container .image-overlay.failed {
  flex-direction: column;
}
.frontend-chat-container .message-image-container .image-overlay.failed .error-text {
  color: #fff;
  margin-top: 8px;
}
.frontend-chat-container .message-failed-icon {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  color: #ff4d4f;
}
.frontend-chat-container .message-audio-container .file-content,
.frontend-chat-container .message-file-container .file-content,
.frontend-chat-container .message-audio-container .audio-content,
.frontend-chat-container .message-file-container .audio-content {
  display: flex;
  align-items: center;
}
.frontend-chat-container .message-audio-container .progress-text,
.frontend-chat-container .message-file-container .progress-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.frontend-chat-container .message-audio-container .file-size,
.frontend-chat-container .message-file-container .file-size {
  margin-left: 8px;
  color: #999;
}
.frontend-chat-container .conversation-item.skeleton {
  pointer-events: none;
}
.frontend-chat-container .conversation-item.skeleton .skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}
.frontend-chat-container .conversation-item.skeleton .skeleton-title {
  width: 120px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}
.frontend-chat-container .conversation-item.skeleton .skeleton-time {
  width: 60px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}
.frontend-chat-container .conversation-item.skeleton .skeleton-text {
  width: 200px;
  height: 14px;
  margin-top: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
.frontend-chat-container .conversation-list .conversation-item .message-avatar .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}
.frontend-chat-container .conversation-list .conversation-item .message-avatar .default-avatar {
  font-size: 24px;
  color: #666;
}
.frontend-chat-container .agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  font-size: 14px;
}
.frontend-chat-container .agent-status .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.frontend-chat-container .agent-status .status-indicator.online {
  background-color: #52c41a;
}
.frontend-chat-container .agent-status .status-indicator.offline {
  background-color: #ff4d4f;
}
.frontend-chat-container .chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}
.frontend-chat-container .chat-header .header-left .back-button {
  cursor: pointer;
  padding: 4px 8px;
}
.frontend-chat-container .chat-header .header-left .back-button:hover {
  background-color: #f5f5f5;
  border-radius: 4px;
}
.frontend-chat-container .chat-header .header-title {
  flex: 1;
  text-align: center;
}
.frontend-chat-container .chat-header .header-title .agent-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  font-size: 14px;
  background: #f5f5f5;
  border-radius: 16px;
}
.frontend-chat-container .chat-header .header-title .agent-status .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.frontend-chat-container .chat-header .header-title .agent-status .status-indicator.online {
  background-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}
.frontend-chat-container .chat-header .header-title .agent-status .status-indicator.offline {
  background-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}
.frontend-chat-container .chat-header .header-right {
  cursor: pointer;
  padding: 4px;
}
.frontend-chat-container .chat-header .header-right:hover {
  background-color: #f5f5f5;
  border-radius: 4px;
}
.frontend-chat-container .access-options-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 20px;
}
.frontend-chat-container .access-options-card .card-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}
.frontend-chat-container .access-options-card .access-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}
.frontend-chat-container .access-options-card .access-buttons .access-button {
  flex: 1;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}
.frontend-chat-container .access-options-card .access-buttons .access-button .anticon {
  font-size: 24px;
  color: #0084ff;
}
.frontend-chat-container .access-options-card .access-buttons .access-button span {
  font-size: 16px;
  color: #333;
}
.frontend-chat-container .access-options-card .access-buttons .access-button:hover {
  border-color: #0084ff;
  background: #f0f7ff;
  transform: translateY(-2px);
}
.frontend-chat-container .access-options-card .access-buttons .access-button.login:hover {
  border-color: #0084ff;
}
.frontend-chat-container .access-options-card .access-buttons .access-button.login:hover .anticon {
  color: #0084ff;
}
.frontend-chat-container .access-options-card .access-buttons .access-button.guest:hover {
  border-color: #52c41a;
}
.frontend-chat-container .access-options-card .access-buttons .access-button.guest:hover .anticon {
  color: #52c41a;
}
.frontend-chat-container .access-options-card .back-option {
  text-align: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
.frontend-chat-container .access-options-card .back-option .ant-btn-link {
  color: #666;
}
.frontend-chat-container .access-options-card .back-option .ant-btn-link:hover {
  color: #0084ff;
}
.frontend-chat-container .guest-login-button {
  text-align: center;
  padding: 16px;
  margin-bottom: 20px;
  background: #f0f7ff;
  border-radius: 8px;
}
.frontend-chat-container .guest-login-button .ant-btn {
  min-width: 160px;
  height: 40px;
  font-size: 16px;
}
.frontend-chat-container .guest-login-button .ant-btn .anticon {
  margin-right: 8px;
}
.frontend-chat-container .guest-login-button .guest-tip {
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}
.frontend-chat-container .chat-login-now {
  padding: 16px;
  text-align: center;
  background: #f0f7ff;
  border-radius: 8px;
  margin-bottom: 16px;
}
.frontend-chat-container .chat-login-now .login-button {
  width: 100%;
  height: 40px;
  font-size: 14px;
}
.frontend-chat-container .chat-login-now .login-button .anticon {
  margin-right: 8px;
}
.login-view .back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  transition: all 0.3s;
}
.login-view .back-button:hover {
  color: #0084ff;
}
.login-view .back-button .anticon {
  font-size: 16px;
}
.ant-modal-confirm .ant-modal-confirm-title {
  font-size: 18px;
  color: #333;
}
.ant-modal-confirm .ant-modal-confirm-content {
  font-size: 14px;
  color: #666;
  margin-top: 16px;
}
.ant-modal-confirm .ant-modal-confirm-content p {
  margin-bottom: 8px;
}
.ant-modal-confirm .ant-modal-confirm-btns {
  margin-top: 24px;
}
.guest-banner {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #e6f7ff;
  padding: 12px 16px;
  border-bottom: 1px solid #91d5ff;
  margin-bottom: 16px;
}
.guest-banner .guest-banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.guest-banner .guest-banner-content span {
  color: #0050b3;
  font-size: 14px;
  font-weight: 500;
}
.guest-banner .guest-banner-content .ant-btn {
  margin-left: 16px;
  background-color: #1890ff;
  border-color: #1890ff;
}
.guest-banner .guest-banner-content .ant-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}
.guest-banner .guest-banner-content .ant-btn .anticon {
  margin-right: 8px;
}

  
  .Bubble {
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 30px;
    margin-bottom: 8px;
    border: 1px solid #e6e6e6;
    background-color: white;
    color: black;
    font-weight: 500 ;
  }
  
  .Bubble.left {
    background-color: #f0f0f0;
    align-self: flex-start;
  }
  
  .Bubble.right {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
  }
  
  .Card {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;
    width: 80%;
    padding: 10px;
    background-color: white;
  }
  
  .CardTitle {
    font-size: 16px;
    font-weight: bold;
    padding: 10px;
  }
  
  .CardText {
    padding: 0 10px 10px;
  }
  
  .CardActions {
    padding: 10px;
    border-top: 1px solid #e6e6e6;
  }
  .card-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  .card-link{
   text-align: right;
   a{
    text-decoration: none;
    color: white;
    background-color: #007bff;
    padding: 6px 12px;
    border-radius: 17px;
    cursor: pointer;  
   }
  }
  .List{
    width: 80%;
    padding: 10px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background-color: white;
    color: black;
    font-weight: 600;
    cursor: pointer;
  }
  .ListItem{
    padding: 10px;
    border-bottom: 1px solid #e6e6e6;
  }
  .Button {
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
  }
  
  .Button:hover {
    background-color: #0056b3;
  }.login-view {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}
.login-view .login-container {
  width: 100%;
  max-width: 320px;
}
.login-view .login-container .login-header {
  text-align: center !important;
  margin-bottom: 24px;
}
.login-view .login-container .login-header .login-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto;
  margin-bottom: 16px;
}
.login-view .login-container .login-header h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 8px;
}
.login-view .login-container .login-header .login-subtitle {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}
.login-view .login-container .error-message {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}
.login-view .login-container .success-message {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.login-view .login-container .success-message .anticon {
  font-size: 16px;
}
.login-view .login-container .input-group {
  position: relative;
  margin-bottom: 16px;
}
.login-view .login-container .input-group .input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
  font-size: 16px;
}
.login-view .login-container .input-group input {
  width: 100%;
  padding: 12px 12px 12px 36px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
}
.login-view .login-container .input-group input:focus {
  outline: none;
  border-color: #0084ff;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}
.login-view .login-container .input-group input::placeholder {
  color: #bfbfbf;
}
.login-view .login-container .input-group input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}
.login-view .login-container .submit-button {
  width: 100%;
  padding: 12px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.login-view .login-container .submit-button:disabled {
  background: #b3d4ff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.login-view .login-container .submit-button .loading-icon {
  font-size: 16px;
  animation: spin 1s linear infinite;
}
.login-view .login-container .submit-button:not(:disabled):hover {
  background: #0070d9;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 132, 255, 0.2);
}
.login-view .login-container .submit-button:not(:disabled):active {
  transform: translateY(0);
}
.login-view .login-container .switch-mode {
  text-align: center;
  margin-top: 16px;
}
.login-view .login-container .switch-mode span {
  color: #0084ff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}
.login-view .login-container .switch-mode span:hover {
  color: #0070d9;
  text-decoration: underline;
}
.login-view .language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}
.login-view .language-selector .language-button {
  cursor: pointer;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}
.login-view .language-selector .language-button:hover {
  color: #0084ff;
  border-color: #0084ff;
}
.login-view .language-selector .language-button .anticon {
  font-size: 16px;
}
.login-view .language-dropdown-overlay .ant-dropdown-menu {
  padding: 4px 0;
}
.login-view .language-dropdown-overlay .ant-dropdown-menu .ant-dropdown-menu-item {
  padding: 8px 16px;
  min-width: 120px;
}
.login-view .language-dropdown-overlay .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
  color: #0084ff;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
