<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销型落地页</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .hero-section {
            position: relative;
            overflow: hidden;
        }
        .feature-card {
            padding: 2rem;
            background-color: #112240;
            border-radius: 8px;
            height: 100%;
        }
        .stats-box {
            text-align: center;
            margin-bottom: 2rem;
        }
        .testimonial-card {
            position: relative;
            padding: 2rem;
            background-color: #fff;
            border-radius: 10px;
            margin: 1rem 0;
        }
        .explore-more {
            display: inline-block;
            color: #6366f1;
            text-decoration: none;
            font-weight: 500;
        }
        .explore-more:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="marketing-landing-page">
        <!-- 导航栏 -->
        <nav class="py-3 bg-white navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <img src="https://7528302.fs1.hubspotusercontent-na1.net/hub/7528302/hubfs/theme_hubspot/elevate/images/hexagontalxio-dark.png" height="30" alt="Logo">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Features</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Pricing</a>
                        </li>
                    </ul>
                    <button class="btn btn-primary rounded-pill ms-3">Get started</button>
                </div>
            </div>
        </nav>

        <!-- 头部区域 -->
        <div class="py-5 hero-section" data-bs-component="hero">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div data-bs-component="rich-text" class="bootstrap-heading">
                            <h1 class="mb-3 display-4 fw-bold">Create legendary brands</h1>
                            <p class="text-muted">Add a brief and powerful description of your business value proposition and how you solve for customers</p>
                        </div>
                        <div class="gap-2 mt-4 d-flex">
                            <div data-bs-component="button">
                                <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Request demo</button>
                            </div>
                            <div data-bs-component="button">
                                <button class="px-4 py-2 bootstrap-button btn btn-outline-secondary rounded-pill">Learn more</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="position-relative">
                            <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/hero-banner.png" class="img-fluid rounded-4" alt="Hero image">
                            <div class="p-3 bg-white shadow position-absolute top-50 end-0 translate-middle-y rounded-3" style="max-width: 200px; right: -30px;">
                                <div class="gap-2 d-flex align-items-center">
                                    <div class="rounded-circle bg-success" style="width: 12px; height: 12px;"></div>
                                    <span>Browser preview</span>
                                </div>
                                <div class="mt-2">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="previewSwitch1" checked>
                                        <label class="form-check-label" for="previewSwitch1">Mobile</label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="previewSwitch2" checked>
                                        <label class="form-check-label" for="previewSwitch2">Desktop</label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="previewSwitch3" checked>
                                        <label class="form-check-label" for="previewSwitch3">Tablet</label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="previewSwitch4" checked>
                                        <label class="form-check-label" for="previewSwitch4">Print</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 特性区域 -->
        <div data-bs-component="feature-cards" class="py-5 text-white bg-dark">
            <div class="container">
                <div class="row">
                    <div class="mb-4 col-md-4">
                        <div class="text-center feature-card">
                            <div class="mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-play-circle" viewBox="0 0 16 16">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                    <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
                                </svg>
                            </div>
                            <h3>Content Creation</h3>
                            <p class="text-white-50">Stand out with our captivating content on social services, videos to engage their digital audience</p>
                        </div>
                    </div>
                    <div class="mb-4 col-md-4">
                        <div class="text-center feature-card">
                            <div class="mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bar-chart" viewBox="0 0 16 16">
                                    <path d="M4 11H2v3h2v-3zm5-4H7v7h2V7zm5-5v12h-2V2h2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1h-2zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-3z"/>
                                </svg>
                            </div>
                            <h3>Marketing Analytics</h3>
                            <p class="text-white-50">Our platform empowers informed decision making, tracking campaign performance, roi, and ROI</p>
                        </div>
                    </div>
                    <div class="mb-4 col-md-4">
                        <div class="text-center feature-card">
                            <div class="mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-graph-up" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
                                </svg>
                            </div>
                            <h3>Journey Optimization</h3>
                            <p class="text-white-50">Use customer data to deliver personalized messaging and content, enhancing engagement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 协作部分 -->
        <div data-bs-component="info-section" class="py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="mb-4 col-lg-6 mb-lg-0">
                        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/analytics-dashboard.png" class="img-fluid rounded-4" alt="Analytics dashboard">
                    </div>
                    <div class="col-lg-6">
                        <div data-bs-component="rich-text">
                            <h2 class="mb-3 fw-bold">Collaborate seamlessly</h2>
                            <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                        </div>
                        <div class="mt-3" data-bs-component="button">
                            <a href="#" class="explore-more">Explore more</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 多渠道营销 -->
        <div data-bs-component="info-section" class="py-5 bg-light">
            <div class="container">
                <div class="row align-items-center">
                    <div class="mb-4 col-lg-6 order-lg-2 mb-lg-0">
                        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/multichannel-campaign.png" class="img-fluid rounded-4" alt="Multichannel campaign">
                    </div>
                    <div class="col-lg-6 order-lg-1">
                        <div data-bs-component="rich-text">
                            <h2 class="mb-3 fw-bold">Manage multi-channel campaigns</h2>
                            <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                        </div>
                        <div class="mt-3" data-bs-component="button">
                            <a href="#" class="explore-more">Explore more</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容创建 -->
        <div data-bs-component="info-section" class="py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="mb-4 col-lg-6 mb-lg-0">
                        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/content-calendar.png" class="img-fluid rounded-4" alt="Content calendar">
                    </div>
                    <div class="col-lg-6">
                        <div data-bs-component="rich-text">
                            <h2 class="mb-3 fw-bold">Create and target content dynamically</h2>
                            <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                        </div>
                        <div class="mt-3" data-bs-component="button">
                            <a href="#" class="explore-more">Explore more</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户推荐 -->
        <div data-bs-component="testimonial-slider" class="py-5 bg-light">
            <div class="container">
                <div class="testimonial-card">
                    <div class="row">
                        <div class="mb-3 text-center col-md-3 mb-md-0">
                            <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/testimonial-avatar.png" class="mb-3 rounded-circle" width="100" height="100" alt="Testimonial avatar">
                        </div>
                        <div class="col-md-9">
                            <div data-bs-component="rich-text">
                                <p class="mb-4 lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
                                <h5 class="mb-1">Neel Kumar</h5>
                                <p class="text-muted">VP of Marketing @ Heptawise</p>
                            </div>
                            <div class="mt-3">
                                <a href="#" class="text-decoration-none">Read case study →</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <div class="gap-2 d-inline-flex">
                        <button class="btn btn-sm btn-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                        <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                        <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                        <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                        <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据统计 -->
        <div data-bs-component="stats" class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-4">
                        <div class="stats-box">
                            <h2 class="display-3 fw-bold text-primary">15k+</h2>
                            <p class="text-muted">Customers of Elevate</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-box">
                            <h2 class="display-3 fw-bold text-primary">200%</h2>
                            <p class="text-muted">Daily active users</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-box">
                            <h2 class="display-3 fw-bold text-primary">300%</h2>
                            <p class="text-muted">Daily active users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 行动号召 -->
        <div data-bs-component="cta" class="py-5 text-white bg-primary">
            <div class="container text-center">
                <h2 class="mb-4 display-6 fw-bold" data-bs-component="rich-text">Ready to Elevate your business?</h2>
                <button data-bs-component="button" class="px-4 py-2 bootstrap-button btn btn-light rounded-pill">Get in touch</button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div data-bs-component="footer" class="py-5 text-white bg-dark">
            <div class="container">
                <div class="text-center">
                    <p>No menus installed from the sidebar, create a new one by navigating to the Navigation Menu tool</p>
                </div>
                <div data-bs-component="social-flow" class="social-flow-block social-flow-container">
                    <div class="social-icons-wrapper">
                        <div class="social-icons-row">
                            <a href="#" class="social-icon facebook-icon" title="Facebook" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                                </svg>
                            </a>
                            <a href="#" class="social-icon twitter-icon" title="Twitter" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/>
                                </svg>
                            </a>
                            <a href="#" class="social-icon instagram-icon" title="Instagram" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                                </svg>
                            </a>
                            <a href="#" class="social-icon linkedin-icon" title="LinkedIn" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/>
                                </svg>
                            </a>
                            <a href="#" class="social-icon youtube-icon" title="YouTube" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <style>
                    .social-flow-container {
                      display: flex;
                      justify-content: center;
                      padding: 15px 0;
                    }
                    .social-icons-wrapper {
                      display: inline-flex;
                      align-items: center;
                    }
                    .social-icons-row {
                      display: flex;
                      flex-wrap: wrap;
                      gap: 15px;
                      justify-content: center;
                    }
                    .social-icon {
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      width: 40px;
                      height: 40px;
                      border-radius: 50%;
                      color: white;
                      font-size: 18px;
                      transition: all 0.3s ease;
                    }
                    .facebook-icon {
                      background-color: #1877F2;
                    }
                    .twitter-icon {
                      background-color: #1DA1F2;
                    }
                    .instagram-icon {
                      background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
                    }
                    .linkedin-icon {
                      background-color: #0A66C2;
                    }
                    .youtube-icon {
                      background-color: #FF0000;
                    }
                    .social-icon:hover {
                      transform: translateY(-3px);
                      box-shadow: 0 5px 10px rgba(0,0,0,0.2);
                    }
                    </style>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>