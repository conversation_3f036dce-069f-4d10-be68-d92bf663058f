APP_NAME=Bwms后台管理系统
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://new-bwms.bingo-test.com

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=root
DB_PREFIX=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
VITE_BASE_URL=${APP_URL}/api/
VITE_APP_NAME=${APP_NAME}

ALIOSS_BUCKET=
ALIOSS_ACCESS_ID=
ALIOSS_ACCESS_SECRET=
ALIOSS_ENDPOINT=
ALIOSS_UPLOAD_DIR=

MULTI_LANGUAGE_ENABLED=true

MODULE_STORE_BASE=http://127.0.0.1:8002
VITE_URL=http://127.0.0.1:8001

# 前台环境变量
VITE_API_BASE_URL=${APP_URL}/api
VITE_FRONTEND_API_BASE_URL=${APP_URL}/api
FRONTEND_APP_NAME=BwmsClient

