CREATE TABLE `news` (
  `news_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `publish_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  `category_id` INT,
  `status` TINYINT NOT NULL COMMENT '0-草稿, 1-已发布, 2-已下架, 3-已归档',
  `archive_time` DATETIME COMMENT '归档时间',
  `show_related` BOOLEAN DEFAULT false COMMENT '是否显示相关文章',
  `is_sponsored` BOOLEAN DEFAULT false COMMENT '是否为赞助内容',
  `geo_restricted` BOOLEAN DEFAULT false COMMENT '是否限制香港地区访问',
  `created_by` INT NOT NULL COMMENT '创建者管理员ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `new_detail` (
  `detail_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `news_id` BIGINT NOT NULL,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `view_count` INT DEFAULT 0,
  `share_count` BIGINT DEFAULT 0,
  `thumbnail_url` VARCHAR(255),
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `seo_title` VARCHAR(200) DEFAULT null COMMENT 'SEO 标题',
  `seo_description` VARCHAR(200) DEFAULT null COMMENT 'SEO 描述',
  `seo_keywords` VARCHAR(200) DEFAULT null COMMENT 'SEO 关键词',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `videos` (
  `video_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `publish_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL COMMENT '0-处理中, 1-可用, 2-已下架',
  `created_by` INT NOT NULL COMMENT '上传者管理员ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `content_videos` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `content_id` BIGINT NOT NULL COMMENT '关联内容ID',
  `content_type` TINYINT NOT NULL COMMENT '1-新闻, 2-节目',
  `video_id` BIGINT NOT NULL,
  `is_primary` BOOLEAN DEFAULT false COMMENT '是否主视频',
  `sort` INT DEFAULT 0 COMMENT '排序',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_content_video` (`content_id`, `content_type`, `video_id`),
  FOREIGN KEY (`video_id`) REFERENCES `videos` (`video_id`) ON DELETE CASCADE
);

CREATE TABLE `video_detail` (
  `detail_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `video_id` BIGINT NOT NULL,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `view_count` BIGINT DEFAULT 0,
  `thumbnails` JSON NOT NULL COMMENT '缩略图JSON，格式：{"large": "url1", "medium": "url2", "small": "url3", "covers": ["url4", "url5", "url6"]}',
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT,
  `duration` INT NOT NULL COMMENT '视频时长(秒)',
  `cloud_url` VARCHAR(255) NOT NULL COMMENT '云端存储URL',
  `local_url` VARCHAR(255) COMMENT '本地存储URL',
  `storage_location` TINYINT NOT NULL DEFAULT 1 COMMENT '1-云端, 2-本地, 3-两者都有',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `categories` (
  `category_id` INT PRIMARY KEY AUTO_INCREMENT,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `name` VARCHAR(50) NOT NULL,
  `content` VARCHAR(255),
  `parent_id` INT,
  `sort` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `tags` (
  `tag_id` INT PRIMARY KEY AUTO_INCREMENT,
  `names` JSON NOT NULL COMMENT '多语言标签名称，格式：{"zh-HK": "香港繁體", "zh-TW": "台灣繁體", "zh-CN": "簡體"}',
  `created_by` INT NOT NULL COMMENT '管理员ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间',
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT
);

CREATE TABLE `news_tags_mapping` (
  `tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `new_id` BIGINT NOT NULL,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `news_author_mapping` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `news_id` BIGINT NOT NULL,
  `admin_id` INT NOT NULL COMMENT '作者ID，对应管理员表',
  `pen_name` VARCHAR(100) COMMENT '笔名，用于前台显示',
  `is_primary` BOOLEAN DEFAULT false COMMENT '是否为主要作者',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `programs` (
  `program_id` INT PRIMARY KEY AUTO_INCREMENT,
  `category_id` int,
  `publish_date` DATE,
  `status` TINYINT NOT NULL COMMENT '0-未开始, 1-进行中, 2-已结束',
  `sort` INT DEFAULT 0,
  `created_by` INT NOT NULL COMMENT '创建者管理员ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `program_detail` (
  `detail_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `program_id` INT NOT NULL,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `thumbnail_url` VARCHAR(255),
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `devices` (
  `device_id` VARCHAR(100) PRIMARY KEY,
  `device_type` VARCHAR(50) NOT NULL COMMENT '如iOS, Android等',
  `device_model` VARCHAR(100),
  `os_version` VARCHAR(50),
  `app_version` VARCHAR(50),
  `fcm_token` VARCHAR(255) COMMENT 'Firebase设备令牌',
  `fcm_token_updated_at` DATETIME COMMENT 'FCM令牌更新时间',
  `push_enabled` BOOLEAN DEFAULT true COMMENT '是否启用推送',
  `push_channel` TINYINT DEFAULT 1 COMMENT '1-FCM, 2-APNS, 3-其他',
  `preferred_lang` VARCHAR(50) DEFAULT 'zh-HK' COMMENT '用户首选语言',
  `last_active_time` DATETIME,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `push_categories` (
  `category_id` INT PRIMARY KEY AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` TEXT COMMENT '分类描述',
  `is_active` BOOLEAN DEFAULT true,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `user_segments` (
  `segment_id` INT PRIMARY KEY AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL COMMENT '分群名称',
  `description` TEXT COMMENT '分群描述',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE `user_segment_mapping` (
  `mapping_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `segment_id` INT NOT NULL,
  `device_id` VARCHAR(100) NOT NULL,
  `score` FLOAT DEFAULT 1.0 COMMENT '用户在该分群中的匹配度分数',
  `last_updated` DATETIME NOT NULL COMMENT '最后更新时间',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_segment_device` (`segment_id`, `device_id`),
  FOREIGN KEY (`segment_id`) REFERENCES `user_segments` (`segment_id`) ON DELETE CASCADE,
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `push_notifications` (
  `notification_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `category_id` INT NOT NULL COMMENT '推送分类ID',
  `title` VARCHAR(255) NOT NULL COMMENT '推送标题',
  `content` TEXT NOT NULL COMMENT '推送内容',
  `deeplink` VARCHAR(255) COMMENT '深度链接',
  `image_url` VARCHAR(255) COMMENT '推送图片',
  `segment_id` INT COMMENT '目标用户分群ID',
  `device_ids` JSON COMMENT '指定设备ID列表',
  `send_type` TINYINT NOT NULL COMMENT '1-立即发送, 2-定时发送',
  `scheduled_time` DATETIME COMMENT '计划发送时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0-待发送, 1-发送中, 2-已发送, 3-已取消',
  `total_devices` INT DEFAULT 0 COMMENT '目标设备总数',
  `sent_count` INT DEFAULT 0 COMMENT '已发送数',
  `click_count` INT DEFAULT 0 COMMENT '点击数',
  `created_by` INT NOT NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`category_id`) REFERENCES `push_categories` (`category_id`),
  FOREIGN KEY (`segment_id`) REFERENCES `user_segments` (`segment_id`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`)
);

CREATE TABLE `push_records` (
  `record_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `notification_id` BIGINT NOT NULL,
  `device_id` VARCHAR(100) NOT NULL,
  `message_id` VARCHAR(100) COMMENT 'FCM消息ID',
  `status` TINYINT NOT NULL COMMENT '0-待发送, 1-发送中, 2-发送成功, 3-发送失败, 4-设备不支持, 5-用户禁用',
  `error_message` TEXT COMMENT '错误信息',
  `sent_time` DATETIME COMMENT '发送时间',
  `delivery_status` TINYINT DEFAULT 0 COMMENT '0-未知, 1-已送达, 2-已显示',
  `is_clicked` BOOLEAN DEFAULT false COMMENT '是否点击',
  `clicked_time` DATETIME COMMENT '点击时间',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`notification_id`) REFERENCES `push_notifications` (`notification_id`),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `votings` (
  `voting_id` INT PRIMARY KEY AUTO_INCREMENT,
  `category_id` INT,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL COMMENT '0-未开始, 1-进行中, 2-已结束',
  `created_by` INT NOT NULL COMMENT '创建者管理员ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间',
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT
);

CREATE TABLE `voting_detail` (
  `detail_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `voting_id` INT NOT NULL,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT,
  `thumbnail_url` VARCHAR(255),
  `imgs` JSON COMMENT '图片数组',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_voting_lang` (`voting_id`, `lang`),
  FOREIGN KEY (`voting_id`) REFERENCES `votings` (`voting_id`) ON DELETE CASCADE
);

CREATE TABLE `voting_questions` (
  `question_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `voting_id` INT NOT NULL,
  `allow_multiple` BOOLEAN DEFAULT false COMMENT '是否允许多选',
  `max_options` INT DEFAULT 1 COMMENT '最多可选选项数',
  `sort` INT DEFAULT 0,
  `title` JSON NOT NULL COMMENT '多语言标题',
  `content` JSON COMMENT '多语言内容',
  `image_url` JSON COMMENT '多语言图片',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间',
  FOREIGN KEY (`voting_id`) REFERENCES `votings` (`voting_id`) ON DELETE CASCADE
);

CREATE TABLE `voting_options` (
  `option_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `question_id` BIGINT NOT NULL,
  `sort` INT DEFAULT 0,
  `title` JSON NOT NULL COMMENT '多语言标题',
  `image_url` JSON COMMENT '多语言图片',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间',
  FOREIGN KEY (`question_id`) REFERENCES `voting_questions` (`question_id`) ON DELETE CASCADE
);

CREATE TABLE `voting_results` (
  `result_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `voting_id` INT NOT NULL,
  `question_id` BIGINT NOT NULL,
  `option_id` BIGINT NOT NULL,
  `device_id` VARCHAR(100) NOT NULL COMMENT '投票设备ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  UNIQUE KEY `uk_voting_device_question` (`voting_id`, `device_id`, `question_id`),
  FOREIGN KEY (`voting_id`) REFERENCES `votings` (`voting_id`) ON DELETE CASCADE,
  FOREIGN KEY (`question_id`) REFERENCES `voting_questions` (`question_id`) ON DELETE CASCADE,
  FOREIGN KEY (`option_id`) REFERENCES `voting_options` (`option_id`) ON DELETE CASCADE,
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `admins` (
  `admin_id` INT PRIMARY KEY AUTO_INCREMENT,
  `username` VARCHAR(50) UNIQUE NOT NULL,
  `password` VARCHAR(255) NOT NULL,
  `real_name` VARCHAR(100) COMMENT '真实姓名',
  `pen_name` VARCHAR(100) COMMENT '笔名，用于前台显示',
  `email` VARCHAR(100) UNIQUE NOT NULL,
  `role_id` INT NOT NULL COMMENT '角色: super_admin, content_editor, etc',
  `avatar_url` VARCHAR(255),
  `last_login` DATETIME,
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0-禁用, 1-启用',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `ad_configurations` (
  `config_id` INT PRIMARY KEY AUTO_INCREMENT,
  `ad_position` VARCHAR(50) NOT NULL COMMENT '广告位置: home_banner, article_inline, etc',
  `ad_unit_id` VARCHAR(100) NOT NULL COMMENT 'Google Ads单元ID',
  `lang` VARCHAR(50) NOT NULL COMMENT '广告内容语言',
  `is_active` BOOLEAN DEFAULT true,
  `start_date` DATE,
  `end_date` DATE,
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '软删除时间'
);

CREATE TABLE `user_browsing_history` (
  `history_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
  `content_type` TINYINT NOT NULL COMMENT '1-新闻, 2-视频, 3-直播, 4-专题',
  `content_id` BIGINT NOT NULL COMMENT '内容ID',
  `category_id` INT COMMENT '内容分类ID',
  `view_duration` INT DEFAULT 0 COMMENT '观看时长(秒)',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL
);

CREATE TABLE `user_search_history` (
  `search_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
  `keyword` VARCHAR(255) NOT NULL COMMENT '搜索关键词',
  `search_result_count` INT DEFAULT 0 COMMENT '搜索结果数量',
  `clicked_content_id` BIGINT COMMENT '点击的内容ID',
  `clicked_content_type` TINYINT COMMENT '点击的内容类型',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `user_share_history` (
  `share_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
  `content_type` TINYINT NOT NULL COMMENT '1-新闻, 2-视频, 3-直播, 4-专题',
  `content_id` BIGINT NOT NULL COMMENT '内容ID',
  `share_platform` VARCHAR(50) NOT NULL COMMENT '分享平台',
  `share_url` VARCHAR(255) COMMENT '分享链接',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `user_favorites` (
  `favorite_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
  `content_type` TINYINT NOT NULL COMMENT '1-新闻, 2-视频, 3-直播, 4-专题',
  `content_id` BIGINT NOT NULL COMMENT '内容ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_device_content` (`device_id`, `content_type`, `content_id`),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `news_ai_summaries` (
  `summary_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `news_id` BIGINT NOT NULL,
  `lang` VARCHAR(50) NOT NULL COMMENT '语言代码',
  `content` TEXT NOT NULL COMMENT '摘要内容',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  FOREIGN KEY (`news_id`) REFERENCES `news` (`news_id`) ON DELETE CASCADE
);

CREATE TABLE `video_transcoding_tasks` (
  `task_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `video_id` BIGINT NOT NULL,
  `status` TINYINT NOT NULL COMMENT '0-等待中, 1-处理中, 2-完成, 3-失败',
  `quality` VARCHAR(10) NOT NULL COMMENT 'SD/HD',
  `input_url` VARCHAR(255) NOT NULL COMMENT '输入视频URL',
  `output_url` VARCHAR(255) COMMENT '输出视频URL',
  `error_message` TEXT COMMENT '错误信息',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) COMMENT '更新时间(完成時間)',
  FOREIGN KEY (`video_id`) REFERENCES `videos` (`video_id`) ON DELETE CASCADE
);

CREATE TABLE `mq_tasks` (
  `task_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `queue_name` VARCHAR(100) NOT NULL COMMENT '队列名称',
  `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型',
  `status` TINYINT NOT NULL COMMENT '0-等待中, 1-处理中, 2-完成, 3-失败',
  `payload` JSON NOT NULL COMMENT '任务数据',
  `error_message` TEXT COMMENT '错误信息',
  `retry_count` INT DEFAULT 0 COMMENT '重试次数',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE `user_recommendations` (
  `recommendation_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `device_id` VARCHAR(100) NOT NULL,
  `content_type` TINYINT NOT NULL COMMENT '1-新闻, 2-视频, 3-直播, 4-专题',
  `content_id` BIGINT NOT NULL,
  `score` FLOAT NOT NULL COMMENT '推荐分数',
  `is_clicked` BOOLEAN DEFAULT false COMMENT '是否点击',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
);

CREATE TABLE `news_cache` (
    `cache_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `news_id` BIGINT NOT NULL,
    `lang` VARCHAR(50) NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `content` TEXT NOT NULL,
    `thumbnail_url` VARCHAR(255),
    `category_id` INT,
    `category_name` VARCHAR(50),
    `tag_names` VARCHAR(255),
    `author_names` VARCHAR(255),
    `publish_time` DATETIME NOT NULL,
    `view_count` INT DEFAULT 0,
    `share_count` BIGINT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_news_lang` (`news_id`, `lang`),
    KEY `idx_category` (`category_id`, `lang`),
    KEY `idx_publish_time` (`publish_time`),
    KEY `idx_view_count` (`view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `vocabulary` (
  `vocab_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `zh_hk` VARCHAR(255) NOT NULL COMMENT '香港繁體',
  `zh_tw` VARCHAR(255) NOT NULL COMMENT '台灣繁體',
  `zh_cn` VARCHAR(255) NOT NULL COMMENT '簡體',
  `created_by` INT NOT NULL COMMENT '創建者管理員ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP DEFAULT null COMMENT '軟刪除時間',
  UNIQUE KEY `uk_zh_hk` (`zh_hk`),
  UNIQUE KEY `uk_zh_tw` (`zh_tw`),
  UNIQUE KEY `uk_zh_cn` (`zh_cn`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT
);

CREATE TABLE `news_edit_sessions` (
  `session_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `news_id` BIGINT NOT NULL,
  `editor_id` INT NOT NULL COMMENT '編輯者ID',
  `last_edit_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最後編輯時間',
  `is_active` BOOLEAN DEFAULT true COMMENT '是否活躍',
  `ended_by` INT COMMENT '結束會話的管理員ID',
  `ended_at` TIMESTAMP NULL COMMENT '會話結束時間',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_news_active` (`news_id`, `is_active`),
  FOREIGN KEY (`news_id`) REFERENCES `news` (`news_id`) ON DELETE CASCADE,
  FOREIGN KEY (`editor_id`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`ended_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
);

CREATE TABLE `hot_news` (
  `hot_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `category_id` INT NOT NULL COMMENT '新聞分類ID',
  `news_id` BIGINT NOT NULL COMMENT '新聞ID',
  `position` INT NOT NULL COMMENT '顯示位置',
  `is_enabled` BOOLEAN DEFAULT true COMMENT '是否啟用',
  `score` FLOAT DEFAULT 0 COMMENT '熱度分數',
  `created_by` INT NOT NULL COMMENT '操作管理員ID',
  `updated_by` INT NOT NULL COMMENT '更新管理員ID',
  `created_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP),
  `updated_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_category_position` (`category_id`, `position`),
  UNIQUE KEY `uk_category_news` (`category_id`, `news_id`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE CASCADE,
  FOREIGN KEY (`news_id`) REFERENCES `news` (`news_id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT
);

CREATE INDEX `idx_hot_news_category_score` ON `hot_news` (`category_id`, `score`, `is_enabled`);
CREATE INDEX `idx_hot_news_manual` ON `hot_news` (`category_id`, `is_enabled`, `position`);

CREATE INDEX `idx_edit_sessions_editor` ON `news_edit_sessions` (`editor_id`, `is_active`);
CREATE INDEX `idx_edit_sessions_time` ON `news_edit_sessions` (`last_edit_time`, `is_active`);

CREATE INDEX `idx_news_status_time` ON `news` (`status`, `publish_time`, `archive_time`);
CREATE INDEX `idx_news_category` ON `news` (`category_id`, `status`);
CREATE INDEX `idx_news_detail_lang` ON `new_detail` (`news_id`, `lang`, `view_count`);
CREATE INDEX `idx_news_tags` ON `news_tags_mapping` (`new_id`, `tag_id`);
CREATE INDEX `idx_news_authors` ON `news_author_mapping` (`news_id`, `admin_id`);

CREATE INDEX `news_composite_index_1` ON `news` (`status`, `publish_time`, `category_id`);
CREATE INDEX `news_detail_composite_index_1` ON `new_detail` (`news_id`, `lang`, `view_count`);
CREATE INDEX `news_tags_mapping_index_1` ON `news_tags_mapping` (`new_id`, `tag_id`);
CREATE INDEX `news_author_mapping_index_1` ON `news_author_mapping` (`news_id`, `admin_id`);

CREATE UNIQUE INDEX `new_detail_index_0` ON `new_detail` (`news_id`, `lang`);

CREATE UNIQUE INDEX `video_detail_index_1` ON `video_detail` (`video_id`, `lang`);

CREATE UNIQUE INDEX `categories_index_2` ON `categories` (`lang`, `name`);

CREATE UNIQUE INDEX `news_author_mapping_index_3` ON `news_author_mapping` (`news_id`, `admin_id`);

CREATE UNIQUE INDEX `program_detail_index_4` ON `program_detail` (`program_id`, `lang`);

CREATE UNIQUE INDEX `voting_detail_index_5` ON `voting_detail` (`voting_id`, `lang`);

CREATE UNIQUE INDEX `voting_question_detail_index_6` ON `voting_question_detail` (`question_id`, `lang`);

CREATE UNIQUE INDEX `voting_option_detail_index_7` ON `voting_option_detail` (`option_id`, `lang`);

CREATE INDEX `news_ai_summaries_index_1` ON `news_ai_summaries` (`news_id`, `lang`);

CREATE INDEX `video_transcoding_tasks_index_1` ON `video_transcoding_tasks` (`video_id`, `status`);

CREATE INDEX `mq_tasks_index_1` ON `mq_tasks` (`queue_name`, `status`);

CREATE INDEX `user_recommendations_index_1` ON `user_recommendations` (`device_id`, `created_at`);

CREATE INDEX `idx_browsing_history_device` ON `user_browsing_history` (`device_id`, `created_at`);
CREATE INDEX `idx_search_history_device` ON `user_search_history` (`device_id`, `created_at`);
CREATE INDEX `idx_share_history_device` ON `user_share_history` (`device_id`, `created_at`);
CREATE INDEX `idx_favorites_device` ON `user_favorites` (`device_id`, `created_at`);
CREATE INDEX `idx_reading_progress_device` ON `user_reading_progress` (`device_id`, `updated_at`);
CREATE INDEX `idx_interactions_device` ON `user_interactions` (`device_id`, `created_at`);
CREATE INDEX `idx_behavior_stats_device` ON `user_behavior_stats` (`device_id`, `date`);

CREATE INDEX `idx_votings_status_time` ON `votings` (`status`, `start_time`, `end_time`);
CREATE INDEX `idx_voting_questions_sort` ON `voting_questions` (`voting_id`, `sort`);
CREATE INDEX `idx_voting_options_sort` ON `voting_options` (`question_id`, `sort`);
CREATE INDEX `idx_voting_results_device` ON `voting_results` (`device_id`, `created_at`);
CREATE INDEX `idx_voting_stats_count` ON `voting_stats` (`voting_id`, `vote_count`);

CREATE INDEX `idx_push_notifications_status` ON `push_notifications` (`status`, `scheduled_time`);
CREATE INDEX `idx_push_notifications_category` ON `push_notifications` (`category_id`, `status`);
CREATE INDEX `idx_push_notifications_segment` ON `push_notifications` (`segment_id`, `status`);
CREATE INDEX `idx_push_records_notification` ON `push_records` (`notification_id`, `status`);
CREATE INDEX `idx_push_records_device` ON `push_records` (`device_id`, `created_at`);

CREATE INDEX `idx_segment_mapping_device` ON `user_segment_mapping` (`device_id`, `score`);
CREATE INDEX `idx_segment_mapping_score` ON `user_segment_mapping` (`segment_id`, `score`);

CREATE INDEX `idx_devices_push` ON `devices` (`push_enabled`, `push_channel`, `last_active_time`);
CREATE INDEX `idx_push_records_message` ON `push_records` (`message_id`);
CREATE INDEX `idx_push_records_delivery` ON `push_records` (`delivery_status`, `is_clicked`);

CREATE INDEX `idx_devices_fcm` ON `devices` (`fcm_token`, `fcm_token_updated_at`);

CREATE INDEX `idx_videos_status` ON `videos` (`status`, `publish_time`);
CREATE INDEX `idx_hot_news_category` ON `hot_news` (`category_id`, `is_enabled`, `position`);
CREATE INDEX `idx_video_thumbnails` ON `video_detail` ((CAST(thumbnails AS CHAR(1000))));
CREATE INDEX `idx_news_geo` ON `news` (`geo_restricted`, `status`);

CREATE INDEX `idx_videos_status` ON `videos` (`status`, `publish_time`);
CREATE INDEX `idx_content_videos_primary` ON `content_videos` (`content_id`, `content_type`, `is_primary`);
CREATE INDEX `idx_content_videos_sort` ON `content_videos` (`content_id`, `content_type`, `sort`);

ALTER TABLE `news` ADD FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

ALTER TABLE `news` ADD FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT;

ALTER TABLE `new_detail` ADD FOREIGN KEY (`news_id`) REFERENCES `news` (`news_id`) ON DELETE CASCADE;

ALTER TABLE `videos` ADD FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT;

ALTER TABLE `video_detail` ADD FOREIGN KEY (`video_id`) REFERENCES `videos` (`video_id`) ON DELETE CASCADE;

ALTER TABLE `categories` ADD FOREIGN KEY (`parent_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

ALTER TABLE `news_author_mapping` ADD FOREIGN KEY (`news_id`) REFERENCES `news` (`news_id`) ON DELETE CASCADE;

ALTER TABLE `news_author_mapping` ADD FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

ALTER TABLE `programs` ADD FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE RESTRICT;

ALTER TABLE `program_detail` ADD FOREIGN KEY (`program_id`) REFERENCES `programs` (`program_id`) ON DELETE CASCADE;

ALTER TABLE `programs` ADD FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`);

ALTER TABLE `news_tags_mapping` ADD FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tag_id`);

ALTER TABLE `news_tags_mapping` ADD FOREIGN KEY (`new_id`) REFERENCES `news` (`news_id`);


CREATE TRIGGER after_news_update
AFTER UPDATE ON news
FOR EACH ROW
BEGIN
    UPDATE news_cache 
    SET updated_at = CURRENT_TIMESTAMP
    WHERE news_id = NEW.news_id;
END//

CREATE TRIGGER after_new_detail_update
AFTER UPDATE ON new_detail
FOR EACH ROW
BEGIN
    UPDATE news_cache 
    SET 
        title = NEW.title,
        content = NEW.content,
        thumbnail_url = NEW.thumbnail_url,
        view_count = NEW.view_count,
        share_count = NEW.share_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE news_id = NEW.news_id AND lang = NEW.lang;
END//

CREATE TRIGGER after_categories_update
AFTER UPDATE ON categories
FOR EACH ROW
BEGIN
    UPDATE news_cache nc
    JOIN news n ON nc.news_id = n.news_id
    SET 
        nc.category_name = NEW.name,
        nc.updated_at = CURRENT_TIMESTAMP
    WHERE n.category_id = NEW.category_id;
END//
DELIMITER ;

CREATE TRIGGER before_content_video_insert
BEFORE INSERT ON content_videos
FOR EACH ROW
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE content_videos 
        SET is_primary = false 
        WHERE content_id = NEW.content_id 
        AND content_type = NEW.content_type
        AND is_primary = true;
    END IF;
END//

CREATE TRIGGER before_content_video_update
BEFORE UPDATE ON content_videos
FOR EACH ROW
BEGIN
    IF NEW.is_primary = true AND OLD.is_primary = false THEN
        UPDATE content_videos 
        SET is_primary = false 
        WHERE content_id = NEW.content_id 
        AND content_type = NEW.content_type
        AND is_primary = true
        AND id != NEW.id;
    END IF;
END//
DELIMITER ;

-- 创建触发器，自动更新热门新闻分数
DELIMITER //
CREATE TRIGGER after_news_metrics_update
AFTER UPDATE ON new_detail
FOR EACH ROW
BEGIN
    IF NEW.view_count != OLD.view_count OR NEW.share_count != OLD.share_count THEN
        UPDATE hot_news 
        SET 
            view_count = NEW.view_count,
            score = (
                NEW.view_count * 1.0 + 
                NEW.share_count * 2.0
            )
        WHERE news_id = NEW.news_id;
    END IF;
END//
DELIMITER ;

-- 创建触发器，自动处理归档状态
DELIMITER //
CREATE TRIGGER before_news_archive
BEFORE UPDATE ON news
FOR EACH ROW
BEGIN
    IF NEW.archive_time IS NOT NULL AND NEW.archive_time <= NOW() THEN
        SET NEW.status = 3; -- 设置为已归档状态
    END IF;
END//

CREATE TRIGGER check_news_archive
BEFORE INSERT ON news
FOR EACH ROW
BEGIN
    IF NEW.archive_time IS NOT NULL AND NEW.archive_time <= NOW() THEN
        SET NEW.status = 3; -- 设置为已归档状态
    END IF;
END//
DELIMITER ;

-- 创建视图：相关文章
CREATE VIEW `v_news_related` AS
SELECT 
    n1.news_id as original_news_id,
    n2.news_id as related_news_id,
    nd2.title as related_title,
    nd2.thumbnail_url as related_thumbnail,
    n2.publish_time as related_publish_time,
    COUNT(DISTINCT ntm2.tag_id) as common_tags_count
FROM news n1
JOIN news_tags_mapping ntm1 ON n1.news_id = ntm1.new_id
JOIN news_tags_mapping ntm2 ON ntm1.tag_id = ntm2.tag_id
JOIN news n2 ON ntm2.new_id = n2.news_id
JOIN new_detail nd2 ON n2.news_id = nd2.news_id
WHERE n1.show_related = true
AND n1.status = 1
AND n2.status = 1
AND n1.news_id != n2.news_id
GROUP BY n1.news_id, n2.news_id
HAVING common_tags_count > 0
ORDER BY common_tags_count DESC, n2.publish_time DESC;

-- 创建触发器，检查地理位置限制
DELIMITER //
CREATE TRIGGER before_news_geo_update
BEFORE UPDATE ON news
FOR EACH ROW
BEGIN
    -- 如果启用了地理位置限制，确保新闻状态为已发布
    IF NEW.geo_restricted = true AND NEW.status != 1 THEN
        SET NEW.status = 1;
    END IF;
END//
DELIMITER ;

-- 创建触发器，自动更新最后编辑时间
DELIMITER //
CREATE TRIGGER after_news_edit_update
AFTER UPDATE ON news
FOR EACH ROW
BEGIN
    -- 更新编辑会话的最后编辑时间
    UPDATE news_edit_sessions
    SET last_edit_time = CURRENT_TIMESTAMP
    WHERE news_id = NEW.news_id
    AND is_active = true;
END//

-- 创建事件，自动结束超时的编辑会话
CREATE EVENT IF NOT EXISTS end_inactive_sessions
ON SCHEDULE EVERY 1 MINUTE
DO
BEGIN
    -- 结束超过30分钟未编辑的会话
    UPDATE news_edit_sessions
    SET 
        is_active = false,
        ended_at = CURRENT_TIMESTAMP
    WHERE is_active = true
    AND last_edit_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE);
END//

-- 创建存储过程，结束编辑会话
CREATE PROCEDURE end_edit_session(
    IN p_news_id BIGINT,
    IN p_ended_by INT
)
BEGIN
    UPDATE news_edit_sessions
    SET 
        is_active = false,
        ended_by = p_ended_by,
        ended_at = CURRENT_TIMESTAMP
    WHERE news_id = p_news_id
    AND is_active = true;
END//
DELIMITER ;

-- 创建索引
CREATE INDEX `idx_tags_names` ON `tags` ((CAST(names AS CHAR(1000))));
CREATE INDEX `idx_tags_created_by` ON `tags` (`created_by`);