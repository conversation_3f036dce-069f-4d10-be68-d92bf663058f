<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1400" height="1700" xmlns="http://www.w3.org/2000/svg" version="1.1">
  <defs>
    <style type="text/css">
      .layer-title { font-size: 22px; font-weight: bold; text-anchor: middle; }
      .section-title { font-size: 20px; font-weight: bold; text-anchor: middle; }
      .component-text { font-size: 13px; text-anchor: middle; dominant-baseline: middle; }
      .component-box { stroke: #333; stroke-width: 1; }
      .category-text { font-size: 15px; font-weight: bold; text-anchor: middle; dominant-baseline: middle; }
      .category-box { stroke: #333; stroke-width: 2; }
      .arrow { stroke: #666; stroke-width: 1.5; marker-end: url(#arrowhead); }
      .tech-box { stroke: #333; stroke-width: 2; fill: #F0F8FF; }
      .tech-title { font-size: 16px; font-weight: bold; text-anchor: middle; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1400" height="1700" fill="white"/>
  
  <!-- 标题 -->
  <text x="700" y="40" font-size="26" font-weight="bold" text-anchor="middle">BWMS 后台管理系统架构图</text>
  <text x="700" y="70" font-size="18" font-style="italic" text-anchor="middle">基于Laravel + Vue3 的模块化架构</text>

  <!-- 技术栈层 (Technology Stack) -->
  <rect x="80" y="100" width="1240" height="90" fill="#F0F8FF" stroke="#4169E1" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="125" class="section-title">核心技术栈</text>
  <g transform="translate(100, 145)">
    <rect x="0" y="0" width="230" height="35" class="tech-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">PHP 8.2+ / Laravel 11</text>
    
    <rect x="250" y="0" width="230" height="35" class="tech-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">Vue 3 + TypeScript</text>
    
    <rect x="500" y="0" width="230" height="35" class="tech-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">Element Plus + Vite</text>
    
    <rect x="750" y="0" width="230" height="35" class="tech-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">MySQL 8.0+ / Redis</text>
    
    <rect x="1000" y="0" width="200" height="35" class="tech-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">Docker / Kubernetes</text>
  </g>

  <!-- 用户访问层 (User Access Layer) -->
  <rect x="80" y="210" width="1240" height="90" fill="#E6F7FF" stroke="#0099CC" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="235" class="section-title">用户访问层</text>
  <g transform="translate(140, 255)">
    <rect x="0" y="0" width="250" height="35" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="125" y="18" class="component-text" fill="white">系统管理员</text>
    
    <rect x="280" y="0" width="250" height="35" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="405" y="18" class="component-text" fill="white">内容管理员</text>
    
    <rect x="560" y="0" width="250" height="35" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="685" y="18" class="component-text" fill="white">业务用户</text>
    
    <rect x="840" y="0" width="250" height="35" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="965" y="18" class="component-text" fill="white">API调用方</text>
  </g>

  <!-- 前端展示层 (Frontend Layer) -->
  <rect x="80" y="320" width="1240" height="130" fill="#E6FFFA" stroke="#00CCCC" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="345" class="section-title">前端展示层</text>
  
  <!-- 前端应用类型 -->
  <g transform="translate(100, 365)">
    <rect x="0" y="0" width="280" height="35" fill="#20B2AA" class="component-box" rx="5" ry="5"/>
    <text x="140" y="18" class="component-text" fill="white">管理后台 (Vue3 + TS)</text>
    
    <rect x="300" y="0" width="280" height="35" fill="#20B2AA" class="component-box" rx="5" ry="5"/>
    <text x="440" y="18" class="component-text" fill="white">前台网站 (Blade模板)</text>
    
    <rect x="600" y="0" width="280" height="35" fill="#20B2AA" class="component-box" rx="5" ry="5"/>
    <text x="740" y="18" class="component-text" fill="white">移动端适配 (响应式)</text>
    
    <rect x="900" y="0" width="300" height="35" fill="#20B2AA" class="component-box" rx="5" ry="5"/>
    <text x="1050" y="18" class="component-text" fill="white">RESTful API / OpenAPI</text>
  </g>
  
  <!-- 前端技术栈 -->
  <g transform="translate(100, 410)">
    <rect x="0" y="0" width="240" height="30" fill="#48D1CC" class="component-box" rx="5" ry="5"/>
    <text x="120" y="15" class="component-text">组件库 (Element Plus)</text>
    
    <rect x="260" y="0" width="240" height="30" fill="#48D1CC" class="component-box" rx="5" ry="5"/>
    <text x="380" y="15" class="component-text">状态管理 (Pinia)</text>
    
    <rect x="520" y="0" width="240" height="30" fill="#48D1CC" class="component-box" rx="5" ry="5"/>
    <text x="640" y="15" class="component-text">路由管理 (Vue Router)</text>
    
    <rect x="780" y="0" width="200" height="30" fill="#48D1CC" class="component-box" rx="5" ry="5"/>
    <text x="880" y="15" class="component-text">国际化 (Vue I18n)</text>
    
    <rect x="1000" y="0" width="200" height="30" fill="#48D1CC" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="15" class="component-text">构建工具 (Vite)</text>
  </g>

  <!-- 业务模块层 (Business Modules) -->
  <rect x="80" y="470" width="1240" height="400" fill="#FFF7E6" stroke="#FFAA00" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="495" class="section-title">业务模块层 (50+ 模块)</text>
  
  <!-- 内容管理模块 -->
  <g transform="translate(100, 520)">
    <rect x="0" y="0" width="60" height="330" fill="#FFAA00" class="category-box" rx="5" ry="5"/>
    <text x="30" y="165" class="category-text" fill="white" transform="rotate(-90, 30, 165)">内容管理</text>
    
    <rect x="75" y="0" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="18" class="component-text">Cms (内容管理)</text>
    
    <rect x="75" y="45" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="63" class="component-text">Editor (富文本编辑)</text>
    
    <rect x="75" y="90" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="108" class="component-text">Media (媒体管理)</text>
    
    <rect x="75" y="135" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="153" class="component-text">FileManager (文件)</text>
    
    <rect x="75" y="180" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="198" class="component-text">Banner (横幅管理)</text>
    
    <rect x="75" y="225" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="243" class="component-text">Nav (导航管理)</text>
    
    <rect x="75" y="270" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="288" class="component-text">SEO (搜索优化)</text>
    
    <rect x="75" y="315" width="210" height="15" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="323" class="component-text" font-size="11">更多模块...</text>
  </g>
  
  <!-- 用户互动模块 -->
  <g transform="translate(405, 520)">
    <rect x="0" y="0" width="60" height="330" fill="#FFAA00" class="category-box" rx="5" ry="5"/>
    <text x="30" y="165" class="category-text" fill="white" transform="rotate(-90, 30, 165)">用户互动</text>
    
    <rect x="75" y="0" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="18" class="component-text">Members (会员管理)</text>
    
    <rect x="75" y="45" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="63" class="component-text">Form (表单系统)</text>
    
    <rect x="75" y="90" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="108" class="component-text">Survey (问卷调查)</text>
    
    <rect x="75" y="135" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="153" class="component-text">Activity (活动管理)</text>
    
    <rect x="75" y="180" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="198" class="component-text">Appointment (预约)</text>
    
    <rect x="75" y="225" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="243" class="component-text">Message (消息通知)</text>
    
    <rect x="75" y="270" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="288" class="component-text">Search (搜索功能)</text>
    
    <rect x="75" y="315" width="210" height="15" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="323" class="component-text" font-size="11">更多模块...</text>
  </g>
  
  <!-- 系统管理模块 -->
  <g transform="translate(710, 520)">
    <rect x="0" y="0" width="60" height="330" fill="#FFAA00" class="category-box" rx="5" ry="5"/>
    <text x="30" y="165" class="category-text" fill="white" transform="rotate(-90, 30, 165)">系统管理</text>
    
    <rect x="75" y="0" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="18" class="component-text">Iam (身份认证)</text>
    
    <rect x="75" y="45" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="63" class="component-text">Dashboard (仪表板)</text>
    
    <rect x="75" y="90" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="108" class="component-text">Config (系统配置)</text>
    
    <rect x="75" y="135" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="153" class="component-text">Security (安全模块)</text>
    
    <rect x="75" y="180" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="198" class="component-text">Log (日志管理)</text>
    
    <rect x="75" y="225" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="243" class="component-text">Setting (系统设置)</text>
    
    <rect x="75" y="270" width="210" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="288" class="component-text">Help (帮助中心)</text>
    
    <rect x="75" y="315" width="210" height="15" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="180" y="323" class="component-text" font-size="11">更多模块...</text>
  </g>

  <!-- 增强功能模块 -->
  <g transform="translate(1015, 520)">
    <rect x="0" y="0" width="60" height="330" fill="#FFAA00" class="category-box" rx="5" ry="5"/>
    <text x="30" y="165" class="category-text" fill="white" transform="rotate(-90, 30, 165)">增强功能</text>
    
    <rect x="75" y="0" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="18" class="component-text">Ai (AI助手)</text>
    
    <rect x="75" y="45" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="63" class="component-text">Backup (备份)</text>
    
    <rect x="75" y="90" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="108" class="component-text">Chart (图表)</text>
    
    <rect x="75" y="135" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="153" class="component-text">Multilingual</text>
    
    <rect x="75" y="180" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="198" class="component-text">CronJob</text>
    
    <rect x="75" y="225" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="243" class="component-text">DataEncrypt</text>
    
    <rect x="75" y="270" width="190" height="35" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="288" class="component-text">WorkFlow</text>
    
    <rect x="75" y="315" width="190" height="15" fill="#FFD580" class="component-box" rx="5" ry="5"/>
    <text x="170" y="323" class="component-text" font-size="11">更多模块...</text>
  </g>

  <!-- 应用服务层 (Application Services) -->
  <rect x="80" y="890" width="1240" height="90" fill="#E6FFE6" stroke="#00AA00" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="915" class="section-title">应用服务层</text>
  <g transform="translate(100, 935)">
    <rect x="0" y="0" width="230" height="35" fill="#99DD99" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">业务服务 (Services)</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#99DD99" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">领域服务 (Domain)</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#99DD99" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">数据仓储 (Repository)</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#99DD99" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">任务队列 (Jobs/Queue)</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#99DD99" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">事件系统 (Events)</text>
  </g>

  <!-- 框架支撑层 (Framework Support) -->
  <rect x="80" y="1000" width="1240" height="90" fill="#FFE6E6" stroke="#FF6666" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1025" class="section-title">框架支撑层</text>
  <g transform="translate(100, 1045)">
    <rect x="0" y="0" width="230" height="35" fill="#FFAAAA" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">Laravel 核心框架</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#FFAAAA" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">Eloquent ORM</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#FFAAAA" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">路由中间件</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#FFAAAA" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">认证授权 (Sanctum)</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#FFAAAA" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">验证器 (Validation)</text>
  </g>

  <!-- 基础设施层 (Infrastructure) -->
  <rect x="80" y="1110" width="1240" height="90" fill="#E6E6FF" stroke="#6666FF" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1135" class="section-title">基础设施层</text>
  <g transform="translate(100, 1155)">
    <rect x="0" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">缓存系统 (Redis)</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">消息队列 (Queue)</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">文件存储 (Storage)</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">搜索引擎 (Elastic)</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">第三方集成</text>
  </g>

  <!-- 数据层 (Data Layer) -->
  <rect x="80" y="1220" width="1240" height="90" fill="#E6F0FF" stroke="#6699FF" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1245" class="section-title">数据层</text>
  <g transform="translate(100, 1265)">
    <rect x="0" y="0" width="230" height="35" fill="#99BBFF" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">MySQL 数据库</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#99BBFF" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">Redis 缓存</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#99BBFF" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">文件系统存储</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#99BBFF" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">云存储 (AWS/阿里云)</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#99BBFF" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">数据备份</text>
  </g>

  <!-- 运维部署层 (DevOps) -->
  <rect x="80" y="1330" width="1240" height="90" fill="#F0F0F0" stroke="#999999" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1355" class="section-title">运维部署层</text>
  <g transform="translate(100, 1375)">
    <rect x="0" y="0" width="230" height="35" fill="#DDDDDD" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">Docker 容器化</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#DDDDDD" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">Kubernetes 编排</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#DDDDDD" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">CI/CD 流水线</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#DDDDDD" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">监控告警</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#DDDDDD" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">负载均衡</text>
  </g>

  <!-- 安全合规层 (Security & Compliance) -->
  <rect x="80" y="1440" width="1240" height="90" fill="#E6E6FF" stroke="#6666FF" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1465" class="section-title">安全合规层</text>
  <g transform="translate(100, 1485)">
    <rect x="0" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">数据加密</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">访问控制</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">安全审计</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">数据备份恢复</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#AAAAFF" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">合规监控</text>
  </g>

  <!-- 开发规范 (Development Standards) -->
  <rect x="80" y="1550" width="1240" height="90" fill="#FFF8DC" stroke="#DAA520" stroke-width="2" rx="10" ry="10"/>
  <text x="700" y="1575" class="section-title">开发规范</text>
  <g transform="translate(100, 1595)">
    <rect x="0" y="0" width="230" height="35" fill="#F0E68C" class="component-box" rx="5" ry="5"/>
    <text x="115" y="18" class="component-text">PSR-12 编码规范</text>
    
    <rect x="250" y="0" width="230" height="35" fill="#F0E68C" class="component-box" rx="5" ry="5"/>
    <text x="365" y="18" class="component-text">Git 分支管理</text>
    
    <rect x="500" y="0" width="230" height="35" fill="#F0E68C" class="component-box" rx="5" ry="5"/>
    <text x="615" y="18" class="component-text">单元测试 (PHPUnit)</text>
    
    <rect x="750" y="0" width="230" height="35" fill="#F0E68C" class="component-box" rx="5" ry="5"/>
    <text x="865" y="18" class="component-text">API 文档规范</text>
    
    <rect x="1000" y="0" width="200" height="35" fill="#F0E68C" class="component-box" rx="5" ry="5"/>
    <text x="1100" y="18" class="component-text">代码审查</text>
  </g>

  <!-- 连接线 -->
  <line x1="700" y1="190" x2="700" y2="210" class="arrow"/>
  <line x1="700" y1="300" x2="700" y2="320" class="arrow"/>
  <line x1="700" y1="450" x2="700" y2="470" class="arrow"/>
  <line x1="700" y1="870" x2="700" y2="890" class="arrow"/>
  <line x1="700" y1="980" x2="700" y2="1000" class="arrow"/>
  <line x1="700" y1="1090" x2="700" y2="1110" class="arrow"/>
  <line x1="700" y1="1200" x2="700" y2="1220" class="arrow"/>
  <line x1="700" y1="1310" x2="700" y2="1330" class="arrow"/>
  <line x1="700" y1="1420" x2="700" y2="1440" class="arrow"/>
  <line x1="700" y1="1530" x2="700" y2="1550" class="arrow"/>
</svg> 