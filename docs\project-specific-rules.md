# 项目特定规则

## 目录
- [模块开发规则](#模块开发规则)
- [路由规则](#路由规则)
- [权限规则](#权限规则)
- [多语言规则](#多语言规则)
- [特殊处理规则](#特殊处理规则)
- [最佳实践](#最佳实践)

## 模块开发规则

### 1. 模块结构规范

```bash
Modules/
└── Course/                    # 模块名（大写开头）
    ├── Api/                   # API 接口
    │   ├── Controller/       # API 控制器
    │   └── route.php        # API 路由
    ├── Web/                  # Web 接口
    │   ├── Controllers/     # Web 控制器
    │   └── route.php       # Web 路由
    ├── Admin/                # 管理后台
    │   ├── Controllers/     # 管理控制器
    │   └── route.php       # 管理路由
    ├── OpenApi/              # 开放接口
    │   ├── Controllers/     # 开放控制器
    │   └── route.php       # 开放路由
    ├── config.json           # 模块配置
    └── module.json          # 模块信息
```

### 2. 模块配置规范

```json
{
    "name": "Course",
    "title": "课程模块",
    "version": "1.0.0",
    "description": "提供课程管理功能",
    "keywords": ["course", "education"],
    "type": "module",
    "require": {
        "Core": ">=1.0",
        "User": ">=1.0"
    },
    "providers": [
        "Modules\\Course\\Providers\\CourseServiceProvider"
    ]
}
```

## 路由规则

[与 template-generation.md 相同的路由规范部分]

## 权限规则

[与 template-generation.md 相同的权限规则部分]

## 多语言规则

[与 template-generation.md 相同的多语言规则部分]

## 特殊处理规则

### 1. 缓存处理

```php
// 缓存键定义
class ModuleCache
{
    public const KEY_PREFIX = 'module:';
    public const TTL_DEFAULT = 3600; // 1小时

    public static function getKey(string $key): string
    {
        return self::KEY_PREFIX . $key;
    }
}

// 缓存使用
class YourService
{
    public function getData(string $key): mixed
    {
        return Cache::remember(
            ModuleCache::getKey($key),
            ModuleCache::TTL_DEFAULT,
            fn() => $this->repository->getData($key)
        );
    }
}
```

### 2. 队列处理

```php
// 队列任务定义
class ProcessModuleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private readonly int $id,
        private readonly array $data
    ) {}

    public function handle(): void
    {
        // 处理耗时操作
    }
}

// 队列使用
class YourService
{
    public function process(int $id, array $data): void
    {
        ProcessModuleJob::dispatch($id, $data)
            ->onQueue('default');
    }
}
```

## 最佳实践

[与 template-generation.md 相同的最佳实践部分]

### 缓存使用规范
- 统一使用 ModuleCache 类管理缓存键
- 合理设置缓存过期时间
- 在更新数据时及时清除相关缓存

### 队列任务规范
- 将耗时操作放入队列处理
- 合理设置队列优先级
- 实现失败重试机制
- 记录任务执行日志
``` 
