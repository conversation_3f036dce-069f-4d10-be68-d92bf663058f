<template>
  <div class="dashboard-container" v-loading="loading" :element-loading-text="t('dashboard.loading')">
    <div class="main-content">
      
      <!-- 统计卡片区域 -->
      <div class="bwms-module" style="margin-top: 24px;" v-if="showDashboardModule && pluginStatus.Dashboard">
        <div class="module-tit" style="margin-bottom: 12px;">
          <h2 class="module-tit-title">{{ t('dashboard.updatesTitle.title') }}</h2>
          <el-button type="text" link class="close-btn" @click="handleClose">
            <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
          </el-button>
        </div>
        <div class="module-con">
          <Dashboard v-if="pluginStatus.Dashboard" />
        </div>
      </div>
      
      <!-- 数据决策 -->
      <div class="bwms-module" style="margin-top: 28px;" v-if="showDataMakingModule">
        <div class="module-tit" style="margin-bottom: 17px;">
          <h2 class="module-tit-title">數據決策</h2>
          <el-button type="text" link class="close-btn" @click="handleDataMakingClose">
            <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
          </el-button>
        </div>
        <div class="module-con">
          <DataMaking />
        </div>
      </div>
    </div>

    <div class="side-panel">
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch, onUnmounted } from 'vue'
import { Plus, ArrowRight, Management, Close } from '@element-plus/icons-vue'
import Dashboard from './components/Dashboard.vue'
import SafeTitle from './components/safeTitle.vue'
import WebsiteStatus from './components/WebsiteStatus.vue'
import UserInfo from './components/UserInfo.vue'
import News from './components/News.vue'
import ToolsSection from './components/ToolsSection.vue'
import AiCapabilities from './components/AiCapabilities.vue'
import WebsiteAnalytics from './components/WebsiteAnalytics.vue'
import DataMaking from './components/DataMaking.vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'
import useEventBus from '/admin/support/eventBus'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const { $on, $off } = useEventBus()
const router = useRouter()

const showPlugins = ref(false)
const loading = ref(false)
const dashboardTitle = ref('')

// 添加插件状态管理
const pluginStatus = ref({
  AiCapabilities: false,
  WebsiteAnalytics: false,
  Ga4Iframe: false,
  ToolsSection: false,
  News: false,
  Dashboard: false,
  WebsiteStatus: false,
  UserInfo: false
})

// 模块显示控制
const showGa4Module = ref(true)
const showAiCapabilitiesModule = ref(true)
const showDashboardModule = ref(true)
const showSafeTitleModule = ref(true)
const showDataMakingModule = ref(true)


const getDashboard = async () => {
  loading.value = true
  try {
    // 首先重置所有插件状态为 false
    Object.keys(pluginStatus.value).forEach(key => {
      pluginStatus.value[key as keyof typeof pluginStatus.value] = false
    })

    const res = await http.get('/dashboard')
    if (res.data.code === 200 && res.data.data) {
      dashboardTitle.value = res.data.data.title
      // 只更新存在的插件状态为 true
      Object.keys(res.data.data).forEach(key => {
        if (key in pluginStatus.value && res.data.data[key].is_exist) {
          pluginStatus.value[key as keyof typeof pluginStatus.value] = true
        }
      })
    }
  } catch (error) {
    console.error('获取 Dashboard 数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshDashboard = () => {
  getDashboard()
}

// 处理GA4模块关闭
const handleGa4Close = () => {
  showGa4Module.value = false
}

// 处理AI能力模块关闭
const handleAiCapabilitiesClose = () => {
  showAiCapabilitiesModule.value = false
}

// 处理Dashboard模块关闭
const handleClose = () => {
  showDashboardModule.value = false
}

// 处理SafeTitle模块关闭
const handleSafeTitleClose = () => {
  showSafeTitleModule.value = false
}

// 处理DataMaking模块关闭
const handleDataMakingClose = () => {
  showDataMakingModule.value = false
}


// 在组件挂载时获取插件状态和添加事件监听
onMounted(() => {
  getDashboard()
  
  // 添加事件监听
  $on('refresh-dashboard', refreshDashboard)
})

// 在组件卸载时清理事件监听
onUnmounted(() => {
  // 清理事件监听
  $off('refresh-dashboard', refreshDashboard)
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  display: flex;
  width: 100%;
  position: relative;

  :deep(.el-loading-mask) {
    height: 86vh;
    background-color: rgba(255, 255, 255, 0.9);
    .el-loading-spinner {
      .el-loading-text {
        color: #37a0ea;
        font-size: 16px;
      }
      .circular {
        .path {
          stroke: #37a0ea;
        }
      }
    }
  }

  @media screen and (max-width: 1400px) {
    flex-wrap: wrap;
  }

  .main-content {
    padding: 0 20px 38px 28px;
    width: calc(100% - 410px);
    flex: 1;

    @media screen and (max-width: 1600px) {
      width: calc(100% - 268px);
    }

    // @media screen and (max-width: 1400px) {
    //   width: 100%;
    // }

    .bwms-module {
      margin-top: 35px;
      padding: 0;
      overflow: visible;

      .module-con {
        overflow: visible;
      }
    }
  }

  .side-panel {
    padding: 35px 33px;
    // padding: 29px 19px 35px 21px;
    width: 410px;
    flex-shrink: 0;
    background-color: #e4eaef;

    @media screen and (max-width: 1600px) {
      width: 268px;
      padding: 29px 19px 35px 21px;
    }

    // @media screen and (max-width: 1400px) {
    //   width: 100%;
    //   display: flex;
    //   flex-wrap: wrap;
    // }

    @media screen and (max-width: 1330px) {
      padding: 10px 20px 38px 28px;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
}


.news-ga4-container {
  display: flex;

  @media screen and (max-width: 1200px) {
    flex-wrap: wrap;
  }

  .ga4-section {
    flex-grow: 1;
    height: 460px;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    @media screen and (max-width: 1200px) {
      margin-bottom: 20px;
      padding-right: 0;
      width: 100%;
    }
  }

  .news-section {
    width: 340px;

    @media screen and (max-width: 1200px) {
      width: 100%;
    }
  }
}
.module-tit-title {
    color: #232323 !important;
    font-weight: bold !important;
    font-size: 18px !important;
    line-height: 24px !important;
  }

:deep(.side-module) {
  margin-bottom: 20px;
  border-radius: 20px;
  border: 1px solid #86c0e9;
  padding: 24px 20px 16px;
  background-color: #fff;

  @media screen and (max-width: 1600px) {
    padding: 16px;
  }

  // @media screen and (max-width: 1400px) {
  //   margin-left: 20px;
  //   margin-bottom: 0;
  //   width: calc(33.33% - 13px);

  //   display: flex;
  //   flex-direction: column;

  //   &:first-child {
  //     margin-left: 0;
  //   }
  // }

  @media screen and (max-width: 1330px) {
    margin-left: 20px;
    margin-bottom: 0;
    width: calc(32% - 6px);
    display: flex;
    flex-direction: column;

    &:first-child {
      margin-left: 0;
    }
  }

  .module-tit {
    margin-bottom: 20px;
    color: #232323;
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    flex-shrink: 0;
  }

  .module-con {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .el-button {
    margin-top: 28px;
    border-radius: 10px;
    border: 2px solid #86c0e9;
    padding: 10px 18px;
    background-color: #f4f9fd;
    width: 100%;
    text-align: left;
    color: #37a0ea;
    flex-shrink: 0;

    & > span {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .content-left {
      flex-grow: 1;
    }

    .el-icon {
      flex-shrink: 0;
      font-size: 14px;
    }
  }
}
</style>
