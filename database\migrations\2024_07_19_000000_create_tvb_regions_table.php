<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// 创建tvb_regions表的迁移文件
// 本文件由AI自动生成，所有字段均带中文注释
return new class extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        // 创建区域管理表
        Schema::create('tvb_regions', function (Blueprint $table) {
            $table->id()->comment('区域ID');
            $table->string('name', 100)->comment('区域名称');
            $table->text('description')->nullable()->comment('区域描述');
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用 1-启用');
            $table->integer('sort')->nullable()->comment('排序');
            $table->unsignedBigInteger('created_by')->comment('创建人');
            $table->unsignedBigInteger('updated_by')->comment('更新人');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index('status', 'idx_status');
            $table->index('name', 'idx_name');
            $table->index('sort', 'idx_sort');
            $table->index('created_at', 'idx_created_at');
        });

        // 插入示例数据
        DB::table('tvb_regions')->insert([
            [
                'name' => '突發',
                'description' => '突發新聞專區',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '港澳',
                'description' => '港澳地區新聞',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '國際',
                'description' => '國際新聞報導',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '財經',
                'description' => '財經資訊與分析',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '體育',
                'description' => '體育賽事與新聞',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '兩岸',
                'description' => '兩岸關係與新聞',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'TVB News',
                'description' => 'TVB 新聞專區',
                'status' => 1,
                'created_by' => 1,
                'updated_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tvb_regions');
    }
}; 