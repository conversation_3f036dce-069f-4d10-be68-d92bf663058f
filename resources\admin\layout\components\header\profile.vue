<template>
  <div class="profile-box">
    <div class="userinfo-box">
      <div class="avatar-pic">
        <img :src="userStore.getAvatar" class="w-12 h-12" />
      </div>
      <div class="nickname-email">
        <div class="nickname">{{ userStore.getNickname }}</div>
        <div class="user-email">{{ userStore.email }}</div>
      </div>
    </div>
    <div class="ul">
      <!-- 账号 -->
      <div class="li tit">{{ $t('header.settings.tit1') }}</div>
      <!-- 个人首选项 -->
      <div class="li link" @click="goPage(`/userSettings`)">{{ $t('header.settings.li1') }}</div>
      <!-- 重置密码 -->
      <!-- <div class="li link" @click="goPage(``)">{{ $t('header.settings.li2') }}</div> -->
    </div>
    <div class="ul">
      <!-- 系统语言 -->
      <div class="li tit">{{ $t('header.settings.tit2') }}</div>
      <!-- 语言选择 -->
      <div class="li link" @click="dialogVisible = true">{{ $t('header.settings.li3') }}</div>
      <!-- 站点选择 -->
      <!-- <div class="li tit" style="margin-top: 20px;">{{ $t('header.settings.tit3') }}</div>
      <div class="li link" @click="siteDialogVisible = true">{{ $t('header.settings.li4') }}</div> -->
    </div>

    <!-- 语言选择弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="t('header.settings.langswitch')" 
      width="570"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="true"
      align-center
      class="plugins-dialog"
      style="height: 284px;"
    >
      <el-select placeholder="Language Select" v-model="lang" size="large">
        <el-option v-for="item in langList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="dialogVisible = false">{{ $t('system.cancel') }}</el-button>
          <el-button size="large" type="primary" @click="confirmHandle">{{ $t('system.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 站点选择弹窗 -->
    <el-dialog 
      v-model="siteDialogVisible" 
      :title="t('header.settings.siteswitch')" 
      width="570"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="true"
      align-center
      class="plugins-dialog"
      style="height: 284px;"
    >
      <el-select v-model="currentSite" size="large" :placeholder="$t('header.settings.selectsite')">
        <el-option 
          v-for="site in siteList" 
          :key="site.id" 
          :label="site.name" 
          :value="site.id"
        >
          <div class="site-option">
            <span class="site-name">{{ site.name }}</span>
            <span class="site-domain">({{ site.domain }})</span>
          </div>
        </el-option>
        <template #empty>
          <div style="text-align: center; padding: 8px 0;">
            {{ t('Cms.list.no_data') }}
          </div>
        </template>
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="siteDialogVisible = false">{{ $t('system.cancel') }}</el-button>
          <el-button size="large" type="primary" @click="confirmSiteHandle">{{ $t('system.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '/admin/stores/modules/user'
import { useAppStore } from '/admin/stores/modules/app'
import Message from '/admin/support/message'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'

import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()

import useEventBus from '/admin/support/eventBus'
const { $emit } = useEventBus()

const userStore = useUserStore()
// 在 script setup 中添加 emits 定义
const emit = defineEmits(['refresh-dashboard'])

// 在 script 部分添加类型定义
interface Site {
  id: number
  code: string
  name: string
  domain: string
  aliases: string[]
  group_id: number
  default_locale: string
  enabled_locales: string[]
  timezone: string
  settings: {
    theme: string
    logo: string
  }
  is_default: boolean
  status: number
  created_at: string
  updated_at: string
  deleted_at: string | null
  created_by: number
  updated_by: number
  group: any
}

// 修改 ref 的类型定义
const siteList = ref<Site[]>([])
const currentSite = ref<number | null>(null)

// 修改 appStore 的类型定义
interface AppStore {
  getLocale: string
  changeLocale: (locale: string) => void
  setCurrentSite: (site: Site) => void
  getCurrentSite: () => Site | null
}

const appStore = useAppStore() as unknown as AppStore

const router = useRouter()

const dialogVisible = ref(false)
const siteDialogVisible = ref(false)
const lang = ref(appStore.getLocale)

const langList = ref([
  {
    label: '简体中文',
    value: 'zh_CN',
  },
  {
    label: '繁體中文',
    value: 'zh_HK',
  },
  {
    label: 'English',
    value: 'en',
  },
])

// 获取站点列表
const getSiteList = async () => {
  try {
    const params: any = {
      page: 1,
      limit: 100
    }
    const response = await http.get('/config/sites', {params})
    if (response.data.code === 200) {
      siteList.value = response.data.data
      
    }
  } catch (error) {
  }
}

// 确认站点切换
const confirmSiteHandle = async () => {
  const selectedSite = siteList.value.find((site: Site) => site.id === currentSite.value)
  if (selectedSite) {
    try {
      // 调用设置默认站点接口
      const response = await http.put(`/config/sites/${selectedSite.id}/default`)
      if (response.data.code === 200) {
        // 设置当前站点
        appStore.setCurrentSite(selectedSite)
        
        siteDialogVisible.value = false
        Message.success(t('header.settings.site_switch_success'))
        
        // 触发刷新事件
        $emit('update:upMenu')
        emit('refresh-dashboard')
        
        // 刷新页面以应用新站点设置
        // window.location.reload()
      }
    } catch (error) {
      Message.error(t('header.settings.site_switch_failed'))
    }
  }
}

const confirmHandle = () => {
  locale.value = lang.value
  appStore.changeLocale(lang.value)
  dialogVisible.value = false
  $emit('update:upMenu')
  
  // 触发刷新 dashboard 数据的事件
  emit('refresh-dashboard')
}

function goPage(url: string) {
  router.push(url)
}

// 初始化时获取站点列表
onMounted(() => {
  // getSiteList()
})
</script>

<style lang="scss">
.profile-box {
  cursor: auto;

  .userinfo-box {
    display: flex;
    align-items: center;

    .avatar-pic {
      border-radius: 50%;
      border: 1px solid #f5f5f5;
      overflow: hidden;
      width: 52px;
      height: 52px;

      img {
        width: 100%;
      }
    }

    .nickname-email {
      padding-left: 15px;
      color: #202020;

      .nickname {
        font-size: 16px;
        font-weight: bold;
      }

      .user-email {
        font-size: 14px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .ul {
    border-bottom: 1px solid #f3f3f3;
    padding-top: 20px;
    padding-bottom: 20px;

    &:last-child {
      border: none;
      padding-bottom: 0;
    }

    .li {
      padding: 4px 0;

      &.tit {
        font-size: 14px;
        color: #b7b7b7;
        line-height: 1.2;
      }

      &.link {
        font-size: 16px;
        color: #202020;
        line-height: 1.2;
        cursor: pointer;
      }
    }
  }
}

:deep(.el-dialog) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  background: transparent !important;
  border: none;
  box-shadow: none;
  overflow: hidden;
  
  // 标题区域样式
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px;
  
    backdrop-filter: blur(8px);
    border-radius: 16px 16px 0 0;
    
    :deep(.el-dialog__title) {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
    
    :deep(.el-dialog__close)   {
      color: #666;
      font-size: 20px;
      
      &:hover {
        color: #333;
      }
    }
  }
  
  // 内容区域样式
  :deep(.el-dialog__body) {
    margin: 0;
    padding: 24px;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    
    :deep(.el-select) {
      width: 100%;
      
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        background: rgba(255, 255, 255, 0.9);
        
        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }
    }
  }
  
  // 底部区域样式
  :deep(.el-dialog__footer) {
    margin: 0;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    border-radius: 0 0 16px 16px;
    
    :deep(.dialog-footer) {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      
      :deep(.el-button) {
        margin: 0;
        height: 36px;
        padding: 0 20px;
        font-size: 14px;
        border-radius: 8px;
        
        // 取消按钮
        &:not(.el-button--primary) {
          color: #606266;
          border: 1px solid #dcdfe6;
          background: transparent;
          
          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background: rgba(255, 255, 255, 0.9);
          }
        }
        
        // 确认按钮
        &.el-button--primary {
          color: #fff;
          border: none;
          background: #409eff;
          
          &:hover {
            background: #66b1ff;
          }
        }
      }
    }
  }
}

// 遮罩层样式
:deep(.el-overlay) {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}
</style>
