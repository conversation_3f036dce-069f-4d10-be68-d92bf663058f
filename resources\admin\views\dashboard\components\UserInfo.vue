<template>
  <div class="side-module">
    <h3 class="module-tit">{{ t('dashboard.userInfo.title') }}</h3>
    <div class="module-con">
      <div class="user-info">
        <div class="user-avatar">
          <img :src="$asset('/Dashboard/Asset/Jacky.png')" alt="User Avatar" />
          <div class="online-status"></div>
        </div>
        <div class="user-details">
          <h2>{{ userInfo.name }}</h2>
          <p>{{ t('dashboard.userInfo.specialist') }}</p>
        </div>
      </div>
    </div>
    <el-button plain @click="handleContact">
      <div class="content-left">{{ t('dashboard.userInfo.contactButton') }}</div>
      <el-icon><ArrowRight /></el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
import { ref, onMounted } from 'vue'

const { t } = useI18n()

const userInfo = ref({
  name: '',
  avatar: '',
  email: '',
})

const getUserInfo = async () => {
  const res = await http.get('/dashboard/userInfo/data')
  if (res.data && res.data.data) {
    userInfo.value = res.data.data
  }
}

const handleContact = () => {
  window.open(`mailto:${userInfo.value.email}`, '_blank')
}

onMounted(() => {
  getUserInfo()
})
</script>

<style lang="scss" scoped>
.user-info {
  display: flex;
  align-items: center;

  .user-avatar {
    border-radius: 50%;
    width: 58px;
    height: 58px;
    position: relative;

    img {
      border-radius: 50%;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .online-status {
      position: absolute;
      right: 4px;
      bottom: 0;

      border-radius: 50%;
      border: 2px solid #fff;
      width: 10px;
      height: 10px;
      background-color: #36b843;
    }
  }

  .user-details {
    padding-left: 15px;

    h2 {
      margin-bottom: 10px;
      font-size: 18px;
      font-weight: 700;
      line-height: 1.1666;
      color: #000;
    }

    p {
      font-size: 14px;
      line-height: 1.1428;
      color: #000;
    }
  }
}
</style>
