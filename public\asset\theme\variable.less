@themeColor: #708B6D; // 主题色
@auxiliaryColor: #8DC585; // 辅助色
@auxiliaryColor1: #9AB38B; // 辅助色
@auxiliaryColor2: #CBE8C3; // 辅助色
@auxiliaryColor3: #7B9E77; // 辅助色
@bodyColor: #000; // 正文颜色

@sPc: 1400px; // PC小屏
@hPad: 1200px; // 横版平板
@vPad: 992px; // 竖版平板
@phone: 576px; // 手机端

.df(@alignItems: stretch, @justifyContent: flex-start, @direction: row) {
	display: flex;
	flex-direction: @direction;
	align-items: @alignItems;
	justify-content: @justifyContent;
}

// 多行溢出隐藏
.vertical(@n) {
	display: -webkit-box;
	-webkit-line-clamp: @n;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.btn-square(@w: 48px, @h: 48px, @bgColor: #FFD100, @mt: 1rem, @fz: 18px, @color: #fff, @iconFz: 18px) {
	margin-top: @mt;
	width: @w;
	height: @h;
	font-size: @fz;
	background-color: @bgColor;
	color: @color;
	transition: all 0.5s ease-in-out;
	.df(center, center);

	.iconfont {
		font-size: @iconFz;
	}
}

.btn-radius(@radius: 50px, @py: 8px, @px: 16px, @color: #383838, @bgColor: #F7F7F7, @hoverBgColor: #aaa, @hoverColor: #fff) {
	border-radius: @radius;
	padding: @py @px;
	color: @color;
	background-color: @bgColor;
	transition: all .35s ease-in-out;
	.df(center, center);

	.iconfont + span {
		margin-left: 5px;
	}

	span + .iconfont {
		display: block;
		margin-right: 5px;
	}

	span {
		display: block;
		line-height: 1.5;
	}

	&:hover {
		background-color: @hoverBgColor;
		color: @hoverColor;
	}
}