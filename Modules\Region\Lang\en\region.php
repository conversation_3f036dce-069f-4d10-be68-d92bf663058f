<?php

return [
    // General Messages
    'success' => 'Operation successful',
    'failed' => 'Operation failed',
    'not_found' => 'Data not found',
    'already_exists' => 'Data already exists',
    'validation_failed' => 'Validation failed',
    'permission_denied' => 'Permission denied',
    'system_error' => 'System error',

    // Region Related
    'region' => 'Region',
    'regions' => 'Region List',
    'region_name' => 'Region Name',
    'region_description' => 'Region Description',
    'region_status' => 'Region Status',
    'region_sort' => 'Region Sort',
    'region_created_at' => 'Created At',
    'region_updated_at' => 'Updated At',

    // Channel Related
    'channel' => 'Channel',
    'channels' => 'Channel List',
    'channel_name' => 'Channel Name',
    'channel_description' => 'Channel Description',
    'channel_status' => 'Channel Status',
    'channel_sort' => 'Channel Sort',
    'channel_url' => 'Channel URL',
    'channel_cover_img' => 'Channel Cover',
    'channel_created_at' => 'Created At',
    'channel_updated_at' => 'Updated At',
    'channel_regions' => 'Associated Regions',
    'channel_region_count' => 'Associated Region Count',

    // Status
    'status_enabled' => 'Enabled',
    'status_disabled' => 'Disabled',
    'status_active' => 'Active',
    'status_inactive' => 'Inactive',

    // Actions
    'create' => 'Create',
    'update' => 'Update',
    'delete' => 'Delete',
    'enable' => 'Enable',
    'disable' => 'Disable',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'batch' => 'Batch Operation',

    // Success Messages
    'create_success' => 'Created successfully',
    'update_success' => 'Updated successfully',
    'delete_success' => 'Deleted successfully',
    'enable_success' => 'Enabled successfully',
    'disable_success' => 'Disabled successfully',
    'batch_success' => 'Batch operation successful',
    'status_update_success' => 'Status updated successfully',

    // Failed Messages
    'create_failed' => 'Creation failed',
    'update_failed' => 'Update failed',
    'delete_failed' => 'Deletion failed',
    'enable_failed' => 'Enable failed',
    'disable_failed' => 'Disable failed',
    'batch_failed' => 'Batch operation failed',
    'status_change_failed' => 'Status change failed',

    // Validation Messages
    'name_required' => 'Name is required',
    'name_max' => 'Name cannot exceed :max characters',
    'description_max' => 'Description cannot exceed :max characters',
    'status_invalid' => 'Invalid status value',
    'sort_invalid' => 'Invalid sort value',
    'url_invalid' => 'Invalid URL format',
    'cover_img_invalid' => 'Invalid cover image format',
    'region_ids_invalid' => 'Invalid region ID format',
    'status_required' => 'Status is required',

    // Error Messages
    'region_not_found' => 'Region not found',
    'region_already_exists' => 'Region already exists',
    'region_create_failed' => 'Failed to create region',
    'region_update_failed' => 'Failed to update region',
    'region_delete_failed' => 'Failed to delete region',
    'region_status_invalid' => 'Invalid region status',
    'region_list_failed' => 'Failed to get region list',

    'channel_not_found' => 'Channel not found',
    'channel_already_exists' => 'Channel already exists',
    'channel_create_failed' => 'Failed to create channel',
    'channel_update_failed' => 'Failed to update channel',
    'channel_delete_failed' => 'Failed to delete channel',
    'channel_status_invalid' => 'Invalid channel status',
    'channel_regions_associate_failed' => 'Failed to associate regions',
    'channel_regions_disassociate_failed' => 'Failed to disassociate regions',
    'channel_list_failed' => 'Failed to get channel list',

    // Batch Operation Messages
    'batch_delete_success' => 'Successfully deleted :count channels',
    'batch_enable_success' => 'Successfully enabled :count channels',
    'batch_disable_success' => 'Successfully disabled :count channels',
    'batch_region_delete_success' => 'Successfully deleted :count regions',
    'batch_region_enable_success' => 'Successfully enabled :count regions',
    'batch_region_disable_success' => 'Successfully disabled :count regions',

    // Parameter Validation
    'action_required' => 'Action type is required',
    'action_invalid' => 'Invalid action type',
    'ids_required' => 'ID list is required',
    'ids_invalid' => 'Invalid ID list format',
    'id_invalid' => 'Invalid ID format',

    // Pagination Related
    'page_info' => 'Page :current of :total records',
    'no_data' => 'No data available',
    'loading' => 'Loading...',

    // Search and Filter
    'search_placeholder' => 'Enter search keywords',
    'filter_all' => 'All',
    'filter_enabled' => 'Enabled',
    'filter_disabled' => 'Disabled',

    // Sorting
    'sort_by_name' => 'Sort by name',
    'sort_by_created_at' => 'Sort by creation time',
    'sort_by_updated_at' => 'Sort by update time',
    'sort_asc' => 'Ascending',
    'sort_desc' => 'Descending',

    // Association Operations
    'associate_success' => 'Association successful',
    'associate_failed' => 'Association failed',
    'disassociate_success' => 'Disassociation successful',
    'disassociate_failed' => 'Disassociation failed',

    // Statistics
    'total_count' => 'Total count',
    'enabled_count' => 'Enabled count',
    'disabled_count' => 'Disabled count',
    'channel_count' => 'Channel count',
    'region_count' => 'Region count',
]; 