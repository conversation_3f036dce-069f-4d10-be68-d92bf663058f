/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    404: typeof import('./resources/admin/components/404/index.vue')['default']
    AccessLog: typeof import('./resources/admin/layout/components/Menu/usercomponents/AccessLog.vue')['default']
    AccountBinding: typeof import('./resources/admin/layout/components/Menu/usercomponents/AccountBinding.vue')['default']
    AccountSafety: typeof import('./resources/admin/layout/components/Menu/usercomponents/AccountSafety.vue')['default']
    Add: typeof import('./resources/admin/components/admin/buttons/add.vue')['default']
    AMisRenderer: typeof import('./resources/admin/components/amis/AMisRenderer.vue')['default']
    AutoPage: typeof import('./resources/admin/layout/autoPage.vue')['default']
    AutoPage1: typeof import('./resources/admin/layout/autoPage1.vue')['default']
    Breadcrumbs: typeof import('./resources/admin/components/breadcrumbs/index.vue')['default']
    Chatbot: typeof import('./resources/admin/layout/components/chatbot.vue')['default']
    ConfigLayout: typeof import('./resources/admin/components/configLayout/index.vue')['default']
    Content: typeof import('./resources/admin/layout/components/content.vue')['default']
    CustomIcon: typeof import('./resources/admin/components/svgicon/CustomIcon.vue')['default']
    Destroy: typeof import('./resources/admin/components/admin/buttons/destroy.vue')['default']
    Dialog: typeof import('./resources/admin/components/admin/dialog/index.vue')['default']
    Editor: typeof import('./resources/admin/components/editor/index.vue')['default']
    EnterpriseIdentitySource: typeof import('./resources/admin/layout/components/Menu/usercomponents/EnterpriseIdentitySource.vue')['default']
    Header: typeof import('./resources/admin/layout/components/header/index.vue')['default']
    IamPage: typeof import('./resources/admin/layout/iamPage.vue')['default']
    Icon: typeof import('./resources/admin/components/icon/index.vue')['default']
    Icons: typeof import('./resources/admin/components/admin/icons/index.vue')['default']
    Item: typeof import('./resources/admin/layout/components/Menu/item.vue')['default']
    Lang: typeof import('./resources/admin/layout/components/header/lang.vue')['default']
    Layout: typeof import('./resources/admin/layout/index.vue')['default']
    Logo: typeof import('./resources/admin/layout/components/header/logo.vue')['default']
    Menu: typeof import('./resources/admin/layout/components/Menu/index.vue')['default']
    MenuItem: typeof import('./resources/admin/layout/components/Menu/menuItem.vue')['default']
    Menus: typeof import('./resources/admin/layout/components/Menu/menus.vue')['default']
    Message: typeof import('./resources/admin/layout/components/header/message.vue')['default']
    MultiFactorAuth: typeof import('./resources/admin/layout/components/Menu/usercomponents/MultiFactorAuth.vue')['default']
    Notification: typeof import('./resources/admin/layout/components/header/notification.vue')['default']
    Operate: typeof import('./resources/admin/components/admin/table/operate.vue')['default']
    Oss: typeof import('./resources/admin/components/admin/upload/oss.vue')['default']
    OtpAuth: typeof import('./resources/admin/layout/components/Menu/usercomponents/OtpAuth.vue')['default']
    Paginate: typeof import('./resources/admin/components/admin/paginate/index.vue')['default']
    Passkey: typeof import('./resources/admin/layout/components/Menu/usercomponents/Passkey.vue')['default']
    PersonalInfo: typeof import('./resources/admin/layout/components/Menu/usercomponents/PersonalInfo.vue')['default']
    Popover: typeof import('./resources/admin/components/popover/index.vue')['default']
    Profile: typeof import('./resources/admin/layout/components/header/profile.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./resources/admin/components/admin/table/search.vue')['default']
    Select: typeof import('./resources/admin/components/admin/select/index.vue')['default']
    Setting: typeof import('./resources/admin/layout/components/header/setting.vue')['default']
    SettingPage: typeof import('./resources/admin/layout/settingPage.vue')['default']
    Show: typeof import('./resources/admin/components/admin/buttons/show.vue')['default']
    Sider: typeof import('./resources/admin/layout/components/sider.vue')['default']
    SocialIdentitySource: typeof import('./resources/admin/layout/components/Menu/usercomponents/SocialIdentitySource.vue')['default']
    Status: typeof import('./resources/admin/components/admin/status/index.vue')['default']
    Theme: typeof import('./resources/admin/layout/components/header/theme.vue')['default']
    Update: typeof import('./resources/admin/components/admin/buttons/update.vue')['default']
    Upload: typeof import('./resources/admin/components/admin/upload/index.vue')['default']
    UserInfoMenu: typeof import('./resources/admin/layout/components/Menu/userInfoMenu.vue')['default']
  }
}
