import { Node, mergeAttributes } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    countdownBlock: {
      /**
       * 插入倒计时模块
       */
      insertCountdown: (options?: { title?: string, targetDate?: string, message?: string }) => ReturnType
    }
  }
}

export const CountdownBlock = Node.create({
  name: 'countdownBlock',
  
  group: 'block',
  
  atom: true,  // 作为一个不可分割的整体
  
  draggable: true,  // 允许拖拽
  
  selectable: true,  // 允许选择

  // 设置可配置的属性
  addAttributes() {
    return {
      title: {
        default: '倒计时',
        parseHTML: element => element.querySelector('.countdown-title')?.textContent || '倒计时'
      },
      targetDate: {
        default: () => {
          const date = new Date();
          date.setDate(date.getDate() + 7); // 默认7天后
          return date.toISOString();
        },
        parseHTML: element => {
          // 从元素上获取日期属性，如果没有则使用默认值
          return element.getAttribute('data-target-date') || null;
        }
      },
      message: {
        default: '活动即将开始，敬请期待！',
        parseHTML: element => element.querySelector('.countdown-message')?.textContent || '活动即将开始，敬请期待！'
      },
      // 样式相关属性
      theme: {
        default: 'primary',
        parseHTML: element => {
          const valueEl = element.querySelector('.countdown-value');
          if (valueEl) {
            const classes = Array.from(valueEl.classList);
            for (const cls of classes) {
              if (cls.startsWith('bg-')) {
                return cls.replace('bg-', '');
              }
            }
          }
          return 'primary';
        }
      }
    }
  },

  // 定义HTML解析规则
  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="countdown"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false;
          }
          return {};
        }
      },
    ]
  },

  // 定义HTML渲染规则
  renderHTML({ node }) {
    const { title, message, theme } = node.attrs;
    
    return [
      'div',
      mergeAttributes(
        { 'data-bs-component': 'countdown', class: 'bootstrap-countdown my-4' },
        { 'data-target-date': node.attrs.targetDate }
      ),
      [
        'div', { class: 'p-0 container-fluid' }, [
          'div', { class: 'row justify-content-center' }, [
            'div', { class: 'col-12 col-md-10 col-lg-8' }, [
              'div', { class: 'countdown-container text-center' }, [
                ['h3', { class: 'countdown-title mb-4' }, title],
                ['div', { class: 'countdown-wrapper d-flex justify-content-center' }, [
                  ['div', { class: 'countdown-item days-item mx-2' }, [
                    ['div', { class: `countdown-value bg-${theme} text-white rounded p-3 mb-1 fs-1 fw-bold` }, '00'],
                    ['div', { class: 'countdown-label' }, '天']
                  ]],
                  ['div', { class: 'countdown-item hours-item mx-2' }, [
                    ['div', { class: `countdown-value bg-${theme} text-white rounded p-3 mb-1 fs-1 fw-bold` }, '00'],
                    ['div', { class: 'countdown-label' }, '时']
                  ]],
                  ['div', { class: 'countdown-item minutes-item mx-2' }, [
                    ['div', { class: `countdown-value bg-${theme} text-white rounded p-3 mb-1 fs-1 fw-bold` }, '00'],
                    ['div', { class: 'countdown-label' }, '分']
                  ]],
                  ['div', { class: 'countdown-item seconds-item mx-2' }, [
                    ['div', { class: `countdown-value bg-${theme} text-white rounded p-3 mb-1 fs-1 fw-bold` }, '00'],
                    ['div', { class: 'countdown-label' }, '秒']
                  ]]
                ]],
                ['div', { class: 'countdown-message mt-4' }, message]
              ]
            ]
          ]
        ]
      ],
      ['script', {}, `
        (function() {
          // 找到所有倒计时组件
          const countdown = document.currentScript.parentElement;
          if (!countdown) return;
          
          // 提取目标日期
          const targetDateStr = countdown.getAttribute('data-target-date');
          const targetDate = targetDateStr ? new Date(targetDateStr) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
          
          // 获取显示元素
          const daysEl = countdown.querySelector('.days-item .countdown-value');
          const hoursEl = countdown.querySelector('.hours-item .countdown-value');
          const minutesEl = countdown.querySelector('.minutes-item .countdown-value');
          const secondsEl = countdown.querySelector('.seconds-item .countdown-value');
          
          if (!daysEl || !hoursEl || !minutesEl || !secondsEl) return;
          
          // 更新倒计时函数
          function updateCountdown() {
            const now = new Date();
            const diff = targetDate.getTime() - now.getTime();
            
            if (diff <= 0) {
              // 倒计时结束
              daysEl.textContent = '00';
              hoursEl.textContent = '00';
              minutesEl.textContent = '00';
              secondsEl.textContent = '00';
              return;
            }
            
            // 计算时间差
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            // 格式化显示
            daysEl.textContent = String(days).padStart(2, '0');
            hoursEl.textContent = String(hours).padStart(2, '0');
            minutesEl.textContent = String(minutes).padStart(2, '0');
            secondsEl.textContent = String(seconds).padStart(2, '0');
          }
          
          // 初始更新
          updateCountdown();
          
          // 每秒更新
          const intervalId = setInterval(updateCountdown, 1000);
          
          // 清理函数
          function cleanup() {
            clearInterval(intervalId);
            document.removeEventListener('visibilitychange', visibilityHandler);
          }
          
          // 处理页面可见性变化，优化性能
          function visibilityHandler() {
            if (document.hidden) {
              clearInterval(intervalId);
            } else {
              updateCountdown();
              setInterval(updateCountdown, 1000);
            }
          }
          
          document.addEventListener('visibilitychange', visibilityHandler);
          
          // 页面卸载时清理
          window.addEventListener('beforeunload', cleanup);
        })();
      `]
    ]
  },

  // 添加命令
  addCommands() {
    return {
      insertCountdown: (options = {}) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            title: options.title || '倒计时',
            targetDate: options.targetDate || (() => {
              const date = new Date();
              date.setDate(date.getDate() + 7);
              return date.toISOString();
            })(),
            message: options.message || '活动即将开始，敬请期待！'
          }
        });
      }
    }
  }
}); 