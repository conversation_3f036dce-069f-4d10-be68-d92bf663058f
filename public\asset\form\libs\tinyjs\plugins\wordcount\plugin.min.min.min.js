!function(){"use strict";function r(t){return t}function g(t){for(var e=a,n=c.length,r=0;r<n;++r){var o=c[r];if(o&&o.test(t)){e=r;break}}return e}function d(t,e,n,r){for(var o,u,c,a,i,s,g,d,l,p=[],h=[],f=0;f<n.length;++f)h.push(t[f]),function(t,e){var n=t[e],r=t[e+1];if(!(e<0||e>t.length-1&&0!==e||0===n&&0===r)){var o=t[e+2];if(0!==n||2!==r&&1!==r&&12!==r||0!==o){var u=t[e-1];return(2!==n&&1!==n&&12!==r||0!==r||0!==u)&&(4!==n&&0!==n||4!==r&&0!==r)&&(3!==n&&1!==n||4!==r||4!==u)&&(4!==n||3!==r&&1!==r||4!==o)&&8!==n&&9!==n&&8!==u&&9!==u&&8!==r&&9!==r&&(5!==n||6!==r)&&(7===n||5===n||6===n||7===r||5===r||6===r||(10!==n||10!==r)&&(11!==r||0!==n&&4!==n&&10!==n&&11!==n)&&(11!==n||0!==r&&4!==r&&10!==r)&&12!==n)}}}(n,f)&&(o=e[f],!r.includeWhitespace&&W.test(o)||!r.includePunctuation&&E.test(o)||(u=f-h.length+1,"http"!==(a=e.slice(u,c=f+1).join(""))&&"https"!==a||(d=f,l=void 0,l=function(t){for(var e=d+1;e<t.length&&!W.test(t[e]);e++);return e}(g=e),i="://"===g.slice(d+1,l).join("").substr(0,3)?l:d,s=t.slice(c,i),Array.prototype.push.apply(h,s),f=i),p.push(h)),h=[]);return p}function o(t,e){for(var n,r=e.getBlockElements(),o=e.getShortEndedElements(),u=[],c="",a=new s(t,t);t=a.next();)3===t.nodeType?c+=t.data.replace(/\uFEFF/g,""):(r[(n=t).nodeName]||o[n.nodeName])&&c.length&&(u.push(c),c="");return c.length&&u.push(c),u}function n(t){return t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length}function l(t,e){var n=o(t,e).join("\n").replace(/\u200B/g,"");return i(n.split(""),r).length}function p(t,e){return n(o(t,e).join(""))}function h(t,e){return n(o(t,e).join("").replace(/\s/g,""))}function f(t,e){return function(){return e(t.getBody(),t.schema)}}function C(t,e){return function(){return e(t.selection.getRng().cloneContents(),t.schema)}}function y(t){return f(t,l)}function v(t,e){var n=e;t.fire("wordCountUpdate",{wordCount:{words:n.body.getWordCount(),characters:n.body.getCharacterCount(),charactersWithoutSpaces:n.body.getCharacterCountWithoutSpaces()}})}var w,t=tinymce.util.Tools.resolve("tinymce.PluginManager"),m=function(){return(m=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},e=[new RegExp("[A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԧԱ-Ֆՙա-ևא-תװ-׳ؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘऄ-हऽॐक़-ॡॱ-ॷॹ-ॿঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘౙౠౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൠൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆༀཀ-ཇཉ-ཬྈ-ྌႠ-Ⴥა-ჺჼᄀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤜᨀ-ᨖᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᯀ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᳩ-ᳬᳮ-ᳱᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⒶ-ⓩⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⴀ-ⴥⴰ-ⵥⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々〻〼ㄅ-ㄭㄱ-ㆎㆠ-ㆺꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚗꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞎꞐꞑꞠ-ꞩꟺ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꨀ-ꨨꩀ-ꩂꩄ-ꩋꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ]"),new RegExp("[-'\\.‘’․﹒＇．]"),new RegExp("[:··״‧︓﹕：]"),new RegExp("[±+*/,;;։،؍٬߸⁄︐︔﹐﹔，；]"),new RegExp("[0-9٠-٩٫۰-۹߀-߉०-९০-৯੦-੯૦-૯୦-୯௦-௯౦-౯೦-೯൦-൯๐-๙໐-໙༠-༩၀-၉႐-႙០-៩᠐-᠙᥆-᥏᧐-᧙᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙꘠-꘩꣐-꣙꤀-꤉꧐-꧙꩐-꩙꯰-꯹]"),new RegExp("\\r"),new RegExp("\\n"),new RegExp("[\v\f\u2028\u2029]"),new RegExp("[̀-ͯ҃-҉֑-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ऀ-ःऺ-़ा-ॏ॑-ॗॢॣঁ-ঃ়া-ৄেৈো-্ৗৢৣਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑੰੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣஂா-ூெ-ைொ-்ௗఁ-ఃా-ౄె-ైొ-్ౕౖౢౣಂಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣംഃാ-ൄെ-ൈൊ-്ൗൢൣංඃ්ා-ුූෘ-ෟෲෳัิ-ฺ็-๎ັິ-ູົຼ່-ໍ༹༘༙༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏႚ-ႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳា-៓៝᠋-᠍ᢩᤠ-ᤫᤰ-᤻ᦰ-ᧀᧈᧉᨗ-ᨛᩕ-ᩞ᩠-᩿᩼ᬀ-ᬄ᬴-᭄᭫-᭳ᮀ-ᮂᮡ-᯦᮪-᯳ᰤ-᰷᳐-᳔᳒-᳨᳭ᳲ᷀-ᷦ᷼-᷿‌‍⃐-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꙯-꙲꙼꙽꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-꣄꣠-꣱ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀ꨩ-ꨶꩃꩌꩍꩻꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꯣ-ꯪ꯬꯭ﬞ︀-️︠-︦ﾞﾟ]"),new RegExp("[­؀-؃۝܏឴឵‎‏‪-‮⁠-⁤⁪-⁯\ufeff￹-￻]"),new RegExp("[〱-〵゛゜゠-ヺー-ヿㇰ-ㇿ㋐-㋾㌀-㍗ｦ-ﾝ]"),new RegExp("[=_‿⁀⁔︳︴﹍-﹏＿∀-⋿<>]"),new RegExp("@")],u=new RegExp("^[!-#%-*,-\\/:;?@\\[-\\]_{}¡«·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰෴๏๚๛༄-༒༺-༽྅࿐-࿔࿙࿚၊-၏჻፡-፨᐀᙭᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰⸱、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･]$"),c=e,a=13,W=/^\s+$/,E=u,i=function(t,e,n){n=m(m({},{includeWhitespace:!1,includePunctuation:!1}),n);for(var r=[],o=[],u=0;u<t.length;u++){var c=e(t[u]);"\ufeff"!==c&&(r.push(t[u]),o.push(c))}var a,i,s=function(t,e){for(var n=t.length,r=new Array(n),o=0;o<n;o++){var u=t[o];r[o]=e(u,o)}return r}(o,(a=g,i={},function(t){if(i[t])return i[t];var e=a(t);return i[t]=e}));return d(r,o,s,n)},s=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),x=tinymce.util.Tools.resolve("tinymce.util.Delay");void 0===w&&(w=300),t.add("wordcount",function(t){var e,n,r,o,u,c,a,i={body:{getWordCount:y(t),getCharacterCount:f(t,p),getCharacterCountWithoutSpaces:f(t,h)},selection:{getWordCount:C(t,l),getCharacterCount:C(t,p),getCharacterCountWithoutSpaces:C(t,h)},getCount:y(t)};function s(){return e.execCommand("mceWordCount")}return a=i,(c=t).addCommand("mceWordCount",function(){var t=a;c.windowManager.open({title:"Word Count",body:{type:"panel",items:[{type:"table",header:["Count","Document","Selection"],cells:[["Words",String(t.body.getWordCount()),String(t.selection.getWordCount())],["Characters (no spaces)",String(t.body.getCharacterCountWithoutSpaces()),String(t.selection.getCharacterCountWithoutSpaces())],["Characters",String(t.body.getCharacterCount()),String(t.selection.getCharacterCount())]]}]},buttons:[{type:"cancel",name:"close",text:"Close",primary:!0}]})}),(e=t).ui.registry.addButton("wordcount",{tooltip:"Word count",icon:"character-count",onAction:s}),e.ui.registry.addMenuItem("wordcount",{text:"Word count",icon:"character-count",onAction:s}),n=t,r=i,o=w,u=x.debounce(function(){return v(n,r)},o),n.on("init",function(){v(n,r),x.setEditorTimeout(n,function(){n.on("SetContent BeforeAddUndo Undo Redo ViewUpdate keyup",u)},0)}),i})}();