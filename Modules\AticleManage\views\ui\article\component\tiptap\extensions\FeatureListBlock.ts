import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import { Editor } from '@tiptap/vue-3'

interface FeatureListAttributes {
  className: string
  componentType: string
}

export const FeatureListBlock = Node.create<{ editor: Editor }>({
  name: 'featureListBlock',
  
  group: 'block',
  
  draggable: true,
  
  selectable: true,
  
  atom: true,
  
  inline: false,

  content: '',

  addOptions() {
    return {
      editor: {} as Editor,
    }
  },

  addAttributes() {
    return {
      componentType: {
        default: 'bootstrap-feature-list',
        parseHTML: element => {
          return element.getAttribute('data-bs-component') || 'bootstrap-feature-list'
        }
      },
      className: {
        default: 'bootstrap-feature-list',
        parseHTML: element => {
          return element.getAttribute('class') || 'bootstrap-feature-list'
        }
      },
      html: {
        default: null,
        parseHTML: element => {
          return element.outerHTML
        }
      },
    }
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="feature-list"]',
        getAttrs: (element: string | HTMLElement): Object => {
          if (typeof element === 'string') return {}
          
          return {
            componentType: element.getAttribute('data-bs-component'),
            className: element.getAttribute('class'),
            html: element.outerHTML,
          }
        },
      },
    ]
  },
  
  renderHTML({ HTMLAttributes }) {
    // 如果存在完整的 HTML 内容，直接返回容器元素
    if (HTMLAttributes.html) {
      const container = document.createElement('div')
      container.innerHTML = HTMLAttributes.html
      
      // 添加必要的属性以确保可以再次解析
      const featureListElement = container.firstElementChild
      if (featureListElement) {
        featureListElement.setAttribute('data-bs-component', 'feature-list')
        return { dom: featureListElement as HTMLElement }
      }
    }

    // 否则，创建新的 bootstrap-feature-list 组件
    return [
      'div',
      mergeAttributes({ 'data-bs-component': 'feature-list', class: 'bootstrap-feature-list' }, HTMLAttributes),
      0 // 内容占位符
    ]
  },
  
  addCommands() {
    return {
      setFeatureList: (attributes = {}) => ({ chain }) => {
        return chain()
          .insertContent({
            type: this.name,
            attrs: attributes,
          })
          .run()
      },
    }
  },
  
  addKeyboardShortcuts() {
    return {
      'Mod-Alt-f': () => this.editor.commands.setFeatureList(),
    }
  },
}); 