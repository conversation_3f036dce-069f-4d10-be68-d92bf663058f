# 编码标准

## 目录
- [PHP 编码规范](#php-编码规范)
- [TypeScript 编码规范](#typescript-编码规范)
- [数据库规范](#数据库规范)
- [注释规范](#注释规范)
- [命名规范](#命名规范)

## PHP 编码规范

### 1. 基本规范

```php
<?php

declare(strict_types=1);

namespace Modules\Course\Services;

use DateTime;
use InvalidArgumentException;
use Modules\Course\Domain\Repositories\CourseRepositoryInterface;

final class CourseService
{
    public function __construct(
        private readonly CourseRepositoryInterface $repository
    ) {
    }

    public function createCourse(array $data): Course
    {
        $this->validateCourseData($data);
        return $this->repository->create($data);
    }

    private function validateCourseData(array $data): void
    {
        if (empty($data['title'])) {
            throw new InvalidArgumentException('课程标题不能为空');
        }
    }
}
```

### 2. 代码风格

- 使用 4 个空格缩进
- 行长度不超过 120 字符
- 文件必须以一个空行结束
- 运算符前后必须有空格
- 逗号后必须��空格
- 括号内部不应有空格

### 3. 类规范

```php
class Course
{
    // 常量声明在顶部
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    // 属性声明
    private string $title;
    private string $description;
    private DateTime $startDate;

    // 构造函数
    public function __construct(string $title)
    {
        $this->title = $title;
    }

    // 公共方法
    public function getTitle(): string
    {
        return $this->title;
    }

    // 私有方法
    private function validateTitle(string $title): void
    {
        if (empty($title)) {
            throw new InvalidArgumentException('标题不能为空');
        }
    }
}
```

## TypeScript 编码规范

### 1. 基本规范

```typescript
// 接口定义
interface Course {
  id: number;
  title: string;
  description: string;
  startDate: Date;
  status: CourseStatus;
}

// 枚举定义
enum CourseStatus {
  Active = 1,
  Inactive = 0,
}

// 类定义
class CourseService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  public async getCourse(id: number): Promise<Course> {
    return this.apiClient.get(`/courses/${id}`);
  }
}
```

### 2. Vue 组件规范

```vue
<template>
  <div class="course-card">
    <h2>{{ course.title }}</h2>
    <p>{{ course.description }}</p>
    <el-button @click="handleClick">
      {{ buttonText }}
    </el-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import type { Course } from '@/types'

export default defineComponent({
  name: 'CourseCard',
  props: {
    course: {
      type: Object as PropType<Course>,
      required: true
    }
  },
  setup(props) {
    const handleClick = () => {
      // 处理点击事件
    }

    return {
      handleClick
    }
  }
})
</script>

<style lang="scss" scoped>
.course-card {
  padding: 16px;
  border-radius: 8px;
  
  h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }
}
</style>
```

## 数据库规范

### 1. 命名规范

```sql
-- 表名：模块名_表名，使用复数形式
CREATE TABLE course_courses (
    -- 主键：使用 id
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    
    -- 字段名：使用下划线命名法
    course_title VARCHAR(255) NOT NULL COMMENT '课程标题',
    course_description TEXT COMMENT '课程描述',
    
    -- 外键：关联表名_id
    category_id BIGINT UNSIGNED COMMENT '分类ID',
    
    -- 状态字段：统一使用 tinyint
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    
    -- 时间戳：统一格式
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    -- 索引命名：idx_字段名
    PRIMARY KEY (id),
    KEY idx_category (category_id),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. 字段规范

- 必须有主键，一般使用 id
- 必须有创建和更新时间戳
- 推荐使用软删除
- 字符集统一使用 utf8mb4
- 所有字段必须有注释
- 适当使用默认值

## 注释规范

### 1. PHP 文档注释

```php
/**
 * 课程服务类
 * 
 * 负责处理课程相关的业务逻辑，包括创建、更新、删除等操作
 */
class CourseService
{
    /**
     * 创建新课程
     *
     * @param array $data 课程数据
     * @throws InvalidArgumentException 当课程数据验证失败时
     * @return Course 创建的课程实例
     */
    public function createCourse(array $data): Course
    {
        // 方法实现
    }
}
```

### 2. TypeScript 注释

```typescript
/**
 * 课程接口
 * @interface Course
 */
interface Course {
  /** 课程ID */
  id: number;
  /** 课程标题 */
  title: string;
  /** 课程描述 */
  description: string;
}

/**
 * 获取课程信息
 * @param {number} id - 课程ID
 * @returns {Promise<Course>} 课程信息
 * @throws {Error} 当课程不存在时
 */
async function getCourse(id: number): Promise<Course> {
  // 函数实现
}
```

### 3. 数据库注释

```sql
-- 表注释
CREATE TABLE course_courses (
    -- 字段注释：描述字段用途、取值范围等
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '课程ID',
    course_title VARCHAR(255) NOT NULL COMMENT '课程标题',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    
    -- 索引注释
    PRIMARY KEY (id) COMMENT '主键索引',
    KEY idx_title (course_title) COMMENT '课程标题索引'
) COMMENT '课程信息表';
```

## 命名规范

### 1. PHP 命名

```php
// 命名空间：大写开头
namespace Modules\Course;

// 类名：大写开头驼峰
class CourseService {}

// 接口名：大写开头驼峰 + Interface
interface CourseRepositoryInterface {}

// 方法名：小写开头驼峰
public function createCourse() {}

// 变量名：小写开头驼峰
private string $courseTitle;

// 常量名：全大写下划线
const MAX_STUDENTS = 100;
```

### 2. TypeScript 命名

```typescript
// 接口名：大写开头驼峰
interface CourseData {
  // 属性名：小写开头驼峰
  courseTitle: string;
  studentCount: number;
}

// 组件名：大写开头驼峰
export const CourseList = defineComponent({});

// 方法名：小写开头驼峰
function getCourseDetails() {}

// 变量名：小写开头驼峰
const courseData = ref<CourseData>();

// 常量名：全大写下划线
const MAX_PAGE_SIZE = 20;
```

### 3. 数据库命名

```sql
-- 表名：模块名_表名（复数）
course_courses
course_chapters
course_students

-- 字段名：下划线分隔
course_title
start_date
created_at

-- 索引名：idx_字段名
idx_course_title
idx_created_at

-- 外键名：fk_当前表_关联表
fk_chapters_course
``` 
