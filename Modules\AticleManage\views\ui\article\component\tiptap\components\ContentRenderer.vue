<template>
  <div class="content-renderer" v-html="sanitizedContent"></div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'

const props = defineProps({
  content: {
    type: String,
    required: true
  },
  height: {
    type: String,
    default: 'auto'
  }
})

// 简单的HTML内容清理函数
const sanitizedContent = computed(() => {
  if (!props.content) return ''
  
  let content = props.content
  
  // 移除危险的标签
  content = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  content = content.replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
  content = content.replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
  content = content.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
  
  // 移除危险的事件属性
  content = content.replace(/\son\w+\s*=\s*["|'].*?["|']/gi, '')
  content = content.replace(/javascript:/gi, '')
  
  return content
})
</script>

<style scoped>
.content-renderer {
  width: 100%;
  height: v-bind(height);
  overflow: auto;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 16px;
  background: white;
}

/* Bootstrap样式支持 */
.content-renderer :deep(.btn) {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.375rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.content-renderer :deep(.btn-primary) {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.content-renderer :deep(.btn-primary:hover) {
  color: #fff;
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.content-renderer :deep(.card) {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.375rem;
}

.content-renderer :deep(.card-body) {
  flex: 1 1 auto;
  padding: 1rem 1rem;
}

.content-renderer :deep(.card-title) {
  margin-bottom: 0.5rem;
}

.content-renderer :deep(.card-text:last-child) {
  margin-bottom: 0;
}
</style> 