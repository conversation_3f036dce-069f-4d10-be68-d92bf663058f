@font-face {
  font-family: "iconfont"; /* Project id 4461434 */
  src: url('iconfont.woff2?t=1710137156305') format('woff2'),
       url('iconfont.woff?t=1710137156305') format('woff'),
       url('iconfont.ttf?t=1710137156305') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-load:before {
  content: "\e608";
}

.icon-warning:before {
  content: "\e609";
}

.icon-check:before {
  content: "\e616";
}

.icon-hr:before {
  content: "\e60a";
}

