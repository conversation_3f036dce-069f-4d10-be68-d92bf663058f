import { createRouter, createWebHistory, RouteRecordRaw, RouteLocationNormalized } from 'vue-router'
import type { App } from 'vue'
import { getModuleRoutes, getModuleViewComponents } from './constantRoutes'
import { getAuthToken, removeAuthToken } from '../support/helper'

const moduleRoutes = getModuleRoutes()
getModuleViewComponents()

export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/frontend/layout/index.vue'),
    children: [
      {
        path: '',
        name: 'home',
        meta: { title: 'home', icon: 'Tickets' },
        component: () => import('@/module/Iam/FrontendView/ui/Home/presentation/HomePage.vue'),
      },
    ],
  },
  {
    path: '/dashboard',
    component: () => import('/admin/layout/index.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        meta: { title: 'Dashboard', icon: 'home', hidden: false },
        component: () => import('/admin/views/dashboard/index.vue'),
      },
    ],
  },
]
  // @ts-ignore
  .concat(moduleRoutes)

const defaultRoutes: RouteRecordRaw[] = [
  {
    path: '/dashboard',
    name: 'dashboard',
    meta: { title: '首页' },
    component: () => import('/admin/views/dashboard/index.vue'),
    props: route => ({ app_code: 'frontend' }),
  },
]

const routes = constantRoutes.concat(defaultRoutes)
const router = createRouter({
  history: createWebHistory('/console/'), // 确保 base 路径与线上环境匹配
  routes,
  // 路由滚动
  scrollBehavior(to, from, savedPosition) {
    return savedPosition || { top: 0, behavior: 'smooth' }
  },
})

// 修改路由拦截
router.beforeEach((to, from, next) => {
  // 检查是否是公开路由（不需要登录）
  if (to.meta.requiresAuth === false) {
    next()
    return
  }

  const token = getAuthToken()

  const ensureAppCode = (route: RouteLocationNormalized): RouteLocationNormalized => {
    if (!route.query || !route.query.app_code) {
      return {
        ...route,
        query: {
          ...route.query,
          app_code: 'frontend',
        },
      }
    }
    return route
  }

  const proceedTo = (destination: Partial<RouteLocationNormalized> & { path?: string }) => {
    const finalDestination = ensureAppCode(destination as RouteLocationNormalized)

    const currentQuery = { ...to.query }
    const newQuery = { ...finalDestination.query }
    delete currentQuery.app_code
    delete newQuery.app_code

    if (finalDestination.path !== to.path || JSON.stringify(currentQuery) !== JSON.stringify(newQuery)) {
      next(finalDestination)
    } else {
      next()
    }
  }

  // 需要登录的路由进行拦截
  if (to.name !== 'login' && !token) {
    proceedTo({ name: 'login', query: { app_code: 'frontend' } })
  } else if (to.name === 'login' && token) {
    proceedTo({ name: 'home', query: { app_code: 'frontend' } })
  } else {
    proceedTo(to)
  }
})

export function bootstrapRouter(app: App) {
  app.use(router)
}

export default router
