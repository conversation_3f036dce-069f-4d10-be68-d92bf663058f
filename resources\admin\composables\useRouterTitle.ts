 import { computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import i18n from '/admin/i18n'

// 翻译路由标题的工具函数
export function translateRouteTitle(titleKey: string): string {
  if (!titleKey) return ''
  
  try {
    // 如果是多语言key，翻译它
    if (titleKey.includes('.')) {
      return i18n.global.t(titleKey)
    }
    // 否则直接返回原标题
    return titleKey
  } catch (error) {
    console.warn(`Failed to translate title: ${titleKey}`, error)
    return titleKey
  }
}

// 预处理路由标题（在路由守卫中使用）
export function preprocessRouteTitle(to: any) {
  if (to.meta?.title) {
    const translatedTitle = translateRouteTitle(to.meta.title as string)
    // 将翻译后的标题存储到路由元信息中
    to.meta._translatedTitle = translatedTitle
  }
}

export function useRouterTitle() {
  const route = useRoute()
  const router = useRouter()
  
  // 计算当前页面标题 - 优先使用预翻译的标题
  const pageTitle = computed(() => {
    // 优先使用预翻译的标题
    if (route.meta?._translatedTitle) {
      return route.meta._translatedTitle as string
    }
    
    const titleKey = route.meta?.title as string
    if (!titleKey) return ''
    
    return translateRouteTitle(titleKey)
  })
  
  // 更新document.title
  const updateDocumentTitle = () => {
    const baseTitle = '内容管理系统' // 可以从配置或多语言文件获取
    const currentTitle = pageTitle.value
    
    document.title = currentTitle 
      ? `${currentTitle} - ${baseTitle}`
      : baseTitle
  }
  
  // 立即更新document.title（同步执行）
  const immediateUpdate = () => {
    nextTick(() => {
      updateDocumentTitle()
    })
  }
  
  // 监听路由变化 - 立即执行
  watch(() => route.path, immediateUpdate, { immediate: true, flush: 'sync' })
  
  // 监听语言变化 - 立即执行
  watch(() => i18n.global.locale.value, immediateUpdate, { flush: 'sync' })
  
  // 监听路由meta变化
  watch(() => route.meta, immediateUpdate, { immediate: true, deep: true, flush: 'sync' })
  
  return {
    pageTitle,
    updateDocumentTitle: immediateUpdate
  }
} 