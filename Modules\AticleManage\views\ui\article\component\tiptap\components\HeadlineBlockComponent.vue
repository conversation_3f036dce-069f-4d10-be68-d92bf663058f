<template>
  <node-view-wrapper class="headline-block-component">
    <div 
      class="headline-block" 
      :class="blockClasses"
      data-type="headline-block"
      :data-title="node.attrs.title"
      :data-content="node.attrs.content"
      :data-align="node.attrs.align"
    >
      <div class="headline-content">
        <h2 class="headline-title">{{ node.attrs.title }}</h2>
        <p class="headline-text">{{ node.attrs.content }}</p>
      </div>
      
      <div v-if="editor.isEditable" class="headline-toolbar">
        <button 
          class="headline-edit-button" 
          @click.stop="openEditDialog"
          title="编辑"
        >
          ✏️
        </button>
        <div class="align-buttons">
          <button 
            class="align-button" 
            :class="{ active: node.attrs.align === 'left' }"
            @click.stop="updateAlignment('left')"
            title="左对齐"
          >
            ⬅️
          </button>
          <button 
            class="align-button" 
            :class="{ active: node.attrs.align === 'center' }"
            @click.stop="updateAlignment('center')"
            title="居中对齐"
          >
            ⏺️
          </button>
          <button 
            class="align-button" 
            :class="{ active: node.attrs.align === 'right' }"
            @click.stop="updateAlignment('right')"
            title="右对齐"
          >
            ➡️
          </button>
        </div>
      </div>
      
      <!-- 编辑对话框 -->
      <el-dialog v-model="showEditDialog" title="编辑标题区块" width="500px" append-to-body >
        <div class="headline-edit-dialog-content">
          <h4>编辑标题区块</h4>
          
          <div class="edit-form-group">
            <label for="headline-title">标题</label>
            <input 
              id="headline-title" 
              v-model="editForm.title" 
              type="text" 
              class="edit-input"
            />
          </div>
          
          <div class="edit-form-group">
            <label for="headline-content">内容</label>
            <textarea 
              id="headline-content" 
              v-model="editForm.content" 
              class="edit-textarea"
            ></textarea>
          </div>
          
          <div class="edit-form-group">
            <label>对齐方式</label>
            <div class="align-options">
              <div 
                class="align-option" 
                :class="{ active: editForm.align === 'left' }"
                @click="editForm.align = 'left'"
              >
                <div class="align-icon">⬅️</div>
                <span>左对齐</span>
              </div>
              <div 
                class="align-option" 
                :class="{ active: editForm.align === 'center' }"
                @click="editForm.align = 'center'"
              >
                <div class="align-icon">⏺️</div>
                <span>居中对齐</span>
              </div>
              <div 
                class="align-option" 
                :class="{ active: editForm.align === 'right' }"
                @click="editForm.align = 'right'"
              >
                <div class="align-icon">➡️</div>
                <span>右对齐</span>
              </div>
            </div>
          </div>
          
          <div class="edit-form-actions">
            <button 
              class="edit-cancel-button" 
              @click="showEditDialog = false"
            >
              取消
            </button>
            <button 
              class="edit-save-button" 
              @click="saveChanges"
            >
              保存
            </button>
          </div>
        </div>
      </el-dialog>
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { NodeViewWrapper } from '@tiptap/vue-3'

const props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  updateAttributes: {
    type: Function,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
})

const showEditDialog = ref(false)

// 编辑表单数据
const editForm = ref({
  title: props.node.attrs.title || '引人注目的标题',
  content: props.node.attrs.content || '详细说明文字，可以添加更多信息描述你的产品或服务。',
  align: props.node.attrs.align || 'center',
})

// 区块样式计算属性
const blockClasses = computed(() => {
  return {
    [`align-${props.node.attrs.align}`]: true
  }
})

// 打开编辑对话框
const openEditDialog = () => {
  // 更新表单数据为当前节点的属性
  editForm.value = {
    title: props.node.attrs.title,
    content: props.node.attrs.content,
    align: props.node.attrs.align,
  }
  showEditDialog.value = true
}

// 更新对齐方式
const updateAlignment = (align: string) => {
  props.updateAttributes({
    align
  })
}

// 保存修改
const saveChanges = () => {
  props.updateAttributes({
    title: editForm.value.title,
    content: editForm.value.content,
    align: editForm.value.align,
  })
  
  showEditDialog.value = false
}

// 点击页面其他区域关闭对话框
const closeDialogOnClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const dialog = document.querySelector('.headline-edit-dialog-content')
  
  if (showEditDialog.value && dialog && !dialog.contains(target) && !target.closest('.headline-edit-button')) {
    showEditDialog.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDialogOnClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', closeDialogOnClickOutside)
})
</script>

<style scoped>
.headline-block-component {
  margin: 2em 0;
  position: relative;
}

.headline-block {
  border: 1px dashed #dcdfe6;
  padding: 2em;
  border-radius: 4px;
  background-color: rgba(245, 247, 250, 0.5);
  position: relative;
}

/* 对齐方式 */
.align-left {
  text-align: left;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right;
}

.headline-content {
  max-width: 800px;
  margin: 0 auto;
}

.headline-title {
  font-size: 2em;
  font-weight: 600;
  color: #303133;
  margin: 0 0 0.5em 0;
  line-height: 1.3;
}

.headline-text {
  font-size: 1.1em;
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

/* 工具栏 */
.headline-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.headline-block:hover .headline-toolbar {
  opacity: 1;
}

.headline-edit-button,
.align-button {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.headline-edit-button:hover,
.align-button:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.align-buttons {
  display: flex;
  gap: 2px;
}

.align-button {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.align-button.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

/* 编辑对话框 */
.headline-edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.headline-edit-dialog-content {
  background-color: white;
  border-radius: 6px;
  padding: 1.5em;
  width: 500px;
  max-width: 90vw;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.headline-edit-dialog-content h4 {
  margin: 0 0 1em 0;
  font-size: 1.2em;
  color: #303133;
}

.edit-form-group {
  margin-bottom: 1em;
}

.edit-form-group label {
  display: block;
  margin-bottom: 0.5em;
  font-size: 0.9em;
  color: #606266;
}

.edit-input,
.edit-textarea {
  width: 100%;
  padding: 0.5em;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.5;
  color: #606266;
}

.edit-textarea {
  min-height: 80px;
  resize: vertical;
}

.align-options {
  display: flex;
  gap: 10px;
  justify-content: space-between;
}

.align-option {
  flex: 1;
  text-align: center;
  cursor: pointer;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.2s;
}

.align-option:hover {
  background-color: #f5f7fa;
}

.align-option.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.align-icon {
  font-size: 1.2em;
  margin-bottom: 0.3em;
}

.edit-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 1.5em;
}

.edit-cancel-button,
.edit-save-button {
  padding: 0.5em 1em;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.edit-cancel-button {
  background-color: white;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.edit-save-button {
  background-color: #409eff;
  border: 1px solid #409eff;
  color: white;
}

.edit-cancel-button:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.edit-save-button:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .headline-block {
    padding: 1.5em;
  }
  
  .headline-title {
    font-size: 1.6em;
  }
  
  .headline-text {
    font-size: 1em;
  }
  
  .align-options {
    flex-direction: column;
    gap: 5px;
  }
}
</style> 