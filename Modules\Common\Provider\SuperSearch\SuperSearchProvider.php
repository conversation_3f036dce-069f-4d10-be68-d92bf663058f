<?php

namespace Modules\Common\Provider\SuperSearch;

use Modules\Common\Provider\ProviderTrait;

class SuperSearchProvider
{
    use ProviderTrait;

    /**
     * @return AbstractSuperSearchProvider[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractSuperSearchProvider
     */
    public static function get($name): AbstractSuperSearchProvider
    {
        return self::getByName($name);
    }
}
