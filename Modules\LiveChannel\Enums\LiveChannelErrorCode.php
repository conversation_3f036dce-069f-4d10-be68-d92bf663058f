<?php

declare(strict_types=1);

namespace Modules\LiveChannel\Enums;

enum LiveChannelErrorCode: int
{
    // 18xxx - LiveChannel模块错误码
    case CHANNEL_NOT_FOUND = 18001;
    case CHANNEL_CREATE_FAILED = 18002;
    case CHANNEL_UPDATE_FAILED = 18003;
    case CHANNEL_DELETE_FAILED = 18004;
    case CHANNEL_VALIDATION_FAILED = 18005;
    case CHANNEL_STATUS_CHANGE_FAILED = 18006;
    case CHANNEL_LIST_FAILED = 18007;
    case CHANNEL_BATCH_ACTION_FAILED = 18008;
    case CHANNEL_NAME_EXISTS = 18009;
    case CHANNEL_NUM_EXISTS = 18010;
    case CHANNEL_STREAM_KEY_INVALID = 18011;

    public function message(): string
    {
        return match($this) {
            self::CHANNEL_NOT_FOUND => 'Channel not found',
            self::CHANNEL_CREATE_FAILED => 'Failed to create channel',
            self::CHANNEL_UPDATE_FAILED => 'Failed to update channel',
            self::CHANNEL_DELETE_FAILED => 'Failed to delete channel',
            self::CHANNEL_VALIDATION_FAILED => 'Validation failed',
            self::CHANNEL_STATUS_CHANGE_FAILED => 'Failed to change status',
            self::CHANNEL_LIST_FAILED => 'Failed to get channel list',
            self::CHANNEL_BATCH_ACTION_FAILED => 'Failed to perform batch action',
            self::CHANNEL_NAME_EXISTS => 'Channel name already exists',
            self::CHANNEL_NUM_EXISTS => 'Channel number already exists',
            self::CHANNEL_STREAM_KEY_INVALID => 'Invalid stream key',
        };
    }

    public function getMessage(string $locale = 'en'): string
    {
        $messages = [
            'en' => $this->message(),
            'zh_CN' => $this->getChineseMessage(),
            'zh_HK' => $this->getTraditionalChineseMessage(),
        ];

        return $messages[$locale] ?? $this->message();
    }

    private function getChineseMessage(): string
    {
        return match($this) {
            self::CHANNEL_NOT_FOUND => '频道不存在',
            self::CHANNEL_CREATE_FAILED => '创建频道失败',
            self::CHANNEL_UPDATE_FAILED => '更新频道失败',
            self::CHANNEL_DELETE_FAILED => '删除频道失败',
            self::CHANNEL_VALIDATION_FAILED => '验证失败',
            self::CHANNEL_STATUS_CHANGE_FAILED => '状态变更失败',
            self::CHANNEL_LIST_FAILED => '获取频道列表失败',
            self::CHANNEL_BATCH_ACTION_FAILED => '批量操作失败',
            self::CHANNEL_NAME_EXISTS => '频道名称已存在',
            self::CHANNEL_NUM_EXISTS => '频道编号已存在',
            self::CHANNEL_STREAM_KEY_INVALID => '直播密钥无效',
        };
    }

    private function getTraditionalChineseMessage(): string
    {
        return match($this) {
            self::CHANNEL_NOT_FOUND => '頻道不存在',
            self::CHANNEL_CREATE_FAILED => '創建頻道失敗',
            self::CHANNEL_UPDATE_FAILED => '更新頻道失敗',
            self::CHANNEL_DELETE_FAILED => '刪除頻道失敗',
            self::CHANNEL_VALIDATION_FAILED => '驗證失敗',
            self::CHANNEL_STATUS_CHANGE_FAILED => '狀態變更失敗',
            self::CHANNEL_LIST_FAILED => '獲取頻道列表失敗',
            self::CHANNEL_BATCH_ACTION_FAILED => '批量操作失敗',
            self::CHANNEL_NAME_EXISTS => '頻道名稱已存在',
            self::CHANNEL_NUM_EXISTS => '頻道編號已存在',
            self::CHANNEL_STREAM_KEY_INVALID => '直播密鑰無效',
        };
    }
} 