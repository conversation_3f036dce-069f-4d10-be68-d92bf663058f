<template>
  <div class="side-module">
    <h3 class="module-tit">{{ t('dashboard.websiteStatus.diskSpace.title') }}</h3>
    <div class="module-con">
      <div class="capacity-chart">
        <template v-if="loading">
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-content">
                <el-skeleton-item variant="circle" :style="{ width: screenWidth <= 1600 ? '120px' : '160px', height: screenWidth <= 1600 ? '120px' : '160px' }" />
                <div class="skeleton-text">
                  <el-skeleton-item variant="text" style="width: 80px; height: 24px; margin: 4px auto;" />
                  <el-skeleton-item variant="text" style="width: 60px; height: 20px; margin: 0 auto;" />
                </div>
              </div>
            </template>
          </el-skeleton>
        </template>
        <template v-else>
          <el-progress 
            type="circle" 
            :percentage="percentCapacity" 
            :width="screenWidth <= 1600 ? 120 : 160" 
            :stroke-width="screenWidth <= 1600 ? 8 : 12" 
            :show-text="false" 
            track-color="#f5f7fa" 
            :color="'#409eff'" 
          />
          <div class="capacity-text">
            <div class="used">{{ diskSpace.used_space }} /</div>
            <div class="total">{{ diskSpace.total_space }}</div>
          </div>
        </template>
      </div>
    </div>
    <el-button plain>
      <div class="content-left">{{ t('dashboard.websiteStatus.renewal.increaseCapacity') }}</div>
      <el-icon><ArrowRight /></el-icon>
    </el-button>
  </div>
  <div class="side-module">
    <h3 class="module-tit">{{ t('dashboard.websiteStatus.renewal.title') }}</h3>
    <div class="module-con">
      <template v-if="loading">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-content">
              <el-skeleton-item variant="text" style="width: 120px; height: 28px; margin: 0 auto 40px;" />
              <el-skeleton-item variant="text" style="width: 200px; height: 20px; margin: 0 auto;" />
            </div>
          </template>
        </el-skeleton>
      </template>
      <template v-else>
        <div class="days">{{ renewal.days }} {{ t('dashboard.websiteStatus.renewal.days') }}</div>
        <div class="date-range">{{ renewal.from }} {{ t('dashboard.websiteStatus.renewal.dateRange.to') }} {{ renewal.to }}</div>
      </template>
    </div>
    <el-button plain>
      <div class="content-left">{{ t('dashboard.websiteStatus.renewal.renewalNow') }}</div>
      <el-icon><ArrowRight /></el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

const { t } = useI18n()
const router = useRouter()
const loading = ref(true)
const screenWidth = ref(window.innerWidth)

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth
}

// 定义响应数据的ref
const diskSpace = ref({
  title: '',
  total_space: '0 GB',
  used_space: '0 GB',
  free_space: '0 GB'
})

const renewal = ref({
  title: '',
  days: 0,
  from: '',
  to: '',
  invoice_url: '#',
  status: 0,
  message: ''
})

// 计算使用百分比
const percentCapacity = computed<number>(() => {
  const used = convertToBytes(diskSpace.value.used_space)
  const total = convertToBytes(diskSpace.value.total_space)
  if (total === 0) return 0
  return parseFloat(((used / total) * 100).toFixed(2))
})

// 转换单位为字节的函数
function convertToBytes(value: string): number {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const regex = /(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)/i
  const match = value.match(regex)

  if (!match) return 0

  const [, num, unit] = match
  const index = units.indexOf(unit.toUpperCase())

  return parseFloat(num) * Math.pow(1024, index)
}

const getStatus = async () => {
  loading.value = true
  try {
    const res = await http.get('/dashboard/websiteStatus/data')
    const data = res.data.data
    
    diskSpace.value = data.disk_space
    renewal.value = data.renewal
  } catch (error) {
    console.error('获取系统状态失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  getStatus()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 跳转到媒体管理页面
const goMedia = () => {
  router.push({
    path: '/media/mediaManager',
  })
}
</script>

<script lang="ts">
export default {
  name: 'WebsiteStatus',
}
</script>

<style lang="scss" scoped>
.capacity-chart {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 1600px) {
    :deep(.el-progress) {
      width: 120px !important;
    }
  }

  :deep(.el-progress) {
    margin-bottom: 0;
  }

  .capacity-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .used {
      margin-bottom: 4px;
      font-size: 18px;
      font-weight: 700;
      color: #000;
      line-height: 1.166;

      @media screen and (max-width: 1600px) {
        font-size: 14px;
      }
    }

    .total {
      font-size: 16px;
      color: #000;
      line-height: 1.1875;

      @media screen and (max-width: 1600px) {
        font-size: 12px;
      }
    }
  }
}

.days {
  margin-bottom: 40px;
  font-size: 20px;
  color: #007ee5;
  line-height: 1.2;

  @media screen and (max-width: 1600px) {
    margin-bottom: 20px;
    font-size: 16px;
  }
}

.date-range {
  font-size: 14px;
  color: #000;
  line-height: 1.1428;
}

.skeleton-content {
  text-align: center;
  
  @media screen and (max-width: 1600px) {
    :deep(.el-skeleton-item.is-circle) {
      width: 120px !important;
      height: 120px !important;
    }
  }
  
  .skeleton-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
  }

  :deep(.el-skeleton__text) {
    background: rgba(0, 0, 0, 0.06);
  }
}
</style>
