<?php

namespace Modules\SEO\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * SEO模型
 * 对应数据库表: tvb_seo
 */
class Seo extends Model
{
    // 使用软删除功能
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'seo';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'title',                // SEO 标题
        'description',          // SEO 描述
        'keywords',             // SEO 关键词
        'regular_url',          // 正则URL
        'allow_index',          // 允许索引
        'allow_follow',         // 允许跟随
        'open_graph_title',     // Open Graph 标题
        'open_graph_description', // Open Graph 描述
        'created_at',           // 创建时间
        'updated_at',           // 更新时间
        'deleted_at',           // 软删除时间
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];
}
