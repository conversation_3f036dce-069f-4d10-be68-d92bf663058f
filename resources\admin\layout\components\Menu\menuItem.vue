<template xmlns="">
  <template v-for="subItem in navItem" :key="subItem.key">
    <el-sub-menu v-if="subItem.children && subItem.children.length" :index="subItem.key" :popper-append-to-body="false">
      <template #title>
        <el-icon>
          <img v-if="isImagePath(subItem.icon)" :src="$asset(subItem.icon)" class="menu-icon-img" alt="" />
          <component v-else :is="subItem.icon" />
        </el-icon>
        <span>{{ subItem.nav_name }}</span>
      </template>
      <MenuItem :isCollapse="isCollapse" :navItem="subItem.children" :isSubMenu="true" />
    </el-sub-menu>
    <el-menu-item v-else :index="subItem.key" @click="goPage(subItem)">
      <el-icon>
        <img v-if="isImagePath(subItem.icon)" :src="$asset(subItem.icon)" class="menu-icon-img" alt="" />
        <component v-else :is="subItem.icon" />
      </el-icon>
      <span>{{ subItem.nav_name }}</span>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAppStore } from '/admin/stores/modules/app'

const router = useRouter()
const appStore = useAppStore()

interface NavItem {
  children?: NavItem[]
  icon: string
  key: string
  nav_name: string
  parent?: string
  path: string
  order: number
}

const props = defineProps<{
  navItem: NavItem[]
  isCollapse: boolean
  isSubMenu: boolean
}>()

function goPage(item: NavItem) {
  appStore.setPageName(item.nav_name)
  router.push(item.path)
}

/**
 * 判断是否为图片路径
 * 支持以下格式:
 * 1. 以 / 开头的绝对路径，如 /Vendor/menu_icon/cms_model_5.png
 * 2. 包含图片扩展名的路径，如 icon.png, icon.jpg, icon.jpeg, icon.svg, icon.webp
 */
function isImagePath(icon: string): boolean {
  if (!icon) return false

  // 检查是否以 / 开头
  if (icon.startsWith('/')) return true

  // 检查是否包含常见图片扩展名
  const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp']
  return imageExtensions.some(ext => icon.toLowerCase().includes(ext))
}
</script>

// 使用 defineOptions 来定义组件选项
defineOptions({
  name: 'MenuItem'
})

<style lang="scss">
.el-sub-menu {
  .el-sub-menu__title {
    padding-top: 8px;
    padding-bottom: 8px;
    font-weight: bold;

    span {
      white-space: normal;
      word-break: break-word;
      line-height: 1.2;
      display: inline-block;
      max-width: 130px; /* 根据实际菜单宽度调整 */
    }
  }

  .el-sub-menu,
  .el-menu-item {
    --el-menu-item-font-size: 14px;
  }
}

.el-menu-item {
  padding-top: 8px;
  padding-bottom: 8px;
  font-weight: bold;
  height: auto !important; /* 允许菜单项高度自适应 */
  line-height: 1.2;
  min-height: 40px; /* 设置最小高度 */

  span {
    white-space: normal;
    word-break: break-word;
    display: inline-block;
    max-width: 130px; /* 根据实际菜单宽度调整 */
  }

  .el-sub-menu,
  .el-menu-item {
    --el-menu-item-font-size: 14px;
  }
}

/* 当菜单折叠时的样式 */
.el-menu--collapse {
  .el-sub-menu__title span,
  .el-menu-item span {
    max-width: none;
  }
}

/* 菜单图标图片样式 */
.menu-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
  vertical-align: middle;
}
</style>
