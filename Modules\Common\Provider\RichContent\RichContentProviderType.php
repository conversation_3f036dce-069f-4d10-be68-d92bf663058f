<?php

namespace Modules\Common\Provider\RichContent;

use Bingo\Core\Type\BaseType;

/**
 * Class RichContentProviderType
 * @package Modules\Common\Provider\RichContent
 * @since 1.8.0
 */
class RichContentProviderType implements BaseType
{
    public static function getList(): array
    {
        return array_build(RichContentProvider::all(), function ($k, $v) {
            /** @var $v AbstractRichContentProvider */
            return [$v->name(), $v->title()];
        });
    }

}
