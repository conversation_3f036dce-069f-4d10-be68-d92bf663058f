/*!
 * 
 * ../css/plugins/multiselect.js.css
 * Litepicker v2.0.12 (https://github.com/wakirin/Litepicker)
 * Package: litepicker (https://www.npmjs.com/package/litepicker)
 * License: MIT (https://github.com/wakirin/Litepicker/blob/master/LICENCE.md)
 * Copyright 2019-2021 Rinat G.
 *     
 * Hash: fc3887e0bb19d54c36db
 * 
 */
:root {
  --litepicker-multiselect-is-selected-color-bg: #2196f3;
  --litepicker-multiselect-is-selected-color: #fff;
  --litepicker-multiselect-hover-color-bg: #2196f3;
  --litepicker-multiselect-hover-color: #fff;
}

.litepicker[data-plugins*="multiselect"] .container__days .day-item {
  position: relative;
  z-index: 1;
}

.litepicker[data-plugins*="multiselect"] .container__days .day-item:not(.is-locked):after {
  content: '';
  position: absolute;
  width: 27px;
  height: 27px;
  top: 50%;
  left: 50%;
  z-index: -1;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.litepicker[data-plugins*="multiselect"] .container__days .day-item:not(.is-locked):hover {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: var(--litepicker-day-color);
  font-weight: bold;
}


.litepicker[data-plugins*="multiselect"] .container__days .day-item.is-selected,
.litepicker[data-plugins*="multiselect"] .container__days .day-item.is-selected:hover {
  color: var(--litepicker-multiselect-is-selected-color);
}

.litepicker[data-plugins*="multiselect"] .container__days .day-item.is-selected:after {
  color: var(--litepicker-multiselect-is-selected-color);
  background-color: var(--litepicker-multiselect-is-selected-color-bg);
}

