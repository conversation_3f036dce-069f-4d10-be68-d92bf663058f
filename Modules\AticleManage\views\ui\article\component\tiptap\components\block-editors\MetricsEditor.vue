<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.metricsEditor.contentTab')" name="content">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.metricsEditor.metricsList')">
            <div v-for="(value, index) in values" :key="index" class="metrics-item-edit">
              <div class="metrics-item-header">
                <span>{{ $t('Editor.metricsEditor.metric') }} #{{ index + 1 }}</span>
                <div class="metrics-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    :title="$t('Editor.metricsEditor.edit')"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="values.length > 1"
                    :title="$t('Editor.metricsEditor.delete')"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="metrics-item-content">
                <el-form-item :label="$t('Editor.metricsEditor.value')">
                  <el-input v-model="values[index]" @input="markAsChanged" :placeholder="$t('Editor.metricsEditor.valuePlaceholder')" />
                </el-form-item>
                <el-form-item :label="$t('Editor.metricsEditor.label')">
                  <el-input v-model="labels[index]" @input="markAsChanged" :placeholder="$t('Editor.metricsEditor.labelPlaceholder')" />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">{{ $t('Editor.metricsEditor.addMetric') }}</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane :label="$t('Editor.metricsEditor.styleTab')" name="style">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.metricsEditor.textColor')">
            <el-select v-model="textColor" @change="markAsChanged" style="width: 100%">
              <el-option v-for="(label, value) in textColorOptions" :key="value" :label="$t('Editor.metricsEditor.textColorOptions.' + value)" :value="value" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('Editor.metricsEditor.headingStyle')">
            <el-select v-model="headingStyle" @change="markAsChanged" style="width: 100%">
              <el-option v-for="(label, value) in headingStyleOptions" :key="value" :label="$t('metricsEditor.headingStyleOptions.' + value)" :value="value" />
            </el-select>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.metricsEditor.textAlign')">
            <el-select v-model="textAlign" @change="markAsChanged" style="width: 100%">
              <el-option v-for="(label, value) in textAlignOptions" :key="value" :label="$t('Editor.metricsEditor.textAlignOptions.' + value)" :value="value" />
            </el-select>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.metricsEditor.layout')">
            <el-select v-model="layout" @change="markAsChanged" style="width: 100%">
              <el-option v-for="(label, value) in layoutOptions" :key="value" :label="$t('Editor.metricsEditor.layoutOptions.' + value)" :value="value" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{ $t('Editor.metricsEditor.applyChanges') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const textColorOptions = {
  primary: t('Editor.metricsEditor.textColorOptions.primary'),
  secondary: t('Editor.metricsEditor.textColorOptions.secondary'),
  success: t('Editor.metricsEditor.textColorOptions.success'),
  danger: t('Editor.metricsEditor.textColorOptions.danger'),
  warning: t('Editor.metricsEditor.textColorOptions.warning'),
  info: t('Editor.metricsEditor.textColorOptions.info'),
  dark: t('Editor.metricsEditor.textColorOptions.dark'),
  light: t('Editor.metricsEditor.textColorOptions.light')
}
const headingStyleOptions = {
  'display-1': t('Editor.metricsEditor.headingStyleOptions.display1'),
  'display-2': t('Editor.metricsEditor.headingStyleOptions.display2'),
  'display-3': t('Editor.metricsEditor.headingStyleOptions.display3'),
  'display-4': t('Editor.metricsEditor.headingStyleOptions.display4'),
  'display-5': t('Editor.metricsEditor.headingStyleOptions.display5'),
  'display-6': t('Editor.metricsEditor.headingStyleOptions.display6'),
  h1: t('Editor.metricsEditor.headingStyleOptions.h1'),
  h2: t('Editor.metricsEditor.headingStyleOptions.h2'),
  h3: t('Editor.metricsEditor.headingStyleOptions.h3'),
  h4: t('Editor.metricsEditor.headingStyleOptions.h4'),
  h5: t('Editor.metricsEditor.headingStyleOptions.h5'),
  h6: t('Editor.metricsEditor.headingStyleOptions.h6')
}
const textAlignOptions = {
  start: t('Editor.metricsEditor.textAlignOptions.start'),
  center: t('Editor.metricsEditor.textAlignOptions.center'),
  end: t('Editor.metricsEditor.textAlignOptions.end')
}
const layoutOptions = {
    horizontal: t('Editor.metricsEditor.layoutOptions.horizontal'),
  vertical: t('Editor.metricsEditor.layoutOptions.vertical')
}

// 定义组件名称
defineOptions({
  name: 'MetricsEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的指标索引
const editingIndex = ref(0)

// 原始HTML和结构信息
const originalHtml = ref('')
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  itemClasses: [] as string[],
  attributes: {} as Record<string, string>
})

// 指标数据
const values = ref<string[]>([])
const labels = ref<string[]>([])
const textColor = ref('primary')
const headingStyle = ref('display-4')
const textAlign = ref('center')
const layout = ref('horizontal')

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  values.value = ['15k+', '200%', '300%']
  labels.value = ['Customers of Elevate', 'Daily active users', 'Daily active users']
  textColor.value = 'primary'
  headingStyle.value = 'display-4'
  textAlign.value = 'center'
  layout.value = 'horizontal'
  
  // 重置结构信息
  originalStructure.value = {
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    itemClasses: [],
    attributes: {}
  }
}

/**
 * 从HTML块元素中提取指标数据
 */
const extractMetricsData = (): boolean => {
  if (!props.blockElement) return false
  
  try {
    // 保存原始HTML以备参考
    originalHtml.value = props.blockElement.outerHTML
    
    // 解析DOM结构，提取原始类和属性
    // 1. 找到主容器
    const container = props.blockElement.closest('[data-bs-component="metrics"]') || props.blockElement
    
    if (container) {
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      // 2. 找到行元素
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        // 检查文本对齐方式
        if (row.classList.contains('text-center')) {
          textAlign.value = 'center'
        } else if (row.classList.contains('text-end')) {
          textAlign.value = 'end'
        } else {
          textAlign.value = 'start'
        }
        
        // 推断布局方式
        const cols = row.querySelectorAll('[class*="col-"]')
        if (cols.length > 0) {
          // 检查是否包含col-12，如果没有md或lg等断点类，则为垂直布局
          const firstCol = cols[0]
          if (firstCol.classList.contains('col-12') && !Array.from(firstCol.classList).some(cls => cls.match(/col-(sm|md|lg|xl)-\d+/))) {
            layout.value = 'vertical'
          } else {
            layout.value = 'horizontal'
          }
        }
      }
    }
    
    // 3. 提取所有指标项
    const metricsItems = props.blockElement.querySelectorAll('.metrics-item, .stats-box')
    
    if (metricsItems.length === 0 && props.blockElement.querySelectorAll('[class*="col-"]').length > 0) {
      // 备用方案：直接查找值和标签元素
      const valueElements = props.blockElement.querySelectorAll('.metrics-value, h1, h2, h3, h4, h5, h6, .display-1, .display-2, .display-3, .display-4, .display-5, .display-6')
      const labelElements = props.blockElement.querySelectorAll('.metrics-label, p')
      
      if (valueElements.length > 0) {
        values.value = Array.from(valueElements).map(el => el.textContent?.trim() || '')
        
        // 提取文本颜色和标题样式
        const firstValueEl = valueElements[0]
        if (firstValueEl) {
          // 提取文本颜色
          const colorClass = Array.from(firstValueEl.classList).find(cls => cls.startsWith('text-'))
          if (colorClass) {
            textColor.value = colorClass.replace('text-', '')
          }
          
          // 提取标题样式
          const classArray = Array.from(firstValueEl.classList)
          for (const className of classArray) {
            if (className.startsWith('display-') || /^h[1-6]$/.test(className)) {
              headingStyle.value = className
              break
            }
          }
        }
        
        if (labelElements.length > 0) {
          labels.value = Array.from(labelElements).map(el => el.textContent?.trim() || '')
        } else {
          // 如果没有找到标签元素，为每个值创建一个空标签
          labels.value = new Array(values.value.length).fill('')
        }
      }
    } else {
      // 提取每个指标项的值和标签
      const extractedValues: string[] = []
      const extractedLabels: string[] = []
      
      metricsItems.forEach(item => {
        // 提取值元素文本
        const valueEl = item.querySelector('.metrics-value, h1, h2, h3, h4, h5, h6, .display-1, .display-2, .display-3, .display-4, .display-5, .display-6')
        if (valueEl) {
          extractedValues.push(valueEl.textContent?.trim() || '')
          
          // 提取样式信息（从第一个元素）
          if (extractedValues.length === 1) {
            // 提取文本颜色
            const colorClass = Array.from(valueEl.classList).find(cls => cls.startsWith('text-'))
            if (colorClass) {
              textColor.value = colorClass.replace('text-', '')
            }
            
            // 提取标题样式
            const classArray = Array.from(valueEl.classList)
            for (const className of classArray) {
              if (className.startsWith('display-') || /^h[1-6]$/.test(className)) {
                headingStyle.value = className
                break
              }
            }
          }
        }
        
        // 提取标签元素文本
        const labelEl = item.querySelector('.metrics-label, p')
        extractedLabels.push(labelEl ? labelEl.textContent?.trim() || '' : '')
        
        // 保存第一个项目的类名，用于重建
        if (extractedValues.length === 1) {
          originalStructure.value.itemClasses = Array.from(item.classList)
        }
      })
      
      // 如果提取成功，更新状态
      if (extractedValues.length > 0) {
        values.value = extractedValues
        labels.value = extractedLabels
      }
    }
    
    // 如果没有值，提供默认值
    if (values.value.length === 0) {
      setDefaultValues()
    }
    
    // 确保labels和values数组长度一致
    while (labels.value.length < values.value.length) {
      labels.value.push('')
    }
    
    while (labels.value.length > values.value.length) {
      labels.value.pop()
    }
    
    return true
  } catch (error) {
    console.error('提取指标数据时出错:', error)
    setDefaultValues()
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    editingIndex.value = -1
    
    // 重新提取数据
    const extracted = extractMetricsData()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加指标
const addItem = () => {
  values.value.push('0')
  labels.value.push('新指标')
  editingIndex.value = values.value.length - 1
  markAsChanged()
}

// 编辑指标
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 删除指标
const removeItem = (index: number) => {
  if (values.value.length > 1) {
    values.value.splice(index, 1)
    labels.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

/**
 * 准备指标HTML
 */
const prepareMetricsHTML = (): string => {
  // 获取布局的列类
  let colClass = 'col-md-4'
  if (layout.value === 'vertical') {
    colClass = 'col-12 mb-4'
  } else {
    // 根据指标数量调整列宽
    switch (values.value.length) {
      case 1:
        colClass = 'col-12'
        break
      case 2:
        colClass = 'col-md-6'
        break
      case 3:
        colClass = 'col-md-4'
        break
      case 4:
        colClass = 'col-md-3'
        break
      default:
        colClass = 'col-md-4'
        break
    }
  }
  
  // 文本对齐类
  const alignClass = textAlign.value === 'start' ? '' : `text-${textAlign.value}`
  
  // 生成每个指标项的HTML
  const itemsHtml = values.value.map((value, index) => {
    const label = labels.value[index] || 'Metric'
    return `
      <div class="${colClass}">
        <div class="metrics-item">
          <${headingStyle.value.startsWith('h') ? headingStyle.value : 'h2'} class="metrics-value ${!headingStyle.value.startsWith('h') ? headingStyle.value : ''} fw-bold text-${textColor.value}">${value}</${headingStyle.value.startsWith('h') ? headingStyle.value : 'h2'}>
          <p class="mb-0 metrics-label">${label}</p>
        </div>
      </div>
    `
  }).join('\n')
  
  // 保留原始容器类或使用默认类
  const containerClasses = originalStructure.value.containerClasses
    .filter(c => !c.includes('metrics'))
    .join(' ') || 'container py-5'
  
  // 保留原始行类或使用默认类
  const rowClasses = originalStructure.value.rowClasses
    .filter(c => !c.includes('text-'))
    .join(' ') || 'row justify-content-center g-4'
  
  // 构建自定义属性字符串
  let attributesStr = ''
  Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
    if (key !== 'class' && key !== 'style' && key !== 'data-bs-component') {
      attributesStr += ` ${key}="${value}"`
    }
  })
  
  // 完整HTML模板
  return `
    <div class="${containerClasses}" data-bs-component="metrics"${attributesStr}>
      <div class="${rowClasses} ${alignClass}">
        ${itemsHtml}
      </div>
    </div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareMetricsHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用指标更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.metrics-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.metrics-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.metrics-item-actions {
  display: flex;
  gap: 5px;
}

.metrics-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style>