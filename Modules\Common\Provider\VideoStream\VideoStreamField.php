<?php

namespace Modules\Common\Provider\VideoStream;

use <PERSON>ron\AbstractField;

abstract class VideoStreamField extends AbstractField
{
    public const SCOPE_ADMIN = 'admin';
    public const SCOPE_MEMBER = 'member';

    protected string $view = 'module::Common.View.field.videoStream';
    protected array $value = [
        'driver' => null,
        'name' => null,
        'path' => null,
    ];

    protected function setup(): void
    {
        parent::setup();
        $this->addVariables([
            'scope' => self::SCOPE_ADMIN
        ]);
    }

    public function scope($scope = null)
    {
        if (null == $scope) {
            return $this->getVariable('scope');
        }
        $this->addVariables([
            'scope' => $scope,
        ]);
        return $this;
    }

    public function unserializeValue($value, AbstractField $field)
    {
        $value = json_decode($value, true);
        if (empty($value['driver'])) {
            $value['driver'] = null;
        }
        if (empty($value['name'])) {
            $value['name'] = null;
        }
        if (empty($value['path'])) {
            $value['path'] = null;
        }
        return $value;
    }

    public function serializeValue($value, $model)
    {
        return SerializeUtil::jsonEncode($value, JSON_UNESCAPED_UNICODE);
    }

    public function prepareInput($value, $model)
    {
        return json_decode($value, true);
    }

    public static function register()
    {
        FieldManager::extend('videoStream', VideoStreamField::class);
    }
}
