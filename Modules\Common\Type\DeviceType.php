<?php

namespace Modules\Common\Type;

use Bingo\Core\Type\BaseType;
use Bingo\Core\Util\AgentUtil;

class DeviceType implements BaseType
{
    public const PC = 1;
    public const MOBILE = 2;

    public static function getList()
    {
        return [
            self::PC => 'PC',
            self::MOBILE => '移动端',
        ];
    }

    public static function current()
    {
        if (AgentUtil::isMobile()) {
            return self::MOBILE;
        }
        return self::PC;
    }
}
