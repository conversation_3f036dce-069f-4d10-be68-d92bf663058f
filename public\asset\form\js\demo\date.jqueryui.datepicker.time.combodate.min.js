$(document).ready(function(){$.when($("head").append('<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" type="text/css" />'),$.getScript("//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"),$.getScript("//cdnjs.cloudflare.com/ajax/libs/moment.js/2.8.4/moment.js"),$.getScript("//vitalets.github.io/combodate/combodate.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("form").attr("novalidate",true);$("body").css("padding-bottom","200px");$("input[type=date]").each(function(){$(this).attr("type","text").after($(this).clone().attr("id",this.id+"_alt").attr("name",this.id+"_alt").datepicker({dateFormat:"mm/dd/yy",changeMonth:true,changeYear:true,altField:this,altFormat:"yy-mm-dd"})).hide()});var style=$("<style>.combodate { display: block } .form-control-date { display: inline-block; }</style>");$('input[type="time"]').attr("type","text").append(style).combodate({customClass:"form-control form-control-date",firstItem:"name",format:"HH:mm",template:"hh : mm a",minuteStep:1})})});