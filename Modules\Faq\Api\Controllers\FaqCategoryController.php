<?php

namespace Modules\Faq\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Faq\Models\FaqCategory;

/**
 * 常见问题分类控制器
 * 负责处理常见问题分类的增删改查等操作
 */
class FaqCategoryController extends Controller
{
    /**
     * 获取常见问题分类列表
     * 支持分页、关键字筛选
     *
     * @param Request $request 请求对象
     * @return array 分类列表
     */
    public function index(Request $request): array
    {
        // 获取分页参数，默认每页10条
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $keyword = $request->input('keyword', '');

        // 构建查询
        $query = FaqCategory::query();

        // 关键字筛选
        if (!empty($keyword)) {
            $query->where('name', 'like', '%' . $keyword . '%');
        }

        // 排序：先按sort_order升序，再按id降序
        $query->orderBy('sort_order', 'asc')->orderBy('id', 'desc');

        // 分页获取数据
        $categories = $query->paginate($limit, ['*'], 'page', $page);

        // 返回JSON响应
        return [
            'total' => $categories->total(),
            'page' => $categories->currentPage(),
            'limit' => $categories->perPage(),
            'items' => $categories->items(),
        ];
    }

    /**
     * 新建常见问题分类
     *
     * @param Request $request 请求对象
     * @return array 创建结果
     */
    public function store(Request $request): array
    {
        // 验证请求参数
        $validated = $request->validate([
            'name'       => 'required|string|max:255',
            'status'     => 'required|integer|in:0,1',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id'  => 'nullable|integer|min:0',
            'root_id'    => 'nullable|integer|min:0',
        ]);

        // 创建分类
        $category = FaqCategory::create($validated);

        // 返回JSON响应
        return $category->toArray();
    }

    /**
     * 获取指定ID的常见问题分类详情
     *
     * @param int $id 分类ID
     * @return array 分类详情
     */
    public function show(int $id): array
    {
        // 查询分类
        $category = FaqCategory::find($id);

        if (!$category) {
            // 未找到
            return [];
        }

        // 返回JSON响应
        return $category->toArray();
    }

    /**
     * 更新指定ID的常见问题分类
     *
     * @param Request $request 请求对象
     * @param int $id 分类ID
     * @return array 更新结果
     */
    public function update(Request $request, int $id): array
    {
        // 查询分类
        $category = FaqCategory::find($id);

        if (!$category) {
            return [];
        }

        // 验证请求参数
        $validated = $request->validate([
            'name'       => 'sometimes|required|string|max:255',
            'status'     => 'sometimes|required|integer|in:0,1',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id'  => 'nullable|integer|min:0',
            'root_id'    => 'nullable|integer|min:0',
        ]);

        // 更新分类
        $category->update($validated);

        // 返回JSON响应
        return $category->toArray();
    }

    /**
     * 删除指定ID的常见问题分类（软删除）
     *
     * @param int $id 分类ID
     * @return array 删除结果
     */
    public function destroy(int $id): array
    {
        // 查询分类
        $category = FaqCategory::find($id);

        if (!$category) {
            return [];
        }

        // 软删除
        $category->delete();

        return [];
    }
}
