<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover v-model="filterDialog">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img :src="$asset('Faq/Asset/FilterIcon.png')" alt="FilterIcon" />
            </el-icon>
            <span>篩選</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="區域名稱">
            <el-input v-model="search.name" placeholder="請輸入區域名稱" size="large" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="resetSearch">
              <el-icon size="16"><Refresh /></el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="doSearch" type="primary">
              <el-icon size="16"><Filter /></el-icon>
              <span>篩選</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" class="add-btn" @click="openAddDialog">
        <el-icon><Plus /></el-icon>
        新增區域
      </el-button>
          </div>

    <!-- 表格区域 -->
    <div class="module-con">
      <div class="box">
        <el-table :data="filteredAreaList" style="width: 100%" v-loading="loading">
          <template #empty>
            <el-empty description="暫無數據" image-size="100px" />
          </template>
          <el-table-column prop="id" label="序號" width="80" />
          <el-table-column prop="name" label="區域名稱" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="區域描述" min-width="180" />
          <el-table-column prop="channelCount" label="關聯頻道" width="150">
            <template #default="scope">
              <span>{{ scope.row.channelCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" label="創建日期" width="180" />
          <el-table-column prop="status" label="狀態" width="100">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.status === '已啟用'">已啟用</el-tag>
              <el-tag type="danger" v-else>已停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editArea(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/EditIcon.png')" alt="" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="deleteArea(scope.row)">
                  <el-icon>
                    <img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每頁顯示</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  暫無數據
                </div>
              </template>
            </el-select>
            <span class="total-text">共 {{ total }} 條記錄</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增对话框 -->
    <el-dialog class="el-dialog-common-cls" v-model="addDialogVisible" :title="dialogTitle" width="500">
      <el-form label-position="top" :model="addForm" :rules="formRules" ref="addFormRef">
        <el-form-item label="區域名稱" prop="name">
          <el-input v-model="addForm.name" placeholder="請輸入區域名稱" />
        </el-form-item>
        <el-form-item label="區域描述" prop="description">
          <el-input 
            v-model="addForm.description" 
            placeholder="請輸入區域描述" 
            type="textarea" 
            :rows="3"
            resize="vertical"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd">確定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Edit, Delete, Search, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '/admin/support/http'

const router = useRouter()

// 搜索相关
const filterDialog = ref(false)
const search = reactive({ name: '' })
const loading = ref(false)

// 区域数据
const areaList = ref([
  { 
    id: 1, 
    name: '突發', 
    description: '突發新聞專區', 
    channelCount: 3, 
    createDate: '2023-01-15', 
    status: '已啟用' 
  },
  { 
    id: 2, 
    name: '港澳', 
    description: '港澳地區新聞', 
    channelCount: 0, 
    createDate: '2023-02-20', 
    status: '已啟用' 
  },
  { 
    id: 3, 
    name: '國際', 
    description: '國際新聞報導', 
    channelCount: 3, 
    createDate: '2023-03-10', 
    status: '已啟用' 
  },
  { 
    id: 4, 
    name: '財經', 
    description: '財經資訊與分析', 
    channelCount: 0, 
    createDate: '2023-04-01', 
    status: '已停用' 
  },
  { 
    id: 5, 
    name: '體育', 
    description: '體育賽事與新聞', 
    channelCount: 3, 
    createDate: '2023-05-05', 
    status: '已啟用' 
  }
])

// 过滤后的区域列表
const filteredAreaList = computed(() => {
  let arr = areaList.value
  if (search.name) {
    arr = arr.filter(area => 
      area.name.includes(search.name) || 
      area.description.includes(search.name)
    )
  }
  return arr
})

// 对话框相关
const addDialogVisible = ref(false)
const addForm = reactive({ 
  name: '', 
  description: ''
})
const dialogTitle = ref('新增區域')
const editIndex = ref(-1)
const addFormRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '請輸入區域名稱', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '請輸入區域描述', trigger: 'blur' }
  ]
}

// 分页相关
const page = ref(1)
const limit = ref(20)
const total = ref(5)

// 搜索相关函数
function resetSearch() {
  search.name = ''
}

function doSearch() {
  filterDialog.value = false
}

// 打开新增对话框
const openAddDialog = () => {
  addForm.name = ''
  addForm.description = ''
  dialogTitle.value = '新增區域'
  editIndex.value = -1
  addDialogVisible.value = true
}

// 提交新增/编辑
const submitAdd = async () => {
  try {
    await addFormRef.value.validate()
    
    if (editIndex.value === -1) {
      // 新增
      const response = await http.post('/admin/region', {
        name: addForm.name,
        description: addForm.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success('新增成功')
        getAreaList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '新增失敗')
      }
    } else {
      // 编辑
      const row = areaList.value[editIndex.value]
      const response = await http.put(`/admin/region/${row.id}`, {
        name: addForm.name,
        description: addForm.description
      })
      
      if (response.data && response.data.code === 200) {
        ElMessage.success('編輯成功')
        getAreaList()
        addDialogVisible.value = false
      } else {
        ElMessage.error(response.data?.message || '編輯失敗')
      }
    }
  } catch (error) {
    if (error === false) {
      ElMessage.error('請檢查表單輸入')
    } else {
      ElMessage.error('操作失敗')
    }
  }
}

// 编辑区域
const editArea = (row: any) => {
  router.push(`/region/edit/${row.id}`)
}

// 删除区域
const deleteArea = (row: any) => {
  ElMessageBox.confirm(
    `確定要刪除區域「${row.name}」嗎？此操作不可恢復！`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await http.delete(`/admin/region/${row.id}`)
      if (response.data && response.data.code === 200) {
        ElMessage.success('刪除成功')
        getAreaList()
      } else {
        ElMessage.error(response.data?.message || '刪除失敗')
      }
    } catch (error) {
      ElMessage.error('刪除失敗')
    }
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}

// 分页处理
const changePage = () => {
  getAreaList()
}

// 获取区域列表
const getAreaList = async () => {
  loading.value = true
  try {
    const params: any = {
      page: page.value,
      limit: limit.value
    }
    
    if (search.name) {
      params.keyword = search.name
    }

    const response = await http.get('/admin/region', params)
    
    if (response.data && response.data.code === 200) {
      areaList.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data?.message || '獲取數據失敗')
      areaList.value = []
      total.value = 0
    }
  } catch (error) {
    areaList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 页面初始化
// onMounted(() => {
//   getAreaList()
// })
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

</style>
