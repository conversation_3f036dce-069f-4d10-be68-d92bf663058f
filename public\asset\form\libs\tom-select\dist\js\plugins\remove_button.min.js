(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.remove_button=factory())})(this,function(){"use strict";const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const getDom=query=>{if(query.jquery){return query[0]}if(query instanceof HTMLElement){return query}if(isHtmlString(query)){var tpl=document.createElement("template");tpl.innerHTML=query.trim();return tpl.content.firstChild}return document.querySelector(query)};const isHtmlString=arg=>{if(typeof arg==="string"&&arg.indexOf("<")>-1){return true}return false};const escape_html=str=>{return(str+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")};const preventDefault=(evt,stop=false)=>{if(evt){evt.preventDefault();if(stop){evt.stopPropagation()}}};const addEvent=(target,type,callback,options)=>{target.addEventListener(type,callback,options)};function plugin(userOptions){const options=Object.assign({label:"&times;",title:"Remove",className:"remove",append:true},userOptions);var self=this;if(!options.append){return}var html='<a href="javascript:void(0)" class="'+options.className+'" tabindex="-1" title="'+escape_html(options.title)+'">'+options.label+"</a>";self.hook("after","setupTemplates",()=>{var orig_render_item=self.settings.render.item;self.settings.render.item=(data,escape)=>{var item=getDom(orig_render_item.call(self,data,escape));var close_button=getDom(html);item.appendChild(close_button);addEvent(close_button,"mousedown",evt=>{preventDefault(evt,true)});addEvent(close_button,"click",evt=>{preventDefault(evt,true);if(self.isLocked)return;if(!self.shouldDelete([item],evt))return;self.removeItem(item);self.refreshOptions(false);self.inputState()});return item}})}return plugin});