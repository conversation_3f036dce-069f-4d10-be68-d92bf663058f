<?php

namespace Modules\Common\Util;

use Bingo\Core\Util\TreeUtil;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

class FilterUtil
{
    private static function categoryGet($categories, $id)
    {
        foreach ($categories as $category) {
            if ($category['id'] == $id) {
                return $category;
            }
        }

    }

    public static function categoryTreeFilter($categoryId, $categories, $keyTitle = 'title', $keyPid = 'pid'): array
    {
        $filterText = [];
        $pageTitle = [];
        $category = null;
        if ($categoryId) {
            $category = self::categoryGet($categories, $categoryId);
            BizException::throwsIfEmpty($category, Code::FAILED, '分类不存在');
            $filterText[] = $category[$keyTitle];
            $pageTitle[] = $category[$keyTitle];
            if ($category[$keyPid]) {
                $parentCategory = self::categoryGet($categories, $category[$keyPid]);
                $pageTitle[] = $parentCategory[$keyTitle];
            }
        }
        $categoryIds = [
            $categoryId
        ];
        $childrenIds = TreeUtil::nodesChildrenIds($categories, $categoryId);
        $categoryIds = array_merge($categoryIds, $childrenIds);
        $categoryChain = TreeUtil::nodesChainWithItems($categories, $categoryId);
        return [
            $category,
            $categoryIds,
            $categoryChain,
            $filterText,
            $pageTitle,
        ];
    }
}
