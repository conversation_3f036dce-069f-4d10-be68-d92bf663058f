<?php

declare(strict_types=1);

namespace Modules\Common\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MultiSiteSyncRequested
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public readonly string $module,
        public readonly string $dataType,
        public readonly int $dataId,
        public readonly array $data,
        public readonly int $sourceSiteId,
        public readonly array $targetSiteIds,
        public readonly string $callbackClass,
        public readonly string $callbackMethod,
        public readonly array $options = []
    ) {}
} 