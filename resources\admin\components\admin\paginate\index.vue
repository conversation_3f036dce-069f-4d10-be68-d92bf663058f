<template>
    <div class="flex justify-end pt-5 paginate-box">
        <el-pagination background :layout="layout" :current-page="page" :page-size="limit" @current-change="changePage" @size-change="changeLimit" :total="parseInt(total)" :page-sizes="pageSizes" />
    </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue'

// const layout = 'total,sizes,prev, pager,next'
const layout = 'prev, pager, next'

const pageSizes = [10, 20, 30, 50]

interface paginate {
    page: number
    limit: number
    total: string
    changePage: number
    changeLimit: number
}

const { page, limit, total, changePage, changeLimit } = inject('paginate') as paginate
</script>

<style scoped lang="scss">
:deep(.el-pagination) {
    --el-color-primary: #ffd100;
    --el-color-white: var(--el-button-text-color);

    .el-pager {
        .number {
            border: 1px solid transparent;
            transition: border 0.35s ease-in-out;

            &.is-active {
                border-color: var(--el-color-primary);
            }
        }
    }
}
</style>
