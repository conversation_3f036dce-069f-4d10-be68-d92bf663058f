export const timelineTemplate = `<div data-bs-component="timeline" class="timeline-block responsive-block">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="main-timeline">
          <!-- 时间线项目 1 -->
          <div class="timeline">
            <div class="timeline-icon">
              <i class="fas fa-rocket"></i>
            </div>
            <div class="timeline-content">
              <span class="date">2023年1月</span>
              <h3 class="title">项目启动</h3>
              <p class="description">
                这是我们项目的起点。在这个阶段，我们确定了项目的范围、目标和关键里程碑。团队成员各司其职，为项目的成功奠定了基础。
              </p>
            </div>
          </div>
          
          <!-- 时间线项目 2 -->
          <div class="timeline">
            <div class="timeline-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <div class="timeline-content">
              <span class="date">2023年3月</span>
              <h3 class="title">开发阶段</h3>
              <p class="description">
                经过两个月的紧张开发，我们完成了核心功能的实现。团队通过敏捷开发方法，确保了项目的质量和进度按计划推进。
              </p>
            </div>
          </div>
          
          <!-- 时间线项目 3 -->
          <div class="timeline">
            <div class="timeline-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="timeline-content">
              <span class="date">2023年6月</span>
              <h3 class="title">测试与优化</h3>
              <p class="description">
                进入测试阶段，我们识别并解决了许多潜在问题。用户反馈帮助我们优化了产品体验，使其更加符合客户需求。
              </p>
            </div>
          </div>
          
          <!-- 时间线项目 4 -->
          <div class="timeline">
            <div class="timeline-icon">
              <i class="fas fa-flag"></i>
            </div>
            <div class="timeline-content">
              <span class="date">2023年9月</span>
              <h3 class="title">正式发布</h3>
              <p class="description">
                历经9个月的努力，我们的产品终于成功发布。市场反应非常积极，用户数量持续增长，为公司带来了可观的收益。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <style>
  .timeline-block {
    padding: 60px 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .main-timeline {
    position: relative;
    width: 100%;
    margin: 0 auto;
    padding: 20px 45px;
    overflow: hidden;
    clear: both;
  }
  
  .main-timeline:before {
    content: "";
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #f0f0f0, #6c5ce7, #f0f0f0);
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 3px;
    box-shadow: 0 0 10px rgba(108, 92, 231, 0.2);
    z-index: 1;
  }
  
  .main-timeline:after {
    content: "";
    display: table;
    clear: both;
  }
  
  .timeline {
    position: relative;
    margin-bottom: 60px;
    transition: all 0.4s ease;
  }
  
  .timeline:last-child {
    margin-bottom: 0;
  }
  
  .timeline:nth-child(even) {
    float: right;
  }
  
  .timeline:nth-child(odd) {
    float: left;
  }
  
  .timeline:nth-child(even),
  .timeline:nth-child(odd) {
    width: 50%;
    clear: both;
  }
  
  .timeline:nth-child(even):before,
  .timeline:nth-child(odd):before {
    content: "";
    width: 12px;
    height: 12px;
    background: #fff;
    border: 3px solid #6c5ce7;
    position: absolute;
    border-radius: 50%;
    top: 42px;
    right: -9px;
    z-index: 2;
    box-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
    transition: all 0.3s ease;
  }
  
  .timeline:nth-child(odd):before {
    right: auto;
    left: -3px;
  }
  
  .timeline-icon {
    width: 70px;
    height: 70px;
    line-height: 65px;
    text-align: center;
    border: 5px solid #6c5ce7;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 10px;
    right: -36px;
    z-index: 3;
    box-shadow: 0 0 15px rgba(108, 92, 231, 0.3);
    transition: all 0.3s ease;
  }
  
  .timeline:nth-child(odd) .timeline-icon {
    right: auto;
    left: -36px;
  }
  
  .timeline-icon i {
    font-size: 30px;
    color: #6c5ce7;
    transition: all 0.3s ease;
  }
  
  .timeline-content {
    padding: 25px 30px;
    background: #f8f9fa;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    border-radius: 10px;
    margin-right: 40px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .timeline:nth-child(odd) .timeline-content {
    margin-right: 0;
    margin-left: 40px;
    text-align: right;
  }
  
  .timeline-content:after {
    content: "";
    display: block;
    border-style: solid;
    border-width: 15px 0 15px 15px;
    border-color: transparent transparent transparent #f8f9fa;
    position: absolute;
    top: 25px;
    right: -15px;
    transition: all 0.3s ease;
  }
  
  .timeline:nth-child(odd) .timeline-content:after {
    border-width: 15px 15px 15px 0;
    border-color: transparent #f8f9fa transparent transparent;
    right: auto;
    left: -15px;
  }
  
  .timeline .date {
    display: inline-block;
    padding: 6px 14px;
    background: #6c5ce7;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    border-radius: 20px;
    margin-bottom: 12px;
    box-shadow: 0 3px 8px rgba(108, 92, 231, 0.3);
    transition: all 0.3s ease;
  }
  
  .timeline .title {
    margin-top: 0;
    font-size: 22px;
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
    position: relative;
    transition: all 0.3s ease;
  }
  
  .timeline .description {
    font-size: 16px;
    color: #555;
    line-height: 1.7;
    margin-bottom: 0;
    transition: all 0.3s ease;
  }
  
  /* 悬停效果 */
  .timeline:hover .timeline-content {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
  }
  
  .timeline:hover .timeline-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(108, 92, 231, 0.5);
  }
  
  .timeline:hover .timeline-icon i {
    transform: rotate(360deg);
    transition: transform 0.8s ease;
  }
  
  .timeline:hover .date {
    background: #5649c0;
    box-shadow: 0 5px 10px rgba(108, 92, 231, 0.4);
  }
  
  .timeline:hover:nth-child(even):before,
  .timeline:hover:nth-child(odd):before {
    background: #6c5ce7;
    transform: scale(1.2);
  }
  
  /* 移动端样式 */
  @media screen and (max-width: 767.98px) {
    .timeline-block {
      padding: 30px 0;
    }
    
    .main-timeline {
      padding: 10px 0 !important;
    }
    
    .main-timeline:before {
      left: 20px;
    }
    
    .timeline:nth-child(even),
    .timeline:nth-child(odd) {
      width: 100%;
      padding-left: 60px;
      padding-right: 0;
      margin-bottom: 40px;
    }
    
    .timeline:nth-child(even):before,
    .timeline:nth-child(odd):before {
      left: 16px;
      right: auto;
    }
    
    .timeline-icon,
    .timeline:nth-child(odd) .timeline-icon {
      left: 0;
      right: auto;
      width: 60px;
      height: 60px;
      line-height: 55px;
    }
    
    .timeline-icon i {
      font-size: 24px;
    }
    
    .timeline-content,
    .timeline:nth-child(odd) .timeline-content {
      margin-left: 15px;
      margin-right: 0;
      text-align: left;
      padding: 20px;
    }
    
    .timeline-content:after,
    .timeline:nth-child(odd) .timeline-content:after {
      border-width: 12px 12px 12px 0;
      border-color: transparent #f8f9fa transparent transparent;
      left: -12px;
      right: auto;
      top: 20px;
    }
    
    .timeline .title {
      font-size: 18px;
      margin-bottom: 10px;
    }
    
    .timeline .description {
      font-size: 14px;
      line-height: 1.5;
    }
    
    .timeline .date {
      font-size: 12px;
      padding: 4px 10px;
    }
  }
  
  /* 平板端样式 */
  @media screen and (min-width: 768px) and (max-width: 991.98px) {
    .timeline-block {
      padding: 40px 0;
    }
    
    .timeline {
      margin-bottom: 50px;
    }
    
    .main-timeline:before {
      left: 30px;
    }
    
    .timeline:nth-child(even),
    .timeline:nth-child(odd) {
      width: 100%;
      padding-left: 70px;
      padding-right: 20px;
      margin-bottom: 40px;
      clear: both;
      float: none;
    }
    
    .timeline:nth-child(even):before,
    .timeline:nth-child(odd):before {
      left: 26px;
      right: auto;
    }
    
    .timeline-icon,
    .timeline:nth-child(odd) .timeline-icon {
      left: 10px;
      right: auto;
      width: 60px;
      height: 60px;
      line-height: 55px;
    }
    
    .timeline-content,
    .timeline:nth-child(odd) .timeline-content {
      margin-left: 0;
      margin-right: 0;
      text-align: left;
      padding: 22px 25px;
      width: 100%;
    }
    
    .timeline-content:after,
    .timeline:nth-child(odd) .timeline-content:after {
      border-width: 12px 12px 12px 0;
      border-color: transparent #f8f9fa transparent transparent;
      left: -12px;
      right: auto;
      top: 20px;
    }
    
    .timeline .title {
      font-size: 20px;
    }
    
    .timeline .description {
      font-size: 15px;
    }
    
    .timeline:last-child {
      margin-bottom: 0;
    }
    
    /* 清除浮动问题 */
    .main-timeline:after {
      content: "";
      display: table;
      clear: both;
    }
  }
  
  /* 桌面端样式 */
  @media screen and (min-width: 992px) {
    .timeline-block {
      padding: 60px 0;
    }
    
    .timeline-content {
      padding: 25px 30px;
    }
    
    .timeline .title {
      font-size: 22px;
    }
    
    .timeline .description {
      font-size: 16px;
    }
  }
  
  /* 移动端预览模式样式 */
  .mobile-preview .timeline-block {
    padding: 30px 0;
  }
  
  .mobile-preview .main-timeline:before {
    left: 20px;
  
  }
  .mobile-preview .main-timeline{
    padding: 20px 0 !important;
  }
  
  .mobile-preview .timeline:nth-child(even),
  .mobile-preview .timeline:nth-child(odd) {
    width: 100%;
    padding-left: 60px;
    padding-right: 0;
    margin-bottom: 40px;
    clear: both;
    float: none;
  }
  
  .mobile-preview .timeline:nth-child(even):before,
  .mobile-preview .timeline:nth-child(odd):before {
    left: 16px;
    right: auto;
  }
  
  .mobile-preview .timeline-icon,
  .mobile-preview .timeline:nth-child(odd) .timeline-icon {
    left: 0;
    right: auto;
    width: 60px;
    height: 60px;
    line-height: 55px;
  }
  
  .mobile-preview .timeline-content,
  .mobile-preview .timeline:nth-child(odd) .timeline-content {
    margin-left: 15px;
    margin-right: 0;
    text-align: left;
    width: calc(100% - 30px);
    box-sizing: border-box;
  }
  
  .mobile-preview .timeline-content:after,
  .mobile-preview .timeline:nth-child(odd) .timeline-content:after {
    border-width: 12px 12px 12px 0;
    border-color: transparent #f8f9fa transparent transparent;
    left: -12px;
    right: auto;
  }
  
  .mobile-preview .main-timeline:after {
    content: "";
    display: table;
    clear: both;
  }
  
  .mobile-preview .timeline:last-child {
    margin-bottom: 0;
  }
  
  /* 桌面预览模式样式 */
  .desktop-preview .timeline-block {
    padding: 60px 0;
  }
  
  .desktop-preview .main-timeline:before {
    left: 50%;
  }
  
  .desktop-preview .timeline:nth-child(even) {
    float: right;
    width: 50%;
    padding-left: 0;
  }
  
  .desktop-preview .timeline:nth-child(odd) {
    float: left;
    width: 50%;
    padding-right: 0;
  }
  
  .desktop-preview .timeline:nth-child(even):before {
    right: -9px;
    left: auto;
  }
  
  .desktop-preview .timeline:nth-child(odd):before {
    left: -3px;
    right: auto;
  }
  
  .desktop-preview .timeline:nth-child(even) .timeline-icon {
    right: -36px;
    left: auto;
  }
  
  .desktop-preview .timeline:nth-child(odd) .timeline-icon {
    left: -36px;
    right: auto;
  }
  
  .desktop-preview .timeline:nth-child(even) .timeline-content {
    margin-right: 40px;
    margin-left: 0;
    text-align: left;
  }
  
  .desktop-preview .timeline:nth-child(odd) .timeline-content {
    margin-left: 40px;
    margin-right: 0;
    text-align: right;
  }
  
  .desktop-preview .timeline:nth-child(even) .timeline-content:after {
    border-width: 15px 0 15px 15px;
    border-color: transparent transparent transparent #f8f9fa;
    right: -15px;
    left: auto;
  }
  
  .desktop-preview .timeline:nth-child(odd) .timeline-content:after {
    border-width: 15px 15px 15px 0;
    border-color: transparent #f8f9fa transparent transparent;
    left: -15px;
    right: auto;
  }
  
  /* 容器大小响应式 */
  .container-sm .main-timeline {
    max-width: 540px;
  }
  
  .container-md .main-timeline {
    max-width: 720px;
  }
  
  .container-lg .main-timeline {
    max-width: 960px;
  }
  
  .container-xl .main-timeline {
    max-width: 1140px;
  }
  
  .container-xxl .main-timeline {
    max-width: 1320px;
  }
  
  /* 清除容器和内容中可能存在的浮动问题 */
  .container:after,
  .row:after,
  .col-12:after {
    content: "";
    display: table;
    clear: both;
  }
  
  /* 确保内容正确包裹和显示 */
  .timeline-block .container {
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  </style>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <script>
  document.addEventListener("DOMContentLoaded", function() {
    const timeline = document.querySelector('.main-timeline');
    const timelineItems = document.querySelectorAll('.timeline');
    const timelineBlock = document.querySelector('.timeline-block');
    
    // 监听滚动事件，实现渐入效果
    function checkScroll() {
      timelineItems.forEach((item, index) => {
        const itemTop = item.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        if (itemTop < windowHeight * 0.8) {
          setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
          }, index * 150); // 依次显示各项，产生连续动画效果
        }
      });
    }
    
    // 初始化样式
    timelineItems.forEach(item => {
      item.style.opacity = '0';
      item.style.transform = 'translateY(30px)';
      item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    });
    
    // 响应式调整
    function handleResize() {
      const windowWidth = window.innerWidth;
      const isMobile = windowWidth < 768;
      const isTablet = windowWidth >= 768 && windowWidth < 992;
      
      // 清除所有可能影响布局的类
      timeline.classList.remove('mobile-view', 'tablet-view');
      
      if (isMobile) {
        timeline.classList.add('mobile-view');
      } else if (isTablet) {
        timeline.classList.add('tablet-view');
        
        // 确保在平板模式下清除可能影响布局的浮动
        timelineItems.forEach(item => {
          item.style.float = 'none';
          item.style.clear = 'both';
        });
      }
      
      // 强制重新计算布局
      timelineBlock.style.display = 'none';
      setTimeout(() => {
        timelineBlock.style.display = '';
        // 确保所有项目可见性
        setTimeout(() => {
          checkScroll();
        }, 100);
      }, 10);
    }
    
    // 初始检查
    handleResize();
    checkScroll();
    
    // 监听事件
    window.addEventListener('scroll', checkScroll);
    window.addEventListener('resize', handleResize);
    
    // 添加交互效果
    timelineItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        this.style.zIndex = '10';
      });
      
      item.addEventListener('mouseleave', function() {
        this.style.zIndex = '1';
      });
    });
  });
  </script>
</div>` 