<?php

namespace Modules\Common\Provider\SiteTemplate;

/**
 * 模板提供者，所有模板都需要在这里注册
 */
class SiteTemplateProvider
{
    /**
     * @var AbstractSiteTemplateProvider[]
     */
    private static array $instances = [
        DefaultSiteTemplateProvider::class,
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    public static function registerQuick($name, $title, $root = null): void
    {
        self::register(QuickSiteTemplateProvider::make($name, $title, $root));
    }

    /**
     * @return AbstractSiteTemplateProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    /**
     * @param $name
     * @return AbstractSiteTemplateProvider|null
     */
    public static function get($name): ?AbstractSiteTemplateProvider
    {
        foreach (self::all() as $provider) {
            if ($provider->name() == $name) {
                return $provider;
            }
        }
        return null;
    }

    public static function map(): array
    {
        return array_build(self::all(), function ($k, $v) {
            /** @var $v AbstractSiteTemplateProvider */
            return [$v->name(), $v->title()];
        });
    }
}
