@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .white-box {
    margin-bottom: 30px;
    padding: 30px 50px;
    background-color: #fff;

    h1 {
      margin-bottom: 10px;
      font-size: 28px;
      font-weight: bold;
      line-height: 1.32;
    }
  
    .case-info {
      margin-bottom: 20px;
      border-bottom: 1px solid #F7F7F7;
      padding-bottom: 20px;
      .df(center);

      span {
        margin-right: 10px;
        display: block;
        font-size: 14px;
        color: #888;
      }
    }
  
    .big {
      position: relative;

      .swiper-slide {
        max-height: 520px;
        text-align: center;

        .img {
          max-width: 100%;
          max-height: 100%;
        }
      }

      .iconfont {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        border-radius: 50%;
        width: 46px;
        height: 46px;
        background-color: rgba(0, 0, 0, 0.35);
        color: #fff;
        .df(center, center);

        &.icon-arrow-right {
          right: 0;
        }

        &.icon-arrow-left {
          left: 0;
        }
      }
    }
  
    .small-pic {
      margin: 20px 0 20px;
      .df(center, center);

      .img {
        padding: 0 5px;
        width: 12.5%;
        cursor: pointer;
        position: relative;

        img {
          width: 100%;
        }

        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          right: 0;
          background-color: rgba(255, 255, 255, .3);
          transition: background-color .35s ease-in-out;
          z-index: 1;
        }

        &:hover,
        &.active {
          &::after {
            background-color: transparent;
          }
        }
      }
    }
  
    p {
      margin-bottom: 10px;
      font-size: 16px;
      line-height: 1.75;
    }
  }

  .btn-list {
    .df();

    margin-bottom: 50px;
    border: 1px solid #eee;
    background-color: #fff;

    .btn-box {
      padding: 0 50px;
      border-right: 1px solid #eee;
      font-size: 16px;
      line-height: 4.375;
      color: #888;

      .df(center, center);

      .iconfont {
        display: block;
        margin-right: 10px;
      }

      &:last-child {
        border-right: none;
      }

      &.prev,
      &.next {
        flex-grow: 1;
      }

      &[href] {
        color: #383838;
      }
    }
  }
}