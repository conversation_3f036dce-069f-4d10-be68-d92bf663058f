<template>
  <div class="common-layout">
    <el-container class="h-screen">
      <el-aside>
        <userInfoMenu></userInfoMenu>
      </el-aside>
      <el-main>
        <breadcrumbs></breadcrumbs>
        <router-view></router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import userInfoMenu from './components/Menu/userInfoMenu.vue'
import Breadcrumbs from '/admin/components/breadcrumbs/index.vue'
</script>

<style lang="scss">
body {
  font-family: 'Inter', 'Microsoft JhengHei';
}

.iam-page {
  padding: 14px 0 14px 18px;
  position: relative;
  z-index: 1;
  background-color: #fff;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: -2;
    background: transparent linear-gradient(69deg, #0e85e5 0%, #c1e4ff 41%, #c2cb6b 57%, #eecf1e 88%, #ffd101 100%) 0% 0% no-repeat padding-box;
    opacity: 0.64;
  }

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: -1;
    opacity: 0.64;
    backdrop-filter: blur(8px);
    background-color: #fff;
  }

  .el-aside {
    --el-aside-width: 264px;
  }

  .el-main {
    --el-main-padding: 0;

    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-button {
      display: none;
    }

    &::-webkit-scrollbar-corner {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgb(0, 126, 229, 0.3);
      border-radius: 5px;
      background-clip: content-box;
    }
  }
}

</style>
