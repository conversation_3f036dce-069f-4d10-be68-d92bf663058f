@import 'element-plus/theme-chalk/dark/css-vars.css';

html.dark {
  background-color: #161d31;
  // 侧边栏背景色
  --sider-bg-color: #283046;
  // header logo 文字颜色
  --header-logo-text-color: #ffffff;
  // 侧边栏菜单的文字颜色
  --sider-menu-text-color: #ffffff;
  // sub menu bg color
  --sider-sub-menu-bg-color: #161d31;
  // 侧边栏子菜单 hover 的颜色
  --sider-sub-menu-hover-bg-color: #343d55;
  // 激活文字颜色
  --sider-ment-active-text-color: var(--el-color-primary);
  // 激活时背景色
  --side-active-menu-bg-color: rgba(255, 255, 255, 0.08);

  /* 自定义深色背景颜色 */
  --el-bg-color: var(--sider-sub-menu-hover-bg-color);
  --el-fill-color-blank: var(--sider-bg-color);
  --el-bg-color-overlay: var(--sider-bg-color);
  --header-bg-color: var(--sider-bg-color);

  // border color
  --el-border-color-lighter: #3b4253;
  --el-fill-color-light: #161d31;
  --el-border-color-light: #E2E2E2;

  // side sub menu margin
  --sider-sub-menu-bg-margin: 0px 0.05rem;

  --el-mask-color: transparent;
}
