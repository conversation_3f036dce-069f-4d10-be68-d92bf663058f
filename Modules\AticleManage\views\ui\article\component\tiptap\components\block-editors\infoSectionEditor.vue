<template>
    <div class="edit-section">
      <el-tabs v-model="activeTab">
        <el-tab-pane :label="$t('Editor.infoSectionEditor.contentTab')" name="content">
          <el-form label-position="top">
            <el-form-item :label="$t('Editor.infoSectionEditor.title')">
              <el-input v-model="title" @input="markAsChanged" />
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.subtitle')">
              <el-input v-model="subtitle" @input="markAsChanged" />
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.description')">
              <el-input type="textarea" v-model="description" :rows="4" @input="markAsChanged" />
            </el-form-item>
            
            <el-divider>{{ $t('Editor.infoSectionEditor.imageSettings') }}</el-divider>
            
            <!-- 图片预览和选择区域 -->
            <div class="image-preview-container">
              <div v-if="imageUrl" class="image-preview">
                <img :src="imageUrl" class="preview-img" />
              </div>
              <div v-else class="image-placeholder">
                <el-icon class="placeholder-icon"><Picture /></el-icon>
                <div class="placeholder-text">{{ $t('infoSectionEditor.noImage') }}</div>
              </div>
            </div>

            <!-- 图片操作按钮 -->
            <div class="button-group">
              <el-button type="primary" class="button-no-border" @click="openFileManager">
                <el-icon class="icon"><Upload /></el-icon>
                <span>{{ $t('Editor.infoSectionEditor.selectImage') }}</span>
              </el-button>
              <el-button v-if="imageUrl" class="delete-btn" @click="confirmDeleteImage">
                <el-icon class="icon"><Delete /></el-icon>
                <span>{{ $t('Editor.infoSectionEditor.deleteImage') }}</span>
              </el-button>
            </div>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.imagePosition')">
              <el-radio-group v-model="imagePosition" @change="markAsChanged">
                <el-radio-button v-for="(label, value) in imagePositionOptions" :key="value" :label="value">{{ $t('Editor.infoSectionEditor.imagePositionOptions.' + value) }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            
            <el-divider>{{ $t('Editor.infoSectionEditor.buttonSettings') }}</el-divider>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.buttonText')">
              <el-input v-model="buttonText" @input="markAsChanged" :placeholder="$t('infoSectionEditor.buttonTextPlaceholder')" />
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.buttonUrl')" v-if="buttonText.trim()">
              <el-input v-model="buttonUrl" @input="markAsChanged" :placeholder="$t('infoSectionEditor.buttonUrlPlaceholder')" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane :label="$t('Editor.infoSectionEditor.styleTab')" name="style">
          <el-form label-position="top">
            <el-form-item :label="$t('Editor.infoSectionEditor.backgroundColor')">
              <el-color-picker v-model="backgroundColor" @change="markAsChanged" show-alpha />
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.textColor')">
              <el-color-picker v-model="textColor" @change="markAsChanged" show-alpha />
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.variant')">
              <el-select v-model="variant" @change="markAsChanged" style="width: 100%">
                <el-option v-for="(label, value) in variantOptions" :key="value" :label="$t('infoSectionEditor.variantOptions.' + value)" :value="value" />
              </el-select>
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.buttonType')">
              <el-select v-model="buttonType" @change="markAsChanged" style="width: 100%">
                <el-option v-for="(label, value) in buttonTypeOptions" :key="value" :label="$t('infoSectionEditor.buttonTypeOptions.' + value)" :value="value" />
              </el-select>
            </el-form-item>
            
            <el-form-item :label="$t('Editor.infoSectionEditor.contentAlignment')">
              <el-radio-group v-model="contentAlignment" @change="markAsChanged">
                <el-radio-button v-for="(label, value) in contentAlignmentOptions" :key="value" :label="value">{{ $t('infoSectionEditor.contentAlignmentOptions.' + value) }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
  
      <!-- 应用按钮，只在有更改时显示 -->
      <div v-if="isChanged" class="apply-button-container">
        <el-button type="primary" @click="applyChanges">{{ $t('Editor.infoSectionEditor.applyChanges') }}</el-button>
      </div>

      <!-- 文件管理器弹窗 -->
      <DocumentsManager 
        :BaseUrl="baseUrl" 
        :token="token" 
        :isMultiSelect="false" 
        :locale="localeLang"
        @confirmSelection="confirmSelection" 
        ref="documentsManagerRef" 
        v-model="visibleDialog"
        :showUploadButton="false" 
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, nextTick, watch } from 'vue'
  import { Minus, Edit, Delete, Plus, Picture, Upload } from '@element-plus/icons-vue'
  import { DocumentsManager } from 'filestudio-bingo'
  import { env, getAuthToken } from '/admin/support/helper'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import { useI18n } from 'vue-i18n'
  
  const { t } = useI18n()
  
  const imagePositionOptions = {
    left: t('Editor.infoSectionEditor.imagePositionOptions.left'),
    right: t('Editor.infoSectionEditor.imagePositionOptions.right')
  }
  const variantOptions = {
    default: t('Editor.infoSectionEditor.variantOptions.default'),
    alternate: t('Editor.infoSectionEditor.variantOptions.alternate')
  }
  const buttonTypeOptions = {
    primary: t('Editor.infoSectionEditor.buttonTypeOptions.primary'),
    secondary: t('Editor.infoSectionEditor.buttonTypeOptions.secondary'),
    success: t('Editor.infoSectionEditor.buttonTypeOptions.success'),
    danger: t('Editor.infoSectionEditor.buttonTypeOptions.danger'),
    warning: t('Editor.infoSectionEditor.buttonTypeOptions.warning'),
    info: t('Editor.infoSectionEditor.buttonTypeOptions.info'),
    light: t('Editor.infoSectionEditor.buttonTypeOptions.light'),
    dark: t('Editor.infoSectionEditor.buttonTypeOptions.dark')
  }
  const contentAlignmentOptions = {
    left: t('Editor.infoSectionEditor.contentAlignmentOptions.left'),
    center: t('Editor.infoSectionEditor.contentAlignmentOptions.center'),
    right: t('Editor.infoSectionEditor.contentAlignmentOptions.right')
  }
  
  // 定义组件名称
  defineOptions({
    name: 'InfoSectionEditor'
  })
  
  const props = defineProps({
    blockElement: {
      type: Object as () => HTMLElement | null,
      default: null
    },
    blockType: {
      type: String,
      default: ''
    }
  })
  
  const emit = defineEmits(['update-block'])
  
  // 当前激活的标签
  const activeTab = ref('content')
  
  // 是否有未保存的更改
  const isChanged = ref(false)
  
  // 原始HTML
  const originalHtml = ref('')
  
  // 内容数据
  const title = ref('信息标题')
  const subtitle = ref('信息副标题')
  const description = ref('这是详细的描述文本，您可以在这里添加任何需要展示的信息。')
  const imageUrl = ref('https://via.placeholder.com/600x400')
  const imagePosition = ref('right')
  const buttonText = ref('了解更多')
  const buttonUrl = ref('#')
  
  // 样式数据
  const backgroundColor = ref('')
  const textColor = ref('')
  const variant = ref('default')
  const buttonType = ref('primary')
  const contentAlignment = ref('left')
  
  // 文件管理器相关
  const visibleDialog = ref(false)
  const documentsManagerRef = ref(null)
  let tokens: string | null = getAuthToken()
  const token = ref<string>(tokens ?? '')
  const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
  const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')
  
  // 标记为已更改
  const markAsChanged = () => {
    isChanged.value = true
  }
  
  /**
   * 设置默认值
   */
  const setDefaultValues = () => {
    // 内容数据默认值
    title.value = '信息标题'
    subtitle.value = '信息副标题'
    description.value = '这是详细的描述文本，您可以在这里添加任何需要展示的信息。'
    imageUrl.value = 'https://via.placeholder.com/600x400'
    imagePosition.value = 'right'
    buttonText.value = '了解更多'
    buttonUrl.value = '#'
    
    // 样式数据默认值
    backgroundColor.value = ''
    textColor.value = ''
    variant.value = 'default'
    buttonType.value = 'primary'
    contentAlignment.value = 'left'
  }
  
  /**
   * 从元素中提取信息区块内容
   */
  const extractInfoSectionContent = (): boolean => {
    if (!props.blockElement) return false
    
    // 保存原始HTML
    originalHtml.value = props.blockElement.outerHTML
    
    try {
      // 提取variant属性
      const dataVariant = props.blockElement.getAttribute('data-variant')
      if (dataVariant) {
        variant.value = dataVariant
      }
      
      // 提取背景色和文本颜色
      const style = props.blockElement.getAttribute('style')
      if (style) {
        // 解析背景色
        const bgMatch = style.match(/background-color:\s*([^;]+)/)
        if (bgMatch && bgMatch[1]) {
          backgroundColor.value = bgMatch[1]
        }
        
        // 解析文本颜色
        const colorMatch = style.match(/color:\s*([^;]+)/)
        if (colorMatch && colorMatch[1]) {
          textColor.value = colorMatch[1]
        }
      }
      
      // 提取标题
      const titleElement = props.blockElement.querySelector('.info-section-title')
      if (titleElement) {
        title.value = titleElement.textContent?.trim() || '信息标题'
      }
      
      // 提取副标题
      const subtitleElement = props.blockElement.querySelector('.info-section-subtitle')
      if (subtitleElement) {
        subtitle.value = subtitleElement.textContent?.trim() || '信息副标题'
      }
      
      // 提取描述
      const descriptionElement = props.blockElement.querySelector('.info-section-description')
      if (descriptionElement) {
        description.value = descriptionElement.textContent?.trim() || '这是详细的描述文本，您可以在这里添加任何需要展示的信息。'
      }
      
      // 提取图片URL
      const imageElement = props.blockElement.querySelector('img')
      if (imageElement) {
        imageUrl.value = imageElement.src || 'https://via.placeholder.com/600x400'
      }
      
      // 提取图片位置
      const rowElement = props.blockElement.querySelector('.row')
      if (rowElement) {
        const firstColContent = rowElement.querySelector('.col-md-6:first-child')
        if (firstColContent && firstColContent.querySelector('img')) {
          imagePosition.value = 'left'
        } else {
          imagePosition.value = 'right'
        }
      }
      
      // 提取内容对齐方式
      const contentCol = props.blockElement.querySelector('.info-content')
      if (contentCol) {
        if (contentCol.classList.contains('text-center')) {
          contentAlignment.value = 'center'
        } else if (contentCol.classList.contains('text-right') || contentCol.classList.contains('text-end')) {
          contentAlignment.value = 'right'
        } else {
          contentAlignment.value = 'left'
        }
      }
      
      // 提取按钮
      const buttonElement = props.blockElement.querySelector('.btn')
      if (buttonElement) {
        buttonText.value = buttonElement.textContent?.trim() || '了解更多'
        
        // 提取按钮链接
        if (buttonElement instanceof HTMLAnchorElement) {
          buttonUrl.value = buttonElement.href || '#'
        }
        
        // 提取按钮样式
        const btnClassMatch = Array.from(buttonElement.classList)
          .find(cls => cls.startsWith('btn-'))
        
        if (btnClassMatch) {
          buttonType.value = btnClassMatch.replace('btn-', '')
        }
      } else {
        // 如果没有找到按钮，则设置为空文本
        buttonText.value = ''
      }
      
      return true
    } catch (error) {
      console.error('提取信息区块内容时出错:', error)
      return false
    }
  }
  
  // 监听 blockElement 的变化
  watch(() => props.blockElement, (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      // 重置更改状态
      isChanged.value = false
      
      // 重新提取数据
      const extracted = extractInfoSectionContent()
      
      // 如果提取失败，使用默认值
      if (!extracted) {
        setDefaultValues()
      }
    }
  }, { immediate: true, deep: true })
  
  // 组件挂载时初始化
  onMounted(() => {
    // 移除原有的初始化逻辑，因为已经由 watch 处理
    isChanged.value = false
  })
  
  /**
   * 准备信息区块HTML
   */
  const prepareInfoSectionHTML = (): string => {
    try {
      // 生成文本对齐类
      const alignClass = contentAlignment.value === 'center' ? 'text-center' : 
                        contentAlignment.value === 'right' ? 'text-end' : 
                        'text-start'
      
      // 生成样式字符串
      let styleStr = ''
      if (backgroundColor.value) {
        styleStr += `background-color: ${backgroundColor.value}; `
      }
      if (textColor.value) {
        styleStr += `color: ${textColor.value}; `
      }
      
      // 根据variant生成不同的布局
      let imageContent = `
        <div class="col-md-6">
          <img src="${imageUrl.value}" alt="${title.value}" class="rounded shadow img-fluid">
        </div>
      `;
      
      let textContent = `
        <div class="col-md-6 info-content ${alignClass} d-flex flex-column justify-content-center">
          <h2 class="mb-3 info-section-title">${title.value}</h2>
          <h5 class="mb-3 info-section-subtitle text-muted">${subtitle.value}</h5>
          <p class="mb-4 info-section-description">${description.value}</p>
          ${buttonText.value ? `<a href="${buttonUrl.value}" class="btn btn-${buttonType.value} align-self-${contentAlignment.value === 'center' ? 'center' : contentAlignment.value}">${buttonText.value}</a>` : ''}
        </div>
      `;
      
      // 根据图片位置排列内容
      let rowContent = '';
      if (imagePosition.value === 'left') {
        rowContent = `
          <div class="row g-4 align-items-center">
            ${imageContent}
            ${textContent}
          </div>
        `;
      } else {
        rowContent = `
          <div class="row g-4 align-items-center">
            ${textContent}
            ${imageContent}
          </div>
        `;
      }
      
      // 生成最终的HTML
      return `
        <div data-bs-component="info-section" class="py-5 info-section-block" data-variant="${variant.value}" style="${styleStr}">
          <div class="container">
            ${rowContent}
          </div>
        </div>
      `;
    } catch (error) {
      console.error('准备信息区块HTML时出错:', error);
      return '';
    }
  }
  
  // 应用更改
  const applyChanges = () => {
    try {
      const html = prepareInfoSectionHTML()
      
      // 发出更新事件
      emit('update-block', { html })
      
      // 重置更改状态
      isChanged.value = false
    } catch (error) {
      console.error('应用信息区块更改时出错:', error)
    }
  }

  // 打开文件管理器
  const openFileManager = () => {
    visibleDialog.value = true
  }

  // 确认删除图片
  const confirmDeleteImage = () => {
    ElMessageBox.confirm(
      '确定要删除当前图片吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      imageUrl.value = ''
      markAsChanged()
      ElMessage.success('图片已删除')
    }).catch(() => {
      // 取消删除，不做任何操作
    })
  }

  // 文件选择确认
  const confirmSelection = (selectedFiles: any[]) => {
    if (!selectedFiles || selectedFiles.length === 0) return
    
    const file = selectedFiles[0]
    imageUrl.value = file.path || file.url
    markAsChanged()
  }
  </script>
  
  <style lang="scss" scoped>
  .edit-section {
    margin-bottom: 20px;
    position: relative;
  }
  
  .image-preview-container {
    width: 100%;
    height: 200px;
    background-color: #F6F6F6;
    margin-bottom: 18px;
    overflow: hidden;
    border-radius: 4px;
  }
  
  .image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .preview-img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
  
  .image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .placeholder-icon {
      font-size: 40px;
      color: #9E9E9E;
      margin-bottom: 10px;
    }
    
    .placeholder-text {
      color: #9E9E9E;
      font-size: 16px;
    }
  }
  
  .button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    
    .delete-btn {
      color: #707070;
      background: #FFFFFF;
      border-radius: 5px;
    }
  }
  
  .apply-button-container {
    margin-top: 20px;
    text-align: center;
    padding: 10px 0;
    border-top: 1px dashed #e4e7ed;
  }
  
  :deep(.el-radio-button__inner) {
    padding: 8px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  </style>