import { Node, mergeAttributes } from '@tiptap/core'

// 定义Bootstrap组件类型
export type BootstrapComponentType = 
  // 容器组件
  | 'container' | 'row' | 'col'
  // 内容组件
  | 'card' | 'alert' | 'badge' | 'breadcrumb' | 'button'
  // 导航组件
  | 'nav' | 'navbar' | 'pagination' | 'tab'
  // 表单组件
  | 'form' | 'input-group' | 'dropdown'
  // 模态框和弹出组件
  | 'modal' | 'tooltip' | 'popover' | 'toast'
  // 进度组件
  | 'progress' | 'spinner'
  // 列表组件
  | 'list-group' | 'accordion'
  // 其他组件
  | 'carousel' | 'jumbotron' | 'media' | 'table' | 'generic'

// Bootstrap组件公共接口
interface BootstrapComponentOptions {
  HTMLAttributes: Record<string, any>
}

// 创建Bootstrap组件Node
function createBootstrapComponent(componentType: BootstrapComponentType) {
  return Node.create({
    name: `bootstrap${componentType.replace(/-/g, '').replace(/^\w/, c => c.toUpperCase())}`,
    group: 'block',
    content: 'block*',
    
    // 较高优先级确保不干扰核心节点
    priority: 100,
    
    draggable: true,
    
    addOptions() {
      return {
        HTMLAttributes: {},
      } as BootstrapComponentOptions
    },
    
    parseHTML() {
      return [
        {
          tag: `div[data-bs-component="${componentType}"]`,
        },
      ]
    },
    
    renderHTML({ HTMLAttributes }) {
      const attrs = {
        'data-bs-component': componentType,
        class: getDefaultClassForComponent(componentType)
      }
      
      return ['div', mergeAttributes(this.options.HTMLAttributes, attrs, HTMLAttributes), 0]
    },
    
    addAttributes() {
      return {
        // 默认样式类
        class: {
          default: getDefaultClassForComponent(componentType),
          parseHTML: element => element.getAttribute('class'),
        },
        // 额外属性
        ...getComponentSpecificAttributes(componentType)
      }
    }
  })
}

// 获取组件默认样式类
function getDefaultClassForComponent(type: BootstrapComponentType): string {
  switch (type) {
    case 'container': return 'container'
    case 'row': return 'row'
    case 'col': return 'col'
    case 'card': return 'card'
    case 'alert': return 'alert alert-primary'
    case 'badge': return 'badge bg-primary'
    case 'breadcrumb': return 'breadcrumb'
    case 'button': return 'btn btn-primary'
    case 'nav': return 'nav'
    case 'navbar': return 'navbar navbar-expand-lg navbar-light bg-light'
    case 'pagination': return 'pagination'
    case 'tab': return 'nav nav-tabs'
    case 'form': return 'form'
    case 'input-group': return 'input-group'
    case 'dropdown': return 'dropdown'
    case 'modal': return 'modal'
    case 'tooltip': return ''
    case 'popover': return ''
    case 'progress': return 'progress'
    case 'spinner': return 'spinner-border'
    case 'list-group': return 'list-group'
    case 'accordion': return 'accordion'
    case 'carousel': return 'carousel slide'
    case 'jumbotron': return 'p-5 mb-4 bg-light rounded-3'
    case 'media': return 'd-flex'
    case 'table': return 'table'
    default: return ''
  }
}

// 获取组件特定属性
function getComponentSpecificAttributes(type: BootstrapComponentType) {
  const commonAttributes = {
    id: {
      default: null,
      parseHTML: element => element.getAttribute('id')
    },
    style: {
      default: null,
      parseHTML: element => element.getAttribute('style')
    }
  }
  
  // 组件特定属性
  const specificAttributes: Record<string, any> = {}
  
  switch (type) {
    case 'button':
      specificAttributes['data-bs-toggle'] = {
        default: null,
        parseHTML: element => element.getAttribute('data-bs-toggle')
      }
      specificAttributes['data-bs-target'] = {
        default: null,
        parseHTML: element => element.getAttribute('data-bs-target')
      }
      break
      
    case 'modal':
    case 'dropdown':
    case 'tooltip':
    case 'popover':
    case 'tab':
      specificAttributes['data-bs-toggle'] = {
        default: type,
        parseHTML: element => element.getAttribute('data-bs-toggle') || type
      }
      break
      
    case 'carousel':
      specificAttributes['data-bs-ride'] = {
        default: 'carousel',
        parseHTML: element => element.getAttribute('data-bs-ride') || 'carousel'
      }
      break
      
    case 'alert':
      specificAttributes['data-bs-dismiss'] = {
        default: null,
        parseHTML: element => element.getAttribute('data-bs-dismiss')
      }
      break
      
    case 'accordion':
      specificAttributes['data-bs-parent'] = {
        default: null,
        parseHTML: element => element.getAttribute('data-bs-parent')
      }
      break
  }
  
  return { ...commonAttributes, ...specificAttributes }
}

// 导出所有Bootstrap组件
export const BootstrapContainer = createBootstrapComponent('container')
export const BootstrapRow = createBootstrapComponent('row')
export const BootstrapCol = createBootstrapComponent('col')
export const BootstrapCard = createBootstrapComponent('card')
export const BootstrapAlert = createBootstrapComponent('alert')
export const BootstrapBadge = createBootstrapComponent('badge')
export const BootstrapBreadcrumb = createBootstrapComponent('breadcrumb')
export const BootstrapButton = createBootstrapComponent('button')
export const BootstrapNav = createBootstrapComponent('nav')
export const BootstrapNavbar = createBootstrapComponent('navbar')
export const BootstrapPagination = createBootstrapComponent('pagination')
export const BootstrapTab = createBootstrapComponent('tab')
export const BootstrapForm = createBootstrapComponent('form')
export const BootstrapInputGroup = createBootstrapComponent('input-group')
export const BootstrapDropdown = createBootstrapComponent('dropdown')
export const BootstrapModal = createBootstrapComponent('modal')
export const BootstrapTooltip = createBootstrapComponent('tooltip')
export const BootstrapPopover = createBootstrapComponent('popover')
export const BootstrapProgress = createBootstrapComponent('progress')
export const BootstrapSpinner = createBootstrapComponent('spinner')
export const BootstrapListGroup = createBootstrapComponent('list-group')
export const BootstrapAccordion = createBootstrapComponent('accordion')
export const BootstrapCarousel = createBootstrapComponent('carousel')
export const BootstrapJumbotron = createBootstrapComponent('jumbotron')
export const BootstrapMedia = createBootstrapComponent('media')
export const BootstrapTable = createBootstrapComponent('table')
export const BootstrapToast = createBootstrapComponent('toast')

// 导出所有组件集合
export const bootstrapComponents = [
  BootstrapContainer,
  BootstrapRow,
  BootstrapCol,
  BootstrapCard,
  BootstrapAlert,
  BootstrapBadge,
  BootstrapBreadcrumb,
  BootstrapButton,
  BootstrapNav,
  BootstrapNavbar,
  BootstrapPagination,
  BootstrapTab,
  BootstrapForm,
  BootstrapInputGroup,
  BootstrapDropdown,
  BootstrapModal,
  BootstrapTooltip,
  BootstrapPopover,
  BootstrapProgress,
  BootstrapSpinner,
  BootstrapListGroup,
  BootstrapAccordion,
  BootstrapCarousel,
  BootstrapJumbotron,
  BootstrapMedia,
  BootstrapTable,
  BootstrapToast
]