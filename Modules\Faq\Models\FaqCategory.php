<?php

namespace Modules\Faq\Models;

use Modules\Common\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 常见问题分类模型
 * 对应数据库表：tvb_faq_category
 */
class FaqCategory extends BaseModel
{
    // 使用软删除功能
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'faq_category';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'name',         // 分类名称
        'status',       // 状态: 0-禁用, 1-启用
        'parent_id',    // 父级分类ID
        'root_id',      // 根分类ID
        'sort_order',   // 排序顺序
        'created_at',   // 创建时间
        'updated_at',   // 更新时间
        'deleted_at',   // 软删除时间
    ];

    /**
     * 日期字段
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取该分类下的常见问题
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function faqs()
    {
        // 关联常见问题表
        return $this->hasMany(Faq::class, 'category_id', 'id');
    }

    /**
     * 获取父级分类
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        // 关联父级分类
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * 获取子分类
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children()
    {
        // 关联子分类
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * 查询启用状态的分类作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        // 只查询启用状态
        return $query->where('status', 1);
    }

    /**
     * 查询禁用状态的分类作用域
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDisabled($query)
    {
        // 只查询禁用状态
        return $query->where('status', 0);
    }
}
