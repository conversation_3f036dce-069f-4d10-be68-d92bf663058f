<template>
  <el-dialog class="el-dialog-common-cls" v-model="visible" title="直播訊息編輯" width="700px">
    <el-tabs v-model="activeLang" class="live-edit-tabs" @tab-change="onTabChange">
      <el-tab-pane name="zh_HK" label="繁體中文">
        <el-form :model="formData.zh_HK" :rules="titleRules" label-position="top" ref="zhHKFormRef">
          <el-form-item label="直播標題 (繁體中文)" prop="title">
            <el-input 
              v-model="formData.zh_HK.title" 
              placeholder="請輸入直播標題" 
              maxlength="50" 
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="直播描述 (繁體中文)" prop="description">
            <el-input 
              type="textarea" 
              v-model="formData.zh_HK.description" 
              placeholder="請輸入直播描述"
              :rows="4"
              resize="vertical"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane name="zh_CN" label="簡體中文">
        <el-form :model="formData.zh_CN" :rules="titleRules" label-position="top" ref="zhCNFormRef">
          <el-form-item label="直播標題 (簡體中文)" prop="title">
            <el-input 
              v-model="formData.zh_CN.title" 
              placeholder="請輸入直播標題" 
              maxlength="50" 
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="直播描述 (簡體中文)" prop="description">
            <el-input 
              type="textarea" 
              v-model="formData.zh_CN.description" 
              placeholder="請輸入直播描述"
              :rows="4"
              resize="vertical"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane name="en" label="英文">
        <el-form :model="formData.en" :rules="titleRules" label-position="top" ref="enFormRef">
          <el-form-item label="直播標題 (英文)" prop="title">
            <el-input 
              v-model="formData.en.title" 
              placeholder="Please enter live stream title" 
              maxlength="50" 
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="直播描述 (英文)" prop="description">
            <el-input 
              type="textarea" 
              v-model="formData.en.description" 
              placeholder="Please enter live stream description"
              :rows="4"
              resize="vertical"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 通用设置 -->
    <div class="common-settings">
      <el-form :model="commonSettings" label-position="top">
        <el-form-item label="發佈時間 (開始)" prop="startTime">
          <div class="time-input-group">
            <el-date-picker 
              v-model="commonSettings.startDate" 
              type="date" 
              placeholder="選擇日期"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              style="width: 50%;"
            />
            <el-time-picker 
              v-model="commonSettings.startTime" 
              placeholder="選擇時間"
              format="HH:mm"
              value-format="HH:mm"
              style="flex: 1;"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="發佈時間 (結束)" prop="endTime">
          <div class="time-input-group">
            <el-date-picker 
              v-model="commonSettings.endDate" 
              type="date" 
              placeholder="選擇日期"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              style="width: 50%;"
            />
            <el-time-picker 
              v-model="commonSettings.endTime" 
              placeholder="選擇時間"
              format="HH:mm"
              value-format="HH:mm"
              style="flex: 1;"
            />
          </div>
        </el-form-item>

        <el-form-item label="直播設置">
          <div class="settings-group">
            <div class="setting-item">
              <span class="setting-label">僅音頻</span>
              <el-switch v-model="commonSettings.audioOnly" />
            </div>
            <div class="setting-item">
              <span class="setting-label">突發直播</span>
              <el-switch v-model="commonSettings.breakingLive" />
            </div>
            <div class="setting-item">
              <span class="setting-label">僅香港地區</span>
              <el-switch v-model="commonSettings.hongKongOnly" />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="封面圖" prop="coverImage">
          <div class="cover-image-upload">
            <div class="upload-area" @click="triggerUpload">
              <div v-if="!commonSettings.coverImage" class="upload-placeholder">
                <el-icon size="40"><Camera /></el-icon>
                <span>點擊上傳封面圖</span>
              </div>
              <img v-else :src="commonSettings.coverImage" class="cover-preview" />
            </div>
            <input 
              ref="fileInput" 
              type="file" 
              accept="image/*" 
              style="display: none" 
              @change="handleFileUpload"
            />
            <div v-if="commonSettings.coverImage" class="remove-image">
              <el-button type="text" @click="removeImage" class="remove-btn">
                移除圖片
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">確定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Camera } from '@element-plus/icons-vue'
import http from '/admin/support/http'

// 定义组件名称
defineOptions({
  name: 'EditDialog'
})

const props = defineProps<{
  modelValue: boolean,
  channelId: number|null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = ref(false)
const activeLang = ref('zh_HK')
const fileInput = ref<HTMLInputElement>()

// 表单引用
const zhHKFormRef = ref()
const zhCNFormRef = ref()
const enFormRef = ref()

// 表单数据
const formData = ref<{
  [key: string]: {
    title: string;
    description: string;
  };
}>({
  zh_HK: {
    title: '',
    description: ''
  },
  zh_CN: {
    title: '',
    description: ''
  },
  en: {
    title: '',
    description: ''
  }
})

// 表单验证规则
const titleRules = {
  title: [
    { required: true, message: '請輸入直播標題', trigger: 'blur' },
    { max: 50, message: '標題長度不能超過50字', trigger: 'blur' }
  ]
}

// 通用设置
const commonSettings = ref({
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  audioOnly: false,
  breakingLive: false,
  hongKongOnly: true,
  coverImage: ''
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.channelId) {
    resetForm()
    fetchChannelData(props.channelId)
  }
})

// 监听频道ID变化
watch(() => props.channelId, (id) => {
  if (visible.value && id) {
    fetchChannelData(id)
  }
})

// 监听对话框关闭
watch(visible, (val) => {
  if (!val) {
    emit('update:modelValue', false)
  }
})

// 获取频道数据
const fetchChannelData = async (id: number) => {
  try {
    const response = await http.get(`/admin/live-channel/${id}`)
    if (response.data && response.data.code === 200) {
      const data = response.data.data
      
      // 填充多语言数据
      if (data.translations) {
        Object.keys(data.translations).forEach(lang => {
          if (formData.value[lang]) {
            formData.value[lang] = {
              title: data.translations[lang].title || '',
              description: data.translations[lang].description || ''
            }
          }
        })
      }
      
      // 填充通用设置
      commonSettings.value = {
        startDate: data.startDate || '',
        startTime: data.startTime || '',
        endDate: data.endDate || '',
        endTime: data.endTime || '',
        audioOnly: data.audioOnly || false,
        breakingLive: data.breakingLive || false,
        hongKongOnly: data.hongKongOnly !== false,
        coverImage: data.coverImage || ''
      }
    }
  } catch (error) {
    ElMessage.error('獲取數據失敗')
  }
}

// 重置表单
const resetForm = () => {
  // 重置表单数据
  formData.value = {
    zh_HK: { title: '', description: '' },
    zh_CN: { title: '', description: '' },
    en: { title: '', description: '' }
  }
  
  // 重置通用设置
  commonSettings.value = {
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    audioOnly: false,
    breakingLive: false,
    hongKongOnly: true,
    coverImage: ''
  }
  
  // 清除表单验证状态
  const formRefs = [zhHKFormRef.value, zhCNFormRef.value, enFormRef.value]
  formRefs.forEach(ref => {
    if (ref) {
      ref.clearValidate()
    }
  })
  
  // 重置标签页
  activeLang.value = 'zh_HK'
}

// 标签页切换
const onTabChange = (lang: string) => {
  activeLang.value = lang
}

// 触发文件上传
const triggerUpload = () => {
  fileInput.value?.click()
}

// 处理文件上传
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    // 这里应该上传文件到服务器
    // 暂时使用本地预览
    const reader = new FileReader()
    reader.onload = (e) => {
      commonSettings.value.coverImage = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

// 移除图片
const removeImage = () => {
  commonSettings.value.coverImage = ''
}

// 取消
const handleCancel = () => {
  visible.value = false
}

// 确认
const handleConfirm = async () => {
  try {
    // 验证所有表单
    const formRefs = [zhHKFormRef.value, zhCNFormRef.value, enFormRef.value]
    const validationPromises = formRefs.map(ref => {
      if (ref) {
        return ref.validate()
      }
      return Promise.resolve()
    })
    
    await Promise.all(validationPromises)
    
    const data = {
      translations: formData.value,
      ...commonSettings.value
    }
    
    if (props.channelId) {
      // 更新
      await http.put(`/admin/live-channel/${props.channelId}`, data)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await http.post('/admin/live-channel', data)
      ElMessage.success('新增成功')
    }
    
    visible.value = false
  } catch (error) {
    if (error === false) {
      ElMessage.error('請檢查表單輸入')
    } else {
      ElMessage.error('操作失敗')
    }
  }
}
</script>

<style lang="scss" scoped>
.live-edit-tabs {
}

.common-settings {
 
}

.time-input-group {
  width: 100%;
  display: flex;
  gap: 12px;
  align-items: center;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .setting-label {
    color: #000;
    font-size: 16px;
  }
}

.cover-image-upload {
  width: 100%;
  .upload-area {
    width: 100%;
    height: 200px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #409eff;
    }
    
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #909399;
      
      span {
        margin-top: 8px;
        font-size: 14px;
      }
    }
    
    .cover-preview {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }
  }
  
  .remove-image {
    margin-top: 8px;
    text-align: center;
    
    .remove-btn {
      color: #f56c6c;
      
      &:hover {
        color: #f78989;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
</style>
