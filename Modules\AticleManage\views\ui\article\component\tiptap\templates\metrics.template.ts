/**
 * Bootstrap Metrics 数据统计模块模板
 */
export default `
<div class="py-5 metrics-section responsive-block" data-bs-component="metrics">
  <div class="container">
    <div class="text-center row justify-content-center">
      <div class="col-12">
        <div class="row g-4 metrics-container">
          <div class="col-12 col-sm-12 col-md-4">
            <div class="metrics-item">
              <div class="metrics-icon">
                <i class="fas fa-users text-primary"></i>
              </div>
              <h2 class="metrics-value display-4 fw-bold text-primary">15k+</h2>
              <p class="mb-0 metrics-label">Customers of Elevate</p>
            </div>
          </div>
          <div class="col-12 col-sm-12 col-md-4">
            <div class="metrics-item">
              <div class="metrics-icon">
                <i class="fas fa-chart-line text-primary"></i>
              </div>
              <h2 class="metrics-value display-4 fw-bold text-primary">15k+</h2>
              <p class="mb-0 metrics-label">Customers of Elevate</p>
            </div>
          </div>
          <div class="col-12 col-sm-12 col-md-4">
            <div class="metrics-item">
              <div class="metrics-icon">
                <i class="fas fa-trophy text-primary"></i>
              </div>
              <h2 class="metrics-value display-4 fw-bold text-primary">15k+</h2>
              <p class="mb-0 metrics-label">Customers of Elevate</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .metrics-section {
      background-color: #fff;
      position: relative;
      overflow: hidden;
    }

    .metrics-container {
      position: relative;
      z-index: 1;
    }

    .metrics-item {
      padding: 2rem;
      background: #fff;
      border-radius: 12px;
      transition: all 0.3s ease;
      height: 100%;
    }

    .metrics-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .metrics-icon {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      color: #4e73df;
    }

    .metrics-value {
      font-size: 3.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(45deg, #4e73df, #36b9cc);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    .metrics-label {
      font-size: 1.125rem;
      color: #6c757d;
      font-weight: 500;
    }

    /* 移动端预览模式样式 */
    .mobile-preview .container {
      padding: 0;
      max-width: 100%;
    }

    .mobile-preview .metrics-container {
      margin: 0;
    }

    .mobile-preview .metrics-container > [class*="col-"] {
      width: 100%;
      max-width: 100%;
      flex: 0 0 100%;
      padding: 0.5rem 1rem;
    }

    .mobile-preview .metrics-item {
      padding: 1.25rem;
      margin: 0;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .mobile-preview .metrics-icon {
      font-size: 1.75rem;
      margin-bottom: 0.75rem;
    }

    .mobile-preview .metrics-value {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .mobile-preview .metrics-label {
      font-size: 0.875rem;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .container {
        padding: 0;
        max-width: 100%;
      }

      .metrics-container {
        margin: 0;
      }

      .metrics-container > [class*="col-"] {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
        padding: 0.5rem 1rem;
      }

      .metrics-item {
        padding: 1.25rem;
        margin: 0;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      .metrics-icon {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
      }

      .metrics-value {
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      .metrics-label {
        font-size: 0.875rem;
      }
    }

    /* 平板端样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .metrics-section {
        padding: 3rem 0;
      }

      .metrics-item {
        padding: 1.75rem;
      }

      .metrics-icon {
        font-size: 2.25rem;
        margin-bottom: 1.25rem;
      }

      .metrics-value {
        font-size: 3rem;
        margin-bottom: 0.75rem;
      }

      .metrics-label {
        font-size: 1.05rem;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .metrics-section {
        padding: 5rem 0;
      }

      .metrics-item {
        padding: 2rem;
      }

      .metrics-icon {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
      }

      .metrics-value {
        font-size: 3.5rem;
        margin-bottom: 1rem;
      }

      .metrics-label {
        font-size: 1.125rem;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .metrics-section {
      padding: 5rem 0;
    }

    .desktop-preview .metrics-item {
      padding: 2rem;
      text-align: center;
    }

    .desktop-preview .metrics-icon {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }

    .desktop-preview .metrics-value {
      font-size: 3.5rem;
      margin-bottom: 1rem;
    }

    .desktop-preview .metrics-label {
      font-size: 1.125rem;
    }
  </style>

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</div>
`; 