/*! For license information please see gridstack-all.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.GridStack=t():e.GridStack=t()}(self,(()=>(()=>{"use strict";var e={d:(t,i)=>{for(var s in i)e.o(i,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:i[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};e.d(t,{GridStack:()=>R});class i{static getElements(e,t=document){const i="getElementById"in t?t:void 0;if("string"==typeof e){if(i&&!isNaN(+e[0])){const t=i.getElementById(e);return t?[t]:[]}let s=t.querySelectorAll(e);return s.length||"."===e[0]||"#"===e[0]||(s=t.querySelectorAll("."+e),s.length||(s=t.querySelectorAll("#"+e))),Array.from(s)}return[e]}static getElement(e,t=document){const i="getElementById"in t?t:void 0;if("string"==typeof e){if(!e.length)return null;if(i&&"#"===e[0])return i.getElementById(e.substring(1));if("#"===e[0]||"."===e[0]||"["===e[0])return t.querySelector(e);if(i&&!isNaN(+e[0]))return i.getElementById(e);let s=t.querySelector(e);return i&&!s&&(s=i.getElementById(e)),s||(s=t.querySelector("."+e)),s}return e}static isIntercepted(e,t){return!(e.y>=t.y+t.h||e.y+e.h<=t.y||e.x+e.w<=t.x||e.x>=t.x+t.w)}static isTouching(e,t){return i.isIntercepted(e,{x:t.x-.5,y:t.y-.5,w:t.w+1,h:t.h+1})}static areaIntercept(e,t){let i=e.x>t.x?e.x:t.x,s=e.x+e.w<t.x+t.w?e.x+e.w:t.x+t.w;if(s<=i)return 0;let o=e.y>t.y?e.y:t.y,n=e.y+e.h<t.y+t.h?e.y+e.h:t.y+t.h;return n<=o?0:(s-i)*(n-o)}static area(e){return e.w*e.h}static sort(e,t,i){return i=i||e.reduce(((e,t)=>Math.max(t.x+t.w,e)),0)||12,-1===t?e.sort(((e,t)=>t.x+t.y*i-(e.x+e.y*i))):e.sort(((e,t)=>e.x+e.y*i-(t.x+t.y*i)))}static createStylesheet(e,t,i){let s=document.createElement("style");const o=i?.nonce;return o&&(s.nonce=o),s.setAttribute("type","text/css"),s.setAttribute("gs-style-id",e),s.styleSheet?s.styleSheet.cssText="":s.appendChild(document.createTextNode("")),t?t.insertBefore(s,t.firstChild):(t=document.getElementsByTagName("head")[0]).appendChild(s),s.sheet}static removeStylesheet(e){let t=document.querySelector("STYLE[gs-style-id="+e+"]");t&&t.parentNode&&t.remove()}static addCSSRule(e,t,i){"function"==typeof e.addRule?e.addRule(t,i):"function"==typeof e.insertRule&&e.insertRule(`${t}{${i}}`)}static toBool(e){return"boolean"==typeof e?e:"string"==typeof e?!(""===(e=e.toLowerCase())||"no"===e||"false"===e||"0"===e):Boolean(e)}static toNumber(e){return null===e||0===e.length?void 0:Number(e)}static parseHeight(e){let t,i="px";if("string"==typeof e){let s=e.match(/^(-[0-9]+\.[0-9]+|[0-9]*\.[0-9]+|-[0-9]+|[0-9]+)(px|em|rem|vh|vw|%)?$/);if(!s)throw new Error("Invalid height");i=s[2]||"px",t=parseFloat(s[1])}else t=e;return{h:t,unit:i}}static defaults(e,...t){return t.forEach((t=>{for(const i in t){if(!t.hasOwnProperty(i))return;null===e[i]||void 0===e[i]?e[i]=t[i]:"object"==typeof t[i]&&"object"==typeof e[i]&&this.defaults(e[i],t[i])}})),e}static same(e,t){if("object"!=typeof e)return e==t;if(typeof e!=typeof t)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const i in e)if(e[i]!==t[i])return!1;return!0}static copyPos(e,t,i=!1){return void 0!==t.x&&(e.x=t.x),void 0!==t.y&&(e.y=t.y),void 0!==t.w&&(e.w=t.w),void 0!==t.h&&(e.h=t.h),i&&(t.minW&&(e.minW=t.minW),t.minH&&(e.minH=t.minH),t.maxW&&(e.maxW=t.maxW),t.maxH&&(e.maxH=t.maxH)),e}static samePos(e,t){return e&&t&&e.x===t.x&&e.y===t.y&&e.w===t.w&&e.h===t.h}static sanitizeMinMax(e){e.minW||delete e.minW,e.minH||delete e.minH,e.maxW||delete e.maxW,e.maxH||delete e.maxH}static removeInternalAndSame(e,t){if("object"==typeof e&&"object"==typeof t)for(let i in e){let s=e[i];if("_"===i[0]||s===t[i])delete e[i];else if(s&&"object"==typeof s&&void 0!==t[i]){for(let e in s)s[e]!==t[i][e]&&"_"!==e[0]||delete s[e];Object.keys(s).length||delete e[i]}}}static removeInternalForSave(e,t=!0){for(let t in e)"_"!==t[0]&&null!==e[t]&&void 0!==e[t]||delete e[t];delete e.grid,t&&delete e.el,e.autoPosition||delete e.autoPosition,e.noResize||delete e.noResize,e.noMove||delete e.noMove,e.locked||delete e.locked,1!==e.w&&e.w!==e.minW||delete e.w,1!==e.h&&e.h!==e.minH||delete e.h}static closestUpByClass(e,t){for(;e;){if(e.classList.contains(t))return e;e=e.parentElement}return null}static throttle(e,t){let i=!1;return(...s)=>{i||(i=!0,setTimeout((()=>{e(...s),i=!1}),t))}}static removePositioningStyles(e){let t=e.style;t.position&&t.removeProperty("position"),t.left&&t.removeProperty("left"),t.top&&t.removeProperty("top"),t.width&&t.removeProperty("width"),t.height&&t.removeProperty("height")}static getScrollElement(e){if(!e)return document.scrollingElement||document.documentElement;const t=getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowY)?e:this.getScrollElement(e.parentElement)}static updateScrollPosition(e,t,i){let s=e.getBoundingClientRect(),o=window.innerHeight||document.documentElement.clientHeight;if(s.top<0||s.bottom>o){let n=s.bottom-o,r=s.top,l=this.getScrollElement(e);if(null!==l){let h=l.scrollTop;s.top<0&&i<0?e.offsetHeight>o?l.scrollTop+=i:l.scrollTop+=Math.abs(r)>Math.abs(i)?i:r:i>0&&(e.offsetHeight>o?l.scrollTop+=i:l.scrollTop+=n>i?i:n),t.top+=l.scrollTop-h}}}static updateScrollResize(e,t,i){const s=this.getScrollElement(t),o=s.clientHeight,n=s===this.getScrollElement()?0:s.getBoundingClientRect().top,r=e.clientY-n,l=r>o-i;r<i?s.scrollBy({behavior:"smooth",top:r-i}):l&&s.scrollBy({behavior:"smooth",top:i-(o-r)})}static clone(e){return null==e||"object"!=typeof e?e:e instanceof Array?[...e]:{...e}}static cloneDeep(e){const t=["parentGrid","el","grid","subGrid","engine"],s=i.clone(e);for(const o in s)s.hasOwnProperty(o)&&"object"==typeof s[o]&&"__"!==o.substring(0,2)&&!t.find((e=>e===o))&&(s[o]=i.cloneDeep(e[o]));return s}static cloneNode(e){const t=e.cloneNode(!0);return t.removeAttribute("id"),t}static appendTo(e,t){let s;s="string"==typeof t?i.getElement(t):t,s&&s.appendChild(e)}static addElStyles(e,t){if(t instanceof Object)for(const i in t)t.hasOwnProperty(i)&&(Array.isArray(t[i])?t[i].forEach((t=>{e.style[i]=t})):e.style[i]=t[i])}static initEvent(e,t){const i={type:t.type},s={button:0,which:0,buttons:1,bubbles:!0,cancelable:!0,target:t.target?t.target:e.target};return e.dataTransfer&&(i.dataTransfer=e.dataTransfer),["altKey","ctrlKey","metaKey","shiftKey"].forEach((t=>i[t]=e[t])),["pageX","pageY","clientX","clientY","screenX","screenY"].forEach((t=>i[t]=e[t])),{...i,...s}}static simulateMouseEvent(e,t,i){const s=document.createEvent("MouseEvents");s.initMouseEvent(t,!0,!0,window,1,e.screenX,e.screenY,e.clientX,e.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,0,e.target),(i||e.target).dispatchEvent(s)}}class s{constructor(e={}){this.addedNodes=[],this.removedNodes=[],this.column=e.column||12,this.maxRow=e.maxRow,this._float=e.float,this.nodes=e.nodes||[],this.onChange=e.onChange}batchUpdate(e=!0){return!!this.batchMode===e||(this.batchMode=e,e?(this._prevFloat=this._float,this._float=!0,this.saveInitial()):(this._float=this._prevFloat,delete this._prevFloat,this._packNodes()._notify())),this}_useEntireRowArea(e,t){return(!this.float||this.batchMode&&!this._prevFloat)&&!this._hasLocked&&(!e._moving||e._skipDown||t.y<=e.y)}_fixCollisions(e,t=e,s,o={}){if(this.sortNodes(-1),!(s=s||this.collide(e,t)))return!1;if(e._moving&&!o.nested&&!this.float&&this.swap(e,s))return!0;let n=t;this._useEntireRowArea(e,t)&&(n={x:0,w:this.column,y:t.y,h:t.h},s=this.collide(e,n,o.skip));let r=!1,l={nested:!0,pack:!1};for(;s=s||this.collide(e,n,o.skip);){let n;if(s.locked||e._moving&&!e._skipDown&&t.y>e.y&&!this.float&&(!this.collide(s,{...s,y:e.y},e)||!this.collide(s,{...s,y:t.y-s.h},e))?(e._skipDown=e._skipDown||t.y>e.y,n=this.moveNode(e,{...t,y:s.y+s.h,...l}),s.locked&&n?i.copyPos(t,e):!s.locked&&n&&o.pack&&(this._packNodes(),t.y=s.y+s.h,i.copyPos(e,t)),r=r||n):n=this.moveNode(s,{...s,y:t.y+t.h,skip:e,...l}),!n)return r;s=void 0}return r}collide(e,t=e,s){return this.nodes.find((o=>o!==e&&o!==s&&i.isIntercepted(o,t)))}collideAll(e,t=e,s){return this.nodes.filter((o=>o!==e&&o!==s&&i.isIntercepted(o,t)))}directionCollideCoverage(e,t,i){if(!t.rect||!e._rect)return;let s,o=e._rect,n={...t.rect};return n.y>o.y?(n.h+=n.y-o.y,n.y=o.y):n.h+=o.y-n.y,n.x>o.x?(n.w+=n.x-o.x,n.x=o.x):n.w+=o.x-n.x,i.forEach((e=>{if(e.locked||!e._rect)return;let t=e._rect,i=Number.MAX_VALUE,r=Number.MAX_VALUE,l=.5;o.y<t.y?i=(n.y+n.h-t.y)/t.h:o.y+o.h>t.y+t.h&&(i=(t.y+t.h-n.y)/t.h),o.x<t.x?r=(n.x+n.w-t.x)/t.w:o.x+o.w>t.x+t.w&&(r=(t.x+t.w-n.x)/t.w);let h=Math.min(r,i);h>l&&(l=h,s=e)})),t.collide=s,s}cacheRects(e,t,i,s,o,n){return this.nodes.forEach((r=>r._rect={y:r.y*t+i,x:r.x*e+n,w:r.w*e-n-s,h:r.h*t-i-o})),this}swap(e,t){if(!t||t.locked||!e||e.locked)return!1;function s(){let i=t.x,s=t.y;return t.x=e.x,t.y=e.y,e.h!=t.h?(e.x=i,e.y=t.y+t.h):e.w!=t.w?(e.x=t.x+t.w,e.y=s):(e.x=i,e.y=s),e._dirty=t._dirty=!0,!0}let o;if(e.w===t.w&&e.h===t.h&&(e.x===t.x||e.y===t.y)&&(o=i.isTouching(e,t)))return s();if(!1!==o){if(e.w===t.w&&e.x===t.x&&(o||(o=i.isTouching(e,t)))){if(t.y<e.y){let i=e;e=t,t=i}return s()}if(!1!==o){if(e.h===t.h&&e.y===t.y&&(o||(o=i.isTouching(e,t)))){if(t.x<e.x){let i=e;e=t,t=i}return s()}return!1}}}isAreaEmpty(e,t,i,s){let o={x:e||0,y:t||0,w:i||1,h:s||1};return!this.collide(o)}compact(){if(0===this.nodes.length)return this;this.batchUpdate().sortNodes();let e=this.nodes;return this.nodes=[],e.forEach((e=>{e.locked||(e.autoPosition=!0),this.addNode(e,!1),e._dirty=!0})),this.batchUpdate(!1)}set float(e){this._float!==e&&(this._float=e||!1,e||this._packNodes()._notify())}get float(){return this._float||!1}sortNodes(e){return this.nodes=i.sort(this.nodes,e,this.column),this}_packNodes(){return this.batchMode||(this.sortNodes(),this.float?this.nodes.forEach((e=>{if(e._updating||void 0===e._orig||e.y===e._orig.y)return;let t=e.y;for(;t>e._orig.y;)--t,this.collide(e,{x:e.x,y:t,w:e.w,h:e.h})||(e._dirty=!0,e.y=t)})):this.nodes.forEach(((e,t)=>{if(!e.locked)for(;e.y>0;){let i=0===t?0:e.y-1;if(0!==t&&this.collide(e,{x:e.x,y:i,w:e.w,h:e.h}))break;e._dirty=e.y!==i,e.y=i}}))),this}prepareNode(e,t){(e=e||{})._id=e._id??s._idSeq++,void 0!==e.x&&void 0!==e.y&&null!==e.x&&null!==e.y||(e.autoPosition=!0);let o={x:0,y:0,w:1,h:1};return i.defaults(e,o),e.autoPosition||delete e.autoPosition,e.noResize||delete e.noResize,e.noMove||delete e.noMove,i.sanitizeMinMax(e),"string"==typeof e.x&&(e.x=Number(e.x)),"string"==typeof e.y&&(e.y=Number(e.y)),"string"==typeof e.w&&(e.w=Number(e.w)),"string"==typeof e.h&&(e.h=Number(e.h)),isNaN(e.x)&&(e.x=o.x,e.autoPosition=!0),isNaN(e.y)&&(e.y=o.y,e.autoPosition=!0),isNaN(e.w)&&(e.w=o.w),isNaN(e.h)&&(e.h=o.h),this.nodeBoundFix(e,t)}nodeBoundFix(e,t){let s=e._orig||i.copyPos({},e);if(e.maxW&&(e.w=Math.min(e.w,e.maxW)),e.maxH&&(e.h=Math.min(e.h,e.maxH)),e.minW&&e.minW<=this.column&&(e.w=Math.max(e.w,e.minW)),e.minH&&(e.h=Math.max(e.h,e.minH)),(1===this.column||e.x+e.w>this.column)&&this.column<12&&!this._inColumnResize&&e._id&&-1===this.findCacheLayout(e,12)){let t={...e};t.autoPosition?(delete t.x,delete t.y):t.x=Math.min(11,t.x),t.w=Math.min(12,t.w),this.cacheOneLayout(t,12)}return e.w>this.column?e.w=this.column:e.w<1&&(e.w=1),this.maxRow&&e.h>this.maxRow?e.h=this.maxRow:e.h<1&&(e.h=1),e.x<0&&(e.x=0),e.y<0&&(e.y=0),e.x+e.w>this.column&&(t?e.w=this.column-e.x:e.x=this.column-e.w),this.maxRow&&e.y+e.h>this.maxRow&&(t?e.h=this.maxRow-e.y:e.y=this.maxRow-e.h),i.samePos(e,s)||(e._dirty=!0),e}getDirtyNodes(e){return e?this.nodes.filter((e=>e._dirty&&!i.samePos(e,e._orig))):this.nodes.filter((e=>e._dirty))}_notify(e){if(this.batchMode||!this.onChange)return this;let t=(e||[]).concat(this.getDirtyNodes());return this.onChange(t),this}cleanNodes(){return this.batchMode||this.nodes.forEach((e=>{delete e._dirty,delete e._lastTried})),this}saveInitial(){return this.nodes.forEach((e=>{e._orig=i.copyPos({},e),delete e._dirty})),this._hasLocked=this.nodes.some((e=>e.locked)),this}restoreInitial(){return this.nodes.forEach((e=>{i.samePos(e,e._orig)||(i.copyPos(e,e._orig),e._dirty=!0)})),this._notify(),this}findEmptyPosition(e,t=this.nodes,s=this.column){t=i.sort(t,-1,s);let o=!1;for(let n=0;!o;++n){let r=n%s,l=Math.floor(n/s);if(r+e.w>s)continue;let h={x:r,y:l,w:e.w,h:e.h};t.find((e=>i.isIntercepted(h,e)))||(e.x=r,e.y=l,delete e.autoPosition,o=!0)}return o}addNode(e,t=!1){return this.nodes.find((t=>t._id===e._id))||(delete(e=this._inColumnResize?this.nodeBoundFix(e):this.prepareNode(e))._temporaryRemoved,delete e._removeDOM,e.autoPosition&&this.findEmptyPosition(e)&&delete e.autoPosition,this.nodes.push(e),t&&this.addedNodes.push(e),this._fixCollisions(e),this.batchMode||this._packNodes()._notify(),e)}removeNode(e,t=!0,i=!1){return this.nodes.find((t=>t===e))?(i&&this.removedNodes.push(e),t&&(e._removeDOM=!0),this.nodes=this.nodes.filter((t=>t!==e)),this._packNodes()._notify([e])):this}removeAll(e=!0){return delete this._layouts,this.nodes.length?(e&&this.nodes.forEach((e=>e._removeDOM=!0)),this.removedNodes=this.nodes,this.nodes=[],this._notify(this.removedNodes)):this}moveNodeCheck(e,t){if(!this.changedPosConstrain(e,t))return!1;if(t.pack=!0,!this.maxRow)return this.moveNode(e,t);let o,n=new s({column:this.column,float:this.float,nodes:this.nodes.map((t=>t===e?(o={...t},o):{...t}))});if(!o)return!1;let r=n.moveNode(o,t)&&n.getRow()<=this.maxRow;if(!r&&!t.resizing&&t.collide){let i=t.collide.el.gridstackNode;if(this.swap(e,i))return this._notify(),!0}return!!r&&(n.nodes.filter((e=>e._dirty)).forEach((e=>{let t=this.nodes.find((t=>t._id===e._id));t&&(i.copyPos(t,e),t._dirty=!0)})),this._notify(),!0)}willItFit(e){if(delete e._willFitPos,!this.maxRow)return!0;let t=new s({column:this.column,float:this.float,nodes:this.nodes.map((e=>({...e})))}),o={...e};return this.cleanupNode(o),delete o.el,delete o._id,delete o.content,delete o.grid,t.addNode(o),t.getRow()<=this.maxRow&&(e._willFitPos=i.copyPos({},o),!0)}changedPosConstrain(e,t){return t.w=t.w||e.w,t.h=t.h||e.h,e.x!==t.x||e.y!==t.y||(e.maxW&&(t.w=Math.min(t.w,e.maxW)),e.maxH&&(t.h=Math.min(t.h,e.maxH)),e.minW&&(t.w=Math.max(t.w,e.minW)),e.minH&&(t.h=Math.max(t.h,e.minH)),e.w!==t.w||e.h!==t.h)}moveNode(e,t){if(!e||!t)return!1;let s;void 0===t.pack&&(s=t.pack=!0),"number"!=typeof t.x&&(t.x=e.x),"number"!=typeof t.y&&(t.y=e.y),"number"!=typeof t.w&&(t.w=e.w),"number"!=typeof t.h&&(t.h=e.h);let o=e.w!==t.w||e.h!==t.h,n=i.copyPos({},e,!0);if(i.copyPos(n,t),n=this.nodeBoundFix(n,o),i.copyPos(t,n),i.samePos(e,t))return!1;let r=i.copyPos({},e),l=this.collideAll(e,n,t.skip),h=!0;if(l.length){let o=e._moving&&!t.nested,r=o?this.directionCollideCoverage(e,t,l):l[0];if(o&&r&&e.grid?.opts?.subGridDynamic&&!e.grid._isTemp){let s=i.areaIntercept(t.rect,r._rect),o=i.area(t.rect),n=i.area(r._rect);s/(o<n?o:n)>.8&&(r.grid.makeSubGrid(r.el,void 0,e),r=void 0)}r?h=!this._fixCollisions(e,n,r,t):(h=!1,s&&delete t.pack)}return h&&(e._dirty=!0,i.copyPos(e,n)),t.pack&&this._packNodes()._notify(),!i.samePos(e,r)}getRow(){return this.nodes.reduce(((e,t)=>Math.max(e,t.y+t.h)),0)}beginUpdate(e){return e._updating||(e._updating=!0,delete e._skipDown,this.batchMode||this.saveInitial()),this}endUpdate(){let e=this.nodes.find((e=>e._updating));return e&&(delete e._updating,delete e._skipDown),this}save(e=!0,t){let s=this._layouts?.length,o=s&&this.column!==s-1?this._layouts[s-1]:null,n=[];return this.sortNodes(),this.nodes.forEach((s=>{let r=o?.find((e=>e._id===s._id)),l={...s};r&&(l.x=r.x,l.y=r.y,l.w=r.w),i.removeInternalForSave(l,!e),t&&t(s,l),n.push(l)})),n}layoutsNodesChange(e){return!this._layouts||this._inColumnResize||this._layouts.forEach(((t,i)=>{if(!t||i===this.column)return this;if(i<this.column)this._layouts[i]=void 0;else{let s=i/this.column;e.forEach((e=>{if(!e._orig)return;let i=t.find((t=>t._id===e._id));i&&(e.y!==e._orig.y&&(i.y+=e.y-e._orig.y),e.x!==e._orig.x&&(i.x=Math.round(e.x*s)),e.w!==e._orig.w&&(i.w=Math.round(e.w*s)))}))}})),this}updateNodeWidths(e,t,s,o="moveScale"){if(!this.nodes.length||!t||e===t)return this;this.cacheLayout(this.nodes,e),this.batchUpdate();let n=[],r=!1;if(1===t&&s?.length){r=!0;let e=0;s.forEach((t=>{t.x=0,t.w=1,t.y=Math.max(t.y,e),e=t.y+t.h})),n=s,s=[]}else s=i.sort(this.nodes,-1,e);let l=[];if(t>e){l=this._layouts[t]||[];let i=this._layouts.length-1;!l.length&&e!==i&&this._layouts[i]?.length&&(e=i,this._layouts[i].forEach((e=>{let t=s.find((t=>t._id===e._id));t&&(t.x=e.x,t.y=e.y,t.w=e.w)})))}if(l.forEach((e=>{let t=s.findIndex((t=>t._id===e._id));-1!==t&&((e.autoPosition||isNaN(e.x)||isNaN(e.y))&&this.findEmptyPosition(e,n),e.autoPosition||(s[t].x=e.x,s[t].y=e.y,s[t].w=e.w,n.push(s[t])),s.splice(t,1))})),s.length)if("function"==typeof o)o(t,e,n,s);else if(!r){let i=t/e,r="move"===o||"moveScale"===o,l="scale"===o||"moveScale"===o;s.forEach((s=>{s.x=1===t?0:r?Math.round(s.x*i):Math.min(s.x,t-1),s.w=1===t||1===e?1:l?Math.round(s.w*i)||1:Math.min(s.w,t),n.push(s)})),s=[]}return r||(n=i.sort(n,-1,t)),this._inColumnResize=!0,this.nodes=[],n.forEach((e=>{this.addNode(e,!1),delete e._orig})),this.batchUpdate(!1),delete this._inColumnResize,this}cacheLayout(e,t,i=!1){let o=[];return e.forEach(((e,t)=>{e._id=e._id??s._idSeq++,o[t]={x:e.x,y:e.y,w:e.w,_id:e._id}})),this._layouts=i?[]:this._layouts||[],this._layouts[t]=o,this}cacheOneLayout(e,t){e._id=e._id??s._idSeq++;let i={x:e.x,y:e.y,w:e.w,_id:e._id};e.autoPosition&&(delete i.x,delete i.y,i.autoPosition=!0),this._layouts=this._layouts||[],this._layouts[t]=this._layouts[t]||[];let o=this.findCacheLayout(e,t);return-1===o?this._layouts[t].push(i):this._layouts[t][o]=i,this}findCacheLayout(e,t){return this._layouts?.[t]?.findIndex((t=>t._id===e._id))??-1}cleanupNode(e){for(let t in e)"_"===t[0]&&"_id"!==t&&delete e[t];return this}}s._idSeq=0;const o={alwaysShowResizeHandle:"mobile",animate:!0,auto:!0,cellHeight:"auto",cellHeightThrottle:100,cellHeightUnit:"px",column:12,draggable:{handle:".grid-stack-item-content",appendTo:"body",scroll:!0},handle:".grid-stack-item-content",itemClass:"grid-stack-item",margin:10,marginUnit:"px",maxRow:0,minRow:0,oneColumnSize:768,placeholderClass:"grid-stack-placeholder",placeholderText:"",removableOptions:{accept:".grid-stack-item"},resizable:{handles:"se"},rtl:"auto"},n={handle:".grid-stack-item-content",appendTo:"body"};class r{}const l="undefined"!=typeof window&&"undefined"!=typeof document&&("ontouchstart"in document||"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0);class h{}function a(e,t){if(e.touches.length>1)return;e.cancelable&&e.preventDefault();const i=e.changedTouches[0],s=document.createEvent("MouseEvents");s.initMouseEvent(t,!0,!0,window,1,i.screenX,i.screenY,i.clientX,i.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(s)}function d(e,t){e.cancelable&&e.preventDefault();const i=document.createEvent("MouseEvents");i.initMouseEvent(t,!0,!0,window,1,e.screenX,e.screenY,e.clientX,e.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(i)}function p(e){h.touchHandled||(h.touchHandled=!0,a(e,"mousedown"))}function g(e){h.touchHandled&&a(e,"mousemove")}function u(e){if(!h.touchHandled)return;h.pointerLeaveTimeout&&(window.clearTimeout(h.pointerLeaveTimeout),delete h.pointerLeaveTimeout);const t=!!r.dragElement;a(e,"mouseup"),t||a(e,"click"),h.touchHandled=!1}function c(e){e.target.releasePointerCapture(e.pointerId)}function m(e){r.dragElement&&d(e,"mouseenter")}function _(e){r.dragElement&&(h.pointerLeaveTimeout=window.setTimeout((()=>{delete h.pointerLeaveTimeout,d(e,"mouseleave")}),10))}class v{constructor(e,t,i){this.moving=!1,this.host=e,this.dir=t,this.option=i,this._mouseDown=this._mouseDown.bind(this),this._mouseMove=this._mouseMove.bind(this),this._mouseUp=this._mouseUp.bind(this),this._init()}_init(){const e=document.createElement("div");return e.classList.add("ui-resizable-handle"),e.classList.add(`${v.prefix}${this.dir}`),e.style.zIndex="100",e.style.userSelect="none",this.el=e,this.host.appendChild(this.el),this.el.addEventListener("mousedown",this._mouseDown),l&&(this.el.addEventListener("touchstart",p),this.el.addEventListener("pointerdown",c)),this}destroy(){return this.moving&&this._mouseUp(this.mouseDownEvent),this.el.removeEventListener("mousedown",this._mouseDown),l&&(this.el.removeEventListener("touchstart",p),this.el.removeEventListener("pointerdown",c)),this.host.removeChild(this.el),delete this.el,delete this.host,this}_mouseDown(e){this.mouseDownEvent=e,document.addEventListener("mousemove",this._mouseMove,!0),document.addEventListener("mouseup",this._mouseUp,!0),l&&(this.el.addEventListener("touchmove",g),this.el.addEventListener("touchend",u)),e.stopPropagation(),e.preventDefault()}_mouseMove(e){let t=this.mouseDownEvent;this.moving?this._triggerEvent("move",e):Math.abs(e.x-t.x)+Math.abs(e.y-t.y)>2&&(this.moving=!0,this._triggerEvent("start",this.mouseDownEvent),this._triggerEvent("move",e)),e.stopPropagation(),e.preventDefault()}_mouseUp(e){this.moving&&this._triggerEvent("stop",e),document.removeEventListener("mousemove",this._mouseMove,!0),document.removeEventListener("mouseup",this._mouseUp,!0),l&&(this.el.removeEventListener("touchmove",g),this.el.removeEventListener("touchend",u)),delete this.moving,delete this.mouseDownEvent,e.stopPropagation(),e.preventDefault()}_triggerEvent(e,t){return this.option[e]&&this.option[e](t),this}}v.prefix="ui-resizable-";class f{constructor(){this._eventRegister={}}get disabled(){return this._disabled}on(e,t){this._eventRegister[e]=t}off(e){delete this._eventRegister[e]}enable(){this._disabled=!1}disable(){this._disabled=!0}destroy(){delete this._eventRegister}triggerEvent(e,t){if(!this.disabled&&this._eventRegister&&this._eventRegister[e])return this._eventRegister[e](t)}}class y extends f{constructor(e,t={}){super(),this._ui=()=>{const e=this.el.parentElement.getBoundingClientRect(),t={width:this.originalRect.width,height:this.originalRect.height+this.scrolled,left:this.originalRect.left,top:this.originalRect.top-this.scrolled},i=this.temporalRect||t;return{position:{left:i.left-e.left,top:i.top-e.top},size:{width:i.width,height:i.height}}},this.el=e,this.option=t,this._mouseOver=this._mouseOver.bind(this),this._mouseOut=this._mouseOut.bind(this),this.enable(),this._setupAutoHide(this.option.autoHide),this._setupHandlers()}on(e,t){super.on(e,t)}off(e){super.off(e)}enable(){super.enable(),this.el.classList.remove("ui-resizable-disabled"),this._setupAutoHide(this.option.autoHide)}disable(){super.disable(),this.el.classList.add("ui-resizable-disabled"),this._setupAutoHide(!1)}destroy(){this._removeHandlers(),this._setupAutoHide(!1),delete this.el,super.destroy()}updateOption(e){let t=e.handles&&e.handles!==this.option.handles,i=e.autoHide&&e.autoHide!==this.option.autoHide;return Object.keys(e).forEach((t=>this.option[t]=e[t])),t&&(this._removeHandlers(),this._setupHandlers()),i&&this._setupAutoHide(this.option.autoHide),this}_setupAutoHide(e){return e?(this.el.classList.add("ui-resizable-autohide"),this.el.addEventListener("mouseover",this._mouseOver),this.el.addEventListener("mouseout",this._mouseOut)):(this.el.classList.remove("ui-resizable-autohide"),this.el.removeEventListener("mouseover",this._mouseOver),this.el.removeEventListener("mouseout",this._mouseOut),r.overResizeElement===this&&delete r.overResizeElement),this}_mouseOver(e){r.overResizeElement||r.dragElement||(r.overResizeElement=this,this.el.classList.remove("ui-resizable-autohide"))}_mouseOut(e){r.overResizeElement===this&&(delete r.overResizeElement,this.el.classList.add("ui-resizable-autohide"))}_setupHandlers(){let e=this.option.handles||"e,s,se";return"all"===e&&(e="n,e,s,w,se,sw,ne,nw"),this.handlers=e.split(",").map((e=>e.trim())).map((e=>new v(this.el,e,{start:e=>{this._resizeStart(e)},stop:e=>{this._resizeStop(e)},move:t=>{this._resizing(t,e)}}))),this}_resizeStart(e){this.originalRect=this.el.getBoundingClientRect(),this.scrollEl=i.getScrollElement(this.el),this.scrollY=this.scrollEl.scrollTop,this.scrolled=0,this.startEvent=e,this._setupHelper(),this._applyChange();const t=i.initEvent(e,{type:"resizestart",target:this.el});return this.option.start&&this.option.start(t,this._ui()),this.el.classList.add("ui-resizable-resizing"),this.triggerEvent("resizestart",t),this}_resizing(e,t){this.scrolled=this.scrollEl.scrollTop-this.scrollY,this.temporalRect=this._getChange(e,t),this._applyChange();const s=i.initEvent(e,{type:"resize",target:this.el});return this.option.resize&&this.option.resize(s,this._ui()),this.triggerEvent("resize",s),this}_resizeStop(e){const t=i.initEvent(e,{type:"resizestop",target:this.el});return this.option.stop&&this.option.stop(t),this.el.classList.remove("ui-resizable-resizing"),this.triggerEvent("resizestop",t),this._cleanHelper(),delete this.startEvent,delete this.originalRect,delete this.temporalRect,delete this.scrollY,delete this.scrolled,this}_setupHelper(){return this.elOriginStyleVal=y._originStyleProp.map((e=>this.el.style[e])),this.parentOriginStylePosition=this.el.parentElement.style.position,window.getComputedStyle(this.el.parentElement).position.match(/static/)&&(this.el.parentElement.style.position="relative"),this.el.style.position="absolute",this.el.style.opacity="0.8",this}_cleanHelper(){return y._originStyleProp.forEach(((e,t)=>{this.el.style[e]=this.elOriginStyleVal[t]||null})),this.el.parentElement.style.position=this.parentOriginStylePosition||null,this}_getChange(e,t){const i=this.startEvent,s={width:this.originalRect.width,height:this.originalRect.height+this.scrolled,left:this.originalRect.left,top:this.originalRect.top-this.scrolled},o=e.clientX-i.clientX,n=e.clientY-i.clientY;t.indexOf("e")>-1?s.width+=o:t.indexOf("w")>-1&&(s.width-=o,s.left+=o),t.indexOf("s")>-1?s.height+=n:t.indexOf("n")>-1&&(s.height-=n,s.top+=n);const r=this._constrainSize(s.width,s.height);return Math.round(s.width)!==Math.round(r.width)&&(t.indexOf("w")>-1&&(s.left+=s.width-r.width),s.width=r.width),Math.round(s.height)!==Math.round(r.height)&&(t.indexOf("n")>-1&&(s.top+=s.height-r.height),s.height=r.height),s}_constrainSize(e,t){const i=this.option.maxWidth||Number.MAX_SAFE_INTEGER,s=this.option.minWidth||e,o=this.option.maxHeight||Number.MAX_SAFE_INTEGER,n=this.option.minHeight||t;return{width:Math.min(i,Math.max(s,e)),height:Math.min(o,Math.max(n,t))}}_applyChange(){let e={left:0,top:0,width:0,height:0};if("absolute"===this.el.style.position){const t=this.el.parentElement,{left:i,top:s}=t.getBoundingClientRect();e={left:i,top:s,width:0,height:0}}return this.temporalRect?(Object.keys(this.temporalRect).forEach((t=>{const i=this.temporalRect[t];this.el.style[t]=i-e[t]+"px"})),this):this}_removeHandlers(){return this.handlers.forEach((e=>e.destroy())),delete this.handlers,this}}y._originStyleProp=["width","height","position","left","top","opacity","zIndex"];class b extends f{constructor(e,t={}){super(),this.el=e,this.option=t;let i=t.handle.substring(1);this.dragEl=e.classList.contains(i)?e:e.querySelector(t.handle)||e,this._mouseDown=this._mouseDown.bind(this),this._mouseMove=this._mouseMove.bind(this),this._mouseUp=this._mouseUp.bind(this),this.enable()}on(e,t){super.on(e,t)}off(e){super.off(e)}enable(){!1!==this.disabled&&(super.enable(),this.dragEl.addEventListener("mousedown",this._mouseDown),l&&(this.dragEl.addEventListener("touchstart",p),this.dragEl.addEventListener("pointerdown",c)),this.el.classList.remove("ui-draggable-disabled"))}disable(e=!1){!0!==this.disabled&&(super.disable(),this.dragEl.removeEventListener("mousedown",this._mouseDown),l&&(this.dragEl.removeEventListener("touchstart",p),this.dragEl.removeEventListener("pointerdown",c)),e||this.el.classList.add("ui-draggable-disabled"))}destroy(){this.dragTimeout&&window.clearTimeout(this.dragTimeout),delete this.dragTimeout,this.dragging&&this._mouseUp(this.mouseDownEvent),this.disable(!0),delete this.el,delete this.helper,delete this.option,super.destroy()}updateOption(e){return Object.keys(e).forEach((t=>this.option[t]=e[t])),this}_mouseDown(e){if(!r.mouseHandled)return 0!==e.button||e.target.closest('input,textarea,button,select,option,[contenteditable="true"],.ui-resizable-handle')||this.option.cancel&&e.target.closest(this.option.cancel)||(this.mouseDownEvent=e,delete this.dragging,delete r.dragElement,delete r.dropElement,document.addEventListener("mousemove",this._mouseMove,!0),document.addEventListener("mouseup",this._mouseUp,!0),l&&(this.dragEl.addEventListener("touchmove",g),this.dragEl.addEventListener("touchend",u)),e.preventDefault(),document.activeElement&&document.activeElement.blur(),r.mouseHandled=!0),!0}_callDrag(e){if(!this.dragging)return;const t=i.initEvent(e,{target:this.el,type:"drag"});this.option.drag&&this.option.drag(t,this.ui()),this.triggerEvent("drag",t)}_mouseMove(e){let t=this.mouseDownEvent;if(this.dragging)if(this._dragFollow(e),r.pauseDrag){const t=Number.isInteger(r.pauseDrag)?r.pauseDrag:100;this.dragTimeout&&window.clearTimeout(this.dragTimeout),this.dragTimeout=window.setTimeout((()=>this._callDrag(e)),t)}else this._callDrag(e);else if(Math.abs(e.x-t.x)+Math.abs(e.y-t.y)>3){this.dragging=!0,r.dragElement=this;let t=this.el.gridstackNode?.grid;t?r.dropElement=t.el.ddElement.ddDroppable:delete r.dropElement,this.helper=this._createHelper(e),this._setupHelperContainmentStyle(),this.dragOffset=this._getDragOffset(e,this.el,this.helperContainment);const s=i.initEvent(e,{target:this.el,type:"dragstart"});this._setupHelperStyle(e),this.option.start&&this.option.start(s,this.ui()),this.triggerEvent("dragstart",s)}return e.preventDefault(),!0}_mouseUp(e){if(document.removeEventListener("mousemove",this._mouseMove,!0),document.removeEventListener("mouseup",this._mouseUp,!0),l&&(this.dragEl.removeEventListener("touchmove",g,!0),this.dragEl.removeEventListener("touchend",u,!0)),this.dragging){delete this.dragging,r.dropElement?.el===this.el.parentElement&&delete r.dropElement,this.helperContainment.style.position=this.parentOriginStylePosition||null,this.helper===this.el?this._removeHelperStyle():this.helper.remove();const t=i.initEvent(e,{target:this.el,type:"dragstop"});this.option.stop&&this.option.stop(t),this.triggerEvent("dragstop",t),r.dropElement&&r.dropElement.drop(e)}delete this.helper,delete this.mouseDownEvent,delete r.dragElement,delete r.dropElement,delete r.mouseHandled,e.preventDefault()}_createHelper(e){let t=this.el;return"function"==typeof this.option.helper?t=this.option.helper(e):"clone"===this.option.helper&&(t=i.cloneNode(this.el)),document.body.contains(t)||i.appendTo(t,"parent"===this.option.appendTo?this.el.parentElement:this.option.appendTo),t===this.el&&(this.dragElementOriginStyle=b.originStyleProp.map((e=>this.el.style[e]))),t}_setupHelperStyle(e){this.helper.classList.add("ui-draggable-dragging");const t=this.helper.style;return t.pointerEvents="none",t["min-width"]=0,t.width=this.dragOffset.width+"px",t.height=this.dragOffset.height+"px",t.willChange="left, top",t.position="fixed",this._dragFollow(e),t.transition="none",setTimeout((()=>{this.helper&&(t.transition=null)}),0),this}_removeHelperStyle(){this.helper.classList.remove("ui-draggable-dragging");let e=this.helper?.gridstackNode;if(!e?._isAboutToRemove&&this.dragElementOriginStyle){let e=this.helper,t=this.dragElementOriginStyle.transition||null;e.style.transition=this.dragElementOriginStyle.transition="none",b.originStyleProp.forEach((t=>e.style[t]=this.dragElementOriginStyle[t]||null)),setTimeout((()=>e.style.transition=t),50)}return delete this.dragElementOriginStyle,this}_dragFollow(e){const t=this.helper.style,i=this.dragOffset;t.left=e.clientX+i.offsetLeft-0+"px",t.top=e.clientY+i.offsetTop-0+"px"}_setupHelperContainmentStyle(){return this.helperContainment=this.helper.parentElement,"fixed"!==this.helper.style.position&&(this.parentOriginStylePosition=this.helperContainment.style.position,window.getComputedStyle(this.helperContainment).position.match(/static/)&&(this.helperContainment.style.position="relative")),this}_getDragOffset(e,t,s){let o=0,n=0;if(s){const e=document.createElement("div");i.addElStyles(e,{opacity:"0",position:"fixed",top:"0px",left:"0px",width:"1px",height:"1px",zIndex:"-999999"}),s.appendChild(e);const t=e.getBoundingClientRect();s.removeChild(e),o=t.left,n=t.top}const r=t.getBoundingClientRect();return{left:r.left,top:r.top,offsetLeft:-e.clientX+r.left-o,offsetTop:-e.clientY+r.top-n,width:r.width,height:r.height}}ui(){const e=this.el.parentElement.getBoundingClientRect(),t=this.helper.getBoundingClientRect();return{position:{top:t.top-e.top,left:t.left-e.left}}}}b.originStyleProp=["transition","pointerEvents","position","left","top","minWidth","willChange"];class w extends f{constructor(e,t={}){super(),this.el=e,this.option=t,this._mouseEnter=this._mouseEnter.bind(this),this._mouseLeave=this._mouseLeave.bind(this),this.enable(),this._setupAccept()}on(e,t){super.on(e,t)}off(e){super.off(e)}enable(){!1!==this.disabled&&(super.enable(),this.el.classList.add("ui-droppable"),this.el.classList.remove("ui-droppable-disabled"),this.el.addEventListener("mouseenter",this._mouseEnter),this.el.addEventListener("mouseleave",this._mouseLeave),l&&(this.el.addEventListener("pointerenter",m),this.el.addEventListener("pointerleave",_)))}disable(e=!1){!0!==this.disabled&&(super.disable(),this.el.classList.remove("ui-droppable"),e||this.el.classList.add("ui-droppable-disabled"),this.el.removeEventListener("mouseenter",this._mouseEnter),this.el.removeEventListener("mouseleave",this._mouseLeave),l&&(this.el.removeEventListener("pointerenter",m),this.el.removeEventListener("pointerleave",_)))}destroy(){this.disable(!0),this.el.classList.remove("ui-droppable"),this.el.classList.remove("ui-droppable-disabled"),super.destroy()}updateOption(e){return Object.keys(e).forEach((t=>this.option[t]=e[t])),this._setupAccept(),this}_mouseEnter(e){if(!r.dragElement)return;if(!this._canDrop(r.dragElement.el))return;e.preventDefault(),e.stopPropagation(),r.dropElement&&r.dropElement!==this&&r.dropElement._mouseLeave(e),r.dropElement=this;const t=i.initEvent(e,{target:this.el,type:"dropover"});this.option.over&&this.option.over(t,this._ui(r.dragElement)),this.triggerEvent("dropover",t),this.el.classList.add("ui-droppable-over")}_mouseLeave(e){if(!r.dragElement||r.dropElement!==this)return;e.preventDefault(),e.stopPropagation();const t=i.initEvent(e,{target:this.el,type:"dropout"});if(this.option.out&&this.option.out(t,this._ui(r.dragElement)),this.triggerEvent("dropout",t),r.dropElement===this){let t;delete r.dropElement;let i=this.el.parentElement;for(;!t&&i;)t=i.ddElement?.ddDroppable,i=i.parentElement;t&&t._mouseEnter(e)}}drop(e){e.preventDefault();const t=i.initEvent(e,{target:this.el,type:"drop"});this.option.drop&&this.option.drop(t,this._ui(r.dragElement)),this.triggerEvent("drop",t)}_canDrop(e){return e&&(!this.accept||this.accept(e))}_setupAccept(){return this.option.accept?("string"==typeof this.option.accept?this.accept=e=>e.matches(this.option.accept):this.accept=this.option.accept,this):this}_ui(e){return{draggable:e.el,...e.ui()}}}class E{static init(e){return e.ddElement||(e.ddElement=new E(e)),e.ddElement}constructor(e){this.el=e}on(e,t){return this.ddDraggable&&["drag","dragstart","dragstop"].indexOf(e)>-1?this.ddDraggable.on(e,t):this.ddDroppable&&["drop","dropover","dropout"].indexOf(e)>-1?this.ddDroppable.on(e,t):this.ddResizable&&["resizestart","resize","resizestop"].indexOf(e)>-1&&this.ddResizable.on(e,t),this}off(e){return this.ddDraggable&&["drag","dragstart","dragstop"].indexOf(e)>-1?this.ddDraggable.off(e):this.ddDroppable&&["drop","dropover","dropout"].indexOf(e)>-1?this.ddDroppable.off(e):this.ddResizable&&["resizestart","resize","resizestop"].indexOf(e)>-1&&this.ddResizable.off(e),this}setupDraggable(e){return this.ddDraggable?this.ddDraggable.updateOption(e):this.ddDraggable=new b(this.el,e),this}cleanDraggable(){return this.ddDraggable&&(this.ddDraggable.destroy(),delete this.ddDraggable),this}setupResizable(e){return this.ddResizable?this.ddResizable.updateOption(e):this.ddResizable=new y(this.el,e),this}cleanResizable(){return this.ddResizable&&(this.ddResizable.destroy(),delete this.ddResizable),this}setupDroppable(e){return this.ddDroppable?this.ddDroppable.updateOption(e):this.ddDroppable=new w(this.el,e),this}cleanDroppable(){return this.ddDroppable&&(this.ddDroppable.destroy(),delete this.ddDroppable),this}}const x=new class{resizable(e,t,i,s){return this._getDDElements(e).forEach((e=>{if("disable"===t||"enable"===t)e.ddResizable&&e.ddResizable[t]();else if("destroy"===t)e.ddResizable&&e.cleanResizable();else if("option"===t)e.setupResizable({[i]:s});else{const i=e.el.gridstackNode.grid;let s=e.el.getAttribute("gs-resize-handles")?e.el.getAttribute("gs-resize-handles"):i.opts.resizable.handles,o=!i.opts.alwaysShowResizeHandle;e.setupResizable({...i.opts.resizable,handles:s,autoHide:o,start:t.start,stop:t.stop,resize:t.resize})}})),this}draggable(e,t,i,s){return this._getDDElements(e).forEach((e=>{if("disable"===t||"enable"===t)e.ddDraggable&&e.ddDraggable[t]();else if("destroy"===t)e.ddDraggable&&e.cleanDraggable();else if("option"===t)e.setupDraggable({[i]:s});else{const i=e.el.gridstackNode.grid;e.setupDraggable({...i.opts.draggable,start:t.start,stop:t.stop,drag:t.drag})}})),this}dragIn(e,t){return this._getDDElements(e).forEach((e=>e.setupDraggable(t))),this}droppable(e,t,i,s){return"function"!=typeof t.accept||t._accept||(t._accept=t.accept,t.accept=e=>t._accept(e)),this._getDDElements(e).forEach((e=>{"disable"===t||"enable"===t?e.ddDroppable&&e.ddDroppable[t]():"destroy"===t?e.ddDroppable&&e.cleanDroppable():"option"===t?e.setupDroppable({[i]:s}):e.setupDroppable(t)})),this}isDroppable(e){return!(!(e&&e.ddElement&&e.ddElement.ddDroppable)||e.ddElement.ddDroppable.disabled)}isDraggable(e){return!(!(e&&e.ddElement&&e.ddElement.ddDraggable)||e.ddElement.ddDraggable.disabled)}isResizable(e){return!(!(e&&e.ddElement&&e.ddElement.ddResizable)||e.ddElement.ddResizable.disabled)}on(e,t,i){return this._getDDElements(e).forEach((e=>e.on(t,(e=>{i(e,r.dragElement?r.dragElement.el:e.target,r.dragElement?r.dragElement.helper:null)})))),this}off(e,t){return this._getDDElements(e).forEach((e=>e.off(t))),this}_getDDElements(e,t=!0){let s=i.getElements(e);if(!s.length)return[];let o=s.map((e=>e.ddElement||(t?E.init(e):null)));return t||o.filter((e=>e)),o}};class R{static init(e={},t=".grid-stack"){let s=R.getGridElement(t);return s?(s.gridstack||(s.gridstack=new R(s,i.cloneDeep(e))),s.gridstack):("string"==typeof t?console.error('GridStack.initAll() no grid was found with selector "'+t+'" - element missing or wrong selector ?\nNote: ".grid-stack" is required for proper CSS styling and drag/drop, and is the default selector.'):console.error("GridStack.init() no grid element was passed."),null)}static initAll(e={},t=".grid-stack"){let s=[];return R.getGridElements(t).forEach((t=>{t.gridstack||(t.gridstack=new R(t,i.cloneDeep(e))),s.push(t.gridstack)})),0===s.length&&console.error('GridStack.initAll() no grid was found with selector "'+t+'" - element missing or wrong selector ?\nNote: ".grid-stack" is required for proper CSS styling and drag/drop, and is the default selector.'),s}static addGrid(e,t={}){if(!e)return null;let i=e;if(i.gridstack){const e=i.gridstack;return t&&(e.opts={...e.opts,...t}),void 0!==t.children&&e.load(t.children),e}if(!e.classList.contains("grid-stack")||R.addRemoveCB)if(R.addRemoveCB)i=R.addRemoveCB(e,t,!0,!0);else{let s=document.implementation.createHTMLDocument("");s.body.innerHTML=`<div class="grid-stack ${t.class||""}"></div>`,i=s.body.children[0],e.appendChild(i)}return R.init(t,i)}static registerEngine(e){R.engineClass=e}get placeholder(){if(!this._placeholder){let e=document.createElement("div");e.className="placeholder-content",this.opts.placeholderText&&(e.innerHTML=this.opts.placeholderText),this._placeholder=document.createElement("div"),this._placeholder.classList.add(this.opts.placeholderClass,o.itemClass,this.opts.itemClass),this.placeholder.appendChild(e)}return this._placeholder}constructor(e,t={}){this._gsEventHandler={},this._extraDragRow=0,this.el=e,t=t||{},e.classList.contains("grid-stack")||this.el.classList.add("grid-stack"),t.row&&(t.minRow=t.maxRow=t.row,delete t.row);let n=i.toNumber(e.getAttribute("gs-row"));"auto"===t.column&&delete t.column,void 0!==t.alwaysShowResizeHandle&&(t._alwaysShowResizeHandle=t.alwaysShowResizeHandle);let h={...i.cloneDeep(o),column:i.toNumber(e.getAttribute("gs-column"))||o.column,minRow:n||i.toNumber(e.getAttribute("gs-min-row"))||o.minRow,maxRow:n||i.toNumber(e.getAttribute("gs-max-row"))||o.maxRow,staticGrid:i.toBool(e.getAttribute("gs-static"))||o.staticGrid,draggable:{handle:(t.handleClass?"."+t.handleClass:t.handle?t.handle:"")||o.draggable.handle},removableOptions:{accept:t.itemClass?"."+t.itemClass:o.removableOptions.accept}};e.getAttribute("gs-animate")&&(h.animate=i.toBool(e.getAttribute("gs-animate"))),this.opts=i.defaults(t,h),t=null,this._initMargin(),1!==this.opts.column&&!this.opts.disableOneColumnMode&&this._widthOrContainer()<=this.opts.oneColumnSize&&(this._prevColumn=this.getColumn(),this.opts.column=1),"auto"===this.opts.rtl&&(this.opts.rtl="rtl"===e.style.direction),this.opts.rtl&&this.el.classList.add("grid-stack-rtl");let a=i.closestUpByClass(this.el,o.itemClass)?.gridstackNode;a&&(a.subGrid=this,this.parentGridItem=a,this.el.classList.add("grid-stack-nested"),a.el.classList.add("grid-stack-sub-grid")),this._isAutoCellHeight="auto"===this.opts.cellHeight,this._isAutoCellHeight||"initial"===this.opts.cellHeight?this.cellHeight(void 0,!1):("number"==typeof this.opts.cellHeight&&this.opts.cellHeightUnit&&this.opts.cellHeightUnit!==o.cellHeightUnit&&(this.opts.cellHeight=this.opts.cellHeight+this.opts.cellHeightUnit,delete this.opts.cellHeightUnit),this.cellHeight(this.opts.cellHeight,!1)),"mobile"===this.opts.alwaysShowResizeHandle&&(this.opts.alwaysShowResizeHandle=l),this._styleSheetClass="grid-stack-instance-"+s._idSeq++,this.el.classList.add(this._styleSheetClass),this._setStaticClass();let d=this.opts.engineClass||R.engineClass||s;if(this.engine=new d({column:this.getColumn(),float:this.opts.float,maxRow:this.opts.maxRow,onChange:e=>{let t=0;this.engine.nodes.forEach((e=>{t=Math.max(t,e.y+e.h)})),e.forEach((e=>{let t=e.el;t&&(e._removeDOM?(t&&t.remove(),delete e._removeDOM):this._writePosAttr(t,e))})),this._updateStyles(!1,t)}}),this.opts.auto&&(this.batchUpdate(),this.getGridItems().forEach((e=>this._prepareElement(e))),this.batchUpdate(!1)),this.opts.children){let e=this.opts.children;delete this.opts.children,e.length&&this.load(e)}this.setAnimation(this.opts.animate),this._updateStyles(),this.el.classList.add("grid-stack-"+this.opts.column),this.opts.subGridDynamic&&!r.pauseDrag&&(r.pauseDrag=!0),void 0!==this.opts.draggable?.pause&&(r.pauseDrag=this.opts.draggable.pause),this._setupRemoveDrop(),this._setupAcceptWidget(),this._updateWindowResizeEvent()}addWidget(e,t){let s,o;if("string"==typeof e){let t=document.implementation.createHTMLDocument("");t.body.innerHTML=e,s=t.body.children[0]}else if(0===arguments.length||1===arguments.length&&(void 0!==(n=e).el||void 0!==n.x||void 0!==n.y||void 0!==n.w||void 0!==n.h||void 0!==n.content))if(o=t=e,o?.el)s=o.el;else if(R.addRemoveCB)s=R.addRemoveCB(this.el,t,!0,!1);else{let e=t?.content||"",i=document.implementation.createHTMLDocument("");i.body.innerHTML=`<div class="grid-stack-item ${this.opts.itemClass||""}"><div class="grid-stack-item-content">${e}</div></div>`,s=i.body.children[0]}else s=e;var n;if(!s)return;let r=this._readAttr(s);return t=i.cloneDeep(t)||{},i.defaults(t,r),o=this.engine.prepareNode(t),this._writeAttr(s,t),this._insertNotAppend?this.el.prepend(s):this.el.appendChild(s),this._prepareElement(s,!0,t),this._updateContainerHeight(),o.subGridOpts&&this.makeSubGrid(o.el,o.subGridOpts,void 0,!1),this._prevColumn&&1===this.opts.column&&(this._ignoreLayoutsNodeChange=!0),this._triggerAddEvent(),this._triggerChangeEvent(),delete this._ignoreLayoutsNodeChange,s}makeSubGrid(e,t,s,o=!0){let n,r=e.gridstackNode;if(r||(r=this.makeWidget(e).gridstackNode),r.subGrid?.el)return r.subGrid;let l,h=this;for(;h&&!n;)n=h.opts?.subGridOpts,h=h.parentGridItem?.grid;t=i.cloneDeep({...n||{},children:void 0,...t||r.subGridOpts}),r.subGridOpts=t,"auto"===t.column&&(l=!0,t.column=Math.max(r.w||1,s?.w||1),t.disableOneColumnMode=!0);let a,d,p=r.el.querySelector(".grid-stack-item-content");if(o){if(this._removeDD(r.el),d={...r,x:0,y:0},i.removeInternalForSave(d),delete d.subGridOpts,r.content&&(d.content=r.content,delete r.content),R.addRemoveCB)a=R.addRemoveCB(this.el,d,!0,!1);else{let e=document.implementation.createHTMLDocument("");e.body.innerHTML='<div class="grid-stack-item"></div>',a=e.body.children[0],a.appendChild(p),e.body.innerHTML='<div class="grid-stack-item-content"></div>',p=e.body.children[0],r.el.appendChild(p)}this._prepareDragDropByNode(r)}if(s){let e=l?t.column:r.w,i=r.h+s.h,o=r.el.style;o.transition="none",this.update(r.el,{w:e,h:i}),setTimeout((()=>o.transition=null))}let g=r.subGrid=R.addGrid(p,t);return s?._moving&&(g._isTemp=!0),l&&(g._autoColumn=!0),o&&g.addWidget(a,d),s&&(s._moving?window.setTimeout((()=>i.simulateMouseEvent(s._event,"mouseenter",g.el)),0):g.addWidget(r.el,r)),g}removeAsSubGrid(e){let t=this.parentGridItem?.grid;t&&(t.batchUpdate(),t.removeWidget(this.parentGridItem.el,!0,!0),this.engine.nodes.forEach((e=>{e.x+=this.parentGridItem.x,e.y+=this.parentGridItem.y,t.addWidget(e.el,e)})),t.batchUpdate(!1),this.parentGridItem&&delete this.parentGridItem.subGrid,delete this.parentGridItem,e&&window.setTimeout((()=>i.simulateMouseEvent(e._event,"mouseenter",t.el)),0))}save(e=!0,t=!1,s=R.saveCB){let n=this.engine.save(e,s);if(n.forEach((i=>{if(e&&i.el&&!i.subGrid&&!s){let e=i.el.querySelector(".grid-stack-item-content");i.content=e?e.innerHTML:void 0,i.content||delete i.content}else if(e||s||delete i.content,i.subGrid?.el){const o=i.subGrid.save(e,t,s);i.subGridOpts=t?o:{children:o},delete i.subGrid}delete i.el})),t){let e=i.cloneDeep(this.opts);e.marginBottom===e.marginTop&&e.marginRight===e.marginLeft&&e.marginTop===e.marginRight&&(e.margin=e.marginTop,delete e.marginTop,delete e.marginRight,delete e.marginBottom,delete e.marginLeft),e.rtl===("rtl"===this.el.style.direction)&&(e.rtl="auto"),this._isAutoCellHeight&&(e.cellHeight="auto"),this._autoColumn&&(e.column="auto",delete e.disableOneColumnMode);const t=e._alwaysShowResizeHandle;return delete e._alwaysShowResizeHandle,void 0!==t?e.alwaysShowResizeHandle=t:delete e.alwaysShowResizeHandle,i.removeInternalAndSame(e,o),e.children=n,e}return n}load(e,t=R.addRemoveCB||!0){let i=R.Utils.sort([...e],-1,this._prevColumn||this.getColumn());this._insertNotAppend=!0,this._prevColumn&&this._prevColumn!==this.opts.column&&i.some((e=>e.x+e.w>this.opts.column))&&(this._ignoreLayoutsNodeChange=!0,this.engine.cacheLayout(i,this._prevColumn,!0));const s=R.addRemoveCB;"function"==typeof t&&(R.addRemoveCB=t);let o=[];return this.batchUpdate(),t&&[...this.engine.nodes].forEach((e=>{i.find((t=>e.id===t.id))||(R.addRemoveCB&&R.addRemoveCB(this.el,e,!1,!1),o.push(e),this.removeWidget(e.el,!0,!1))})),i.forEach((e=>{let i=e.id||0===e.id?this.engine.nodes.find((t=>t.id===e.id)):void 0;if(i){if(this.update(i.el,e),e.subGridOpts?.children){let t=i.el.querySelector(".grid-stack");t&&t.gridstack&&(t.gridstack.load(e.subGridOpts.children),this._insertNotAppend=!0)}}else t&&this.addWidget(e)})),this.engine.removedNodes=o,this.batchUpdate(!1),delete this._ignoreLayoutsNodeChange,delete this._insertNotAppend,s?R.addRemoveCB=s:delete R.addRemoveCB,this}batchUpdate(e=!0){return this.engine.batchUpdate(e),e||(this._triggerRemoveEvent(),this._triggerAddEvent(),this._triggerChangeEvent()),this}getCellHeight(e=!1){if(this.opts.cellHeight&&"auto"!==this.opts.cellHeight&&(!e||!this.opts.cellHeightUnit||"px"===this.opts.cellHeightUnit))return this.opts.cellHeight;let t=this.el.querySelector("."+this.opts.itemClass);if(t){let e=i.toNumber(t.getAttribute("gs-h"));return Math.round(t.offsetHeight/e)}let s=parseInt(this.el.getAttribute("gs-current-row"));return s?Math.round(this.el.getBoundingClientRect().height/s):this.opts.cellHeight}cellHeight(e,t=!0){if(t&&void 0!==e&&this._isAutoCellHeight!==("auto"===e)&&(this._isAutoCellHeight="auto"===e,this._updateWindowResizeEvent()),"initial"!==e&&"auto"!==e||(e=void 0),void 0===e){let t=-this.opts.marginRight-this.opts.marginLeft+this.opts.marginTop+this.opts.marginBottom;e=this.cellWidth()+t}let s=i.parseHeight(e);return this.opts.cellHeightUnit===s.unit&&this.opts.cellHeight===s.h||(this.opts.cellHeightUnit=s.unit,this.opts.cellHeight=s.h,t&&this._updateStyles(!0)),this}cellWidth(){return this._widthOrContainer()/this.getColumn()}_widthOrContainer(){return this.el.clientWidth||this.el.parentElement.clientWidth||window.innerWidth}compact(){return this.engine.compact(),this._triggerChangeEvent(),this}column(e,t="moveScale"){if(e<1||this.opts.column===e)return this;let i,s=this.getColumn();return 1===e?this._prevColumn=s:delete this._prevColumn,this.el.classList.remove("grid-stack-"+s),this.el.classList.add("grid-stack-"+e),this.opts.column=this.engine.column=e,1===e&&this.opts.oneColumnModeDomSort&&(i=[],this.getGridItems().forEach((e=>{e.gridstackNode&&i.push(e.gridstackNode)})),i.length||(i=void 0)),this.engine.updateNodeWidths(s,e,i,t),this._isAutoCellHeight&&this.cellHeight(),this._ignoreLayoutsNodeChange=!0,this._triggerChangeEvent(),delete this._ignoreLayoutsNodeChange,this}getColumn(){return this.opts.column}getGridItems(){return Array.from(this.el.children).filter((e=>e.matches("."+this.opts.itemClass)&&!e.matches("."+this.opts.placeholderClass)))}destroy(e=!0){if(this.el)return this.offAll(),this._updateWindowResizeEvent(!0),this.setStatic(!0,!1),this.setAnimation(!1),e?this.el.parentNode.removeChild(this.el):(this.removeAll(e),this.el.classList.remove(this._styleSheetClass),this.el.removeAttribute("gs-current-row")),this._removeStylesheet(),this.parentGridItem&&delete this.parentGridItem.subGrid,delete this.parentGridItem,delete this.opts,delete this._placeholder,delete this.engine,delete this.el.gridstack,delete this.el,this}float(e){return this.opts.float!==e&&(this.opts.float=this.engine.float=e,this._triggerChangeEvent()),this}getFloat(){return this.engine.float}getCellFromPixel(e,t=!1){let i,s=this.el.getBoundingClientRect();i=t?{top:s.top+document.documentElement.scrollTop,left:s.left}:{top:this.el.offsetTop,left:this.el.offsetLeft};let o=e.left-i.left,n=e.top-i.top,r=s.width/this.getColumn(),l=s.height/parseInt(this.el.getAttribute("gs-current-row"));return{x:Math.floor(o/r),y:Math.floor(n/l)}}getRow(){return Math.max(this.engine.getRow(),this.opts.minRow)}isAreaEmpty(e,t,i,s){return this.engine.isAreaEmpty(e,t,i,s)}makeWidget(e){let t=R.getElement(e);return this._prepareElement(t,!0),this._updateContainerHeight(),this._triggerAddEvent(),this._triggerChangeEvent(),t}on(e,t){if(-1!==e.indexOf(" "))return e.split(" ").forEach((e=>this.on(e,t))),this;if("change"===e||"added"===e||"removed"===e||"enable"===e||"disable"===e){let i="enable"===e||"disable"===e;this._gsEventHandler[e]=i?e=>t(e):e=>t(e,e.detail),this.el.addEventListener(e,this._gsEventHandler[e])}else"drag"===e||"dragstart"===e||"dragstop"===e||"resizestart"===e||"resize"===e||"resizestop"===e||"dropped"===e?this._gsEventHandler[e]=t:console.log("GridStack.on("+e+') event not supported, but you can still use $(".grid-stack").on(...) while jquery-ui is still used internally.');return this}off(e){return-1!==e.indexOf(" ")?(e.split(" ").forEach((e=>this.off(e))),this):("change"!==e&&"added"!==e&&"removed"!==e&&"enable"!==e&&"disable"!==e||this._gsEventHandler[e]&&this.el.removeEventListener(e,this._gsEventHandler[e]),delete this._gsEventHandler[e],this)}offAll(){return Object.keys(this._gsEventHandler).forEach((e=>this.off(e))),this}removeWidget(e,t=!0,i=!0){return R.getElements(e).forEach((e=>{if(e.parentElement&&e.parentElement!==this.el)return;let s=e.gridstackNode;s||(s=this.engine.nodes.find((t=>e===t.el))),s&&(delete e.gridstackNode,this._removeDD(e),this.engine.removeNode(s,t,i),t&&e.parentElement&&e.remove())})),i&&(this._triggerRemoveEvent(),this._triggerChangeEvent()),this}removeAll(e=!0){return this.engine.nodes.forEach((e=>{delete e.el.gridstackNode,this._removeDD(e.el)})),this.engine.removeAll(e),this._triggerRemoveEvent(),this}setAnimation(e){return e?this.el.classList.add("grid-stack-animate"):this.el.classList.remove("grid-stack-animate"),this}setStatic(e,t=!0,i=!0){return this.opts.staticGrid===e||(this.opts.staticGrid=e,this._setupRemoveDrop(),this._setupAcceptWidget(),this.engine.nodes.forEach((s=>{this._prepareDragDropByNode(s),s.subGrid&&i&&s.subGrid.setStatic(e,t,i)})),t&&this._setStaticClass()),this}update(e,t){if(arguments.length>2){console.warn("gridstack.ts: `update(el, x, y, w, h)` is deprecated. Use `update(el, {x, w, content, ...})`. It will be removed soon");let i=arguments,s=1;return t={x:i[s++],y:i[s++],w:i[s++],h:i[s++]},this.update(e,t)}return R.getElements(e).forEach((e=>{if(!e||!e.gridstackNode)return;let s=e.gridstackNode,o=i.cloneDeep(t);delete o.autoPosition;let n,r=["x","y","w","h"];if(r.some((e=>void 0!==o[e]&&o[e]!==s[e]))&&(n={},r.forEach((e=>{n[e]=void 0!==o[e]?o[e]:s[e],delete o[e]}))),!n&&(o.minW||o.minH||o.maxW||o.maxH)&&(n={}),o.content){let t=e.querySelector(".grid-stack-item-content");t&&t.innerHTML!==o.content&&(t.innerHTML=o.content),delete o.content}let l=!1,h=!1;for(const e in o)"_"!==e[0]&&s[e]!==o[e]&&(s[e]=o[e],l=!0,h=h||!this.opts.staticGrid&&("noResize"===e||"noMove"===e||"locked"===e));i.sanitizeMinMax(s),n&&(this.engine.cleanNodes().beginUpdate(s).moveNode(s,n),this._updateContainerHeight(),this._triggerChangeEvent(),this.engine.endUpdate()),l&&this._writeAttr(e,s),h&&this._prepareDragDropByNode(s)})),this}margin(e){if(!("string"==typeof e&&e.split(" ").length>1)){let t=i.parseHeight(e);if(this.opts.marginUnit===t.unit&&this.opts.margin===t.h)return}return this.opts.margin=e,this.opts.marginTop=this.opts.marginBottom=this.opts.marginLeft=this.opts.marginRight=void 0,this._initMargin(),this._updateStyles(!0),this}getMargin(){return this.opts.margin}willItFit(e){if(arguments.length>1){console.warn("gridstack.ts: `willItFit(x,y,w,h,autoPosition)` is deprecated. Use `willItFit({x, y,...})`. It will be removed soon");let e=arguments,t=0,i={x:e[t++],y:e[t++],w:e[t++],h:e[t++],autoPosition:e[t++]};return this.willItFit(i)}return this.engine.willItFit(e)}_triggerChangeEvent(){if(this.engine.batchMode)return this;let e=this.engine.getDirtyNodes(!0);return e&&e.length&&(this._ignoreLayoutsNodeChange||this.engine.layoutsNodesChange(e),this._triggerEvent("change",e)),this.engine.saveInitial(),this}_triggerAddEvent(){return this.engine.batchMode||this.engine.addedNodes?.length&&(this._ignoreLayoutsNodeChange||this.engine.layoutsNodesChange(this.engine.addedNodes),this.engine.addedNodes.forEach((e=>{delete e._dirty})),this._triggerEvent("added",this.engine.addedNodes),this.engine.addedNodes=[]),this}_triggerRemoveEvent(){return this.engine.batchMode||this.engine.removedNodes?.length&&(this._triggerEvent("removed",this.engine.removedNodes),this.engine.removedNodes=[]),this}_triggerEvent(e,t){let i=t?new CustomEvent(e,{bubbles:!1,detail:t}):new Event(e);return this.el.dispatchEvent(i),this}_removeStylesheet(){return this._styles&&(i.removeStylesheet(this._styleSheetClass),delete this._styles),this}_updateStyles(e=!1,t){if(e&&this._removeStylesheet(),t||(t=this.getRow()),this._updateContainerHeight(),0===this.opts.cellHeight)return this;let s=this.opts.cellHeight,o=this.opts.cellHeightUnit,n=`.${this._styleSheetClass} > .${this.opts.itemClass}`;if(!this._styles){let e=this.opts.styleInHead?void 0:this.el.parentNode;if(this._styles=i.createStylesheet(this._styleSheetClass,e,{nonce:this.opts.nonce}),!this._styles)return this;this._styles._max=0,i.addCSSRule(this._styles,n,`min-height: ${s}${o}`);let t=this.opts.marginTop+this.opts.marginUnit,r=this.opts.marginBottom+this.opts.marginUnit,l=this.opts.marginRight+this.opts.marginUnit,h=this.opts.marginLeft+this.opts.marginUnit,a=`${n} > .grid-stack-item-content`,d=`.${this._styleSheetClass} > .grid-stack-placeholder > .placeholder-content`;i.addCSSRule(this._styles,a,`top: ${t}; right: ${l}; bottom: ${r}; left: ${h};`),i.addCSSRule(this._styles,d,`top: ${t}; right: ${l}; bottom: ${r}; left: ${h};`),i.addCSSRule(this._styles,`${n} > .ui-resizable-ne`,`right: ${l}`),i.addCSSRule(this._styles,`${n} > .ui-resizable-e`,`right: ${l}`),i.addCSSRule(this._styles,`${n} > .ui-resizable-se`,`right: ${l}; bottom: ${r}`),i.addCSSRule(this._styles,`${n} > .ui-resizable-nw`,`left: ${h}`),i.addCSSRule(this._styles,`${n} > .ui-resizable-w`,`left: ${h}`),i.addCSSRule(this._styles,`${n} > .ui-resizable-sw`,`left: ${h}; bottom: ${r}`)}if((t=t||this._styles._max)>this._styles._max){let e=e=>s*e+o;for(let s=this._styles._max+1;s<=t;s++){let t=e(s);i.addCSSRule(this._styles,`${n}[gs-y="${s-1}"]`,`top: ${e(s-1)}`),i.addCSSRule(this._styles,`${n}[gs-h="${s}"]`,`height: ${t}`)}this._styles._max=t}return this}_updateContainerHeight(){if(!this.engine||this.engine.batchMode)return this;let e=this.getRow()+this._extraDragRow;if(this.el.setAttribute("gs-current-row",String(e)),0===e)return this.el.style.removeProperty("min-height"),this;let t=this.opts.cellHeight,i=this.opts.cellHeightUnit;return t?(this.el.style.minHeight=e*t+i,this):this}_prepareElement(e,t=!1,s){e.classList.add(this.opts.itemClass),s=s||this._readAttr(e),e.gridstackNode=s,s.el=e,s.grid=this;let o={...s};return s=this.engine.addNode(s,t),i.same(s,o)||this._writeAttr(e,s),this._prepareDragDropByNode(s),this}_writePosAttr(e,t){return void 0!==t.x&&null!==t.x&&e.setAttribute("gs-x",String(t.x)),void 0!==t.y&&null!==t.y&&e.setAttribute("gs-y",String(t.y)),t.w>1?e.setAttribute("gs-w",String(t.w)):e.removeAttribute("gs-w"),t.h>1?e.setAttribute("gs-h",String(t.h)):e.removeAttribute("gs-h"),this}_writeAttr(e,t){if(!t)return this;this._writePosAttr(e,t);let i={autoPosition:"gs-auto-position",noResize:"gs-no-resize",noMove:"gs-no-move",locked:"gs-locked",id:"gs-id"};for(const s in i)t[s]?e.setAttribute(i[s],String(t[s])):e.removeAttribute(i[s]);return this}_readAttr(e){let t={};t.x=i.toNumber(e.getAttribute("gs-x")),t.y=i.toNumber(e.getAttribute("gs-y")),t.w=i.toNumber(e.getAttribute("gs-w")),t.h=i.toNumber(e.getAttribute("gs-h")),t.w>1||e.removeAttribute("gs-w"),t.h>1||e.removeAttribute("gs-h"),t.autoPosition=i.toBool(e.getAttribute("gs-auto-position")),t.noResize=i.toBool(e.getAttribute("gs-no-resize")),t.noMove=i.toBool(e.getAttribute("gs-no-move")),t.locked=i.toBool(e.getAttribute("gs-locked")),t.id=e.getAttribute("gs-id"),t.maxW=i.toNumber(e.getAttribute("gs-max-w")),t.maxW&&e.removeAttribute("gs-max-w"),t.minW=i.toNumber(e.getAttribute("gs-min-w")),t.minW&&e.removeAttribute("gs-min-w"),t.maxH=i.toNumber(e.getAttribute("gs-max-h")),t.maxH&&e.removeAttribute("gs-max-h"),t.minH=i.toNumber(e.getAttribute("gs-min-h")),t.minH&&e.removeAttribute("gs-min-h");for(const e in t){if(!t.hasOwnProperty(e))return;t[e]||0===t[e]||delete t[e]}return t}_setStaticClass(){let e=["grid-stack-static"];return this.opts.staticGrid?(this.el.classList.add(...e),this.el.setAttribute("gs-static","true")):(this.el.classList.remove(...e),this.el.removeAttribute("gs-static")),this}onParentResize(){if(!this.el||!this.el.clientWidth)return;let e=!1;if(this._autoColumn&&this.parentGridItem)this.opts.column!==this.parentGridItem.w&&(e=!0,this.column(this.parentGridItem.w,"none"));else{let t=!this.opts.disableOneColumnMode&&this.el.clientWidth<=this.opts.oneColumnSize;1===this.opts.column!==t&&(e=!0,this.opts.animate&&this.setAnimation(!1),this.column(t?1:this._prevColumn),this.opts.animate&&this.setAnimation(!0))}return this._isAutoCellHeight&&(!e&&this.opts.cellHeightThrottle?(this._cellHeightThrottle||(this._cellHeightThrottle=i.throttle((()=>this.cellHeight()),this.opts.cellHeightThrottle)),this._cellHeightThrottle()):this.cellHeight()),this.engine.nodes.forEach((e=>{e.subGrid&&e.subGrid.onParentResize()})),this}_updateWindowResizeEvent(e=!1){const t=(this._isAutoCellHeight||!this.opts.disableOneColumnMode)&&!this.parentGridItem;return e||!t||this._windowResizeBind?!e&&t||!this._windowResizeBind||(window.removeEventListener("resize",this._windowResizeBind),delete this._windowResizeBind):(this._windowResizeBind=this.onParentResize.bind(this),window.addEventListener("resize",this._windowResizeBind)),this}static getElement(e=".grid-stack-item"){return i.getElement(e)}static getElements(e=".grid-stack-item"){return i.getElements(e)}static getGridElement(e){return R.getElement(e)}static getGridElements(e){return i.getElements(e)}_initMargin(){let e,t=0,s=[];return"string"==typeof this.opts.margin&&(s=this.opts.margin.split(" ")),2===s.length?(this.opts.marginTop=this.opts.marginBottom=s[0],this.opts.marginLeft=this.opts.marginRight=s[1]):4===s.length?(this.opts.marginTop=s[0],this.opts.marginRight=s[1],this.opts.marginBottom=s[2],this.opts.marginLeft=s[3]):(e=i.parseHeight(this.opts.margin),this.opts.marginUnit=e.unit,t=this.opts.margin=e.h),void 0===this.opts.marginTop?this.opts.marginTop=t:(e=i.parseHeight(this.opts.marginTop),this.opts.marginTop=e.h,delete this.opts.margin),void 0===this.opts.marginBottom?this.opts.marginBottom=t:(e=i.parseHeight(this.opts.marginBottom),this.opts.marginBottom=e.h,delete this.opts.margin),void 0===this.opts.marginRight?this.opts.marginRight=t:(e=i.parseHeight(this.opts.marginRight),this.opts.marginRight=e.h,delete this.opts.margin),void 0===this.opts.marginLeft?this.opts.marginLeft=t:(e=i.parseHeight(this.opts.marginLeft),this.opts.marginLeft=e.h,delete this.opts.margin),this.opts.marginUnit=e.unit,this.opts.marginTop===this.opts.marginBottom&&this.opts.marginLeft===this.opts.marginRight&&this.opts.marginTop===this.opts.marginRight&&(this.opts.margin=this.opts.marginTop),this}static getDD(){return x}static setupDragIn(e,t,s=document){void 0!==t?.pause&&(r.pauseDrag=t.pause),t={...n,...t||{}};let o="string"==typeof e?i.getElements(e,s):e;o.length&&o?.forEach((e=>{x.isDraggable(e)||x.dragIn(e,t)}))}movable(e,t){return this.opts.staticGrid||R.getElements(e).forEach((e=>{let i=e.gridstackNode;i&&(t?delete i.noMove:i.noMove=!0,this._prepareDragDropByNode(i))})),this}resizable(e,t){return this.opts.staticGrid||R.getElements(e).forEach((e=>{let i=e.gridstackNode;i&&(t?delete i.noResize:i.noResize=!0,this._prepareDragDropByNode(i))})),this}disable(e=!0){if(!this.opts.staticGrid)return this.enableMove(!1,e),this.enableResize(!1,e),this._triggerEvent("disable"),this}enable(e=!0){if(!this.opts.staticGrid)return this.enableMove(!0,e),this.enableResize(!0,e),this._triggerEvent("enable"),this}enableMove(e,t=!0){return this.opts.staticGrid||(this.opts.disableDrag=!e,this.engine.nodes.forEach((i=>{this.movable(i.el,e),i.subGrid&&t&&i.subGrid.enableMove(e,t)}))),this}enableResize(e,t=!0){return this.opts.staticGrid||(this.opts.disableResize=!e,this.engine.nodes.forEach((i=>{this.resizable(i.el,e),i.subGrid&&t&&i.subGrid.enableResize(e,t)}))),this}_removeDD(e){return x.draggable(e,"destroy").resizable(e,"destroy"),e.gridstackNode&&delete e.gridstackNode._initDD,delete e.ddElement,this}_setupAcceptWidget(){if(this.opts.staticGrid||!this.opts.acceptWidgets&&!this.opts.removable)return x.droppable(this.el,"destroy"),this;let e,t,s=(s,o,n)=>{let r=o.gridstackNode;if(!r)return;n=n||o;let l=this.el.getBoundingClientRect(),{top:h,left:a}=n.getBoundingClientRect();a-=l.left,h-=l.top;let d={position:{top:h,left:a}};if(r._temporaryRemoved){if(r.x=Math.max(0,Math.round(a/t)),r.y=Math.max(0,Math.round(h/e)),delete r.autoPosition,this.engine.nodeBoundFix(r),!this.engine.willItFit(r)){if(r.autoPosition=!0,!this.engine.willItFit(r))return void x.off(o,"drag");r._willFitPos&&(i.copyPos(r,r._willFitPos),delete r._willFitPos)}this._onStartMoving(n,s,d,r,t,e)}else this._dragOrResize(n,s,d,r,t,e)};return x.droppable(this.el,{accept:e=>{let t=e.gridstackNode;if(t?.grid===this)return!0;if(!this.opts.acceptWidgets)return!1;let i=!0;if("function"==typeof this.opts.acceptWidgets)i=this.opts.acceptWidgets(e);else{let t=!0===this.opts.acceptWidgets?".grid-stack-item":this.opts.acceptWidgets;i=e.matches(t)}if(i&&t&&this.opts.maxRow){let e={w:t.w,h:t.h,minW:t.minW,minH:t.minH};i=this.engine.willItFit(e)}return i}}).on(this.el,"dropover",((i,o,n)=>{let r=o.gridstackNode;if(r?.grid===this&&!r._temporaryRemoved)return!1;r?.grid&&r.grid!==this&&!r._temporaryRemoved&&r.grid._leave(o,n),t=this.cellWidth(),e=this.getCellHeight(!0),r||(r=this._readAttr(o)),r.grid||(r._isExternal=!0,o.gridstackNode=r),n=n||o;let l=r.w||Math.round(n.offsetWidth/t)||1,h=r.h||Math.round(n.offsetHeight/e)||1;return r.grid&&r.grid!==this?(o._gridstackNodeOrig||(o._gridstackNodeOrig=r),o.gridstackNode=r={...r,w:l,h,grid:this},this.engine.cleanupNode(r).nodeBoundFix(r),r._initDD=r._isExternal=r._temporaryRemoved=!0):(r.w=l,r.h=h,r._temporaryRemoved=!0),this._itemRemoving(r.el,!1),x.on(o,"drag",s),s(i,o,n),!1})).on(this.el,"dropout",((e,t,i)=>{let s=t.gridstackNode;return!!s&&(s.grid&&s.grid!==this||(this._leave(t,i),this._isTemp&&this.removeAsSubGrid(s)),!1)})).on(this.el,"drop",((e,t,s)=>{let n=t.gridstackNode;if(n?.grid===this&&!n._isExternal)return!1;let r=!!this.placeholder.parentElement;this.placeholder.remove();let l=t._gridstackNodeOrig;if(delete t._gridstackNodeOrig,r&&l?.grid&&l.grid!==this){let e=l.grid;e.engine.removedNodes.push(l),e._triggerRemoveEvent()._triggerChangeEvent(),e.parentGridItem&&!e.engine.nodes.length&&e.opts.subGridDynamic&&e.removeAsSubGrid()}if(!n)return!1;if(r&&(this.engine.cleanupNode(n),n.grid=this),x.off(t,"drag"),s!==t?(s.remove(),t.gridstackNode=l,r&&(t=t.cloneNode(!0))):(t.remove(),this._removeDD(t)),!r)return!1;t.gridstackNode=n,n.el=t;let h=n.subGrid?.el?.gridstack;return i.copyPos(n,this._readAttr(this.placeholder)),i.removePositioningStyles(t),this._writeAttr(t,n),t.classList.add(o.itemClass,this.opts.itemClass),this.el.appendChild(t),h&&(h.parentGridItem=n,h.opts.styleInHead||h._updateStyles(!0)),this._updateContainerHeight(),this.engine.addedNodes.push(n),this._triggerAddEvent(),this._triggerChangeEvent(),this.engine.endUpdate(),this._gsEventHandler.dropped&&this._gsEventHandler.dropped({...e,type:"dropped"},l&&l.grid?l:void 0,n),window.setTimeout((()=>{n.el&&n.el.parentElement?this._prepareDragDropByNode(n):this.engine.removeNode(n),delete n.grid._isTemp})),!1})),this}_itemRemoving(e,t){let i=e?e.gridstackNode:void 0;i&&i.grid&&(t?i._isAboutToRemove=!0:delete i._isAboutToRemove,t?e.classList.add("grid-stack-item-removing"):e.classList.remove("grid-stack-item-removing"))}_setupRemoveDrop(){if(!this.opts.staticGrid&&"string"==typeof this.opts.removable){let e=document.querySelector(this.opts.removable);if(!e)return this;x.isDroppable(e)||x.droppable(e,this.opts.removableOptions).on(e,"dropover",((e,t)=>this._itemRemoving(t,!0))).on(e,"dropout",((e,t)=>this._itemRemoving(t,!1)))}return this}_prepareDragDropByNode(e){let t=e.el;const s=e.noMove||this.opts.disableDrag,o=e.noResize||this.opts.disableResize;if(this.opts.staticGrid||s&&o)return e._initDD&&(this._removeDD(t),delete e._initDD),t.classList.add("ui-draggable-disabled","ui-resizable-disabled"),this;if(!e._initDD){let s,o,n=(i,n)=>{this._gsEventHandler[i.type]&&this._gsEventHandler[i.type](i,i.target),s=this.cellWidth(),o=this.getCellHeight(!0),this._onStartMoving(t,i,n,e,s,o)},r=(i,n)=>{this._dragOrResize(t,i,n,e,s,o)},l=s=>{this.placeholder.remove(),delete e._moving,delete e._event,delete e._lastTried;let o=s.target;if(o.gridstackNode&&o.gridstackNode.grid===this){if(e.el=o,e._isAboutToRemove){let i=t.gridstackNode.grid;i._gsEventHandler[s.type]&&i._gsEventHandler[s.type](s,o),this._removeDD(t),i.engine.removedNodes.push(e),i._triggerRemoveEvent(),delete t.gridstackNode,delete e.el,t.remove()}else i.removePositioningStyles(o),e._temporaryRemoved?(i.copyPos(e,e._orig),this._writePosAttr(o,e),this.engine.addNode(e)):this._writePosAttr(o,e),this._gsEventHandler[s.type]&&this._gsEventHandler[s.type](s,o);this._extraDragRow=0,this._updateContainerHeight(),this._triggerChangeEvent(),this.engine.endUpdate()}};x.draggable(t,{start:n,stop:l,drag:r}).resizable(t,{start:n,stop:l,resize:r}),e._initDD=!0}return x.draggable(t,s?"disable":"enable").resizable(t,o?"disable":"enable"),this}_onStartMoving(e,t,i,s,o,n){this.engine.cleanNodes().beginUpdate(s),this._writePosAttr(this.placeholder,s),this.el.appendChild(this.placeholder),s.el=this.placeholder,s._lastUiPosition=i.position,s._prevYPix=i.position.top,s._moving="dragstart"===t.type,delete s._lastTried,"dropover"===t.type&&s._temporaryRemoved&&(this.engine.addNode(s),s._moving=!0),this.engine.cacheRects(o,n,this.opts.marginTop,this.opts.marginRight,this.opts.marginBottom,this.opts.marginLeft),"resizestart"===t.type&&(x.resizable(e,"option","minWidth",o*(s.minW||1)).resizable(e,"option","minHeight",n*(s.minH||1)),s.maxW&&x.resizable(e,"option","maxWidth",o*s.maxW),s.maxH&&x.resizable(e,"option","maxHeight",n*s.maxH))}_dragOrResize(e,t,s,o,n,r){let l,h={...o._orig},a=this.opts.marginLeft,d=this.opts.marginRight,p=this.opts.marginTop,g=this.opts.marginBottom,u=Math.round(.1*r),c=Math.round(.1*n);if(a=Math.min(a,c),d=Math.min(d,c),p=Math.min(p,u),g=Math.min(g,u),"drag"===t.type){if(o._temporaryRemoved)return;let t=s.position.top-o._prevYPix;o._prevYPix=s.position.top,!1!==this.opts.draggable.scroll&&i.updateScrollPosition(e,s.position,t);let l=s.position.left+(s.position.left>o._lastUiPosition.left?-d:a),u=s.position.top+(s.position.top>o._lastUiPosition.top?-g:p);h.x=Math.round(l/n),h.y=Math.round(u/r);let c=this._extraDragRow;if(this.engine.collide(o,h)){let e=this.getRow(),t=Math.max(0,h.y+o.h-e);this.opts.maxRow&&e+t>this.opts.maxRow&&(t=Math.max(0,this.opts.maxRow-e)),this._extraDragRow=t}else this._extraDragRow=0;if(this._extraDragRow!==c&&this._updateContainerHeight(),o.x===h.x&&o.y===h.y)return}else if("resize"===t.type){if(h.x<0)return;if(i.updateScrollResize(t,e,r),h.w=Math.round((s.size.width-a)/n),h.h=Math.round((s.size.height-p)/r),o.w===h.w&&o.h===h.h)return;if(o._lastTried&&o._lastTried.w===h.w&&o._lastTried.h===h.h)return;let d=s.position.left+a,g=s.position.top+p;h.x=Math.round(d/n),h.y=Math.round(g/r),l=!0}o._event=t,o._lastTried=h;let m={x:s.position.left+a,y:s.position.top+p,w:(s.size?s.size.width:o.w*n)-a-d,h:(s.size?s.size.height:o.h*r)-p-g};if(this.engine.moveNodeCheck(o,{...h,cellWidth:n,cellHeight:r,rect:m,resizing:l})){o._lastUiPosition=s.position,this.engine.cacheRects(n,r,p,d,g,a),delete o._skipDown,l&&o.subGrid&&o.subGrid.onParentResize(),this._extraDragRow=0,this._updateContainerHeight();let e=t.target;this._writePosAttr(e,o),this._gsEventHandler[t.type]&&this._gsEventHandler[t.type](t,e)}}_leave(e,t){let i=e.gridstackNode;i&&(x.off(e,"drag"),i._temporaryRemoved||(i._temporaryRemoved=!0,this.engine.removeNode(i),i.el=i._isExternal&&t?t:e,!0===this.opts.removable&&this._itemRemoving(e,!0),e._gridstackNodeOrig?(e.gridstackNode=e._gridstackNodeOrig,delete e._gridstackNodeOrig):i._isExternal&&(delete i.el,delete e.gridstackNode,this.engine.restoreInitial())))}commit(){return this.batchUpdate(!1).prototype,this}}return R.Utils=i,R.Engine=s,R.GDRev="8.1.0",t.GridStack})()));
//# sourceMappingURL=gridstack-all.js.map