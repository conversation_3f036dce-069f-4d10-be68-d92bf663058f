import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import alias from '@rollup/plugin-alias'
import vueJsx from '@vitejs/plugin-vue-jsx'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import Icons from 'unplugin-icons/vite'
import MonacoEditorPlugin from 'vite-plugin-monaco-editor'
const rootPath = resolve(__dirname)
import { createHtmlPlugin } from 'vite-plugin-html'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: tag => false,
          },
        },
      }),
      vueJsx(),
      react(),
      MonacoEditorPlugin({
        // 配置需要的语言
        languageWorkers: ['editorWorkerService', 'json', 'css', 'html', 'typescript'],
        // 自定义 worker 配置
        customWorkers: [
          {
            label: 'json',
            entry: 'monaco-editor/esm/vs/language/json/json.worker',
          },
          {
            label: 'css',
            entry: 'monaco-editor/esm/vs/language/css/css.worker',
          },
          {
            label: 'html',
            entry: 'monaco-editor/esm/vs/language/html/html.worker',
          },
          {
            label: 'typescript',
            entry: 'monaco-editor/esm/vs/language/typescript/ts.worker',
          },
          {
            label: 'editorWorkerService',
            entry: 'monaco-editor/esm/vs/editor/editor.worker',
          },
        ],
      }),
      createHtmlPlugin({
        minify: true,
        template: 'public/wms.html',
        inject: {
          data: {
            title: env.APP_NAME,
          },
        },
      }),
      alias({
        entries: [
          {
            find: '/admin',
            replacement: resolve(rootPath, 'resources/admin'),
          },
          {
            find: '/frontend',
            replacement: resolve(rootPath, 'resources/frontend'),
          },
          {
            find: '@/module',
            replacement: resolve(rootPath, 'Modules'),
          },
          {
            find: '@/public',
            replacement: resolve(rootPath, 'public'),
          },
          {
            find: '@admin',
            replacement: resolve(rootPath, 'resources/admin/'),
          },
          {
            find: '@/frontend',
            replacement: resolve(rootPath, 'resources/frontend/'),
          },
        ],
      }),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
        // resolvers: [ ElementPlusResolver({importStyle: 'sass'}) ]
      }),
      Components({
        dirs: ['resources/admin/components/', 'resources/admin/layout/'],

        extensions: ['vue'],

        deep: true,

        dts: true,

        include: [/\.vue$/, /\.vue\?vue/],

        exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/, /[\\/]\.nuxt[\\/]/],
        // resolvers: [ ElementPlusResolver({ importStyle: 'sass'}) ]
      }),
      // 暂时禁用 Icons 插件以解决 ESM/CJS 兼容性问题
      // TODO: 等待 unplugin-icons 更新后恢复
      // Icons({
      //   compiler: 'vue3',
      //   autoInstall: true,
      // }),
    ],

    publicDir: './public',
    define: {
      'process.env.BASE_URL': JSON.stringify(env.VITE_BASE_URL),
      'process.env.ASSET_URL': JSON.stringify(env.VITE_ASSET_URL),
      'process.env.NODE_ENV': JSON.stringify(mode === 'development' ? 'development' : 'production'),
    },
    css: {
      preprocessorOptions: {
        scss: {
          implementation: require('sass'),
          api: 'modern',
          sassOptions: {
            silenceDeprecations: ['legacy-js-api'],
          },
        },
      },
    },
    server: {
      host: '127.0.0.1',
      port: 8002,
      open: true, // 自动打开浏览器
      cors: true, // 允许跨域
      strictPort: false, // 端口占用直接退出
      hmr: true,
      fs: {
        allow: ['./'],
      },
      // 配置静态文件服务
      proxy: {
        '/Vendor': {
          target: 'http://127.0.0.1:8000',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/Vendor/, '/Vendor'),
        },
      },
    },
    build: {
      chunkSizeWarningLimit: 20000,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          pure_funcs: ['console.log', 'console.info'],
          drop_debugger: true,
        },
      },
      // emptyOutDir: false,
      outDir: 'public/wms',
      assetsDir: 'assets',
      rollupOptions: {
        input: './public/wms.html',
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',

          entryFileNames: 'assets/js/[name]-[hash].js',

          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',

          globals: {
            // 定义全局变量
            process: 'process',
          },
          manualChunks: {
            // 将 worker 文件打包到 assets/js/vs 目录下
            'vs/language/json/jsonWorker': ['monaco-editor/esm/vs/language/json/json.worker'],
            'vs/language/css/cssWorker': ['monaco-editor/esm/vs/language/css/css.worker'],
            'vs/language/html/htmlWorker': ['monaco-editor/esm/vs/language/html/html.worker'],
            'vs/language/typescript/tsWorker': ['monaco-editor/esm/vs/language/typescript/ts.worker'],
            'vs/editor/editor.worker': ['monaco-editor/esm/vs/editor/editor.worker'],
          },
        },
      },
    },
    optimizeDeps: {
      exclude: ['amis'], // 排除 amis 包以避免 eval 警告
    },
  }
})
