$(document).ready(function(){$.when($("head").append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/9.0.7/css/intlTelInput.css" type="text/css" />'),$.getScript("//cdnjs.cloudflare.com/ajax/libs/intl-tel-input/9.0.7/js/intlTelInput.min.js"),$.getScript("//cdnjs.cloudflare.com/ajax/libs/intl-tel-input/9.0.7/js/utils.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("input[type=tel]").each(function(){var that=this;var altID=this.id+"_alt";$(this).after($(this).clone().attr("id",altID).attr("name",altID)).hide();$("#"+altID).change(function(){$(that).val($(this).intlTelInput("getNumber"))}).intlTelInput()});$("body").css("padding-bottom","140px");$(".intl-tel-input").css("display","inherit")})});