const zh_CN = {
    system: {
        name: 'BWMS 管理系统',
        chinese: '中文',
        english: '英文',
        confirm: '确定',
        cancel: '取消',
        warning: '警告',
        next: '下一页',
        prev: '上一页',
        yes: '是',
        no: '否',
        add: '新增',
        edit: '编辑',
        finish: '完成',
        back: '返回',
        update: '更新',
        export: '导出',
        search: '搜索',
        refresh: '重置',
        detail: '详情',
        delete: '删除',
        prompt: '提示',
        more: '更多',
        logout: '登出',
        fail: '失败',
        success: '成功',
        close: '关闭',
        download: '下载',
        lang: {
            en: '英文',
            zh_HK: '繁体中文',
            zh_CN: '简体中文',
        },
    },

    login: {
        email: '邮箱',
        password: '密码',
        sign_in: '登录',
        welcome: '👏欢迎回来',
        lost_password: '忘记密码?',
        remember: '记住我',
        verify: {
            email: {
                required: '请先输入邮箱',
                invalid: '邮箱地址无效',
            },

            password: {
                required: '请先输入密码',
            },
        },
    },

    register: {
        sign_up: '注册',
    },
    generate: {
        schema: {
            title: '创建数据表',
            name: '表名称',
            name_verify: '请输入表名称',
            engine: {
                name: '表引擎',
                verify: '请选择表引擎',
                placeholder: '选择表引擎',
            },
            default_field: {
                name: '默认字段',
                created_at: '创建时间',
                updated_at: '更新时间',
                creator: '创建人',
                delete_at: '软删除',
            },
            comment: {
                name: '表注释',
                verify: '请填写表注释/说明',
            },

            structure: {
                title: '创建数据结构',
                field_name: {
                    name: '字段名称',
                    verify: '请填写字段名称',
                },
                length: '长度',
                type: {
                    name: '类型',
                    placeholder: '选择字段类型',
                    verify: '请先选择字段类型',
                },
                form_label: '表单 Label',
                form_component: '表单组件',
                list: '列表',
                form: '表单',
                unique: '唯一',
                search: '查询',
                search_op: {
                    name: '搜索操作符',
                    placeholder: '选择搜索操作符',
                },
                nullable: 'nullable',
                default: '默认值',
                rules: {
                    name: '验证规则',
                    placeholder: '选择验证规则',
                },
                operate: '操作',
                comment: '字段注释',
            },
        },
        code: {
            title: '生成代码',
            module: {
                name: '模块',
                placeholder: '请选择模块',
                verify: '请选择模块',
            },
            controller: {
                name: '控制器',
                placeholder: '请输入控制器名称',
                verify: '请输入控制器名称',
            },
            model: {
                name: '模型',
                placeholder: '请输入模型名称',
                verify: '请输入模型名称',
            },
            paginate: '分页',
            menu: {
                name: '菜单名称',
                placeholder: '请输入菜单名称',
                verify: '请输入菜单名称',
            },
        },
    },

    module: {
        create: '创建模块',
        update: '更新模块',
        form: {
            name: {
                title: '模块名称',
                required: '请输入模块名称',
            },

            path: {
                title: '模块目录',
                required: '请输入模块目录',
            },

            desc: {
                title: '模块描述',
            },

            keywords: {
                title: '模块关键字',
            },

            dirs: {
                title: '默认目录',
                Controller: 'Controller 目录',
                Model: 'Model 目录',
                Database: 'Database 目录',
                Request: 'Request 目录',
            },
        },
    },
    widgets: {
        widget_configuration: '小部件配置',
        report_widget: '配置报告小部件',
        width: '宽度（1-12）',
        newLine: '强制新行',
        title: '小部件标题',
        manage: '管理小部件',
        add: '添加小部件',
        widgets: '小部件',
        close: '关闭',
        sure: '确定',
        loading: '加载中...',
        title_required: '请输入小部件标题',
        x: 'x轴',
        y: 'y轴',
        h: '高度',
    },

    header: {
        settings: {
            tit1: '账号',
            li1: '个人资料和首选项',
            li2: '重置密码',
            tit2: '系统语言',
            li3: '语言切换',
            langswitch: '语言切换',
        },
        contact: {
            title: '您的售后服务专员',
            label1: '服务时间',
            label2: '电子邮件',
            btn_text: '发送邮件',
        },
    },
    home: {
        title: '控制面板',
        btn_text1: '添加小部件',
        dialog_tit1: '确认删除该模块？',
        activityLog: {
            th1: '账户',
            th2: '浏览器',
            th3: '平台',
            th4: 'IP',
            th5: '状态',
            th6: '登录时间',
        },
        analytics: {
            placeholder1: '开始时间',
            placeholder2: '结束时间',
            reportTypes: {
                basicUser: '基本用户报告',
                trafficSource: '流量来源报告',
                pagePerformance: '页面性能报告',
                ecommerce: '电子商务报告',
                userBehavior: '用户行为报告',
                mobileApp: '移动应用报告',
                adPerformance: '广告效果报告',
                content: '内容分析报告',
                geographic: '地理位置报告',
                technical: '技术分析报告',
                event: '事件分析报告',
                conversionFunnel: '转化漏斗报告',
            },
        },
        news: {
            title: '新闻',
        },
        system: {
            title1: '网站存储容量',
            title2: '离续订还有几天',
            con1: '天',
            btn_text1: '查看存储',
            btn_text2: '立即续订',
            btn_text3: '增加容量',
        },
    },
    404: {
        tips: '抱歉，您访问的页面不存在',
        btn_text: '回到首页',
    },
    analytics: {
        dimensions: {
            date: '日期',
            country: '国家',
            deviceCategory: '设备类别',
            source: '来源',
            medium: '媒介',
            campaign: '活动',
            pagePath: '页面路径',
            itemName: '商品名称',
            itemCategory: '商品类别',
            sessionSourceMedium: '会话来源/媒介',
            landingPage: '着陆页',
            appVersion: '应用版本',
            operatingSystem: '操作系统',
            adGroup: '广告组',
            adContent: '广告内容',
            pageTitle: '页面标题',
            region: '地区',
            city: '城市',
            browser: '浏览器',
            eventName: '事件名称',
            eventCategory: '事件类别',
        },
        metrics: {
            totalUsers: '总用户数',
            newUsers: '新用户数',
            sessions: '会话数',
            bounceRate: '跳出率',
            screenPageViews: '页面浏览量',
            averageSessionDuration: '平均会话时长',
            conversions: '转化次数',
            averagePageLoadTime: '平均页面加载时间',
            exitRate: '退出率',
            itemViews: '商品浏览次数',
            itemsAddedToCart: '加入购物车次数',
            purchases: '购买次数',
            itemRevenue: '商品收入',
            engagementRate: '参与率',
            conversionsPerSession: '每次会话转化次数',
            crashFreeUsersRate: '无崩溃用户率',
            userEngagementDuration: '用户参与时长',
            adClicks: '广告点击次数',
            adImpressions: '广告展示次数',
            adCost: '广告成本',
            adConversions: '广告转化次数',
            averageTimeOnPage: '平均页面停留时间',
            entrances: '入口次数',
            eventCount: '事件次数',
            eventValue: '事件价值',
            addToCarts: '加入购物车次数',
            checkouts: '结账次数',
        },
    },
}

export default zh_CN
