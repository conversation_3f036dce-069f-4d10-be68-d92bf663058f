# Web布局模板

## 概述

Web布局定义了前端Web页面的整体结构和布局方式。本文档提供了Web布局的标准模板和最佳实践。

## 基本结构

```vue
<!-- src/layouts/WebLayout.vue -->
<template>
  <div class="web-layout">
    <!-- 头部导航 -->
    <header class="web-header">
      <nav class="nav-container">
        <router-link to="/" class="logo">
          <img src="@/assets/logo.png" alt="Logo">
        </router-link>
        
        <div class="nav-menu">
          <router-link 
            v-for="item in menuItems" 
            :key="item.path"
            :to="item.path"
            class="nav-item"
          >
            {{ item.title }}
          </router-link>
        </div>
        
        <div class="nav-right">
          <template v-if="isLoggedIn">
            <el-dropdown>
              <span class="user-info">
                <el-avatar :src="userInfo.avatar" />
                {{ userInfo.nickname }}
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="toUserCenter">
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleLogout">
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <router-link to="/login" class="login-btn">
              登录
            </router-link>
            <router-link to="/register" class="register-btn">
              注册
            </router-link>
          </template>
        </div>
      </nav>
    </header>

    <!-- 主要内容区 -->
    <main class="web-main">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 页脚 -->
    <footer class="web-footer">
      <div class="footer-content">
        <div class="footer-links">
          <div class="link-group" v-for="group in footerLinks" :key="group.title">
            <h4>{{ group.title }}</h4>
            <ul>
              <li v-for="link in group.links" :key="link.url">
                <a :href="link.url" target="_blank">{{ link.text }}</a>
              </li>
            </ul>
          </div>
        </div>
        <div class="copyright">
          © {{ currentYear }} Your Company. All rights reserved.
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import type { MenuItems, FooterLinks } from '@/types'

// 路由实例
const router = useRouter()

// 用户状态
const userStore = useUserStore()
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userInfo = computed(() => userStore.userInfo)

// 导航菜单
const menuItems = ref<MenuItems>([
  { path: '/', title: '首页' },
  { path: '/products', title: '产品' },
  { path: '/solutions', title: '解决方案' },
  { path: '/about', title: '关于我们' }
])

// 页脚链接
const footerLinks = ref<FooterLinks>([
  {
    title: '关于我们',
    links: [
      { text: '公司简介', url: '/about' },
      { text: '联系我们', url: '/contact' },
      { text: '加入我们', url: '/jobs' }
    ]
  },
  {
    title: '帮助中心',
    links: [
      { text: '使用文档', url: '/docs' },
      { text: '常见问题', url: '/faq' },
      { text: '技术支持', url: '/support' }
    ]
  },
  {
    title: '友情链接',
    links: [
      { text: '合作伙伴', url: '/partners' },
      { text: '开发者社区', url: '/community' },
      { text: '官方博客', url: '/blog' }
    ]
  }
])

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 用户中心
const toUserCenter = () => {
  router.push('/user')
}

// 退出登录
const handleLogout = async () => {
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}
</script>

<style scoped lang="scss">
.web-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.web-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .logo {
    img {
      height: 40px;
    }
  }
  
  .nav-menu {
    display: flex;
    gap: 24px;
    
    .nav-item {
      color: #333;
      text-decoration: none;
      font-size: 16px;
      
      &:hover,
      &.router-link-active {
        color: var(--el-color-primary);
      }
    }
  }
  
  .nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }
    
    .login-btn,
    .register-btn {
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
    }
    
    .login-btn {
      color: var(--el-color-primary);
      border: 1px solid var(--el-color-primary);
    }
    
    .register-btn {
      color: #fff;
      background: var(--el-color-primary);
    }
  }
}

.web-main {
  flex: 1;
  margin-top: 64px;
  padding: 24px;
  max-width: 1200px;
  margin: 64px auto 0;
}

.web-footer {
  background: #f5f5f5;
  padding: 48px 0 24px;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 48px;
    margin-bottom: 48px;
    
    .link-group {
      h4 {
        font-size: 18px;
        margin-bottom: 16px;
      }
      
      ul {
        list-style: none;
        padding: 0;
        
        li {
          margin-bottom: 8px;
          
          a {
            color: #666;
            text-decoration: none;
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }
  
  .copyright {
    text-align: center;
    color: #999;
    font-size: 14px;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
  }
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
```

## 规范要求

1. 布局结构
   - 头部导航固定
   - 内容区自适应
   - 页脚固定底部
   - 响应式设计

2. 组件组织
   - 布局组件分离
   - 导航组件复用
   - 页脚组件复用
   - 过渡动画统一

3. 样式规范
   - 使用SCSS预处理
   - BEM命名规范
   - 主题色变量
   - 响应式断点

4. 功能实现
   - 用户状态管理
   - 路由导航控制
   - 页面过渡动画
   - 组件懒加载

## 最佳实践

1. 响应式布局
```scss
// 响应式断点
$breakpoints: (
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.nav-menu {
  display: flex;
  gap: 24px;
  
  @include respond-to('md') {
    display: none;
  }
}
```

2. 主题定制
```typescript
// src/styles/variables.scss
:root {
  // 主题色
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 文字颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-color: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景色
  --background-color: #f5f7fa;
  
  // 阴影
  --box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  --box-shadow-light: 0 2px 4px rgba(0,0,0,0.12);
  --box-shadow-dark: 0 2px 4px rgba(0,0,0,0.24);
}
```

3. 布局组件
```vue
<!-- src/components/layout/WebHeader.vue -->
<template>
  <header class="web-header">
    <!-- 头部内容 -->
  </header>
</template>

<!-- src/components/layout/WebFooter.vue -->
<template>
  <footer class="web-footer">
    <!-- 页脚内容 -->
  </footer>
</template>
```

## 注意事项

1. 布局结构清晰
2. 组件职责单一
3. 样式命名规范
4. 主题可配置
5. 响应式适配
6. 性能优化
7. 代码复用
8. 动画流畅
9. 用户体验
10. 维护性好
