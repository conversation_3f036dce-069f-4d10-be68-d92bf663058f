<template>
  <div class="message-content">
    <div class="image-container">
      <img :src="props.message.url" :alt="props.message.caption || 'Image'" @click="handleImageClick" />
      <div v-if="props.message.caption" class="image-caption">{{ props.message.caption }}</div>
    </div>
    <div class="message-time">{{ props.message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import type { ImageMessage } from './types'

const props = defineProps<{
  message: ImageMessage
}>()

const emit = defineEmits<{
  (e: 'action', action: string, payload?: any): void
}>()

const handleImageClick = () => {
  emit('action', 'OPEN_IMAGE', { url: props.message.url })
}
</script>

<script lang="ts">
export default {
  name: 'MessageImage'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 70%;
}

.image-container {
  border-radius: 12px;
  overflow: hidden;
  background: #f5f5f5;
}

.image-container img {
  width: 100%;
  height: auto;
  max-width: 200px;
  cursor: pointer;
  display: block;
  transition: transform 0.2s ease;
}

.image-container img:hover {
  transform: scale(1.02);
}

.image-caption {
  padding: 8px 12px;
  font-size: 13px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  opacity: 0.7;
}
</style> 