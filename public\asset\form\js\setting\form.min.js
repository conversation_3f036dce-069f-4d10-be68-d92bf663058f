/*!
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2022
 * @version 1.6.5
 *
 * Client validation extension for the yii2-builder extension
 *
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */!function(t){"use strict";var e=function(e,r){var s=this;s.$element=t(e),s.options=r,s.init()};e.prototype={constructor:e,init:function(){var t=this,e=t.$element.closest("form");t.$target=t.$element.find(".kv-nested-attribute-block"),e.on("reset.yiiActiveForm",function(){setTimeout(function(){t.$target.removeClass("has-success has-error")},100)}),e.on("afterValidateAttribute",function(e,r,s){t.validate(r,s)})},validate:function(e,r){var s=this;0!==s.$target.length&&s.$target.each(function(){var s=!1,a=!1,i=t(this);return i.find("input").each(function(){var i=t(this).attr("id");i===e.id&&(r.length>0?(s=!0,a=!1):s!==!1||e.cancelled||2!==e.status&&3!==e.status||(a=!0))}),s?void i.removeClass("has-success has-error").addClass("has-error"):void(a&&i.removeClass("has-success has-error").addClass("has-success"))})}},t.fn.kvFormBuilder=function(r){var s=Array.apply(null,arguments);return s.shift(),this.each(function(){var a=t(this),i=a.data("kvFormBuilder"),n="object"==typeof r&&r;i||(i=new e(this,t.extend({},n,t(this).data())),a.data("kvFormBuilder",i)),"string"==typeof r&&i[r].apply(i,s)})},t.fn.kvFormBuilder.Constructor=e}(window.jQuery);
