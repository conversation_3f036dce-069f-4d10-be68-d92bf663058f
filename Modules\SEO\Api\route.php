<?php

use Illuminate\Support\Facades\Route;
use Modules\SEO\Api\Controllers\SeoController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('admin')->group(function () {
    Route::prefix('seo')->group(function () {
        Route::get('/', [SeoController::class, 'index'])->name('seo.index');
        Route::post('/', [SeoController::class, 'store'])->name('seo.store');
        Route::get('/{id}', [SeoController::class, 'show'])->name('seo.show');
        Route::put('/{id}', [SeoController::class, 'update'])->name('seo.update');
        Route::delete('/{id}', [SeoController::class, 'destroy'])->name('seo.destroy');
    });
});
