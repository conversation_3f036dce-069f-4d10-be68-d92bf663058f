<!DOCTYPE html>
<html>
<head>
    <title>Pusher Demo</title>
    <meta charset="UTF-8" />
    <!-- 引入依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://js.pusher.com/7.0/pusher.min.js"></script>
</head>
<body>
<h1>Presence Channel Demo</h1>
<div id="online-users"></div>
<div id="messages"></div>

<script>
    // 初始化 Pusher
    const pusher = new Pusher('e057d7a91a99c62ed780', {
        cluster: 'ap3',
        authEndpoint: '/api/chat/pusher/auth', // 后端鉴权端点
        auth: {
            headers: {
                'Authorization': 'Bearer H4BfmaHEZ3xJUfzQypsdr7KS0GiLiVrPQgzMOb5cwvp2reYLyBD25uymKLbh',
            },
        }
    });

    // 订阅存在频道
    const channel = pusher.subscribe('presence-my-channel');

    // 监听成员加入事件
    channel.bind('pusher:subscription_succeeded', (members) => {
        alert('订阅成功');
        updateMemberList(members.members);
    });

    channel.bind('pusher:member_added', (member) => {
        updateMemberList(channel.members.members);
    });

    channel.bind('pusher:member_removed', (member) => {
        updateMemberList(channel.members.members);
    });

    // 监听新消息事件
    channel.bind('new-message', (data) => {
        $('#messages').append(`
            <div>
                [${data.time}] ${data.message}
            </div>
        `);
    });

    // 更新在线用户列表
    function updateMemberList(members) {
        console.log("成员数据:", members);  // 调试
        let html = '<h2>Online Users:</h2>';
        Object.values(members).forEach(user => {
            console.log("当前用户:", user); // 调试
            html += `
                <div>
                    <img src="${user.avatar}" width="30" height="30">
                    ${user.name}
                </div>
            `;
        });
        $('#online-users').html(html);
    }
</script>
</body>


</html>
