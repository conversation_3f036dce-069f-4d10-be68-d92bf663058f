<template>
  <div class="message-content">
    <div class="file-container">
      <div class="file-icon">
        <el-icon><Document /></el-icon>
      </div>
      <div class="file-info">
        <div class="file-name">{{ props.message.fileName }}</div>
        <div class="file-meta">
          <span v-if="props.message.fileSize">{{ formatFileSize(props.message.fileSize) }}</span>
          <span v-if="props.message.fileType">{{ props.message.fileType.toUpperCase() }}</span>
        </div>
      </div>
      <div class="file-actions">
        <el-button size="small" @click="handleDownload">
          <el-icon><Download /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="message-time">{{ props.message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import { Document, Download } from '@element-plus/icons-vue'
import type { FileMessage } from './types'

const props = defineProps<{
  message: FileMessage
}>()

const emit = defineEmits<{
  (e: 'action', action: string, payload?: any): void
}>()

const handleDownload = () => {
  emit('action', 'DOWNLOAD_FILE', { 
    url: props.message.fileUrl,
    fileName: props.message.fileName 
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<script lang="ts">
export default {
  name: 'MessageFile'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 70%;
}

.file-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6BBAD2;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  word-break: break-all;
}

.file-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 8px;
}

.file-actions {
  display: flex;
  gap: 4px;
}

.file-actions .el-button {
  --el-button-bg-color: #6BBAD2;
  --el-button-border-color: #6BBAD2;
  --el-button-text-color: #fff;
  --el-button-hover-bg-color: #559CD7;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  opacity: 0.7;
}
</style> 