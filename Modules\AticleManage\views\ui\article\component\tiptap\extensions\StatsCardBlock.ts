import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { statsCardTemplate } from '../templates/statsCard.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    statsCardBlock: {
      insertStatsCardBlock: () => ReturnType
    }
  }
}

export const StatsCardBlock = Node.create({
  name: 'statsCardBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="stats-card"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'stats-card',
      'class': 'stats-card-block'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertStatsCardBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(statsCardTemplate)
        },
    }
  },
}) 