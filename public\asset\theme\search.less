@import "./variable.less";

.bwms-page {
  .banner {
    max-height: 255px;
    overflow: hidden;
  }

  .search-result {
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #fff;

    .search-keyword {
      color: @bodyColor;
      font-size: 14px;
      line-height: 1.4;
      .df(center);

      .keyword,
      .num {
        margin: 0 8px;
        display: block;
        color: @themeColor;
        font-weight: bold;
      }
    }
  }

  .result-list {
    padding: 10px;
    background-color: #fff;

    .red {
      color: #ff0000 !important;
    }

    .tit {
      margin-bottom: 10px;
      font-size: 14px;
      color: @bodyColor;
    }

    .list {
      .item {
        margin-top: 10px;
        border-bottom: 1px dashed #E5E9EE;
        padding: 10px 0;

        .item-tit {
          color: @bodyColor;
          font-size: 20px;
          display: block;
        }

        .desc {
          margin-top: 5px;
          font-size: 14px;
          color: @bodyColor;
          line-height: 1.4;
          .vertical(2);
        }

        .classification {
          margin-top: 10px;
          color: #c4cfdb;
          font-size: 13px;
          
          a {
            color: #c4cfdb;
            font-size: 13px;
          }
        }
      }
    }
  }

  .pagination-box {
    margin-top: 30px;
    .df(center, center);

    a {
      margin: 5px;
      border-radius: 5px;
      color: #666;
      font-size: 13px;

      min-width: 30px;
      min-height: 30px;
      .df(center, center);

      &.active {
        background-color: #3555CC;
        color: #fff;
      }

      &:hover {
        color: #3555CC;
      }
    }

    .more {
      color: #666;
      font-size: 13px;
    }
  }
}