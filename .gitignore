/vendor/
node_modules/
npm-debug.log
yarn-error.log
# <PERSON>vel 4 specific
bootstrap/compiled.php
app/storage/

# Laravel 5 & <PERSON><PERSON> specific
public/storage
public/hot

# <PERSON>vel 5 & <PERSON><PERSON> specific with changed public path
public_html/storage
public_html/hot

storage/*.key
storage/temp/*
storage/clockwork
storage/framework
.env
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache
.php-cs-fixer.cache
.idea
.gitee
composer.lock
yarn.lock
/public/admin/
/public/files/
/public/uploads/
/public/temp
/public/vendor
/public/frontend/
/storage/debugbar
.vscode
/public/wms/
/public/Vendor/
/Modules/*/vendor
/Modules/*/composer.lock
.ddev
bootstrap/cache
.phpunit.cache
.specstory

composer.phar

**/caddy
frankenphp
frankenphp-worker.php
.nvmrc
tvb_new(1).md
1.md