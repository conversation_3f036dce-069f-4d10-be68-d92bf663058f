<template>
  <div class="piece-box pending-approval-box mt-2.5">
    <div class="piece-tit flex justify-between items-center">
      <div class="tit">Pending Approval</div>
      <div class="total right-box flex items-center">
        <div class="total-list flex">
          <div class="item mx-4" v-for="item in totalList" :key="item.id">
            <span class="text">{{ item.text }}: {{ item.list.length }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="piece-con">
      <template v-for="item in totalList" :key="item.id">
        <div class="table-con mb-4" v-if="item.list.length">
          <el-table :data="item.list" max-height="270">
            <el-table-column :fixed="index === 0" :prop="th.prop" :label="th.label" :width="th.width" v-for="(th, index) in item.tableH" :key="index" />
          </el-table>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";

let totalList = reactive([
  {
    id: 0,
    text: "Webpage",
    list: [
      {
        moduleTit: "About Jockey Club, JCCCP Project and SRACP",
        Author: "Admin2",
        Approval: "1st",
        Time: "2024-02-20 17:14:37"
      }
    ],
    tableH: [
      {
        prop: "moduleTit",
        label: "Webpage",
        width: ""
      },
      {
        prop: "Author",
        label: "Author",
        width: "150"
      },
      {
        prop: "Approval",
        label: "Approval",
        width: "150"
      },
      {
        prop: "Time",
        label: "Time",
        width: "200"
      }
    ]
  },
  {
    id: 0,
    text: "Article",
    list: [
      {
        moduleTit: "How to manage your emotion avoid to be angry when facing problems",
        Author: "Admin2",
        Approval: "3st",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "How to manage your emotion avoid to be angry when facing problems, How to manage your emotion avoid How to manage your emotion avoid to be angry when facing problems, How to manage your emotion avoid",
        Author: "Admin2",
        Approval: "5st",
        Time: "2024-02-20 17:14:37"
      }
    ],
    tableH: [
      {
        prop: "moduleTit",
        label: "Article",
        width: ""
      },
      {
        prop: "Author",
        label: "Author",
        width: "150"
      },
      {
        prop: "Approval",
        label: "Approval",
        width: "150"
      },
      {
        prop: "Time",
        label: "Time",
        width: "200"
      }
    ]
  },
  {
    id: 0,
    text: "Member",
    list: [
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 1",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "peterchan123 @outlook.com",
        Author: "Company name 2",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 3",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 4",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 5",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 6",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      },
      {
        moduleTit: "<EMAIL>",
        Author: "Company name 7",
        Approval: "8888 8888",
        Time: "2024-02-20 17:14:37"
      }
    ],
    tableH: [
      {
        prop: "moduleTit",
        label: "Member",
        width: ""
      },
      {
        prop: "Author",
        label: "Company",
        width: "150"
      },
      {
        prop: "Approval",
        label: "Tel",
        width: "150"
      },
      {
        prop: "Time",
        label: "Time",
        width: "200"
      }
    ]
  },
  {
    id: 0,
    text: "Products",
    number: 1,
    list: [
      {
        moduleTit: "IPhone 12 Plus XRRSS with wifi router",
        Author: "Admin3",
        Approval: "1st",
        Time: "2024-02-20 17:14:37"
      }
    ],
    tableH: [
      {
        prop: "moduleTit",
        label: "Products",
        width: ""
      },
      {
        prop: "Author",
        label: "Author",
        width: "150"
      },
      {
        prop: "Approval",
        label: "Approval",
        width: "150"
      },
      {
        prop: "Time",
        label: "Time",
        width: "200"
      }
    ]
  },
  {
    id: 0,
    text: "Career",
    number: 0,
    list: []
  }
]);
</script>

<style lang="scss" scoped>
.pending-approval-box {
  .piece-con {
    .table-con {
      border-radius: var(--el-table-border-radius);
      padding: 10px;
      background-color: #fff;

      :deep(.el-table) {
        width: 100%;

        .cell {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .el-table__header-wrapper {

          th.el-table__cell {
            background-color: #F2F2F2;
          }
        }
      }
    }
  }
}
</style>