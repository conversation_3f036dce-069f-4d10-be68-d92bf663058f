<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 加密密钥配置
    |--------------------------------------------------------------------------
    |
    | 这里定义了用于各种加密算法的密钥和初始化向量(IV)
    |
    */
    'keys' => [
        'aes256' => [
            'secret' => env('ENCRYPTION_AES256_SECRET', '0123456789abcdef0123456789abcdef'), // 32字节密钥
            'iv' => env('ENCRYPTION_AES256_IV', '0123456789abcdef'),                        // 16字节IV
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RSA 密钥配置
    |--------------------------------------------------------------------------
    |
    | 用于RSA加密的公钥和私钥ID
    |
    */
    'rsa' => [
        'public_key_id' => env('ENCRYPTION_RSA_PUBLIC_KEY_ID', ''),
        'private_key_id' => env('ENCRYPTION_RSA_PRIVATE_KEY_ID', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | AWS KMS 配置
    |--------------------------------------------------------------------------
    |
    | AWS KMS服务的配置
    |
    */
    'aws' => [
        'region' => env('AWS_REGION', 'us-west-2'),
        'kms_key_id' => env('AWS_KMS_KEY_ID', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | 默认加密类型
    |--------------------------------------------------------------------------
    |
    | 默认使用的加密类型
    | 1 = AES256, 2 = RSA2048, 3 = AWS_HSM
    |
    */
    'default_type' => env('ENCRYPTION_DEFAULT_TYPE', 1),
];
