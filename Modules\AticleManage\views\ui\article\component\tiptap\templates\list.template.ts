export default `
<div data-bs-component="list" class="bootstrap-list">
  <div class="container-fluid p-0">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="custom-list list-style-circle" data-list-style="circle" data-list-color="#6c5ce7">
          <div class="list-container">
            <div class="list-item">
              <div class="list-marker" style="background-color: #6c5ce7;"></div>
              <div class="list-item-text">这是第一个列表项，您可以在这里添加详细的描述文本。</div>
            </div>
            <div class="list-item">
              <div class="list-marker" style="background-color: #6c5ce7;"></div>
              <div class="list-item-text">这是第二个列表项，您可以在这里添加详细的描述文本。</div>
            </div>
            <div class="list-item">
              <div class="list-marker" style="background-color: #6c5ce7;"></div>
              <div class="list-item-text">这是第三个列表项，您可以在这里添加详细的描述文本。</div>
            </div>
            <div class="list-item">
              <div class="list-marker" style="background-color: #6c5ce7;"></div>
              <div class="list-item-text">这是第四个列表项，您可以在这里添加详细的描述文本。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
.bootstrap-list .custom-list {
  margin: 30px 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.bootstrap-list .list-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bootstrap-list .list-item {
  display: flex;
  align-items: flex-start;
}

.bootstrap-list .list-marker {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-top: 6px;
  margin-right: 15px;
  flex-shrink: 0;
}

.bootstrap-list .list-item-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

/* 不同的列表样式 */
.bootstrap-list .list-style-circle .list-marker {
  border-radius: 50%;
}

.bootstrap-list .list-style-square .list-marker {
  border-radius: 0;
}

.bootstrap-list .list-style-disc .list-marker {
  border-radius: 50%;
  border: none;
}

.bootstrap-list .list-style-decimal {
  counter-reset: list-counter;
}

.bootstrap-list .list-style-decimal .list-item {
  counter-increment: list-counter;
}

.bootstrap-list .list-style-decimal .list-marker {
  display: none;
}

.bootstrap-list .list-style-decimal .list-item-text::before {
  content: counter(list-counter) ".";
  margin-right: 8px;
  font-weight: bold;
  color: #6c5ce7;
}
</style>
` 