import { RouteRecordRaw } from 'vue-router'

// 假设所有模块默认导出一个RouteRecordRaw[]类型的数组
export function getModuleRoutes() {
  // 动态导入路由模块
  const modules = import.meta.glob('@/module/**/views/router.ts', { eager: true })
  let moduleRoutes: RouteRecordRaw[] = []

  Object.values(modules).forEach((module: any) => {
    // 直接访问module.default，该属性为RouteRecordRaw[]类型
    const routes: RouteRecordRaw[] = module.default
    if (routes) {
      // 為每個路由添加 query 參數
      moduleRoutes = moduleRoutes.concat(routes)
    }
  })

  return moduleRoutes
}

export function getModuleViewComponents() {
  return import.meta.glob(['@/module/**/views/**/*.vue', '@/module/!User/views/**/*.vue', '@/module/!Develop/views/**/*.vue', '@/module/!Common/views/**/*.vue'])
}
