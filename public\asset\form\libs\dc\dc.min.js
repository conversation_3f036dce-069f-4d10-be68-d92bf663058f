(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?factory(exports,require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3"),require("d3")):typeof define==="function"&&define.amd?define(["exports","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3","d3"],factory):(global=global||self,factory(global.dc={},global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3,global.d3))})(this,function(exports,d3TimeFormat,d3Time,d3Format,d3Selection,d3Dispatch,d3Array,d3Scale,d3Interpolate,d3ScaleChromatic,d3Axis,d3Zoom,d3Brush,d3Timer,d3Shape,d3Geo,d3Ease,d3Hierarchy,d3,d3Collection){"use strict";const version="4.2.7";class BadArgumentException extends Error{}const constants={CHART_CLASS:"dc-chart",DEBUG_GROUP_CLASS:"debug",STACK_CLASS:"stack",DESELECTED_CLASS:"deselected",SELECTED_CLASS:"selected",NODE_INDEX_NAME:"__index__",GROUP_INDEX_NAME:"__group_index__",DEFAULT_CHART_GROUP:"__default_chart_group__",EVENT_DELAY:40,NEGLIGIBLE_NUMBER:1e-10};class Logger{constructor(){this.enableDebugLog=false;this._alreadyWarned={}}warn(msg){if(console){if(console.warn){console.warn(msg)}else if(console.log){console.log(msg)}}return this}warnOnce(msg){if(!this._alreadyWarned[msg]){this._alreadyWarned[msg]=true;logger.warn(msg)}return this}debug(msg){if(this.enableDebugLog&&console){if(console.debug){console.debug(msg)}else if(console.log){console.log(msg)}}return this}}const logger=new Logger;class Config{constructor(){this._defaultColors=Config._schemeCategory20c;this.dateFormat=d3TimeFormat.timeFormat("%m/%d/%Y");this._renderlet=null;this.disableTransitions=false}defaultColors(colors){if(!arguments.length){if(this._defaultColors===Config._schemeCategory20c){logger.warnOnce("You are using d3.schemeCategory20c, which has been removed in D3v5. "+"See the explanation at https://github.com/d3/d3/blob/master/CHANGES.md#changes-in-d3-50. "+"DC is using it for backward compatibility, however it will be changed in DCv3.1. "+"You can change it by calling dc.config.defaultColors(newScheme). "+"See https://github.com/d3/d3-scale-chromatic for some alternatives.")}return this._defaultColors}this._defaultColors=colors;return this}}Config._schemeCategory20c=["#3182bd","#6baed6","#9ecae1","#c6dbef","#e6550d","#fd8d3c","#fdae6b","#fdd0a2","#31a354","#74c476","#a1d99b","#c7e9c0","#756bb1","#9e9ac8","#bcbddc","#dadaeb","#636363","#969696","#bdbdbd","#d9d9d9"];const config=new Config;const d3compat={eventHandler:handler=>function eventHandler(a,b){console.warn("No d3.js compatbility event handler registered, defaulting to v6 behavior.");handler.call(this,b,a)},nester:({key,sortKeys,sortValues,entries})=>{throw new Error("No d3.js compatbility nester registered, load v5 or v6 compability layer.")},pointer:()=>{throw new Error("No d3.js compatbility pointer registered, load v5 or v6 compability layer.")}};class ChartRegistry{constructor(){this._chartMap={}}_initializeChartGroup(group){if(!group){group=constants.DEFAULT_CHART_GROUP}if(!this._chartMap[group]){this._chartMap[group]=[]}return group}has(chart){for(const e in this._chartMap){if(this._chartMap[e].indexOf(chart)>=0){return true}}return false}register(chart,group){const _chartMap=this._chartMap;group=this._initializeChartGroup(group);_chartMap[group].push(chart)}deregister(chart,group){group=this._initializeChartGroup(group);for(let i=0;i<this._chartMap[group].length;i++){if(this._chartMap[group][i].anchorName()===chart.anchorName()){this._chartMap[group].splice(i,1);break}}}clear(group){if(group){delete this._chartMap[group]}else{this._chartMap={}}}list(group){group=this._initializeChartGroup(group);return this._chartMap[group]}}const chartRegistry=new ChartRegistry;const registerChart=function(chart,group){chartRegistry.register(chart,group)};const deregisterChart=function(chart,group){chartRegistry.deregister(chart,group)};const hasChart=function(chart){return chartRegistry.has(chart)};const deregisterAllCharts=function(group){chartRegistry.clear(group)};const filterAll=function(group){const charts=chartRegistry.list(group);for(let i=0;i<charts.length;++i){charts[i].filterAll()}};const refocusAll=function(group){const charts=chartRegistry.list(group);for(let i=0;i<charts.length;++i){if(charts[i].focus){charts[i].focus()}}};const renderAll=function(group){const charts=chartRegistry.list(group);for(let i=0;i<charts.length;++i){charts[i].render()}if(config._renderlet!==null){config._renderlet(group)}};const redrawAll=function(group){const charts=chartRegistry.list(group);for(let i=0;i<charts.length;++i){charts[i].redraw()}if(config._renderlet!==null){config._renderlet(group)}};const transition=function(selection,duration,delay,name){if(config.disableTransitions||duration<=0){return selection}let s=selection.transition(name);if(duration>=0||duration!==undefined){s=s.duration(duration)}if(delay>=0||delay!==undefined){s=s.delay(delay)}return s};const optionalTransition=function(enable,duration,delay,name){if(enable){return function(selection){return transition(selection,duration,delay,name)}}else{return function(selection){return selection}}};const afterTransition=function(_transition,callback){if(_transition.empty()||!_transition.duration){callback.call(_transition)}else{let n=0;_transition.each(()=>{++n}).on("end",()=>{if(!--n){callback.call(_transition)}})}};const renderlet=function(_){if(!arguments.length){return config._renderlet}config._renderlet=_;return null};const instanceOfChart=function(o){return o instanceof Object&&o.__dcFlag__&&true};const events={current:null};events.trigger=function(closure,delay){if(!delay){closure();return}events.current=closure;setTimeout(()=>{if(closure===events.current){closure()}},delay)};const filters={};filters.RangedFilter=function(low,high){const range=new Array(low,high);range.isFiltered=function(value){return value>=this[0]&&value<this[1]};range.filterType="RangedFilter";return range};filters.TwoDimensionalFilter=function(filter){if(filter===null){return null}const f=filter;f.isFiltered=function(value){return value.length&&value.length===f.length&&value[0]===f[0]&&value[1]===f[1]};f.filterType="TwoDimensionalFilter";return f};filters.RangedTwoDimensionalFilter=function(filter){if(filter===null){return null}const f=filter;let fromBottomLeft;if(f[0]instanceof Array){fromBottomLeft=[[Math.min(filter[0][0],filter[1][0]),Math.min(filter[0][1],filter[1][1])],[Math.max(filter[0][0],filter[1][0]),Math.max(filter[0][1],filter[1][1])]]}else{fromBottomLeft=[[filter[0],-Infinity],[filter[1],Infinity]]}f.isFiltered=function(value){let x,y;if(value instanceof Array){x=value[0];y=value[1]}else{x=value;y=fromBottomLeft[0][1]}return x>=fromBottomLeft[0][0]&&x<fromBottomLeft[1][0]&&y>=fromBottomLeft[0][1]&&y<fromBottomLeft[1][1]};f.filterType="RangedTwoDimensionalFilter";return f};filters.HierarchyFilter=function(path){if(path===null){return null}const filter=path.slice(0);filter.isFiltered=function(value){if(!(filter.length&&value&&value.length&&value.length>=filter.length)){return false}for(let i=0;i<filter.length;i++){if(value[i]!==filter[i]){return false}}return true};return filter};class InvalidStateException extends Error{}const pluck=function(n,f){if(!f){return function(d){return d[n]}}return function(d,i){return f.call(d,d[n],i)}};const utils={};utils.printSingleValue=function(filter){let s=`${filter}`;if(filter instanceof Date){s=config.dateFormat(filter)}else if(typeof filter==="string"){s=filter}else if(utils.isFloat(filter)){s=utils.printSingleValue.fformat(filter)}else if(utils.isInteger(filter)){s=Math.round(filter)}return s};utils.printSingleValue.fformat=d3Format.format(".2f");utils._toTimeFunc=function(t){const mappings={second:d3Time.timeSecond,minute:d3Time.timeMinute,hour:d3Time.timeHour,day:d3Time.timeDay,week:d3Time.timeWeek,month:d3Time.timeMonth,year:d3Time.timeYear};return mappings[t]};utils.add=function(l,r,t){if(typeof r==="string"){r=r.replace("%","")}if(l instanceof Date){if(typeof r==="string"){r=+r}if(t==="millis"){return new Date(l.getTime()+r)}t=t||d3Time.timeDay;if(typeof t!=="function"){t=utils._toTimeFunc(t)}return t.offset(l,r)}else if(typeof r==="string"){const percentage=+r/100;return l>0?l*(1+percentage):l*(1-percentage)}else{return l+r}};utils.subtract=function(l,r,t){if(typeof r==="string"){r=r.replace("%","")}if(l instanceof Date){if(typeof r==="string"){r=+r}if(t==="millis"){return new Date(l.getTime()-r)}t=t||d3Time.timeDay;if(typeof t!=="function"){t=utils._toTimeFunc(t)}return t.offset(l,-r)}else if(typeof r==="string"){const percentage=+r/100;return l<0?l*(1+percentage):l*(1-percentage)}else{return l-r}};utils.isNumber=function(n){return n===+n};utils.isFloat=function(n){return n===+n&&n!==(n|0)};utils.isInteger=function(n){return n===+n&&n===(n|0)};utils.isNegligible=function(n){return!utils.isNumber(n)||n<constants.NEGLIGIBLE_NUMBER&&n>-constants.NEGLIGIBLE_NUMBER};utils.clamp=function(val,min,max){return val<min?min:val>max?max:val};utils.constant=function(x){return function(){return x}};let _idCounter=0;utils.uniqueId=function(){return++_idCounter};utils.nameToId=function(name){return name.toLowerCase().replace(/[\s]/g,"_").replace(/[\.']/g,"")};utils.appendOrSelect=function(parent,selector,tag){tag=tag||selector;let element=parent.select(selector);if(element.empty()){element=parent.append(tag)}return element};utils.safeNumber=function(n){return utils.isNumber(+n)?+n:0};utils.arraysEqual=function(a1,a2){if(!a1&&!a2){return true}if(!a1||!a2){return false}return a1.length===a2.length&&a1.every((elem,i)=>elem.valueOf()===a2[i].valueOf())};utils.allChildren=function(node){let paths=[];paths.push(node.path);console.log("currentNode",node);if(node.children){for(let i=0;i<node.children.length;i++){paths=paths.concat(utils.allChildren(node.children[i]))}}return paths};utils.toHierarchy=function(list,accessor){const root={key:"root",children:[]};for(let i=0;i<list.length;i++){const data=list[i];const parts=data.key;const value=accessor(data);let currentNode=root;for(let j=0;j<parts.length;j++){const currentPath=parts.slice(0,j+1);const children=currentNode.children;const nodeName=parts[j];let childNode;if(j+1<parts.length){childNode=findChild(children,nodeName);if(childNode===void 0){childNode={key:nodeName,children:[],path:currentPath};children.push(childNode)}currentNode=childNode}else{childNode={key:nodeName,value:value,data:data,path:currentPath};children.push(childNode)}}}return root};function findChild(children,nodeName){for(let k=0;k<children.length;k++){if(children[k].key===nodeName){return children[k]}}}utils.getAncestors=function(node){const path=[];let current=node;while(current.parent){path.unshift(current.name);current=current.parent}return path};utils.arraysIdentical=function(a,b){let i=a.length;if(i!==b.length){return false}while(i--){if(a[i]!==b[i]){return false}}return true};const printers={};printers.filters=function(filters){let s="";for(let i=0;i<filters.length;++i){if(i>0){s+=", "}s+=printers.filter(filters[i])}return s};printers.filter=function(filter){let s="";if(typeof filter!=="undefined"&&filter!==null){if(filter instanceof Array){if(filter.length>=2){s=`[${filter.map(e=>utils.printSingleValue(e)).join(" -> ")}]`}else if(filter.length>=1){s=utils.printSingleValue(filter[0])}}else{s=utils.printSingleValue(filter)}}return s};const units={};units.integers=function(start,end){return Math.abs(end-start)};units.ordinal=function(){throw new Error("dc.units.ordinal should not be called - it is a placeholder")};units.fp={};units.fp.precision=function(precision){const _f=function(s,e){const d=Math.abs((e-s)/_f.resolution);if(utils.isNegligible(d-Math.floor(d))){return Math.floor(d)}else{return Math.ceil(d)}};_f.resolution=precision;return _f};const _defaultFilterHandler=(dimension,filters)=>{if(filters.length===0){dimension.filter(null)}else if(filters.length===1&&!filters[0].isFiltered){dimension.filterExact(filters[0])}else if(filters.length===1&&filters[0].filterType==="RangedFilter"){dimension.filterRange(filters[0])}else{dimension.filterFunction(d=>{for(let i=0;i<filters.length;i++){const filter=filters[i];if(filter.isFiltered){if(filter.isFiltered(d)){return true}}else if(filter<=d&&filter>=d){return true}}return false})}return filters};const _defaultHasFilterHandler=(filters,filter)=>{if(filter===null||typeof filter==="undefined"){return filters.length>0}return filters.some(f=>filter<=f&&filter>=f)};const _defaultRemoveFilterHandler=(filters,filter)=>{for(let i=0;i<filters.length;i++){if(filters[i]<=filter&&filters[i]>=filter){filters.splice(i,1);break}}return filters};const _defaultAddFilterHandler=(filters,filter)=>{filters.push(filter);return filters};const _defaultResetFilterHandler=filters=>[];class BaseMixin{constructor(){this.__dcFlag__=utils.uniqueId();this._svgDescription=null;this._keyboardAccessible=false;this._dimension=undefined;this._group=undefined;this._anchor=undefined;this._root=undefined;this._svg=undefined;this._isChild=undefined;this._minWidth=200;this._defaultWidthCalc=element=>{const width=element&&element.getBoundingClientRect&&element.getBoundingClientRect().width;return width&&width>this._minWidth?width:this._minWidth};this._widthCalc=this._defaultWidthCalc;this._minHeight=200;this._defaultHeightCalc=element=>{const height=element&&element.getBoundingClientRect&&element.getBoundingClientRect().height;return height&&height>this._minHeight?height:this._minHeight};this._heightCalc=this._defaultHeightCalc;this._width=undefined;this._height=undefined;this._useViewBoxResizing=false;this._keyAccessor=pluck("key");this._valueAccessor=pluck("value");this._label=pluck("key");this._ordering=pluck("key");this._renderLabel=false;this._title=d=>`${this.keyAccessor()(d)}: ${this.valueAccessor()(d)}`;this._renderTitle=true;this._controlsUseVisibility=false;this._transitionDuration=750;this._transitionDelay=0;this._filterPrinter=printers.filters;this._mandatoryAttributesList=["dimension","group"];this._chartGroup=constants.DEFAULT_CHART_GROUP;this._listeners=d3Dispatch.dispatch("preRender","postRender","preRedraw","postRedraw","filtered","zoomed","renderlet","pretransition");this._legend=undefined;this._commitHandler=undefined;this._defaultData=group=>group.all();this._data=this._defaultData;this._filters=[];this._filterHandler=_defaultFilterHandler;this._hasFilterHandler=_defaultHasFilterHandler;this._removeFilterHandler=_defaultRemoveFilterHandler;this._addFilterHandler=_defaultAddFilterHandler;this._resetFilterHandler=_defaultResetFilterHandler}height(height){if(!arguments.length){if(!utils.isNumber(this._height)){this._height=this._heightCalc(this._root.node())}return this._height}this._heightCalc=height?typeof height==="function"?height:utils.constant(height):this._defaultHeightCalc;this._height=undefined;return this}width(width){if(!arguments.length){if(!utils.isNumber(this._width)){this._width=this._widthCalc(this._root.node())}return this._width}this._widthCalc=width?typeof width==="function"?width:utils.constant(width):this._defaultWidthCalc;this._width=undefined;return this}minWidth(minWidth){if(!arguments.length){return this._minWidth}this._minWidth=minWidth;return this}minHeight(minHeight){if(!arguments.length){return this._minHeight}this._minHeight=minHeight;return this}useViewBoxResizing(useViewBoxResizing){if(!arguments.length){return this._useViewBoxResizing}this._useViewBoxResizing=useViewBoxResizing;return this}dimension(dimension){if(!arguments.length){return this._dimension}this._dimension=dimension;this.expireCache();return this}data(callback){if(!arguments.length){return this._data(this._group)}this._data=typeof callback==="function"?callback:utils.constant(callback);this.expireCache();return this}group(group,name){if(!arguments.length){return this._group}this._group=group;this._groupName=name;this.expireCache();return this}ordering(orderFunction){if(!arguments.length){return this._ordering}this._ordering=orderFunction;this.expireCache();return this}_computeOrderedGroups(data){return Array.from(data).sort((a,b)=>d3Array.ascending(this._ordering(a),this._ordering(b)))}filterAll(){return this.filter(null)}select(sel){return this._root.select(sel)}selectAll(sel){return this._root?this._root.selectAll(sel):null}anchor(parent,chartGroup){if(!arguments.length){return this._anchor}if(instanceOfChart(parent)){this._anchor=parent.anchor();if(this._anchor.children){this._anchor=`#${parent.anchorName()}`}this._root=parent.root();this._isChild=true}else if(parent){if(parent.select&&parent.classed){this._anchor=parent.node()}else{this._anchor=parent}this._root=d3Selection.select(this._anchor);this._root.classed(constants.CHART_CLASS,true);registerChart(this,chartGroup);this._isChild=false}else{throw new BadArgumentException("parent must be defined")}this._chartGroup=chartGroup;return this}anchorName(){const a=this.anchor();if(a&&a.id){return a.id}if(a&&a.replace){return a.replace("#","")}return`dc-chart${this.chartID()}`}root(rootElement){if(!arguments.length){return this._root}this._root=rootElement;return this}svg(svgElement){if(!arguments.length){return this._svg}this._svg=svgElement;return this}resetSvg(){this.select("svg").remove();return this.generateSvg()}sizeSvg(){if(this._svg){if(!this._useViewBoxResizing){this._svg.attr("width",this.width()).attr("height",this.height())}else if(!this._svg.attr("viewBox")){this._svg.attr("viewBox",`0 0 ${this.width()} ${this.height()}`)}}}generateSvg(){this._svg=this.root().append("svg");if(this._svgDescription||this._keyboardAccessible){this._svg.append("desc").attr("id",`desc-id-${this.__dcFlag__}`).html(`${this.svgDescription()}`);this._svg.attr("tabindex","0").attr("role","img").attr("aria-labelledby",`desc-id-${this.__dcFlag__}`)}this.sizeSvg();return this._svg}svgDescription(description){if(!arguments.length){return this._svgDescription||this.constructor.name}this._svgDescription=description;return this}keyboardAccessible(keyboardAccessible){if(!arguments.length){return this._keyboardAccessible}this._keyboardAccessible=keyboardAccessible;return this}filterPrinter(filterPrinterFunction){if(!arguments.length){return this._filterPrinter}this._filterPrinter=filterPrinterFunction;return this}controlsUseVisibility(controlsUseVisibility){if(!arguments.length){return this._controlsUseVisibility}this._controlsUseVisibility=controlsUseVisibility;return this}turnOnControls(){if(this._root){const attribute=this.controlsUseVisibility()?"visibility":"display";this.selectAll(".reset").style(attribute,null);this.selectAll(".filter").text(this._filterPrinter(this.filters())).style(attribute,null)}return this}turnOffControls(){if(this._root){const attribute=this.controlsUseVisibility()?"visibility":"display";const value=this.controlsUseVisibility()?"hidden":"none";this.selectAll(".reset").style(attribute,value);this.selectAll(".filter").style(attribute,value).text(this.filter())}return this}transitionDuration(duration){if(!arguments.length){return this._transitionDuration}this._transitionDuration=duration;return this}transitionDelay(delay){if(!arguments.length){return this._transitionDelay}this._transitionDelay=delay;return this}_mandatoryAttributes(_){if(!arguments.length){return this._mandatoryAttributesList}this._mandatoryAttributesList=_;return this}checkForMandatoryAttributes(a){if(!this[a]||!this[a]()){throw new InvalidStateException(`Mandatory attribute chart.${a} is missing on chart[#${this.anchorName()}]`)}}render(){this._height=this._width=undefined;this._listeners.call("preRender",this,this);if(this._mandatoryAttributesList){this._mandatoryAttributesList.forEach(e=>this.checkForMandatoryAttributes(e))}const result=this._doRender();if(this._legend){this._legend.render()}this._activateRenderlets("postRender");return result}_makeKeyboardAccessible(onClickFunction,...onClickArgs){const tabElements=this._svg.selectAll(".dc-tabbable").attr("tabindex",0);if(onClickFunction){tabElements.on("keydown",d3compat.eventHandler((d,event)=>{if(event.keyCode===13&&typeof d==="object"){onClickFunction.call(this,d,...onClickArgs)}if(event.keyCode===32&&typeof d==="object"){onClickFunction.call(this,d,...onClickArgs);event.preventDefault()}}))}}_activateRenderlets(event){this._listeners.call("pretransition",this,this);if(this.transitionDuration()>0&&this._svg){this._svg.transition().duration(this.transitionDuration()).delay(this.transitionDelay()).on("end",()=>{this._listeners.call("renderlet",this,this);if(event){this._listeners.call(event,this,this)}})}else{this._listeners.call("renderlet",this,this);if(event){this._listeners.call(event,this,this)}}}redraw(){this.sizeSvg();this._listeners.call("preRedraw",this,this);const result=this._doRedraw();if(this._legend){this._legend.render()}this._activateRenderlets("postRedraw");return result}commitHandler(commitHandler){if(!arguments.length){return this._commitHandler}this._commitHandler=commitHandler;return this}redrawGroup(){if(this._commitHandler){this._commitHandler(false,(error,result)=>{if(error){console.log(error)}else{redrawAll(this.chartGroup())}})}else{redrawAll(this.chartGroup())}return this}renderGroup(){if(this._commitHandler){this._commitHandler(false,(error,result)=>{if(error){console.log(error)}else{renderAll(this.chartGroup())}})}else{renderAll(this.chartGroup())}return this}_invokeFilteredListener(f){if(f!==undefined){this._listeners.call("filtered",this,this,f)}}_invokeZoomedListener(){this._listeners.call("zoomed",this,this)}hasFilterHandler(hasFilterHandler){if(!arguments.length){return this._hasFilterHandler}this._hasFilterHandler=hasFilterHandler;return this}hasFilter(filter){return this._hasFilterHandler(this._filters,filter)}removeFilterHandler(removeFilterHandler){if(!arguments.length){return this._removeFilterHandler}this._removeFilterHandler=removeFilterHandler;return this}addFilterHandler(addFilterHandler){if(!arguments.length){return this._addFilterHandler}this._addFilterHandler=addFilterHandler;return this}resetFilterHandler(resetFilterHandler){if(!arguments.length){return this._resetFilterHandler}this._resetFilterHandler=resetFilterHandler;return this}applyFilters(filters){if(this.dimension()&&this.dimension().filter){const fs=this._filterHandler(this.dimension(),filters);if(fs){filters=fs}}return filters}replaceFilter(filter){this._filters=this._resetFilterHandler(this._filters);this.filter(filter);return this}filter(filter){if(!arguments.length){return this._filters.length>0?this._filters[0]:null}let filters=this._filters;if(filter instanceof Array&&filter[0]instanceof Array&&!filter.isFiltered){filter[0].forEach(f=>{if(this._hasFilterHandler(filters,f)){filters=this._removeFilterHandler(filters,f)}else{filters=this._addFilterHandler(filters,f)}})}else if(filter===null){filters=this._resetFilterHandler(filters)}else{if(this._hasFilterHandler(filters,filter)){filters=this._removeFilterHandler(filters,filter)}else{filters=this._addFilterHandler(filters,filter)}}this._filters=this.applyFilters(filters);this._invokeFilteredListener(filter);if(this._root!==null&&this.hasFilter()){this.turnOnControls()}else{this.turnOffControls()}return this}filters(){return this._filters}highlightSelected(e){d3Selection.select(e).classed(constants.SELECTED_CLASS,true);d3Selection.select(e).classed(constants.DESELECTED_CLASS,false)}fadeDeselected(e){d3Selection.select(e).classed(constants.SELECTED_CLASS,false);d3Selection.select(e).classed(constants.DESELECTED_CLASS,true)}resetHighlight(e){d3Selection.select(e).classed(constants.SELECTED_CLASS,false);d3Selection.select(e).classed(constants.DESELECTED_CLASS,false)}onClick(datum){const filter=this.keyAccessor()(datum);events.trigger(()=>{this.filter(filter);this.redrawGroup()})}filterHandler(filterHandler){if(!arguments.length){return this._filterHandler}this._filterHandler=filterHandler;return this}_doRender(){return this}_doRedraw(){return this}legendables(){return[]}legendHighlight(){}legendReset(){}legendToggle(){}isLegendableHidden(){return false}keyAccessor(keyAccessor){if(!arguments.length){return this._keyAccessor}this._keyAccessor=keyAccessor;return this}valueAccessor(valueAccessor){if(!arguments.length){return this._valueAccessor}this._valueAccessor=valueAccessor;return this}label(labelFunction,enableLabels){if(!arguments.length){return this._label}this._label=labelFunction;if(enableLabels===undefined||enableLabels){this._renderLabel=true}return this}renderLabel(renderLabel){if(!arguments.length){return this._renderLabel}this._renderLabel=renderLabel;return this}title(titleFunction){if(!arguments.length){return this._title}this._title=titleFunction;return this}renderTitle(renderTitle){if(!arguments.length){return this._renderTitle}this._renderTitle=renderTitle;return this}chartGroup(chartGroup){if(!arguments.length){return this._chartGroup}if(!this._isChild){deregisterChart(this,this._chartGroup)}this._chartGroup=chartGroup;if(!this._isChild){registerChart(this,this._chartGroup)}return this}expireCache(){return this}legend(legend){if(!arguments.length){return this._legend}this._legend=legend;this._legend.parent(this);return this}chartID(){return this.__dcFlag__}options(opts){const applyOptions=["anchor","group","xAxisLabel","yAxisLabel","stack","title","point","getColor","overlayGeoJson"];for(const o in opts){if(typeof this[o]==="function"){if(opts[o]instanceof Array&&applyOptions.indexOf(o)!==-1){this[o].apply(this,opts[o])}else{this[o].call(this,opts[o])}}else{logger.debug(`Not a valid option setter name: ${o}`)}}return this}on(event,listener){this._listeners.on(event,listener);return this}renderlet(renderletFunction){logger.warnOnce('chart.renderlet has been deprecated. Please use chart.on("renderlet.<renderletKey>", renderletFunction)');this.on(`renderlet.${utils.uniqueId()}`,renderletFunction);return this}}const baseMixin=()=>new BaseMixin;const ColorMixin=Base=>class extends Base{constructor(){super();this._colors=d3Scale.scaleOrdinal(config.defaultColors());this._colorAccessor=d=>this.keyAccessor()(d);this._colorCalculator=undefined;{const chart=this;chart.getColor=function(d,i){return chart._colorCalculator?chart._colorCalculator.call(this,d,i):chart._colors(chart._colorAccessor.call(this,d,i))}}}calculateColorDomain(){const newDomain=[d3Array.min(this.data(),this.colorAccessor()),d3Array.max(this.data(),this.colorAccessor())];this._colors.domain(newDomain);return this}colors(colorScale){if(!arguments.length){return this._colors}if(colorScale instanceof Array){this._colors=d3Scale.scaleQuantize().range(colorScale)}else{this._colors=typeof colorScale==="function"?colorScale:utils.constant(colorScale)}return this}ordinalColors(r){return this.colors(d3Scale.scaleOrdinal().range(r))}linearColors(r){return this.colors(d3Scale.scaleLinear().range(r).interpolate(d3Interpolate.interpolateHcl))}colorAccessor(colorAccessor){if(!arguments.length){return this._colorAccessor}this._colorAccessor=colorAccessor;return this}colorDomain(domain){if(!arguments.length){return this._colors.domain()}this._colors.domain(domain);return this}colorCalculator(colorCalculator){if(!arguments.length){return this._colorCalculator||this.getColor}this._colorCalculator=colorCalculator;return this}};const BubbleMixin=Base=>class extends ColorMixin(Base){constructor(){super();this._maxBubbleRelativeSize=.3;this._minRadiusWithLabel=10;this._sortBubbleSize=false;this._elasticRadius=false;this._excludeElasticZero=true;this.BUBBLE_NODE_CLASS="node";this.BUBBLE_CLASS="bubble";this.MIN_RADIUS=10;this.renderLabel(true);this.data(group=>{const data=group.all();if(this._keyboardAccessible){data.sort((a,b)=>d3Array.ascending(this.keyAccessor()(a),this.keyAccessor()(b)))}if(this._sortBubbleSize){const radiusAccessor=this.radiusValueAccessor();data.sort((a,b)=>d3Array.descending(radiusAccessor(a),radiusAccessor(b)))}return data});this._r=d3Scale.scaleLinear().domain([0,100])}_rValueAccessor(d){return d.r}r(bubbleRadiusScale){if(!arguments.length){return this._r}this._r=bubbleRadiusScale;return this}elasticRadius(elasticRadius){if(!arguments.length){return this._elasticRadius}this._elasticRadius=elasticRadius;return this}calculateRadiusDomain(){if(this._elasticRadius){this.r().domain([this.rMin(),this.rMax()])}}radiusValueAccessor(radiusValueAccessor){if(!arguments.length){return this._rValueAccessor}this._rValueAccessor=radiusValueAccessor;return this}rMin(){let values=this.data().map(this.radiusValueAccessor());if(this._excludeElasticZero){values=values.filter(value=>value>0)}return d3Array.min(values)}rMax(){return d3Array.max(this.data(),e=>this.radiusValueAccessor()(e))}bubbleR(d){const value=this.radiusValueAccessor()(d);let r=this.r()(value);if(isNaN(r)||value<=0){r=0}return r}_labelFunction(d){return this.label()(d)}_shouldLabel(d){return this.bubbleR(d)>this._minRadiusWithLabel}_labelOpacity(d){return this._shouldLabel(d)?1:0}_labelPointerEvent(d){return this._shouldLabel(d)?"all":"none"}_doRenderLabel(bubbleGEnter){if(this.renderLabel()){let label=bubbleGEnter.select("text");if(label.empty()){label=bubbleGEnter.append("text").attr("text-anchor","middle").attr("dy",".3em").on("click",d3compat.eventHandler(d=>this.onClick(d)))}label.attr("opacity",0).attr("pointer-events",d=>this._labelPointerEvent(d)).text(d=>this._labelFunction(d));transition(label,this.transitionDuration(),this.transitionDelay()).attr("opacity",d=>this._labelOpacity(d))}}doUpdateLabels(bubbleGEnter){if(this.renderLabel()){const labels=bubbleGEnter.select("text").attr("pointer-events",d=>this._labelPointerEvent(d)).text(d=>this._labelFunction(d));transition(labels,this.transitionDuration(),this.transitionDelay()).attr("opacity",d=>this._labelOpacity(d))}}_titleFunction(d){return this.title()(d)}_doRenderTitles(g){if(this.renderTitle()){const title=g.select("title");if(title.empty()){g.append("title").text(d=>this._titleFunction(d))}}}doUpdateTitles(g){if(this.renderTitle()){g.select("title").text(d=>this._titleFunction(d))}}sortBubbleSize(sortBubbleSize){if(!arguments.length){return this._sortBubbleSize}this._sortBubbleSize=sortBubbleSize;return this}minRadius(radius){if(!arguments.length){return this.MIN_RADIUS}this.MIN_RADIUS=radius;return this}minRadiusWithLabel(radius){if(!arguments.length){return this._minRadiusWithLabel}this._minRadiusWithLabel=radius;return this}maxBubbleRelativeSize(relativeSize){if(!arguments.length){return this._maxBubbleRelativeSize}this._maxBubbleRelativeSize=relativeSize;return this}excludeElasticZero(excludeZero){if(!arguments.length){return this._excludeElasticZero}this._excludeElasticZero=excludeZero;return this}fadeDeselectedArea(selection){if(this.hasFilter()){const chart=this;this.selectAll(`g.${chart.BUBBLE_NODE_CLASS}`).each(function(d){if(chart.isSelectedNode(d)){chart.highlightSelected(this)}else{chart.fadeDeselected(this)}})}else{const chart=this;this.selectAll(`g.${chart.BUBBLE_NODE_CLASS}`).each(function(){chart.resetHighlight(this)})}}isSelectedNode(d){return this.hasFilter(d.key)}onClick(d){const filter=d.key;events.trigger(()=>{this.filter(filter);this.redrawGroup()})}};const CapMixin=Base=>class extends Base{constructor(){super();this._cap=Infinity;this._takeFront=true;this._othersLabel="Others";this._othersGrouper=(topItems,restItems)=>{const restItemsSum=d3Array.sum(restItems,this.valueAccessor()),restKeys=restItems.map(this.keyAccessor());if(restItemsSum>0){return topItems.concat([{others:restKeys,key:this.othersLabel(),value:restItemsSum}])}return topItems};this.ordering(kv=>-kv.value);this.data(group=>{if(this._cap===Infinity){return this._computeOrderedGroups(group.all())}else{let items=group.all(),rest;items=this._computeOrderedGroups(items);if(this._cap){if(this._takeFront){rest=items.slice(this._cap);items=items.slice(0,this._cap)}else{const start=Math.max(0,items.length-this._cap);rest=items.slice(0,start);items=items.slice(start)}}if(this._othersGrouper){return this._othersGrouper(items,rest)}return items}})}cappedKeyAccessor(d,i){if(d.others){return d.key}return this.keyAccessor()(d,i)}cappedValueAccessor(d,i){if(d.others){return d.value}return this.valueAccessor()(d,i)}cap(count){if(!arguments.length){return this._cap}this._cap=count;return this}takeFront(takeFront){if(!arguments.length){return this._takeFront}this._takeFront=takeFront;return this}othersLabel(label){if(!arguments.length){return this._othersLabel}this._othersLabel=label;return this}othersGrouper(grouperFunction){if(!arguments.length){return this._othersGrouper}this._othersGrouper=grouperFunction;return this}onClick(d){if(d.others){this.filter([d.others])}super.onClick(d)}};class MarginMixin extends BaseMixin{constructor(){super();this._margin={top:10,right:50,bottom:30,left:30}}margins(margins){if(!arguments.length){return this._margin}this._margin=margins;return this}effectiveWidth(){return this.width()-this.margins().left-this.margins().right}effectiveHeight(){return this.height()-this.margins().top-this.margins().bottom}}const GRID_LINE_CLASS="grid-line";const HORIZONTAL_CLASS="horizontal";const VERTICAL_CLASS="vertical";const Y_AXIS_LABEL_CLASS="y-axis-label";const X_AXIS_LABEL_CLASS="x-axis-label";const CUSTOM_BRUSH_HANDLE_CLASS="custom-brush-handle";const DEFAULT_AXIS_LABEL_PADDING=12;class CoordinateGridMixin extends ColorMixin(MarginMixin){constructor(){super();this.colors(d3Scale.scaleOrdinal(d3ScaleChromatic.schemeCategory10));this._mandatoryAttributes().push("x");this._parent=undefined;this._g=undefined;this._chartBodyG=undefined;this._x=undefined;this._origX=undefined;this._xOriginalDomain=undefined;this._xAxis=null;this._xUnits=units.integers;this._xAxisPadding=0;this._xAxisPaddingUnit=d3Time.timeDay;this._xElasticity=false;this._xAxisLabel=undefined;this._xAxisLabelPadding=0;this._lastXDomain=undefined;this._y=undefined;this._yAxis=null;this._yAxisPadding=0;this._yElasticity=false;this._yAxisLabel=undefined;this._yAxisLabelPadding=0;this._brush=d3Brush.brushX();this._gBrush=undefined;this._brushOn=true;this._parentBrushOn=false;this._round=undefined;this._ignoreBrushEvents=false;this._renderHorizontalGridLine=false;this._renderVerticalGridLine=false;this._resizing=false;this._unitCount=undefined;this._zoomScale=[1,Infinity];this._zoomOutRestrict=true;this._zoom=d3Zoom.zoom().on("zoom",d3compat.eventHandler((d,evt)=>this._onZoom(evt)));this._nullZoom=d3Zoom.zoom().on("zoom",null);this._hasBeenMouseZoomable=false;this._ignoreZoomEvents=false;this._rangeChart=undefined;this._focusChart=undefined;this._mouseZoomable=false;this._clipPadding=0;this._fOuterRangeBandPadding=.5;this._fRangeBandPadding=0;this._useRightYAxis=false;this._useTopXAxis=false}rescale(){this._unitCount=undefined;this._resizing=true;return this}resizing(resizing){if(!arguments.length){return this._resizing}this._resizing=resizing;return this}rangeChart(rangeChart){if(!arguments.length){return this._rangeChart}this._rangeChart=rangeChart;this._rangeChart.focusChart(this);return this}zoomScale(extent){if(!arguments.length){return this._zoomScale}this._zoomScale=extent;return this}zoomOutRestrict(zoomOutRestrict){if(!arguments.length){return this._zoomOutRestrict}this._zoomOutRestrict=zoomOutRestrict;return this}_generateG(parent){if(parent===undefined){this._parent=this.svg()}else{this._parent=parent}const href=window.location.href.split("#")[0];this._g=this._parent.append("g");this._chartBodyG=this._g.append("g").attr("class","chart-body").attr("transform",`translate(${this.margins().left}, ${this.margins().top})`).attr("clip-path",`url(${href}#${this._getClipPathId()})`);return this._g}g(gElement){if(!arguments.length){return this._g}this._g=gElement;return this}mouseZoomable(mouseZoomable){if(!arguments.length){return this._mouseZoomable}this._mouseZoomable=mouseZoomable;return this}chartBodyG(chartBodyG){if(!arguments.length){return this._chartBodyG}this._chartBodyG=chartBodyG;return this}x(xScale){if(!arguments.length){return this._x}this._x=xScale;this._xOriginalDomain=this._x.domain();this.rescale();return this}xOriginalDomain(){return this._xOriginalDomain}xUnits(xUnits){if(!arguments.length){return this._xUnits}this._xUnits=xUnits;return this}xAxis(xAxis){if(!arguments.length){if(!this._xAxis){this._xAxis=this._createXAxis()}return this._xAxis}this._xAxis=xAxis;return this}elasticX(elasticX){if(!arguments.length){return this._xElasticity}this._xElasticity=elasticX;return this}xAxisPadding(padding){if(!arguments.length){return this._xAxisPadding}this._xAxisPadding=padding;return this}xAxisPaddingUnit(unit){if(!arguments.length){return this._xAxisPaddingUnit}this._xAxisPaddingUnit=unit;return this}xUnitCount(){if(this._unitCount===undefined){if(this.isOrdinal()){this._unitCount=this.x().domain().length}else{this._unitCount=this.xUnits()(this.x().domain()[0],this.x().domain()[1]);if(this._unitCount instanceof Array){this._unitCount=this._unitCount.length}}}return this._unitCount}useRightYAxis(useRightYAxis){if(!arguments.length){return this._useRightYAxis}if(this._useRightYAxis!==useRightYAxis&&this._yAxis){logger.warn("Value of useRightYAxis has been altered, after yAxis was created. "+"You might get unexpected yAxis behavior. "+"Make calls to useRightYAxis sooner in your chart creation process.")}this._useRightYAxis=useRightYAxis;return this}useTopXAxis(useTopXAxis){if(!arguments.length){return this._useTopXAxis}if(this._useTopXAxis!==useTopXAxis&&this._xAxis){logger.warn("Value of useTopXAxis has been altered, after xAxis was created. "+"You might get unexpected yAxis behavior. "+"Make calls to useTopXAxis sooner in your chart creation process.")}this._useTopXAxis=useTopXAxis;return this}isOrdinal(){return this.xUnits()===units.ordinal}_useOuterPadding(){return true}_ordinalXDomain(){const groups=this._computeOrderedGroups(this.data());return groups.map(this.keyAccessor())}_createXAxis(){return this._useTopXAxis?d3Axis.axisTop():d3Axis.axisBottom()}_prepareXAxis(g,render){if(!this.isOrdinal()){if(this.elasticX()){this._x.domain([this.xAxisMin(),this.xAxisMax()])}}else{if(!this._x.bandwidth){logger.warn("For compatibility with d3v4+, dc.js d3.0 ordinal bar/line/bubble charts need "+"d3.scaleBand() for the x scale, instead of d3.scaleOrdinal(). "+"Replacing .x() with a d3.scaleBand with the same domain - "+"make the same change in your code to avoid this warning!");this._x=d3Scale.scaleBand().domain(this._x.domain())}if(this.elasticX()||this._x.domain().length===0){this._x.domain(this._ordinalXDomain())}}const xdom=this._x.domain();if(render||!utils.arraysEqual(this._lastXDomain,xdom)){this.rescale()}this._lastXDomain=xdom;if(this.isOrdinal()){this._x.range([0,this.xAxisLength()]).paddingInner(this._fRangeBandPadding).paddingOuter(this._useOuterPadding()?this._fOuterRangeBandPadding:0)}else{this._x.range([0,this.xAxisLength()])}if(!this._xAxis){this._xAxis=this._createXAxis()}this._xAxis=this._xAxis.scale(this.x());this._renderVerticalGridLines(g)}renderXAxis(g){let axisXG=g.select("g.x");if(axisXG.empty()){axisXG=g.append("g").attr("class","axis x").attr("transform",`translate(${this.margins().left},${this._xAxisY()})`)}let axisXLab=g.select(`text.${X_AXIS_LABEL_CLASS}`);const axisXLabY=this._useTopXAxis?this._xAxisLabelPadding:this.height()-this._xAxisLabelPadding;if(axisXLab.empty()&&this.xAxisLabel()){axisXLab=g.append("text").attr("class",X_AXIS_LABEL_CLASS).attr("transform",`translate(${this.margins().left+this.xAxisLength()/2},${axisXLabY})`).attr("text-anchor","middle")}if(this.xAxisLabel()&&axisXLab.text()!==this.xAxisLabel()){axisXLab.text(this.xAxisLabel())}transition(axisXG,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${this.margins().left},${this._xAxisY()})`).call(this._xAxis);transition(axisXLab,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${this.margins().left+this.xAxisLength()/2},${axisXLabY})`)}_renderVerticalGridLines(g){let gridLineG=g.select(`g.${VERTICAL_CLASS}`);if(this._renderVerticalGridLine){if(gridLineG.empty()){gridLineG=g.insert("g",":first-child").attr("class",`${GRID_LINE_CLASS} ${VERTICAL_CLASS}`).attr("transform",`translate(${this.margins().left},${this.margins().top})`)}const ticks=this._xAxis.tickValues()?this._xAxis.tickValues():typeof this._x.ticks==="function"?this._x.ticks.apply(this._x,this._xAxis.tickArguments()):this._x.domain();const lines=gridLineG.selectAll("line").data(ticks);const linesGEnter=lines.enter().append("line").attr("x1",d=>this._x(d)).attr("y1",this._xAxisY()-this.margins().top).attr("x2",d=>this._x(d)).attr("y2",0).attr("opacity",0);transition(linesGEnter,this.transitionDuration(),this.transitionDelay()).attr("opacity",.5);transition(lines,this.transitionDuration(),this.transitionDelay()).attr("x1",d=>this._x(d)).attr("y1",this._xAxisY()-this.margins().top).attr("x2",d=>this._x(d)).attr("y2",0);lines.exit().remove()}else{gridLineG.selectAll("line").remove()}}_xAxisY(){return this._useTopXAxis?this.margins().top:this.height()-this.margins().bottom}xAxisLength(){return this.effectiveWidth()}xAxisLabel(labelText,padding){if(!arguments.length){return this._xAxisLabel}this._xAxisLabel=labelText;this.margins().bottom-=this._xAxisLabelPadding;this._xAxisLabelPadding=padding===undefined?DEFAULT_AXIS_LABEL_PADDING:padding;this.margins().bottom+=this._xAxisLabelPadding;return this}_createYAxis(){return this._useRightYAxis?d3Axis.axisRight():d3Axis.axisLeft()}_prepareYAxis(g){if(this._y===undefined||this.elasticY()){if(this._y===undefined){this._y=d3Scale.scaleLinear()}const _min=this.yAxisMin()||0;const _max=this.yAxisMax()||0;this._y.domain([_min,_max]).rangeRound([this.yAxisHeight(),0])}this._y.range([this.yAxisHeight(),0]);if(!this._yAxis){this._yAxis=this._createYAxis()}this._yAxis.scale(this._y);this._renderHorizontalGridLinesForAxis(g,this._y,this._yAxis)}renderYAxisLabel(axisClass,text,rotation,labelXPosition){labelXPosition=labelXPosition||this._yAxisLabelPadding;let axisYLab=this.g().select(`text.${Y_AXIS_LABEL_CLASS}.${axisClass}-label`);const labelYPosition=this.margins().top+this.yAxisHeight()/2;if(axisYLab.empty()&&text){axisYLab=this.g().append("text").attr("transform",`translate(${labelXPosition},${labelYPosition}),rotate(${rotation})`).attr("class",`${Y_AXIS_LABEL_CLASS} ${axisClass}-label`).attr("text-anchor","middle").text(text)}if(text&&axisYLab.text()!==text){axisYLab.text(text)}transition(axisYLab,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${labelXPosition},${labelYPosition}),rotate(${rotation})`)}renderYAxisAt(axisClass,axis,position){let axisYG=this.g().select(`g.${axisClass}`);if(axisYG.empty()){axisYG=this.g().append("g").attr("class",`axis ${axisClass}`).attr("transform",`translate(${position},${this.margins().top})`)}transition(axisYG,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${position},${this.margins().top})`).call(axis)}renderYAxis(){const axisPosition=this._useRightYAxis?this.width()-this.margins().right:this._yAxisX();this.renderYAxisAt("y",this._yAxis,axisPosition);const labelPosition=this._useRightYAxis?this.width()-this._yAxisLabelPadding:this._yAxisLabelPadding;const rotation=this._useRightYAxis?90:-90;this.renderYAxisLabel("y",this.yAxisLabel(),rotation,labelPosition)}_renderHorizontalGridLinesForAxis(g,scale,axis){let gridLineG=g.select(`g.${HORIZONTAL_CLASS}`);if(this._renderHorizontalGridLine){const ticks=axis.tickValues()?axis.tickValues():scale.ticks?scale.ticks.apply(scale,axis.tickArguments()):scale.domain();if(gridLineG.empty()){gridLineG=g.insert("g",":first-child").attr("class",`${GRID_LINE_CLASS} ${HORIZONTAL_CLASS}`).attr("transform",`translate(${this.margins().left},${this.margins().top})`)}const lines=gridLineG.selectAll("line").data(ticks);const linesGEnter=lines.enter().append("line").attr("x1",1).attr("y1",d=>scale(d)).attr("x2",this.xAxisLength()).attr("y2",d=>scale(d)).attr("opacity",0);transition(linesGEnter,this.transitionDuration(),this.transitionDelay()).attr("opacity",.5);transition(lines,this.transitionDuration(),this.transitionDelay()).attr("x1",1).attr("y1",d=>scale(d)).attr("x2",this.xAxisLength()).attr("y2",d=>scale(d));lines.exit().remove()}else{gridLineG.selectAll("line").remove()}}_yAxisX(){return this.useRightYAxis()?this.width()-this.margins().right:this.margins().left}yAxisLabel(labelText,padding){if(!arguments.length){return this._yAxisLabel}this._yAxisLabel=labelText;this.margins().left-=this._yAxisLabelPadding;this._yAxisLabelPadding=padding===undefined?DEFAULT_AXIS_LABEL_PADDING:padding;this.margins().left+=this._yAxisLabelPadding;return this}y(yScale){if(!arguments.length){return this._y}this._y=yScale;this.rescale();return this}yAxis(yAxis){if(!arguments.length){if(!this._yAxis){this._yAxis=this._createYAxis()}return this._yAxis}this._yAxis=yAxis;return this}elasticY(elasticY){if(!arguments.length){return this._yElasticity}this._yElasticity=elasticY;return this}renderHorizontalGridLines(renderHorizontalGridLines){if(!arguments.length){return this._renderHorizontalGridLine}this._renderHorizontalGridLine=renderHorizontalGridLines;return this}renderVerticalGridLines(renderVerticalGridLines){if(!arguments.length){return this._renderVerticalGridLine}this._renderVerticalGridLine=renderVerticalGridLines;return this}xAxisMin(){const m=d3Array.min(this.data(),e=>this.keyAccessor()(e));return utils.subtract(m,this._xAxisPadding,this._xAxisPaddingUnit)}xAxisMax(){const m=d3Array.max(this.data(),e=>this.keyAccessor()(e));return utils.add(m,this._xAxisPadding,this._xAxisPaddingUnit)}yAxisMin(){const m=d3Array.min(this.data(),e=>this.valueAccessor()(e));return utils.subtract(m,this._yAxisPadding)}yAxisMax(){const m=d3Array.max(this.data(),e=>this.valueAccessor()(e));return utils.add(m,this._yAxisPadding)}yAxisPadding(padding){if(!arguments.length){return this._yAxisPadding}this._yAxisPadding=padding;return this}yAxisHeight(){return this.effectiveHeight()}round(round){if(!arguments.length){return this._round}this._round=round;return this}_rangeBandPadding(_){if(!arguments.length){return this._fRangeBandPadding}this._fRangeBandPadding=_;return this}_outerRangeBandPadding(_){if(!arguments.length){return this._fOuterRangeBandPadding}this._fOuterRangeBandPadding=_;return this}filter(_){if(!arguments.length){return super.filter()}super.filter(_);this.redrawBrush(_,false);return this}brush(_){if(!arguments.length){return this._brush}this._brush=_;return this}renderBrush(g,doTransition){if(this._brushOn){this._brush.on("start brush end",d3compat.eventHandler((d,evt)=>this._brushing(evt)));this._gBrush=g.append("g").attr("class","brush").attr("transform",`translate(${this.margins().left},${this.margins().top})`);this.setBrushExtents();this.createBrushHandlePaths(this._gBrush,doTransition);this.redrawBrush(this.filter(),doTransition)}}createBrushHandlePaths(gBrush){let brushHandles=gBrush.selectAll(`path.${CUSTOM_BRUSH_HANDLE_CLASS}`).data([{type:"w"},{type:"e"}]);brushHandles=brushHandles.enter().append("path").attr("class",CUSTOM_BRUSH_HANDLE_CLASS).merge(brushHandles);brushHandles.attr("d",d=>this.resizeHandlePath(d))}extendBrush(brushSelection){if(brushSelection&&this.round()){brushSelection[0]=this.round()(brushSelection[0]);brushSelection[1]=this.round()(brushSelection[1])}return brushSelection}brushIsEmpty(brushSelection){return!brushSelection||brushSelection[1]<=brushSelection[0]}_brushing(evt){if(this._ignoreBrushEvents){return}let brushSelection=evt.selection;if(brushSelection){brushSelection=brushSelection.map(this.x().invert)}brushSelection=this.extendBrush(brushSelection);this.redrawBrush(brushSelection,false);const rangedFilter=this.brushIsEmpty(brushSelection)?null:filters.RangedFilter(brushSelection[0],brushSelection[1]);events.trigger(()=>{this.applyBrushSelection(rangedFilter)},constants.EVENT_DELAY)}applyBrushSelection(rangedFilter){this.replaceFilter(rangedFilter);this.redrawGroup()}_withoutBrushEvents(closure){const oldValue=this._ignoreBrushEvents;this._ignoreBrushEvents=true;try{closure()}finally{this._ignoreBrushEvents=oldValue}}setBrushExtents(doTransition){this._withoutBrushEvents(()=>{this._brush.extent([[0,0],[this.effectiveWidth(),this.effectiveHeight()]])});this._gBrush.call(this._brush)}redrawBrush(brushSelection,doTransition){if(this._brushOn&&this._gBrush){if(this._resizing){this.setBrushExtents(doTransition)}if(!brushSelection){this._withoutBrushEvents(()=>{this._gBrush.call(this._brush.move,null)});this._gBrush.selectAll(`path.${CUSTOM_BRUSH_HANDLE_CLASS}`).attr("display","none")}else{const scaledSelection=[this._x(brushSelection[0]),this._x(brushSelection[1])];const gBrush=optionalTransition(doTransition,this.transitionDuration(),this.transitionDelay())(this._gBrush);this._withoutBrushEvents(()=>{gBrush.call(this._brush.move,scaledSelection)});gBrush.selectAll(`path.${CUSTOM_BRUSH_HANDLE_CLASS}`).attr("display",null).attr("transform",(d,i)=>`translate(${this._x(brushSelection[i])}, 0)`).attr("d",d=>this.resizeHandlePath(d))}}this.fadeDeselectedArea(brushSelection)}fadeDeselectedArea(brushSelection){}resizeHandlePath(d){d=d.type;const e=+(d==="e"),x=e?1:-1,y=this.effectiveHeight()/3;return`M${.5*x},${y}A6,6 0 0 ${e} ${6.5*x},${y+6}V${2*y-6}A6,6 0 0 ${e} ${.5*x},${2*y}Z`+`M${2.5*x},${y+8}V${2*y-8}M${4.5*x},${y+8}V${2*y-8}`}_getClipPathId(){return`${this.anchorName().replace(/[ .#=\[\]"]/g,"-")}-clip`}clipPadding(padding){if(!arguments.length){return this._clipPadding}this._clipPadding=padding;return this}_generateClipPath(){const defs=utils.appendOrSelect(this._parent,"defs");const id=this._getClipPathId();const chartBodyClip=utils.appendOrSelect(defs,`#${id}`,"clipPath").attr("id",id);const padding=this._clipPadding*2;utils.appendOrSelect(chartBodyClip,"rect").attr("width",this.xAxisLength()+padding).attr("height",this.yAxisHeight()+padding).attr("transform",`translate(-${this._clipPadding}, -${this._clipPadding})`)}_preprocessData(){}_doRender(){this.resetSvg();this._preprocessData();this._generateG();this._generateClipPath();this._drawChart(true);this._configureMouseZoom();return this}_doRedraw(){this._preprocessData();this._drawChart(false);this._generateClipPath();return this}_drawChart(render){if(this.isOrdinal()){this._brushOn=false}this._prepareXAxis(this.g(),render);this._prepareYAxis(this.g());this.plotData();if(this.elasticX()||this._resizing||render){this.renderXAxis(this.g())}if(this.elasticY()||this._resizing||render){this.renderYAxis(this.g())}if(render){this.renderBrush(this.g(),false)}else{this.redrawBrush(this.filter(),this._resizing)}this.fadeDeselectedArea(this.filter());this.resizing(false)}_configureMouseZoom(){this._origX=this._x.copy();if(this._mouseZoomable){this._enableMouseZoom()}else if(this._hasBeenMouseZoomable){this._disableMouseZoom()}}_enableMouseZoom(){this._hasBeenMouseZoomable=true;const extent=[[0,0],[this.effectiveWidth(),this.effectiveHeight()]];this._zoom.scaleExtent(this._zoomScale).extent(extent).duration(this.transitionDuration());if(this._zoomOutRestrict){const zoomScaleMin=Math.max(this._zoomScale[0],1);this._zoom.translateExtent(extent).scaleExtent([zoomScaleMin,this._zoomScale[1]])}this.root().call(this._zoom);this._updateD3zoomTransform()}_disableMouseZoom(){this.root().call(this._nullZoom)}_zoomHandler(newDomain,noRaiseEvents){let domFilter;if(this._hasRangeSelected(newDomain)){this.x().domain(newDomain);domFilter=filters.RangedFilter(newDomain[0],newDomain[1])}else{this.x().domain(this._xOriginalDomain);domFilter=null}this.replaceFilter(domFilter);this.rescale();this.redraw();if(!noRaiseEvents){if(this._rangeChart&&!utils.arraysEqual(this.filter(),this._rangeChart.filter())){events.trigger(()=>{this._rangeChart.replaceFilter(domFilter);this._rangeChart.redraw()})}this._invokeZoomedListener();events.trigger(()=>{this.redrawGroup()},constants.EVENT_DELAY)}}_domainToZoomTransform(newDomain,origDomain,xScale){const k=(origDomain[1]-origDomain[0])/(newDomain[1]-newDomain[0]);const xt=-1*xScale(newDomain[0]);return d3Zoom.zoomIdentity.scale(k).translate(xt,0)}_updateD3zoomTransform(){if(this._zoom){this._withoutZoomEvents(()=>{this._zoom.transform(this.root(),this._domainToZoomTransform(this.x().domain(),this._xOriginalDomain,this._origX))})}}_withoutZoomEvents(closure){const oldValue=this._ignoreZoomEvents;this._ignoreZoomEvents=true;try{closure()}finally{this._ignoreZoomEvents=oldValue}}_onZoom(evt){if(this._ignoreZoomEvents){return}const newDomain=evt.transform.rescaleX(this._origX).domain();this.focus(newDomain,false)}_checkExtents(ext,outerLimits){if(!ext||ext.length!==2||!outerLimits||outerLimits.length!==2){return ext}if(ext[0]>outerLimits[1]||ext[1]<outerLimits[0]){console.warn("Could not intersect extents, will reset")}return[ext[0]>outerLimits[0]?ext[0]:outerLimits[0],ext[1]<outerLimits[1]?ext[1]:outerLimits[1]]}focus(range,noRaiseEvents){if(this._zoomOutRestrict){range=this._checkExtents(range,this._xOriginalDomain);if(this._rangeChart){range=this._checkExtents(range,this._rangeChart.x().domain())}}this._zoomHandler(range,noRaiseEvents);this._updateD3zoomTransform()}refocused(){return!utils.arraysEqual(this.x().domain(),this._xOriginalDomain)}focusChart(c){if(!arguments.length){return this._focusChart}this._focusChart=c;this.on("filtered.dcjs-range-chart",chart=>{if(!chart.filter()){events.trigger(()=>{this._focusChart.x().domain(this._focusChart.xOriginalDomain(),true)})}else if(!utils.arraysEqual(chart.filter(),this._focusChart.filter())){events.trigger(()=>{this._focusChart.focus(chart.filter(),true)})}});return this}brushOn(brushOn){if(!arguments.length){return this._brushOn}this._brushOn=brushOn;return this}parentBrushOn(brushOn){if(!arguments.length){return this._parentBrushOn}this._parentBrushOn=brushOn;return this}gBrush(){return this._gBrush}_hasRangeSelected(range){return range instanceof Array&&range.length>1}}const d3Box=function(){let width=1;let height=1;let duration=0;const delay=0;let domain=null;let value=Number;let whiskers=boxWhiskers;let quartiles=boxQuartiles;let tickFormat=null;let renderDataPoints=false;const dataRadius=3;let dataOpacity=.3;let dataWidthPortion=.8;let renderTitle=false;let showOutliers=true;let boldOutlier=false;function box(g){g.each(function(data,index){data=data.map(value).sort(d3Array.ascending);const _g=d3Selection.select(this);const n=data.length;let min;let max;if(n===0){return}const quartileData=data.quartiles=quartiles(data);const whiskerIndices=whiskers&&whiskers.call(this,data,index),whiskerData=whiskerIndices&&whiskerIndices.map(_i=>data[_i]);const outlierIndices=whiskerIndices?d3Array.range(0,whiskerIndices[0]).concat(d3Array.range(whiskerIndices[1]+1,n)):d3Array.range(n);if(showOutliers){min=data[0];max=data[n-1]}else{min=data[whiskerIndices[0]];max=data[whiskerIndices[1]]}const pointIndices=d3Array.range(whiskerIndices[0],whiskerIndices[1]+1);const x1=d3Scale.scaleLinear().domain(domain&&domain.call(this,data,index)||[min,max]).range([height,0]);const x0=this.__chart__||d3Scale.scaleLinear().domain([0,Infinity]).range(x1.range());this.__chart__=x1;const center=_g.selectAll("line.center").data(whiskerData?[whiskerData]:[]);center.enter().insert("line","rect").attr("class","center").attr("x1",width/2).attr("y1",d=>x0(d[0])).attr("x2",width/2).attr("y2",d=>x0(d[1])).style("opacity",1e-6).transition().duration(duration).delay(delay).style("opacity",1).attr("y1",d=>x1(d[0])).attr("y2",d=>x1(d[1]));center.transition().duration(duration).delay(delay).style("opacity",1).attr("x1",width/2).attr("x2",width/2).attr("y1",d=>x1(d[0])).attr("y2",d=>x1(d[1]));center.exit().transition().duration(duration).delay(delay).style("opacity",1e-6).attr("y1",d=>x1(d[0])).attr("y2",d=>x1(d[1])).remove();const _box=_g.selectAll("rect.box").data([quartileData]);_box.enter().append("rect").attr("class","box").attr("x",0).attr("y",d=>x0(d[2])).attr("width",width).attr("height",d=>x0(d[0])-x0(d[2])).style("fill-opacity",renderDataPoints?.1:1).transition().duration(duration).delay(delay).attr("y",d=>x1(d[2])).attr("height",d=>x1(d[0])-x1(d[2]));_box.transition().duration(duration).delay(delay).attr("width",width).attr("y",d=>x1(d[2])).attr("height",d=>x1(d[0])-x1(d[2]));const medianLine=_g.selectAll("line.median").data([quartileData[1]]);medianLine.enter().append("line").attr("class","median").attr("x1",0).attr("y1",x0).attr("x2",width).attr("y2",x0).transition().duration(duration).delay(delay).attr("y1",x1).attr("y2",x1);medianLine.transition().duration(duration).delay(delay).attr("x1",0).attr("x2",width).attr("y1",x1).attr("y2",x1);const whisker=_g.selectAll("line.whisker").data(whiskerData||[]);whisker.enter().insert("line","circle, text").attr("class","whisker").attr("x1",0).attr("y1",x0).attr("x2",width).attr("y2",x0).style("opacity",1e-6).transition().duration(duration).delay(delay).attr("y1",x1).attr("y2",x1).style("opacity",1);whisker.transition().duration(duration).delay(delay).attr("x1",0).attr("x2",width).attr("y1",x1).attr("y2",x1).style("opacity",1);whisker.exit().transition().duration(duration).delay(delay).attr("y1",x1).attr("y2",x1).style("opacity",1e-6).remove();if(showOutliers){const outlierClass=boldOutlier?"outlierBold":"outlier";const outlierSize=boldOutlier?3:5;const outlierX=boldOutlier?function(){return Math.floor(Math.random()*(width*dataWidthPortion)+1+(width-width*dataWidthPortion)/2)}:function(){return width/2};const outlier=_g.selectAll(`circle.${outlierClass}`).data(outlierIndices,Number);outlier.enter().insert("circle","text").attr("class",outlierClass).attr("r",outlierSize).attr("cx",outlierX).attr("cy",i=>x0(data[i])).style("opacity",1e-6).transition().duration(duration).delay(delay).attr("cy",i=>x1(data[i])).style("opacity",.6);if(renderTitle){outlier.selectAll("title").remove();outlier.append("title").text(i=>data[i])}outlier.transition().duration(duration).delay(delay).attr("cx",outlierX).attr("cy",i=>x1(data[i])).style("opacity",.6);outlier.exit().transition().duration(duration).delay(delay).attr("cy",0).style("opacity",1e-6).remove()}if(renderDataPoints){const point=_g.selectAll("circle.data").data(pointIndices);point.enter().insert("circle","text").attr("class","data").attr("r",dataRadius).attr("cx",()=>Math.floor(Math.random()*(width*dataWidthPortion)+1+(width-width*dataWidthPortion)/2)).attr("cy",i=>x0(data[i])).style("opacity",1e-6).transition().duration(duration).delay(delay).attr("cy",i=>x1(data[i])).style("opacity",dataOpacity);if(renderTitle){point.selectAll("title").remove();point.append("title").text(i=>data[i])}point.transition().duration(duration).delay(delay).attr("cx",()=>Math.floor(Math.random()*(width*dataWidthPortion)+1+(width-width*dataWidthPortion)/2)).attr("cy",i=>x1(data[i])).style("opacity",dataOpacity);point.exit().transition().duration(duration).delay(delay).attr("cy",0).style("opacity",1e-6).remove()}const format=tickFormat||x1.tickFormat(8);const boxTick=_g.selectAll("text.box").data(quartileData);boxTick.enter().append("text").attr("class","box").attr("dy",".3em").attr("dx",(d,i)=>i&1?6:-6).attr("x",(d,i)=>i&1?width:0).attr("y",x0).attr("text-anchor",(d,i)=>i&1?"start":"end").text(format).transition().duration(duration).delay(delay).attr("y",x1);boxTick.transition().duration(duration).delay(delay).text(format).attr("x",(d,i)=>i&1?width:0).attr("y",x1);const whiskerTick=_g.selectAll("text.whisker").data(whiskerData||[]);whiskerTick.enter().append("text").attr("class","whisker").attr("dy",".3em").attr("dx",6).attr("x",width).attr("y",x0).text(format).style("opacity",1e-6).transition().duration(duration).delay(delay).attr("y",x1).style("opacity",1);whiskerTick.transition().duration(duration).delay(delay).text(format).attr("x",width).attr("y",x1).style("opacity",1);whiskerTick.exit().transition().duration(duration).delay(delay).attr("y",x1).style("opacity",1e-6).remove();delete data.quartiles});d3Timer.timerFlush()}box.width=function(x){if(!arguments.length){return width}width=x;return box};box.height=function(x){if(!arguments.length){return height}height=x;return box};box.tickFormat=function(x){if(!arguments.length){return tickFormat}tickFormat=x;return box};box.showOutliers=function(x){if(!arguments.length){return showOutliers}showOutliers=x;return box};box.boldOutlier=function(x){if(!arguments.length){return boldOutlier}boldOutlier=x;return box};box.renderDataPoints=function(x){if(!arguments.length){return renderDataPoints}renderDataPoints=x;return box};box.renderTitle=function(x){if(!arguments.length){return renderTitle}renderTitle=x;return box};box.dataOpacity=function(x){if(!arguments.length){return dataOpacity}dataOpacity=x;return box};box.dataWidthPortion=function(x){if(!arguments.length){return dataWidthPortion}dataWidthPortion=x;return box};box.duration=function(x){if(!arguments.length){return duration}duration=x;return box};box.domain=function(x){if(!arguments.length){return domain}domain=x===null?x:typeof x==="function"?x:utils.constant(x);return box};box.value=function(x){if(!arguments.length){return value}value=x;return box};box.whiskers=function(x){if(!arguments.length){return whiskers}whiskers=x;return box};box.quartiles=function(x){if(!arguments.length){return quartiles}quartiles=x;return box};return box};function boxWhiskers(d){return[0,d.length-1]}function boxQuartiles(d){return[d3Array.quantile(d,.25),d3Array.quantile(d,.5),d3Array.quantile(d,.75)]}class StackMixin extends CoordinateGridMixin{constructor(){super();this._stackLayout=d3Shape.stack();this._stack=[];this._titles={};this._hidableStacks=false;this._evadeDomainFilter=false;this.data(()=>{const layers=this._stack.filter(this._visibility);if(!layers.length){return[]}layers.forEach((l,i)=>this._prepareValues(l,i));const v4data=layers[0].values.map((v,i)=>{const col={x:v.x};layers.forEach(layer=>{col[layer.name]=layer.values[i].y});return col});const keys=layers.map(layer=>layer.name);const v4result=this.stackLayout().keys(keys)(v4data);v4result.forEach((series,i)=>{series.forEach((ys,j)=>{layers[i].values[j].y0=ys[0];layers[i].values[j].y1=ys[1]})});return layers});this.colorAccessor(function(d){return this.layer||this.name||d.name||d.layer})}_prepareValues(layer,layerIdx){const valAccessor=layer.accessor||this.valueAccessor();layer.name=String(layer.name||layerIdx);const allValues=layer.group.all().map((d,i)=>({x:this.keyAccessor()(d,i),y:layer.hidden?null:valAccessor(d,i),data:d,layer:layer.name,hidden:layer.hidden}));layer.domainValues=allValues.filter(l=>this._domainFilter()(l));layer.values=this.evadeDomainFilter()?allValues:layer.domainValues}_domainFilter(){if(!this.x()){return utils.constant(true)}const xDomain=this.x().domain();if(this.isOrdinal()){return()=>true}if(this.elasticX()){return()=>true}return p=>p.x>=xDomain[0]&&p.x<=xDomain[xDomain.length-1]}stack(group,name,accessor){if(!arguments.length){return this._stack}if(arguments.length<=2){accessor=name}const layer={group:group};if(typeof name==="string"){layer.name=name}if(typeof accessor==="function"){layer.accessor=accessor}this._stack.push(layer);return this}group(g,n,f){if(!arguments.length){return super.group()}this._stack=[];this._titles={};this.stack(g,n);if(f){this.valueAccessor(f)}return super.group(g,n)}hidableStacks(hidableStacks){if(!arguments.length){return this._hidableStacks}this._hidableStacks=hidableStacks;return this}_findLayerByName(n){const i=this._stack.map(pluck("name")).indexOf(n);return this._stack[i]}hideStack(stackName){const layer=this._findLayerByName(stackName);if(layer){layer.hidden=true}return this}showStack(stackName){const layer=this._findLayerByName(stackName);if(layer){layer.hidden=false}return this}getValueAccessorByIndex(index){return this._stack[index].accessor||this.valueAccessor()}yAxisMin(){const m=d3Array.min(this._flattenStack(),p=>p.y<0?p.y+p.y0:p.y0);return utils.subtract(m,this.yAxisPadding())}yAxisMax(){const m=d3Array.max(this._flattenStack(),p=>p.y>0?p.y+p.y0:p.y0);return utils.add(m,this.yAxisPadding())}_flattenStack(){const values=this.data().map(layer=>layer.domainValues);return[].concat(...values)}xAxisMin(){const m=d3Array.min(this._flattenStack(),pluck("x"));return utils.subtract(m,this.xAxisPadding(),this.xAxisPaddingUnit())}xAxisMax(){const m=d3Array.max(this._flattenStack(),pluck("x"));return utils.add(m,this.xAxisPadding(),this.xAxisPaddingUnit())}title(stackName,titleAccessor){if(!stackName){return super.title()}if(typeof stackName==="function"){return super.title(stackName)}if(stackName===this._groupName&&typeof titleAccessor==="function"){return super.title(titleAccessor)}if(typeof titleAccessor!=="function"){return this._titles[stackName]||super.title()}this._titles[stackName]=titleAccessor;return this}stackLayout(_stack){if(!arguments.length){return this._stackLayout}this._stackLayout=_stack;return this}evadeDomainFilter(evadeDomainFilter){if(!arguments.length){return this._evadeDomainFilter}this._evadeDomainFilter=evadeDomainFilter;return this}_visibility(l){return!l.hidden}_ordinalXDomain(){const flat=this._flattenStack().map(pluck("data"));const ordered=this._computeOrderedGroups(flat);return ordered.map(this.keyAccessor())}legendables(){return this._stack.map((layer,i)=>({chart:this,name:layer.name,hidden:layer.hidden||false,color:this.getColor.call(layer,layer.values,i)}))}isLegendableHidden(d){const layer=this._findLayerByName(d.name);return layer?layer.hidden:false}legendToggle(d){if(this._hidableStacks){if(this.isLegendableHidden(d)){this.showStack(d.name)}else{this.hideStack(d.name)}this.renderGroup()}}}const MIN_BAR_WIDTH=1;const DEFAULT_GAP_BETWEEN_BARS=2;const LABEL_PADDING=3;class BarChart extends StackMixin{constructor(parent,chartGroup){super();this._gap=DEFAULT_GAP_BETWEEN_BARS;this._centerBar=false;this._alwaysUseRounding=false;this._barWidth=undefined;this.label(d=>utils.printSingleValue(d.y0+d.y),false);this.anchor(parent,chartGroup)}outerPadding(padding){if(!arguments.length){return this._outerRangeBandPadding()}return this._outerRangeBandPadding(padding)}rescale(){super.rescale();this._barWidth=undefined;return this}render(){if(this.round()&&this._centerBar&&!this._alwaysUseRounding){logger.warn("By default, brush rounding is disabled if bars are centered. "+"See dc.js bar chart API documentation for details.")}return super.render()}plotData(){let layers=this.chartBodyG().selectAll("g.stack").data(this.data());this._calculateBarWidth();layers=layers.enter().append("g").attr("class",(d,i)=>`stack _${i}`).merge(layers);const last=layers.size()-1;{const chart=this;layers.each(function(d,i){const layer=d3Selection.select(this);chart._renderBars(layer,i,d);if(chart.renderLabel()&&last===i){chart._renderLabels(layer,i,d)}})}}_barHeight(d){return utils.safeNumber(Math.abs(this.y()(d.y+d.y0)-this.y()(d.y0)))}_labelXPos(d){let x=this.x()(d.x);if(!this._centerBar){x+=this._barWidth/2}if(this.isOrdinal()&&this._gap!==undefined){x+=this._gap/2}return utils.safeNumber(x)}_labelYPos(d){let y=this.y()(d.y+d.y0);if(d.y<0){y-=this._barHeight(d)}return utils.safeNumber(y-LABEL_PADDING)}_renderLabels(layer,layerIndex,data){const labels=layer.selectAll("text.barLabel").data(data.values,pluck("x"));const labelsEnterUpdate=labels.enter().append("text").attr("class","barLabel").attr("text-anchor","middle").attr("x",d=>this._labelXPos(d)).attr("y",d=>this._labelYPos(d)).merge(labels);if(this.isOrdinal()){labelsEnterUpdate.on("click",d3compat.eventHandler(d=>this.onClick(d)));labelsEnterUpdate.attr("cursor","pointer")}transition(labelsEnterUpdate,this.transitionDuration(),this.transitionDelay()).attr("x",d=>this._labelXPos(d)).attr("y",d=>this._labelYPos(d)).text(d=>this.label()(d));transition(labels.exit(),this.transitionDuration(),this.transitionDelay()).attr("height",0).remove()}_barXPos(d){let x=this.x()(d.x);if(this._centerBar){x-=this._barWidth/2}if(this.isOrdinal()&&this._gap!==undefined){x+=this._gap/2}return utils.safeNumber(x)}_renderBars(layer,layerIndex,data){const bars=layer.selectAll("rect.bar").data(data.values,pluck("x"));const enter=bars.enter().append("rect").attr("class","bar").classed("dc-tabbable",this._keyboardAccessible).attr("fill",pluck("data",this.getColor)).attr("x",d=>this._barXPos(d)).attr("y",this.yAxisHeight()).attr("height",0);const barsEnterUpdate=enter.merge(bars);if(this.renderTitle()){enter.append("title").text(pluck("data",this.title(data.name)))}if(this.isOrdinal()){barsEnterUpdate.on("click",d3compat.eventHandler(d=>this.onClick(d)))}if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick)}transition(barsEnterUpdate,this.transitionDuration(),this.transitionDelay()).attr("x",d=>this._barXPos(d)).attr("y",d=>{let y=this.y()(d.y+d.y0);if(d.y<0){y-=this._barHeight(d)}return utils.safeNumber(y)}).attr("width",this._barWidth).attr("height",d=>this._barHeight(d)).attr("fill",pluck("data",this.getColor)).select("title").text(pluck("data",this.title(data.name)));transition(bars.exit(),this.transitionDuration(),this.transitionDelay()).attr("x",d=>this.x()(d.x)).attr("width",this._barWidth*.9).remove()}_calculateBarWidth(){if(this._barWidth===undefined){const numberOfBars=this.xUnitCount();if(this.isOrdinal()&&this._gap===undefined){this._barWidth=Math.floor(this.x().bandwidth())}else if(this._gap){this._barWidth=Math.floor((this.xAxisLength()-(numberOfBars-1)*this._gap)/numberOfBars)}else{this._barWidth=Math.floor(this.xAxisLength()/(1+this.barPadding())/numberOfBars)}if(this._barWidth===Infinity||isNaN(this._barWidth)||this._barWidth<MIN_BAR_WIDTH){this._barWidth=MIN_BAR_WIDTH}}}fadeDeselectedArea(brushSelection){const bars=this.chartBodyG().selectAll("rect.bar");if(this.isOrdinal()){if(this.hasFilter()){bars.classed(constants.SELECTED_CLASS,d=>this.hasFilter(d.x));bars.classed(constants.DESELECTED_CLASS,d=>!this.hasFilter(d.x))}else{bars.classed(constants.SELECTED_CLASS,false);bars.classed(constants.DESELECTED_CLASS,false)}}else if(this.brushOn()||this.parentBrushOn()){if(!this.brushIsEmpty(brushSelection)){const start=brushSelection[0];const end=brushSelection[1];bars.classed(constants.DESELECTED_CLASS,d=>d.x<start||d.x>=end)}else{bars.classed(constants.DESELECTED_CLASS,false)}}}centerBar(centerBar){if(!arguments.length){return this._centerBar}this._centerBar=centerBar;return this}onClick(d){super.onClick(d.data)}barPadding(barPadding){if(!arguments.length){return this._rangeBandPadding()}this._rangeBandPadding(barPadding);this._gap=undefined;return this}_useOuterPadding(){return this._gap===undefined}gap(gap){if(!arguments.length){return this._gap}this._gap=gap;return this}extendBrush(brushSelection){if(brushSelection&&this.round()&&(!this._centerBar||this._alwaysUseRounding)){brushSelection[0]=this.round()(brushSelection[0]);brushSelection[1]=this.round()(brushSelection[1])}return brushSelection}alwaysUseRounding(alwaysUseRounding){if(!arguments.length){return this._alwaysUseRounding}this._alwaysUseRounding=alwaysUseRounding;return this}legendHighlight(d){const colorFilter=(color,inv)=>function(){const item=d3Selection.select(this);const match=item.attr("fill")===color;return inv?!match:match};if(!this.isLegendableHidden(d)){this.g().selectAll("rect.bar").classed("highlight",colorFilter(d.color)).classed("fadeout",colorFilter(d.color,true))}}legendReset(){this.g().selectAll("rect.bar").classed("highlight",false).classed("fadeout",false)}xAxisMax(){let max=super.xAxisMax();if("resolution"in this.xUnits()){const res=this.xUnits().resolution;max+=res}return max}}const barChart=(parent,chartGroup)=>new BarChart(parent,chartGroup);function defaultWhiskersIQR(k){return d=>{const q1=d.quartiles[0];const q3=d.quartiles[2];const iqr=(q3-q1)*k;let i=-1;let j=d.length;do{++i}while(d[i]<q1-iqr);do{--j}while(d[j]>q3+iqr);return[i,j]}}class BoxPlot extends CoordinateGridMixin{constructor(parent,chartGroup){super();this._whiskerIqrFactor=1.5;this._whiskersIqr=defaultWhiskersIQR;this._whiskers=this._whiskersIqr(this._whiskerIqrFactor);this._box=d3Box();this._tickFormat=null;this._renderDataPoints=false;this._dataOpacity=.3;this._dataWidthPortion=.8;this._showOutliers=true;this._boldOutlier=false;this._yRangePadding=8;this._boxWidth=(innerChartWidth,xUnits)=>{if(this.isOrdinal()){return this.x().bandwidth()}else{return innerChartWidth/(1+this.boxPadding())/xUnits}};this.x(d3Scale.scaleBand());this.xUnits(units.ordinal);this.data(group=>group.all().map(d=>{d.map=accessor=>accessor.call(d,d);return d}).filter(d=>{const values=this.valueAccessor()(d);return values.length!==0}));this.boxPadding(.8);this.outerPadding(.5);this.anchor(parent,chartGroup)}boxPadding(padding){if(!arguments.length){return this._rangeBandPadding()}return this._rangeBandPadding(padding)}outerPadding(padding){if(!arguments.length){return this._outerRangeBandPadding()}return this._outerRangeBandPadding(padding)}boxWidth(boxWidth){if(!arguments.length){return this._boxWidth}this._boxWidth=typeof boxWidth==="function"?boxWidth:utils.constant(boxWidth);return this}_boxTransform(d,i){const xOffset=this.x()(this.keyAccessor()(d,i));return`translate(${xOffset}, 0)`}_preprocessData(){if(this.elasticX()){this.x().domain([])}}plotData(){this._calculatedBoxWidth=this._boxWidth(this.effectiveWidth(),this.xUnitCount());this._box.whiskers(this._whiskers).width(this._calculatedBoxWidth).height(this.effectiveHeight()).value(this.valueAccessor()).domain(this.y().domain()).duration(this.transitionDuration()).tickFormat(this._tickFormat).renderDataPoints(this._renderDataPoints).dataOpacity(this._dataOpacity).dataWidthPortion(this._dataWidthPortion).renderTitle(this.renderTitle()).showOutliers(this._showOutliers).boldOutlier(this._boldOutlier);const boxesG=this.chartBodyG().selectAll("g.box").data(this.data(),this.keyAccessor());const boxesGEnterUpdate=this._renderBoxes(boxesG);this._updateBoxes(boxesGEnterUpdate);this._removeBoxes(boxesG);this.fadeDeselectedArea(this.filter())}_renderBoxes(boxesG){const boxesGEnter=boxesG.enter().append("g");boxesGEnter.attr("class","box").classed("dc-tabbable",this._keyboardAccessible).attr("transform",(d,i)=>this._boxTransform(d,i)).call(this._box).on("click",d3compat.eventHandler(d=>{this.filter(this.keyAccessor()(d));this.redrawGroup()})).selectAll("circle").classed("dc-tabbable",this._keyboardAccessible);if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick)}return boxesGEnter.merge(boxesG)}_updateBoxes(boxesG){const chart=this;transition(boxesG,this.transitionDuration(),this.transitionDelay()).attr("transform",(d,i)=>this._boxTransform(d,i)).call(this._box).each(function(d){const color=chart.getColor(d,0);d3Selection.select(this).select("rect.box").attr("fill",color);d3Selection.select(this).selectAll("circle.data").attr("fill",color)})}_removeBoxes(boxesG){boxesG.exit().remove().call(this._box)}_minDataValue(){return d3Array.min(this.data(),e=>d3Array.min(this.valueAccessor()(e)))}_maxDataValue(){return d3Array.max(this.data(),e=>d3Array.max(this.valueAccessor()(e)))}_yAxisRangeRatio(){return(this._maxDataValue()-this._minDataValue())/this.effectiveHeight()}onClick(d){this.filter(this.keyAccessor()(d));this.redrawGroup()}fadeDeselectedArea(brushSelection){const chart=this;if(this.hasFilter()){if(this.isOrdinal()){this.g().selectAll("g.box").each(function(d){if(chart.isSelectedNode(d)){chart.highlightSelected(this)}else{chart.fadeDeselected(this)}})}else{if(!(this.brushOn()||this.parentBrushOn())){return}const start=brushSelection[0];const end=brushSelection[1];this.g().selectAll("g.box").each(function(d){const key=chart.keyAccessor()(d);if(key<start||key>=end){chart.fadeDeselected(this)}else{chart.highlightSelected(this)}})}}else{this.g().selectAll("g.box").each(function(){chart.resetHighlight(this)})}}isSelectedNode(d){return this.hasFilter(this.keyAccessor()(d))}yAxisMin(){const padding=this._yRangePadding*this._yAxisRangeRatio();return utils.subtract(this._minDataValue()-padding,this.yAxisPadding())}yAxisMax(){const padding=this._yRangePadding*this._yAxisRangeRatio();return utils.add(this._maxDataValue()+padding,this.yAxisPadding())}tickFormat(tickFormat){if(!arguments.length){return this._tickFormat}this._tickFormat=tickFormat;return this}yRangePadding(yRangePadding){if(!arguments.length){return this._yRangePadding}this._yRangePadding=yRangePadding;return this}renderDataPoints(show){if(!arguments.length){return this._renderDataPoints}this._renderDataPoints=show;return this}dataOpacity(opacity){if(!arguments.length){return this._dataOpacity}this._dataOpacity=opacity;return this}dataWidthPortion(percentage){if(!arguments.length){return this._dataWidthPortion}this._dataWidthPortion=percentage;return this}showOutliers(show){if(!arguments.length){return this._showOutliers}this._showOutliers=show;return this}boldOutlier(show){if(!arguments.length){return this._boldOutlier}this._boldOutlier=show;return this}}const boxPlot=(parent,chartGroup)=>new BoxPlot(parent,chartGroup);class BubbleChart extends BubbleMixin(CoordinateGridMixin){constructor(parent,chartGroup){super();this.transitionDuration(750);this.transitionDelay(0);this.anchor(parent,chartGroup)}_bubbleLocator(d){return`translate(${this._bubbleX(d)},${this._bubbleY(d)})`}plotData(){this.calculateRadiusDomain();this.r().range([this.MIN_RADIUS,this.xAxisLength()*this.maxBubbleRelativeSize()]);const data=this.data();let bubbleG=this.chartBodyG().selectAll(`g.${this.BUBBLE_NODE_CLASS}`).data(data,d=>d.key);if(this.sortBubbleSize()||this.keyboardAccessible()){bubbleG.order()}this._removeNodes(bubbleG);bubbleG=this._renderNodes(bubbleG);this._updateNodes(bubbleG);this.fadeDeselectedArea(this.filter())}_renderNodes(bubbleG){const bubbleGEnter=bubbleG.enter().append("g");bubbleGEnter.attr("class",this.BUBBLE_NODE_CLASS).attr("transform",d=>this._bubbleLocator(d)).append("circle").attr("class",(d,i)=>`${this.BUBBLE_CLASS} _${i}`).on("click",d3compat.eventHandler(d=>this.onClick(d))).classed("dc-tabbable",this._keyboardAccessible).attr("fill",this.getColor).attr("r",0);bubbleG=bubbleGEnter.merge(bubbleG);transition(bubbleG,this.transitionDuration(),this.transitionDelay()).select(`circle.${this.BUBBLE_CLASS}`).attr("r",d=>this.bubbleR(d)).attr("opacity",d=>this.bubbleR(d)>0?1:0);if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick)}this._doRenderLabel(bubbleGEnter);this._doRenderTitles(bubbleGEnter);return bubbleG}_updateNodes(bubbleG){transition(bubbleG,this.transitionDuration(),this.transitionDelay()).attr("transform",d=>this._bubbleLocator(d)).select(`circle.${this.BUBBLE_CLASS}`).attr("fill",this.getColor).attr("r",d=>this.bubbleR(d)).attr("opacity",d=>this.bubbleR(d)>0?1:0);this.doUpdateLabels(bubbleG);this.doUpdateTitles(bubbleG)}_removeNodes(bubbleG){bubbleG.exit().remove()}_bubbleX(d){let x=this.x()(this.keyAccessor()(d));if(isNaN(x)||!isFinite(x)){x=0}return x}_bubbleY(d){let y=this.y()(this.valueAccessor()(d));if(isNaN(y)||!isFinite(y)){y=0}return y}renderBrush(){}redrawBrush(brushSelection,doTransition){this.fadeDeselectedArea(brushSelection)}}const bubbleChart=(parent,chartGroup)=>new BubbleChart(parent,chartGroup);const BUBBLE_OVERLAY_CLASS="bubble-overlay";const BUBBLE_NODE_CLASS="node";const BUBBLE_CLASS="bubble";class BubbleOverlay extends BubbleMixin(BaseMixin){constructor(parent,chartGroup){super();this._g=undefined;this._points=[];this._keyboardAccessible=false;this.transitionDuration(750);this.transitionDelay(0);this.radiusValueAccessor(d=>d.value);this.anchor(parent,chartGroup)}point(name,x,y){this._points.push({name:name,x:x,y:y});return this}_doRender(){this._g=this._initOverlayG();this.r().range([this.MIN_RADIUS,this.width()*this.maxBubbleRelativeSize()]);this._initializeBubbles();this.fadeDeselectedArea(this.filter());return this}_initOverlayG(){this._g=this.select(`g.${BUBBLE_OVERLAY_CLASS}`);if(this._g.empty()){this._g=this.svg().append("g").attr("class",BUBBLE_OVERLAY_CLASS)}return this._g}_initializeBubbles(){const data=this._mapData();this.calculateRadiusDomain();this._points.forEach(point=>{const nodeG=this._getNodeG(point,data);let circle=nodeG.select(`circle.${BUBBLE_CLASS}`);if(circle.empty()){circle=nodeG.append("circle").attr("class",BUBBLE_CLASS).classed("dc-tabbable",this._keyboardAccessible).attr("r",0).attr("fill",this.getColor).on("click",d3compat.eventHandler(d=>this.onClick(d)))}if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick)}transition(circle,this.transitionDuration(),this.transitionDelay()).attr("r",d=>this.bubbleR(d));this._doRenderLabel(nodeG);this._doRenderTitles(nodeG)})}_mapData(){const data={};this.data().forEach(datum=>{data[this.keyAccessor()(datum)]=datum});return data}_getNodeG(point,data){const bubbleNodeClass=`${BUBBLE_NODE_CLASS} ${utils.nameToId(point.name)}`;let nodeG=this._g.select(`g.${utils.nameToId(point.name)}`);if(nodeG.empty()){nodeG=this._g.append("g").attr("class",bubbleNodeClass).attr("transform",`translate(${point.x},${point.y})`)}nodeG.datum(data[point.name]);return nodeG}_doRedraw(){this._updateBubbles();this.fadeDeselectedArea(this.filter());return this}_updateBubbles(){const data=this._mapData();this.calculateRadiusDomain();this._points.forEach(point=>{const nodeG=this._getNodeG(point,data);const circle=nodeG.select(`circle.${BUBBLE_CLASS}`);transition(circle,this.transitionDuration(),this.transitionDelay()).attr("r",d=>this.bubbleR(d)).attr("fill",this.getColor);this.doUpdateLabels(nodeG);this.doUpdateTitles(nodeG)})}debug(flag){if(flag){let debugG=this.select(`g.${constants.DEBUG_GROUP_CLASS}`);if(debugG.empty()){debugG=this.svg().append("g").attr("class",constants.DEBUG_GROUP_CLASS)}const debugText=debugG.append("text").attr("x",10).attr("y",20);debugG.append("rect").attr("width",this.width()).attr("height",this.height()).on("mousemove",d3compat.eventHandler((d,evt)=>{const position=d3compat.pointer(evt,debugG.node());const msg=`${position[0]}, ${position[1]}`;debugText.text(msg)}))}else{this.selectAll(".debug").remove()}return this}}const bubbleOverlay=(parent,chartGroup)=>new BubbleOverlay(parent,chartGroup);const GROUP_CSS_CLASS="dc-cbox-group";const ITEM_CSS_CLASS="dc-cbox-item";class CboxMenu extends BaseMixin{constructor(parent,chartGroup){super();this._cbox=undefined;this._promptText="Select all";this._multiple=false;this._inputType="radio";this._promptValue=null;this._uniqueId=utils.uniqueId();this.data(group=>group.all().filter(this._filterDisplayed));this._filterDisplayed=d=>this.valueAccessor()(d)>0;this._order=(a,b)=>{if(this.keyAccessor()(a)>this.keyAccessor()(b)){return 1}if(this.keyAccessor()(a)<this.keyAccessor()(b)){return-1}return 0};this.anchor(parent,chartGroup)}_doRender(){return this._doRedraw()}_doRedraw(){this.select("ul").remove();this._cbox=this.root().append("ul").classed(GROUP_CSS_CLASS,true);this._renderOptions();if(this.hasFilter()&&this._multiple){this._cbox.selectAll("input").property("checked",d=>d&&this.filters().indexOf(String(this.keyAccessor()(d)))>=0||false)}else if(this.hasFilter()){this._cbox.selectAll("input").property("checked",d=>{if(!d){return false}return this.keyAccessor()(d)===this.filter()})}return this}_renderOptions(){let options=this._cbox.selectAll(`li.${ITEM_CSS_CLASS}`).data(this.data(),d=>this.keyAccessor()(d));options.exit().remove();options=options.enter().append("li").classed(ITEM_CSS_CLASS,true).merge(options);options.append("input").attr("type",this._inputType).attr("value",d=>this.keyAccessor()(d)).attr("name",`domain_${this._uniqueId}`).attr("id",(d,i)=>`input_${this._uniqueId}_${i}`);options.append("label").attr("for",(d,i)=>`input_${this._uniqueId}_${i}`).text(this.title());const chart=this;if(this._multiple){this._cbox.append("li").append("input").attr("type","reset").text(this._promptText).on("click",d3compat.eventHandler(function(d,evt){return chart._onChange(d,evt,this)}))}else{const li=this._cbox.append("li");li.append("input").attr("type",this._inputType).attr("value",this._promptValue).attr("name",`domain_${this._uniqueId}`).attr("id",(d,i)=>`input_${this._uniqueId}_all`).property("checked",true);li.append("label").attr("for",(d,i)=>`input_${this._uniqueId}_all`).text(this._promptText)}this._cbox.selectAll(`li.${ITEM_CSS_CLASS}`).sort(this._order);this._cbox.on("change",d3compat.eventHandler(function(d,evt){return chart._onChange(d,evt,this)}));return options}_onChange(d,evt,element){let values;const target=d3Selection.select(evt.target);let options;if(!target.datum()){values=this._promptValue||null}else{options=d3Selection.select(element).selectAll("input").filter(function(o){if(o){return this.checked}});values=options.nodes().map(option=>option.value);if(!this._multiple&&values.length===1){values=values[0]}}this.onChange(values)}onChange(val){if(val&&this._multiple){this.replaceFilter([val])}else if(val){this.replaceFilter(val)}else{this.filterAll()}events.trigger(()=>{this.redrawGroup()})}order(order){if(!arguments.length){return this._order}this._order=order;return this}promptText(promptText){if(!arguments.length){return this._promptText}this._promptText=promptText;return this}filterDisplayed(filterDisplayed){if(!arguments.length){return this._filterDisplayed}this._filterDisplayed=filterDisplayed;return this}multiple(multiple){if(!arguments.length){return this._multiple}this._multiple=multiple;if(this._multiple){this._inputType="checkbox"}else{this._inputType="radio"}return this}promptValue(promptValue){if(!arguments.length){return this._promptValue}this._promptValue=promptValue;return this}}const cboxMenu=(parent,chartGroup)=>new CboxMenu(parent,chartGroup);const SUB_CHART_CLASS="sub";const DEFAULT_RIGHT_Y_AXIS_LABEL_PADDING=12;class CompositeChart extends CoordinateGridMixin{constructor(parent,chartGroup){super();this._children=[];this._childOptions={};this._shareColors=false;this._shareTitle=true;this._alignYAxes=false;this._rightYAxis=d3Axis.axisRight();this._rightYAxisLabel=0;this._rightYAxisLabelPadding=DEFAULT_RIGHT_Y_AXIS_LABEL_PADDING;this._rightY=undefined;this._rightAxisGridLines=false;this._mandatoryAttributes([]);this.transitionDuration(500);this.transitionDelay(0);this.on("filtered.dcjs-composite-chart",chart=>{for(let i=0;i<this._children.length;++i){this._children[i].replaceFilter(this.filter())}});this.anchor(parent,chartGroup)}_generateG(){const g=super._generateG();for(let i=0;i<this._children.length;++i){const child=this._children[i];this._generateChildG(child,i);if(!child.dimension()){child.dimension(this.dimension())}if(!child.group()){child.group(this.group())}child.chartGroup(this.chartGroup());child.svg(this.svg());child.xUnits(this.xUnits());child.transitionDuration(this.transitionDuration(),this.transitionDelay());child.parentBrushOn(this.brushOn());child.brushOn(false);child.renderTitle(this.renderTitle());child.elasticX(this.elasticX())}return g}rescale(){super.rescale();this._children.forEach(child=>{child.rescale()});return this}resizing(resizing){if(!arguments.length){return super.resizing()}super.resizing(resizing);this._children.forEach(child=>{child.resizing(resizing)});return this}_prepareYAxis(){const left=this._leftYAxisChildren().length!==0;const right=this._rightYAxisChildren().length!==0;const ranges=this._calculateYAxisRanges(left,right);if(left){this._prepareLeftYAxis(ranges)}if(right){this._prepareRightYAxis(ranges)}if(this._leftYAxisChildren().length>0&&!this._rightAxisGridLines){this._renderHorizontalGridLinesForAxis(this.g(),this.y(),this.yAxis())}else if(this._rightYAxisChildren().length>0){this._renderHorizontalGridLinesForAxis(this.g(),this._rightY,this._rightYAxis)}}renderYAxis(){if(this._leftYAxisChildren().length!==0){this.renderYAxisAt("y",this.yAxis(),this.margins().left);this.renderYAxisLabel("y",this.yAxisLabel(),-90)}if(this._rightYAxisChildren().length!==0){this.renderYAxisAt("yr",this.rightYAxis(),this.width()-this.margins().right);this.renderYAxisLabel("yr",this.rightYAxisLabel(),90,this.width()-this._rightYAxisLabelPadding)}}_calculateYAxisRanges(left,right){let lyAxisMin,lyAxisMax,ryAxisMin,ryAxisMax;let ranges;if(left){lyAxisMin=this._yAxisMin();lyAxisMax=this._yAxisMax()}if(right){ryAxisMin=this._rightYAxisMin();ryAxisMax=this._rightYAxisMax()}if(this.alignYAxes()&&left&&right){ranges=this._alignYAxisRanges(lyAxisMin,lyAxisMax,ryAxisMin,ryAxisMax)}return ranges||{lyAxisMin:lyAxisMin,lyAxisMax:lyAxisMax,ryAxisMin:ryAxisMin,ryAxisMax:ryAxisMax}}_alignYAxisRanges(lyAxisMin,lyAxisMax,ryAxisMin,ryAxisMax){const extentRatio=(ryAxisMax-ryAxisMin)/(lyAxisMax-lyAxisMin);return{lyAxisMin:Math.min(lyAxisMin,ryAxisMin/extentRatio),lyAxisMax:Math.max(lyAxisMax,ryAxisMax/extentRatio),ryAxisMin:Math.min(ryAxisMin,lyAxisMin*extentRatio),ryAxisMax:Math.max(ryAxisMax,lyAxisMax*extentRatio)}}_prepareRightYAxis(ranges){const needDomain=this.rightY()===undefined||this.elasticY(),needRange=needDomain||this.resizing();if(this.rightY()===undefined){this.rightY(d3Scale.scaleLinear())}if(needDomain){this.rightY().domain([ranges.ryAxisMin,ranges.ryAxisMax])}if(needRange){this.rightY().rangeRound([this.yAxisHeight(),0])}this.rightY().range([this.yAxisHeight(),0]);this.rightYAxis(this.rightYAxis().scale(this.rightY()))}_prepareLeftYAxis(ranges){const needDomain=this.y()===undefined||this.elasticY(),needRange=needDomain||this.resizing();if(this.y()===undefined){this.y(d3Scale.scaleLinear())}if(needDomain){this.y().domain([ranges.lyAxisMin,ranges.lyAxisMax])}if(needRange){this.y().rangeRound([this.yAxisHeight(),0])}this.y().range([this.yAxisHeight(),0]);this.yAxis(this.yAxis().scale(this.y()))}_generateChildG(child,i){child._generateG(this.g());child.g().attr("class",`${SUB_CHART_CLASS} _${i}`)}plotData(){for(let i=0;i<this._children.length;++i){const child=this._children[i];if(!child.g()){this._generateChildG(child,i)}if(this._shareColors){child.colors(this.colors())}child.x(this.x());child.xAxis(this.xAxis());if(child.useRightYAxis()){child.y(this.rightY());child.yAxis(this.rightYAxis())}else{child.y(this.y());child.yAxis(this.yAxis())}child.plotData();child._activateRenderlets()}}useRightAxisGridLines(useRightAxisGridLines){if(!arguments){return this._rightAxisGridLines}this._rightAxisGridLines=useRightAxisGridLines;return this}childOptions(childOptions){if(!arguments.length){return this._childOptions}this._childOptions=childOptions;this._children.forEach(child=>{child.options(this._childOptions)});return this}fadeDeselectedArea(brushSelection){if(this.brushOn()){for(let i=0;i<this._children.length;++i){const child=this._children[i];child.fadeDeselectedArea(brushSelection)}}}rightYAxisLabel(rightYAxisLabel,padding){if(!arguments.length){return this._rightYAxisLabel}this._rightYAxisLabel=rightYAxisLabel;this.margins().right-=this._rightYAxisLabelPadding;this._rightYAxisLabelPadding=padding===undefined?DEFAULT_RIGHT_Y_AXIS_LABEL_PADDING:padding;this.margins().right+=this._rightYAxisLabelPadding;return this}compose(subChartArray){this._children=subChartArray;this._children.forEach(child=>{child.height(this.height());child.width(this.width());child.margins(this.margins());if(this._shareTitle){child.title(this.title())}child.options(this._childOptions)});this.rescale();return this}_setChildrenProperty(prop,value){this._children.forEach(child=>{child[prop](value)})}height(height){if(!arguments.length){return super.height()}super.height(height);this._setChildrenProperty("height",height);return this}width(width){if(!arguments.length){return super.width()}super.width(width);this._setChildrenProperty("width",width);return this}margins(margins){if(!arguments.length){return super.margins()}super.margins(margins);this._setChildrenProperty("margins",margins);return this}children(){return this._children}shareColors(shareColors){if(!arguments.length){return this._shareColors}this._shareColors=shareColors;return this}shareTitle(shareTitle){if(!arguments.length){return this._shareTitle}this._shareTitle=shareTitle;return this}rightY(yScale){if(!arguments.length){return this._rightY}this._rightY=yScale;this.rescale();return this}alignYAxes(alignYAxes){if(!arguments.length){return this._alignYAxes}this._alignYAxes=alignYAxes;this.rescale();return this}_leftYAxisChildren(){return this._children.filter(child=>!child.useRightYAxis())}_rightYAxisChildren(){return this._children.filter(child=>child.useRightYAxis())}_getYAxisMin(charts){return charts.map(c=>c.yAxisMin())}_yAxisMin(){return d3Array.min(this._getYAxisMin(this._leftYAxisChildren()))}_rightYAxisMin(){return d3Array.min(this._getYAxisMin(this._rightYAxisChildren()))}_getYAxisMax(charts){return charts.map(c=>c.yAxisMax())}_yAxisMax(){return utils.add(d3Array.max(this._getYAxisMax(this._leftYAxisChildren())),this.yAxisPadding())}_rightYAxisMax(){return utils.add(d3Array.max(this._getYAxisMax(this._rightYAxisChildren())),this.yAxisPadding())}_getAllXAxisMinFromChildCharts(){return this._children.map(c=>c.xAxisMin())}xAxisMin(){return utils.subtract(d3Array.min(this._getAllXAxisMinFromChildCharts()),this.xAxisPadding(),this.xAxisPaddingUnit())}_getAllXAxisMaxFromChildCharts(){return this._children.map(c=>c.xAxisMax())}xAxisMax(){return utils.add(d3Array.max(this._getAllXAxisMaxFromChildCharts()),this.xAxisPadding(),this.xAxisPaddingUnit())}legendables(){return this._children.reduce((items,child)=>{if(this._shareColors){child.colors(this.colors())}items.push.apply(items,child.legendables());return items},[])}legendHighlight(d){for(let j=0;j<this._children.length;++j){const child=this._children[j];child.legendHighlight(d)}}legendReset(d){for(let j=0;j<this._children.length;++j){const child=this._children[j];child.legendReset(d)}}legendToggle(){console.log("composite should not be getting legendToggle itself")}rightYAxis(rightYAxis){if(!arguments.length){return this._rightYAxis}this._rightYAxis=rightYAxis;return this}yAxisMin(){throw new Error("Not supported for this chart type")}yAxisMax(){throw new Error("Not supported for this chart type")}}const compositeChart=(parent,chartGroup)=>new CompositeChart(parent,chartGroup);class DataCount extends BaseMixin{constructor(parent,chartGroup){super();this._formatNumber=d3Format.format(",d");this._crossfilter=null;this._groupAll=null;this._html={some:"",all:""};this._mandatoryAttributes(["crossfilter","groupAll"]);this.anchor(parent,chartGroup)}html(options){if(!arguments.length){return this._html}if(options.all){this._html.all=options.all}if(options.some){this._html.some=options.some}return this}formatNumber(formatter){if(!arguments.length){return this._formatNumber}this._formatNumber=formatter;return this}_doRender(){const tot=this.crossfilter().size(),val=this.groupAll().value();const all=this._formatNumber(tot);const selected=this._formatNumber(val);if(tot===val&&this._html.all!==""){this.root().html(this._html.all.replace("%total-count",all).replace("%filter-count",selected))}else if(this._html.some!==""){this.root().html(this._html.some.replace("%total-count",all).replace("%filter-count",selected))}else{this.selectAll(".total-count").text(all);this.selectAll(".filter-count").text(selected)}return this}_doRedraw(){return this._doRender()}crossfilter(cf){if(!arguments.length){return this._crossfilter}this._crossfilter=cf;return this}dimension(cf){logger.warnOnce("consider using dataCount.crossfilter instead of dataCount.dimension for clarity");if(!arguments.length){return this.crossfilter()}return this.crossfilter(cf)}groupAll(groupAll){if(!arguments.length){return this._groupAll}this._groupAll=groupAll;return this}group(groupAll){logger.warnOnce("consider using dataCount.groupAll instead of dataCount.group for clarity");if(!arguments.length){return this.groupAll()}return this.groupAll(groupAll)}}const dataCount=(parent,chartGroup)=>new DataCount(parent,chartGroup);const LABEL_CSS_CLASS="dc-grid-label";const ITEM_CSS_CLASS$1="dc-grid-item";const SECTION_CSS_CLASS="dc-grid-section dc-grid-group";const GRID_CSS_CLASS="dc-grid-top";class DataGrid extends BaseMixin{constructor(parent,chartGroup){super();this._section=null;this._size=999;this._html=function(d){return`you need to provide an html() handling param:  ${JSON.stringify(d)}`};this._sortBy=function(d){return d};this._order=d3Array.ascending;this._beginSlice=0;this._endSlice=undefined;this._htmlSection=d=>`<div class='${SECTION_CSS_CLASS}'><h1 class='${LABEL_CSS_CLASS}'>${this.keyAccessor()(d)}</h1></div>`;this._mandatoryAttributes(["dimension","section"]);this.anchor(parent,chartGroup)}_doRender(){this.selectAll(`div.${GRID_CSS_CLASS}`).remove();this._renderItems(this._renderSections());return this}_renderSections(){const sections=this.root().selectAll(`div.${GRID_CSS_CLASS}`).data(this._nestEntries(),d=>this.keyAccessor()(d));const itemSection=sections.enter().append("div").attr("class",GRID_CSS_CLASS);if(this._htmlSection){itemSection.html(d=>this._htmlSection(d))}sections.exit().remove();return itemSection}_nestEntries(){let entries=this.dimension().top(this._size);entries=entries.sort((a,b)=>this._order(this._sortBy(a),this._sortBy(b))).slice(this._beginSlice,this._endSlice);return d3compat.nester({key:this.section(),sortKeys:this._order,entries:entries})}_renderItems(sections){let items=sections.order().selectAll(`div.${ITEM_CSS_CLASS$1}`).data(d=>d.values);items.exit().remove();items=items.enter().append("div").attr("class",ITEM_CSS_CLASS$1).html(d=>this._html(d)).merge(items);return items}_doRedraw(){return this._doRender()}section(section){if(!arguments.length){return this._section}this._section=section;return this}group(section){logger.warnOnce("consider using dataGrid.section instead of dataGrid.group for clarity");if(!arguments.length){return this.section()}return this.section(section)}beginSlice(beginSlice){if(!arguments.length){return this._beginSlice}this._beginSlice=beginSlice;return this}endSlice(endSlice){if(!arguments.length){return this._endSlice}this._endSlice=endSlice;return this}size(size){if(!arguments.length){return this._size}this._size=size;return this}html(html){if(!arguments.length){return this._html}this._html=html;return this}htmlSection(htmlSection){if(!arguments.length){return this._htmlSection}this._htmlSection=htmlSection;return this}htmlGroup(htmlSection){logger.warnOnce("consider using dataGrid.htmlSection instead of dataGrid.htmlGroup for clarity");if(!arguments.length){return this.htmlSection()}return this.htmlSection(htmlSection)}sortBy(sortByFunction){if(!arguments.length){return this._sortBy}this._sortBy=sortByFunction;return this}order(order){if(!arguments.length){return this._order}this._order=order;return this}}const dataGrid=(parent,chartGroup)=>new DataGrid(parent,chartGroup);const LABEL_CSS_CLASS$1="dc-table-label";const ROW_CSS_CLASS="dc-table-row";const COLUMN_CSS_CLASS="dc-table-column";const SECTION_CSS_CLASS$1="dc-table-section dc-table-group";const HEAD_CSS_CLASS="dc-table-head";class DataTable extends BaseMixin{constructor(parent,chartGroup){super();this._size=25;this._columns=[];this._sortBy=d=>d;this._order=d3Array.ascending;this._beginSlice=0;this._endSlice=undefined;this._showSections=true;this._section=()=>"";this._mandatoryAttributes(["dimension"]);this.anchor(parent,chartGroup)}_doRender(){this.selectAll("tbody").remove();this._renderRows(this._renderSections());return this}_doColumnValueFormat(v,d){return typeof v==="function"?v(d):typeof v==="string"?d[v]:v.format(d)}_doColumnHeaderFormat(d){return typeof d==="function"?this._doColumnHeaderFnToString(d):typeof d==="string"?this._doColumnHeaderCapitalize(d):String(d.label)}_doColumnHeaderCapitalize(s){return s.charAt(0).toUpperCase()+s.slice(1)}_doColumnHeaderFnToString(f){let s=String(f);const i1=s.indexOf("return ");if(i1>=0){const i2=s.lastIndexOf(";");if(i2>=0){s=s.substring(i1+7,i2);const i3=s.indexOf("numberFormat");if(i3>=0){s=s.replace("numberFormat","")}}}return s}_renderSections(){let bAllFunctions=true;this._columns.forEach(f=>{bAllFunctions=bAllFunctions&typeof f==="function"});if(!bAllFunctions){let thead=this.selectAll("thead").data([0]);thead.exit().remove();thead=thead.enter().append("thead").merge(thead);let headrow=thead.selectAll("tr").data([0]);headrow.exit().remove();headrow=headrow.enter().append("tr").merge(headrow);const headcols=headrow.selectAll("th").data(this._columns);headcols.exit().remove();headcols.enter().append("th").merge(headcols).attr("class",HEAD_CSS_CLASS).html(d=>this._doColumnHeaderFormat(d))}const sections=this.root().selectAll("tbody").data(this._nestEntries(),d=>this.keyAccessor()(d));const rowSection=sections.enter().append("tbody");if(this._showSections===true){rowSection.append("tr").attr("class",SECTION_CSS_CLASS$1).append("td").attr("class",LABEL_CSS_CLASS$1).attr("colspan",this._columns.length).html(d=>this.keyAccessor()(d))}sections.exit().remove();return rowSection}_nestEntries(){let entries;if(this._order===d3Array.ascending){entries=this.dimension().bottom(this._size)}else{entries=this.dimension().top(this._size)}entries=entries.sort((a,b)=>this._order(this._sortBy(a),this._sortBy(b))).slice(this._beginSlice,this._endSlice);return d3compat.nester({key:this.section(),sortKeys:this._order,entries:entries})}_renderRows(sections){const rows=sections.order().selectAll(`tr.${ROW_CSS_CLASS}`).data(d=>d.values);const rowEnter=rows.enter().append("tr").attr("class",ROW_CSS_CLASS);this._columns.forEach((v,i)=>{rowEnter.append("td").attr("class",`${COLUMN_CSS_CLASS} _${i}`).html(d=>this._doColumnValueFormat(v,d))});rows.exit().remove();return rows}_doRedraw(){return this._doRender()}section(section){if(!arguments.length){return this._section}this._section=section;return this}group(section){logger.warnOnce("consider using dataTable.section instead of dataTable.group for clarity");if(!arguments.length){return this.section()}return this.section(section)}size(size){if(!arguments.length){return this._size}this._size=size;return this}beginSlice(beginSlice){if(!arguments.length){return this._beginSlice}this._beginSlice=beginSlice;return this}endSlice(endSlice){if(!arguments.length){return this._endSlice}this._endSlice=endSlice;return this}columns(columns){if(!arguments.length){return this._columns}this._columns=columns;return this}sortBy(sortBy){if(!arguments.length){return this._sortBy}this._sortBy=sortBy;return this}order(order){if(!arguments.length){return this._order}this._order=order;return this}showSections(showSections){if(!arguments.length){return this._showSections}this._showSections=showSections;return this}showGroups(showSections){logger.warnOnce("consider using dataTable.showSections instead of dataTable.showGroups for clarity");if(!arguments.length){return this.showSections()}return this.showSections(showSections)}}const dataTable=(parent,chartGroup)=>new DataTable(parent,chartGroup);class GeoChoroplethChart extends ColorMixin(BaseMixin){constructor(parent,chartGroup){super();this.colorAccessor(d=>d||0);this._geoPath=d3Geo.geoPath();this._projectionFlag=undefined;this._projection=undefined;this._geoJsons=[];this.anchor(parent,chartGroup)}_doRender(){this.resetSvg();for(let layerIndex=0;layerIndex<this._geoJsons.length;++layerIndex){const states=this.svg().append("g").attr("class",`layer${layerIndex}`);let regionG=states.selectAll(`g.${this._geoJson(layerIndex).name}`).data(this._geoJson(layerIndex).data);regionG=regionG.enter().append("g").attr("class",this._geoJson(layerIndex).name).merge(regionG);regionG.append("path").classed("dc-tabbable",this._keyboardAccessible).attr("fill","white").attr("d",this._getGeoPath());regionG.append("title");this._plotData(layerIndex)}this._projectionFlag=false}_plotData(layerIndex){const data=this._generateLayeredData();if(this._isDataLayer(layerIndex)){const regionG=this._renderRegionG(layerIndex);this._renderPaths(regionG,layerIndex,data);this._renderTitles(regionG,layerIndex,data)}}_generateLayeredData(){const data={};const groupAll=this.data();for(let i=0;i<groupAll.length;++i){data[this.keyAccessor()(groupAll[i])]=this.valueAccessor()(groupAll[i])}return data}_isDataLayer(layerIndex){return this._geoJson(layerIndex).keyAccessor}_renderRegionG(layerIndex){const regionG=this.svg().selectAll(this._layerSelector(layerIndex)).classed("selected",d=>this._isSelected(layerIndex,d)).classed("deselected",d=>this._isDeselected(layerIndex,d)).attr("class",d=>{const layerNameClass=this._geoJson(layerIndex).name;const regionClass=utils.nameToId(this._geoJson(layerIndex).keyAccessor(d));let baseClasses=`${layerNameClass} ${regionClass}`;if(this._isSelected(layerIndex,d)){baseClasses+=" selected"}if(this._isDeselected(layerIndex,d)){baseClasses+=" deselected"}return baseClasses});return regionG}_layerSelector(layerIndex){return`g.layer${layerIndex} g.${this._geoJson(layerIndex).name}`}_isSelected(layerIndex,d){return this.hasFilter()&&this.hasFilter(this._getKey(layerIndex,d))}_isDeselected(layerIndex,d){return this.hasFilter()&&!this.hasFilter(this._getKey(layerIndex,d))}_getKey(layerIndex,d){return this._geoJson(layerIndex).keyAccessor(d)}_geoJson(index){return this._geoJsons[index]}_renderPaths(regionG,layerIndex,data){const paths=regionG.select("path").attr("fill",function(){const currentFill=d3Selection.select(this).attr("fill");if(currentFill){return currentFill}return"none"}).on("click",d3compat.eventHandler(d=>this.onClick(d,layerIndex)));if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick,layerIndex)}transition(paths,this.transitionDuration(),this.transitionDelay()).attr("fill",(d,i)=>this.getColor(data[this._geoJson(layerIndex).keyAccessor(d)],i))}onClick(d,layerIndex){const selectedRegion=this._geoJson(layerIndex).keyAccessor(d);events.trigger(()=>{this.filter(selectedRegion);this.redrawGroup()})}_renderTitles(regionG,layerIndex,data){if(this.renderTitle()){regionG.selectAll("title").text(d=>{const key=this._getKey(layerIndex,d);const value=data[key];return this.title()({key:key,value:value})})}}_doRedraw(){for(let layerIndex=0;layerIndex<this._geoJsons.length;++layerIndex){this._plotData(layerIndex);if(this._projectionFlag){this.svg().selectAll(`g.${this._geoJson(layerIndex).name} path`).attr("d",this._getGeoPath())}}this._projectionFlag=false}overlayGeoJson(json,name,keyAccessor){for(let i=0;i<this._geoJsons.length;++i){if(this._geoJsons[i].name===name){this._geoJsons[i].data=json;this._geoJsons[i].keyAccessor=keyAccessor;return this}}this._geoJsons.push({name:name,data:json,keyAccessor:keyAccessor});return this}projection(projection){if(!arguments.length){return this._projection}this._projection=projection;this._projectionFlag=true;return this}_getGeoPath(){if(this._projection===undefined){logger.warn("choropleth projection default of geoAlbers is deprecated,"+" in next version projection will need to be set explicitly");return this._geoPath.projection(d3Geo.geoAlbersUsa())}return this._geoPath.projection(this._projection)}geoJsons(){return this._geoJsons}geoPath(){return this._geoPath}removeGeoJson(name){const geoJsons=[];for(let i=0;i<this._geoJsons.length;++i){const layer=this._geoJsons[i];if(layer.name!==name){geoJsons.push(layer)}}this._geoJsons=geoJsons;return this}}const geoChoroplethChart=(parent,chartGroup)=>new GeoChoroplethChart(parent,chartGroup);const DEFAULT_BORDER_RADIUS=6.75;class HeatMap extends ColorMixin(MarginMixin){constructor(parent,chartGroup){super();this._chartBody=undefined;this._cols=undefined;this._rows=undefined;this._colOrdering=d3Array.ascending;this._rowOrdering=d3Array.ascending;this._colScale=d3Scale.scaleBand();this._rowScale=d3Scale.scaleBand();this._xBorderRadius=DEFAULT_BORDER_RADIUS;this._yBorderRadius=DEFAULT_BORDER_RADIUS;this._mandatoryAttributes(["group"]);this.title(this.colorAccessor());this._colsLabel=d=>d;this._rowsLabel=d=>d;this._xAxisOnClick=d=>{this._filterAxis(0,d)};this._yAxisOnClick=d=>{this._filterAxis(1,d)};this._boxOnClick=d=>{const filter=d.key;events.trigger(()=>{this.filter(filters.TwoDimensionalFilter(filter));this.redrawGroup()})};this.anchor(parent,chartGroup)}colsLabel(labelFunction){if(!arguments.length){return this._colsLabel}this._colsLabel=labelFunction;return this}rowsLabel(labelFunction){if(!arguments.length){return this._rowsLabel}this._rowsLabel=labelFunction;return this}_filterAxis(axis,value){const cellsOnAxis=this.selectAll(".box-group").filter(d=>d.key[axis]===value);const unfilteredCellsOnAxis=cellsOnAxis.filter(d=>!this.hasFilter(d.key));events.trigger(()=>{const selection=unfilteredCellsOnAxis.empty()?cellsOnAxis:unfilteredCellsOnAxis;const filtersList=selection.data().map(kv=>filters.TwoDimensionalFilter(kv.key));this.filter([filtersList]);this.redrawGroup()})}filter(filter){const nonstandardFilter=f=>{logger.warnOnce("heatmap.filter taking a coordinate is deprecated - please pass dc.filters.TwoDimensionalFilter instead");return this._filter(filters.TwoDimensionalFilter(f))};if(!arguments.length){return super.filter()}if(filter!==null&&filter.filterType!=="TwoDimensionalFilter"&&!(Array.isArray(filter)&&Array.isArray(filter[0])&&filter[0][0].filterType==="TwoDimensionalFilter")){return nonstandardFilter(filter)}return super.filter(filter)}rows(rows){if(!arguments.length){return this._rows}this._rows=rows;return this}rowOrdering(rowOrdering){if(!arguments.length){return this._rowOrdering}this._rowOrdering=rowOrdering;return this}cols(cols){if(!arguments.length){return this._cols}this._cols=cols;return this}colOrdering(colOrdering){if(!arguments.length){return this._colOrdering}this._colOrdering=colOrdering;return this}_doRender(){this.resetSvg();this._chartBody=this.svg().append("g").attr("class","heatmap").attr("transform",`translate(${this.margins().left},${this.margins().top})`);return this._doRedraw()}_doRedraw(){const data=this.data();let rows=this.rows()||data.map(this.valueAccessor()),cols=this.cols()||data.map(this.keyAccessor());if(this._rowOrdering){rows=rows.sort(this._rowOrdering)}if(this._colOrdering){cols=cols.sort(this._colOrdering)}rows=this._rowScale.domain(rows);cols=this._colScale.domain(cols);const rowCount=rows.domain().length,colCount=cols.domain().length,boxWidth=Math.floor(this.effectiveWidth()/colCount),boxHeight=Math.floor(this.effectiveHeight()/rowCount);cols.rangeRound([0,this.effectiveWidth()]);rows.rangeRound([this.effectiveHeight(),0]);let boxes=this._chartBody.selectAll("g.box-group").data(this.data(),(d,i)=>`${this.keyAccessor()(d,i)}\0${this.valueAccessor()(d,i)}`);boxes.exit().remove();const gEnter=boxes.enter().append("g").attr("class","box-group");gEnter.append("rect").attr("class","heat-box").classed("dc-tabbable",this._keyboardAccessible).attr("fill","white").attr("x",(d,i)=>cols(this.keyAccessor()(d,i))).attr("y",(d,i)=>rows(this.valueAccessor()(d,i))).on("click",d3compat.eventHandler(this.boxOnClick()));if(this._keyboardAccessible){this._makeKeyboardAccessible(this.boxOnClick)}boxes=gEnter.merge(boxes);if(this.renderTitle()){gEnter.append("title");boxes.select("title").text(this.title())}transition(boxes.select("rect"),this.transitionDuration(),this.transitionDelay()).attr("x",(d,i)=>cols(this.keyAccessor()(d,i))).attr("y",(d,i)=>rows(this.valueAccessor()(d,i))).attr("rx",this._xBorderRadius).attr("ry",this._yBorderRadius).attr("fill",this.getColor).attr("width",boxWidth).attr("height",boxHeight);let gCols=this._chartBody.select("g.cols");if(gCols.empty()){gCols=this._chartBody.append("g").attr("class","cols axis")}let gColsText=gCols.selectAll("text").data(cols.domain());gColsText.exit().remove();gColsText=gColsText.enter().append("text").attr("x",d=>cols(d)+boxWidth/2).style("text-anchor","middle").attr("y",this.effectiveHeight()).attr("dy",12).on("click",d3compat.eventHandler(this.xAxisOnClick())).text(this.colsLabel()).merge(gColsText);transition(gColsText,this.transitionDuration(),this.transitionDelay()).text(this.colsLabel()).attr("x",d=>cols(d)+boxWidth/2).attr("y",this.effectiveHeight());let gRows=this._chartBody.select("g.rows");if(gRows.empty()){gRows=this._chartBody.append("g").attr("class","rows axis")}let gRowsText=gRows.selectAll("text").data(rows.domain());gRowsText.exit().remove();gRowsText=gRowsText.enter().append("text").style("text-anchor","end").attr("x",0).attr("dx",-2).attr("y",d=>rows(d)+boxHeight/2).attr("dy",6).on("click",d3compat.eventHandler(this.yAxisOnClick())).text(this.rowsLabel()).merge(gRowsText);transition(gRowsText,this.transitionDuration(),this.transitionDelay()).text(this.rowsLabel()).attr("y",d=>rows(d)+boxHeight/2);if(this.hasFilter()){const chart=this;this.selectAll("g.box-group").each(function(d){if(chart.isSelectedNode(d)){chart.highlightSelected(this)}else{chart.fadeDeselected(this)}})}else{const chart=this;this.selectAll("g.box-group").each(function(){chart.resetHighlight(this)})}return this}boxOnClick(handler){if(!arguments.length){return this._boxOnClick}this._boxOnClick=handler;return this}xAxisOnClick(handler){if(!arguments.length){return this._xAxisOnClick}this._xAxisOnClick=handler;return this}yAxisOnClick(handler){if(!arguments.length){return this._yAxisOnClick}this._yAxisOnClick=handler;return this}xBorderRadius(xBorderRadius){if(!arguments.length){return this._xBorderRadius}this._xBorderRadius=xBorderRadius;return this}yBorderRadius(yBorderRadius){if(!arguments.length){return this._yBorderRadius}this._yBorderRadius=yBorderRadius;return this}isSelectedNode(d){return this.hasFilter(d.key)}}const heatMap=(parent,chartGroup)=>new HeatMap(parent,chartGroup);class HtmlLegend{constructor(){this._htmlLegendDivCssClass="dc-html-legend";this._legendItemCssClassHorizontal="dc-legend-item-horizontal";this._legendItemCssClassVertical="dc-legend-item-vertical";this._parent=undefined;this._container=undefined;this._legendText=pluck("name");this._maxItems=undefined;this._horizontal=false;this._legendItemClass=undefined;this._highlightSelected=false;this._keyboardAccessible=false}parent(p){if(!arguments.length){return this._parent}this._parent=p;return this}render(){this._defaultLegendItemCssClass=this._horizontal?this._legendItemCssClassHorizontal:this._legendItemCssClassVertical;this._container.select(`div.${this._htmlLegendDivCssClass}`).remove();const container=this._container.append("div").attr("class",this._htmlLegendDivCssClass);container.attr("style",`max-width:${this._container.nodes()[0].style.width}`);let legendables=this._parent.legendables();const filters=this._parent.filters();if(this._maxItems!==undefined){legendables=legendables.slice(0,this._maxItems)}const legendItemClassName=this._legendItemClass?this._legendItemClass:this._defaultLegendItemCssClass;const itemEnter=container.selectAll(`div.${legendItemClassName}`).data(legendables).enter().append("div").classed(legendItemClassName,true).on("mouseover",d3compat.eventHandler(d=>this._parent.legendHighlight(d))).on("mouseout",d3compat.eventHandler(d=>this._parent.legendReset(d))).on("click",d3compat.eventHandler(d=>this._parent.legendToggle(d)));if(this._highlightSelected){itemEnter.classed(constants.SELECTED_CLASS,d=>filters.indexOf(d.name)!==-1)}itemEnter.append("span").attr("class","dc-legend-item-color").style("background-color",pluck("color"));itemEnter.append("span").attr("class","dc-legend-item-label").classed("dc-tabbable",this._keyboardAccessible).attr("title",this._legendText).text(this._legendText);if(this._keyboardAccessible){this._makeLegendKeyboardAccessible()}}container(container){if(!arguments.length){return this._container}this._container=d3Selection.select(container);return this}legendItemClass(legendItemClass){if(!arguments.length){return this._legendItemClass}this._legendItemClass=legendItemClass;return this}highlightSelected(highlightSelected){if(!arguments.length){return this._highlightSelected}this._highlightSelected=highlightSelected;return this}horizontal(horizontal){if(!arguments.length){return this._horizontal}this._horizontal=horizontal;return this}legendText(legendText){if(!arguments.length){return this._legendText}this._legendText=legendText;return this}maxItems(maxItems){if(!arguments.length){return this._maxItems}this._maxItems=utils.isNumber(maxItems)?maxItems:undefined;return this}keyboardAccessible(keyboardAccessible){if(!arguments.length){return this._keyboardAccessible}this._keyboardAccessible=keyboardAccessible;return this}_makeLegendKeyboardAccessible(){if(!this._parent._svgDescription){this._parent.svg().append("desc").attr("id",`desc-id-${this._parent.__dcFlag__}`).html(`${this._parent.svgDescription()}`);this._parent.svg().attr("tabindex","0").attr("role","img").attr("aria-labelledby",`desc-id-${this._parent.__dcFlag__}`)}const tabElements=this.container().selectAll(".dc-legend-item-label.dc-tabbable").attr("tabindex",0);tabElements.on("keydown",d3compat.eventHandler((d,event)=>{if(event.keyCode===13&&typeof d==="object"){d.chart.legendToggle(d)}if(event.keyCode===32&&typeof d==="object"){d.chart.legendToggle(d);event.preventDefault()}})).on("focus",d3compat.eventHandler(d=>{this._parent.legendHighlight(d)})).on("blur",d3compat.eventHandler(d=>{this._parent.legendReset(d)}))}}const htmlLegend=()=>new HtmlLegend;const LABEL_GAP=2;class Legend{constructor(){this._parent=undefined;this._x=0;this._y=0;this._itemHeight=12;this._gap=5;this._horizontal=false;this._legendWidth=560;this._itemWidth=70;this._autoItemWidth=false;this._legendText=pluck("name");this._maxItems=undefined;this._highlightSelected=false;this._keyboardAccessible=false;this._g=undefined}parent(p){if(!arguments.length){return this._parent}this._parent=p;return this}x(x){if(!arguments.length){return this._x}this._x=x;return this}y(y){if(!arguments.length){return this._y}this._y=y;return this}gap(gap){if(!arguments.length){return this._gap}this._gap=gap;return this}highlightSelected(highlightSelected){if(!arguments.length){return this._highlightSelected}this._highlightSelected=highlightSelected;return this}itemHeight(itemHeight){if(!arguments.length){return this._itemHeight}this._itemHeight=itemHeight;return this}horizontal(horizontal){if(!arguments.length){return this._horizontal}this._horizontal=horizontal;return this}legendWidth(legendWidth){if(!arguments.length){return this._legendWidth}this._legendWidth=legendWidth;return this}itemWidth(itemWidth){if(!arguments.length){return this._itemWidth}this._itemWidth=itemWidth;return this}autoItemWidth(autoItemWidth){if(!arguments.length){return this._autoItemWidth}this._autoItemWidth=autoItemWidth;return this}legendText(legendText){if(!arguments.length){return this._legendText}this._legendText=legendText;return this}maxItems(maxItems){if(!arguments.length){return this._maxItems}this._maxItems=utils.isNumber(maxItems)?maxItems:undefined;return this}keyboardAccessible(keyboardAccessible){if(!arguments.length){return this._keyboardAccessible}this._keyboardAccessible=keyboardAccessible;return this}_legendItemHeight(){return this._gap+this._itemHeight}_makeLegendKeyboardAccessible(){if(!this._parent._svgDescription){this._parent.svg().append("desc").attr("id",`desc-id-${this._parent.__dcFlag__}`).html(`${this._parent.svgDescription()}`);this._parent.svg().attr("tabindex","0").attr("role","img").attr("aria-labelledby",`desc-id-${this._parent.__dcFlag__}`)}const tabElements=this._parent.svg().selectAll(".dc-legend .dc-tabbable").attr("tabindex",0);tabElements.on("keydown",d3compat.eventHandler((d,event)=>{if(event.keyCode===13&&typeof d==="object"){d.chart.legendToggle(d)}if(event.keyCode===32&&typeof d==="object"){d.chart.legendToggle(d);event.preventDefault()}})).on("focus",d3compat.eventHandler(d=>{this._parent.legendHighlight(d)})).on("blur",d3compat.eventHandler(d=>{this._parent.legendReset(d)}))}render(){this._parent.svg().select("g.dc-legend").remove();this._g=this._parent.svg().append("g").attr("class","dc-legend").attr("transform",`translate(${this._x},${this._y})`);let legendables=this._parent.legendables();const filters=this._parent.filters();if(this._maxItems!==undefined){legendables=legendables.slice(0,this._maxItems)}const itemEnter=this._g.selectAll("g.dc-legend-item").data(legendables).enter().append("g").attr("class","dc-legend-item").on("mouseover",d3compat.eventHandler(d=>{this._parent.legendHighlight(d)})).on("mouseout",d3compat.eventHandler(d=>{this._parent.legendReset(d)})).on("click",d3compat.eventHandler(d=>{d.chart.legendToggle(d)}));if(this._highlightSelected){itemEnter.classed(constants.SELECTED_CLASS,d=>filters.indexOf(d.name)!==-1)}this._g.selectAll("g.dc-legend-item").classed("fadeout",d=>d.chart.isLegendableHidden(d));if(legendables.some(pluck("dashstyle"))){itemEnter.append("line").attr("x1",0).attr("y1",this._itemHeight/2).attr("x2",this._itemHeight).attr("y2",this._itemHeight/2).attr("stroke-width",2).attr("stroke-dasharray",pluck("dashstyle")).attr("stroke",pluck("color"))}else{itemEnter.append("rect").attr("width",this._itemHeight).attr("height",this._itemHeight).attr("fill",d=>d?d.color:"blue")}{const self=this;itemEnter.append("text").text(self._legendText).classed("dc-tabbable",this._keyboardAccessible).attr("x",self._itemHeight+LABEL_GAP).attr("y",function(){return self._itemHeight/2+(this.clientHeight?this.clientHeight:13)/2-2});if(this._keyboardAccessible){this._makeLegendKeyboardAccessible()}}let cumulativeLegendTextWidth=0;let row=0;{const self=this;itemEnter.attr("transform",function(d,i){if(self._horizontal){const itemWidth=self._autoItemWidth===true?this.getBBox().width+self._gap:self._itemWidth;if(cumulativeLegendTextWidth+itemWidth>self._legendWidth&&cumulativeLegendTextWidth>0){++row;cumulativeLegendTextWidth=0}const translateBy=`translate(${cumulativeLegendTextWidth},${row*self._legendItemHeight()})`;cumulativeLegendTextWidth+=itemWidth;return translateBy}else{return`translate(0,${i*self._legendItemHeight()})`}})}}}const legend=()=>new Legend;const DEFAULT_DOT_RADIUS=5;const TOOLTIP_G_CLASS="dc-tooltip";const DOT_CIRCLE_CLASS="dot";const Y_AXIS_REF_LINE_CLASS="yRef";const X_AXIS_REF_LINE_CLASS="xRef";const DEFAULT_DOT_OPACITY=1e-6;const LABEL_PADDING$1=3;class LineChart extends StackMixin{constructor(parent,chartGroup){super();this._renderArea=false;this._dotRadius=DEFAULT_DOT_RADIUS;this._dataPointRadius=null;this._dataPointFillOpacity=DEFAULT_DOT_OPACITY;this._dataPointStrokeOpacity=DEFAULT_DOT_OPACITY;this._curve=null;this._interpolate=null;this._tension=null;this._defined=undefined;this._dashStyle=undefined;this._xyTipsOn=true;this.transitionDuration(500);this.transitionDelay(0);this._rangeBandPadding(1);this.label(d=>utils.printSingleValue(d.y0+d.y),false);this.anchor(parent,chartGroup)}plotData(){const chartBody=this.chartBodyG();let layersList=chartBody.select("g.stack-list");if(layersList.empty()){layersList=chartBody.append("g").attr("class","stack-list")}let layers=layersList.selectAll("g.stack").data(this.data());const layersEnter=layers.enter().append("g").attr("class",(d,i)=>`stack _${i}`);layers=layersEnter.merge(layers);this._drawLine(layersEnter,layers);this._drawArea(layersEnter,layers);this._drawDots(chartBody,layers);if(this.renderLabel()){this._drawLabels(layers)}}curve(curve){if(!arguments.length){return this._curve}this._curve=curve;return this}interpolate(interpolate){logger.warnOnce("dc.lineChart.interpolate has been deprecated since version 3.0 use dc.lineChart.curve instead");if(!arguments.length){return this._interpolate}this._interpolate=interpolate;return this}tension(tension){logger.warnOnce("dc.lineChart.tension has been deprecated since version 3.0 use dc.lineChart.curve instead");if(!arguments.length){return this._tension}this._tension=tension;return this}defined(defined){if(!arguments.length){return this._defined}this._defined=defined;return this}dashStyle(dashStyle){if(!arguments.length){return this._dashStyle}this._dashStyle=dashStyle;return this}renderArea(renderArea){if(!arguments.length){return this._renderArea}this._renderArea=renderArea;return this}_getColor(d,i){return this.getColor.call(d,d.values,i)}_getCurveFactory(){let curve=null;if(this._curve){return this._curve}if(typeof this._interpolate==="function"){curve=this._interpolate}else{const mapping={linear:d3Shape.curveLinear,"linear-closed":d3Shape.curveLinearClosed,step:d3Shape.curveStep,"step-before":d3Shape.curveStepBefore,"step-after":d3Shape.curveStepAfter,basis:d3Shape.curveBasis,"basis-open":d3Shape.curveBasisOpen,"basis-closed":d3Shape.curveBasisClosed,bundle:d3Shape.curveBundle,cardinal:d3Shape.curveCardinal,"cardinal-open":d3Shape.curveCardinalOpen,"cardinal-closed":d3Shape.curveCardinalClosed,monotone:d3Shape.curveMonotoneX};curve=mapping[this._interpolate]}if(!curve){curve=d3Shape.curveLinear}if(this._tension!==null){if(typeof curve.tension!=="function"){logger.warn("tension was specified but the curve/interpolate does not support it.")}else{curve=curve.tension(this._tension)}}return curve}_drawLine(layersEnter,layers){const _line=d3Shape.line().x(d=>this.x()(d.x)).y(d=>this.y()(d.y+d.y0)).curve(this._getCurveFactory());if(this._defined){_line.defined(this._defined)}const path=layersEnter.append("path").attr("class","line").attr("stroke",(d,i)=>this._getColor(d,i));if(this._dashStyle){path.attr("stroke-dasharray",this._dashStyle)}transition(layers.select("path.line"),this.transitionDuration(),this.transitionDelay()).attr("stroke",(d,i)=>this._getColor(d,i)).attr("d",d=>this._safeD(_line(d.values)))}_drawArea(layersEnter,layers){if(this._renderArea){const _area=d3Shape.area().x(d=>this.x()(d.x)).y1(d=>this.y()(d.y+d.y0)).y0(d=>this.y()(d.y0)).curve(this._getCurveFactory());if(this._defined){_area.defined(this._defined)}layersEnter.append("path").attr("class","area").attr("fill",(d,i)=>this._getColor(d,i)).attr("d",d=>this._safeD(_area(d.values)));transition(layers.select("path.area"),this.transitionDuration(),this.transitionDelay()).attr("fill",(d,i)=>this._getColor(d,i)).attr("d",d=>this._safeD(_area(d.values)))}}_safeD(d){return!d||d.indexOf("NaN")>=0?"M0,0":d}_drawDots(chartBody,layers){if(this.xyTipsOn()==="always"||!(this.brushOn()||this.parentBrushOn())&&this.xyTipsOn()){const tooltipListClass=`${TOOLTIP_G_CLASS}-list`;let tooltips=chartBody.select(`g.${tooltipListClass}`);if(tooltips.empty()){tooltips=chartBody.append("g").attr("class",tooltipListClass)}layers.each((data,layerIndex)=>{let points=data.values;if(this._defined){points=points.filter(this._defined)}let g=tooltips.select(`g.${TOOLTIP_G_CLASS}._${layerIndex}`);if(g.empty()){g=tooltips.append("g").attr("class",`${TOOLTIP_G_CLASS} _${layerIndex}`)}this._createRefLines(g);const dots=g.selectAll(`circle.${DOT_CIRCLE_CLASS}`).data(points,pluck("x"));const chart=this;const dotsEnterModify=dots.enter().append("circle").attr("class",DOT_CIRCLE_CLASS).classed("dc-tabbable",this._keyboardAccessible).attr("cx",d=>utils.safeNumber(this.x()(d.x))).attr("cy",d=>utils.safeNumber(this.y()(d.y+d.y0))).attr("r",this._getDotRadius()).style("fill-opacity",this._dataPointFillOpacity).style("stroke-opacity",this._dataPointStrokeOpacity).attr("fill",this.getColor).attr("stroke",this.getColor).on("mousemove",function(){const dot=d3Selection.select(this);chart._showDot(dot);chart._showRefLines(dot,g)}).on("mouseout",function(){const dot=d3Selection.select(this);chart._hideDot(dot);chart._hideRefLines(g)}).merge(dots);if(this._keyboardAccessible){this._svg.selectAll(".dc-tabbable").attr("tabindex",0).on("focus",function(){const dot=d3Selection.select(this);chart._showDot(dot);chart._showRefLines(dot,g)}).on("blur",function(){const dot=d3Selection.select(this);chart._hideDot(dot);chart._hideRefLines(g)})}dotsEnterModify.call(dot=>this._doRenderTitle(dot,data));transition(dotsEnterModify,this.transitionDuration()).attr("cx",d=>utils.safeNumber(this.x()(d.x))).attr("cy",d=>utils.safeNumber(this.y()(d.y+d.y0))).attr("fill",this.getColor);dots.exit().remove()})}}_drawLabels(layers){const chart=this;layers.each(function(data,layerIndex){const layer=d3Selection.select(this);const labels=layer.selectAll("text.lineLabel").data(data.values,pluck("x"));const labelsEnterModify=labels.enter().append("text").attr("class","lineLabel").attr("text-anchor","middle").merge(labels);transition(labelsEnterModify,chart.transitionDuration()).attr("x",d=>utils.safeNumber(chart.x()(d.x))).attr("y",d=>{const y=chart.y()(d.y+d.y0)-LABEL_PADDING$1;return utils.safeNumber(y)}).text(d=>chart.label()(d));transition(labels.exit(),chart.transitionDuration()).attr("height",0).remove()})}_createRefLines(g){const yRefLine=g.select(`path.${Y_AXIS_REF_LINE_CLASS}`).empty()?g.append("path").attr("class",Y_AXIS_REF_LINE_CLASS):g.select(`path.${Y_AXIS_REF_LINE_CLASS}`);yRefLine.style("display","none").attr("stroke-dasharray","5,5");const xRefLine=g.select(`path.${X_AXIS_REF_LINE_CLASS}`).empty()?g.append("path").attr("class",X_AXIS_REF_LINE_CLASS):g.select(`path.${X_AXIS_REF_LINE_CLASS}`);xRefLine.style("display","none").attr("stroke-dasharray","5,5")}_showDot(dot){dot.style("fill-opacity",.8);dot.style("stroke-opacity",.8);dot.attr("r",this._dotRadius);return dot}_showRefLines(dot,g){const x=dot.attr("cx");const y=dot.attr("cy");const yAxisX=this._yAxisX()-this.margins().left;const yAxisRefPathD=`M${yAxisX} ${y}L${x} ${y}`;const xAxisRefPathD=`M${x} ${this.yAxisHeight()}L${x} ${y}`;g.select(`path.${Y_AXIS_REF_LINE_CLASS}`).style("display","").attr("d",yAxisRefPathD);g.select(`path.${X_AXIS_REF_LINE_CLASS}`).style("display","").attr("d",xAxisRefPathD)}_getDotRadius(){return this._dataPointRadius||this._dotRadius}_hideDot(dot){dot.style("fill-opacity",this._dataPointFillOpacity).style("stroke-opacity",this._dataPointStrokeOpacity).attr("r",this._getDotRadius())}_hideRefLines(g){g.select(`path.${Y_AXIS_REF_LINE_CLASS}`).style("display","none");g.select(`path.${X_AXIS_REF_LINE_CLASS}`).style("display","none")}_doRenderTitle(dot,d){if(this.renderTitle()){dot.select("title").remove();dot.append("title").text(pluck("data",this.title(d.name)))}}xyTipsOn(xyTipsOn){if(!arguments.length){return this._xyTipsOn}this._xyTipsOn=xyTipsOn;return this}dotRadius(dotRadius){if(!arguments.length){return this._dotRadius}this._dotRadius=dotRadius;return this}renderDataPoints(options){if(!arguments.length){return{fillOpacity:this._dataPointFillOpacity,strokeOpacity:this._dataPointStrokeOpacity,radius:this._dataPointRadius}}else if(!options){this._dataPointFillOpacity=DEFAULT_DOT_OPACITY;this._dataPointStrokeOpacity=DEFAULT_DOT_OPACITY;this._dataPointRadius=null}else{this._dataPointFillOpacity=options.fillOpacity||.8;this._dataPointStrokeOpacity=options.strokeOpacity||0;this._dataPointRadius=options.radius||2}return this}_colorFilter(color,dashstyle,inv){return function(){const item=d3Selection.select(this);const match=item.attr("stroke")===color&&item.attr("stroke-dasharray")===(dashstyle instanceof Array?dashstyle.join(","):null)||item.attr("fill")===color;return inv?!match:match}}legendHighlight(d){if(!this.isLegendableHidden(d)){this.g().selectAll("path.line, path.area").classed("highlight",this._colorFilter(d.color,d.dashstyle)).classed("fadeout",this._colorFilter(d.color,d.dashstyle,true))}}legendReset(){this.g().selectAll("path.line, path.area").classed("highlight",false).classed("fadeout",false)}legendables(){const legendables=super.legendables();if(!this._dashStyle){return legendables}return legendables.map(l=>{l.dashstyle=this._dashStyle;return l})}}const lineChart=(parent,chartGroup)=>new LineChart(parent,chartGroup);const SPAN_CLASS="number-display";class NumberDisplay extends BaseMixin{constructor(parent,chartGroup){super();this._formatNumber=d3Format.format(".2s");this._html={one:"",some:"",none:""};this._lastValue=undefined;this._ariaLiveRegion=false;this._mandatoryAttributes(["group"]);this.ordering(kv=>kv.value);this.data(group=>{const valObj=group.value?group.value():this._maxBin(group.all());return this.valueAccessor()(valObj)});this.transitionDuration(250);this.transitionDelay(0);this.anchor(parent,chartGroup)}html(html){if(!arguments.length){return this._html}if(html.none){this._html.none=html.none}else if(html.one){this._html.none=html.one}else if(html.some){this._html.none=html.some}if(html.one){this._html.one=html.one}else if(html.some){this._html.one=html.some}if(html.some){this._html.some=html.some}else if(html.one){this._html.some=html.one}return this}value(){return this.data()}_maxBin(all){if(!all.length){return null}const sorted=this._computeOrderedGroups(all);return sorted[sorted.length-1]}_doRender(){const newValue=this.value();let span=this.selectAll(`.${SPAN_CLASS}`);if(span.empty()){span=span.data([0]).enter().append("span").attr("class",SPAN_CLASS).classed("dc-tabbable",this._keyboardAccessible).merge(span);if(this._keyboardAccessible){span.attr("tabindex","0")}if(this._ariaLiveRegion){this.transitionDuration(0);span.attr("aria-live","polite")}}{const chart=this;span.transition().duration(chart.transitionDuration()).delay(chart.transitionDelay()).ease(d3Ease.easeQuad).tween("text",function(){const interpStart=isFinite(chart._lastValue)?chart._lastValue:0;const interp=d3Interpolate.interpolateNumber(interpStart||0,newValue);chart._lastValue=newValue;const node=this;return t=>{let html=null;const num=chart.formatNumber()(interp(t));if(newValue===0&&chart._html.none!==""){html=chart._html.none}else if(newValue===1&&chart._html.one!==""){html=chart._html.one}else if(chart._html.some!==""){html=chart._html.some}node.innerHTML=html?html.replace("%number",num):num}})}}_doRedraw(){return this._doRender()}formatNumber(formatter){if(!arguments.length){return this._formatNumber}this._formatNumber=formatter;return this}ariaLiveRegion(ariaLiveRegion){if(!arguments.length){return this._ariaLiveRegion}this._ariaLiveRegion=ariaLiveRegion;return this}}const numberDisplay=(parent,chartGroup)=>new NumberDisplay(parent,chartGroup);const DEFAULT_MIN_ANGLE_FOR_LABEL=.5;class PieChart extends CapMixin(ColorMixin(BaseMixin)){constructor(parent,chartGroup){super();this._sliceCssClass="pie-slice";this._labelCssClass="pie-label";this._sliceGroupCssClass="pie-slice-group";this._labelGroupCssClass="pie-label-group";this._emptyCssClass="empty-chart";this._emptyTitle="empty";this._radius=undefined;this._givenRadius=undefined;this._innerRadius=0;this._externalRadiusPadding=0;this._g=undefined;this._cx=undefined;this._cy=undefined;this._minAngleForLabel=DEFAULT_MIN_ANGLE_FOR_LABEL;this._externalLabelRadius=undefined;this._drawPaths=false;this.colorAccessor(d=>this.cappedKeyAccessor(d));this.title(d=>`${this.cappedKeyAccessor(d)}: ${this.cappedValueAccessor(d)}`);this.label(d=>this.cappedKeyAccessor(d));this.renderLabel(true);this.transitionDuration(350);this.transitionDelay(0);this.anchor(parent,chartGroup)}slicesCap(cap){return this.cap(cap)}_doRender(){this.resetSvg();this._g=this.svg().append("g").attr("transform",`translate(${this.cx()},${this.cy()})`);this._g.append("g").attr("class",this._sliceGroupCssClass);this._g.append("g").attr("class",this._labelGroupCssClass);this._drawChart();return this}_drawChart(){const maxRadius=d3Array.min([this.width(),this.height()])/2;this._radius=this._givenRadius&&this._givenRadius<maxRadius?this._givenRadius:maxRadius;const arcs=this._buildArcs();const pieLayout=this._pieLayout();let pieData;if(d3Array.sum(this.data(),d=>this.cappedValueAccessor(d))){pieData=pieLayout(this.data());this._g.classed(this._emptyCssClass,false)}else{pieData=pieLayout([{key:this._emptyTitle,value:1,others:[this._emptyTitle]}]);this._g.classed(this._emptyCssClass,true)}if(this._g){const slices=this._g.select(`g.${this._sliceGroupCssClass}`).selectAll(`g.${this._sliceCssClass}`).data(pieData);const labels=this._g.select(`g.${this._labelGroupCssClass}`).selectAll(`text.${this._labelCssClass}`).data(pieData);this._removeElements(slices,labels);this._createElements(slices,labels,arcs,pieData);this._updateElements(pieData,arcs);this._highlightFilter();transition(this._g,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${this.cx()},${this.cy()})`)}}_createElements(slices,labels,arcs,pieData){const slicesEnter=this._createSliceNodes(slices);this._createSlicePath(slicesEnter,arcs);this._createTitles(slicesEnter);this._createLabels(labels,pieData,arcs)}_createSliceNodes(slices){return slices.enter().append("g").attr("class",(d,i)=>`${this._sliceCssClass} _${i}`).classed("dc-tabbable",this._keyboardAccessible)}_createSlicePath(slicesEnter,arcs){const slicePath=slicesEnter.append("path").attr("fill",(d,i)=>this._fill(d,i)).on("click",d3compat.eventHandler(d=>this._onClick(d))).attr("d",(d,i)=>this._safeArc(d,i,arcs));if(this._keyboardAccessible){this._makeKeyboardAccessible(this._onClick)}const tranNodes=transition(slicePath,this.transitionDuration(),this.transitionDelay());if(tranNodes.attrTween){const chart=this;tranNodes.attrTween("d",function(d){return chart._tweenPie(d,this)})}}_createTitles(slicesEnter){if(this.renderTitle()){slicesEnter.append("title").text(d=>this.title()(d.data))}}_applyLabelText(labels){labels.text(d=>{const data=d.data;if((this._sliceHasNoData(data)||this._sliceTooSmall(d))&&!this._isSelectedSlice(d)){return""}return this.label()(d.data)})}_positionLabels(labels,arcs){this._applyLabelText(labels);transition(labels,this.transitionDuration(),this.transitionDelay()).attr("transform",d=>this._labelPosition(d,arcs)).attr("text-anchor","middle")}_highlightSlice(i,whether){this.select(`g.pie-slice._${i}`).classed("highlight",whether)}_createLabels(labels,pieData,arcs){if(this.renderLabel()){const labelsEnter=labels.enter().append("text").attr("class",(d,i)=>{let classes=`${this._sliceCssClass} ${this._labelCssClass} _${i}`;if(this._externalLabelRadius){classes+=" external"}return classes}).on("click",d3compat.eventHandler(d=>this._onClick(d))).on("mouseover",d3compat.eventHandler(d=>{this._highlightSlice(d.index,true)})).on("mouseout",d3compat.eventHandler(d=>{this._highlightSlice(d.index,false)}));this._positionLabels(labelsEnter,arcs);if(this._externalLabelRadius&&this._drawPaths){this._updateLabelPaths(pieData,arcs)}}}_updateLabelPaths(pieData,arcs){let polyline=this._g.selectAll(`polyline.${this._sliceCssClass}`).data(pieData);polyline.exit().remove();polyline=polyline.enter().append("polyline").attr("class",(d,i)=>`pie-path _${i} ${this._sliceCssClass}`).on("click",d3compat.eventHandler(d=>this._onClick(d))).on("mouseover",d3compat.eventHandler(d=>{this._highlightSlice(d.index,true)})).on("mouseout",d3compat.eventHandler(d=>{this._highlightSlice(d.index,false)})).merge(polyline);const arc2=d3Shape.arc().outerRadius(this._radius-this._externalRadiusPadding+this._externalLabelRadius).innerRadius(this._radius-this._externalRadiusPadding);const tranNodes=transition(polyline,this.transitionDuration(),this.transitionDelay());if(tranNodes.attrTween){tranNodes.attrTween("points",function(d){let current=this._current||d;current={startAngle:current.startAngle,endAngle:current.endAngle};const _interpolate=d3Interpolate.interpolate(current,d);this._current=_interpolate(0);return t=>{const d2=_interpolate(t);return[arcs.centroid(d2),arc2.centroid(d2)]}})}else{tranNodes.attr("points",d=>[arcs.centroid(d),arc2.centroid(d)])}tranNodes.style("visibility",d=>d.endAngle-d.startAngle<1e-4?"hidden":"visible")}_updateElements(pieData,arcs){this._updateSlicePaths(pieData,arcs);this._updateLabels(pieData,arcs);this._updateTitles(pieData)}_updateSlicePaths(pieData,arcs){const slicePaths=this._g.selectAll(`g.${this._sliceCssClass}`).data(pieData).select("path").attr("d",(d,i)=>this._safeArc(d,i,arcs));const tranNodes=transition(slicePaths,this.transitionDuration(),this.transitionDelay());if(tranNodes.attrTween){const chart=this;tranNodes.attrTween("d",function(d){return chart._tweenPie(d,this)})}tranNodes.attr("fill",(d,i)=>this._fill(d,i))}_updateLabels(pieData,arcs){if(this.renderLabel()){const labels=this._g.selectAll(`text.${this._labelCssClass}`).data(pieData);this._positionLabels(labels,arcs);if(this._externalLabelRadius&&this._drawPaths){this._updateLabelPaths(pieData,arcs)}}}_updateTitles(pieData){if(this.renderTitle()){this._g.selectAll(`g.${this._sliceCssClass}`).data(pieData).select("title").text(d=>this.title()(d.data))}}_removeElements(slices,labels){slices.exit().remove();labels.exit().remove()}_highlightFilter(){const chart=this;if(this.hasFilter()){this.selectAll(`g.${this._sliceCssClass}`).each(function(d){if(chart._isSelectedSlice(d)){chart.highlightSelected(this)}else{chart.fadeDeselected(this)}})}else{this.selectAll(`g.${this._sliceCssClass}`).each(function(){chart.resetHighlight(this)})}}externalRadiusPadding(externalRadiusPadding){if(!arguments.length){return this._externalRadiusPadding}this._externalRadiusPadding=externalRadiusPadding;return this}innerRadius(innerRadius){if(!arguments.length){return this._innerRadius}this._innerRadius=innerRadius;return this}radius(radius){if(!arguments.length){return this._givenRadius}this._givenRadius=radius;return this}cx(cx){if(!arguments.length){return this._cx||this.width()/2}this._cx=cx;return this}cy(cy){if(!arguments.length){return this._cy||this.height()/2}this._cy=cy;return this}_buildArcs(){return d3Shape.arc().outerRadius(this._radius-this._externalRadiusPadding).innerRadius(this._innerRadius)}_isSelectedSlice(d){return this.hasFilter(this.cappedKeyAccessor(d.data))}_doRedraw(){this._drawChart();return this}minAngleForLabel(minAngleForLabel){if(!arguments.length){return this._minAngleForLabel}this._minAngleForLabel=minAngleForLabel;return this}_pieLayout(){return d3Shape.pie().sort(null).value(d=>this.cappedValueAccessor(d))}_sliceTooSmall(d){const angle=d.endAngle-d.startAngle;return isNaN(angle)||angle<this._minAngleForLabel}_sliceHasNoData(d){return this.cappedValueAccessor(d)===0}_isOffCanvas(current){return!current||isNaN(current.startAngle)||isNaN(current.endAngle)}_fill(d,i){return this.getColor(d.data,i)}_onClick(d){if(this._g.attr("class")!==this._emptyCssClass){this.onClick(d.data)}}_safeArc(d,i,_arc){let path=_arc(d,i);if(path.indexOf("NaN")>=0){path="M0,0"}return path}emptyTitle(title){if(arguments.length===0){return this._emptyTitle}this._emptyTitle=title;return this}externalLabels(externalLabelRadius){if(arguments.length===0){return this._externalLabelRadius}else if(externalLabelRadius){this._externalLabelRadius=externalLabelRadius}else{this._externalLabelRadius=undefined}return this}drawPaths(drawPaths){if(arguments.length===0){return this._drawPaths}this._drawPaths=drawPaths;return this}_labelPosition(d,_arc){let centroid;if(this._externalLabelRadius){centroid=d3Shape.arc().outerRadius(this._radius-this._externalRadiusPadding+this._externalLabelRadius).innerRadius(this._radius-this._externalRadiusPadding+this._externalLabelRadius).centroid(d)}else{centroid=_arc.centroid(d)}if(isNaN(centroid[0])||isNaN(centroid[1])){return"translate(0,0)"}else{return`translate(${centroid})`}}legendables(){return this.data().map((d,i)=>{const legendable={name:d.key,data:d.value,others:d.others,chart:this};legendable.color=this.getColor(d,i);return legendable})}legendHighlight(d){this._highlightSliceFromLegendable(d,true)}legendReset(d){this._highlightSliceFromLegendable(d,false)}legendToggle(d){this.onClick({key:d.name,others:d.others})}_highlightSliceFromLegendable(legendable,highlighted){this.selectAll("g.pie-slice").each(function(d){if(legendable.name===d.data.key){d3Selection.select(this).classed("highlight",highlighted)}})}_tweenPie(b,element){b.innerRadius=this._innerRadius;let current=element._current;if(this._isOffCanvas(current)){current={startAngle:0,endAngle:0}}else{current={startAngle:current.startAngle,endAngle:current.endAngle}}const i=d3Interpolate.interpolate(current,b);element._current=i(0);return t=>this._safeArc(i(t),0,this._buildArcs())}}const pieChart=(parent,chartGroup)=>new PieChart(parent,chartGroup);class RowChart extends CapMixin(ColorMixin(MarginMixin)){constructor(parent,chartGroup){super();this._g=undefined;this._labelOffsetX=10;this._labelOffsetY=15;this._hasLabelOffsetY=false;this._dyOffset="0.35em";this._titleLabelOffsetX=2;this._gap=5;this._fixedBarHeight=false;this._rowCssClass="row";this._titleRowCssClass="titlerow";this._renderTitleLabel=false;this._x=undefined;this._elasticX=undefined;this._xAxis=d3Axis.axisBottom();this._rowData=undefined;this.rowsCap=this.cap;this.title(d=>`${this.cappedKeyAccessor(d)}: ${this.cappedValueAccessor(d)}`);this.label(d=>this.cappedKeyAccessor(d));this.anchor(parent,chartGroup)}_calculateAxisScale(){if(!this._x||this._elasticX){const _extent=d3Array.extent(this._rowData,d=>this.cappedValueAccessor(d));if(_extent[0]>0){_extent[0]=0}if(_extent[1]<0){_extent[1]=0}this._x=d3Scale.scaleLinear().domain(_extent).range([0,this.effectiveWidth()])}this._xAxis.scale(this._x)}_drawAxis(){let axisG=this._g.select("g.axis");this._calculateAxisScale();if(axisG.empty()){axisG=this._g.append("g").attr("class","axis")}axisG.attr("transform",`translate(0, ${this.effectiveHeight()})`);transition(axisG,this.transitionDuration(),this.transitionDelay()).call(this._xAxis)}_doRender(){this.resetSvg();this._g=this.svg().append("g").attr("transform",`translate(${this.margins().left},${this.margins().top})`);this._drawChart();return this}x(scale){if(!arguments.length){return this._x}this._x=scale;return this}_drawGridLines(){this._g.selectAll("g.tick").select("line.grid-line").remove();this._g.selectAll("g.tick").append("line").attr("class","grid-line").attr("x1",0).attr("y1",0).attr("x2",0).attr("y2",()=>-this.effectiveHeight())}_drawChart(){this._rowData=this.data();this._drawAxis();this._drawGridLines();let rows=this._g.selectAll(`g.${this._rowCssClass}`).data(this._rowData);this._removeElements(rows);rows=this._createElements(rows).merge(rows);this._updateElements(rows)}_createElements(rows){const rowEnter=rows.enter().append("g").attr("class",(d,i)=>`${this._rowCssClass} _${i}`);rowEnter.append("rect").attr("width",0);this._createLabels(rowEnter);return rowEnter}_removeElements(rows){rows.exit().remove()}_rootValue(){const root=this._x(0);return root===-Infinity||root!==root?this._x(1):root}_updateElements(rows){const n=this._rowData.length;let height;if(!this._fixedBarHeight){height=(this.effectiveHeight()-(n+1)*this._gap)/n}else{height=this._fixedBarHeight}if(!this._hasLabelOffsetY){this._labelOffsetY=height/2}const rect=rows.attr("transform",(d,i)=>`translate(0,${(i+1)*this._gap+i*height})`).select("rect").attr("height",height).attr("fill",this.getColor).on("click",d3compat.eventHandler(d=>this._onClick(d))).classed("dc-tabbable",this._keyboardAccessible).classed("deselected",d=>this.hasFilter()?!this._isSelectedRow(d):false).classed("selected",d=>this.hasFilter()?this._isSelectedRow(d):false);if(this._keyboardAccessible){this._makeKeyboardAccessible(d=>this._onClick(d))}transition(rect,this.transitionDuration(),this.transitionDelay()).attr("width",d=>Math.abs(this._rootValue()-this._x(this.cappedValueAccessor(d)))).attr("transform",d=>this._translateX(d));this._createTitles(rows);this._updateLabels(rows)}_createTitles(rows){if(this.renderTitle()){rows.select("title").remove();rows.append("title").text(this.title())}}_createLabels(rowEnter){if(this.renderLabel()){rowEnter.append("text").on("click",d3compat.eventHandler(d=>this._onClick(d)))}if(this.renderTitleLabel()){rowEnter.append("text").attr("class",this._titleRowCssClass).on("click",d3compat.eventHandler(d=>this._onClick(d)))}}_updateLabels(rows){if(this.renderLabel()){const lab=rows.select("text").attr("x",this._labelOffsetX).attr("y",this._labelOffsetY).attr("dy",this._dyOffset).on("click",d3compat.eventHandler(d=>this._onClick(d))).attr("class",(d,i)=>`${this._rowCssClass} _${i}`).text(d=>this.label()(d));transition(lab,this.transitionDuration(),this.transitionDelay()).attr("transform",d=>this._translateX(d))}if(this.renderTitleLabel()){const titlelab=rows.select(`.${this._titleRowCssClass}`).attr("x",this.effectiveWidth()-this._titleLabelOffsetX).attr("y",this._labelOffsetY).attr("dy",this._dyOffset).attr("text-anchor","end").on("click",d3compat.eventHandler(d=>this._onClick(d))).attr("class",(d,i)=>`${this._titleRowCssClass} _${i}`).text(d=>this.title()(d));transition(titlelab,this.transitionDuration(),this.transitionDelay()).attr("transform",d=>this._translateX(d))}}renderTitleLabel(renderTitleLabel){if(!arguments.length){return this._renderTitleLabel}this._renderTitleLabel=renderTitleLabel;return this}_onClick(d){this.onClick(d)}_translateX(d){const x=this._x(this.cappedValueAccessor(d)),x0=this._rootValue(),s=x>x0?x0:x;return`translate(${s},0)`}_doRedraw(){this._drawChart();return this}xAxis(xAxis){if(!arguments.length){return this._xAxis}this._xAxis=xAxis;return this}fixedBarHeight(fixedBarHeight){if(!arguments.length){return this._fixedBarHeight}this._fixedBarHeight=fixedBarHeight;return this}gap(gap){if(!arguments.length){return this._gap}this._gap=gap;return this}elasticX(elasticX){if(!arguments.length){return this._elasticX}this._elasticX=elasticX;return this}labelOffsetX(labelOffsetX){if(!arguments.length){return this._labelOffsetX}this._labelOffsetX=labelOffsetX;return this}labelOffsetY(labelOffsety){if(!arguments.length){return this._labelOffsetY}this._labelOffsetY=labelOffsety;this._hasLabelOffsetY=true;return this}titleLabelOffsetX(titleLabelOffsetX){if(!arguments.length){return this._titleLabelOffsetX}this._titleLabelOffsetX=titleLabelOffsetX;return this}_isSelectedRow(d){return this.hasFilter(this.cappedKeyAccessor(d))}}const rowChart=(parent,chartGroup)=>new RowChart(parent,chartGroup);class ScatterPlot extends CoordinateGridMixin{constructor(parent,chartGroup){super();this._symbol=d3Shape.symbol();this._existenceAccessor=d=>d.value;const originalKeyAccessor=this.keyAccessor();this.keyAccessor(d=>originalKeyAccessor(d)[0]);this.valueAccessor(d=>originalKeyAccessor(d)[1]);this.colorAccessor(()=>this._groupName);this.title(d=>`${this.keyAccessor()(d)},${this.valueAccessor()(d)}: ${this.existenceAccessor()(d)}`);this._highlightedSize=7;this._symbolSize=5;this._excludedSize=3;this._excludedColor=null;this._excludedOpacity=1;this._emptySize=0;this._emptyOpacity=0;this._nonemptyOpacity=1;this._emptyColor=null;this._filtered=[];this._canvas=null;this._context=null;this._useCanvas=false;this.brush(d3Brush.brush());this._symbol.size((d,i)=>this._elementSize(d,i));this.anchor(parent,chartGroup)}_canvasElementSize(d,isFiltered){if(!this._existenceAccessor(d)){return this._emptySize/Math.sqrt(Math.PI)}else if(isFiltered){return this._symbolSize/Math.sqrt(Math.PI)}else{return this._excludedSize/Math.sqrt(Math.PI)}}_elementSize(d,i){if(!this._existenceAccessor(d)){return Math.pow(this._emptySize,2)}else if(this._filtered[i]){return Math.pow(this._symbolSize,2)}else{return Math.pow(this._excludedSize,2)}}_locator(d){return`translate(${this.x()(this.keyAccessor()(d))},${this.y()(this.valueAccessor()(d))})`}filter(filter){if(!arguments.length){return super.filter()}return super.filter(filters.RangedTwoDimensionalFilter(filter))}resetSvg(){if(!this._useCanvas){return super.resetSvg()}else{super.resetSvg();this.select("canvas").remove();const svgSel=this.svg();const rootSel=this.root();rootSel.style("position","relative");svgSel.style("position","relative");const svgLeft=isNaN(parseInt(svgSel.style("left"),10))?0:parseInt(svgSel.style("left"),10);const svgTop=isNaN(parseInt(svgSel.style("top"),10))?0:parseInt(svgSel.style("top"),10);const width=this.effectiveWidth();const height=this.effectiveHeight();const margins=this.margins();const devicePixelRatio=window.devicePixelRatio||1;this._canvas=this.root().append("canvas").attr("x",0).attr("y",0).attr("width",width*devicePixelRatio).attr("height",height*devicePixelRatio).style("width",`${width}px`).style("height",`${height}px`).style("position","absolute").style("top",`${margins.top+svgTop}px`).style("left",`${margins.left+svgLeft}px`).style("z-index",-1).style("pointer-events","none");this._context=this._canvas.node().getContext("2d");this._context.scale(devicePixelRatio,devicePixelRatio);this._context.rect(0,0,width,height);this._context.clip();this._context.imageSmoothingQuality="high";return this.svg()}}_resizeCanvas(){const width=this.effectiveWidth();const height=this.effectiveHeight();const devicePixelRatio=window.devicePixelRatio||1;this._canvas.attr("width",width*devicePixelRatio).attr("height",height*devicePixelRatio).style("width",`${width}px`).style("height",`${height}px`);this._context.scale(devicePixelRatio,devicePixelRatio)}useCanvas(useCanvas){if(!arguments.length){return this._useCanvas}this._useCanvas=useCanvas;return this}canvas(canvasElement){if(!arguments.length){return this._canvas}this._canvas=canvasElement;return this}context(){return this._context}_plotOnCanvas(legendHighlightDatum){this._resizeCanvas();const context=this.context();context.clearRect(0,0,(context.canvas.width+2)*1,(context.canvas.height+2)*1);const data=this.data();data.forEach((d,i)=>{const isFiltered=!this.filter()||this.filter().isFiltered([d.key[0],d.key[1]]);let cOpacity=1;if(!this._existenceAccessor(d)){cOpacity=this._emptyOpacity}else if(isFiltered){cOpacity=this._nonemptyOpacity}else{cOpacity=this.excludedOpacity()}let cColor=null;if(this._emptyColor&&!this._existenceAccessor(d)){cColor=this._emptyColor}else if(this.excludedColor()&&!isFiltered){cColor=this.excludedColor()}else{cColor=this.getColor(d)}let cSize=this._canvasElementSize(d,isFiltered);if(legendHighlightDatum){const isHighlighted=cColor===legendHighlightDatum.color;const fadeOutOpacity=.1;if(!isHighlighted){cOpacity=fadeOutOpacity}if(isHighlighted){cSize=this._highlightedSize/Math.sqrt(Math.PI)}}context.save();context.globalAlpha=cOpacity;context.beginPath();context.arc(this.x()(this.keyAccessor()(d)),this.y()(this.valueAccessor()(d)),cSize,0,2*Math.PI,true);context.fillStyle=cColor;context.fill();context.restore()})}_plotOnSVG(){const data=this.data();if(this._keyboardAccessible){data.sort((a,b)=>d3Array.ascending(this.keyAccessor()(a),this.keyAccessor()(b)))}let symbols=this.chartBodyG().selectAll("path.symbol").data(data);transition(symbols.exit(),this.transitionDuration(),this.transitionDelay()).attr("opacity",0).remove();symbols=symbols.enter().append("path").attr("class","symbol").classed("dc-tabbable",this._keyboardAccessible).attr("opacity",0).attr("fill",this.getColor).attr("transform",d=>this._locator(d)).merge(symbols);if(this._keyboardAccessible){this._makeKeyboardAccessible();symbols.order()}symbols.call(s=>this._renderTitles(s,data));symbols.each((d,i)=>{this._filtered[i]=!this.filter()||this.filter().isFiltered([this.keyAccessor()(d),this.valueAccessor()(d)])});transition(symbols,this.transitionDuration(),this.transitionDelay()).attr("opacity",(d,i)=>{if(!this._existenceAccessor(d)){return this._emptyOpacity}else if(this._filtered[i]){return this._nonemptyOpacity}else{return this.excludedOpacity()}}).attr("fill",(d,i)=>{if(this._emptyColor&&!this._existenceAccessor(d)){return this._emptyColor}else if(this.excludedColor()&&!this._filtered[i]){return this.excludedColor()}else{return this.getColor(d)}}).attr("transform",d=>this._locator(d)).attr("d",this._symbol)}plotData(){if(this._useCanvas){this._plotOnCanvas()}else{this._plotOnSVG()}}_renderTitles(_symbol,_d){if(this.renderTitle()){_symbol.selectAll("title").remove();_symbol.append("title").text(d=>this.title()(d))}}existenceAccessor(accessor){if(!arguments.length){return this._existenceAccessor}this._existenceAccessor=accessor;return this}symbol(type){if(!arguments.length){return this._symbol.type()}this._symbol.type(type);return this}customSymbol(customSymbol){if(!arguments.length){return this._symbol}this._symbol=customSymbol;this._symbol.size((d,i)=>this._elementSize(d,i));return this}symbolSize(symbolSize){if(!arguments.length){return this._symbolSize}this._symbolSize=symbolSize;return this}highlightedSize(highlightedSize){if(!arguments.length){return this._highlightedSize}this._highlightedSize=highlightedSize;return this}excludedSize(excludedSize){if(!arguments.length){return this._excludedSize}this._excludedSize=excludedSize;return this}excludedColor(excludedColor){if(!arguments.length){return this._excludedColor}this._excludedColor=excludedColor;return this}excludedOpacity(excludedOpacity){if(!arguments.length){return this._excludedOpacity}this._excludedOpacity=excludedOpacity;return this}emptySize(emptySize){if(!arguments.length){return this._emptySize}this._emptySize=emptySize;return this}hiddenSize(emptySize){if(!arguments.length){return this.emptySize()}return this.emptySize(emptySize)}emptyColor(emptyColor){if(!arguments.length){return this._emptyColor}this._emptyColor=emptyColor;return this}emptyOpacity(emptyOpacity){if(!arguments.length){return this._emptyOpacity}this._emptyOpacity=emptyOpacity;return this}nonemptyOpacity(nonemptyOpacity){if(!arguments.length){return this._emptyOpacity}this._nonemptyOpacity=nonemptyOpacity;return this}legendables(){return[{chart:this,name:this._groupName,color:this.getColor()}]}legendHighlight(d){if(this._useCanvas){this._plotOnCanvas(d)}else{this._resizeSymbolsWhere(s=>s.attr("fill")===d.color,this._highlightedSize);this.chartBodyG().selectAll(".chart-body path.symbol").filter(function(){return d3Selection.select(this).attr("fill")!==d.color}).classed("fadeout",true)}}legendReset(d){if(this._useCanvas){this._plotOnCanvas(d)}else{this._resizeSymbolsWhere(s=>s.attr("fill")===d.color,this._symbolSize);this.chartBodyG().selectAll(".chart-body path.symbol").filter(function(){return d3Selection.select(this).attr("fill")!==d.color}).classed("fadeout",false)}}_resizeSymbolsWhere(condition,size){const symbols=this.chartBodyG().selectAll(".chart-body path.symbol").filter(function(){return condition(d3Selection.select(this))});const oldSize=this._symbol.size();this._symbol.size(Math.pow(size,2));transition(symbols,this.transitionDuration(),this.transitionDelay()).attr("d",this._symbol);this._symbol.size(oldSize)}createBrushHandlePaths(){}extendBrush(brushSelection){if(this.round()){brushSelection[0]=brushSelection[0].map(this.round());brushSelection[1]=brushSelection[1].map(this.round())}return brushSelection}brushIsEmpty(brushSelection){return!brushSelection||brushSelection[0][0]>=brushSelection[1][0]||brushSelection[0][1]>=brushSelection[1][1]}_brushing(evt){if(this._ignoreBrushEvents){return}let brushSelection=evt.selection;let brushIsEmpty=this.brushIsEmpty(brushSelection);if(brushSelection){brushSelection=brushSelection.map(point=>point.map((coord,i)=>{const scale=i===0?this.x():this.y();return scale.invert(coord)}));brushSelection=this.extendBrush(brushSelection);brushIsEmpty=brushIsEmpty&&this.brushIsEmpty(brushSelection)}this.redrawBrush(brushSelection,false);const ranged2DFilter=brushIsEmpty?null:filters.RangedTwoDimensionalFilter(brushSelection);events.trigger(()=>{this.replaceFilter(ranged2DFilter);this.redrawGroup()},constants.EVENT_DELAY)}redrawBrush(brushSelection,doTransition){this._gBrush=this.gBrush();if(this.brushOn()&&this._gBrush){if(this.resizing()){this.setBrushExtents(doTransition)}if(!brushSelection){this._withoutBrushEvents(()=>{this._gBrush.call(this.brush().move,brushSelection)})}else{brushSelection=brushSelection.map(point=>point.map((coord,i)=>{const scale=i===0?this.x():this.y();return scale(coord)}));const gBrush=optionalTransition(doTransition,this.transitionDuration(),this.transitionDelay())(this._gBrush);this._withoutBrushEvents(()=>{gBrush.call(this.brush().move,brushSelection)})}}this.fadeDeselectedArea(brushSelection)}}const scatterPlot=(parent,chartGroup)=>new ScatterPlot(parent,chartGroup);const SELECT_CSS_CLASS="dc-select-menu";const OPTION_CSS_CLASS="dc-select-option";class SelectMenu extends BaseMixin{constructor(parent,chartGroup){super();this._select=undefined;this._promptText="Select all";this._multiple=false;this._promptValue=null;this._numberVisible=null;this.data(group=>group.all().filter(this._filterDisplayed));this._filterDisplayed=d=>this.valueAccessor()(d)>0;this._order=(a,b)=>{if(this.keyAccessor()(a)>this.keyAccessor()(b)){return 1}if(this.keyAccessor()(a)<this.keyAccessor()(b)){return-1}return 0};this.anchor(parent,chartGroup)}_doRender(){this.select("select").remove();this._select=this.root().append("select").classed(SELECT_CSS_CLASS,true);this._select.append("option").text(this._promptText).attr("value","");this._doRedraw();return this}_doRedraw(){this._setAttributes();this._renderOptions();if(this.hasFilter()&&this._multiple){this._select.selectAll("option").property("selected",d=>typeof d!=="undefined"&&this.filters().indexOf(String(this.keyAccessor()(d)))>=0)}else if(this.hasFilter()){this._select.property("value",this.filter())}else{this._select.property("value","")}return this}_renderOptions(){const options=this._select.selectAll(`option.${OPTION_CSS_CLASS}`).data(this.data(),d=>this.keyAccessor()(d));options.exit().remove();options.enter().append("option").classed(OPTION_CSS_CLASS,true).attr("value",d=>this.keyAccessor()(d)).merge(options).text(this.title());this._select.selectAll(`option.${OPTION_CSS_CLASS}`).sort(this._order);this._select.on("change",d3compat.eventHandler((d,evt)=>this._onChange(d,evt)))}_onChange(_d,evt){let values;const target=evt.target;if(target.selectedOptions){const selectedOptions=Array.prototype.slice.call(target.selectedOptions);values=selectedOptions.map(d=>d.value)}else{const options=[].slice.call(evt.target.options);values=options.filter(option=>option.selected).map(option=>option.value)}if(values.length===1&&values[0]===""){values=this._promptValue||null}else if(!this._multiple&&values.length===1){values=values[0]}this.onChange(values)}onChange(val){if(val&&this._multiple){this.replaceFilter([val])}else if(val){this.replaceFilter(val)}else{this.filterAll()}events.trigger(()=>{this.redrawGroup()})}_setAttributes(){if(this._multiple){this._select.attr("multiple",true)}else{this._select.attr("multiple",null)}if(this._numberVisible!==null){this._select.attr("size",this._numberVisible)}else{this._select.attr("size",null)}}order(order){if(!arguments.length){return this._order}this._order=order;return this}promptText(promptText){if(!arguments.length){return this._promptText}this._promptText=promptText;return this}filterDisplayed(filterDisplayed){if(!arguments.length){return this._filterDisplayed}this._filterDisplayed=filterDisplayed;return this}multiple(multiple){if(!arguments.length){return this._multiple}this._multiple=multiple;return this}promptValue(promptValue){if(!arguments.length){return this._promptValue}this._promptValue=promptValue;return this}numberVisible(numberVisible){if(!arguments.length){return this._numberVisible}this._numberVisible=numberVisible;return this}size(numberVisible){logger.warnOnce("selectMenu.size is ambiguous - use selectMenu.numberVisible instead");if(!arguments.length){return this.numberVisible()}return this.numberVisible(numberVisible)}}const selectMenu=(parent,chartGroup)=>new SelectMenu(parent,chartGroup);class SeriesChart extends CompositeChart{constructor(parent,chartGroup){super(parent,chartGroup);this._keySort=(a,b)=>d3Array.ascending(this.keyAccessor()(a),this.keyAccessor()(b));this._charts={};this._chartFunction=lineChart;this._chartGroup=chartGroup;this._seriesAccessor=undefined;this._seriesSort=d3Array.ascending;this._valueSort=this._keySort;this._mandatoryAttributes().push("seriesAccessor","chart");this.shareColors(true)}_compose(subChartArray){super.compose(subChartArray)}compose(subChartArray){throw new Error("Not supported for this chart type")}_preprocessData(){const keep=[];let childrenChanged;const nesting=d3compat.nester({key:this._seriesAccessor,sortKeys:this._seriesSort,sortValues:this._valueSort,entries:this.data()});const children=nesting.map((sub,i)=>{const subChart=this._charts[sub.key]||this._chartFunction(this,this._chartGroup,sub.key,i);if(!this._charts[sub.key]){childrenChanged=true}this._charts[sub.key]=subChart;keep.push(sub.key);return subChart.dimension(this.dimension()).group({all:typeof sub.values==="function"?sub.values:utils.constant(sub.values)},sub.key).keyAccessor(this.keyAccessor()).valueAccessor(this.valueAccessor()).brushOn(false)});Object.keys(this._charts).filter(c=>keep.indexOf(c)===-1).forEach(c=>{this._clearChart(c);childrenChanged=true});this._compose(children);if(childrenChanged&&this.legend()){this.legend().render()}}_clearChart(c){if(this._charts[c].g()){this._charts[c].g().remove()}delete this._charts[c]}_resetChildren(){Object.keys(this._charts).map(this._clearChart.bind(this));this._charts={}}chart(chartFunction){if(!arguments.length){return this._chartFunction}this._chartFunction=chartFunction;this._resetChildren();return this}seriesAccessor(accessor){if(!arguments.length){return this._seriesAccessor}this._seriesAccessor=accessor;this._resetChildren();return this}seriesSort(sortFunction){if(!arguments.length){return this._seriesSort}this._seriesSort=sortFunction;this._resetChildren();return this}valueSort(sortFunction){if(!arguments.length){return this._valueSort}this._valueSort=sortFunction;this._resetChildren();return this}}const seriesChart=(parent,chartGroup)=>new SeriesChart(parent,chartGroup);const DEFAULT_MIN_ANGLE_FOR_LABEL$1=.5;class SunburstChart extends ColorMixin(BaseMixin){constructor(parent,chartGroup){super();this._sliceCssClass="pie-slice";this._emptyCssClass="empty-chart";this._emptyTitle="empty";this._radius=undefined;this._givenRadius=undefined;this._innerRadius=0;this._ringSizes=null;this._g=undefined;this._cx=undefined;this._cy=undefined;this._minAngleForLabel=DEFAULT_MIN_ANGLE_FOR_LABEL$1;this._externalLabelRadius=undefined;this.colorAccessor(d=>this.keyAccessor()(d));this.ordering(pluck("key"));this.title(d=>`${this.keyAccessor()(d)}: ${this._extendedValueAccessor(d)}`);this.label(d=>this.keyAccessor()(d));this.renderLabel(true);this.transitionDuration(350);this.anchor(parent,chartGroup)}_extendedValueAccessor(d){if(d.path){return d.value}return this.valueAccessor()(d)}_scaleRadius(ringIndex,y){if(ringIndex===0){return this._innerRadius}else{const customRelativeRadius=d3Array.sum(this.ringSizes().relativeRingSizes.slice(0,ringIndex));const scaleFactor=ringIndex*(1/this.ringSizes().relativeRingSizes.length)/customRelativeRadius;const standardRadius=(y-this.ringSizes().rootOffset)/(1-this.ringSizes().rootOffset)*(this._radius-this._innerRadius);return this._innerRadius+standardRadius/scaleFactor}}_doRender(){this.resetSvg();this._g=this.svg().append("g").attr("transform",`translate(${this.cx()},${this.cy()})`);this._drawChart();return this}_drawChart(){const maxRadius=d3Array.min([this.width(),this.height()])/2;this._radius=this._givenRadius&&this._givenRadius<maxRadius?this._givenRadius:maxRadius;const arcs=this._buildArcs();let partitionedNodes,cdata;if(d3Array.sum(this.data(),this.valueAccessor())){cdata=utils.toHierarchy(this.data(),this.valueAccessor());partitionedNodes=this._partitionNodes(cdata);partitionedNodes.nodes.shift();this._g.classed(this._emptyCssClass,false)}else{cdata=utils.toHierarchy([],d=>d.value);partitionedNodes=this._partitionNodes(cdata);this._g.classed(this._emptyCssClass,true)}this.ringSizes().rootOffset=partitionedNodes.rootOffset;this.ringSizes().relativeRingSizes=partitionedNodes.relativeRingSizes;if(this._g){const slices=this._g.selectAll(`g.${this._sliceCssClass}`).data(partitionedNodes.nodes);this._createElements(slices,arcs,partitionedNodes.nodes);this._updateElements(partitionedNodes.nodes,arcs);this._removeElements(slices);this._highlightFilter();transition(this._g,this.transitionDuration(),this.transitionDelay()).attr("transform",`translate(${this.cx()},${this.cy()})`)}}_createElements(slices,arcs,sunburstData){const slicesEnter=this._createSliceNodes(slices);this._createSlicePath(slicesEnter,arcs);this._createTitles(slicesEnter);this._createLabels(sunburstData,arcs)}_createSliceNodes(slices){return slices.enter().append("g").attr("class",(d,i)=>`${this._sliceCssClass} _${i} ${this._sliceCssClass}-level-${d.depth}`)}_createSlicePath(slicesEnter,arcs){const slicePath=slicesEnter.append("path").attr("fill",(d,i)=>this._fill(d,i)).on("click",d3compat.eventHandler(d=>this.onClick(d))).classed("dc-tabbable",this._keyboardAccessible).attr("d",d=>this._safeArc(arcs,d));if(this._keyboardAccessible){this._makeKeyboardAccessible(this.onClick)}const tranNodes=transition(slicePath,this.transitionDuration());if(tranNodes.attrTween){const chart=this;tranNodes.attrTween("d",function(d){return chart._tweenSlice(d,this)})}}_createTitles(slicesEnter){if(this.renderTitle()){slicesEnter.append("title").text(d=>this.title()(d))}}_positionLabels(labelsEnter,arcs){transition(labelsEnter,this.transitionDuration()).attr("transform",d=>this._labelPosition(d,arcs)).attr("text-anchor","middle").text(d=>{if(this._sliceHasNoData(d)||this._sliceTooSmall(d)){return""}return this.label()(d)})}_createLabels(sunburstData,arcs){if(this.renderLabel()){const labels=this._g.selectAll(`text.${this._sliceCssClass}`).data(sunburstData);labels.exit().remove();const labelsEnter=labels.enter().append("text").attr("class",(d,i)=>{let classes=`${this._sliceCssClass} _${i}`;if(this._externalLabelRadius){classes+=" external"}return classes}).on("click",d3compat.eventHandler(d=>this.onClick(d)));this._positionLabels(labelsEnter,arcs)}}_updateElements(sunburstData,arcs){this._updateSlicePaths(sunburstData,arcs);this._updateLabels(sunburstData,arcs);this._updateTitles(sunburstData)}_updateSlicePaths(sunburstData,arcs){const slicePaths=this._g.selectAll(`g.${this._sliceCssClass}`).data(sunburstData).select("path").attr("d",(d,i)=>this._safeArc(arcs,d));const tranNodes=transition(slicePaths,this.transitionDuration());if(tranNodes.attrTween){const chart=this;tranNodes.attrTween("d",function(d){return chart._tweenSlice(d,this)})}tranNodes.attr("fill",(d,i)=>this._fill(d,i))}_updateLabels(sunburstData,arcs){if(this.renderLabel()){const labels=this._g.selectAll(`text.${this._sliceCssClass}`).data(sunburstData);this._positionLabels(labels,arcs)}}_updateTitles(sunburstData){if(this.renderTitle()){this._g.selectAll(`g.${this._sliceCssClass}`).data(sunburstData).select("title").text(d=>this.title()(d))}}_removeElements(slices){slices.exit().remove()}_highlightFilter(){const chart=this;if(chart.hasFilter()){chart.selectAll(`g.${chart._sliceCssClass}`).each(function(d){if(chart._isSelectedSlice(d)){chart.highlightSelected(this)}else{chart.fadeDeselected(this)}})}else{chart.selectAll(`g.${chart._sliceCssClass}`).each(function(d){chart.resetHighlight(this)})}}innerRadius(innerRadius){if(!arguments.length){return this._innerRadius}this._innerRadius=innerRadius;return this}radius(radius){if(!arguments.length){return this._givenRadius}this._givenRadius=radius;return this}cx(cx){if(!arguments.length){return this._cx||this.width()/2}this._cx=cx;return this}cy(cy){if(!arguments.length){return this._cy||this.height()/2}this._cy=cy;return this}minAngleForLabel(minAngleForLabel){if(!arguments.length){return this._minAngleForLabel}this._minAngleForLabel=minAngleForLabel;return this}emptyTitle(title){if(arguments.length===0){return this._emptyTitle}this._emptyTitle=title;return this}externalLabels(externalLabelRadius){if(arguments.length===0){return this._externalLabelRadius}else if(externalLabelRadius){this._externalLabelRadius=externalLabelRadius}else{this._externalLabelRadius=undefined}return this}defaultRingSizes(){return{partitionDy:()=>this._radius*this._radius,scaleInnerRadius:d=>d.data.path&&d.data.path.length===1?this._innerRadius:Math.sqrt(d.y0),scaleOuterRadius:d=>Math.sqrt(d.y1),relativeRingSizesFunction:()=>[]}}equalRingSizes(){return this.relativeRingSizes(ringCount=>{const result=[];for(let i=0;i<ringCount;i++){result.push(1/ringCount)}return result})}relativeRingSizes(relativeRingSizesFunction){function assertPortionsArray(relativeSizes,numberOfRings){if(!Array.isArray(relativeSizes)){throw new BadArgumentException("relativeRingSizes function must return an array")}const portionsSum=d3Array.sum(relativeSizes);if(Math.abs(portionsSum-1)>constants.NEGLIGIBLE_NUMBER){throw new BadArgumentException(`relativeRingSizes : portions must add up to 1, but sum was ${portionsSum}`)}if(relativeSizes.length!==numberOfRings){throw new BadArgumentException(`relativeRingSizes : number of values must match number of rings (${numberOfRings}) but was ${relativeSizes.length}`)}}return{partitionDy:()=>1,scaleInnerRadius:d=>this._scaleRadius(d.data.path.length-1,d.y0),scaleOuterRadius:d=>this._scaleRadius(d.data.path.length,d.y1),relativeRingSizesFunction:ringCount=>{const result=relativeRingSizesFunction(ringCount);assertPortionsArray(result,ringCount);return result}}}ringSizes(ringSizes){if(!arguments.length){if(!this._ringSizes){this._ringSizes=this.defaultRingSizes()}return this._ringSizes}this._ringSizes=ringSizes;return this}_buildArcs(){return d3Shape.arc().startAngle(d=>d.x0).endAngle(d=>d.x1).innerRadius(d=>this.ringSizes().scaleInnerRadius(d)).outerRadius(d=>this.ringSizes().scaleOuterRadius(d))}_isSelectedSlice(d){return this._isPathFiltered(d.path)}_isPathFiltered(path){for(let i=0;i<this.filters().length;i++){const currentFilter=this.filters()[i];if(currentFilter.isFiltered(path)){return true}}return false}_filtersForPath(path){const pathFilter=filters.HierarchyFilter(path);const filtersList=[];for(let i=0;i<this.filters().length;i++){const currentFilter=this.filters()[i];if(currentFilter.isFiltered(path)||pathFilter.isFiltered(currentFilter)){filtersList.push(currentFilter)}}return filtersList}_doRedraw(){this._drawChart();return this}_partitionNodes(data){const getSortable=function(d){return{key:d.data.key,value:d.value}};const _hierarchy=d3Hierarchy.hierarchy(data).sum(d=>d.children?0:this._extendedValueAccessor(d)).sort((a,b)=>d3Array.ascending(this.ordering()(getSortable(a)),this.ordering()(getSortable(b))));const _partition=d3Hierarchy.partition().size([2*Math.PI,this.ringSizes().partitionDy()]);_partition(_hierarchy);const nodes=_hierarchy.descendants().map(d=>{d.key=d.data.key;d.path=d.data.path;return d});const relativeSizes=this.ringSizes().relativeRingSizesFunction(_hierarchy.height);return{nodes:nodes,rootOffset:_hierarchy.y1,relativeRingSizes:relativeSizes}}_sliceTooSmall(d){const angle=d.x1-d.x0;return isNaN(angle)||angle<this._minAngleForLabel}_sliceHasNoData(d){return this._extendedValueAccessor(d)===0}_isOffCanvas(d){return!d||isNaN(d.x0)||isNaN(d.y0)}_fill(d,i){return this.getColor(d.data,i)}onClick(d){if(this._g.attr("class")===this._emptyCssClass){return}const path=d.path||d.key;const filter=filters.HierarchyFilter(path);const filtersList=this._filtersForPath(path);let exactMatch=false;for(let j=filtersList.length-1;j>=0;j--){const currentFilter=filtersList[j];if(utils.arraysIdentical(currentFilter,path)){exactMatch=true}this.filter(filtersList[j])}events.trigger(()=>{if(!exactMatch){this.filter(filter)}this.redrawGroup()})}_safeArc(_arc,d){let path=_arc(d);if(path.indexOf("NaN")>=0){path="M0,0"}return path}_labelPosition(d,_arc){let centroid;if(this._externalLabelRadius){centroid=d3Shape.arc().outerRadius(this._radius+this._externalLabelRadius).innerRadius(this._radius+this._externalLabelRadius).centroid(d)}else{centroid=_arc.centroid(d)}if(isNaN(centroid[0])||isNaN(centroid[1])){return"translate(0,0)"}else{return`translate(${centroid})`}}legendables(){return this.data().map((d,i)=>{const legendable={name:d.key,data:d.value,others:d.others,chart:this};legendable.color=this.getColor(d,i);return legendable})}legendHighlight(d){this._highlightSliceFromLegendable(d,true)}legendReset(d){this._highlightSliceFromLegendable(d,false)}legendToggle(d){this.onClick({key:d.name,others:d.others})}_highlightSliceFromLegendable(legendable,highlighted){this.selectAll("g.pie-slice").each(function(d){if(legendable.name===d.key){d3Selection.select(this).classed("highlight",highlighted)}})}_tweenSlice(d,element){let current=element._current;if(this._isOffCanvas(current)){current={x0:0,x1:0,y0:0,y1:0}}const tweenTarget={x0:d.x0,x1:d.x1,y0:d.y0,y1:d.y1};const i=d3Interpolate.interpolate(current,tweenTarget);element._current=i(0);return t=>this._safeArc(this._buildArcs(),Object.assign({},d,i(t)))}}const sunburstChart=(parent,chartGroup)=>new SunburstChart(parent,chartGroup);const INPUT_CSS_CLASS="dc-text-filter-input";class TextFilterWidget extends BaseMixin{constructor(parent,chartGroup){super();this._normalize=s=>s.toLowerCase();this._filterFunctionFactory=query=>{query=this._normalize(query);return d=>this._normalize(d).indexOf(query)!==-1};this._placeHolder="search";this.group(()=>{throw"the group function on textFilterWidget should never be called, please report the issue"});this.anchor(parent,chartGroup)}_doRender(){this.select("input").remove();this._input=this.root().append("input").classed(INPUT_CSS_CLASS,true);const chart=this;this._input.on("input",function(){chart.dimension().filterFunction(chart._filterFunctionFactory(this.value));events.trigger(()=>{chart.redrawGroup()},constants.EVENT_DELAY)});this._doRedraw();return this}_doRedraw(){this.root().selectAll("input").attr("placeholder",this._placeHolder);return this}normalize(normalize){if(!arguments.length){return this._normalize}this._normalize=normalize;return this}placeHolder(placeHolder){if(!arguments.length){return this._placeHolder}this._placeHolder=placeHolder;return this}filterFunctionFactory(filterFunctionFactory){if(!arguments.length){return this._filterFunctionFactory}this._filterFunctionFactory=filterFunctionFactory;return this}}const textFilterWidget=(parent,chartGroup)=>new TextFilterWidget(parent,chartGroup);const majorVer=+d3.version[0];if(majorVer<6){Object.assign(d3compat,{eventHandler:handler=>function eventHandler(d,_){handler.call(this,d,d3Selection.event)},callHandler:function callHandler(handler,that,_,d){handler.call(that,d)},nester:({key,sortKeys,sortValues,entries})=>{const nester=d3Collection.nest().key(key);if(sortKeys){nester.sortKeys(sortKeys)}if(sortValues){nester.sortValues(sortValues)}return nester.entries(entries)},pointer:(evt,elem)=>d3Selection.mouse(elem)})}const majorVer$1=+d3.version[0];if(majorVer$1>5){Object.assign(d3compat,{eventHandler:handler=>function eventHandler(event,d){handler.call(this,d,event)},callHandler:function callHandler(handler,that,event,d){handler.call(that,event,d)},nester:({key,sortKeys,sortValues,entries})=>{if(sortValues){entries=[...entries].sort(sortValues)}let out=d3Array.groups(entries,key);if(sortKeys){out=out.sort(sortKeys)}return out.map(e=>({key:`${e[0]}`,values:e[1]}))},pointer:d3Selection.pointer})}exports.BadArgumentException=BadArgumentException;exports.BarChart=BarChart;exports.BaseMixin=BaseMixin;exports.BoxPlot=BoxPlot;exports.BubbleChart=BubbleChart;exports.BubbleMixin=BubbleMixin;exports.BubbleOverlay=BubbleOverlay;exports.CapMixin=CapMixin;exports.CboxMenu=CboxMenu;exports.ColorMixin=ColorMixin;exports.CompositeChart=CompositeChart;exports.Config=Config;exports.CoordinateGridMixin=CoordinateGridMixin;exports.DataCount=DataCount;exports.DataGrid=DataGrid;exports.DataTable=DataTable;exports.GeoChoroplethChart=GeoChoroplethChart;exports.HeatMap=HeatMap;exports.HtmlLegend=HtmlLegend;exports.InvalidStateException=InvalidStateException;exports.Legend=Legend;exports.LineChart=LineChart;exports.Logger=Logger;exports.MarginMixin=MarginMixin;exports.NumberDisplay=NumberDisplay;exports.PieChart=PieChart;exports.RowChart=RowChart;exports.ScatterPlot=ScatterPlot;exports.SelectMenu=SelectMenu;exports.SeriesChart=SeriesChart;exports.StackMixin=StackMixin;exports.SunburstChart=SunburstChart;exports.TextFilterWidget=TextFilterWidget;exports.afterTransition=afterTransition;exports.barChart=barChart;exports.baseMixin=baseMixin;exports.boxPlot=boxPlot;exports.bubbleChart=bubbleChart;exports.bubbleOverlay=bubbleOverlay;exports.cboxMenu=cboxMenu;exports.chartRegistry=chartRegistry;exports.compositeChart=compositeChart;exports.config=config;exports.constants=constants;exports.d3Box=d3Box;exports.d3compat=d3compat;exports.dataCount=dataCount;exports.dataGrid=dataGrid;exports.dataTable=dataTable;exports.deregisterAllCharts=deregisterAllCharts;exports.deregisterChart=deregisterChart;exports.events=events;exports.filterAll=filterAll;exports.filters=filters;exports.geoChoroplethChart=geoChoroplethChart;exports.hasChart=hasChart;exports.heatMap=heatMap;exports.htmlLegend=htmlLegend;exports.instanceOfChart=instanceOfChart;exports.legend=legend;exports.lineChart=lineChart;exports.logger=logger;exports.numberDisplay=numberDisplay;exports.optionalTransition=optionalTransition;exports.pieChart=pieChart;exports.pluck=pluck;exports.printers=printers;exports.redrawAll=redrawAll;exports.refocusAll=refocusAll;exports.registerChart=registerChart;exports.renderAll=renderAll;exports.renderlet=renderlet;exports.rowChart=rowChart;exports.scatterPlot=scatterPlot;exports.selectMenu=selectMenu;exports.seriesChart=seriesChart;exports.sunburstChart=sunburstChart;exports.textFilterWidget=textFilterWidget;exports.transition=transition;exports.units=units;exports.utils=utils;exports.version=version;Object.defineProperty(exports,"__esModule",{value:true})});