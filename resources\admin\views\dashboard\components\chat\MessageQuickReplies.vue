<template>
  <div class="message-content">
    <div class="message-body" v-html="formattedContent"></div>
    <div class="quick-replies">
      <el-button
        v-for="reply in message.quickReplies"
        :key="reply.id"
        size="small"
        :class="{
          'article-button': reply.isArticle,
          'primary-button': reply.style === 'primary',
          'secondary-button': reply.style === 'secondary',
          'danger-button': reply.style === 'danger'
        }"
        @click="handleReply(reply)"
      >
        <div v-if="reply.isArticle" class="article-content">
          <div class="article-info">{{ reply.text }}</div>
          <div class="select-button">选择</div>
        </div>
        <span v-else>{{ reply.text }}</span>
      </el-button>
    </div>
    <div class="message-time">{{ message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { QuickRepliesMessage, QuickReply } from './types'

const props = defineProps<{
  message: QuickRepliesMessage
}>()

const emit = defineEmits<{
  (e: 'select', reply: QuickReply): void
}>()

// 格式化消息内容
const formattedContent = computed(() => {
  if (!props.message.content) return ''
  
  return props.message.content
    // 处理换行符
    .replace(/\n/g, '<br>')
    // 处理markdown样式的粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 移除可能导致emoji乱码的处理
    // .replace(/([🔧📊📝👋])\s*/g, '$1 ')
    // 确保每个功能项之间有适当的间距
    .replace(/<br><br>/g, '<br>')
})

const handleReply = (reply: QuickReply) => {
  emit('select', reply)
}
</script>

<script lang="ts">
export default {
  name: 'MessageQuickReplies'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 85%;
  width: 100%;
}

.message-body {
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.6;
  word-wrap: break-word;
  margin-bottom: 12px;
  background: #e8f4ff;
  border-radius: 0 12px 12px 12px;
  
  // 优化文本格式
  :deep(strong) {
    font-weight: 600;
    color: #2c3e50;
  }
  
  :deep(br) {
    line-height: 1.8;
  }
  
  // 确保适当的段落间距
  :deep(p) {
    margin: 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.quick-replies {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
  width: 100%;
}

.quick-replies :deep(.el-button) {
  width: 100% !important;
  height: auto !important;
  min-height: 40px !important;
  padding: 12px 16px !important;
  margin: 0 !important;
  border-radius: 8px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  text-align: left !important;
  justify-content: space-between !important;
  white-space: pre-line !important;
  word-wrap: break-word !important;
  line-height: 1.4 !important;
  display: flex !important;
  align-items: flex-start !important;
  position: relative !important;
  
  background-color: #fff !important;
  border: 1px solid #e9ecef !important;
  color: #333 !important;
  
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 文章选择按钮特殊样式 */
.quick-replies :deep(.el-button.article-button) {
  background-color: #f8f9fa !important;
  border: 1px solid #6BBAD2 !important;
  border-radius: 8px !important;
  padding: 16px !important;
  min-height: 70px !important;
  text-align: left !important;
  position: relative !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  white-space: pre-line !important;
  line-height: 1.3 !important;
}

.article-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 16px;
}

.article-info {
  flex: 1;
  text-align: left;
  line-height: 1.3;
  color: #333;
}

.select-button {
  background: #6BBAD2;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  flex-shrink: 0;
  align-self: center;
}

.quick-replies :deep(.el-button.article-button):hover {
  background-color: #e9ecef !important;
  border-color: #6BBAD2 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(107, 186, 210, 0.2) !important;
}

.quick-replies :deep(.el-button.article-button):hover .select-button {
  background: #559CD7;
}

/* 主要按钮样式 (蓝色背景) */
.quick-replies :deep(.el-button.primary-button) {
  background-color: #6BBAD2 !important;
  border: 1px solid #6BBAD2 !important;
  color: #fff !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 45px !important;
  font-weight: 600 !important;
}

.quick-replies :deep(.el-button.primary-button):hover {
  background-color: #559CD7 !important;
  border-color: #559CD7 !important;
  color: #fff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(107, 186, 210, 0.4) !important;
}

/* 次要按钮样式 (白色背景，蓝色边框) */
.quick-replies :deep(.el-button.secondary-button) {
  background-color: #fff !important;
  border: 1px solid #6BBAD2 !important;
  color: #6BBAD2 !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 40px !important;
}

.quick-replies :deep(.el-button.secondary-button):hover {
  background-color: #f0f9ff !important;
  border-color: #6BBAD2 !important;
  color: #6BBAD2 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(107, 186, 210, 0.2) !important;
}

/* 危险按钮样式 (红色边框) */
.quick-replies :deep(.el-button.danger-button) {
  background-color: #fff !important;
  border: 1px solid #dc3545 !important;
  color: #dc3545 !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 40px !important;
}

.quick-replies :deep(.el-button.danger-button):hover {
  background-color: #f8f9fa !important;
  border-color: #dc3545 !important;
  color: #dc3545 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.2) !important;
}

/* 其他按钮保持原样式 */
.quick-replies :deep(.el-button:not(.article-button):not(.primary-button):not(.secondary-button):not(.danger-button)) {
  background-color: #fff !important;
  border: 1px solid #6BBAD2 !important;
  color: #6BBAD2 !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 40px !important;
}

.quick-replies :deep(.el-button:not(.article-button):not(.primary-button):not(.secondary-button):not(.danger-button)):hover {
  background-color: #6BBAD2 !important;
  border-color: #6BBAD2 !important;
  color: #fff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(107, 186, 210, 0.3) !important;
}

.quick-replies :deep(.el-button):active {
  transform: translateY(0) !important;
  background-color: #559CD7 !important;
  border-color: #559CD7 !important;
}

.quick-replies :deep(.el-button):focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(107, 186, 210, 0.2) !important;
}

/* 重置Element Plus的默认样式 */
.quick-replies :deep(.el-button.el-button--small) {
  font-size: 13px !important;
  padding: 12px 16px !important;
}

.quick-replies :deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

.message-time {
  font-size: 11px;
  color: #999;
  opacity: 0.7;
  margin-top: 4px;
}
</style> 