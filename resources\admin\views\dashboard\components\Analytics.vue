<template>
  <div class="bwms-module">
    <div class="module-tit">
      <h2>{{ reportTitle }}</h2>
      <div class="btn-list">
        <div class="btn-box">
          <el-dropdown @command="selectReportType">
            <span>
              {{ t(reportTypes[changeType].name) }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="(report, key) in reportTypes" :key="key" :command="key">
                  {{ t(report.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="btn-box">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="-"
            :start-placeholder="t('home.analytics.placeholder1')"
            format="YYYY/MM/DD"
            :end-placeholder="t('home.analytics.placeholder2')"
            :clearable="false"
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>
    <div class="module-con">
      <div class="row">
        <div class="item">
          <div class="box">
            <div class="metrics-container">
              <div
                v-for="metric in reportTypes[changeType].metrics"
                :key="metric"
                class="metric-item"
                :class="{ 'metric-focused': focusedMetric === metric }"
                :style="getBackgroundColor(metric)"
                @click="toggleMetricFocus(metric)"
              >
                <div class="metric-value">{{ metricValues[metric] || '0' }}</div>
                <div class="metric-type">{{ t(metric) }}</div>
              </div>
            </div>
            <div class="echarts-container">
              <div ref="echartsRef" class="echart-content" style="height: 400px; min-width: 1200px">
                <div class="arrow-icons">
                  <el-icon size="20" color="#747474">
                    <Right />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, Ref } from 'vue'
import * as ECharts from 'echarts'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
import { ArrowDown, Right } from '@element-plus/icons-vue'
import type { CSSProperties } from 'vue'

const { t } = useI18n()

function getLastMonthDateRange(): [string, string] {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)

  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return [formatDate(lastMonth), formatDate(lastMonthEnd)]
}

const time = ref<[string, string]>(getLastMonthDateRange())
const echartsRef: Ref<HTMLElement | null> = ref(null)
let echarts: ECharts.ECharts | null = null

const reportTitle = ref('Google Analytics 4 Report')
const changeType = ref<keyof typeof reportTypes>('basic_user')
const metricValues = ref<Record<string, string>>({})
const focusedMetric = ref<string | null>(null)

// GA4 数据
const ga4Data = ref<any>(null)

interface ReportType {
  dimensions: string[]
  metrics: string[]
  name: string
}

// 完整的报告类型定义
const reportTypes = {
  basic_user: {
    dimensions: ['analytics.dimensions.date', 'analytics.dimensions.country', 'analytics.dimensions.deviceCategory'],
    metrics: ['analytics.metrics.totalUsers', 'analytics.metrics.newUsers', 'analytics.metrics.sessions', 'analytics.metrics.bounceRate'],
    name: 'home.analytics.reportTypes.basicUser',
  },
  traffic_source: {
    dimensions: ['analytics.dimensions.date', 'analytics.dimensions.source', 'analytics.dimensions.medium', 'analytics.dimensions.campaign'],
    metrics: ['analytics.metrics.sessions', 'analytics.metrics.screenPageViews', 'analytics.metrics.averageSessionDuration', 'analytics.metrics.conversions'],
    name: 'home.analytics.reportTypes.trafficSource',
  },
  page_performance: {
    dimensions: ['analytics.dimensions.pagePath', 'analytics.dimensions.deviceCategory'],
    metrics: ['analytics.metrics.screenPageViews', 'analytics.metrics.averagePageLoadTime', 'analytics.metrics.bounceRate', 'analytics.metrics.exitRate'],
    name: 'home.analytics.reportTypes.pagePerformance',
  },
  ecommerce: {
    dimensions: ['analytics.dimensions.date', 'analytics.dimensions.itemName', 'analytics.dimensions.itemCategory'],
    metrics: ['analytics.metrics.itemViews', 'analytics.metrics.itemsAddedToCart', 'analytics.metrics.purchases', 'analytics.metrics.itemRevenue'],
    name: 'home.analytics.reportTypes.ecommerce',
  },
  user_behavior: {
    dimensions: ['analytics.dimensions.sessionSourceMedium', 'analytics.dimensions.landingPage'],
    metrics: ['analytics.metrics.sessions', 'analytics.metrics.engagementRate', 'analytics.metrics.conversionsPerSession', 'analytics.metrics.averageSessionDuration'],
    name: 'home.analytics.reportTypes.userBehavior',
  },
  mobile_app: {
    dimensions: ['analytics.dimensions.appVersion', 'analytics.dimensions.operatingSystem'],
    metrics: ['analytics.metrics.screenPageViews', 'analytics.metrics.crashFreeUsersRate', 'analytics.metrics.userEngagementDuration', 'analytics.metrics.sessions'],
    name: 'home.analytics.reportTypes.mobileApp',
  },
  ad_performance: {
    dimensions: ['analytics.dimensions.date', 'analytics.dimensions.adGroup', 'analytics.dimensions.adContent'],
    metrics: ['analytics.metrics.adClicks', 'analytics.metrics.adImpressions', 'analytics.metrics.adCost', 'analytics.metrics.adConversions'],
    name: 'home.analytics.reportTypes.adPerformance',
  },
  content: {
    dimensions: ['analytics.dimensions.pageTitle', 'analytics.dimensions.pagePath'],
    metrics: ['analytics.metrics.screenPageViews', 'analytics.metrics.averageTimeOnPage', 'analytics.metrics.entrances', 'analytics.metrics.exitRate'],
    name: 'home.analytics.reportTypes.content',
  },
  geographic: {
    dimensions: ['analytics.dimensions.country', 'analytics.dimensions.region', 'analytics.dimensions.city'],
    metrics: ['analytics.metrics.totalUsers', 'analytics.metrics.sessions', 'analytics.metrics.screenPageViews', 'analytics.metrics.conversions'],
    name: 'home.analytics.reportTypes.geographic',
  },
  technical: {
    dimensions: ['analytics.dimensions.browser', 'analytics.dimensions.operatingSystem', 'analytics.dimensions.deviceCategory'],
    metrics: ['analytics.metrics.totalUsers', 'analytics.metrics.sessions', 'analytics.metrics.averagePageLoadTime', 'analytics.metrics.bounceRate'],
    name: 'home.analytics.reportTypes.technical',
  },
  event: {
    dimensions: ['analytics.dimensions.eventName', 'analytics.dimensions.eventCategory'],
    metrics: ['analytics.metrics.eventCount', 'analytics.metrics.eventValue', 'analytics.metrics.totalUsers', 'analytics.metrics.sessions'],
    name: 'home.analytics.reportTypes.event',
  },
  conversion_funnel: {
    dimensions: ['analytics.dimensions.date', 'analytics.dimensions.sessionSourceMedium'],
    metrics: ['analytics.metrics.sessions', 'analytics.metrics.addToCarts', 'analytics.metrics.checkouts', 'analytics.metrics.purchases'],
    name: 'home.analytics.reportTypes.conversionFunnel',
  },
}

// 定义一个类型，允许任何字符串作为键
type MetricColors = {
  [key: string]: string
}

const backgroundColors: MetricColors = {
  'analytics.metrics.totalUsers': '#E6F7FF',
  'analytics.metrics.newUsers': '#FFEBE5',
  'analytics.metrics.sessions': '#E7F8E7',
  'analytics.metrics.bounceRate': '#FFF0F6',
  'analytics.metrics.screenPageViews': '#F3E7F8',
  'analytics.metrics.averageSessionDuration': '#E7EFFF',
  'analytics.metrics.conversions': '#FFE8E7',
  'analytics.metrics.averagePageLoadTime': '#E6FFE5',
  'analytics.metrics.exitRate': '#EAF2FF',
  'analytics.metrics.itemViews': '#FFFBE6',
  'analytics.metrics.itemsAddedToCart': '#F0F8FF',
  'analytics.metrics.purchases': '#FFF5E6',
  'analytics.metrics.itemRevenue': '#E6FFF7',
  'analytics.metrics.engagementRate': '#FFE6F6',
  'analytics.metrics.conversionsPerSession': '#F2FFE6',
  'analytics.metrics.crashFreeUsersRate': '#E6ECFF',
  'analytics.metrics.userEngagementDuration': '#FFF6E6',
  'analytics.metrics.adClicks': '#E6FFFA',
  'analytics.metrics.adImpressions': '#FFE6E6',
  'analytics.metrics.adCost': '#E6F9FF',
  'analytics.metrics.adConversions': '#FFF1E6',
  'analytics.metrics.averageTimeOnPage': '#E6FFE9',
  'analytics.metrics.entrances': '#FFE6F3',
  'analytics.metrics.eventCount': '#E6F0FF',
  'analytics.metrics.eventValue': '#FFFFE6',
  'analytics.metrics.addToCarts': '#E6FEFF',
  'analytics.metrics.checkouts': '#FFE6EC',
}

const getBackgroundColor = (metric: string): CSSProperties => {
  return {
    backgroundColor: backgroundColors[metric] || '#FFFBEA', // 默认颜色
    padding: '20px',
    borderRadius: '10px',
    textAlign: 'center',
    margin: '10px',
  }
}

const selectReportType = (reportType: keyof typeof reportTypes) => {
  if (changeType.value !== reportType) {
    changeType.value = reportType
    focusedMetric.value = null
    getGA4Data()
  }
}

const handleDateChange = (dateRange: Date[]) => {
  if (dateRange && dateRange.length === 2) {
    time.value = dateRange.map(date => {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }) as [string, string]
    getGA4Data()
  }
}

const getGA4Data = async () => {
  if (!echarts) return
  try {
    echarts.showLoading()
    const [startDate, endDate] = time.value
    const res = await http.get(`/system/widgets`, {
      type: 'ga4_widget',
      startDate,
      endDate,
      reportType: changeType.value,
    })

    if (res.data.code === 200) {
      ga4Data.value = res.data.data[0].data
      reportTitle.value = ga4Data.value.title || 'Google Analytics 4 Report'
      processGA4Data()
    }
  } catch (error) {
    console.error('Error fetching GA4 data:', error)
  } finally {
    if (echarts) {
      echarts.hideLoading()
    }
  }
}

const processGA4Data = () => {
  if (!ga4Data.value) return

  const allMetrics = reportTypes[changeType.value].metrics
  const aggregatedData: Record<string, number[]> = {}

  ga4Data.value.data.forEach((item: any) => {
    const date = item.dimensions[0]
    if (!aggregatedData[date]) {
      aggregatedData[date] = new Array(allMetrics.length).fill(0)
    }
    allMetrics.forEach((metric: string, index: number) => {
      const metricIndex = ga4Data.value.metrics.indexOf(metric.split('.').pop())
      if (metricIndex !== -1) {
        aggregatedData[date][index] += parseFloat(item.metrics[metricIndex] || '0')
      }
    })
  })

  // 更新所有指标的值
  metricValues.value = allMetrics.reduce((acc: Record<string, string>, metric: string, index: number) => {
    acc[metric] = Object.values(aggregatedData)
      .reduce((sum, values) => sum + values[index], 0)
      .toFixed(2)
    return acc
  }, {})

  updateChart() // 确保在处理完数据后更新图表
}

const updateChart = () => {
  if (!ga4Data.value || !echarts) return

  const categories = Object.keys(
    ga4Data.value.data.reduce((acc: Record<string, boolean>, item: any) => {
      acc[item.dimensions[0]] = true
      return acc
    }, {}),
  ).sort()

  const metricsToShow = focusedMetric.value ? [focusedMetric.value] : reportTypes[changeType.value].metrics

  const series = metricsToShow.map((metric: string) => {
    const metricIndex = ga4Data.value.metrics.indexOf(metric.split('.').pop())
    return {
      name: t(metric),
      type: 'line',
      data: categories.map(date => {
        const item = ga4Data.value.data.find((d: any) => d.dimensions[0] === date)
        return item ? parseFloat(item.metrics[metricIndex] || '0') : 0
      }),
    }
  })

  // 完全重置图表配置
  echarts.setOption(
    {
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      legend: {
        data: series.map(s => s.name),
        selected: series.reduce((acc, s) => ({ ...acc, [s.name]: true }), {}),
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: Math.floor(categories.length / 10),
          rotate: 45,
          margin: 60,
        },
      },
      yAxis: { type: 'value' },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '20%',
        top: '10%',
        containLabel: true,
      },
      series: series,
    },
    true,
  ) // 使用 true 作为第二个参数，强制完全重新渲染图表
}

const toggleMetricFocus = (metric: string) => {
  focusedMetric.value = focusedMetric.value === metric ? null : metric
  updateChart() // 直接调用 updateChart，而不是 processGA4Data
}

onMounted(() => {
  if (echartsRef.value) {
    // @ts-ignore
    echarts = ECharts.init(echartsRef.value)
    getGA4Data()

    window.addEventListener('resize', () => {
      echarts?.resize()
    })
  }
})
</script>

<script lang="ts">
export default {
  name: 'Analytics',
}
</script>

<style lang="scss" scoped>
.item {
  width: 100%;

  .box {
    padding: 16px;

    .data-set-list {
      display: flex;
      gap: 10px;
      margin: 0 -7px 26px;
      width: 100%;
      min-width: 1500px;
    }
  }
}

.echarts-container {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;

  .echart-content {
    min-width: 1200px;
  }

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #3176e0;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f0f0f0;
    border-radius: 3px;
  }
}

.metrics-container {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.metric-item {
  min-width: 15%;
  margin-right: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &.metric-focused {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
  }
}

.metric-value {
  font-size: 24px;
  color: #333;
}

.metric-type {
  font-size: 14px;
  color: #666;
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-border-radius: 4px;
}
</style>

