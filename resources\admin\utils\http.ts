import ax, { type AxiosRequestConfig } from 'axios'
import { useAmisAdminStore } from '/admin/stores/admin'
import { env, getAuthToken } from '/admin/support/helper'
import Message from '/admin/support/message'
import { Code } from '/admin/enum/app'
import router from '/admin/router'

const { config } = useAmisAdminStore()

const baseURL = env('VITE_BASE_URL')
const axios = ax.create({
    baseURL: baseURL,
    timeout: 20 * 1000,
    maxBodyLength: 5 * 1024 * 1024,
    // withCredentials: true,
})

axios.interceptors.request.use(
    (config: AxiosRequestConfig | any) => {
        const token = getAuthToken()
        if (token) {
            config.headers = {
                ...config.headers,
                'Authorization': `Bearer ${token}`,
            }
        }
        config.params = {
            ...config.params,
        }
        return config
    },
    function(error) {
        return Promise.reject(error)
    },
)

// 简化响应拦截器 - 去掉登录验证逻辑
axios.interceptors.response.use(
    (response) => {
        // 简单检查响应结构，直接返回成功的响应
        const { code, message } = response.data || {}
        if (code === 200 || !code) {
            // 请求成功，直接返回数据
            return response
        } else {
            // 其他错误情况，显示错误信息但不重定向到登录页
            Message.error(message || 'Request failed.')
            return Promise.reject(response)
        }
    },
    function(error) {
        // 全局错误处理，不包含登录相关逻辑
        Message.error(error.message || 'Network error.')
        return Promise.reject(error)
    },
)

const http: Http = {
    get(url, params) {
        return new Promise((resolve, reject) => {
            axios
                .get(url, { params })
                .then((res) => {
                    resolve(res.data)
                })
                .catch((err) => {
                    // 检查err.response是否存在，如果不存在，使用整个err对象
                    if (err.data && err.data.message) {
                        // 如果err.response存在且有data，则使用它
                        reject({ message: err.data.message, error: err });
                    } else {
                        // 如果err.response不存在，可能是网络错误或其他情况，返回更通用的错误信息
                        reject({ message: "An error occurred", error: err });
                    }
                });
        })
    },

    post(url, params) {
        return new Promise((resolve, reject) => {
            axios
                .post(url, JSON.stringify(params), {
                    headers: {
                        'X-CSRF-TOKEN':
                            document.head
                                .querySelector('meta[name="csrf-token"]')
                                ?.getAttribute('content') ?? '',
                    },
                })
                .then((res) => {
                    resolve(res.data)
                })
                .catch((err) => {
                    reject(err.data)
                })
        })
    },
}

interface Http {
    get(url: string, params?: object): Promise<any>

    post(url: string, params?: object): Promise<any>
}

export default http
