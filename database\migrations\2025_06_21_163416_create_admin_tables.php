<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 运行迁移
     * 创建IAM用户表 - 基于TVB.sql的admins表重构，
     */
    public function up(): void
    {
        Schema::create('admins', function (Blueprint $table) {
            // 主键
            $table->id()->comment('用户ID');
            
            // 基础信息字段
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('password', 255)->comment('密码（加密）');
            $table->string('email', 100)->unique()->comment('邮箱地址');
            $table->string('first_name', 50)->nullable()->comment('名字');
            $table->string('last_name', 50)->nullable()->comment('姓氏');
            $table->string('real_name', 100)->nullable()->comment('真实姓名');
            $table->string('pen_name', 100)->nullable()->comment('笔名，用于前台显示');
            
            // 联系信息
            $table->string('phone', 20)->nullable()->comment('手机号码');
            $table->string('avatar_url', 255)->nullable()->comment('头像URL');
            
            // 状态
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用，1-启用，2-待激活');
            
            // 登录相关
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            $table->string('last_login_ip', 45)->nullable()->comment('最后登录IP');
            $table->integer('login_count')->default(0)->comment('登录次数');
            
            // 语言
            $table->string('language', 10)->default('zh_HK')->comment('语言');
            
            // 审计字段
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新者ID');
            
            //时间字段
            $table->timestamp('password_changed_at')->nullable()->comment('密码最后修改时间');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['status'], 'idx_status');
            $table->index(['last_login_at'], 'idx_last_login');
            $table->index(['created_at'], 'idx_created_at');
            $table->index(['email'], 'idx_email');
            $table->index(['username'], 'idx_username');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('admins');
    }
};