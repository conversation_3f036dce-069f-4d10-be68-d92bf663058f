<template>
  <div class="ga4-iframe-container">
    <!-- 内容区域 -->
    <div class="ga4-content">
      <div v-if="iframeHtml" v-html="iframeHtml" class="iframe-wrapper"></div>
      <div v-else class="loading">{{ t('dashboard.ga4Iframe.loading') }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

const { t } = useI18n()
const iframeHtml = ref('')

const getGa4 = async () => {
  try {
    const res = await http.get('/dashboard/ga4Iframe/data')
    if (res.data && res.data.data) {
      iframeHtml.value = res.data.data.iframe
    }
  } catch (error) {
    console.error(t('dashboard.ga4Iframe.error'), error)
  }
}

onMounted(() => {
  getGa4()
})
</script>

<script lang="ts">
export default {
  name: 'Ga4Iframe',
}
</script>

<style scoped lang="scss">
.ga4-iframe-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;


  .ga4-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .iframe-wrapper {
      position: relative;
      width: 100%;
      flex: 1;
      min-height: 0;

      :deep(iframe) {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
      }
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
    }
  }
}
:deep(.canvas-layout) {
    .mainBlock {
      overflow: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(189,216,248, 1);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(189,216,248, 1);
        }
      }
    }
  }
@media screen and (max-width: 768px) {
  .ga4-iframe-container {
    min-height: 300px;
    height: calc(100vh - 200px);
  }
}

@media screen and (max-width: 480px) {
  .ga4-iframe-container {
    min-height: 250px;
    height: calc(100vh - 150px);
  }
}
</style>
