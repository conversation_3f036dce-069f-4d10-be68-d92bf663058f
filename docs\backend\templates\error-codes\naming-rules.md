# 错误码命名规则

## 概述

本文档规定了错误码的命名规则和组织方式，以确保错误码的一致性和可维护性。

## 错误码格式

### 1. 基本结构

错误码采用 5 位数字格式：`AABBB`

- `AA`: 模块编号（10-99）
- `BBB`: 具体错误编号（000-999）

### 2. 模块编号分配

- 10: 通用错误
- 11: 业务错误
- 12: 用户模块
- 13: 订单模块
- 14: 支付模块
- 15: 商品模块
- 16: 库存模块
- 17: 物流模块
- 18: 营销模块
- 19: 统计模块
- 20-29: 预留给其他核心模块
- 30-99: 预留给扩展模块

### 3. 错误编号分配

每个模块的错误编号按以下规则分配：

- 000-099: 通用错误
- 100-199: 创建相关错误
- 200-299: 更新相关错误
- 300-399: 删除相关错误
- 400-499: 查询相关错误
- 500-599: 状态相关错误
- 600-699: 验证相关错误
- 700-799: 处理相关错误
- 800-899: 业务相关错误
- 900-999: 系统相关错误

## 命名规范

### 1. 枚举命名

```php
enum UserErrorCode: int
{
    /**
     * 通用错误 (12000-12099)
     */
    case USER_NOT_FOUND = 12000;
    case USER_ALREADY_EXISTS = 12001;
    case USER_DISABLED = 12002;

    /**
     * 创建相关错误 (12100-12199)
     */
    case USER_CREATE_FAILED = 12100;
    case USER_REGISTER_FAILED = 12101;
    case USER_IMPORT_FAILED = 12102;

    /**
     * 更新相关错误 (12200-12299)
     */
    case USER_UPDATE_FAILED = 12200;
    case USER_PASSWORD_UPDATE_FAILED = 12201;
    case USER_PROFILE_UPDATE_FAILED = 12202;

    /**
     * 删除相关错误 (12300-12399)
     */
    case USER_DELETE_FAILED = 12300;
    case USER_BATCH_DELETE_FAILED = 12301;
    case USER_SOFT_DELETE_FAILED = 12302;

    /**
     * 查询相关错误 (12400-12499)
     */
    case USER_QUERY_FAILED = 12400;
    case USER_SEARCH_FAILED = 12401;
    case USER_LIST_FAILED = 12402;

    /**
     * 状态相关错误 (12500-12599)
     */
    case USER_STATUS_INVALID = 12500;
    case USER_STATUS_UPDATE_FAILED = 12501;
    case USER_STATE_TRANSITION_FAILED = 12502;

    /**
     * 验证相关错误 (12600-12699)
     */
    case USER_VALIDATION_FAILED = 12600;
    case USER_PASSWORD_VALIDATION_FAILED = 12601;
    case USER_EMAIL_VALIDATION_FAILED = 12602;

    /**
     * 处理相关错误 (12700-12799)
     */
    case USER_PROCESS_FAILED = 12700;
    case USER_SYNC_FAILED = 12701;
    case USER_IMPORT_PROCESS_FAILED = 12702;

    /**
     * 业务相关错误 (12800-12899)
     */
    case USER_BUSINESS_RULE_VIOLATED = 12800;
    case USER_PERMISSION_DENIED = 12801;
    case USER_QUOTA_EXCEEDED = 12802;

    /**
     * 系统相关错误 (12900-12999)
     */
    case USER_SYSTEM_ERROR = 12900;
    case USER_DATABASE_ERROR = 12901;
    case USER_NETWORK_ERROR = 12902;
}
```

### 2. 错误信息命名

```php
public function message(): string
{
    return match ($this) {
        // 通用错误
        self::USER_NOT_FOUND => '用户不存在',
        self::USER_ALREADY_EXISTS => '用户已存在',
        self::USER_DISABLED => '用户已禁用',

        // 创建相关错误
        self::USER_CREATE_FAILED => '创建用户失败',
        self::USER_REGISTER_FAILED => '用户注册失败',
        self::USER_IMPORT_FAILED => '用户导入失败',

        // 更新相关错误
        self::USER_UPDATE_FAILED => '更新用户失败',
        self::USER_PASSWORD_UPDATE_FAILED => '更新密码失败',
        self::USER_PROFILE_UPDATE_FAILED => '更新用户资料失败',

        // 删除相关错误
        self::USER_DELETE_FAILED => '删除用户失败',
        self::USER_BATCH_DELETE_FAILED => '批量删除用户失败',
        self::USER_SOFT_DELETE_FAILED => '软删除用户失败',

        // 查询相关错误
        self::USER_QUERY_FAILED => '查询用户失败',
        self::USER_SEARCH_FAILED => '搜索用户失败',
        self::USER_LIST_FAILED => '获取用户列表失败',

        // 状态相关错误
        self::USER_STATUS_INVALID => '用户状态无效',
        self::USER_STATUS_UPDATE_FAILED => '更新用户状态失败',
        self::USER_STATE_TRANSITION_FAILED => '用户状态转换失败',

        // 验证相关错误
        self::USER_VALIDATION_FAILED => '用户验证失败',
        self::USER_PASSWORD_VALIDATION_FAILED => '密码验证失败',
        self::USER_EMAIL_VALIDATION_FAILED => '邮箱验证失败',

        // 处理相关错误
        self::USER_PROCESS_FAILED => '处理用户数据失败',
        self::USER_SYNC_FAILED => '同步用户数据失败',
        self::USER_IMPORT_PROCESS_FAILED => '处理用户导入数据失败',

        // 业务相关错误
        self::USER_BUSINESS_RULE_VIOLATED => '违反用户业务规则',
        self::USER_PERMISSION_DENIED => '用户权限不足',
        self::USER_QUOTA_EXCEEDED => '超出用户配额限制',

        // 系统相关错误
        self::USER_SYSTEM_ERROR => '用户系统错误',
        self::USER_DATABASE_ERROR => '用户数据库错误',
        self::USER_NETWORK_ERROR => '用户网络错误',
    };
}
```

## 最佳实践

### 1. 错误码定义

```php
// 好的实践 - 清晰的分组和注释
enum OrderErrorCode: int
{
    /**
     * 通用错误 (13000-13099)
     */
    case ORDER_NOT_FOUND = 13000;
    case ORDER_ALREADY_EXISTS = 13001;

    /**
     * 状态相关错误 (13500-13599)
     */
    case ORDER_STATUS_INVALID = 13500;
    case ORDER_STATUS_TRANSITION_FAILED = 13501;
}

// 不好的实践 - 混乱的编号和缺少注释
enum OrderErrorCode: int
{
    case ERROR_1 = 13001;
    case ERROR_2 = 13005;
    case ERROR_3 = 13002;
}
```

### 2. 错误信息定义

```php
// 好的实践 - 清晰的错误描述
public function message(): string
{
    return match ($this) {
        self::ORDER_NOT_FOUND => '订单不存在，请检查订单号',
        self::ORDER_STATUS_INVALID => '订单状态无效，当前状态不允许此操作',
    };
}

// 不好的实践 - 模糊的错误描述
public function message(): string
{
    return match ($this) {
        self::ORDER_NOT_FOUND => '订单错误',
        self::ORDER_STATUS_INVALID => '状态错误',
    };
}
```

## 注意事项

1. 错误码编号要按规则分配
2. 保持模块内错误码的连续性
3. 错误信息要清晰明确
4. 及时更新错误码文档
5. 避免重复定义错误码
6. 保持向后兼容性
7. 合理规划错误码空间
8. 定���检查和维护
9. 统一错误信息格式
10. 考虑多语言支持
