export const aboutPageTemplate = `
<div class="marketing-team-page responsive-block">
  <!-- 标题区 -->
  <div data-bs-component="bootstrap-heading" class="py-5 text-center hero-section">
    <div class="container">
      <div data-bs-component="richTextBlock">
        <h1 class="display-4 fw-bold">The dream team of digital marketing</h1>
        <p class="text-muted">Elevate has revolutionized the digital marketing world since 2018, transforming from a small team of passionate marketers to a globally distributed creative force</p>
      </div>
    </div>
  </div>

  <!-- 团队仪表板展示 -->
  <div data-bs-component="infoSectionBlock" class="dashboard-showcase">
    <div class="container py-4">
      <div class="p-4 mb-4 dashboard-container rounded-4">
        <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/about-banner.webp" class="img-fluid rounded-3" alt="Marketing dashboard">
      </div>
    </div>
  </div>

  <!-- 数据统计 -->
  <div data-bs-component="bootstrap-metrics" class="py-5 stats-section bg-light">
    <div class="container">
      <div class="text-center row">
        <div class="mb-4 col-md-4 mb-md-0">
          <div data-bs-component="richTextBlock">
            <h2 class="display-5 fw-bold text-primary">100M+</h2>
            <p class="text-muted">Monthly active users</p>
          </div>
        </div>
        <div class="mb-4 col-md-4 mb-md-0">
          <div data-bs-component="richTextBlock">
            <h2 class="display-5 fw-bold text-primary">200k+</h2>
            <p class="text-muted">Marketing assets provided</p>
          </div>
        </div>
        <div class="col-md-4">
          <div data-bs-component="richTextBlock">
            <h2 class="display-5 fw-bold text-primary">150</h2>
            <p class="text-muted">Countries across the world</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 服务介绍 -->
  <div data-bs-component="featureCardsBlock" class="py-5 text-white services-section bg-dark">
    <div class="container">
      <div class="mb-5 text-center">
        <div data-bs-component="richTextBlock">
          <h2 class="display-6 fw-bold">Our services</h2>
        </div>
      </div>
      <div class="row">
        <div class="mb-4 col-md-4">
          <div class="p-4 service-card h-100">
            <div class="mb-3 service-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-play-circle text-primary" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
              </svg>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Content Creation</h3>
              <p class="text-white-50">We develop engaging, innovative content across various channels, tailored to engage those in digital audience</p>
            </div>
          </div>
        </div>
        <div class="mb-4 col-md-4">
          <div class="p-4 service-card h-100">
            <div class="mb-3 service-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bar-chart text-primary" viewBox="0 0 16 16">
                <path d="M4 11H2v3h2v-3zm5-4H7v7h2V7zm5-5v12h-2V2h2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1h-2zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-3z"/>
              </svg>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Marketing Analytics</h3>
              <p class="text-white-50">Our platform empowers informed decision making, tracking campaign performance, analytics, and ROI</p>
            </div>
          </div>
        </div>
        <div class="mb-4 col-md-4">
          <div class="p-4 service-card h-100">
            <div class="mb-3 service-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-graph-up text-primary" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
              </svg>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Journey Optimization</h3>
              <p class="text-white-50">Use customer data to deliver personalized messaging and content, enhancing engagement and conversion, optimizing sales</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 使命宣言 -->
  <div data-bs-component="richTextBlock" class="py-5 mission-section">
    <div class="container">
      <div class="text-center">
        <span class="mission-icon">➕</span>
        <div data-bs-component="richTextBlock">
          <h3 class="mb-2 text-uppercase small text-muted">OUR MISSION</h3>
          <h2 class="mb-4 display-6 fw-bold">To revolutionize digital marketing with solutions that empower businesses to achieve unparalleled success</h2>
        </div>
      </div>
    </div>
  </div>

  <!-- 核心价值观 -->
  <div data-bs-component="layoutBlock" class="py-5 values-section bg-light">
    <div class="container">
      <div class="mb-4 text-center">
        <div data-bs-component="richTextBlock">
          <h2 class="display-6 fw-bold">Our values</h2>
        </div>
      </div>
      <div class="row" data-bs-component="featureCardsBlock">
        <div class="mb-4 col-md-4">
          <div class="p-4 text-center bg-white shadow-sm value-card h-100 rounded-4">
            <div class="mb-3 value-icon bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
              <span class="fs-4 text-primary">🚀</span>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Innovation</h3>
              <p class="text-muted">Embrace creativity and forward-thinking approach to inspire innovative solutions</p>
            </div>
          </div>
        </div>
        <div class="mb-4 col-md-4">
          <div class="p-4 text-center bg-white shadow-sm value-card h-100 rounded-4">
            <div class="mb-3 value-icon bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
              <span class="fs-4 text-primary">⭐</span>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Integrity</h3>
              <p class="text-muted">Operate with honesty, transparency, and ethical practices in all interactions</p>
            </div>
          </div>
        </div>
        <div class="mb-4 col-md-4">
          <div class="p-4 text-center bg-white shadow-sm value-card h-100 rounded-4">
            <div class="mb-3 value-icon bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
              <span class="fs-4 text-primary">💜</span>
            </div>
            <div data-bs-component="richTextBlock">
              <h3>Empathy</h3>
              <p class="text-muted">Demonstrate compassion and genuine care for clients to support their needs</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 团队展示 -->
  <div data-bs-component="layoutBlock" class="py-5 team-section">
    <div class="container">
      <div class="mb-4 text-center">
        <div data-bs-component="richTextBlock">
          <h2 class="display-6 fw-bold">Our team</h2>
          <p class="text-muted">Meet the awesome folks who make all of this possible day to day</p>
        </div>
      </div>
      <div class="row">
        <!-- 团队成员 1 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 2 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-2.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 3 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-3.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 4 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 5 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-2.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 6 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-3.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 7 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
        <!-- 团队成员 8 -->
        <div class="mb-4 col-md-3 col-sm-6">
          <div class="text-center team-card">
            <div class="mb-3">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-2.webp" class="img-fluid rounded-4" alt="Team member">
            </div>
            <div data-bs-component="richTextBlock">
              <h5 class="mb-1">Team member</h5>
              <p class="text-muted small">Role here</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 加入团队 -->
  <div data-bs-component="bootstrap-cta" class="py-5 text-white join-team-section bg-dark">
    <div class="container">
      <div class="p-4 text-center join-team-container p-md-5 rounded-4">
        <div data-bs-component="richTextBlock">
          <h2 class="mb-4 fw-bold">Join our team</h2>
          <p class="mb-4">Let's work on great things together and enjoy at the perks of Elevate. Explore exciting opportunities and be a part of a dynamic and innovative work environment.</p>
        </div>
        <div data-bs-component="bootstrap-button">
          <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Get in touch</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚导航 -->
  <div data-bs-component="footerBlock" class="py-5 footer-nav bg-light">
    <div class="container">
      <div class="py-3 text-center">
        <div class="mb-3">
          <p class="text-muted">No menus selected</p>
        </div>
        <div class="footer-nav-dots d-flex justify-content-center">
          <span class="mx-1 footer-dot"></span>
          <span class="mx-1 footer-dot"></span>
          <span class="mx-1 footer-dot"></span>
          <span class="mx-1 footer-dot"></span>
          <span class="mx-1 footer-dot"></span>
        </div>
      </div>
      <div class="mt-3 text-center">
        <div data-bs-component="richTextBlock">
          <p class="text-muted small">Privacy Policy | Legal | © 2023, All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>

<style>
/* 基础样式 */
.marketing-team-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

/* 标题区域 */
.hero-section {
  padding: 80px 0 60px;
}

.hero-section h1 {
  margin-bottom: 1rem;
}

/* 仪表板展示 */
.dashboard-showcase {
  position: relative;
}

.dashboard-container {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* 数据统计 */
.stats-section {
  background-color: #f8f9fa;
}

.stats-section h2 {
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.stats-section .col-md-4:hover h2 {
  transform: scale(1.05);
}

/* 服务区域 */
.service-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.service-icon {
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

/* 使命宣言 */
.mission-section {
  position: relative;
  padding: 80px 0;
}

.mission-icon {
  display: inline-block;
  font-size: 1.5rem;
  color: #6c5ce7;
  margin-bottom: 1rem;
}

/* 核心价值观 */
.value-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.value-icon {
  transition: transform 0.3s ease;
}

.value-card:hover .value-icon {
  transform: scale(1.1);
}

/* 团队展示 */
.team-card {
  transition: transform 0.3s ease;
}

.team-card img {
  transition: transform 0.5s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-card:hover {
  transform: translateY(-5px);
}

.team-card:hover img {
  transform: scale(1.03);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 加入团队 */
.join-team-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.join-team-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.bootstrap-button {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bootstrap-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 页脚导航 */
.footer-nav {
  background-color: #f8f9fa;
}

.footer-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #6c5ce7;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.footer-dot:hover {
  opacity: 1;
  transform: scale(1.5);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
  .hero-section {
    padding: 60px 0 40px;
  }
  
  .mission-section {
    padding: 60px 0;
  }
  
  .team-card, .value-card, .service-card {
    margin-bottom: 1.5rem;
  }
  
  .stats-section h2 {
    font-size: 2.5rem;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease forwards;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // 获取页面元素
  const sections = document.querySelectorAll('[data-bs-component]');
  const statsNumbers = document.querySelectorAll('.stats-section h2');
  let animated = false;
  
  // 检查元素是否在视口中并应用动画
  function checkScroll() {
    sections.forEach((section) => {
      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if (sectionTop < windowHeight * 0.8) {
        section.classList.add('animate-fadeInUp');
      }
    });
    
    // 数字增长动画
    if (!animated) {
      const statsSection = document.querySelector('.stats-section');
      if (!statsSection) return;
      
      const statsSectionTop = statsSection.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if (statsSectionTop < windowHeight * 0.8) {
        statsNumbers.forEach(number => {
          const targetText = number.innerText;
          const isPlus = targetText.includes('+');
          const isPercentage = targetText.includes('%');
          const isK = targetText.includes('k');
          
          let targetNumber = targetText;
          if (isPlus) targetNumber = targetNumber.replace('+', '');
          if (isPercentage) targetNumber = targetNumber.replace('%', '');
          if (isK) targetNumber = targetNumber.replace('k', '') * 1000;
          
          targetNumber = parseFloat(targetNumber);
          
          let startNumber = 0;
          const duration = 2000; // 2秒
          const interval = 20; // 每20毫秒更新一次
          const steps = duration / interval;
          const increment = targetNumber / steps;
          
          const timer = setInterval(() => {
            startNumber += increment;
            if (startNumber >= targetNumber) {
              startNumber = targetNumber;
              clearInterval(timer);
            }
            
            let displayText = Math.floor(startNumber);
            if (isK) displayText = (displayText / 1000).toFixed(1) + 'k';
            if (isPlus) displayText += '+';
            if (isPercentage) displayText += '%';
            
            number.textContent = displayText;
          }, interval);
        });
        
        animated = true;
      }
    }
  }
  
  // 初始检查
  checkScroll();
  
  // 监听滚动事件
  window.addEventListener('scroll', checkScroll);
  
  // 团队卡片悬停效果
  const teamCards = document.querySelectorAll('.team-card');
  teamCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });
  
  // 价值卡片悬停效果
  const valueCards = document.querySelectorAll('.value-card');
  valueCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
      this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.1)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.05)';
    });
  });
});
</script>
`
