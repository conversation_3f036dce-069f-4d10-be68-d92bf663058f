<?php

namespace Modules\Common\Schedule;

use Bingo\Core\Util\PlatformUtil;
use Bingo\Core\Util\ShellUtil;
use Modules\Common\Provider\Schedule\AbstractScheduleBiz;

class TempFileCleanScheduleBiz extends AbstractScheduleBiz
{
    public function cron(): string
    {
        return $this->cronEveryHour();
    }

    public function title(): string
    {
        return 'temp文件自动清理';
    }

    public function run(): void
    {
        $tempPath = public_path('temp');
        if (PlatformUtil::isWindows()) {
            //TODO
            // $command = 'forfiles /P ' . ShellUtil::pathQuote($tempPath) . ' /M * /D -7 /C "cmd /c if @isdir==TRUE (rmdir /s /q @path) else (del /q @path)"';
        } else {
            $command = 'find "'.$tempPath.'" -mtime +7 -maxdepth 1 -exec rm -rfv {} \\;';
        }
        if (! empty($command)) {
            ShellUtil::run($command);
        }
    }

}
