import { Node, mergeAttributes } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    layoutBlock: {
      /**
       * 插入Bootstrap布局块
       */
      insertLayoutBlock: (options: { html: string }) => ReturnType
    }
  }
}

export const LayoutBlock = Node.create({
  name: 'layoutBlock',
  
  group: 'block',
  
  content: 'block*',
  
  atom: false,
  
  draggable: true,
  
  inline: false,
  
  selectable: true,

  // 支持添加自定义HTML属性
  addAttributes() {
    return {
      // 保存布局类型
      layoutType: {
        default: 'single-column',
        parseHTML: element => {
          // 支持我们所有的布局类型
          const validTypes = [
            'single-column', 
            'two-column', 
            'three-column', 
            '1-3-2-3', 
            '2-3-1-3', 
            'four-column'
          ];
          
          const type = element.getAttribute('data-bs-layout');
          return validTypes.includes(type) ? type : 'single-column';
        }
      },
      // 保存原始HTML内容
      content: {
        default: '',
        parseHTML: element => {
          const temp = document.createElement('div')
          Array.from(element.childNodes).forEach(node => {
            temp.appendChild(node.cloneNode(true))
          })
          return temp.innerHTML
        }
      },
      // 保存class
      class: {
        default: null,
        parseHTML: element => {
          return element.getAttribute('class')
        }
      },
      // 保存style
      style: {
        default: null,
        parseHTML: element => {
          return element.getAttribute('style')
        }
      }
    }
  },

  // 定义HTML解析规则
  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="layout"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }
          
          return {
            layoutType: element.getAttribute('data-bs-layout'),
            class: element.getAttribute('class'),
            style: element.getAttribute('style'),
            content: element.innerHTML
          }
        },
        priority: 100 // 给布局块较高的优先级
      }
    ]
  },

  // 定义HTML渲染规则
  renderHTML({ node }) {
    try {
      // 基础属性
      const attrs: Record<string, string> = {
        'data-bs-component': 'layout',
        'data-bs-layout': node.attrs.layoutType || 'single-column'
      }

      // 添加class
      if (node.attrs.class) {
        attrs.class = node.attrs.class
      } else {
        attrs.class = `bootstrap-layout layout-${node.attrs.layoutType} my-4`
      }

      // 添加style
      if (node.attrs.style) {
        attrs.style = node.attrs.style
      }

      // 使用 prosemirror-model 的数组格式来渲染
      return [
        'div',
        mergeAttributes(attrs),
        ...createChildNodes(node.attrs.content)
      ]
    } catch (error) {
      console.error('Layout block render error:', error)
      // 返回一个基础的错误提示组件
      return ['div', { 
        'data-bs-component': 'layout',
        'data-bs-layout': 'error',
        'class': 'bootstrap-layout layout-error alert alert-danger'
      }, '布局渲染错误']
    }
  },

  // 添加命令
  addCommands() {
    return {
      insertLayoutBlock: options => ({ commands }) => {
        try {
          // 创建临时容器解析HTML
          const temp = document.createElement('div')
          temp.innerHTML = options.html.trim()
          
          // 获取第一个元素
          const element = temp.firstElementChild
          if (!element || !(element instanceof HTMLElement)) {
            return false
          }

          // 确保有data-bs-layout属性
          const layoutType = element.getAttribute('data-bs-layout') || 'single-column'
          element.setAttribute('data-bs-layout', layoutType)
          
          // 确保有data-bs-component属性
          element.setAttribute('data-bs-component', 'layout')

          // 收集属性
          const attrs = {
            layoutType,
            class: element.getAttribute('class'),
            style: element.getAttribute('style'),
            content: element.innerHTML
          }

          // 插入内容
          return commands.insertContent({
            type: this.name,
            attrs
          })
        } catch (error) {
          console.error('Layout block insert error:', error)
          return false
        }
      }
    }
  }
})

// 辅助函数：将 HTML 字符串转换为 ProseMirror 节点数组
function createChildNodes(html: string): any[] {
  if (!html) return []

  const temp = document.createElement('div')
  temp.innerHTML = html

  return Array.from(temp.childNodes).map(node => {
    if (node.nodeType === 1) { // ELEMENT_NODE
      if (node instanceof HTMLElement) {
        const attrs: Record<string, string> = {}
        
        // 收集属性
        Array.from(node.attributes).forEach(attr => {
          attrs[attr.name] = attr.value
        })

        // 递归处理子节点
        return [
          node.tagName.toLowerCase(),
          attrs,
          ...createChildNodes(node.innerHTML)
        ]
      }
    } else if (node.nodeType === 3) { // TEXT_NODE
      return node.textContent || ''
    }

    return ''
  })
} 