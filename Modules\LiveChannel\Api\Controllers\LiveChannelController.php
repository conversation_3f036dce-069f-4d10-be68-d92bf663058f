<?php

declare(strict_types=1);

namespace Modules\LiveChannel\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\LiveChannel\Services\LiveChannelService;
use Modules\LiveChannel\Enums\LiveChannelErrorCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Exception;

class LiveChannelController extends Controller
{
    protected $liveChannelService;

    public function __construct(LiveChannelService $liveChannelService)
    {
        $this->liveChannelService = $liveChannelService;
    }

    /**
     * 获取频道列表
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        try {
            // 获取分页参数，默认每页20条
            $page = (int) $request->input('page', 1);
            $limit = (int) $request->input('limit', 20);
            $keyword = $request->input('keyword', '');
            $status = $request->input('status', '');
            $live_status = $request->input('live_status', '');
            $start_date = $request->input('start_date', '');
            $end_date = $request->input('end_date', '');
            $sort_field = $request->input('sort_field', 'sort');
            $sort_order = $request->input('sort_order', 'asc');

            // 构建查询参数，过滤空值
            $params = [
                'keyword' => $keyword,
                'status' => $status !== '' ? $status : null,
                'live_status' => $live_status !== '' ? $live_status : null,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'sort_field' => $sort_field,
                'sort_order' => $sort_order,
                'per_page' => $limit,
                'page' => $page,
            ];

            // 获取频道列表
            $channels = $this->liveChannelService->getList($params);

            // 返回统一格式
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.success'),
                'data' => [
                    'total' => $channels->total(),
                    'page' => $channels->currentPage(),
                    'limit' => $channels->perPage(),
                    'items' => $channels->getCollection()->toArray(),
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_LIST_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取频道详情
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        try {
            // 查询频道
            $channel = $this->liveChannelService->getDetail($id);

            if (!$channel) {
                return [
                    'code' => LiveChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('LiveChannel::livechannel.channel_not_found'),
                    'data' => null
                ];
            }

            // 返回详情
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.success'),
                'data' => $channel->toArray()
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_NOT_FOUND->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_query_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 创建频道
     * @param Request $request
     * @return array
     */
    public function store(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'required|string|max:100',
                'name_hk' => 'required|string|max:100',
                'description' => 'required|string',
                'description_hk' => 'required|string',
                'cover_image_url' => 'required|url|max:255',
                'stream_url' => 'nullable|url|max:500',
                'stream_key' => 'nullable|string|max:500',
                'start_time' => 'required|date',
                'end_time' => 'required|date|after:start_time',
                'is_audio_only' => 'required|boolean',
                'is_breaking_news' => 'required|boolean',
                'is_hk_only' => 'required|boolean',
                'live_status' => 'nullable|integer|in:0,1,2',
                'status' => 'nullable|integer|in:0,1',
                'sort' => 'nullable|integer|min:0',
                'channel_num' => 'nullable|string|max:20',
            ], [
                'name.required' => Lang::get('LiveChannel::livechannel.name_required'),
                'name_hk.required' => Lang::get('LiveChannel::livechannel.name_hk_required'),
                'description.required' => Lang::get('LiveChannel::livechannel.description_required'),
                'description_hk.required' => Lang::get('LiveChannel::livechannel.description_hk_required'),
                'cover_image_url.required' => Lang::get('LiveChannel::livechannel.cover_image_url_required'),
                'cover_image_url.url' => Lang::get('LiveChannel::livechannel.cover_image_url_url'),
                'start_time.required' => Lang::get('LiveChannel::livechannel.start_time_required'),
                'end_time.required' => Lang::get('LiveChannel::livechannel.end_time_required'),
                'end_time.after' => Lang::get('LiveChannel::livechannel.end_time_after'),
                'is_audio_only.required' => Lang::get('LiveChannel::livechannel.is_audio_only_required'),
                'is_breaking_news.required' => Lang::get('LiveChannel::livechannel.is_breaking_news_required'),
                'is_hk_only.required' => Lang::get('LiveChannel::livechannel.is_hk_only_required'),
            ]);

            // 设置默认值
            $validated['live_status'] = $validated['live_status'] ?? 0;
            $validated['status'] = $validated['status'] ?? 1;
            $validated['sort'] = $validated['sort'] ?? 0;
            $validated['created_by'] = 1;
            $validated['updated_by'] = 1;

            // 记录请求日志
            \Log::info('创建直播频道请求', [
                'request_data' => $request->all(),
                'validated_data' => $validated,
                'user_id' => 1
            ]);

            // 创建频道
            $channel = $this->liveChannelService->create($validated);

            // 记录成功日志
            \Log::info('直播频道创建成功', [
                'channel_id' => $channel->id,
                'channel_name' => $channel->name
            ]);

            // 返回创建的数据
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.create_success'),
                'data' => $channel->toArray()
            ];

        } catch (\Illuminate\Validation\ValidationException $e) {
            // 记录验证错误
            \Log::error('直播频道创建验证失败', [
                'errors' => $e->errors(),
                'request_data' => $request->all(),
                'timestamp' => now()
            ]);
            
            // 返回详细的验证错误信息
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
            
        } catch (Exception $e) {
            // 记录其他错误
            \Log::error('直播频道创建失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'timestamp' => now()
            ]);
            
            return [
                'code' => LiveChannelErrorCode::CHANNEL_CREATE_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_create_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新频道
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'nullable|string|max:100',
                'name_hk' => 'nullable|string|max:100',
                'description' => 'nullable|string',
                'description_hk' => 'nullable|string',
                'cover_image_url' => 'nullable|url|max:255',
                'stream_url' => 'nullable|url|max:500',
                'stream_key' => 'nullable|string|max:500',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after:start_time',
                'is_audio_only' => 'boolean',
                'is_breaking_news' => 'boolean',
                'is_hk_only' => 'boolean',
                'live_status' => 'integer|in:0,1,2',
                'status' => 'integer|in:0,1',
                'sort' => 'integer|min:0',
                'channel_num' => 'nullable|string|max:20',
            ]);

            $validated['updated_by'] = 1;

            // 更新频道
            $channel = $this->liveChannelService->update($id, $validated);

            if (!$channel) {
                return [
                    'code' => LiveChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('LiveChannel::livechannel.channel_not_found'),
                    'data' => null
                ];
            }

            // 返回更新后的数据
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.update_success'),
                'data' => $channel->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_UPDATE_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_update_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 删除频道（软删除）
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        try {
            // 查询频道
            $channel = $this->liveChannelService->getDetail($id);

            if (!$channel) {
                return [
                    'code' => LiveChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('LiveChannel::livechannel.channel_not_found'),
                    'data' => null
                ];
            }

            // 删除频道
            $result = $this->liveChannelService->delete($id);

            if (!$result) {
                return [
                    'code' => LiveChannelErrorCode::CHANNEL_DELETE_FAILED->value,
                    'message' => Lang::get('LiveChannel::livechannel.channel_delete_failed'),
                    'data' => null
                ];
            }

            // 返回删除结果
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.delete_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('LiveChannel::livechannel.delete_success'),
                    'affected_count' => 1
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_DELETE_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_delete_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 批量删除
     * @param Request $request
     * @return array
     */
    public function batchDelete(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:channels,id'
            ], [
                'ids.required' => Lang::get('LiveChannel::livechannel.ids_required'),
                'ids.array' => Lang::get('LiveChannel::livechannel.ids_invalid'),
                'ids.*.integer' => Lang::get('LiveChannel::livechannel.id_invalid'),
                'ids.*.exists' => Lang::get('LiveChannel::livechannel.channel_not_found'),
            ]);

            $ids = $validated['ids'];

            // 执行批量删除
            $result = $this->liveChannelService->batchDelete($ids);

            // 返回批量删除结果
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.batch_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('LiveChannel::livechannel.batch_delete_success', ['count' => count($ids)]),
                    'affected_count' => count($ids)
                ]
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_BATCH_ACTION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.batch_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新直播状态
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function updateLiveStatus(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'live_status' => 'required|integer|in:0,1,2'
            ], [
                'live_status.required' => Lang::get('LiveChannel::livechannel.live_status_required'),
                'live_status.in' => Lang::get('LiveChannel::livechannel.live_status_in'),
            ]);

            $status = $validated['live_status'];
            $channel = $this->liveChannelService->updateLiveStatus($id, $status);

            if (!$channel) {
                return [
                    'code' => LiveChannelErrorCode::CHANNEL_NOT_FOUND->value,
                    'message' => Lang::get('LiveChannel::livechannel.channel_not_found'),
                    'data' => null
                ];
            }

            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.live_status_update_success'),
                'data' => $channel->toArray()
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_STATUS_CHANGE_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.live_status_change_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 批量更新状态
     * @param Request $request
     * @return array
     */
    public function batchUpdateStatus(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:channels,id',
                'status' => 'required|integer|in:0,1'
            ], [
                'ids.required' => Lang::get('LiveChannel::livechannel.ids_required'),
                'ids.array' => Lang::get('LiveChannel::livechannel.ids_invalid'),
                'ids.*.integer' => Lang::get('LiveChannel::livechannel.id_invalid'),
                'ids.*.exists' => Lang::get('LiveChannel::livechannel.channel_not_found'),
                'status.required' => Lang::get('LiveChannel::livechannel.status_required'),
                'status.in' => Lang::get('LiveChannel::livechannel.status_invalid'),
            ]);

            $ids = $validated['ids'];
            $status = $validated['status'];

            // 执行批量更新状态
            $result = $this->liveChannelService->batchUpdateStatus($ids, $status);

            // 返回批量更新结果
            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.batch_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get('LiveChannel::livechannel.batch_live_status_success', ['count' => count($ids)]),
                    'affected_count' => count($ids)
                ]
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_BATCH_ACTION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.batch_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取直播中的频道
     * @return array
     */
    public function getLiveChannels(): array
    {
        try {
            $channels = $this->liveChannelService->getLiveChannels();

            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.success'),
                'data' => $channels->toArray()
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_LIST_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取频道统计
     * @return array
     */
    public function getStatistics(): array
    {
        try {
            $statistics = $this->liveChannelService->getStatistics();

            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.success'),
                'data' => $statistics
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_LIST_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.channel_list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 绑定直播配置
     * @param Request $request
     * @return array
     */
    public function bindStreamConfig(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'id' => 'required|integer|exists:channels,id',
                'stream_url' => 'required|url|max:500',
                'stream_key' => 'required|string|max:500',
            ], [
                'id.required' => Lang::get('LiveChannel::livechannel.id_required'),
                'id.integer' => Lang::get('LiveChannel::livechannel.id_integer'),
                'id.exists' => Lang::get('LiveChannel::livechannel.id_exists'),
                'stream_url.required' => Lang::get('LiveChannel::livechannel.stream_url_required'),
                'stream_url.url' => Lang::get('LiveChannel::livechannel.stream_url_url'),
                'stream_key.required' => Lang::get('LiveChannel::livechannel.stream_key_required'),
                'stream_key.string' => Lang::get('LiveChannel::livechannel.stream_key_string'),
            ]);

            // 绑定直播配置
            $channel = $this->liveChannelService->bindStreamConfig(
                $validated['id'],
                $validated['stream_url'],
                $validated['stream_key']
            );

            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.bind_stream_config_success'),
                'data' => [
                    'id' => $channel->id,
                    'name' => $channel->name,
                    'stream_url' => $channel->stream_url,
                    'stream_key' => $channel->stream_key,
                    'updated_at' => $channel->updated_at
                ]
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_VALIDATION_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_UPDATE_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.bind_stream_config_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取频道下拉选项
     * @param Request $request
     * @return array
     */
    public function getChannelOptions(Request $request): array
    {
        try {
            // 支持多种参数名
            $keyword = $request->get('keyword', $request->get('key', ''));
            $limit = $request->get('limit', 20);

            $channels = $this->liveChannelService->getChannelOptions($keyword, $limit);

            return [
                'code' => 200,
                'message' => Lang::get('LiveChannel::livechannel.get_options_success'),
                'data' => $channels
            ];
        } catch (Exception $e) {
            return [
                'code' => LiveChannelErrorCode::CHANNEL_LIST_FAILED->value,
                'message' => Lang::get('LiveChannel::livechannel.get_options_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
} 