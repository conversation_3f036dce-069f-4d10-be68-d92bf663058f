<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="950" xmlns="http://www.w3.org/2000/svg" version="1.1">
  <defs>
    <style type="text/css">
      .layer-title { font-size: 20px; font-weight: bold; text-anchor: middle; }
      .section-title { font-size: 18px; font-weight: bold; text-anchor: middle; }
      .component-text { font-size: 13px; text-anchor: middle; dominant-baseline: middle; }
      .component-box { stroke: #333; stroke-width: 1; }
      .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
      .data-flow { stroke: #4FC08D; stroke-width: 2; marker-end: url(#greenArrow); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4FC08D" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1200" height="950" fill="white"/>
  
  <!-- 标题 -->
  <text x="600" y="40" font-size="24" font-weight="bold" text-anchor="middle">BWMS 前台架构图</text>
  <text x="600" y="65" font-size="16" font-style="italic" text-anchor="middle">Vue 3 + TypeScript + BWMS API</text>

  <!-- 用户访问层 -->
  <rect x="60" y="100" width="1080" height="80" fill="#E6F7FF" stroke="#0099CC" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="125" class="section-title">用户访问层</text>
  <g transform="translate(100, 145)">
    <rect x="0" y="0" width="200" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">网站访客</text>
    
    <rect x="220" y="0" width="200" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="320" y="13" class="component-text" fill="white">注册用户</text>
    
    <rect x="440" y="0" width="200" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="540" y="13" class="component-text" fill="white">会员用户</text>
    
    <rect x="660" y="0" width="200" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="760" y="13" class="component-text" fill="white">移动端用户</text>
    
    <rect x="880" y="0" width="200" height="25" fill="#0099CC" class="component-box" rx="5" ry="5"/>
    <text x="980" y="13" class="component-text" fill="white">PWA用户</text>
  </g>

  <!-- 前端展示层 - Vue3 -->
  <rect x="60" y="200" width="1080" height="160" fill="#F0F8FF" stroke="#4FC08D" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="225" class="section-title">前端展示层 - Vue 3 技术栈</text>
  
  <!-- Vue3 核心框架 -->
  <g transform="translate(80, 245)">
    <rect x="0" y="0" width="260" height="35" fill="#4FC08D" class="component-box" rx="5" ry="5"/>
    <text x="130" y="18" class="component-text" fill="white">Vue 3.3+ 框架</text>
    
    <rect x="280" y="0" width="260" height="35" fill="#3178C6" class="component-box" rx="5" ry="5"/>
    <text x="410" y="18" class="component-text" fill="white">TypeScript 5.0+</text>
    
    <rect x="560" y="0" width="260" height="35" fill="#646CFF" class="component-box" rx="5" ry="5"/>
    <text x="690" y="18" class="component-text" fill="white">Vite 4.0+ 构建</text>
    
    <rect x="840" y="0" width="240" height="35" fill="#FF6B6B" class="component-box" rx="5" ry="5"/>
    <text x="960" y="18" class="component-text" fill="white">PWA 支持</text>
  </g>
  
  <!-- Vue3 生态系统 -->
  <g transform="translate(80, 290)">
    <rect x="0" y="0" width="200" height="30" fill="#41B883" class="component-box" rx="5" ry="5"/>
    <text x="100" y="15" class="component-text" fill="white">Vue Router 4</text>
    
    <rect x="220" y="0" width="200" height="30" fill="#41B883" class="component-box" rx="5" ry="5"/>
    <text x="320" y="15" class="component-text" fill="white">Pinia 状态管理</text>
    
    <rect x="440" y="0" width="200" height="30" fill="#41B883" class="component-box" rx="5" ry="5"/>
    <text x="540" y="15" class="component-text" fill="white">Vue I18n 国际化</text>
    
    <rect x="660" y="0" width="200" height="30" fill="#41B883" class="component-box" rx="5" ry="5"/>
    <text x="760" y="15" class="component-text" fill="white">Element Plus UI</text>
    
    <rect x="880" y="0" width="200" height="30" fill="#41B883" class="component-box" rx="5" ry="5"/>
    <text x="980" y="15" class="component-text" fill="white">Axios HTTP</text>
  </g>
  
  <!-- Vue3 增强功能 -->
  <g transform="translate(80, 330)">
    <rect x="0" y="0" width="180" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white" font-size="11">组合式API</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white" font-size="11">响应式系统</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white" font-size="11">虚拟DOM</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white" font-size="11">Tree-shaking</text>
    
    <rect x="800" y="0" width="180" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="890" y="13" class="component-text" fill="white" font-size="11">懒加载</text>
    
    <rect x="1000" y="0" width="140" height="25" fill="#35495E" class="component-box" rx="5" ry="5"/>
    <text x="1070" y="13" class="component-text" fill="white" font-size="11">SSR支持</text>
  </g>

  <!-- API 接口层 -->
  <rect x="60" y="380" width="1080" height="100" fill="#E6FFE6" stroke="#00AA00" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="405" class="section-title">BWMS API 接口层</text>
  <g transform="translate(100, 425)">
    <rect x="0" y="0" width="200" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">RESTful API</text>
    
    <rect x="220" y="0" width="200" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="320" y="13" class="component-text" fill="white">内容管理 API</text>
    
    <rect x="440" y="0" width="200" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="540" y="13" class="component-text" fill="white">用户管理 API</text>
    
    <rect x="660" y="0" width="200" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="760" y="13" class="component-text" fill="white">媒体管理 API</text>
    
    <rect x="880" y="0" width="200" height="25" fill="#00AA00" class="component-box" rx="5" ry="5"/>
    <text x="980" y="13" class="component-text" fill="white">实时通讯 API</text>
  </g>
  
  <g transform="translate(100, 455)">
    <rect x="0" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="120" y="10" class="component-text" font-size="11">JWT 认证</text>
    
    <rect x="260" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="380" y="10" class="component-text" font-size="11">GraphQL 支持</text>
    
    <rect x="520" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="640" y="10" class="component-text" font-size="11">WebSocket</text>
    
    <rect x="780" y="0" width="240" height="20" fill="#99DD99" class="component-box" rx="3" ry="3"/>
    <text x="900" y="10" class="component-text" font-size="11">类型安全</text>
  </g>

  <!-- 后端服务层 -->
  <rect x="60" y="500" width="1080" height="100" fill="#FFE6E6" stroke="#FF6666" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="525" class="section-title">BWMS 后端服务层</text>
  <g transform="translate(100, 545)">
    <rect x="0" y="0" width="200" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">Laravel 框架</text>
    
    <rect x="220" y="0" width="200" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="320" y="13" class="component-text" fill="white">业务逻辑层</text>
    
    <rect x="440" y="0" width="200" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="540" y="13" class="component-text" fill="white">数据仓储层</text>
    
    <rect x="660" y="0" width="200" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="760" y="13" class="component-text" fill="white">实时服务</text>
    
    <rect x="880" y="0" width="200" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="980" y="13" class="component-text" fill="white">队列处理</text>
  </g>
  
  <g transform="translate(100, 575)">
    <rect x="0" y="0" width="360" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="180" y="10" class="component-text" font-size="11">Eloquent ORM + 关系映射</text>
    
    <rect x="380" y="0" width="360" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="560" y="10" class="component-text" font-size="11">Laravel Sanctum 认证</text>
    
    <rect x="760" y="0" width="320" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="920" y="10" class="component-text" font-size="11">Laravel Echo 实时</text>
  </g>

  <!-- 数据存储层 -->
  <rect x="60" y="620" width="1080" height="80" fill="#E6F0FF" stroke="#6699FF" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="645" class="section-title">数据存储层</text>
  <g transform="translate(120, 665)">
    <rect x="0" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">MySQL 数据库</text>
    
    <rect x="240" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="340" y="13" class="component-text" fill="white">Redis 缓存</text>
    
    <rect x="480" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="580" y="13" class="component-text" fill="white">Elasticsearch</text>
    
    <rect x="720" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="820" y="13" class="component-text" fill="white">CDN + OSS</text>
  </g>

  <!-- 技术特点说明 -->
  <rect x="60" y="720" width="1080" height="170" fill="#F8F9FA" stroke="#6C757D" stroke-width="2" rx="10" ry="10"/>
  <text x="600" y="745" class="section-title">Vue3 前台架构特点</text>
  
  <g transform="translate(100, 770)">
    <circle cx="10" cy="10" r="5" fill="#28A745"/>
    <text x="25" y="15" font-size="14" font-weight="bold">优势：</text>
    <text x="80" y="15" font-size="13">• 现代化开发体验，组合式API</text>
    <text x="280" y="15" font-size="13">• TypeScript类型安全</text>
    <text x="450" y="15" font-size="13">• 响应式数据绑定</text>
    <text x="620" y="15" font-size="13">• 组件化开发</text>
    <text x="750" y="15" font-size="13">• HMR热更新</text>
    
    <circle cx="10" cy="40" r="5" fill="#FFC107"/>
    <text x="25" y="45" font-size="14" font-weight="bold">适用：</text>
    <text x="80" y="45" font-size="13">• 复杂交互的前台应用</text>
    <text x="250" y="45" font-size="13">• SPA单页应用</text>
    <text x="380" y="45" font-size="13">• PWA渐进式应用</text>
    <text x="540" y="45" font-size="13">• 移动端混合应用</text>
    <text x="700" y="45" font-size="13">• 实时应用</text>
    
    <circle cx="10" cy="70" r="5" fill="#17A2B8"/>
    <text x="25" y="75" font-size="14" font-weight="bold">技术：</text>
    <text x="80" y="75" font-size="13">• Vue 3.3+ 组合式API</text>
    <text x="240" y="75" font-size="13">• TypeScript 类型检查</text>
    <text x="400" y="75" font-size="13">• Vite 极速构建</text>
    <text x="540" y="75" font-size="13">• Pinia 状态管理</text>
    <text x="680" y="75" font-size="13">• Element Plus UI</text>
    
    <circle cx="10" cy="100" r="5" fill="#6F42C1"/>
    <text x="25" y="105" font-size="14" font-weight="bold">特性：</text>
    <text x="80" y="105" font-size="13">• 虚拟DOM性能优化</text>
    <text x="240" y="105" font-size="13">• Tree-shaking减小包体积</text>
    <text x="420" y="105" font-size="13">• 代码分割懒加载</text>
    <text x="580" y="105" font-size="13">• PWA离线支持</text>
    <text x="740" y="105" font-size="13">• SSR服务端渲染</text>
    
    <circle cx="10" cy="130" r="5" fill="#DC3545"/>
    <text x="25" y="135" font-size="14" font-weight="bold">注意：</text>
    <text x="80" y="135" font-size="13">• 需要学习Vue3新特性和TypeScript</text>
    <text x="350" y="135" font-size="13">• 初始配置相对复杂</text>
    <text x="550" y="135" font-size="13">• SEO需要SSR支持</text>
  </g>

  <!-- 数据流箭头 -->
  <line x1="600" y1="180" x2="600" y2="200" class="arrow"/>
  <line x1="600" y1="360" x2="600" y2="380" class="arrow"/>
  <line x1="600" y1="480" x2="600" y2="500" class="arrow"/>
  <line x1="600" y1="600" x2="600" y2="620" class="arrow"/>
  
  <!-- Vue3 响应式数据流 -->
  <line x1="200" y1="290" x2="200" y2="425" class="data-flow"/>
  <text x="210" y="355" font-size="12" fill="#4FC08D">响应式请求</text>
  
  <line x1="1000" y1="425" x2="1000" y2="290" class="data-flow"/>
  <text x="900" y="355" font-size="12" fill="#4FC08D">实时数据绑定</text>
  
  <!-- 状态管理数据流 -->
  <line x1="400" y1="320" x2="500" y2="425" class="data-flow"/>
  <text x="430" y="375" font-size="11" fill="#4FC08D">Pinia Store</text>
</svg> 