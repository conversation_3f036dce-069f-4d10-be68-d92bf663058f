# 基础组件模板

## 概述

基础组件是前端开发中最基础的可复用组件。本文档提供了基础组件的标准模板和最佳实践。

## 组件类型

1. 按钮组件
```vue
<template>
  <button
    class="base-button"
    :class="[
      `base-button--${type}`,
      `base-button--${size}`,
      { 'is-disabled': disabled }
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <i v-if="loading" class="el-icon-loading" />
    <i v-if="icon && !loading" :class="icon" />
    <span v-if="$slots.default"><slot /></span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'large' | 'default' | 'small'
  icon?: string
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  loading: false,
  disabled: false
})

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.base-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid transparent;
  outline: none;
  padding: 8px 15px;
  font-size: 14px;
  border-radius: 4px;
  transition: .3s;
  
  &--primary {
    color: #fff;
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    
    &:hover {
      background-color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
  
  &--large {
    padding: 12px 19px;
    font-size: 16px;
  }
  
  &--small {
    padding: 5px 11px;
    font-size: 12px;
  }
  
  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
  
  [class^="el-icon-"] {
    margin-right: 5px;
  }
}
</style>
```

2. 输入框组件
```vue
<template>
  <div
    class="base-input"
    :class="[
      `base-input--${size}`,
      { 'is-disabled': disabled }
    ]"
  >
    <input
      ref="input"
      v-model="inputValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      class="base-input__inner"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    >
    <span
      v-if="clearable && inputValue"
      class="base-input__clear"
      @click="handleClear"
    >
      <i class="el-icon-close" />
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  modelValue?: string
  type?: 'text' | 'password' | 'number'
  size?: 'large' | 'default' | 'small'
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  maxlength?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  type: 'text',
  size: 'default',
  placeholder: '',
  disabled: false,
  clearable: false
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'input', value: string): void
  (e: 'change', value: string): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
  (e: 'clear'): void
}>()

const input = ref<HTMLInputElement>()
const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleInput = (event: Event) => {
  const value = (event.target as HTMLInputElement).value
  emit('input', value)
}

const handleChange = (event: Event) => {
  const value = (event.target as HTMLInputElement).value
  emit('change', value)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleClear = () => {
  inputValue.value = ''
  emit('clear')
  input.value?.focus()
}
</script>

<style lang="scss" scoped>
.base-input {
  position: relative;
  display: inline-flex;
  width: 100%;
  
  &__inner {
    width: 100%;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: border-color .2s;
    
    &:focus {
      outline: none;
      border-color: var(--el-color-primary);
    }
    
    &:disabled {
      background-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
  
  &__clear {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: #c0c4cc;
    cursor: pointer;
    
    &:hover {
      color: #909399;
    }
  }
  
  &--large {
    .base-input__inner {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
    }
  }
  
  &--small {
    .base-input__inner {
      height: 24px;
      line-height: 24px;
      font-size: 12px;
    }
  }
}
</style>
```

## 规范要求

1. 组件命名
   - 使用 PascalCase
   - 前缀统一
   - 语义化
   - 避免冲突

2. Props 定义
   - 类型声明
   - 默认值
   - 必填校验
   - 说明文档

3. 事件处理
   - 命名规范
   - 参数类型
   - 事件冒泡
   - 防抖节流

4. 样式规范
   - BEM 命名
   - 作用域隔离
   - 主题变量
   - 响应式

## 最佳实践

1. Props 类型定义
```typescript
interface Props {
  // 必填属性
  value: string | number
  // 可选属性
  type?: 'success' | 'warning' | 'error'
  // 带默认值的属性
  size?: 'large' | 'default' | 'small'
  // 布尔属性
  disabled?: boolean
  // 回调函数
  onChange?: (value: string) => void
}

// 默认值设置
const props = withDefaults(defineProps<Props>(), {
  type: 'success',
  size: 'default',
  disabled: false
})
```

2. 事件发射
```typescript
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'focus', event: FocusEvent): void
}>()

// 使用方式
const handleChange = (event: Event) => {
  const value = (event.target as HTMLInputElement).value
  emit('change', value)
}
```

3. 样式编写
```scss
.base-component {
  // 块
  &__header {
    // 元素
  }
  
  &__content {
    // 元素
  }
  
  &--primary {
    // 修饰符
  }
  
  &--large {
    // 修饰符
  }
  
  @media screen and (max-width: 768px) {
    // 响应式
  }
}
```

## 注意事项

1. 组件设计
   - 单一职责
   - 可复用性
   - 可扩展性
   - 可维护性

2. 性能优化
   - 计算属性缓存
   - 事件委托
   - 按需加载
   - 懒渲染

3. 代码质量
   - TypeScript 类型
   - 注释完整
   - 代码格式
   - 测试覆盖

4. 使用文档
   - 组件说明
   - 属性文档
   - 事件文档
   - 使用示例
