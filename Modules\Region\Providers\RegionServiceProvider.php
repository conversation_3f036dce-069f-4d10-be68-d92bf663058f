<?php

namespace Modules\Region\Providers;

use Bingo\Providers\BingoModuleServiceProvider;

class RegionServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
       // 加载语言文件
       $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Region'.DIRECTORY_SEPARATOR.'Lang';
       $this->loadTranslationsFrom($path, 'Region');

       // 注册导航
       $this->registerNavigation();
       
       // 加载视图
       $this->loadViewsFrom(dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Region'.DIRECTORY_SEPARATOR.'views', 'Region');
       
       // 注册模块权限
       $this->registerModulePermissions();
       
       // 注册小部件管理器
       $this->registerWidgetManager();
       
       // 注册API路由
       $this->registerApiRoutes();
    }
 
    protected function navigation(): array
    {
        return [];
    }

    /**
     * 获取模块名称
     * @return string
     */
    public function moduleName(): string
    {
        return 'Region';
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [];
    }

    protected function registerApiRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Api/route.php');
    }
} 