<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\BaseModel;

/**
 * 管理员笔名关联模型
 *
 * @property int $id 关联ID
 * @property int $admin_id 管理员ID
 * @property int $pen_name_id 笔名ID
 * @property bool $is_default 是否为默认笔名
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class PenNameRelation extends BaseModel
{
    use SoftDeletes;

    /**
     * 关联的表名
     * @var string
     */
    protected $table = 'admin_pen_names';

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段
     * @var array
     */
    protected $fillable = [
        'admin_id',
        'pen_name_id',
        'is_default',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_deleted',
    ];

    /**
     * 字段类型转换
     * @var array
     */
    protected $casts = [
        'admin_id' => 'integer',
        'pen_name_id' => 'integer',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联管理员
     * @return BelongsTo
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }

    /**
     * 关联笔名
     * @return BelongsTo
     */
    public function penName(): BelongsTo
    {
        return $this->belongsTo(PenName::class, 'pen_name_id', 'id');
    }
}
