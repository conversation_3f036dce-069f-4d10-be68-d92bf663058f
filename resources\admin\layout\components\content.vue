<template>
  <div :class="'w-full flex-1 overflow-auto transition-spacing duration-300 ease-linear ' + mlClass">
    <!-- Header -->
    <!-- <Header /> -->
    <!-- Tag view -->
    <!--<div class=""></div>-->
    <!-- Container -->
    <div class="flex flex-col min-h-full p-1 mx-auto sm:p-3 lg:container">
      <div class="flex-1">
          <router-view />
      </div>
      <div class="w-full text-center text-gray-400">
          <el-link href="https://hk-bingo.com/" target="_blank">{{ $t('system.name') }} </el-link> @copyright 2009 ~ {{ year }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { useAppStore } from '/admin/stores/modules/app'

const appStore = useAppStore()

const mlClass = computed(() => {
  return appStore.isExpand ? 'ml-0' : 'ml-0'
})

const year = computed(() => {
  const date = new Date()

  return date.getFullYear()
})
</script>
