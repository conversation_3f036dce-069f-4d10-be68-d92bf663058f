export const socialFlowTemplate = `<div data-bs-component="social-flow" class="social-flow-block social-flow-container">
  <div class="social-icons-wrapper">
    <div class="social-icons-row">
      <a href="#" class="social-icon facebook-icon" title="Facebook" target="_blank">
        <i class="fab fa-facebook-f"></i>
      </a>
      <a href="#" class="social-icon twitter-icon" title="Twitter" target="_blank">
        <i class="fab fa-twitter"></i>
      </a>
      <a href="#" class="social-icon instagram-icon" title="Instagram" target="_blank">
        <i class="fab fa-instagram"></i>
      </a>
      <a href="#" class="social-icon linkedin-icon" title="LinkedIn" target="_blank">
        <i class="fab fa-linkedin-in"></i>
      </a>
      <a href="#" class="social-icon youtube-icon" title="YouTube" target="_blank">
        <i class="fab fa-youtube"></i>
      </a>
    </div>
  </div>
<style>
.social-flow-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
  width: 100%;
}

.social-icons-wrapper {
  display: inline-flex;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.social-icons-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  width: 100%;
}

.social-icons-column {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
}

.social-icon.square {
  border-radius: 8px;
}

.icon-sm .social-icon {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.icon-lg .social-icon {
  width: 48px;
  height: 48px;
  font-size: 22px;
}

.facebook-icon {
  background-color: #1877F2;
}

.twitter-icon {
  background-color: #1DA1F2;
}

.instagram-icon {
  background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.linkedin-icon {
  background-color: #0A66C2;
}

.youtube-icon {
  background-color: #FF0000;
}

.social-icon:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0,0,0,0.2);
}

/* 移动端样式 */
@media (max-width: 767.98px) {
  .social-flow-container {
    padding: 10px;
  }

  .social-icons-row {
    gap: 10px;
  }

  .social-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

/* 平板端样式 */
@media (min-width: 768px) and (max-width: 991.98px) {
  .social-flow-container {
    padding: 12px;
  }

  .social-icons-row {
    gap: 12px;
  }

  .social-icon {
    width: 38px;
    height: 38px;
    font-size: 17px;
  }
}

/* 桌面端样式 */
@media (min-width: 992px) {
  .social-flow-container {
    padding: 15px;
  }

  .social-icons-row {
    gap: 15px;
  }

  .social-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

/* 移动端预览模式样式 */
.mobile-preview .social-flow-container {
  padding: 10px;
}

.mobile-preview .social-icons-row {
  gap: 10px;
}

.mobile-preview .social-icon {
  width: 36px;
  height: 36px;
  font-size: 16px;
}

/* 桌面预览模式样式 */
.desktop-preview .social-flow-container {
  padding: 15px;
}

.desktop-preview .social-icons-row {
  gap: 15px;
}

.desktop-preview .social-icon {
  width: 40px;
  height: 40px;
  font-size: 18px;
}

/* 容器大小响应式 */
.container-sm .social-flow-container {
  max-width: 540px;
}

.container-md .social-flow-container {
  max-width: 720px;
}

.container-lg .social-flow-container {
  max-width: 960px;
}

.container-xl .social-flow-container {
  max-width: 1140px;
}

.container-xxl .social-flow-container {
  max-width: 1320px;
}
</style>
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</div>`;
