(function(w,modal){w.FormWidget=w.EasyForms=modal()})(window,function(){var transitionEvent=whichTransitionEvent();function Modal(options){var defaults={autoOpen:false,closeButton:true,cssClass:[],closeLabel:"Close",closeMethods:["overlay","button","escape"],onClose:null,onOpen:null,beforeClose:null};this.options=extendDefaults({},defaults,options);this.init()}Modal.prototype.init=function(){if(this.modal){return}_build.call(this);_initializeEvents.call(this);document.body.insertBefore(this.modal,document.body.firstChild);if(this.options.autoOpen===true){this.open()}};Modal.prototype.destroy=function(){if(this.modal===null){return}_unbindEvents.call(this);this.modal.parentNode.removeChild(this.modal);this.modal=null};Modal.prototype.open=function(){if(this.modal.style.removeProperty){this.modal.style.removeProperty("display")}else{this.modal.style.removeAttribute("display")}document.body.classList.add("ef-enabled");this.modal.classList.add("ef-modal--open");var self=this;if(transitionEvent){this.modal.addEventListener(transitionEvent,function handler(){if(typeof self.options.onOpen==="function"){self.options.onOpen.call(self)}self.modal.removeEventListener(transitionEvent,handler,false)},false)}else{if(typeof self.options.onOpen==="function"){self.options.onOpen.call(self)}}this.checkOverflow()};Modal.prototype.isOpen=function(){return!!this.modal.classList.contains("ef-modal--open")};Modal.prototype.close=function(){if(typeof this.options.beforeClose==="function"){var close=this.options.beforeClose.call(this);if(!close)return}document.body.classList.remove("ef-enabled");this.modal.classList.remove("ef-modal--open");var self=this;if(transitionEvent){this.modal.addEventListener(transitionEvent,function handler(){self.modal.removeEventListener(transitionEvent,handler,false);if(typeof self.options.onClose==="function"){self.options.onClose.call(this)}},false)}else{if(typeof self.options.onClose==="function"){self.options.onClose.call(this)}}};Modal.prototype.setContent=function(content){if(typeof content==="string"){this.modalBoxContent.innerHTML=content}else{this.modalBoxContent.innerHTML="";this.modalBoxContent.appendChild(content)}};Modal.prototype.getContent=function(){return this.modalBoxContent};Modal.prototype.isOverflow=function(){var viewportHeight=window.innerHeight;var modalHeight=this.modalBox.clientHeight;return modalHeight>=viewportHeight};Modal.prototype.checkOverflow=function(){if(this.modal.classList.contains("ef-modal--open")){if(this.isOverflow()){this.modal.classList.add("ef-modal--overflow")}else{this.modal.classList.remove("ef-modal--overflow")}}};function _build(){this.modal=document.createElement("div");this.modal.classList.add("ef-modal");if(this.options.closeMethods.length===0||this.options.closeMethods.indexOf("overlay")===-1){this.modal.classList.add("ef-modal--noOverlayClose")}this.options.cssClass.forEach(function(item){if(typeof item==="string"){this.modal.classList.add(item)}},this);if(this.options.closeMethods.indexOf("button")!==-1){this.modalCloseBtn=document.createElement("button");this.modalCloseBtn.classList.add("ef-modal__close");this.modalCloseBtnIcon=document.createElement("span");this.modalCloseBtnIcon.classList.add("ef-modal__closeIcon");this.modalCloseBtnIcon.innerHTML="×";this.modalCloseBtnLabel=document.createElement("span");this.modalCloseBtnLabel.classList.add("ef-modal__closeLabel");this.modalCloseBtnLabel.innerHTML=this.options.closeLabel;this.modalCloseBtn.appendChild(this.modalCloseBtnIcon);this.modalCloseBtn.appendChild(this.modalCloseBtnLabel)}this.modalBox=document.createElement("div");this.modalBox.classList.add("ef-modal-box");this.modalBoxContent=document.createElement("div");this.modalBoxContent.classList.add("ef-modal-box__content");this.modalBox.appendChild(this.modalBoxContent);if(this.options.closeMethods.indexOf("button")!==-1){this.modal.appendChild(this.modalCloseBtn)}this.modal.appendChild(this.modalBox)}function _initializeEvents(){this._events={clickCloseBtn:this.close.bind(this),clickOverlay:_handleClickOutside.bind(this),resize:this.checkOverflow.bind(this),keyboardNav:_handleKeyboardNav.bind(this)};if(this.options.closeMethods.indexOf("button")!==-1){this.modalCloseBtn.addEventListener("click",this._events.clickCloseBtn)}this.modal.addEventListener("mousedown",this._events.clickOverlay);window.addEventListener("resize",this._events.resize);document.addEventListener("keydown",this._events.keyboardNav)}function _handleKeyboardNav(event){if(this.options.closeMethods.indexOf("escape")!==-1&&event.which===27&&this.isOpen()){this.close()}}function _handleClickOutside(event){if(this.options.closeMethods.indexOf("overlay")!==-1&&!_findAncestor(event.target,"ef-modal")&&event.clientX<this.modal.clientWidth){this.close()}}function _findAncestor(el,cls){while((el=el.parentElement)&&!el.classList.contains(cls)){}return el}function _unbindEvents(){if(this.options.closeMethods.indexOf("button")!==-1){this.modalCloseBtn.removeEventListener("click",this._events.clickCloseBtn)}this.modal.removeEventListener("mousedown",this._events.clickOverlay);window.removeEventListener("resize",this._events.resize);document.removeEventListener("keydown",this._events.keyboardNav)}function extendDefaults(){for(var i=1;i<arguments.length;i++){for(var key in arguments[i]){if(arguments[i].hasOwnProperty(key)){arguments[0][key]=arguments[i][key]}}}return arguments[0]}function whichTransitionEvent(){var t;var el=document.createElement("ef-transition");var transitions={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(t in transitions){if(el.style[t]!==undefined){return transitions[t]}}}return{Modal:Modal}});