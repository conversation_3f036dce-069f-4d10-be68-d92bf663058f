<template>
  <div class="piece-box activity-log-box">
    <div class="piece-tit flex justify-between items-center">
      <div class="tit">String&Traffic</div>
    </div>
    <div class="piece-con">
      <div class="progress-box">
        <div class="progress-tit">
          <span>Disk Space</span>
          <span>10GB</span>
        </div>
        <div class="progress-con">
          <el-icon size="20"><MessageBox /></el-icon>
          <el-progress :percentage="10" color="#FFD100" :show-text="false" :stroke-width="15" />
        </div>
        <div class="progress-info">
          <span>0.4MB(10%)</span>
          <span>10GB</span>
        </div>
      </div>
      <div class="progress-box">
        <div class="progress-tit">
          <span>Hosting Stats</span>
          <span>100GB/month</span>
        </div>
        <div class="progress-con">
          <el-icon size="20"><Odometer /></el-icon>
          <el-progress :percentage="10" color="#FFD100" :show-text="false" :stroke-width="15" />
        </div>
        <div class="progress-info">
          <span>0.4MB(10%)</span>
          <span>100GB/month</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.activity-log-box {
  .piece-con {
    border-radius: var(--el-table-border-radius);
    padding: 10px;
    background-color: #fff;
    color: #050505;

    .progress-box {
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;

      &:last-child {
        margin-bottom: 0;
      }

      .progress-tit,
      .progress-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .progress-tit {
        margin-bottom: 10px;
        font-size: 0.75rem;
        font-weight: bold;
      }

      .progress-con {
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 10px;
          flex-shrink: 0;
        }

        .el-progress {
          flex-grow: 1;
          height: 20px;
        }
      }
      
      .progress-info {
        margin-top: 10px;
        color: #B1B1B1;
        font-size: 0.625rem;
      }
    }
  }
}
</style>