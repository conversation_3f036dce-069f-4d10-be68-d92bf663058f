/**
 * 手风琴模板
 * 增加媒体查询相关的响应式类，适配不同设备显示效果
 */

export const accordionTemplate = `
<div data-bs-component="accordion" class="accordion responsive-block" id="accordionExample">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <!-- 手风琴组件 - 添加响应式样式 -->
        <div class="accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
              <span class="d-block d-md-inline">手风琴项目 #1</span>
            </button>
          </h2>
          <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
            <div class="accordion-body">
              <div class="d-flex flex-column flex-md-row align-items-md-center">
                <div class="mb-3 mb-md-0">
                  这是第一个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。
                </div>
                <!-- 可选的媒体内容区域 -->
                <div class="d-none d-md-block ms-md-3">
                  <!-- 在平板和桌面端显示的媒体内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
              <span class="d-block d-md-inline">手风琴项目 #2</span>
            </button>
          </h2>
          <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
            <div class="accordion-body">
              <div class="d-flex flex-column flex-md-row align-items-md-center">
                <div class="mb-3 mb-md-0">
                  这是第二个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。
                </div>
                <!-- 可选的媒体内容区域 -->
                <div class="d-none d-md-block ms-md-3">
                  <!-- 在平板和桌面端显示的媒体内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 移动端特定样式 -->
        <div class="mt-3 text-center d-md-none">
          <small class="text-muted">在小屏幕上以垂直方式显示内容</small>
        </div>
        
      
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

export default accordionTemplate; 