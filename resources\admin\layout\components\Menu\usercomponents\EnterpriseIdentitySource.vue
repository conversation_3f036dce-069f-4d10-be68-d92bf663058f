<template>
  <div class="bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box">
        <div class="identity-source">
         
          <el-table 
            :data="enterpriseSources" 
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column 
              prop="name" 
              :label="t('dashboard.enterpriseIdentity.table.account')" 
              width="180"
            >
              <template #default="scope">
                <div class="source-info">
                  <img :src="scope.row.logo" :alt="scope.row.name" class="source-icon" />
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column 
              prop="status" 
              :label="t('dashboard.enterpriseIdentity.table.status')" 
              width="180"
            >
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                  {{ scope.row.status === 1 ? 
                    t('dashboard.enterpriseIdentity.table.statusTags.normal') : 
                    t('dashboard.enterpriseIdentity.table.statusTags.abnormal') 
                  }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column 
              :label="t('dashboard.enterpriseIdentity.table.boundAccount')" 
              width="180"
            >
              <template #default="scope">
                <span v-if="hasUserInfo(scope.row)">
                  {{ scope.row.user_info[0].value }}
                </span>
                <span v-else>{{ t('dashboard.enterpriseIdentity.status.notBound') }}</span>
              </template>
            </el-table-column>
            
            <el-table-column :label="t('dashboard.enterpriseIdentity.table.actions')">
              <template #default="scope">
                <el-button 
                  :type="hasUserInfo(scope.row) ? 'danger' : 'primary'" 
                  link
                  @click="hasUserInfo(scope.row) ? viewDetails(scope.row) : syncNow(scope.row)"
                >
                  {{ hasUserInfo(scope.row) ? 
                    t('dashboard.enterpriseIdentity.actions.unbind') : 
                    t('dashboard.enterpriseIdentity.actions.bind') 
                  }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

const { t } = useI18n()

interface EnterpriseSource {
  type: string
  name: string
  desc: string
  logo: string
  'bind-url': string
  status: number
  user_info: {
    name: string
    value: string
  }[] | null
}

const enterpriseSources = ref<EnterpriseSource[]>([])
const loading = ref(false)

// 添加判断用户信息是否存在的工具函数
const hasUserInfo = (source: EnterpriseSource): boolean => {
  return !!source.user_info && Array.isArray(source.user_info) && source.user_info.length > 0
}

const getEnterpriseSources = async () => {
  loading.value = true
  try {
    const response = await http.get('/iam/identity/bindlist?type=enterprise')
    // 确保 user_info 为数组
    enterpriseSources.value = (response.data.data || []).map((item: EnterpriseSource) => ({
      ...item,
      user_info: Array.isArray(item.user_info) ? item.user_info : []
    }))
  } catch (error) {
    console.error('Failed to fetch enterprise sources:', error)
    // Mock data for demonstration
    enterpriseSources.value = [
      {
        type: 'windows-ad',
        name: 'Windows AD',
        desc: 'Windows AD 是 Microsoft 提供的本地化用户目录管理服务。',
        logo: 'http://127.0.0.1:8000/Vendor/iam/Asset/social-connections/windows-active-directory2.svg',
        'bind-url': 'https://127.0.0.1:8000/account/windowsad/bind?next_url=https://127.0.0.1:8001/console/third',
        status: 1,
        user_info: [{ name: '企业账号', value: '<EMAIL>' }],
      },
      {
        type: 'oidc',
        name: 'OIDC',
        desc: 'OIDC 是一个基于 OAuth2 协议的身份认证标准协议。',
        logo: 'http://127.0.0.1:8000/Vendor/iam/Asset/social-connections/oidc_logo2.svg',
        'bind-url': 'https://127.0.0.1:8000/account/oidc/bind?next_url=https://127.0.0.1:8001/console/third',
        status: 1,
        user_info: [],
      },
    ]
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getEnterpriseSources()
})

const syncNow = (source: EnterpriseSource) => {
  ElMessage.success(t('dashboard.enterpriseIdentity.messages.syncSuccess', { name: source.name }))
  // 这里添加同步逻辑
}

const viewDetails = (source: EnterpriseSource) => {
  ElMessage.info(t('dashboard.enterpriseIdentity.messages.viewDetails', { name: source.name }))
  // 这里添加查看详情的逻辑
}
</script>

<style lang="scss" scoped>
.identity-source {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
  }
}

.source-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.source-icon {
  width: 24px;
  height: 24px;
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      margin: 0;
    }
  }
}
</style>
