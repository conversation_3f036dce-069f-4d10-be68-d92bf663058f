<template>
  <div class="common-layout">
    <el-container class="h-screen">
      <el-header>
        <Headers />
      </el-header>
      <el-container class="overflow-hidden">
        <el-aside width="264px" class="h-full">
          <Menus />
        </el-aside>
        <el-main style="justify-content: space-between;">
          <Breadcrumbs v-if="!shouldHideBreadcrumbs" />
          <router-view></router-view>
          <el-footer> All rights reserved © 2009 - {{ currentYear }} Bingo(HK). </el-footer>
        </el-main>
      </el-container>

    </el-container>
  </div>
</template>

<script lang="ts" setup>
import Menus from './components/Menu/menus.vue'
import Headers from './components/header/index.vue'
import Breadcrumbs from '/admin/components/breadcrumbs/index.vue'
import { useRoute } from 'vue-router'
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouterTitle } from '/admin/composables/useRouterTitle'

const route = useRoute()
const isHome = computed(() => route.path === '/dashboard')
const currentYear = computed(() => new Date().getFullYear())

// 不显示面包屑的路由列表 - 可根据需要添加更多路由
const hideBreadcrumbsRoutes = [
  '/dashboard',
  '/form/statistics',
  "/form/theme",
  "/auditlog/dashboard",
  "/auditlog/logs",
  "/auditlog/reports",
  "/course/list",
  "/course/create",
  "/course/edit",
  "/abtest/list",
  "/abtest/settings",
  "/activity/calendar",
  // 审批相关路由 - 添加完整的审批路由路径
  "/approval/records/:id/approval",
 
]

// 计算是否应该隐藏面包屑
const shouldHideBreadcrumbs = computed(() => {
  // 增强判断逻辑，支持动态路由参数
  const currentPath = route.path
  return hideBreadcrumbsRoutes.some(path => {
    // 处理包含参数的路径匹配
    if (path.includes(':')) {
      // 将路由模式转换为正则表达式
      const regexPath = path.replace(/:\w+/g, '[^/]+')
      const regex = new RegExp(`^${regexPath}$`)
      return regex.test(currentPath)
    }
    // 精确匹配
    return path === currentPath
  })
})

// 🔥 启用路由标题更新 - 在组件挂载后立即处理
const { pageTitle, updateDocumentTitle } = useRouterTitle()

// 在组件挂载时立即更新标题
onMounted(() => {
  nextTick(() => {
    updateDocumentTitle()
  })
})
</script>

<style lang="scss">
body {
  font-family: 'Inter', 'Microsoft JhengHei';
}
// html[lang='zh_CN'] body {
//   font-family: 'Microsoft JhengHei';
// }
// html[lang='en'] body {
//   font-family: 'Inter';
// }

.el-header {
  --el-header-height: 80px;
  --el-header-padding: 16px 30px 16px 20px;
  box-shadow: 0px 3px 6px #0000004d;
  position: relative;
  z-index: 999;
}

.el-aside,
.el-main {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgb(0, 126, 229, 0.3);
    border-radius: 5px;
    background-clip: content-box;
  }
}

.el-aside {
  box-shadow: 0px 3px 6px #00000029;
  padding: 1px 0;
}

.el-main {
  --el-main-padding: 0;
  background-color: #f4f7f9;

  display: flex;
  flex-direction: column;
}

.el-footer {
  --el-footer-height: 50px;

  background-color: #19496a;
  font-size: 14px;
  color: #fff;
  line-height: 1;

  display: flex;
  align-items: center;
  justify-content: center;
}

.bwms-module {
  margin-top: -30px;
  padding: 0 34px 20px;
  flex-grow: 1;
  overflow: hidden;

  display: flex;
  flex-direction: column;

  .module-tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22px;

    h2 {
      color: #19496A;
      font-size: 24px;
      font-weight: bold;
    }

    .btn-list {
      display: flex;
      align-items: center;

      .bwms-btn {
        padding: 12px 20px;
        display: flex;
        --el-button-border-color: #86C0E9;
        --el-button-text-color: #37A0EA;
        --el-border: 2px solid var(--el-button-border-color);
        --el-button-bg-color: #F4F9FD;

        img {
          margin-right: 10px;
          width: 26px;
        }

        .el-icon {
          margin-left: 42px;
        }
      }
    }
  }

  .module-header {
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    min-height: 32px;

    .btn-list {
      display: flex;
    }
  }

  .module-con {
    flex-grow: 1;
    overflow: hidden;

    display: flex;
    flex-direction: column;

    .box {
      border-radius: 10px;
      padding: 5px 20px 15px;
      background-color: #fff;
      flex-grow: 1;
      overflow: hidden;

      &.scroll-box {
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }

        &::-webkit-scrollbar-button {
          display: none;
        }

        &::-webkit-scrollbar-corner {
          background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgb(0, 126, 229, 0.3);
          border-radius: 5px;
          background-clip: content-box;
        }
      }

      .el-tabs {
        display: flex;
        height: 100%;

        .el-tabs__header {
          flex-shrink: 0;
        }

        .el-tabs__content {
          flex-grow: 1;
          overflow-x: hidden;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
          }

          &::-webkit-scrollbar-button {
            display: none;
          }

          &::-webkit-scrollbar-corner {
            background: #f1f1f1;
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgb(0, 126, 229, 0.3);
            border-radius: 5px;
            background-clip: content-box;
          }

          .el-tab-pane {
            height: 100%;
          }
        }
      }
    }

    .box-footer {
      margin-top: 20px;
      flex-shrink: 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
