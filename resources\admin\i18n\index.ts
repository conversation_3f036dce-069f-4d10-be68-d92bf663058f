import Cache from '/admin/support/cache'
import { createI18n } from 'vue-i18n'
import en from './languages/en'
import zh_CN from './languages/zh_CN'
import type { App } from 'vue'
import zh_HK from '/admin/i18n/languages/zh_HK'

// 动态导入模块的语言文件
const modules = import.meta.glob('@/module/*/Lang/*/common.json') as Record<string, () => Promise<{ default: Record<string, any> }>>

const loadLocaleMessages = (): Record<string, any> => {
  const messages: Record<string, any> = {
    en: { ...en },
    zh_CN: { ...zh_CN },
    zh_HK: { ...zh_HK },
  }

  for (const path in modules) {
    try {
      const langFileModule = modules[path]
      langFileModule()
        .then(langFile => {
          const parts = path.split('/')
          const lang = parts[parts.length - 2] // 获取语言代码，如 en, zh_CN, zh_HK

          if (!messages[lang]) {
            messages[lang] = {}
          }

          const moduleName = parts[2] // 获取模块名称，如 Cms, Blog, Banner
          if (!messages[lang][moduleName]) {
            messages[lang][moduleName] = {}
          }

          messages[lang][moduleName] = {
            ...(messages[lang][moduleName] || {}),
            ...langFile.default,
          }
        })
        .catch(error => {
          console.warn(`Failed to load language file at ${path}:`, error)
          // 忽略错误并继续加载其他语言文件
        })
    } catch (error) {
      console.warn(`Failed to load language file at ${path}:`, error)
      // 忽略错误并继续加载其他语言文件
    }
  }

  return messages
}

const i18n = createI18n({
  locale: Cache.get('language') || 'zh_HK',
  globalInjection: true,
  legacy: false, // 确保不使用 legacy 模式
})

export function bootstrapI18n(app: App): void {
  const messages = loadLocaleMessages()
  for (const [lang, message] of Object.entries(messages)) {
    i18n.global.setLocaleMessage(lang, message)
  }

  app.use(i18n)
}

export default i18n
