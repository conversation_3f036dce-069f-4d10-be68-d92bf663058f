.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;
}
.bwms-page .form-register {
  margin: 60px auto;
  box-shadow: 0 0 10px #eee;
  border-radius: 5px;
  border: 1px solid #cecece;
  padding: 30px 50px;
  background-color: #fff;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .form-register .logo {
  margin-bottom: 30px;
  width: 32%;
}
.bwms-page .form-register .tabs {
  width: 100%;
}
.bwms-page .form-register .tabs .tab-list {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .form-register .tabs .tab-list .tab-item {
  margin: 0 20px;
  padding: 6px 0;
  color: #333;
  font-size: 24px;
  position: relative;
  cursor: pointer;
}
.bwms-page .form-register .tabs .tab-list .tab-item::before {
  border-radius: 2px;
  content: '';
  width: 0;
  height: 3px;
  transition: height 0.3s;
  background-color: #ff9600;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}
.bwms-page .form-register .tabs .tab-list .tab-item.active {
  color: #ff9600;
}
.bwms-page .form-register .tabs .tab-list .tab-item.active::before {
  width: 30%;
}
.bwms-page .form-register .tabs .pane-list {
  width: 100%;
  position: relative;
}
.bwms-page .form-register .tabs .pane-list .tab-pane {
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  opacity: 0;
}
.bwms-page .form-register .tabs .pane-list .tab-pane.active {
  position: static;
  opacity: 1;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .inp-box {
  margin-bottom: 16px;
  border-radius: 0px;
  border: 1px solid #eaeaea;
  padding: 6px 14px;
  background-color: #f7f7f7;
  font-size: 18px;
  line-height: 2.55;
  width: 100%;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-bottom: 16px;
  border: 1px solid #eaeaea;
  background-color: #f7f7f7;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp .inp-box {
  margin-bottom: 0;
  border: none;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp button {
  margin-bottom: 0;
  border-radius: 4px;
  padding: 6px 14px;
  color: #fff;
  background-color: #999;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp button .iconfont + span {
  margin-left: 5px;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp button span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp button span {
  display: block;
  line-height: 1.5;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .has-btn-inp button:hover {
  background-color: #999;
  color: #fff;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .check-box {
  margin-bottom: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .check-box label {
  margin-left: 5px;
  font-size: 14px;
  color: #999;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .check-box label a {
  font-size: 14px;
  color: #999;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button {
  margin-bottom: 16px;
  border-radius: 0;
  padding: 22px 22px;
  color: #fff;
  background-color: #ff9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button .iconfont + span {
  margin-left: 5px;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button span {
  display: block;
  line-height: 1.5;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button:hover {
  background-color: #ffbe99;
  color: #fff;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form button[disabled] {
  color: #ffbe99;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .existing {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.bwms-page .form-register .tabs .pane-list .tab-pane form .existing a {
  margin-left: 10px;
  display: block;
  font-size: 14px;
  color: #999;
}
