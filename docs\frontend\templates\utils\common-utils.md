# 通用工具函数模板

## 概述

本文档提供了前端开发中常用的工具函数模板和最佳实践。

## 基本结构

```typescript
// src/utils/common.ts

/**
 * 日期时间格式化
 * @param date 日期对象或时间戳
 * @param format 格式化模板
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | number | string, format = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('HH', hour.toString().padStart(2, '0'))
    .replace('mm', minute.toString().padStart(2, '0'))
    .replace('ss', second.toString().padStart(2, '0'))
}

/**
 * 金额格式化
 * @param amount 金额
 * @param decimals 小数位数
 * @param separator 千分位分隔符
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number, decimals = 2, separator = ','): string {
  const parts = amount.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  return parts.join('.')
}

/**
 * 文件大小格式化
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
}

/**
 * 防抖函数
 * @param fn 需要防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null
  
  return function(this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param fn 需要节流的函数
 * @param limit 时间限制
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return function(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      fn.apply(this, args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * 深拷贝
 * @param obj 需要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as any
  }
  
  if (obj instanceof Object) {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [key, deepClone(value)])
    ) as any
  }
  
  return obj
}

/**
 * URL参数解析
 * @param url URL字符串
 * @returns 解析后的参数对象
 */
export function parseUrlParams(url: string): Record<string, string> {
  const params: Record<string, string> = {}
  const search = url.split('?')[1]
  
  if (!search) return params
  
  const searchParams = new URLSearchParams(search)
  for (const [key, value] of searchParams) {
    params[key] = value
  }
  
  return params
}

/**
 * 对象转URL参数
 * @param params 参数对象
 * @returns URL参数字符串
 */
export function objectToUrlParams(params: Record<string, any>): string {
  return Object.entries(params)
    .filter(([_, value]) => value !== null && value !== undefined)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&')
}

/**
 * 生成UUID
 * @returns UUID字符串
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 树形数据转换
 * @param data 原始数据
 * @param id ID字段名
 * @param parentId 父ID字段名
 * @returns 树形结构数据
 */
export function arrayToTree<T extends Record<string, any>>(
  data: T[],
  id = 'id',
  parentId = 'parentId'
): T[] {
  const result: T[] = []
  const map = new Map<any, T>()
  
  // 构建映射关系
  data.forEach(item => {
    map.set(item[id], { ...item, children: [] })
  })
  
  // 构建树形结构
  data.forEach(item => {
    const node = map.get(item[id])
    if (item[parentId] === null || item[parentId] === undefined) {
      result.push(node!)
    } else {
      const parent = map.get(item[parentId])
      parent?.children.push(node!)
    }
  })
  
  return result
}

/**
 * 树形数据扁平化
 * @param tree 树形数据
 * @returns 扁平化后的数组
 */
export function treeToArray<T extends { children?: T[] }>(tree: T[]): T[] {
  return tree.reduce<T[]>((result, node) => {
    const { children, ...item } = node
    return result.concat(
      [item as T],
      children ? treeToArray(children) : []
    )
  }, [])
}
```

## 使用示例

1. 日期格式化
```typescript
const date = new Date()
console.log(formatDate(date)) // 2023-12-26 10:30:00
console.log(formatDate(date, 'YYYY年MM月DD日')) // 2023年12月26日
```

2. 金额格式化
```typescript
console.log(formatAmount(1234567.89)) // 1,234,567.89
console.log(formatAmount(1234.5, 0)) // 1,235
```

3. 防抖函数
```typescript
const handleSearch = debounce((keyword: string) => {
  console.log('搜索关键字:', keyword)
}, 300)

// 在输入框onChange事件中使用
<el-input @input="handleSearch" />
```

4. 节流函数
```typescript
const handleScroll = throttle(() => {
  console.log('滚动事件')
}, 200)

// 在滚动事件中使用
window.addEventListener('scroll', handleScroll)
```

5. 树形数据转换
```typescript
const data = [
  { id: 1, name: '部门1', parentId: null },
  { id: 2, name: '部门2', parentId: 1 },
  { id: 3, name: '部门3', parentId: 1 }
]

const tree = arrayToTree(data)
console.log(tree)
/*
[
  {
    id: 1,
    name: '部门1',
    parentId: null,
    children: [
      { id: 2, name: '部门2', parentId: 1, children: [] },
      { id: 3, name: '部门3', parentId: 1, children: [] }
    ]
  }
]
*/
```

## 注意事项

1. 类型安全
   - 使用TypeScript类型声明
   - 避免any类型
   - 处理边界情况
   - 函数参数校验

2. 性能优化
   - 避免重复计算
   - 合理使用缓存
   - 减少循环嵌套
   - 优化大数据处理

3. 代码规范
   - 函数职责单一
   - 参数命名语义化
   - 返回值类型明确
   - 注释完整清晰

4. 最佳实践
   - 错误处理完善
   - 参数默认值
   - 函数组合复用
   - 保持代码简洁

5. 测试建议
   - 单元测试覆盖
   - 边界条件测试
   - 性能测试
   - 文档示例
