<?php

namespace Modules\Common\Provider\SmsTemplate;

use Closure;

/**
 * Class SmsTemplateProvider
 * @package Modules\Common\Provider\SmsTemplate
 */
class SmsTemplateProvider
{
    /**
     * @var AbstractSmsTemplateProvider[]
     */
    private static array $instances = [
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractSmsTemplateProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function map(): array
    {
        $map = [];
        foreach (self::all() as $item) {
            $map[$item->name()] = [
                'name' => $item->name(),
                'title' => $item->title(),
                'description' => $item->description(),
                'example' => $item->example(),
            ];
        }
        return $map;
    }
}
