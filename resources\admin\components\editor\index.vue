<template>
  <div class="edit-box">
    <component :is="RequiredComponent" v-if="RequiredComponent" :modelValue="content" width="100%" height="500px" placeholder="请输入" @update:upEditContent="upEditContent"/>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef } from 'vue'
import http from '/admin/support/http'

interface ModuleItem {
  label: string
  value: string
}

const { content } = defineProps({
  content: {
    type: String,
    default: '',
    require: true,
  },
})

// 已安装的模块列表
let moduleInstall = ref<ModuleItem[]>([])
const RequiredComponent = shallowRef(null as any)

const emit = defineEmits(['update:upEditContent']);
function upEditContent (val:string) {
  emit('update:upEditContent', val);
}

onMounted(async () => {
  const res = await http.get(`options/modules`)
  moduleInstall.value = res.data.data

  try {
    // TODO: 暂定 CFM 关键字, 后续更改
    if (moduleInstall.value.find(item => item.value === 'Editor') !== undefined) {
      RequiredComponent.value = (await import('@/module/Editor/views/components/n1ed_editor.vue')).default
    } else {
      RequiredComponent.value = (await import('@/module/Editor/views/components/tinyMce.vue')).default
    }
    
  } catch (error) {
    console.error('Error loading component:', error)
  }
})
</script>

<style scoped>
.edit-box {
  width: 100%;
}
</style>
