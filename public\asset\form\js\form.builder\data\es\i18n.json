{"phrases": {"form.name": "Nombre del Formulario", "form.description": "Ayuda a identificar el formulario en las páginas de administración.", "form.layout": "Layout del Formulario", "form.disabled": "Desactivar elementos del formulario", "form.sourceCode": "Previsualizar código fuente", "formSteps.title": "Páginas del Formulario", "formSteps.id": "ID / Nombre", "formSteps.steps": "Pasos", "formSteps.progressBar": "Barra de progreso", "formSteps.noTitles": "Sin títulos", "formSteps.noStages": "Sin etapas", "formSteps.noSteps": "Sin contexto", "popover.save": "Guardar", "popover.delete": "Eliminar", "popover.cancel": "<PERSON><PERSON><PERSON>", "popover.more": "Más opciones", "tab.fields": "Campos", "tab.settings": "Configuración", "tab.code": "Código", "alert.warning": "Advertencia!", "alert.errorSavingData": "Ocurrió un error al guardar el formulario. Por favor, inténtelo después.", "alert.unsavedChanges": "SU FORMULARIO TIENE CAMBIOS QUE NO HAN SIDO GUARDADOS! TODOS LOS CAMBIOS SE PERDERÁN!", "widget.button": "Enviar", "widget.checkbox": "Checkbox", "widget.date": "<PERSON><PERSON>", "widget.email": "Email", "widget.file": "Subir archivo", "widget.heading": "Encabezado", "widget.hidden": "Campo oculto", "widget.number": "Número", "widget.pageBreak": "Ruptura", "widget.paragraph": "<PERSON><PERSON><PERSON><PERSON>", "widget.radio": "Radio Button", "widget.recaptcha": "reCaptcha", "widget.selectList": "Lista", "widget.snippet": "Snippet", "widget.text": "Texto", "widget.textArea": "Area de texto", "heading.title": "Encabezado", "paragraph.title": "<PERSON><PERSON><PERSON><PERSON>", "text.title": "Texto", "number.title": "Número", "date.title": "<PERSON><PERSON>", "email.title": "Email", "textarea.title": "Area de Texto", "checkbox.title": "Checkbox", "radio.title": "Radio", "selectlist.title": "Lista de Selección", "hidden.title": "Campo Oculto", "file.title": "Subir archivo", "snippet.title": "Snippet", "recaptcha.title": "reCAPTCHA", "pagebreak.title": "Page Break", "button.title": "<PERSON>ón", "component.id": "ID / Nombre", "component.text": "Texto", "component.inputType": "Tipo de Input", "component.type": "Tipo", "component.size": "<PERSON><PERSON><PERSON>", "component.label": "Etiqueta", "component.placeholder": "Placeholder", "component.required": "Obligatorio", "component.predefinedValue": "Valor predefinido", "component.helpText": "Texto de ayuda", "component.fieldSize": "Tamaño del campo", "component.groupName": "Nombre del grupo", "component.checkboxes": "Checkboxes", "component.radios": "Radios", "component.options": "Opciones", "component.value": "Valor", "component.accept": "Aceptar", "component.pattern": "<PERSON><PERSON><PERSON>", "component.integerPattern": "Patrón de enteros", "component.numberPattern": "Patrón de números", "component.prev": "Texto del botón Previo", "component.next": "Texto del botón Next", "component.buttonText": "Texto del botón", "component.src": "<PERSON><PERSON> de la imagen", "component.inline": "En line", "component.unique": "Único", "component.readOnly": "Sólo lectura", "component.integerOnly": "<PERSON><PERSON><PERSON>", "component.minNumber": "<PERSON>ú<PERSON><PERSON>", "component.maxNumber": "Número <PERSON>", "component.stepNumber": "Número de Step", "component.minDate": "<PERSON><PERSON>", "component.maxDate": "<PERSON><PERSON>", "component.minSize": "<PERSON><PERSON><PERSON>", "component.maxSize": "<PERSON><PERSON><PERSON> m<PERSON>", "component.htmlCode": "Código HTML", "component.theme": "<PERSON><PERSON>", "component.checkDNS": "Check DNS", "component.multiple": "<PERSON><PERSON><PERSON><PERSON>", "component.disabled": "Desactivado", "component.cssClass": "Clase CSS", "component.labelClass": "Clase CSS de la etiqueta", "component.containerClass": "Clase CSS del contenedor"}}