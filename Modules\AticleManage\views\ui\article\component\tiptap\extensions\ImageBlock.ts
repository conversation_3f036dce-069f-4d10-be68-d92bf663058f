import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

export interface ImageBlockOptions {
  HTMLAttributes: Record<string, any>
  assetFunction?: (path: string) => string
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    imageBlock: {
      /**
       * 设置图片块
       */
      setImageBlock: (attributes?: { 
        src?: string
        alt?: string
        rounded?: string
        effect?: string
        width?: string
      }) => ReturnType,
      /**
       * 更新图片块HTML
       */
      updateImageBlockHTML: (html: string) => ReturnType
    }
  }
}

export const ImageBlock = Node.create<ImageBlockOptions>({
  name: 'imageBlock',

  group: 'block',

  content: 'inline*',

  draggable: true,

  isolating: true,

  addOptions() {
    return {
      HTMLAttributes: {},
      assetFunction: (path: string) => path,
    }
  },

  addAttributes() {
    return {
      rawHTML: {
        default: '',
        parseHTML: element => {
          const html = element.getAttribute('data-raw-html') || element.outerHTML
          return html
        },
        renderHTML: attributes => {
          if (!attributes.rawHTML) {
            return {}
          }

          return {
            'data-raw-html': attributes.rawHTML,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="image"]',
        getAttrs: element => {
          if (typeof element === 'string') return {}
          
          const imgElement = element.querySelector('img')
          if (imgElement) {
            const src = imgElement.getAttribute('src')
            if (src && src.includes('${$asset(')) {
              const path = src.match(/\$asset\('([^']+)'\)/)?.[1]
              if (path) {
                imgElement.setAttribute('src', this.options.assetFunction(path))
              }
            }
          }
          
          return {
            rawHTML: element.outerHTML,
          }
        },
      },
      {
        tag: 'div[data-bs-component="image-group"]',
        getAttrs: element => {
          if (typeof element === 'string') return {}
          
          const imgElements = element.querySelectorAll('img')
          imgElements.forEach(img => {
            const src = img.getAttribute('src')
            if (src && src.includes('${$asset(')) {
              const path = src.match(/\$asset\('([^']+)'\)/)?.[1]
              if (path) {
                img.setAttribute('src', this.options.assetFunction(path))
              }
            }
          })
          
          return {
            rawHTML: element.outerHTML,
          }
        },
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    if (HTMLAttributes.rawHTML) {
      const template = document.createElement('template')
      let html = HTMLAttributes.rawHTML

      const assetRegex = /\$\{\\*\$asset\('([^']+)'\)\}/g
      html = html.replace(assetRegex, (match, path) => {
        return this.options.assetFunction(path)
      })

      template.innerHTML = html
      const element = template.content.firstElementChild

      if (element) {
        element.querySelectorAll('img').forEach(img => {
          const src = img.getAttribute('src')
          if (src && src.includes('${$asset(')) {
            const path = src.match(/\$asset\('([^']+)'\)/)?.[1]
            if (path) {
              img.setAttribute('src', this.options.assetFunction(path))
            }
          }
        })

        return element
      }
    }

    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addCommands() {
    return {
      setImageBlock:
        attributes =>
        ({ commands }) => {
          return commands.setNode(this.name, attributes)
        },
      updateImageBlockHTML:
        html =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, {
            rawHTML: html
          })
        }
    }
  }
}) 