import {defineStore} from "pinia";
import {computed, reactive, ref, getCurrentInstance} from "vue";
import {useGetPageJson} from "/admin/utils/api";
import Cache from '/admin/support/cache'

interface pageList {
    [key: string]: any
}


export const usePagesStore = defineStore("pages", () => {

    let pages: pageList = {}

    const thisPage = reactive({
        loading: true,
        error: false,
        errorMessage: "",
        pageJson: null as any,
        showMenu: true,
        showHeader: true,
    })


    const internalInstance = getCurrentInstance();


    const getPageJson = async (path: string) => {
        try {
            if (internalInstance && internalInstance.appContext.config.globalProperties.$Progress) {
                internalInstance.appContext.config.globalProperties.$Progress.start();
            }
            thisPage.loading = true
            // 获取当前语言偏好
            const locale = Cache.get('language') || 'zh_HK';
            // 获取当前站点信息
            const site = Cache.get('site');
            const siteId = site ? JSON.parse(site).id : null;

            // 检查URL中是否已经包含了查询参数
            const hasQuery = path.includes("?");

            // 构建查询参数
            const queryParams = new URLSearchParams();
            queryParams.append('locale', locale);
            if (siteId) {
                queryParams.append('site_id', siteId.toString());
            }

            // 构建完整路径
            const fullPath = hasQuery 
                ? `${path}&${queryParams.toString()}`
                : `${path}?${queryParams.toString()}`;

            const res = await useGetPageJson(fullPath) // http.get(path)

            if (res.action) {
                if (res.action == 'jump' && res.url) {
                    window.location.href = res.url
                }
                if (res.action == "renderPage") {
                    thisPage.showHeader = res.showHeader ?? true
                    thisPage.showMenu = res.showMenu ?? true
                }
            } else {
                thisPage.showHeader = true
                thisPage.showMenu = true
            }
            pages[path] = res.data
            thisPage.pageJson = pages[path]
            thisPage.error = false
            thisPage.loading = false
            if (internalInstance && internalInstance.appContext.config.globalProperties.$Progress) {
                internalInstance.appContext.config.globalProperties.$Progress.finish();
            }
        } catch (e) {
            console.log(e)
            thisPage.error = true
            thisPage.loading = false
            // @ts-ignore
            thisPage.errorMessage = e.message
            if (internalInstance && internalInstance.appContext.config.globalProperties.$Progress) {
                internalInstance.appContext.config.globalProperties.$Progress.fail();
            }
        }
    }

    return {
        thisPage,
        getPageJson
    }

})
