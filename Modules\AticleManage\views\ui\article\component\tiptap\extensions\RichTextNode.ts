import { Node, mergeAttributes } from '@tiptap/core'

export const RichTextNode = Node.create({
  name: 'richtext',
  
  group: 'block',
  
  content: 'inline*',
  
  // 启用拖拽
  draggable: true,
  
  // 保持内容隔离，避免编辑器的默认行为影响内容
  isolating: true,
  
  // 定义HTML解析规则
  parseHTML() {
    return [
      // 匹配带有内联样式的任何元素
      {
        tag: 'h1[style], h2[style], h3[style], h4[style], h5[style], h6[style], p[style], div[style], span[style], ul[style], ol[style], li[style], blockquote[style]',
      },
      // 匹配特定的样式类，如果有的话
      {
        tag: '.rich-content, .styled-content'
      },
      {
        tag: 'div.rich-text-node',
      },
      {
        tag: 'div[data-bs-component="a-class"]',
      }
    ]
  },
  
  // 定义HTML渲染规则
  renderHTML({ HTMLAttributes, node }) {
    // 保留所有原始属性，确保内联样式被保留
    return ['div', mergeAttributes(HTMLAttributes, { 'data-rich-content': 'true' }), 0]
  },
  
  // 添加特定的属性处理
  addAttributes() {
    // 返回通用的样式属性，允许它们被保留
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  }
}) 