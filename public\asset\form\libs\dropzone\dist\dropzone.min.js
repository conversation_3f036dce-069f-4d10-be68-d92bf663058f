var $6mU8w$swchelpers=require("@swc/helpers");var $6mU8w$justextend=require("just-extend");function $parcel$interopDefault(a){return a&&a.__esModule?a.default:a}function $parcel$defineInteropFlag(a){Object.defineProperty(a,"__esModule",{value:true,configurable:true})}function $parcel$export(e,n,v,s){Object.defineProperty(e,n,{get:v,set:s,enumerable:true,configurable:true})}$parcel$defineInteropFlag(module.exports);$parcel$export(module.exports,"default",function(){return $a601ff30f483e917$export$2e2bcd8739ae039});$parcel$export(module.exports,"Dropzone",function(){return $a601ff30f483e917$export$2e2bcd8739ae039});var $b1d17cfb1d15c36a$export$2e2bcd8739ae039=function(){"use strict";function $b1d17cfb1d15c36a$export$2e2bcd8739ae039(){$6mU8w$swchelpers.classCallCheck(this,$b1d17cfb1d15c36a$export$2e2bcd8739ae039)}$6mU8w$swchelpers.createClass($b1d17cfb1d15c36a$export$2e2bcd8739ae039,[{key:"on",value:function on(event,fn){this._callbacks=this._callbacks||{};if(!this._callbacks[event])this._callbacks[event]=[];this._callbacks[event].push(fn);return this}},{key:"emit",value:function emit(event){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){args[_key-1]=arguments[_key]}this._callbacks=this._callbacks||{};var callbacks=this._callbacks[event];var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(callbacks)try{for(var _iterator=callbacks[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var callback=_step.value;callback.apply(this,args)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.element)this.element.dispatchEvent(this.makeEvent("dropzone:"+event,{args:args}));return this}},{key:"makeEvent",value:function makeEvent(eventName,detail){var params={bubbles:true,cancelable:true,detail:detail};if(typeof window.CustomEvent==="function")return new CustomEvent(eventName,params);else{var evt=document.createEvent("CustomEvent");evt.initCustomEvent(eventName,params.bubbles,params.cancelable,params.detail);return evt}}},{key:"off",value:function off(event,fn){if(!this._callbacks||arguments.length===0){this._callbacks={};return this}var callbacks=this._callbacks[event];if(!callbacks)return this;if(arguments.length===1){delete this._callbacks[event];return this}for(var i=0;i<callbacks.length;i++){var callback=callbacks[i];if(callback===fn){callbacks.splice(i,1);break}}return this}}]);return $b1d17cfb1d15c36a$export$2e2bcd8739ae039}();var $69c61888cc1f4c57$exports={};$69c61888cc1f4c57$exports='<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail=""></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size=""></span></div>\n    <div class="dz-filename"><span data-dz-name=""></span></div>\n  </div>\n  <div class="dz-progress">\n    <span class="dz-upload" data-dz-uploadprogress=""></span>\n  </div>\n  <div class="dz-error-message"><span data-dz-errormessage=""></span></div>\n  <div class="dz-success-mark">\n    <svg width="54" height="54" viewBox="0 0 54 54" fill="white" xmlns="http://www.w3.org/2000/svg">\n      <path d="M10.2071 29.7929L14.2929 25.7071C14.6834 25.3166 15.3166 25.3166 15.7071 25.7071L21.2929 31.2929C21.6834 31.6834 22.3166 31.6834 22.7071 31.2929L38.2929 15.7071C38.6834 15.3166 39.3166 15.3166 39.7071 15.7071L43.7929 19.7929C44.1834 20.1834 44.1834 20.8166 43.7929 21.2071L22.7071 42.2929C22.3166 42.6834 21.6834 42.6834 21.2929 42.2929L10.2071 31.2071C9.81658 30.8166 9.81658 30.1834 10.2071 29.7929Z"></path>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54" height="54" viewBox="0 0 54 54" fill="white" xmlns="http://www.w3.org/2000/svg">\n      <path d="M26.2929 20.2929L19.2071 13.2071C18.8166 12.8166 18.1834 12.8166 17.7929 13.2071L13.2071 17.7929C12.8166 18.1834 12.8166 18.8166 13.2071 19.2071L20.2929 26.2929C20.6834 26.6834 20.6834 27.3166 20.2929 27.7071L13.2071 34.7929C12.8166 35.1834 12.8166 35.8166 13.2071 36.2071L17.7929 40.7929C18.1834 41.1834 18.8166 41.1834 19.2071 40.7929L26.2929 33.7071C26.6834 33.3166 27.3166 33.3166 27.7071 33.7071L34.7929 40.7929C35.1834 41.1834 35.8166 41.1834 36.2071 40.7929L40.7929 36.2071C41.1834 35.8166 41.1834 35.1834 40.7929 34.7929L33.7071 27.7071C33.3166 27.3166 33.3166 26.6834 33.7071 26.2929L40.7929 19.2071C41.1834 18.8166 41.1834 18.1834 40.7929 17.7929L36.2071 13.2071C35.8166 12.8166 35.1834 12.8166 34.7929 13.2071L27.7071 20.2929C27.3166 20.6834 26.6834 20.6834 26.2929 20.2929Z"></path>\n    </svg>\n  </div>\n</div>\n';var $b657c03155fc27e2$var$defaultOptions={url:null,method:"post",withCredentials:false,timeout:null,parallelUploads:2,uploadMultiple:false,chunking:false,forceChunking:false,chunkSize:2097152,parallelChunkUploads:false,retryChunks:false,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:true,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,defaultHeaders:true,clickable:true,ignoreHiddenFiles:true,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:true,autoQueue:true,addRemoveLinks:false,previewsContainer:null,disablePreviews:false,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:false,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(files,xhr,chunk){if(chunk)return{dzuuid:chunk.file.upload.uuid,dzchunkindex:chunk.index,dztotalfilesize:chunk.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:chunk.file.upload.totalChunkCount,dzchunkbyteoffset:chunk.index*this.options.chunkSize}},accept:function(file,done){return done()},chunksUploaded:function chunksUploaded(file,done){done()},binaryBody:false,fallback:function(){var messageElement;this.element.className="".concat(this.element.className," dz-browser-not-supported");var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=this.element.getElementsByTagName("div")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var child=_step.value;if(/(^| )dz-message($| )/.test(child.className)){messageElement=child;child.className="dz-message";break}}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(!messageElement){messageElement=$a601ff30f483e917$export$2e2bcd8739ae039.createElement('<div class="dz-message"><span></span></div>');this.element.appendChild(messageElement)}var span=messageElement.getElementsByTagName("span")[0];if(span){if(span.textContent!=null)span.textContent=this.options.dictFallbackMessage;else if(span.innerText!=null)span.innerText=this.options.dictFallbackMessage}return this.element.appendChild(this.getFallbackForm())},resize:function(file,width,height,resizeMethod){var info={srcX:0,srcY:0,srcWidth:file.width,srcHeight:file.height};var srcRatio=file.width/file.height;if(width==null&&height==null){width=info.srcWidth;height=info.srcHeight}else if(width==null)width=height*srcRatio;else if(height==null)height=width/srcRatio;width=Math.min(width,info.srcWidth);height=Math.min(height,info.srcHeight);var trgRatio=width/height;if(info.srcWidth>width||info.srcHeight>height){if(resizeMethod==="crop"){if(srcRatio>trgRatio){info.srcHeight=file.height;info.srcWidth=info.srcHeight*trgRatio}else{info.srcWidth=file.width;info.srcHeight=info.srcWidth/trgRatio}}else if(resizeMethod==="contain"){if(srcRatio>trgRatio)height=width/srcRatio;else width=height*srcRatio}else throw new Error("Unknown resizeMethod '".concat(resizeMethod,"'"))}info.srcX=(file.width-info.srcWidth)/2;info.srcY=(file.height-info.srcHeight)/2;info.trgWidth=width;info.trgHeight=height;return info},transformFile:function(file,done){if((this.options.resizeWidth||this.options.resizeHeight)&&file.type.match(/image.*/))return this.resizeImage(file,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,done);else return done(file)},previewTemplate:$parcel$interopDefault($69c61888cc1f4c57$exports),drop:function(e){return this.element.classList.remove("dz-drag-hover")},dragstart:function(e){},dragend:function(e){return this.element.classList.remove("dz-drag-hover")},dragenter:function(e){return this.element.classList.add("dz-drag-hover")},dragover:function(e){return this.element.classList.add("dz-drag-hover")},dragleave:function(e){return this.element.classList.remove("dz-drag-hover")},paste:function(e){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(file){if(this.element===this.previewsContainer)this.element.classList.add("dz-started");if(this.previewsContainer&&!this.options.disablePreviews){var _this=this;file.previewElement=$a601ff30f483e917$export$2e2bcd8739ae039.createElement(this.options.previewTemplate.trim());file.previewTemplate=file.previewElement;this.previewsContainer.appendChild(file.previewElement);var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=file.previewElement.querySelectorAll("[data-dz-name]")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var node=_step.value;node.textContent=file.name}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}var _iteratorNormalCompletion1=true,_didIteratorError1=false,_iteratorError1=undefined;try{for(var _iterator1=file.previewElement.querySelectorAll("[data-dz-size]")[Symbol.iterator](),_step1;!(_iteratorNormalCompletion1=(_step1=_iterator1.next()).done);_iteratorNormalCompletion1=true){node=_step1.value;node.innerHTML=this.filesize(file.size)}}catch(err){_didIteratorError1=true;_iteratorError1=err}finally{try{if(!_iteratorNormalCompletion1&&_iterator1.return!=null){_iterator1.return()}}finally{if(_didIteratorError1){throw _iteratorError1}}}if(this.options.addRemoveLinks){file._removeLink=$a601ff30f483e917$export$2e2bcd8739ae039.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>"));file.previewElement.appendChild(file._removeLink)}var removeFileEvent=function(e){var _this1=_this;e.preventDefault();e.stopPropagation();if(file.status===$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING)return $a601ff30f483e917$export$2e2bcd8739ae039.confirm(_this.options.dictCancelUploadConfirmation,function(){return _this1.removeFile(file)});else{var _this2=_this;if(_this.options.dictRemoveFileConfirmation)return $a601ff30f483e917$export$2e2bcd8739ae039.confirm(_this.options.dictRemoveFileConfirmation,function(){return _this2.removeFile(file)});else return _this.removeFile(file)}};var _iteratorNormalCompletion2=true,_didIteratorError2=false,_iteratorError2=undefined;try{for(var _iterator2=file.previewElement.querySelectorAll("[data-dz-remove]")[Symbol.iterator](),_step2;!(_iteratorNormalCompletion2=(_step2=_iterator2.next()).done);_iteratorNormalCompletion2=true){var removeLink=_step2.value;removeLink.addEventListener("click",removeFileEvent)}}catch(err){_didIteratorError2=true;_iteratorError2=err}finally{try{if(!_iteratorNormalCompletion2&&_iterator2.return!=null){_iterator2.return()}}finally{if(_didIteratorError2){throw _iteratorError2}}}}},removedfile:function(file){if(file.previewElement!=null&&file.previewElement.parentNode!=null)file.previewElement.parentNode.removeChild(file.previewElement);return this._updateMaxFilesReachedClass()},thumbnail:function(file,dataUrl){if(file.previewElement){file.previewElement.classList.remove("dz-file-preview");var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=file.previewElement.querySelectorAll("[data-dz-thumbnail]")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var thumbnailElement=_step.value;thumbnailElement.alt=file.name;thumbnailElement.src=dataUrl}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return setTimeout(function(){return file.previewElement.classList.add("dz-image-preview")},1)}},error:function(file,message){if(file.previewElement){file.previewElement.classList.add("dz-error");if(typeof message!=="string"&&message.error)message=message.error;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=file.previewElement.querySelectorAll("[data-dz-errormessage]")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var node=_step.value;node.textContent=message}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}},errormultiple:function(){},processing:function(file){if(file.previewElement){file.previewElement.classList.add("dz-processing");if(file._removeLink)return file._removeLink.innerHTML=this.options.dictCancelUpload}},processingmultiple:function(){},uploadprogress:function(file,progress,bytesSent){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(file.previewElement)try{for(var _iterator=file.previewElement.querySelectorAll("[data-dz-uploadprogress]")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var node=_step.value;node.nodeName==="PROGRESS"?node.value=progress:node.style.width="".concat(progress,"%")}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(file){if(file.previewElement)return file.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(file){return this.emit("error",file,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(file){if(file._removeLink)file._removeLink.innerHTML=this.options.dictRemoveFile;if(file.previewElement)return file.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}};var $b657c03155fc27e2$export$2e2bcd8739ae039=$b657c03155fc27e2$var$defaultOptions;var $a601ff30f483e917$export$2e2bcd8739ae039=function(Emitter){"use strict";$6mU8w$swchelpers.inherits($a601ff30f483e917$export$2e2bcd8739ae039,Emitter);function $a601ff30f483e917$export$2e2bcd8739ae039(el,options){$6mU8w$swchelpers.classCallCheck(this,$a601ff30f483e917$export$2e2bcd8739ae039);var _this;_this=$6mU8w$swchelpers.possibleConstructorReturn(this,$6mU8w$swchelpers.getPrototypeOf($a601ff30f483e917$export$2e2bcd8739ae039).call(this));var fallback,left;_this.element=el;_this.clickableElements=[];_this.listeners=[];_this.files=[];if(typeof _this.element==="string")_this.element=document.querySelector(_this.element);if(!_this.element||_this.element.nodeType==null)throw new Error("Invalid dropzone element.");if(_this.element.dropzone)throw new Error("Dropzone already attached.");$a601ff30f483e917$export$2e2bcd8739ae039.instances.push($6mU8w$swchelpers.assertThisInitialized(_this));_this.element.dropzone=$6mU8w$swchelpers.assertThisInitialized(_this);var elementOptions=(left=$a601ff30f483e917$export$2e2bcd8739ae039.optionsForElement(_this.element))!=null?left:{};_this.options=$parcel$interopDefault($6mU8w$justextend)(true,{},$b657c03155fc27e2$export$2e2bcd8739ae039,elementOptions,options!=null?options:{});_this.options.previewTemplate=_this.options.previewTemplate.replace(/\n*/g,"");if(_this.options.forceFallback||!$a601ff30f483e917$export$2e2bcd8739ae039.isBrowserSupported())return $6mU8w$swchelpers.possibleConstructorReturn(_this,_this.options.fallback.call($6mU8w$swchelpers.assertThisInitialized(_this)));if(_this.options.url==null)_this.options.url=_this.element.getAttribute("action");if(!_this.options.url)throw new Error("No URL provided.");if(_this.options.acceptedFiles&&_this.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(_this.options.uploadMultiple&&_this.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");if(_this.options.binaryBody&&_this.options.uploadMultiple)throw new Error("You cannot set both: binaryBody and uploadMultiple.");if(_this.options.acceptedMimeTypes){_this.options.acceptedFiles=_this.options.acceptedMimeTypes;delete _this.options.acceptedMimeTypes}if(_this.options.renameFilename!=null)_this.options.renameFile=function(file){return _this.options.renameFilename.call($6mU8w$swchelpers.assertThisInitialized(_this),file.name,file)};if(typeof _this.options.method==="string")_this.options.method=_this.options.method.toUpperCase();if((fallback=_this.getExistingFallback())&&fallback.parentNode)fallback.parentNode.removeChild(fallback);if(_this.options.previewsContainer!==false){if(_this.options.previewsContainer)_this.previewsContainer=$a601ff30f483e917$export$2e2bcd8739ae039.getElement(_this.options.previewsContainer,"previewsContainer");else _this.previewsContainer=_this.element}if(_this.options.clickable){if(_this.options.clickable===true)_this.clickableElements=[_this.element];else _this.clickableElements=$a601ff30f483e917$export$2e2bcd8739ae039.getElements(_this.options.clickable,"clickable")}_this.init();return _this}$6mU8w$swchelpers.createClass($a601ff30f483e917$export$2e2bcd8739ae039,[{key:"getAcceptedFiles",value:function getAcceptedFiles(){return this.files.filter(function(file){return file.accepted}).map(function(file){return file})}},{key:"getRejectedFiles",value:function getRejectedFiles(){return this.files.filter(function(file){return!file.accepted}).map(function(file){return file})}},{key:"getFilesWithStatus",value:function getFilesWithStatus(status){return this.files.filter(function(file){return file.status===status}).map(function(file){return file})}},{key:"getQueuedFiles",value:function getQueuedFiles(){return this.getFilesWithStatus($a601ff30f483e917$export$2e2bcd8739ae039.QUEUED)}},{key:"getUploadingFiles",value:function getUploadingFiles(){return this.getFilesWithStatus($a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING)}},{key:"getAddedFiles",value:function getAddedFiles(){return this.getFilesWithStatus($a601ff30f483e917$export$2e2bcd8739ae039.ADDED)}},{key:"getActiveFiles",value:function getActiveFiles(){return this.files.filter(function(file){return file.status===$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING||file.status===$a601ff30f483e917$export$2e2bcd8739ae039.QUEUED}).map(function(file){return file})}},{key:"init",value:function init(){var _this12=this,_this1=this,_this2=this,_this3=this,_this4=this,_this5=this,_this6=this,_this7=this,_this8=this,_this9=this,_this10=this;if(this.element.tagName==="form")this.element.setAttribute("enctype","multipart/form-data");if(this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message"))this.element.appendChild($a601ff30f483e917$export$2e2bcd8739ae039.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>")));if(this.clickableElements.length){var _this=this;var setupHiddenFileInput=function(){var _this11=_this;if(_this.hiddenFileInput)_this.hiddenFileInput.parentNode.removeChild(_this.hiddenFileInput);_this.hiddenFileInput=document.createElement("input");_this.hiddenFileInput.setAttribute("type","file");if(_this.options.maxFiles===null||_this.options.maxFiles>1)_this.hiddenFileInput.setAttribute("multiple","multiple");_this.hiddenFileInput.className="dz-hidden-input";if(_this.options.acceptedFiles!==null)_this.hiddenFileInput.setAttribute("accept",_this.options.acceptedFiles);if(_this.options.capture!==null)_this.hiddenFileInput.setAttribute("capture",_this.options.capture);_this.hiddenFileInput.setAttribute("tabindex","-1");_this.hiddenFileInput.style.visibility="hidden";_this.hiddenFileInput.style.position="absolute";_this.hiddenFileInput.style.top="0";_this.hiddenFileInput.style.left="0";_this.hiddenFileInput.style.height="0";_this.hiddenFileInput.style.width="0";$a601ff30f483e917$export$2e2bcd8739ae039.getElement(_this.options.hiddenInputContainer,"hiddenInputContainer").appendChild(_this.hiddenFileInput);_this.hiddenFileInput.addEventListener("change",function(){var _hiddenFileInput=_this11.hiddenFileInput,files=_hiddenFileInput.files;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(files.length)try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;_this11.addFile(file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}_this11.emit("addedfiles",files);setupHiddenFileInput()})};setupHiddenFileInput()}this.URL=window.URL!==null?window.URL:window.webkitURL;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=this.events[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var eventName=_step.value;this.on(eventName,this.options[eventName])}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}this.on("uploadprogress",function(){return _this12.updateTotalUploadProgress()});this.on("removedfile",function(){return _this1.updateTotalUploadProgress()});this.on("canceled",function(file){return _this2.emit("complete",file)});this.on("complete",function(file){var _this11=_this3;if(_this3.getAddedFiles().length===0&&_this3.getUploadingFiles().length===0&&_this3.getQueuedFiles().length===0)return setTimeout(function(){return _this11.emit("queuecomplete")},0)});var containsFiles=function containsFiles(e){if(e.dataTransfer.types)for(var i=0;i<e.dataTransfer.types.length;i++){if(e.dataTransfer.types[i]==="Files")return true}return false};var noPropagation=function noPropagation(e){if(!containsFiles(e))return;e.stopPropagation();if(e.preventDefault)return e.preventDefault();else return e.returnValue=false};this.listeners=[{element:this.element,events:{dragstart:function(e){return _this4.emit("dragstart",e)},dragenter:function(e){noPropagation(e);return _this5.emit("dragenter",e)},dragover:function(e){var efct;try{efct=e.dataTransfer.effectAllowed}catch(error){}e.dataTransfer.dropEffect="move"===efct||"linkMove"===efct?"move":"copy";noPropagation(e);return _this6.emit("dragover",e)},dragleave:function(e){return _this7.emit("dragleave",e)},drop:function(e){noPropagation(e);return _this8.drop(e)},dragend:function(e){return _this9.emit("dragend",e)}}}];this.clickableElements.forEach(function(clickableElement){var _this11=_this10;return _this10.listeners.push({element:clickableElement,events:{click:function(evt){if(clickableElement!==_this11.element||evt.target===_this11.element||$a601ff30f483e917$export$2e2bcd8739ae039.elementInside(evt.target,_this11.element.querySelector(".dz-message")))_this11.hiddenFileInput.click();return true}}})});this.enable();return this.options.init.call(this)}},{key:"destroy",value:function destroy(){this.disable();this.removeAllFiles(true);if(this.hiddenFileInput!=null?this.hiddenFileInput.parentNode:undefined){this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);this.hiddenFileInput=null}delete this.element.dropzone;return $a601ff30f483e917$export$2e2bcd8739ae039.instances.splice($a601ff30f483e917$export$2e2bcd8739ae039.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function updateTotalUploadProgress(){var totalUploadProgress;var totalBytesSent=0;var totalBytes=0;var activeFiles=this.getActiveFiles();if(activeFiles.length){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=this.getActiveFiles()[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;totalBytesSent+=file.upload.bytesSent;totalBytes+=file.upload.total}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}totalUploadProgress=100*totalBytesSent/totalBytes}else totalUploadProgress=100;return this.emit("totaluploadprogress",totalUploadProgress,totalBytes,totalBytesSent)}},{key:"_getParamName",value:function _getParamName(n){if(typeof this.options.paramName==="function")return this.options.paramName(n);else return"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(n,"]"):"")}},{key:"_renameFile",value:function _renameFile(file){if(typeof this.options.renameFile!=="function")return file.name;return this.options.renameFile(file)}},{key:"getFallbackForm",value:function getFallbackForm(){var existingFallback,form;if(existingFallback=this.getExistingFallback())return existingFallback;var fieldsString='<div class="dz-fallback">';if(this.options.dictFallbackText)fieldsString+="<p>".concat(this.options.dictFallbackText,"</p>");fieldsString+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':undefined,' /><input type="submit" value="Upload!"></div>');var fields=$a601ff30f483e917$export$2e2bcd8739ae039.createElement(fieldsString);if(this.element.tagName!=="FORM"){form=$a601ff30f483e917$export$2e2bcd8739ae039.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>'));form.appendChild(fields)}else{this.element.setAttribute("enctype","multipart/form-data");this.element.setAttribute("method",this.options.method)}return form!=null?form:fields}},{key:"getExistingFallback",value:function getExistingFallback(){var getFallback=function getFallback(elements){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=elements[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var el=_step.value;if(/(^| )fallback($| )/.test(el.className))return el}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}};var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=["div","form"][Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var tagName=_step.value;var fallback;if(fallback=getFallback(this.element.getElementsByTagName(tagName)))return fallback}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}},{key:"setupEventListeners",value:function setupEventListeners(){return this.listeners.map(function(elementListeners){return function(){var result=[];for(var event in elementListeners.events){var listener=elementListeners.events[event];result.push(elementListeners.element.addEventListener(event,listener,false))}return result}()})}},{key:"removeEventListeners",value:function removeEventListeners(){return this.listeners.map(function(elementListeners){return function(){var result=[];for(var event in elementListeners.events){var listener=elementListeners.events[event];result.push(elementListeners.element.removeEventListener(event,listener,false))}return result}()})}},{key:"disable",value:function disable(){var _this=this;this.clickableElements.forEach(function(element){return element.classList.remove("dz-clickable")});this.removeEventListeners();this.disabled=true;return this.files.map(function(file){return _this.cancelUpload(file)})}},{key:"enable",value:function enable(){delete this.disabled;this.clickableElements.forEach(function(element){return element.classList.add("dz-clickable")});return this.setupEventListeners()}},{key:"filesize",value:function filesize(size){var selectedSize=0;var selectedUnit="b";if(size>0){var units=["tb","gb","mb","kb","b"];for(var i=0;i<units.length;i++){var unit=units[i];var cutoff=Math.pow(this.options.filesizeBase,4-i)/10;if(size>=cutoff){selectedSize=size/Math.pow(this.options.filesizeBase,4-i);selectedUnit=unit;break}}selectedSize=Math.round(10*selectedSize)/10}return"<strong>".concat(selectedSize,"</strong> ").concat(this.options.dictFileSizeUnits[selectedUnit])}},{key:"_updateMaxFilesReachedClass",value:function _updateMaxFilesReachedClass(){if(this.options.maxFiles!=null&&this.getAcceptedFiles().length>=this.options.maxFiles){if(this.getAcceptedFiles().length===this.options.maxFiles)this.emit("maxfilesreached",this.files);return this.element.classList.add("dz-max-files-reached")}else return this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function drop(e){if(!e.dataTransfer)return;this.emit("drop",e);var files=[];for(var i=0;i<e.dataTransfer.files.length;i++)files[i]=e.dataTransfer.files[i];if(files.length){var _dataTransfer=e.dataTransfer,items=_dataTransfer.items;if(items&&items.length&&items[0].webkitGetAsEntry!=null)this._addFilesFromItems(items);else this.handleFiles(files)}this.emit("addedfiles",files)}},{key:"paste",value:function paste(e){if($a601ff30f483e917$var$__guard__(e!=null?e.clipboardData:undefined,function(x){return x.items})==null)return;this.emit("paste",e);var _clipboardData=e.clipboardData,items=_clipboardData.items;if(items.length)return this._addFilesFromItems(items)}},{key:"handleFiles",value:function handleFiles(files){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;this.addFile(file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}},{key:"_addFilesFromItems",value:function _addFilesFromItems(items){var _this=this;return function(){var result=[];var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=items[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var item=_step.value;var entry;if(item.webkitGetAsEntry!=null&&(entry=item.webkitGetAsEntry())){if(entry.isFile)result.push(_this.addFile(item.getAsFile()));else if(entry.isDirectory)result.push(_this._addFilesFromDirectory(entry,entry.name));else result.push(undefined)}else if(item.getAsFile!=null){if(item.kind==null||item.kind==="file")result.push(_this.addFile(item.getAsFile()));else result.push(undefined)}else result.push(undefined)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return result}()}},{key:"_addFilesFromDirectory",value:function _addFilesFromDirectory(directory,path){var _this=this;var dirReader=directory.createReader();var errorHandler=function(error){return $a601ff30f483e917$var$__guardMethod__(console,"log",function(o){return o.log(error)})};var readEntries=function(){var _this1=_this;return dirReader.readEntries(function(entries){if(entries.length>0){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=entries[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var entry=_step.value;var _this2=_this1;if(entry.isFile)entry.file(function(file){if(_this2.options.ignoreHiddenFiles&&file.name.substring(0,1)===".")return;file.fullPath="".concat(path,"/").concat(file.name);return _this2.addFile(file)});else if(entry.isDirectory)_this1._addFilesFromDirectory(entry,"".concat(path,"/").concat(entry.name))}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}readEntries()}return null},errorHandler)};return readEntries()}},{key:"accept",value:function accept(file,done){if(this.options.maxFilesize&&file.size>this.options.maxFilesize*1048576)done(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(file.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize));else if(!$a601ff30f483e917$export$2e2bcd8739ae039.isValidFile(file,this.options.acceptedFiles))done(this.options.dictInvalidFileType);else if(this.options.maxFiles!=null&&this.getAcceptedFiles().length>=this.options.maxFiles){done(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles));this.emit("maxfilesexceeded",file)}else this.options.accept.call(this,file,done)}},{key:"addFile",value:function addFile(file){var _this=this;file.upload={uuid:$a601ff30f483e917$export$2e2bcd8739ae039.uuidv4(),progress:0,total:file.size,bytesSent:0,filename:this._renameFile(file)};this.files.push(file);file.status=$a601ff30f483e917$export$2e2bcd8739ae039.ADDED;this.emit("addedfile",file);this._enqueueThumbnail(file);this.accept(file,function(error){if(error){file.accepted=false;_this._errorProcessing([file],error)}else{file.accepted=true;if(_this.options.autoQueue)_this.enqueueFile(file)}_this._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function enqueueFiles(files){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;this.enqueueFile(file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return null}},{key:"enqueueFile",value:function enqueueFile(file){if(file.status===$a601ff30f483e917$export$2e2bcd8739ae039.ADDED&&file.accepted===true){var _this=this;file.status=$a601ff30f483e917$export$2e2bcd8739ae039.QUEUED;if(this.options.autoProcessQueue)return setTimeout(function(){return _this.processQueue()},0)}else throw new Error("This file can't be queued because it has already been processed or was rejected.")}},{key:"_enqueueThumbnail",value:function _enqueueThumbnail(file){if(this.options.createImageThumbnails&&file.type.match(/image.*/)&&file.size<=this.options.maxThumbnailFilesize*1048576){var _this=this;this._thumbnailQueue.push(file);return setTimeout(function(){return _this._processThumbnailQueue()},0)}}},{key:"_processThumbnailQueue",value:function _processThumbnailQueue(){var _this=this;if(this._processingThumbnail||this._thumbnailQueue.length===0)return;this._processingThumbnail=true;var file=this._thumbnailQueue.shift();return this.createThumbnail(file,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,true,function(dataUrl){_this.emit("thumbnail",file,dataUrl);_this._processingThumbnail=false;return _this._processThumbnailQueue()})}},{key:"removeFile",value:function removeFile(file){if(file.status===$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING)this.cancelUpload(file);this.files=$a601ff30f483e917$var$without(this.files,file);this.emit("removedfile",file);if(this.files.length===0)return this.emit("reset")}},{key:"removeAllFiles",value:function removeAllFiles(cancelIfNecessary){if(cancelIfNecessary==null)cancelIfNecessary=false;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=this.files.slice()[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;if(file.status!==$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING||cancelIfNecessary)this.removeFile(file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return null}},{key:"resizeImage",value:function resizeImage(file,width,height,resizeMethod,callback){var _this=this;return this.createThumbnail(file,width,height,resizeMethod,true,function(dataUrl,canvas){if(canvas==null)return callback(file);else{var _options=_this.options,resizeMimeType=_options.resizeMimeType;if(resizeMimeType==null)resizeMimeType=file.type;var resizedDataURL=canvas.toDataURL(resizeMimeType,_this.options.resizeQuality);if(resizeMimeType==="image/jpeg"||resizeMimeType==="image/jpg")resizedDataURL=$a601ff30f483e917$var$ExifRestore.restore(file.dataURL,resizedDataURL);return callback($a601ff30f483e917$export$2e2bcd8739ae039.dataURItoBlob(resizedDataURL))}})}},{key:"createThumbnail",value:function createThumbnail(file,width,height,resizeMethod,fixOrientation,callback){var _this=this;var fileReader=new FileReader;fileReader.onload=function(){file.dataURL=fileReader.result;if(file.type==="image/svg+xml"){if(callback!=null)callback(fileReader.result);return}_this.createThumbnailFromUrl(file,width,height,resizeMethod,fixOrientation,callback)};fileReader.readAsDataURL(file)}},{key:"displayExistingFile",value:function displayExistingFile(mockFile,imageUrl,callback,crossOrigin,param){var resizeThumbnail=param===void 0?true:param;this.emit("addedfile",mockFile);this.emit("complete",mockFile);if(!resizeThumbnail){this.emit("thumbnail",mockFile,imageUrl);if(callback)callback()}else{var _this=this;var onDone=function(thumbnail){_this.emit("thumbnail",mockFile,thumbnail);if(callback)callback()};mockFile.dataURL=imageUrl;this.createThumbnailFromUrl(mockFile,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,this.options.fixOrientation,onDone,crossOrigin)}}},{key:"createThumbnailFromUrl",value:function createThumbnailFromUrl(file,width,height,resizeMethod,fixOrientation,callback,crossOrigin){var _this=this;var img=document.createElement("img");if(crossOrigin)img.crossOrigin=crossOrigin;fixOrientation=getComputedStyle(document.body)["imageOrientation"]=="from-image"?false:fixOrientation;img.onload=function(){var _this1=_this;var loadExif=function(callback){return callback(1)};if(typeof EXIF!=="undefined"&&EXIF!==null&&fixOrientation)loadExif=function(callback){return EXIF.getData(img,function(){return callback(EXIF.getTag(this,"Orientation"))})};return loadExif(function(orientation){file.width=img.width;file.height=img.height;var resizeInfo=_this1.options.resize.call(_this1,file,width,height,resizeMethod);var canvas=document.createElement("canvas");var ctx=canvas.getContext("2d");canvas.width=resizeInfo.trgWidth;canvas.height=resizeInfo.trgHeight;if(orientation>4){canvas.width=resizeInfo.trgHeight;canvas.height=resizeInfo.trgWidth}switch(orientation){case 2:ctx.translate(canvas.width,0);ctx.scale(-1,1);break;case 3:ctx.translate(canvas.width,canvas.height);ctx.rotate(Math.PI);break;case 4:ctx.translate(0,canvas.height);ctx.scale(1,-1);break;case 5:ctx.rotate(.5*Math.PI);ctx.scale(1,-1);break;case 6:ctx.rotate(.5*Math.PI);ctx.translate(0,-canvas.width);break;case 7:ctx.rotate(.5*Math.PI);ctx.translate(canvas.height,-canvas.width);ctx.scale(-1,1);break;case 8:ctx.rotate(-.5*Math.PI);ctx.translate(-canvas.height,0);break}$a601ff30f483e917$var$drawImageIOSFix(ctx,img,resizeInfo.srcX!=null?resizeInfo.srcX:0,resizeInfo.srcY!=null?resizeInfo.srcY:0,resizeInfo.srcWidth,resizeInfo.srcHeight,resizeInfo.trgX!=null?resizeInfo.trgX:0,resizeInfo.trgY!=null?resizeInfo.trgY:0,resizeInfo.trgWidth,resizeInfo.trgHeight);var thumbnail=canvas.toDataURL("image/png");if(callback!=null)return callback(thumbnail,canvas)})};if(callback!=null)img.onerror=callback;return img.src=file.dataURL}},{key:"processQueue",value:function processQueue(){var _options=this.options,parallelUploads=_options.parallelUploads;var processingLength=this.getUploadingFiles().length;var i=processingLength;if(processingLength>=parallelUploads)return;var queuedFiles=this.getQueuedFiles();if(!(queuedFiles.length>0))return;if(this.options.uploadMultiple)return this.processFiles(queuedFiles.slice(0,parallelUploads-processingLength));else while(i<parallelUploads){if(!queuedFiles.length)return;this.processFile(queuedFiles.shift());i++}}},{key:"processFile",value:function processFile(file){return this.processFiles([file])}},{key:"processFiles",value:function processFiles(files){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;file.processing=true;file.status=$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING;this.emit("processing",file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.options.uploadMultiple)this.emit("processingmultiple",files);return this.uploadFiles(files)}},{key:"_getFilesWithXhr",value:function _getFilesWithXhr(xhr){var files;return files=this.files.filter(function(file){return file.xhr===xhr}).map(function(file){return file})}},{key:"cancelUpload",value:function cancelUpload(file){if(file.status===$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING){var groupedFiles=this._getFilesWithXhr(file.xhr);var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=groupedFiles[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var groupedFile=_step.value;groupedFile.status=$a601ff30f483e917$export$2e2bcd8739ae039.CANCELED}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(typeof file.xhr!=="undefined")file.xhr.abort();var _iteratorNormalCompletion1=true,_didIteratorError1=false,_iteratorError1=undefined;try{for(var _iterator1=groupedFiles[Symbol.iterator](),_step1;!(_iteratorNormalCompletion1=(_step1=_iterator1.next()).done);_iteratorNormalCompletion1=true){var groupedFile=_step1.value;this.emit("canceled",groupedFile)}}catch(err){_didIteratorError1=true;_iteratorError1=err}finally{try{if(!_iteratorNormalCompletion1&&_iterator1.return!=null){_iterator1.return()}}finally{if(_didIteratorError1){throw _iteratorError1}}}if(this.options.uploadMultiple)this.emit("canceledmultiple",groupedFiles)}else if(file.status===$a601ff30f483e917$export$2e2bcd8739ae039.ADDED||file.status===$a601ff30f483e917$export$2e2bcd8739ae039.QUEUED){file.status=$a601ff30f483e917$export$2e2bcd8739ae039.CANCELED;this.emit("canceled",file);if(this.options.uploadMultiple)this.emit("canceledmultiple",[file])}if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function resolveOption(option){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){args[_key-1]=arguments[_key]}if(typeof option==="function")return option.apply(this,args);return option}},{key:"uploadFile",value:function uploadFile(file){return this.uploadFiles([file])}},{key:"uploadFiles",value:function uploadFiles(files){var _this=this;this._transformFiles(files,function(transformedFiles){if(_this.options.chunking){var transformedFile=transformedFiles[0];files[0].upload.chunked=_this.options.chunking&&(_this.options.forceChunking||transformedFile.size>_this.options.chunkSize);files[0].upload.totalChunkCount=Math.ceil(transformedFile.size/_this.options.chunkSize)}if(files[0].upload.chunked){var _this1=_this,_this4=_this;var file=files[0];var transformedFile=transformedFiles[0];var startedChunkCount=0;file.upload.chunks=[];var handleNextChunk=function(){var chunkIndex=0;while(file.upload.chunks[chunkIndex]!==undefined)chunkIndex++;if(chunkIndex>=file.upload.totalChunkCount)return;startedChunkCount++;var start=chunkIndex*_this1.options.chunkSize;var end=Math.min(start+_this1.options.chunkSize,transformedFile.size);var dataBlock={name:_this1._getParamName(0),data:transformedFile.webkitSlice?transformedFile.webkitSlice(start,end):transformedFile.slice(start,end),filename:file.upload.filename,chunkIndex:chunkIndex};file.upload.chunks[chunkIndex]={file:file,index:chunkIndex,dataBlock:dataBlock,status:$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING,progress:0,retries:0};_this1._uploadData(files,[dataBlock])};file.upload.finishedChunkUpload=function(chunk,response){var _this5=_this4;var allFinished=true;chunk.status=$a601ff30f483e917$export$2e2bcd8739ae039.SUCCESS;chunk.dataBlock=null;chunk.response=chunk.xhr.responseText;chunk.responseHeaders=chunk.xhr.getAllResponseHeaders();chunk.xhr=null;for(var i=0;i<file.upload.totalChunkCount;i++){if(file.upload.chunks[i]===undefined)return handleNextChunk();if(file.upload.chunks[i].status!==$a601ff30f483e917$export$2e2bcd8739ae039.SUCCESS)allFinished=false}if(allFinished)_this4.options.chunksUploaded(file,function(){_this5._finished(files,response,null)})};if(_this.options.parallelChunkUploads)for(var i=0;i<file.upload.totalChunkCount;i++)handleNextChunk();else handleNextChunk()}else{var dataBlocks=[];for(var i=0;i<files.length;i++)dataBlocks[i]={name:_this._getParamName(i),data:transformedFiles[i],filename:files[i].upload.filename};_this._uploadData(files,dataBlocks)}})}},{key:"_getChunk",value:function _getChunk(file,xhr){for(var i=0;i<file.upload.totalChunkCount;i++){if(file.upload.chunks[i]!==undefined&&file.upload.chunks[i].xhr===xhr)return file.upload.chunks[i]}}},{key:"_uploadData",value:function _uploadData(files,dataBlocks){var _this=this,_this7=this,_this8=this,_this9=this;var xhr=new XMLHttpRequest;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;file.xhr=xhr}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(files[0].upload.chunked)files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr=xhr;var method=this.resolveOption(this.options.method,files,dataBlocks);var url=this.resolveOption(this.options.url,files,dataBlocks);xhr.open(method,url,true);var timeout=this.resolveOption(this.options.timeout,files);if(timeout)xhr.timeout=this.resolveOption(this.options.timeout,files);xhr.withCredentials=!!this.options.withCredentials;xhr.onload=function(e){_this._finishedUploading(files,xhr,e)};xhr.ontimeout=function(){_this7._handleUploadError(files,xhr,"Request timedout after ".concat(_this7.options.timeout/1e3," seconds"))};xhr.onerror=function(){_this8._handleUploadError(files,xhr)};var progressObj=xhr.upload!=null?xhr.upload:xhr;progressObj.onprogress=function(e){return _this9._updateFilesUploadProgress(files,xhr,e)};var headers=this.options.defaultHeaders?{Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}:{};if(this.options.binaryBody)headers["Content-Type"]=files[0].type;if(this.options.headers)$parcel$interopDefault($6mU8w$justextend)(headers,this.options.headers);for(var headerName in headers){var headerValue=headers[headerName];if(headerValue)xhr.setRequestHeader(headerName,headerValue)}if(this.options.binaryBody){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;this.emit("sending",file,xhr)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.options.uploadMultiple)this.emit("sendingmultiple",files,xhr);this.submitRequest(xhr,null,files)}else{var formData=new FormData;if(this.options.params){var additionalParams=this.options.params;if(typeof additionalParams==="function")additionalParams=additionalParams.call(this,files,xhr,files[0].upload.chunked?this._getChunk(files[0],xhr):null);for(var key in additionalParams){var value=additionalParams[key];if(Array.isArray(value))for(var i=0;i<value.length;i++)formData.append(key,value[i]);else formData.append(key,value)}}var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;this.emit("sending",file,xhr,formData)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.options.uploadMultiple)this.emit("sendingmultiple",files,xhr,formData);this._addFormElementData(formData);for(var i=0;i<dataBlocks.length;i++){var dataBlock=dataBlocks[i];formData.append(dataBlock.name,dataBlock.data,dataBlock.filename)}this.submitRequest(xhr,formData,files)}}},{key:"_transformFiles",value:function _transformFiles(files,done){var _this=this,_loop=function(i){_this.options.transformFile.call(_this,files[i],function(transformedFile){transformedFiles[i]=transformedFile;if(++doneCounter===files.length)done(transformedFiles)})};var transformedFiles=[];var doneCounter=0;for(var i=0;i<files.length;i++)_loop(i)}},{key:"_addFormElementData",value:function _addFormElementData(formData){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(this.element.tagName==="FORM")try{for(var _iterator=this.element.querySelectorAll("input, textarea, select, button")[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var input=_step.value;var inputName=input.getAttribute("name");var inputType=input.getAttribute("type");if(inputType)inputType=inputType.toLowerCase();if(typeof inputName==="undefined"||inputName===null)continue;if(input.tagName==="SELECT"&&input.hasAttribute("multiple")){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=input.options[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var option=_step.value;if(option.selected)formData.append(inputName,option.value)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}else if(!inputType||inputType!=="checkbox"&&inputType!=="radio"||input.checked)formData.append(inputName,input.value)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}},{key:"_updateFilesUploadProgress",value:function _updateFilesUploadProgress(files,xhr,e){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(!files[0].upload.chunked)try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;if(file.upload.total&&file.upload.bytesSent&&file.upload.bytesSent==file.upload.total)continue;if(e){file.upload.progress=100*e.loaded/e.total;file.upload.total=e.total;file.upload.bytesSent=e.loaded}else{file.upload.progress=100;file.upload.bytesSent=file.upload.total}this.emit("uploadprogress",file,file.upload.progress,file.upload.bytesSent)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}else{var file=files[0];var chunk=this._getChunk(file,xhr);if(e){chunk.progress=100*e.loaded/e.total;chunk.total=e.total;chunk.bytesSent=e.loaded}else{chunk.progress=100;chunk.bytesSent=chunk.total}file.upload.progress=0;file.upload.total=0;file.upload.bytesSent=0;for(var i=0;i<file.upload.totalChunkCount;i++)if(file.upload.chunks[i]&&typeof file.upload.chunks[i].progress!=="undefined"){file.upload.progress+=file.upload.chunks[i].progress;file.upload.total+=file.upload.chunks[i].total;file.upload.bytesSent+=file.upload.chunks[i].bytesSent}file.upload.progress=file.upload.progress/file.upload.totalChunkCount;this.emit("uploadprogress",file,file.upload.progress,file.upload.bytesSent)}}},{key:"_finishedUploading",value:function _finishedUploading(files,xhr,e){var response;if(files[0].status===$a601ff30f483e917$export$2e2bcd8739ae039.CANCELED)return;if(xhr.readyState!==4)return;if(xhr.responseType!=="arraybuffer"&&xhr.responseType!=="blob"){response=xhr.responseText;if(xhr.getResponseHeader("content-type")&&~xhr.getResponseHeader("content-type").indexOf("application/json"))try{response=JSON.parse(response)}catch(error){e=error;response="Invalid JSON response from server."}}this._updateFilesUploadProgress(files,xhr);if(!(200<=xhr.status&&xhr.status<300))this._handleUploadError(files,xhr,response);else if(files[0].upload.chunked)files[0].upload.finishedChunkUpload(this._getChunk(files[0],xhr),response);else this._finished(files,response,e)}},{key:"_handleUploadError",value:function _handleUploadError(files,xhr,response){if(files[0].status===$a601ff30f483e917$export$2e2bcd8739ae039.CANCELED)return;if(files[0].upload.chunked&&this.options.retryChunks){var chunk=this._getChunk(files[0],xhr);if(chunk.retries++<this.options.retryChunksLimit){this._uploadData(files,[chunk.dataBlock]);return}else console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(files,response||this.options.dictResponseError.replace("{{statusCode}}",xhr.status),xhr)}},{key:"submitRequest",value:function submitRequest(xhr,formData,files){if(xhr.readyState!=1){console.warn("Cannot send this request because the XMLHttpRequest.readyState is not OPENED.");return}if(this.options.binaryBody){if(files[0].upload.chunked){var chunk=this._getChunk(files[0],xhr);xhr.send(chunk.dataBlock.data)}else xhr.send(files[0])}else xhr.send(formData)}},{key:"_finished",value:function _finished(files,responseText,e){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;file.status=$a601ff30f483e917$export$2e2bcd8739ae039.SUCCESS;this.emit("success",file,responseText,e);this.emit("complete",file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.options.uploadMultiple){this.emit("successmultiple",files,responseText,e);this.emit("completemultiple",files)}if(this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function _errorProcessing(files,message,xhr){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=files[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var file=_step.value;file.status=$a601ff30f483e917$export$2e2bcd8739ae039.ERROR;this.emit("error",file,message,xhr);this.emit("complete",file)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}if(this.options.uploadMultiple){this.emit("errormultiple",files,message,xhr);this.emit("completemultiple",files)}if(this.options.autoProcessQueue)return this.processQueue()}}],[{key:"initClass",value:function initClass(){this.prototype.Emitter=$b1d17cfb1d15c36a$export$2e2bcd8739ae039;this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"];this.prototype._thumbnailQueue=[];this.prototype._processingThumbnail=false}},{key:"uuidv4",value:function uuidv4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){var r=Math.random()*16|0,v=c==="x"?r:r&3|8;return v.toString(16)})}}]);return $a601ff30f483e917$export$2e2bcd8739ae039}($b1d17cfb1d15c36a$export$2e2bcd8739ae039);$a601ff30f483e917$export$2e2bcd8739ae039.initClass();$a601ff30f483e917$export$2e2bcd8739ae039.options={};$a601ff30f483e917$export$2e2bcd8739ae039.optionsForElement=function(element){if(element.getAttribute("id"))return $a601ff30f483e917$export$2e2bcd8739ae039.options[$a601ff30f483e917$var$camelize(element.getAttribute("id"))];else return undefined};$a601ff30f483e917$export$2e2bcd8739ae039.instances=[];$a601ff30f483e917$export$2e2bcd8739ae039.forElement=function(element){if(typeof element==="string")element=document.querySelector(element);if((element!=null?element.dropzone:undefined)==null)throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return element.dropzone};$a601ff30f483e917$export$2e2bcd8739ae039.discover=function(){var dropzones;if(document.querySelectorAll)dropzones=document.querySelectorAll(".dropzone");else{dropzones=[];var checkElements=function(elements){return function(){var result=[];var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=elements[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var el=_step.value;if(/(^| )dropzone($| )/.test(el.className))result.push(dropzones.push(el));else result.push(undefined)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return result}()};checkElements(document.getElementsByTagName("div"));checkElements(document.getElementsByTagName("form"))}return function(){var result=[];var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=dropzones[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var dropzone=_step.value;if($a601ff30f483e917$export$2e2bcd8739ae039.optionsForElement(dropzone)!==false)result.push(new $a601ff30f483e917$export$2e2bcd8739ae039(dropzone));else result.push(undefined)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return result}()};$a601ff30f483e917$export$2e2bcd8739ae039.blockedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i];$a601ff30f483e917$export$2e2bcd8739ae039.isBrowserSupported=function(){var capableBrowser=true;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector){if(!("classList"in document.createElement("a")))capableBrowser=false;else{if($a601ff30f483e917$export$2e2bcd8739ae039.blacklistedBrowsers!==undefined)$a601ff30f483e917$export$2e2bcd8739ae039.blockedBrowsers=$a601ff30f483e917$export$2e2bcd8739ae039.blacklistedBrowsers;var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=$a601ff30f483e917$export$2e2bcd8739ae039.blockedBrowsers[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var regex=_step.value;if(regex.test(navigator.userAgent)){capableBrowser=false;continue}}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}}else capableBrowser=false;return capableBrowser};$a601ff30f483e917$export$2e2bcd8739ae039.dataURItoBlob=function(dataURI){var byteString=atob(dataURI.split(",")[1]);var mimeString=dataURI.split(",")[0].split(":")[1].split(";")[0];var ab=new ArrayBuffer(byteString.length);var ia=new Uint8Array(ab);for(var i=0,end=byteString.length,asc=0<=end;asc?i<=end:i>=end;asc?i++:i--)ia[i]=byteString.charCodeAt(i);return new Blob([ab],{type:mimeString})};var $a601ff30f483e917$var$without=function(list,rejectedItem){return list.filter(function(item){return item!==rejectedItem}).map(function(item){return item})};var $a601ff30f483e917$var$camelize=function(str){return str.replace(/[\-_](\w)/g,function(match){return match.charAt(1).toUpperCase()})};$a601ff30f483e917$export$2e2bcd8739ae039.createElement=function(string){var div=document.createElement("div");div.innerHTML=string;return div.childNodes[0]};$a601ff30f483e917$export$2e2bcd8739ae039.elementInside=function(element,container){if(element===container)return true;while(element=element.parentNode){if(element===container)return true}return false};$a601ff30f483e917$export$2e2bcd8739ae039.getElement=function(el,name){var element;if(typeof el==="string")element=document.querySelector(el);else if(el.nodeType!=null)element=el;if(element==null)throw new Error("Invalid `".concat(name,"` option provided. Please provide a CSS selector or a plain HTML element."));return element};$a601ff30f483e917$export$2e2bcd8739ae039.getElements=function(els,name){var el,elements;if(els instanceof Array){elements=[];try{var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=els[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){el=_step.value;elements.push(this.getElement(el,name))}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}catch(e){elements=null}}else if(typeof els==="string"){elements=[];var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=document.querySelectorAll(els)[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){el=_step.value;elements.push(el)}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}}else if(els.nodeType!=null)elements=[els];if(elements==null||!elements.length)throw new Error("Invalid `".concat(name,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return elements};$a601ff30f483e917$export$2e2bcd8739ae039.confirm=function(question,accepted,rejected){if(window.confirm(question))return accepted();else if(rejected!=null)return rejected()};$a601ff30f483e917$export$2e2bcd8739ae039.isValidFile=function(file,acceptedFiles){if(!acceptedFiles)return true;acceptedFiles=acceptedFiles.split(",");var mimeType=file.type;var baseMimeType=mimeType.replace(/\/.*$/,"");var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;try{for(var _iterator=acceptedFiles[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true){var validType=_step.value;validType=validType.trim();if(validType.charAt(0)==="."){if(file.name.toLowerCase().indexOf(validType.toLowerCase(),file.name.length-validType.length)!==-1)return true}else if(/\/\*$/.test(validType)){if(baseMimeType===validType.replace(/\/.*$/,""))return true}else{if(mimeType===validType)return true}}}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return false};if(typeof jQuery!=="undefined"&&jQuery!==null)jQuery.fn.dropzone=function(options){return this.each(function(){return new $a601ff30f483e917$export$2e2bcd8739ae039(this,options)})};$a601ff30f483e917$export$2e2bcd8739ae039.ADDED="added";$a601ff30f483e917$export$2e2bcd8739ae039.QUEUED="queued";$a601ff30f483e917$export$2e2bcd8739ae039.ACCEPTED=$a601ff30f483e917$export$2e2bcd8739ae039.QUEUED;$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING="uploading";$a601ff30f483e917$export$2e2bcd8739ae039.PROCESSING=$a601ff30f483e917$export$2e2bcd8739ae039.UPLOADING;$a601ff30f483e917$export$2e2bcd8739ae039.CANCELED="canceled";$a601ff30f483e917$export$2e2bcd8739ae039.ERROR="error";$a601ff30f483e917$export$2e2bcd8739ae039.SUCCESS="success";var $a601ff30f483e917$var$detectVerticalSquash=function $a601ff30f483e917$var$detectVerticalSquash(img){var iw=img.naturalWidth;var ih=img.naturalHeight;var canvas=document.createElement("canvas");canvas.width=1;canvas.height=ih;var ctx=canvas.getContext("2d");ctx.drawImage(img,0,0);var ref=ctx.getImageData(1,0,1,ih),data=ref.data;var sy=0;var ey=ih;var py=ih;while(py>sy){var alpha=data[(py-1)*4+3];if(alpha===0)ey=py;else sy=py;py=ey+sy>>1}var ratio=py/ih;if(ratio===0)return 1;else return ratio};var $a601ff30f483e917$var$drawImageIOSFix=function $a601ff30f483e917$var$drawImageIOSFix(ctx,img,sx,sy,sw,sh,dx,dy,dw,dh){var vertSquashRatio=$a601ff30f483e917$var$detectVerticalSquash(img);return ctx.drawImage(img,sx,sy,sw,sh,dx,dy,dw,dh/vertSquashRatio)};var $a601ff30f483e917$var$ExifRestore=function(){"use strict";function $a601ff30f483e917$var$ExifRestore(){$6mU8w$swchelpers.classCallCheck(this,$a601ff30f483e917$var$ExifRestore)}$6mU8w$swchelpers.createClass($a601ff30f483e917$var$ExifRestore,null,[{key:"initClass",value:function initClass(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function encode64(input){var output="";var chr1=undefined;var chr2=undefined;var chr3="";var enc1=undefined;var enc2=undefined;var enc3=undefined;var enc4="";var i=0;while(true){chr1=input[i++];chr2=input[i++];chr3=input[i++];enc1=chr1>>2;enc2=(chr1&3)<<4|chr2>>4;enc3=(chr2&15)<<2|chr3>>6;enc4=chr3&63;if(isNaN(chr2))enc3=enc4=64;else if(isNaN(chr3))enc4=64;output=output+this.KEY_STR.charAt(enc1)+this.KEY_STR.charAt(enc2)+this.KEY_STR.charAt(enc3)+this.KEY_STR.charAt(enc4);chr1=chr2=chr3="";enc1=enc2=enc3=enc4="";if(!(i<input.length))break}return output}},{key:"restore",value:function restore(origFileBase64,resizedFileBase64){if(!origFileBase64.match("data:image/jpeg;base64,"))return resizedFileBase64;var rawImage=this.decode64(origFileBase64.replace("data:image/jpeg;base64,",""));var segments=this.slice2Segments(rawImage);var image=this.exifManipulation(resizedFileBase64,segments);return"data:image/jpeg;base64,".concat(this.encode64(image))}},{key:"exifManipulation",value:function exifManipulation(resizedFileBase64,segments){var exifArray=this.getExifArray(segments);var newImageArray=this.insertExif(resizedFileBase64,exifArray);var aBuffer=new Uint8Array(newImageArray);return aBuffer}},{key:"getExifArray",value:function getExifArray(segments){var seg=undefined;var x=0;while(x<segments.length){seg=segments[x];if(seg[0]===255&seg[1]===225)return seg;x++}return[]}},{key:"insertExif",value:function insertExif(resizedFileBase64,exifArray){var imageData=resizedFileBase64.replace("data:image/jpeg;base64,","");var buf=this.decode64(imageData);var separatePoint=buf.indexOf(255,3);var mae=buf.slice(0,separatePoint);var ato=buf.slice(separatePoint);var array=mae;array=array.concat(exifArray);array=array.concat(ato);return array}},{key:"slice2Segments",value:function slice2Segments(rawImageArray){var head=0;var segments=[];while(true){var length;if(rawImageArray[head]===255&rawImageArray[head+1]===218)break;if(rawImageArray[head]===255&rawImageArray[head+1]===216)head+=2;else{length=rawImageArray[head+2]*256+rawImageArray[head+3];var endPoint=head+length+2;var seg=rawImageArray.slice(head,endPoint);segments.push(seg);head=endPoint}if(head>rawImageArray.length)break}return segments}},{key:"decode64",value:function decode64(input){var output="";var chr1=undefined;var chr2=undefined;var chr3="";var enc1=undefined;var enc2=undefined;var enc3=undefined;var enc4="";var i=0;var buf=[];var base64test=/[^A-Za-z0-9\+\/\=]/g;if(base64test.exec(input))console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding.");input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(true){enc1=this.KEY_STR.indexOf(input.charAt(i++));enc2=this.KEY_STR.indexOf(input.charAt(i++));enc3=this.KEY_STR.indexOf(input.charAt(i++));enc4=this.KEY_STR.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;buf.push(chr1);if(enc3!==64)buf.push(chr2);if(enc4!==64)buf.push(chr3);chr1=chr2=chr3="";enc1=enc2=enc3=enc4="";if(!(i<input.length))break}return buf}}]);return $a601ff30f483e917$var$ExifRestore}();$a601ff30f483e917$var$ExifRestore.initClass();var $a601ff30f483e917$var$contentLoaded=function $a601ff30f483e917$var$contentLoaded(win,fn){var done=false;var top=true;var doc=win.document;var root=doc.documentElement;var add=doc.addEventListener?"addEventListener":"attachEvent";var rem=doc.addEventListener?"removeEventListener":"detachEvent";var pre=doc.addEventListener?"":"on";var init=function(e){if(e.type==="readystatechange"&&doc.readyState!=="complete")return;(e.type==="load"?win:doc)[rem](pre+e.type,init,false);if(!done&&(done=true))return fn.call(win,e.type||e)};var poll=function(){try{root.doScroll("left")}catch(e){setTimeout(poll,50);return}return init("poll")};if(doc.readyState!=="complete"){if(doc.createEventObject&&root.doScroll){try{top=!win.frameElement}catch(error){}if(top)poll()}doc[add](pre+"DOMContentLoaded",init,false);doc[add](pre+"readystatechange",init,false);return win[add](pre+"load",init,false)}};function $a601ff30f483e917$var$__guard__(value,transform){return typeof value!=="undefined"&&value!==null?transform(value):undefined}function $a601ff30f483e917$var$__guardMethod__(obj,methodName,transform){if(typeof obj!=="undefined"&&obj!==null&&typeof obj[methodName]==="function")return transform(obj,methodName);else return undefined}