<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

<Files "cronjob-runner.php">
    # 允许长时间运行
    php_value max_execution_time 0
    php_value set_time_limit 0

    # 限制访问IP
    Order Deny,Allow
    Deny from all
    Allow from 127.0.0.1
    Allow from your-ip-address
</Files>

<Files "cronjob-polling.php">
    php_value max_execution_time 0
    php_value set_time_limit 0
</Files>
