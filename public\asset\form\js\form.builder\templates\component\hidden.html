<!-- Hidden -->
<div>
    <input type="hidden" id="{{= field.id }}" name="{{= field.id }}" value="{{= field.predefinedValue }}" data-alias="{{= field.alias }}"{{ if (field.label) { }} data-label="{{= field.label }}"{{ } }}{{ if (field.unique) { }} data-unique="{{= field.unique }}"{{ } }}{{ if (field.disabled) { }} disabled{{ } }}{{ _.each(field.customAttributes, function(attribute, i){ var attrs = attribute.split("|"); var attr = attrs[0]; var value = (attrs.length > 1) ? attrs[1] : attrs[0]; }}{{ if (attr) { }} {{- attr }}="{{- value }}"{{ } }}{{ }) }}>
</div>
