<template>
  <div class="bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box">
        <div class="account-safety">
         
          <div class="safety-content">
            <div class="safety-score">
              <el-progress type="circle" :percentage="safetyScore" :color="scoreColor">
                <template #default="{ percentage }">
                  <span class="percentage-value">{{ percentage }}</span>
                </template>
              </el-progress>
              <span class="safety-level">
                {{ t('dashboard.accountSafety.score.level', { level: t(`dashboard.accountSafety.score.levels.${safetyLevel}`) }) }}
              </span>
            </div>
            <div class="safety-items">
              <div class="safety-item">
                <div class="item-info">
                  <span class="item-title">{{ t('dashboard.accountSafety.password.title') }}</span>
                  <div class="password-strength">
                    <span :class="['strength', passwordStrength]"></span>
                    <span :class="['strength', passwordStrength === t('dashboard.accountSafety.password.strength.medium') || 
                      passwordStrength === t('dashboard.accountSafety.password.strength.strong') ? passwordStrength : '']"></span>
                    <span :class="['strength', passwordStrength === t('dashboard.accountSafety.password.strength.strong') ? passwordStrength : '']"></span>
                    <span class="strength-text">{{ passwordStrength }}</span>
                  </div>
                  <span class="item-description">{{ t('dashboard.accountSafety.password.description') }}</span>
                </div>
                <div class="item-actions">
                  <el-button link class="action-link" @click="modifyPassword">
                    {{ t('dashboard.accountSafety.password.modify') }}
                  </el-button>
                  <el-button link class="action-link" @click="showModifyPasswordDialog">
                    {{ t('dashboard.accountSafety.password.modifyNow') }}
                  </el-button>
                </div>
              </div>
              <div class="safety-item">
                <div class="item-info">
                  <span class="item-title">{{ t('dashboard.accountSafety.accountDeletion.title') }}</span>
                  <span class="item-description">{{ t('dashboard.accountSafety.accountDeletion.description') }}</span>
                </div>
                <div class="item-actions">
                  <el-button link class="action-link danger" @click="deleteAccount">
                    <el-icon><Delete /></el-icon>
                    {{ t('dashboard.accountSafety.accountDeletion.delete') }}
                  </el-button>
                </div>
              </div>
              <div class="safety-item">
                <div class="item-info">
                  <span class="item-title">{{ t('dashboard.accountSafety.mfa.title') }}</span>
                  <span class="item-description">
                    {{ securityInfo.mfaEnrolled ? t('dashboard.accountSafety.mfa.enabled') : t('dashboard.accountSafety.mfa.disabled') }}
                  </span>
                </div>
                <div class="item-actions">
                  <el-button link class="action-link" @click="toggleMFA">
                    {{ securityInfo.mfaEnrolled ? t('dashboard.accountSafety.mfa.disable') : t('dashboard.accountSafety.mfa.enable') }}
                  </el-button>
                </div>
              </div>
              <div class="safety-item">
                <div class="item-info">
                  <span class="item-title">{{ t('dashboard.accountSafety.email.title') }}</span>
                  <span class="item-description">
                    {{ securityInfo.emailBinded ? t('dashboard.accountSafety.email.bound') : t('dashboard.accountSafety.email.unbound') }}
                  </span>
                </div>
                <div class="item-actions">
                  <el-button link class="action-link" @click="manageEmail">
                    {{ securityInfo.emailBinded ? t('dashboard.accountSafety.email.modify') : t('dashboard.accountSafety.email.bind') }}
                  </el-button>
                </div>
              </div>
              <div class="safety-item">
                <div class="item-info">
                  <span class="item-title">{{ t('dashboard.accountSafety.phone.title') }}</span>
                  <span class="item-description">
                    {{ securityInfo.phoneBinded ? t('dashboard.accountSafety.phone.bound') : t('dashboard.accountSafety.phone.unbound') }}
                  </span>
                </div>
                <div class="item-actions">
                  <el-button link class="action-link" @click="managePhone">
                    {{ securityInfo.phoneBinded ? t('dashboard.accountSafety.phone.modify') : t('dashboard.accountSafety.phone.bind') }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码的弹窗 -->
    <el-dialog 
      v-model="modifyPasswordDialogVisible" 
      :title="t('dashboard.accountSafety.password.dialog.title')" 
      :close-on-click-modal="false" 
      custom-class="modify-password-dialog"
    >
      <el-form :model="passwordForm" label-position="top" :rules="passwordRules" ref="passwordFormRef">
        <el-form-item :label="t('dashboard.accountSafety.password.dialog.oldPassword')" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password" 
            show-password 
            :placeholder="t('dashboard.accountSafety.password.dialog.placeholder.oldPassword')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="t('dashboard.accountSafety.password.dialog.newPassword')" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            show-password 
            :placeholder="t('dashboard.accountSafety.password.dialog.placeholder.newPassword')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="t('dashboard.accountSafety.password.dialog.confirmPassword')" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            show-password 
            :placeholder="t('dashboard.accountSafety.password.dialog.placeholder.confirmPassword')"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modifyPasswordDialogVisible = false">
            {{ t('dashboard.accountSafety.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="submitModifyPassword">
            {{ t('dashboard.accountSafety.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 账号注销的弹窗 -->
    <el-dialog 
      v-model="deleteAccountDialogVisible" 
      :title="t('dashboard.accountSafety.accountDeletion.dialog.title')" 
      :close-on-click-modal="false" 
      custom-class="delete-account-dialog"
    >
      <div class="warning-message">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <span>{{ t('dashboard.accountSafety.accountDeletion.dialog.warning') }}</span>
      </div>
      <el-form :model="deleteAccountForm" label-position="top">
        <el-form-item 
          v-if="!userStore.userInfo.phone" 
          :label="t('dashboard.accountSafety.accountDeletion.dialog.account')"
        >
          <el-input 
            v-model="deleteAccountForm.account" 
            :placeholder="t('dashboard.accountSafety.accountDeletion.dialog.placeholder.account')"
          ></el-input>
        </el-form-item>
        <el-form-item 
          v-if="!userStore.userInfo.phone" 
          :label="t('dashboard.accountSafety.accountDeletion.dialog.password')"
        >
          <el-input 
            v-model="deleteAccountForm.password" 
            type="password" 
            :placeholder="t('dashboard.accountSafety.accountDeletion.dialog.placeholder.password')"
          ></el-input>
        </el-form-item>
        <el-form-item 
          :label="userStore.userInfo.phone ? 
            t('dashboard.accountSafety.accountDeletion.dialog.verificationCode') : 
            t('dashboard.accountSafety.accountDeletion.dialog.phone')"
        >
          <div v-if="userStore.userInfo.phone" class="verification-code-input">
            <el-input 
              v-model="deleteAccountForm.code" 
              :placeholder="t('dashboard.accountSafety.accountDeletion.dialog.placeholder.code')"
            >
              <template #append>
                <el-button 
                  :disabled="isCountingDown" 
                  @click="sendVerificationCode" 
                  :loading="isCountingDown"
                >
                  {{ isCountingDown ? t('dashboard.accountSafety.common.countdown', { seconds: countdown }) : t('dashboard.accountSafety.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
          <el-input 
            v-else 
            v-model="deleteAccountForm.phone" 
            :placeholder="t('dashboard.accountSafety.accountDeletion.dialog.placeholder.phone')"
          >
            <template #prepend>
              <el-select v-model="deleteAccountForm.phoneCountryCode" style="width: 100px">
                <el-option label="+86" value="+86"></el-option>
                <el-option label="+852" value="+852"></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item 
          v-if="!userStore.userInfo.phone" 
          :label="t('dashboard.accountSafety.accountDeletion.dialog.verificationCode')"
        >
          <div class="verification-code-input">
            <el-input 
              v-model="deleteAccountForm.code" 
              :placeholder="t('dashboard.accountSafety.accountDeletion.dialog.placeholder.code')"
            >
              <template #append>
                <el-button 
                  :disabled="isCountingDown" 
                  @click="sendVerificationCode" 
                  :loading="isCountingDown"
                >
                  {{ isCountingDown ? t('dashboard.accountSafety.common.countdown', { seconds: countdown }) : t('dashboard.accountSafety.common.sendCode') }}
                </el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteAccountDialogVisible = false">
            {{ t('dashboard.accountSafety.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="confirmDeleteAccount">
            {{ t('dashboard.accountSafety.common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { Delete, WarningFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { UserCenterService } from '../application/UserCenterService'
import { useUserStore } from '/admin/stores/modules/user/userStore'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const userCenterService = new UserCenterService()
const userStore: any = useUserStore()
const appCode = ref(localStorage.getItem('app_code') || 'admin')
const i18n = useI18n()
const t = i18n.t

interface SecurityInfo {
  passwordSecurityLevel: number
  mfaEnrolled: boolean
  passwordSet: boolean
  emailBinded: boolean
  phoneBinded: boolean
  securityScore: number
}

const securityInfo = ref<any>({
  passwordSecurityLevel: 0,
  mfaEnrolled: false,
  passwordSet: false,
  emailBinded: false,
  phoneBinded: false,
  securityScore: 0,
})

const safetyScore = computed(() => securityInfo.value.securityScore)
const passwordStrength = computed(() => {
  const level = securityInfo.value.passwordSecurityLevel
  if (level <= 3) return '弱'
  if (level <= 6) return '中'
  return '强'
})
const verificationCode = ref('')
const deleteAccountDialogVisible = ref(false)
const safetyLevel = computed(() => {
  if (safetyScore.value < 60) return '低'
  if (safetyScore.value < 80) return '中'
  return '高'
})

const scoreColor = computed(() => {
  if (safetyScore.value < 60) return '#F56C6C'
  if (safetyScore.value < 80) return '#E6A23C'
  return '#67C23A'
})

const modifyPassword = () => {}

const modifyPasswordNow = () => {}

const deleteAccount = () => {
  deleteAccountDialogVisible.value = true
}

const isCountingDown = ref(false)
const countdown = ref(60)
const countdownText = ref('发送验证码')

const deleteAccountForm = reactive({
  account: '',
  password: '',
  phone: '',
  phoneCountryCode: '+86',
  code: '',
})

const sendVerificationCode = async () => {
  if (isCountingDown.value) return

  let phoneNumber = userStore.userInfo.phone || deleteAccountForm.phone
  if (!phoneNumber) {
    ElMessage.warning('请输入手机号')
    return
  }

  try {
    isCountingDown.value = true
    await userCenterService.sendSmsCode(phoneNumber, 'CHANNEL_DELETE_ACCOUNT', deleteAccountForm.phoneCountryCode, appCode.value)
    countdown.value = 60
    countdownText.value = `${countdown.value}s后重新发送`
    const timer = setInterval(() => {
      countdown.value--
      countdownText.value = `${countdown.value}s后重新发送`
      if (countdown.value <= 0) {
        clearInterval(timer)
        isCountingDown.value = false
        countdownText.value = '发送验证码'
      }
    }, 1000)
    ElMessage.success('验证码已发送')
  } catch (error) {
    console.error('发送验证码失败', error)
    ElMessage.error('发送验证码失败，请稍后重试')
    isCountingDown.value = false
  }
}

const confirmDeleteAccount = async () => {
  if (!deleteAccountForm.code) {
    ElMessage.warning('请输入验证码')
    return
  }

  try {
    const payload = {
      verifyMethod: userStore.userInfo.phone ? 'PHONE_PASSCODE' : 'PASSWORD',
      phonePassCodePayload: {
        phoneNumber: userStore.userInfo.phone || deleteAccountForm.phone,
        passCode: deleteAccountForm.code,
        phoneCountryCode: deleteAccountForm.phoneCountryCode,
      },
      emailPassCodePayload: {
        email: '',
        passCode: '',
      },
      passwordPayload: {
        password: deleteAccountForm.password,
        passwordEncryptType: 'none',
      },
    }

    await userCenterService.verifyDeleteAccountRequest(payload)
    ElMessage.success('账号注销请求已提交')
    deleteAccountDialogVisible.value = false
  } catch (error) {
    console.error('账号注销失败', error)
    ElMessage.error('账号注销失败，请检查输入信息是否正确')
  }
}

const getSafetyScore = async () => {
  try {
    const response = await userCenterService.getSecurityInfo()
    securityInfo.value = response
  } catch (error) {
    console.error('获取安全信息失败', error)
    ElMessage.error('获取安全信息失败，请稍后重试')
  }
}
// 新增的代码
const modifyPasswordDialogVisible = ref(false)
const passwordFormRef = ref<FormInstance>()
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const passwordRules: FormRules = {
  oldPassword: [{ required: true, message: '请输入原始密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

const showModifyPasswordDialog = () => {
  modifyPasswordDialogVisible.value = true
}

const submitModifyPassword = async () => {
  if (!passwordFormRef.value) return
  await passwordFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        await userCenterService.updatePassword(passwordForm.oldPassword, passwordForm.newPassword)
        modifyPasswordDialogVisible.value = false
        // 重置表单
        passwordForm.oldPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
      } catch (error) {
        ElMessage.error('修改密码失败，请检查原密码是否正确')
      }
    } else {
      console.log('验证失败', fields)
    }
  })
}

const toggleMFA = () => {
  // TODO: Implement MFA toggle logic
  console.log('Toggle MFA')
}

const manageEmail = () => {
  // TODO: Implement email management logic
  console.log('Manage email')
}

const managePhone = () => {
  // TODO: Implement phone management logic
  console.log('Manage phone')
}

onMounted(() => {
  getSafetyScore()
})
</script>

<style lang="scss" scoped>
.account-safety {
  padding: 20px;
}

.safety-content {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}

.safety-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.percentage-value {
  font-size: 28px;
  font-weight: bold;
}

.safety-level {
  margin-top: 10px;
  font-size: 14px;
}

.safety-items {
  width: 100%;
}

.safety-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.item-info {
  display: flex;
  flex-direction: column;
}

.item-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.password-strength {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.strength {
  width: 30px;
  height: 6px;
  margin-right: 5px;
  background-color: #dcdfe6;
}

.strength.弱 {
  background-color: #f56c6c;
}
.strength.中 {
  background-color: #e6a23c;
}
.strength.强 {
  background-color: #67c23a;
}

.strength-text {
  margin-left: 10px;
  color: #909399;
}

.item-description {
  color: #909399;
  font-size: 14px;
}

.item-actions {
  display: flex;
  align-items: center;
}

.action-link {
  color: #409eff;
  margin-left: 10px;
}

.action-link.danger {
  color: #f56c6c;
}

.action-link .el-icon {
  margin-right: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modify-password-dialog :deep(.el-dialog__body) {
  padding-top: 10px;
}

.modify-password-dialog :deep(.el-form-item__label) {
  padding-bottom: 4px;
}

.modify-password-dialog :deep(.el-input__wrapper) {
  padding: 1px 11px;
}

.modify-password-dialog :deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer .el-button {
  min-width: 80px;
}

.delete-account-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
}

.warning-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  color: #f56c6c;
}

.warning-icon {
  font-size: 20px;
  margin-right: 10px;
}

.verification-code {
  margin-bottom: 20px;
}

.verification-code span {
  display: block;
  margin-bottom: 10px;
}

.verification-code-input {
  display: flex;
}

.verification-code-input .el-input {
  flex: 1;
}

.verification-code-input .el-button {
  margin-left: 10px;
  width: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  min-width: 80px;
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .el-form-item__label {
    padding-bottom: 8px;
  }
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .el-icon {
    margin: 0;
  }
}
</style>