@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .contact-info {
    background-color: #FFFFFF;

    .container {
      .df(center);
      
      .left {
        .df(stretch, flex-start, column);
        flex-grow: 1;

        .module-tit {
          .df(stretch, flex-start, column);

          h4 {
            margin-bottom: 20px;
            font-size: 36px;
            color: #333;
            line-height: 1.3;
          }
  
          .desc {
            font-size: 16px;
            line-height: 1.75;
          }
        }
  
        .info-list {
          .df(stretch, flex-start, column);

          .item {
            margin-bottom: 30px;
            .df(center);

            .iconfont {
              font-size: 42px;
              color: #121212;
            }

            .info {
              padding-left: 20px;

              .label {
                margin-bottom: 5px;
                font-size: 16px;
                color: #333;
                font-weight: bold;
                line-height: 1.5;
              }

              .details {
                color: #333;
                line-height: 1.5;
                font-size: 16px;

                a {
                  color: #333;
                  line-height: 1.5;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
  
      .right {
        border: 1px solid #eee;
        padding: 20px 30px;
        text-align: center;

        .label {
          font-size: 16px;
          color: #333;
          line-height: 1.75;
        }
  
        .text {
          color: #ff9600;
          font-size: 16px;
          line-height: 1.75;
        }
  
        .pic {
          max-width: 233px;
        }
      }
    }
  }

  .feedback-module {
    background-color: #CACACA;
    position: relative;
    
    .df(stretch, flex-start, row-reverse);

    .left {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

      .container {
        height: 100%;
      }

      .box {
        padding-right: 80px;
        width: 50%;
        height: 100%;

        .df(center, center, column);

        .module-tit {
          margin-bottom: 30px;

          h4 {
            font-size: 24px;
            color: #fff;
          }
        }
  
        .from {
          .df(stretch, flex-start, column);
          width: 100%;

          .el-form-item {
            margin-bottom: 20px;

            .el-form-item__content {
              .el-input {
                .el-input__wrapper {
                  border: 0;
                  border-radius: 0;
                  box-shadow: 0 0px 0px rgba(0,0,0,0.0) inset;
                  background-color: #fff;
                  height: 50px;
                  line-height: 50px;

                  input {
                    font-size: 16px;
                  }
                }
              }
            }
          }
  
          textarea {
            margin-bottom: 20px;
            padding: 12px;
            line-height: 24px;
            width: 100%;
            font-size: 16px;
            background: #fff;
            border: 0;
          }

          button {
            .btn-radius(0, 12px, 30px, #fff, #ff9600, #FCB319, #fff);

            border: none;
            width: 100%;
            height: auto;
          }
        }
      }
    }

    .right {
      width: 50%;
    }
  }
}