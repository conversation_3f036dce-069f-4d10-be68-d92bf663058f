$(document).ready(function(){$.when($("head").append('<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/ui-lightness/jquery-ui.css" type="text/css" />'),$.getScript("//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$("body").css("padding-bottom","200px");$.datepicker.regional["fr"]={clearText:"Effacer",clearStatus:"",closeText:"Fermer",prevText:"&#x3c;PrÃ©c",nextText:"Suiv&#x3e;",currentText:"Aujourd'hui",monthNames:["<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","Septem<PERSON>","<PERSON><PERSON>re","<PERSON>em<PERSON>","Decembre"],monthNamesShort:["<PERSON>","<PERSON><PERSON>","<PERSON>","Avr","<PERSON>","Jun","<PERSON>","Aou","Sep","Oct","Nov","Dec"],dayNames:["Dimanche","<PERSON>i","Mardi","Mercredi","Jeudi","Vendredi","<PERSON>di"],day<PERSON>ames<PERSON>hort:["Dim","Lun","Mar","Mer","Jeu","Ven","Sam"],dayNamesMin:["Di","Lu","Ma","Me","Je","Ve","Sa"],weekHeader:"Sm",dateFormat:"dd-mm-yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:"",minDate:0,maxDate:"+120M +0D",numberOfMonths:1,showButtonPanel:true};$.datepicker.setDefaults($.datepicker.regional["fr"]);$("input[type=date]").each(function(){var that=this;$(that).attr("type","text").after($(that).clone().attr("id",that.id+"_alt").attr("name",that.id+"_alt").datepicker({dateFormat:"mm/dd/yy",changeMonth:true,changeYear:true,altField:this,altFormat:"yy-mm-dd"}).on("change",function(){$(that).trigger("change")})).hide()})})});