# 领域接口模板

## 概述

领域接口定义了领域服务和仓储的契约,是实现依赖倒置的重要手段。本文档提供了领域接口的标准模板和最佳实践。

## 基本结构

### 仓储接口

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Interfaces;

use Modules\YourModule\Domain\Entities\YourEntity;
use Modules\YourModule\Domain\Collections\YourCollection;
use Modules\YourModule\Domain\Criteria\YourCriteria;
use Modules\YourModule\Domain\Exceptions\YourModuleNotFoundException;

interface YourRepositoryInterface
{
    /**
     * 根据ID查找记录
     *
     * @param int $id
     * @throws YourModuleNotFoundException
     * @return YourEntity
     */
    public function findById(int $id): YourEntity;

    /**
     * 根据条件查找记录
     *
     * @param YourCriteria $criteria
     * @return YourCollection
     */
    public function findByCriteria(YourCriteria $criteria): YourCollection;

    /**
     * 保存记录
     *
     * @param YourEntity $entity
     * @return YourEntity
     */
    public function save(YourEntity $entity): YourEntity;

    /**
     * 删除记录
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;
}
```

### 领域服务接口

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Interfaces;

use Modules\YourModule\Domain\Entities\YourEntity;
use Modules\YourModule\Domain\ValueObjects\Money;
use Modules\YourModule\Domain\Exceptions\YourModuleException;

interface YourServiceInterface
{
    /**
     * 创建记录
     *
     * @param array $data
     * @throws YourModuleException
     * @return YourEntity
     */
    public function create(array $data): YourEntity;

    /**
     * 更新记录
     *
     * @param int $id
     * @param array $data
     * @throws YourModuleException
     * @return YourEntity
     */
    public function update(int $id, array $data): YourEntity;

    /**
     * 激活记录
     *
     * @param int $id
     * @throws YourModuleException
     * @return YourEntity
     */
    public function activate(int $id): YourEntity;

    /**
     * 停用记录
     *
     * @param int $id
     * @throws YourModuleException
     * @return YourEntity
     */
    public function deactivate(int $id): YourEntity;
}
```

### 事件监听器接口

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Interfaces;

use Modules\YourModule\Domain\Events\YourModuleCreatedEvent;
use Modules\YourModule\Domain\Events\YourModuleActivatedEvent;
use Modules\YourModule\Domain\Events\YourModuleDeactivatedEvent;

interface YourEventListenerInterface
{
    /**
     * 处理记录创建事件
     *
     * @param YourModuleCreatedEvent $event
     * @return void
     */
    public function handleCreated(YourModuleCreatedEvent $event): void;

    /**
     * 处理记录激活事件
     *
     * @param YourModuleActivatedEvent $event
     * @return void
     */
    public function handleActivated(YourModuleActivatedEvent $event): void;

    /**
     * 处理记录停用事件
     *
     * @param YourModuleDeactivatedEvent $event
     * @return void
     */
    public function handleDeactivated(YourModuleDeactivatedEvent $event): void;
}
```

## 规范要求

1. 命名规范
   - 接口名：使用 Interface 后缀
   - 方法名：使用小驼峰命名法
   - 参数名：使用小驼峰命名法
   - 常量名：使用大写下划线

2. 方法定义
   - 方法签名明确
   - 参数类型声明
   - 返回值类型声明
   - 异常声明

3. 接口设计
   - 职责单一
   - 方法语义化
   - 依赖最小化
   - 接口隔离

4. 文档注释
   - 接口说明完整
   - 方法说明清晰
   - 参数说明详细
   - 返回值说明明确

## 最佳实践

1. 仓储接口设计
```php
interface RepositoryInterface
{
    public function find($id);
    public function findAll();
    public function save($entity);
    public function delete($id);
}

interface YourRepositoryInterface extends RepositoryInterface
{
    public function findByStatus(string $status): array;
    public function findActive(): array;
    public function findInactive(): array;
}
```

2. 服务接口设计
```php
interface ServiceInterface
{
    public function create(array $data);
    public function update($id, array $data);
    public function delete($id);
}

interface YourServiceInterface extends ServiceInterface
{
    public function activate(int $id): void;
    public function deactivate(int $id): void;
    public function calculateTotal(int $id): Money;
}
```

3. 事件接口设计
```php
interface EventInterface
{
    public function getEventName(): string;
    public function getEventData(): array;
    public function getOccurredOn(): DateTime;
}

interface EventListenerInterface
{
    public function handle(EventInterface $event): void;
}
```

## 注意事项

1. 接口要简单明确
2. 避免接口污染
3. 遵循接口隔离
4. 保持向后兼容
5. 合理使用继承
6. 注意接口粒度
7. 文档要完整
8. 考虑扩展性
9. 保持一致性
10. 易于实现
