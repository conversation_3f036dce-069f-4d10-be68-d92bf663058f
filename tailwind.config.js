/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  darkMode: 'class',
  content: [
    './resources/admin/**/!(node_modules)/*.{vue,js,ts,jsx,tsx}',
    './Modules/**/views/**/!(node_modules)/*.{vue,js,ts,jsx,tsx}',
    './resources/frontend/**/!(node_modules)/*.{vue,js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      transitionProperty: {
        width: 'width',
        spacing: 'margin, padding',
      },
      colors: {
        'regal-dark': '#283046',
      },
    },
  },
  plugins: [],
}