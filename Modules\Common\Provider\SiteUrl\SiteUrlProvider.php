<?php

namespace Modules\Common\Provider\SiteUrl;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

/**
 * 网站链接提供者
 * 当链接地址更新、创建、删除时，会自动调用该 Provider
 */
class SiteUrlProvider
{
    private static array $list = [];
    private static bool $init = false;

    public static function register($provider): void
    {
        self::$list[] = $provider;
    }

    public static function get(): array
    {
        if (! self::$init) {
            self::$init = true;
            foreach (self::$list as $k => $v) {
                if (is_string($v)) {
                    self::$list[$k] = app($v);
                }
            }
        }
        return self::$list;
    }

    /**
     * 链接更新/新增触发
     * @param $biz
     * @param $url string 使用绝对路径，不要带 http 协议，如 /xxx/xxx
     * @param $title string 链接标题
     * @param $param array ['biz'=>'xxx']
     * @throws BizException
     * @example
     * SiteUrlProvider::updateBiz('xxx', bingostart_web_url('xxx/xxx'));
     * SiteUrlProvider::updateBiz('xxx', bingostart_web_url('xxx/xxx'), 'xxx');
     * SiteUrlProvider::updateBiz('xxx', bingostart_web_url('xxx/xxx'), 'xxx', ['biz'=>'xxx']);
     */
    public static function updateBiz($biz, string $url, string $title = '', array $param = []): void
    {
        $param['biz'] = $biz;
        BizException::throwsIfEmpty($url, Code::FAILED, 'SiteUrlProvider.Error -> url empty');
        foreach (self::get() as $instance) {
            /** @var AbstractSiteUrlProvider $instance */
            $instance->update($url, $title, $param);
        }
    }

    /**
     * 链接更新/新增触发
     * @param $url string 使用绝对路径，不要带 http 协议，如 /xxx/xxx
     * @param $title string 链接标题
     * @param $param array ['biz'=>'xxx']
     * @throws BizException
     * @example
     * SiteUrlProvider::update(bingostart_web_url('xxx/xxx'));
     * SiteUrlProvider::update(bingostart_web_url('xxx/xxx'), 'xxx');
     * SiteUrlProvider::update(bingostart_web_url('xxx/xxx'), 'xxx', ['biz'=>'xxx']);
     * @deprecated delete at 2024-03-14
     */
    public static function update(string $url, string $title = '', array $param = [])
    {
        BizException::throwsIfEmpty($url, Code::FAILED, 'SiteUrlProvider.Error -> url empty');
        foreach (self::get() as $instance) {
            /** @var AbstractSiteUrlProvider $instance */
            $instance->update($url, $title, $param);
        }
    }

    /**
     * 链接删除触发
     * @param $url string 使用绝对路径，不要带 http 协议，如 /xxx/xxx
     * @throws BizException
     * @example
     * SiteUrlProvider::delete(bingostart_web_url('xxx/xxx'));
     */
    public static function delete(string $url)
    {
        BizException::throwsIfEmpty($url, Code::FAILED, 'SiteUrlProvider.Error -> url empty');
        foreach (self::get() as $instance) {
            /** @var AbstractSiteUrlProvider $instance */
            $instance->delete($url);
        }
    }

}
