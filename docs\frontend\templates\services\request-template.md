# 请求模板

## 概述

请求模板定义了前端与后端API交互的标准方式。本文档提供了请求模板的标准结构和最佳实践。

## 基本结构

```typescript
// src/utils/request.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const userStore = useUserStore()
    
    // 添加token
    if (userStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${userStore.token}`
      }
    }
    
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data
    
    // 处理业务错误
    if (code !== 0) {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，跳转登录
          const userStore = useUserStore()
          userStore.logout()
          router.push('/login')
          break
          
        case 403:
          ElMessage.error('没有权限')
          break
          
        case 404:
          ElMessage.error('资源不存在')
          break
          
        case 500:
          ElMessage.error('服务器错误')
          break
          
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else {
      // 网络错误
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

export default request

// src/api/user.ts
import request from '@/utils/request'
import type { LoginParams, LoginResult } from '@/types'

export const login = (data: LoginParams) => {
  return request<LoginResult>({
    url: '/auth/login',
    method: 'post',
    data
  })
}

export const getUserInfo = () => {
  return request<UserInfo>({
    url: '/user/info',
    method: 'get'
  })
}

export const updateUserInfo = (data: Partial<UserInfo>) => {
  return request<UserInfo>({
    url: '/user/info',
    method: 'put',
    data
  })
}

// src/types/api.d.ts
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface LoginParams {
  username: string
  password: string
}

export interface LoginResult {
  token: string
  expires: number
}

export interface UserInfo {
  id: number
  username: string
  nickname: string
  avatar: string
  roles: string[]
  permissions: string[]
}
```

## 规范要求

1. 请求配置
   - 基础URL配置
   - 超时设置
   - 请求头设置
   - 拦截器配置

2. 错误处理
   - HTTP错误处理
   - 业务错误处理
   - 网络错误处理
   - 错误提示统一

3. 类型定义
   - 请求参数类型
   - 响应数据类型
   - API接口类型
   - 错误类型

4. 接口组织
   - 模块化管理
   - 接口聚合
   - 类型复用
   - 命名规范

## 最佳实践

1. 请求取消
```typescript
import { ref } from 'vue'
import type { Ref } from 'vue'
import axios, { AxiosRequestConfig, Canceler } from 'axios'

export function useRequest<T>(config: AxiosRequestConfig) {
  const data = ref<T>()
  const loading = ref(false)
  const error = ref<Error>()
  
  // 创建取消令牌
  const cancelToken = axios.CancelToken
  let cancel: Canceler
  
  const execute = async () => {
    try {
      loading.value = true
      error.value = undefined
      
      const response = await request<T>({
        ...config,
        cancelToken: new cancelToken((c) => {
          cancel = c
        })
      })
      
      data.value = response
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  const cancelRequest = () => {
    if (cancel) {
      cancel('请求已取消')
    }
  }
  
  return {
    data,
    loading,
    error,
    execute,
    cancelRequest
  }
}
```

2. 请求重试
```typescript
import axios, { AxiosError } from 'axios'

const request = axios.create({
  baseURL: '/api',
  timeout: 5000
})

request.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const config = error.config
    
    // 设置重试次数
    config.retryCount = config.retryCount ?? 0
    
    if (config.retryCount >= 3) {
      return Promise.reject(error)
    }
    
    // 重试延迟
    const delay = Math.pow(2, config.retryCount) * 1000
    
    config.retryCount++
    
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(axios(config))
      }, delay)
    })
  }
)
```

3. 请求缓存
```typescript
import { ref } from 'vue'
import type { Ref } from 'vue'

interface CacheItem<T> {
  data: T
  timestamp: number
}

const cache = new Map<string, CacheItem<any>>()

export function useCachedRequest<T>(
  key: string,
  requestFn: () => Promise<T>,
  options = { ttl: 5 * 60 * 1000 }
) {
  const data: Ref<T | undefined> = ref()
  const loading = ref(false)
  const error = ref<Error>()
  
  const execute = async (force = false) => {
    const cached = cache.get(key)
    const now = Date.now()
    
    // 检查缓存是否有效
    if (
      !force &&
      cached &&
      now - cached.timestamp < options.ttl
    ) {
      data.value = cached.data
      return
    }
    
    try {
      loading.value = true
      error.value = undefined
      
      const response = await requestFn()
      
      data.value = response
      cache.set(key, {
        data: response,
        timestamp: now
      })
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  return {
    data,
    loading,
    error,
    execute
  }
}
```

## 注意事项

1. 统一错误处理
2. 请求超时设置
3. Token管理
4. 类型安��
5. 请求取消
6. 并发控制
7. 数据缓存
8. 重试机制
9. 日志记录
10. 性能优化
