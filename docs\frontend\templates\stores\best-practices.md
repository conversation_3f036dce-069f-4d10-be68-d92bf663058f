# 状态管理最佳实践

## 概述

本文档提供了使用 Pinia 进行状态管理的最佳实践和规范指南。

## 基本原则

1. 状态设计
   - 状态最小化
   - 数据扁平化
   - 避免冗余
   - 类型安全

2. Store组织
   - 按模块拆分
   - 职责单一
   - 依赖清晰
   - 接口统一

3. 性能优化
   - 合理缓存
   - 按需加载
   - 避免频繁更新
   - 减少计算开销

4. 代码规范
   - 类型声明
   - 命名规范
   - 注释完整
   - 错误处理

## 最佳实践

1. Store模块化
```typescript
// src/stores/modules/user.ts
import { defineStore } from 'pinia'
import type { UserInfo, LoginParams } from '@/types'
import { login, getUserInfo, updateUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  // Getters
  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => 
    permissions.value.includes(permission)
  )

  // Actions
  async function login(params: LoginParams) {
    try {
      const { token: accessToken } = await login(params)
      token.value = accessToken
      await fetchUserInfo()
    } catch (error) {
      throw error
    }
  }

  async function fetchUserInfo() {
    try {
      const data = await getUserInfo()
      userInfo.value = data
      permissions.value = data.permissions
    } catch (error) {
      throw error
    }
  }

  function logout() {
    token.value = ''
    userInfo.value = null
    permissions.value = []
  }

  return {
    token,
    userInfo,
    permissions,
    isLoggedIn,
    hasPermission,
    login,
    fetchUserInfo,
    logout
  }
})

// src/stores/modules/app.ts
export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebar = reactive({
    opened: true,
    withoutAnimation: false
  })
  const device = ref<'desktop' | 'mobile'>('desktop')
  const size = ref<'default' | 'small' | 'large'>('default')

  // Actions
  function toggleSidebar(withoutAnimation?: boolean) {
    sidebar.opened = !sidebar.opened
    sidebar.withoutAnimation = withoutAnimation ?? false
  }

  function closeSidebar(withoutAnimation?: boolean) {
    sidebar.opened = false
    sidebar.withoutAnimation = withoutAnimation ?? false
  }

  function toggleDevice(val: 'desktop' | 'mobile') {
    device.value = val
  }

  function setSize(val: 'default' | 'small' | 'large') {
    size.value = val
  }

  return {
    sidebar,
    device,
    size,
    toggleSidebar,
    closeSidebar,
    toggleDevice,
    setSize
  }
})
```

2. 持久化处理
```typescript
// src/stores/plugins/persist.ts
import { PiniaPluginContext } from 'pinia'
import { watch } from 'vue'

interface PersistOptions {
  key?: string
  paths?: string[]
}

export function createPersistedState(options: PersistOptions = {}) {
  return ({ store }: PiniaPluginContext) => {
    const key = options.key ?? store.$id
    
    // 恢复状态
    const savedState = localStorage.getItem(key)
    if (savedState) {
      store.$patch(JSON.parse(savedState))
    }
    
    // 监听变化
    watch(
      () => store.$state,
      (state) => {
        const saveState = options.paths
          ? options.paths.reduce((obj, path) => {
              obj[path] = state[path]
              return obj
            }, {})
          : state
          
        localStorage.setItem(key, JSON.stringify(saveState))
      },
      { deep: true }
    )
  }
}

// src/stores/index.ts
import { createPinia } from 'pinia'
import { createPersistedState } from './plugins/persist'

const pinia = createPinia()

// 注册插件
pinia.use(createPersistedState({
  key: 'app-state',
  paths: ['user.token', 'app.size']
}))

export default pinia
```

3. 类型支持
```typescript
// src/stores/types.ts
export interface UserState {
  token: string
  userInfo: UserInfo | null
  permissions: string[]
}

export interface AppState {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
  device: 'desktop' | 'mobile'
  size: 'default' | 'small' | 'large'
}

// src/stores/modules/user.ts
import type { UserState } from '../types'

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: '',
    userInfo: null,
    permissions: []
  })
})
```

4. 组合Store
```typescript
// src/composables/useAuth.ts
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/modules/user'
import { usePermissionStore } from '@/stores/modules/permission'

export function useAuth() {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  
  const { userInfo, permissions } = storeToRefs(userStore)
  const { routes } = storeToRefs(permissionStore)
  
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }
  
  const hasRole = (role: string) => {
    return userInfo.value?.roles.includes(role) ?? false
  }
  
  return {
    userInfo,
    permissions,
    routes,
    hasPermission,
    hasRole
  }
}
```

5. 错误处理
```typescript
// src/stores/modules/error.ts
export const useErrorStore = defineStore('error', () => {
  const errors = ref<Error[]>([])
  const lastError = computed(() => errors.value[errors.value.length - 1])
  
  function addError(error: Error) {
    errors.value.push(error)
  }
  
  function clearErrors() {
    errors.value = []
  }
  
  return {
    errors,
    lastError,
    addError,
    clearErrors
  }
})

// 使用示例
const errorStore = useErrorStore()
const userStore = useUserStore()

try {
  await userStore.login(loginForm)
} catch (error) {
  errorStore.addError(error)
  ElMessage.error(error.message)
}
```

## 注意事项

1. 状态设计
   - 避免存储可以计算得到的状态
   - 避免存储组件级别的状态
   - 避免存储路由参数
   - 合理使用本地存储

2. 性能优化
   - 避免频繁的状态更新
   - 合理使用计算属性
   - 按需加载Store
   - 及时清理无用状态

3. 类型安全
   - 定义完整的类型
   - 使用类型推导
   - 避免any类型
   - 处理空值情况

4. 最佳实践
   - 统一的错误处理
   - 合理的缓存策略
   - 清晰的命名规范
   - 完整的单元测试

5. 开发建议
   - 使用组合式API
   - 遵循单一职责
   - 保持代码简洁
   - 编写详细注释
