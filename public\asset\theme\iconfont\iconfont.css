@font-face {
  font-family: "iconfont"; /* Project id 4491682 */
  src: url('iconfont.woff2?t=1712743727142') format('woff2'),
       url('iconfont.woff?t=1712743727142') format('woff'),
       url('iconfont.ttf?t=1712743727142') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-edit:before {
  content: "\e63e";
}

.icon-close:before {
  content: "\e62b";
}

.icon-link:before {
  content: "\e781";
}

.icon-sort:before {
  content: "\e75e";
}

.icon-eye:before {
  content: "\e648";
}

.icon-arrow-back:before {
  content: "\e763";
}

.icon-love:before {
  content: "\e63f";
}

.icon-love-fill:before {
  content: "\e640";
}

.icon-home:before {
  content: "\e639";
}

.icon-tel-line:before {
  content: "\e64a";
}

.icon-loc-line:before {
  content: "\e654";
}

.icon-email-line:before {
  content: "\e65e";
}

.icon-arrow:before {
  content: "\e762";
}

.icon-arrow-left:before {
  content: "\e652";
}

.icon-arrow-right:before {
  content: "\e653";
}

.icon-search:before {
  content: "\e622";
}

.icon-user:before {
  content: "\e624";
}

