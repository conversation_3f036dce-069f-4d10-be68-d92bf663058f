/**
 * 卡片模板
 * 增加媒体查询相关的响应式类，适配不同设备显示效果
 */

export const cardTemplate = `
<div data-bs-component="card" class="card-block responsive-block">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="card">
          <!-- 响应式图片处理 -->
          <img src="https://via.placeholder.com/300x200" class="card-img-top d-md-none" alt="卡片图片">
          <img src="https://via.placeholder.com/600x300" class="card-img-top d-none d-md-block d-lg-none" alt="卡片图片">
          <img src="https://via.placeholder.com/800x400" class="card-img-top d-none d-lg-block" alt="卡片图片">
          
          <div class="card-body">
            <h5 class="card-title">卡片标题</h5>
            <p class="card-text">
              <span class="d-block d-md-none">移动端简洁内容描述</span>
              <span class="d-none d-md-block">卡片内容示例文本，在平板和桌面端可以显示更多详细信息。</span>
            </p>
            <a href="#" class="btn btn-primary">
              <span class="d-none d-md-inline">了解</span>更多
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

export default cardTemplate; 