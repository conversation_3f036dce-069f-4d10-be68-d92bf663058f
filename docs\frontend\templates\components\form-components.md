# 表单组件模板

## 概述

表单组件是前端开发中最常用的组件之一。本文档提供了表单组件的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="120px"
    class="custom-form"
  >
    <el-form-item label="名称" prop="name">
      <el-input v-model="formData.name" placeholder="请输入名称" />
    </el-form-item>
    
    <el-form-item label="状态" prop="status">
      <el-select v-model="formData.status" placeholder="请选择状态">
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

interface FormData {
  name: string
  status: number
}

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<FormData>({
  name: '',
  status: 1
})

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // TODO: 调用 API 提交数据
    ElMessage.success('提交成功')
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

// 重置表单
const handleReset = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>

<style lang="scss" scoped>
.custom-form {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  
  :deep(.el-form-item__label) {
    font-weight: bold;
  }
}
</style>
```

## 规范要求

1. 组件结构
   - 使用 `<script setup>` 语法
   - 类型声明完整
   - 组件样式作用域隔离
   - 表单验证规则清晰

2. 命名规范
   - 组件名使用 PascalCase
   - 事件处理函数使用 handle 前缀
   - Props 和 Emits 类型声明
   - 变量名语义化

3. 表单验证
   - 必填字段验证
   - 格式验证规则
   - 自定义验证函数
   - 错误提示友好

4. 样式规范
   - 使用 scoped 样式
   - 样式命名规范
   - 深度选择器使用
   - 主题变量复用

## 最佳实践

1. 表单数据定义
```typescript
interface FormData {
  name: string
  email: string
  phone: string
  status: number
  remark?: string
}

const formData = reactive<FormData>({
  name: '',
  email: '',
  phone: '',
  status: 1,
  remark: ''
})
```

2. 表单验证规则
```typescript
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
})
```

## 常见问题

1. 表单验证
```typescript
// 好的实践 - 统一的验证处理
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // 提交逻辑
  } catch (error) {
    // 错误处理
  }
}

// 不好的实践 - 零散的验证处理
const handleSubmit = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      // 提交逻辑
    }
  })
}
```

2. 表单重置
```typescript
// 好的实践 - 完整的重置处理
const handleReset = () => {
  formRef.value?.resetFields()
  // 重置其他相关状态
  uploadFiles.value = []
  selectedOptions.value = []
}

// 不好的实践 - 部分重置
const handleReset = () => {
  formData.name = ''
  formData.email = ''
}
```

## 注意事项

1. 表单验证要完整
2. 错误提示要友好
3. 重置功能要彻底
4. 提交防重复
5. 样式要规范
6. 考虑表单性能
7. 注意数据类型
8. 处理异常情况
9. 保持代码整洁
10. 组件要可复用
