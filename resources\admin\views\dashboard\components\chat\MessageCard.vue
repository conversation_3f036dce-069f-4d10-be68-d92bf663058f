<template>
  <div class="message-content">
    <div class="card-container">
      <div v-if="props.message.image" class="card-image">
        <img :src="props.message.image" :alt="props.message.title" />
      </div>
      <div class="card-body">
        <h4 class="card-title">{{ props.message.title }}</h4>
        <p class="card-description">{{ props.message.description }}</p>
        <div v-if="props.message.buttons" class="card-buttons">
          <el-button
            v-for="button in props.message.buttons"
            :key="button.text"
            size="small"
            @click="handleButtonClick(button)"
          >
            {{ button.text }}
          </el-button>
        </div>
      </div>
    </div>
    <div class="message-time">{{ props.message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import type { CardMessage } from './types'

const props = defineProps<{
  message: CardMessage
}>()

const emit = defineEmits<{
  (e: 'action', action: string, payload?: any): void
}>()

const handleButtonClick = (button: any) => {
  emit('action', button.action, button.payload)
}
</script>

<script lang="ts">
export default {
  name: 'MessageCard'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 70%;
}

.card-container {
  border-radius: 12px;
  overflow: hidden;
  background: white;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-body {
  padding: 12px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: #333;
}

.card-description {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin: 0 0 10px 0;
}

.card-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.card-buttons .el-button {
  --el-button-bg-color: #fff;
  --el-button-border-color: #6BBAD2;
  --el-button-text-color: #6BBAD2;
  --el-button-hover-bg-color: #6BBAD2;
  --el-button-hover-text-color: #fff;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  opacity: 0.7;
}
</style> 