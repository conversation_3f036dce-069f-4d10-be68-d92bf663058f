# 仓储层模板

## 概述

仓储层用于封装数据访问逻辑，实现领域对象的持久化。本文档提供了仓储层的标准模板和最佳实践。

## 基本结构

### 仓储接口

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Repositories;

use Modules\YourModule\Domain\Entities\YourEntity;

interface YourRepositoryInterface
{
    public function findById(int $id): ?YourEntity;
    
    public function findByIds(array $ids): array;
    
    public function findAll(array $criteria = [], array $orderBy = [], int $limit = null, int $offset = null): array;
    
    public function save(YourEntity $entity): YourEntity;
    
    public function delete(YourEntity $entity): void;
    
    public function count(array $criteria = []): int;
}
```

### 仓储实现

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Repositories;

use Modules\YourModule\Domain\Entities\YourEntity;
use Modules\YourModule\Models\YourModel;
use Modules\YourModule\Exceptions\YourException;
use Modules\YourModule\Enums\YourErrorCode;

final class YourRepository implements YourRepositoryInterface
{
    public function findById(int $id): ?YourEntity
    {
        $model = YourModel::find($id);
        
        if (!$model) {
            return null;
        }
        
        return $this->mapToEntity($model);
    }
    
    public function findByIds(array $ids): array
    {
        $models = YourModel::whereIn('id', $ids)->get();
        
        return $models->map(fn($model) => $this->mapToEntity($model))->all();
    }
    
    public function findAll(array $criteria = [], array $orderBy = [], int $limit = null, int $offset = null): array
    {
        $query = YourModel::query();
        
        // 应用查询条件
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        
        // 应用排序
        foreach ($orderBy as $field => $direction) {
            $query->orderBy($field, $direction);
        }
        
        // 应用分页
        if ($limit) {
            $query->limit($limit);
        }
        
        if ($offset) {
            $query->offset($offset);
        }
        
        $models = $query->get();
        
        return $models->map(fn($model) => $this->mapToEntity($model))->all();
    }
    
    public function save(YourEntity $entity): YourEntity
    {
        $model = $entity->getId() 
            ? YourModel::find($entity->getId())
            : new YourModel();
            
        if (!$model) {
            throw YourException::notFound($entity->getId());
        }
        
        // 映射实体到模型
        $model->fill($this->mapToArray($entity));
        $model->save();
        
        return $this->mapToEntity($model);
    }
    
    public function delete(YourEntity $entity): void
    {
        $model = YourModel::find($entity->getId());
        
        if (!$model) {
            throw YourException::notFound($entity->getId());
        }
        
        $model->delete();
    }
    
    public function count(array $criteria = []): int
    {
        $query = YourModel::query();
        
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        
        return $query->count();
    }
    
    private function mapToEntity(YourModel $model): YourEntity
    {
        return new YourEntity(
            id: $model->id,
            name: $model->name,
            // 映射其他属性...
            createdAt: $model->created_at
        );
    }
    
    private function mapToArray(YourEntity $entity): array
    {
        return [
            'name' => $entity->getName(),
            // 映射其他属���...
        ];
    }
}
```

## 规范要求

1. 接口设计
   - 定义标准的 CRUD 方法
   - 使用类型提示
   - 返回领域实体
   - 方法命名规范

2. 实现规范
   - 实现所有接口方法
   - 处理数据映射
   - 异常处理
   - 查询条件封装

3. 数据映射
   - 模型到实体的映射
   - 实体到数组的映射
   - 保持数据一致性
   - 处理关联关系

4. 异常处理
   - 使用领域异常
   - 处理查询异常
   - 处理并发异常
   - 异常信息明确

## 最佳实践

1. 查询条件封装
```php
namespace Modules\YourModule\Domain\Criteria;

class YourCriteria
{
    public function __construct(
        private readonly array $filters = [],
        private readonly array $orderBy = [],
        private readonly ?int $limit = null,
        private readonly ?int $offset = null
    ) {
    }
    
    public function getFilters(): array
    {
        return $this->filters;
    }
    
    // 其他 getter 方法...
}

// 在仓储中使用
public function findByCriteria(YourCriteria $criteria): array
{
    return $this->findAll(
        $criteria->getFilters(),
        $criteria->getOrderBy(),
        $criteria->getLimit(),
        $criteria->getOffset()
    );
}
```

2. 关联关系处理
```php
class YourRepository implements YourRepositoryInterface
{
    public function findWithRelations(int $id): ?YourEntity
    {
        $model = YourModel::with(['relation1', 'relation2'])->find($id);
        
        if (!$model) {
            return null;
        }
        
        return $this->mapToEntityWithRelations($model);
    }
    
    private function mapToEntityWithRelations(YourModel $model): YourEntity
    {
        $entity = $this->mapToEntity($model);
        
        // 映射关联
        if ($model->relationLoaded('relation1')) {
            $entity->setRelation1($this->mapRelation1($model->relation1));
        }
        
        return $entity;
    }
}
```

## 常见问题

1. 查询优化
```php
// 好的实践 - 使用预加载
public function findAllWithRelations(array $criteria): array
{
    $query = YourModel::with(['relation1', 'relation2']);
    
    foreach ($criteria as $field => $value) {
        $query->where($field, $value);
    }
    
    $models = $query->get();
    
    return $models->map(fn($model) => $this->mapToEntityWithRelations($model))->all();
}

// 不好的实践 - N+1 问题
public function findAll(array $criteria): array
{
    $models = YourModel::where($criteria)->get();
    
    return $models->map(function($model) {
        $entity = $this->mapToEntity($model);
        $entity->setRelation1($model->relation1); // 这里会触发额外的查询
        return $entity;
    })->all();
}
```

2. 并发处理
```php
public function save(YourEntity $entity): YourEntity
{
    try {
        DB::beginTransaction();
        
        $model = YourModel::lockForUpdate()->find($entity->getId());
        
        if (!$model) {
            throw YourException::notFound($entity->getId());
        }
        
        $model->fill($this->mapToArray($entity));
        $model->save();
        
        DB::commit();
        
        return $this->mapToEntity($model);
    } catch (\Exception $e) {
        DB::rollBack();
        throw $e;
    }
}
```

## 注意事项

1. 保持仓储接口的稳定性
2. 正确处理数据映射关系
3. 注意查询性能优化
4. 处理好并发访问
5. 合理使用事务
6. 保持代码的可测试性
