@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .download-details {
    margin-bottom: 52px;

    .container {
      .df(flex-start);

      .download-content {
        padding: 30px 50px;
        background-color: #fff;
        flex-grow: 1;

        h3 {
          margin-bottom: 10px;
          font-weight: bold;
          font-size: 28px;
          color: #333;
        }

        .download-info {
          margin-bottom: 20px;
          border-bottom: 1px solid #F7F7F7;
          padding-bottom: 20px;
          .df(center);

          span {
            margin-right: 10px;
            display: block;
            font-size: 14px;
            color: #888;
          }
        }

        .rich-text {
          margin-bottom: 58px;

          p {
            margin-bottom: 10px;
            font-size: 16px;
            line-height: 1.75;
          }
        }

        .collection-btn {
          margin-bottom: 30px;
          .df(center, center);

          .btn-box {
            .btn-radius(50px, 10px, 16px, #383838, #F7F7F7, #aaa, #fff);
          }
        }

        .tag-list {
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
          .df(center);

          .tag-text {
            color: #333;
            font-size: 14px;
          }

          .tag-item {
            margin-left: 10px;
            border: 1px solid #ff9600;
            .btn-radius(50px, 5px, 14px, #ff9600, transparent, #ff9600, #fff);
          }
        }

        .more {
          margin-top: 30px;
          .df(center);

          .more-news {
            flex-grow: 1;

            .prev-news {
              margin-bottom: 10px;
              color: #333;
              font-size: 14px;
              line-height: 1.71;
              .df(center);

              a {
                color: #333;
                line-height: 1.71;
              }
            }

            .next-news {
              font-size: 14px;
              color: #333;
              line-height: 1.71;
              .df(center);

              span {
                color: #888;
                line-height: 1.71;
              }
            }
          }

          .back-list {
            .btn-box {
              .btn-radius(50px, 12px, 14px, #fff, #ff9600, #FCB319);
            }
          }
        }
      }

      .side {
        padding-left: 30px;
        width: calc(25% + 30px);
        flex-shrink: 0;

        .tit {
          margin-bottom: 10px;
          line-height: 1.11;
          color: #333;
          font-size: 18px;
          .df(stretch);

          &::before {
            margin-right: 15px;
            content: '';
            display: block;
            width: 5px;
            background-color: #ff9600;
          }
        }

        .download-address {
          margin-bottom: 20px;
          padding: 20px;
          background-color: #fff;

          .address-list {
            .download-item {
              .df(center);

              .btn-radius(8px, 15px, 25px, #fff, #ff9600, #FCB319);
              margin-bottom: 10px;
              cursor: pointer;
            }
          }

          .download-num {
            margin-top: 30px;
            .df(center);

            .item {
              .df(center, center);
              flex-grow: 1;
              font-size: 14px;
              color: #888;

              .iconfont {
                margin-right: 5px;
                display: block;
              }
            }
          }
        }

        .tag {
          padding: 20px;
          background-color: #fff;

          .tag-list {
            .df(center);
            flex-wrap: wrap;

            .tag-item {
              margin-bottom: 10px;
              margin-right: 10px;
              border: 1px solid #ff9600;
              .btn-radius(50px, 5px, 12px, #ff9600, transparent, #ff9600, #fff);
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}