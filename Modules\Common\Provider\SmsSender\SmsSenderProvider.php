<?php

namespace Modules\Common\Provider\SmsSender;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

/**
 * Class SmsSenderProvider
 * @package Modules\Common\Provider\SmsSender
 */
class SmsSenderProvider
{
    /**
     * @var AbstractSmsSenderProvider[]
     */
    private static array $instances = [
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractSmsSenderProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    /**
     * @param $name
     * @return AbstractSmsSenderProvider
     * @throws BizException
     */
    public static function get($name)
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }
        BizException::throws(Code::FAILED, '没有找到SmsSenderProvider');
    }

    /**
     * @return bool
     * @since 1.7.0
     */
    public static function hasProvider(): bool
    {
        $provider = app()->config->get('SmsSenderProvider');
        return ! empty($provider);
    }
}
