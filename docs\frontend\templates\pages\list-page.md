# 列表页面模板

## 概述

列表页面是系统中最常见的数据展示界面，通常包含搜索、筛选、分页等功能。本文档提供了列表页面的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <div class="list-page">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        @submit.prevent="handleSearch"
      >
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入关键词"
            clearable
            @clear="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            @clear="handleSearch"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期" prop="dateRange">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card">
      <template #header>
        <div class="card-header">
          <span>数据列表</span>
          <div class="right">
            <el-button type="primary" @click="handleAdd">新增</el-button>
            <el-button
              type="danger"
              :disabled="!selectedIds.length"
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
            <el-button @click="handleExport">导出</el-button>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="title" label="标题" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">
              {{ row.title }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                type="primary"
                link
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useListStore } from '@/stores/list'
import type { ListItem, SearchForm } from '@/types'

// 路由
const router = useRouter()

// Store
const listStore = useListStore()

// 数据
const loading = ref(false)
const tableData = ref<ListItem[]>([])
const selectedIds = ref<number[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = ref<SearchForm>({
  keyword: '',
  status: undefined,
  dateRange: []
})

// 选项数据
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchForm.value,
      startDate: searchForm.value.dateRange?.[0],
      endDate: searchForm.value.dateRange?.[1]
    }
    const { data, total: totalCount } = await listStore.getList(params)
    tableData.value = data
    total.value = totalCount
  } catch (error) {
    console.error(error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  page.value = 1
  loadData()
}

const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    status: undefined,
    dateRange: []
  }
  handleSearch()
}

const handleSelectionChange = (selection: ListItem[]) => {
  selectedIds.value = selection.map(item => item.id)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  page.value = val
  loadData()
}

const handleAdd = () => {
  router.push('/form')
}

const handleEdit = (row: ListItem) => {
  router.push(`/form/${row.id}`)
}

const handleView = (row: ListItem) => {
  router.push(`/detail/${row.id}`)
}

const handleDelete = async (row: ListItem) => {
  try {
    await ElMessageBox.confirm('确认删除该记录吗？', '提示', {
      type: 'warning'
    })
    await listStore.deleteItem(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    console.error(error)
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedIds.value.length} 条记录吗？`,
      '提示',
      { type: 'warning' }
    )
    await listStore.batchDelete(selectedIds.value)
    ElMessage.success('批量删除成功')
    loadData()
  } catch (error) {
    console.error(error)
  }

}

const handleExport = async () => {
  try {
    loading.value = true
    await listStore.exportData(searchForm.value)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error(error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.list-page {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .operation-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
```

## 规范要求

1. 页面结构
   - 搜索区域
   - 操作区域
   - 表格区域
   - 分页区域

2. 数据管理
   - 使用 Pinia Store
   - 响应式数据
   - 分页处理
   - 搜索条件

3. 功能实现
   - 条件搜索
   - 数据筛选
   - 批量操作
   - 导入导出

4. 交互处理
   - 加载状态
   - 错误处理
   - 操作确认
   - 结果反馈

## 最佳实践

1. Store 定义
```typescript
// stores/list.ts
export const useListStore = defineStore('list', () => {
  const getList = async (params: ListParams) => {
    const response = await api.getList(params)
    return response.data
  }
  
  const deleteItem = async (id: number) => {
    return await api.deleteItem(id)
  }
  
  const batchDelete = async (ids: number[]) => {
    return await api.batchDelete(ids)
  }
  
  return { getList, deleteItem, batchDelete }
})
```

2. 类型定义
```typescript
// types/list.ts
export interface ListItem {
  id: number
  title: string
  status: number
  created_at: string
  [key: string]: any
}

export interface SearchForm {
  keyword?: string
  status?: number
  dateRange?: string[]
}

export interface ListParams extends SearchForm {
  page: number
  pageSize: number
  startDate?: string
  endDate?: string
}
```

## 常见问题

1. 表格列配置
```vue
<!-- 自定义列 -->
<el-table-column label="操作" width="200">
  <template #default="{ row }">
    <el-button-group>
      <el-button @click="handleEdit(row)">编辑</el-button>
      <el-button @click="handleDelete(row)">删除</el-button>
    </el-button-group>
  </template>
</el-table-column>

<!-- 格式化列 -->
<el-table-column prop="status" label="状态">
  <template #default="{ row }">
    <el-tag :type="row.status ? 'success' : 'danger'">
      {{ row.status ? '启用' : '禁用' }}
    </el-tag>
  </template>
</el-table-column>
```

2. 搜索表单处理
```typescript
const handleSearch = () => {
  // 重置页码
  page.value = 1
  // 处理日期范围
  const params = {
    ...searchForm.value,
    startDate: searchForm.value.dateRange?.[0],
    endDate: searchForm.value.dateRange?.[1]
  }
  loadData(params)
}
```

## 注意事项

1. 合理使用加载状态
2. 处理空数据状态
3. 优化搜索性能
4. 实现批量操作确认
5. 添加必要的权限控制
6. 注意表格性能优化
</rewritten_file>
