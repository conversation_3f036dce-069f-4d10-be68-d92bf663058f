.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .news-details {
  margin-bottom: 52px;
}
.bwms-page .news-details .container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content {
  padding: 30px 50px;
  background-color: #fff;
  flex-grow: 1;
}
.bwms-page .news-details .container .news-content h3 {
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 28px;
  color: #333;
}
.bwms-page .news-details .container .news-content .news-info {
  margin-bottom: 20px;
  border-bottom: 1px solid #F7F7F7;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content .news-info span {
  margin-right: 10px;
  display: block;
  font-size: 14px;
  color: #888;
}
.bwms-page .news-details .container .news-content .rich-text {
  margin-bottom: 58px;
}
.bwms-page .news-details .container .news-content .rich-text p {
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 1.75;
}
.bwms-page .news-details .container .news-content .collection-btn {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .news-details .container .news-content .collection-btn .btn-box {
  border-radius: 50px;
  padding: 10px 16px;
  color: #383838;
  background-color: #F7F7F7;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .news-details .container .news-content .collection-btn .btn-box .iconfont + span {
  margin-left: 5px;
}
.bwms-page .news-details .container .news-content .collection-btn .btn-box span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .news-details .container .news-content .collection-btn .btn-box span {
  display: block;
  line-height: 1.5;
}
.bwms-page .news-details .container .news-content .collection-btn .btn-box:hover {
  background-color: #aaa;
  color: #fff;
}
.bwms-page .news-details .container .news-content .tag-list {
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content .tag-list .tag-text {
  color: #333;
  font-size: 14px;
}
.bwms-page .news-details .container .news-content .tag-list .tag-item {
  margin-left: 10px;
  border: 1px solid #ff9600;
  border-radius: 50px;
  padding: 5px 14px;
  color: #ff9600;
  background-color: transparent;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .news-details .container .news-content .tag-list .tag-item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .news-details .container .news-content .tag-list .tag-item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .news-details .container .news-content .tag-list .tag-item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .news-details .container .news-content .tag-list .tag-item:hover {
  background-color: transparent;
  color: #fff;
}
.bwms-page .news-details .container .news-content .more {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content .more .more-news {
  flex-grow: 1;
}
.bwms-page .news-details .container .news-content .more .more-news .prev-news {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
  line-height: 1.71;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content .more .more-news .prev-news a {
  color: #333;
  line-height: 1.71;
}
.bwms-page .news-details .container .news-content .more .more-news .next-news {
  font-size: 14px;
  color: #333;
  line-height: 1.71;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .news-content .more .more-news .next-news span {
  color: #888;
  line-height: 1.71;
}
.bwms-page .news-details .container .news-content .more .back-list .btn-box {
  border-radius: 50px;
  padding: 12px 14px;
  color: #fff;
  background-color: #ff9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .news-details .container .news-content .more .back-list .btn-box .iconfont + span {
  margin-left: 5px;
}
.bwms-page .news-details .container .news-content .more .back-list .btn-box span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .news-details .container .news-content .more .back-list .btn-box span {
  display: block;
  line-height: 1.5;
}
.bwms-page .news-details .container .news-content .more .back-list .btn-box:hover {
  background-color: #FCB319;
  color: #fff;
}
.bwms-page .news-details .container .side {
  padding-left: 30px;
  width: calc(25% + 30px);
  flex-shrink: 0;
}
.bwms-page .news-details .container .side .tit {
  margin-bottom: 10px;
  line-height: 1.11;
  color: #333;
  font-size: 18px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .news-details .container .side .tit::before {
  margin-right: 15px;
  content: '';
  display: block;
  width: 5px;
  background-color: #ff9600;
}
.bwms-page .news-details .container .side .random-news {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
}
.bwms-page .news-details .container .side .random-news .random-list .news-item {
  padding-top: 5px;
  padding-bottom: 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .news-details .container .side .random-news .random-list .news-item::before {
  content: "";
  margin-right: 12px;
  border-radius: 50%;
  width: 6px;
  height: 6px;
  background-color: #ccc;
  display: block;
  flex-shrink: 0;
}
.bwms-page .news-details .container .side .random-news .random-list .news-item a {
  color: #6E6E6E;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
  line-height: 1.71;
  transition: color 0.35s ease-in-out;
}
.bwms-page .news-details .container .side .random-news .random-list .news-item a:hover {
  color: #ff9600;
}
.bwms-page .news-details .container .side .product-sell {
  padding: 20px;
  background-color: #fff;
}
.bwms-page .news-details .container .side .product-sell .product-list .item {
  margin-bottom: 20px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .news-details .container .side .product-sell .product-list .item .pic {
  width: 100px;
}
.bwms-page .news-details .container .side .product-sell .product-list .item .pic img {
  transform: scale(1);
  transition: transform 0.35s ease-in-out;
}
.bwms-page .news-details .container .side .product-sell .product-list .item .product-name {
  padding-left: 15px;
  color: #6E6E6E;
  line-height: 1.5;
  font-size: 16px;
}
.bwms-page .news-details .container .side .product-sell .product-list .item:hover .pic img {
  transform: scale(1.1);
}
