export const landingPageTemplate = `
<div class="landing-page responsive-block">
  <!-- Schedule Demo Section -->
  <div data-bs-component="hero" class="py-5 text-white bg-dark hero-section">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h1 class="mb-4 display-4 fw-bold">Schedule a demo with us</h1>
            <p class="mb-4 lead">Experience our solution in action. Schedule a demo with one of our team members and we'll show you how to what your prospects can expect to learn.</p>
          </div>
          <div class="mb-4 bullet-points">
            <p><i class="fas fa-check me-2"></i> Type #1: Explain what the demo will cover</p>
            <p><i class="fas fa-check me-2"></i> Type #2: Explain what the demo will cover</p>
            <p><i class="fas fa-check me-2"></i> Type #3: Explain what the demo will cover</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Partners Section -->
  <div data-bs-component="partners" class="py-4 bg-dark">
    <div class="container">
      <div class="mb-4 text-center">
        <p class="text-muted" style="color: white;">Trusted by leading companies</p>
      </div>
      <div class="row align-items-center justify-content-center">
        <div class="mb-3 col-6 col-md-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" class="img-fluid" alt="Partner logo">
        </div>
        <div class="mb-3 col-6 col-md-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/spectroxium-light.webp" class="img-fluid" alt="Partner logo">
        </div>
        <div class="mb-3 col-6 col-md-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" class="img-fluid" alt="Partner logo">
        </div>
        <div class="mb-3 col-6 col-md-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" class="img-fluid" alt="Partner logo">
        </div>
        <div class="mb-3 col-6 col-md-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/spectroxium-light.webp" class="img-fluid" alt="Partner logo">
        </div>
      </div>
    </div>
  </div>

  <!-- Features Section -->
  <div data-bs-component="features" class="py-5">
    <div class="container">
      <!-- Feature 1 -->
      <div class="mb-5 row align-items-center">
        <div class="col-lg-6">
          <img src="https://new-bwms.bingo-test.com/tiptap/value-prop-one.webp" class="img-fluid rounded-4" alt="Increase reach">
        </div>
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Increase reach and engagement</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>

      <!-- Feature 2 -->
      <div class="mb-5 row align-items-center">
        <div class="col-lg-6 order-lg-2">
          <img src="https://new-bwms.bingo-test.com/tiptap/value-prop-two.webp" class="img-fluid rounded-4" alt="Optimize Performance">
        </div>
        <div class="col-lg-6 order-lg-1">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Optimize Performance and ROI</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>

      <!-- Feature 3 -->
      <div class="row align-items-center">
        <div class="col-lg-6">
          <img src="https://new-bwms.bingo-test.com/tiptap/value-prop-three.webp" class="img-fluid rounded-4" alt="Streamline Workflow">
        </div>
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Streamline Workflow and Collaboration</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Testimonials Section -->
  <div data-bs-component="testimonials" class="py-5 bg-light">
    <div class="container">
      <div class="testimonial-slider">
        <div class="testimonial-slide active">
          <div class="row align-items-center">
            <div class="text-center col-lg-3">
              <img src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="mb-3 rounded-circle" width="120" alt="Testimonial">
              <h5 class="mb-1">Neil Kumar</h5>
              <p class="text-muted">VP of Marketing @ Lyralytics</p>
            </div>
            <div class="col-lg-9">
              <p class="lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
              <a href="#" class="text-decoration-none">Read case study →</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- FAQ Section -->
  <div data-bs-component="faq" class="py-5">
    <div class="container">
      <div class="accordion" id="faqAccordion">
        <div class="accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
              What is the dress code for the company event or conference?
            </button>
          </h2>
          <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Business casual attire is recommended for all company events and conferences.
            </div>
          </div>
        </div>
        <div class="accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
              Is there transportation provided to the event venue?
            </button>
          </h2>
          <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Yes, shuttle service will be provided from designated pickup points.
            </div>
          </div>
        </div>
        <!-- Add more FAQ items as needed -->
      </div>
    </div>
  </div>

  <!-- CTA Section -->
  <div data-bs-component="cta" class="py-5 text-white bg-primary">
    <div class="container text-center">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <h2 class="mb-4 display-6 fw-bold">Join Elevate today and start elevating your marketing game</h2>
          <button class="px-5 btn btn-light btn-lg rounded-pill">Get Started</button>
        </div>
      </div>
    </div>
  </div>

  <style>
    .landing-page {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    .hero-section {
      background-color: #1a1a1a;
      position: relative;
      overflow: hidden;
    }

    .bullet-points p {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
    }

    .bullet-points i {
      color: #00ff00;
    }

    .partners img {
      max-height: 40px;
      width: auto;
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }

    .partners img:hover {
      opacity: 1;
    }

    .testimonial-slider {
      position: relative;
      padding: 2rem;
      background: white;
      border-radius: 1rem;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .accordion-button:not(.collapsed) {
      background-color: #f8f9fa;
      color: #0d6efd;
    }

    .accordion-button:focus {
      box-shadow: none;
      border-color: rgba(0,0,0,.125);
    }

    @media (max-width: 768px) {
      .hero-section {
        text-align: center;
      }

      .bullet-points {
        text-align: left;
      }
    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Add any necessary JavaScript for interactivity
      const accordionButtons = document.querySelectorAll('.accordion-button');
      
      accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
          const isCollapsed = this.classList.contains('collapsed');
          
          // Remove active state from all buttons
          accordionButtons.forEach(btn => {
            btn.classList.add('collapsed');
            btn.setAttribute('aria-expanded', 'false');
          });
          
          // Set active state for clicked button
          if (isCollapsed) {
            this.classList.remove('collapsed');
            this.setAttribute('aria-expanded', 'true');
          }
        });
      });
    });
  </script>
</div>
`
