#!/bin/bash
# 使用说明：
# 1. ./prod.sh install 安装：执行 composer install。
# 2. ./prod.sh update 更新：执行 composer update（默认操作）。
# 3. ./prod.sh build：执行 yarn install 以更新前端依赖，并执行 yarn build。
# 4. ./prod.sh frontend：切换到 resources/frontend 目录并执行 yarn build，然后返回原始目录。
# 5. ./prod.sh asset <module_name>：执行资源迁移命令并指定模块名称。
# 6. ./prod.sh routelist：列出所有路由信息。
# 7. ./prod.sh modules：生成和更新所有模块的package.json。
# 8. ./prod.sh module <module_name>：生成指定模块的package.json。
# 9. ./prod.sh init：初始化部署，设置目录权限等。
# 10. ./prod.sh check：检查系统环境要求：
#     - Node.js >= 20.0.0
#     - PHP >= 8.2
#     - 必需的 PHP 扩展（bcmath, gd, curl 等）
#     使用 ✅ 表示符合要求，❌ 表示不符合要求

# 定义需要执行 git pull 的命令
NEED_PULL=("install" "update" "build")

# 生成模块package.json的函数
generate_module_package() {
  local module_name=$1
  local module_path="Modules/$module_name"
  local main_config="resources/admin/main.json"

  # 检查模块目录是否存在
  if [ ! -d "$module_path" ]; then
    echo "模块 $module_name 不存在"
    return 1
  fi

  # 检查main.json是否存在
  if [ ! -f "$main_config" ]; then
    echo "main.json 配置文件不存在"
    return 1
  fi

  # 使用jq从main.json提取模块配置并生成package.json
  if command -v jq >/dev/null 2>&1; then
    # 提取framework依赖
    local framework_deps=$(jq -r '.framework.dependencies' "$main_config")
    
    # 提取模块特定依赖
    local module_deps="{}"
    if jq -e ".modules.$module_name" "$main_config" >/dev/null; then
      module_deps=$(jq -r ".modules.$module_name.dependencies // {}" "$main_config")
    fi

    # 生成package.json
    jq -n \
      --arg name "@bwms/$module_name-module" \
      --arg version "1.0.0" \
      --argjson deps "$module_deps" \
      --argjson peer_deps "$framework_deps" \
      '{
        "name": $name,
        "version": $version,
        "private": true,
        "type": "module",
        "dependencies": $deps,
        "peerDependencies": $peer_deps
      }' > "$module_path/package.json"

    echo "已生成 $module_name 模块的 package.json"
  else
    echo "请安装jq工具以处理JSON文件"
    return 1
  fi
}

# 生成所有模块的package.json
generate_all_modules() {
  echo "开始生成所有模块的package.json..."
  
  # 遍历Modules目录下的所有模块
  for module in Modules/*/; do
    module=${module%/}  # 移除尾部的斜杠
    module=${module#Modules/}  # 移除前缀路径
    generate_module_package "$module"
  done

  echo "所有模块的package.json生成完成"
}

# 检查是否需要执行 git pull
should_pull=false
for cmd in "${NEED_PULL[@]}"; do
  if [ "$cmd" = "$1" ]; then
    should_pull=true
    break
  fi
done

if [ "$should_pull" = true ]; then
  # 检查当前分支的追踪状态
  CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
  TRACKING_BRANCH=$(git rev-parse --abbrev-ref --symbolic-full-name @{u} 2>/dev/null)
  
  if [ $? -ne 0 ]; then
    echo "设置分支追踪关系..."
    git branch --set-upstream-to=origin/$CURRENT_BRANCH $CURRENT_BRANCH
    if [ $? -ne 0 ]; then
      echo "无法自动设置分支追踪关系，请手动执行以下命令之一："
      echo "1. git branch --set-upstream-to=origin/master master"
      echo "2. git branch --set-upstream-to=origin/main main"
      echo "3. 或者指定正确的远程分支：git branch --set-upstream-to=origin/<branch> $CURRENT_BRANCH"
      exit 1
    fi
  fi

  # 执行 git pull 更新代码
  echo "执行 git pull 更新代码..."
  git pull
  if [ $? -ne 0 ]; then
    echo "git pull 失败，尝试执行 git reset --hard 还原代码..."
    git reset --hard
    git pull
    chmod +x ./prod.sh
    if [ $? -ne 0 ]; then
      echo "代码更新失败，请手动检查。"
      exit 1
    fi
  fi

  # 定义 composer.json 文件路径
  COMPOSER_FILE="composer.json"

  # 使用 sed 删除 repositories 配置
  sed -i '/"repositories"/,/]/d' "$COMPOSER_FILE"

  echo "已从 composer.json 中删除 repositories 配置。"
fi

# 允许在 root 环境下运行 composer 插件
export COMPOSER_ALLOW_SUPERUSER=1

# 检查第一个参数并根据参数执行操作
case "$1" in
  "check")
    echo "开始检查系统环境..."
    
    # 检查 Node.js 版本
    echo "检查 Node.js 版本..."
    if command -v node >/dev/null 2>&1; then
      NODE_VERSION=$(node -v | cut -d'v' -f2)
      if [ "$(echo "$NODE_VERSION 20.0.0" | awk '{print ($1 >= $2)}')" -eq 1 ]; then
        echo "✅ Node.js 版本 $NODE_VERSION 符合要求 (>= 20.0.0)"
      else
        echo "❌ Node.js 版本 $NODE_VERSION 不符合要求，请安装 20.0.0 或更高版本"
        exit 1
      fi
    else
      echo "❌ 未安装 Node.js"
      exit 1
    fi

    # 检查 PHP 版本
    echo "检查 PHP 版本..."
    if command -v php >/dev/null 2>&1; then
      PHP_VERSION=$(php -v | grep -oE '^PHP [0-9]+\.[0-9]+' | awk '{print $2}')
      if [ "$(echo "$PHP_VERSION 8.2" | awk '{print ($1 >= $2)}')" -eq 1 ]; then
        echo "✅ PHP 版本 $PHP_VERSION 符合要求 (>= 8.2)"
      else
        echo "❌ PHP 版本 $PHP_VERSION 不符合要求，请安装 8.2 或更高版本"
        exit 1
      fi
    else
      echo "❌ 未安装 PHP"
      exit 1
    fi

    # 定义所需的 PHP 扩展
    REQUIRED_EXTENSIONS=(
      "bcmath" "bz2" "calendar" "core" "ctype" "curl" "date" "dba" "dom" "exif"
      "ffi" "fileinfo" "filter" "ftp" "gd" "gettext" "gmp" "hash" "iconv"
      "imagick" "intl" "json" "ldap" "libxml" "mbstring" "mysqli" "mysqlnd"
      "odbc" "openssl" "pcntl" "pcre" "pdo" "pdo_dblib" "pdo_mysql" "pdo_odbc"
      "pdo_pgsql" "pdo_sqlite" "pgsql" "phar" "posix" "pspell" "random"
      "readline" "reflection" "session" "shmop" "simplexml" "soap" "sockets"
      "sodium" "spl" "sqlite3" "standard" "sysvmsg" "sysvsem" "sysvshm" "tidy"
      "tokenizer" "xml" "xmlreader" "xmlwriter" "xsl" "zend opcache" "zip" "zlib"
    )

    # 检查 PHP 扩展
    echo "检查 PHP 扩展..."
    MISSING_EXTENSIONS=()
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
      if ! php -m | grep -iq "^${ext}$"; then
        MISSING_EXTENSIONS+=("$ext")
      fi
    done

    if [ ${#MISSING_EXTENSIONS[@]} -eq 0 ]; then
      echo "✅ 所有必需的 PHP 扩展都已安装"
    else
      echo "❌ 缺少以下 PHP 扩展："
      printf '%s\n' "${MISSING_EXTENSIONS[@]}"
      exit 1
    fi

    echo "✅ 环境检查完成，所有要求都满足！"
    ;;
  "init")
    echo "开始初始化部署..."
    
    # 设置基本权限，只处理关键目录
    echo "设置目录权限..."
    
    echo "1. 设置 storage 目录权限..."
    find storage -type d -exec chmod 777 {} \;
    find storage -type f -exec chmod 666 {} \;
    chown -R www:www storage
    
    echo "2. 设置 bootstrap/cache 目录权限..."
    mkdir -p bootstrap/cache
    chmod -R 777 bootstrap/cache
    chown -R www:www bootstrap/cache
    
    echo "3. 创建并设置框架目录权限..."
    mkdir -p storage/framework/{sessions,views,cache,testing}
    mkdir -p storage/framework/cache/data
    find storage/framework -type d -exec chmod 777 {} \;
    find storage/framework -type f -exec chmod 666 {} \;
    chown -R www:www storage/framework
    
    echo "4. 创建并设置日志目录权限..."
    mkdir -p storage/logs
    chmod -R 777 storage/logs
    chown -R www:www storage/logs
    
    echo "5. 设置其他关键目录权限..."
    chmod -R 755 bootstrap
    chmod -R 755 config
    chmod -R 755 resources
    chmod -R 755 routes
    chmod -R 755 app
    chmod -R 755 public
    
    echo "6. 设置关键文件权限..."
    chmod 644 .env* 2>/dev/null || true
    chmod 644 composer.json 2>/dev/null || true
    chmod 644 composer.lock 2>/dev/null || true
    chmod 755 artisan
    
    # 删除现有的存储链接
    echo "7. 重置存储链接..."
    rm -f public/storage
    
    # 创建环境文件（如果不存在）
    echo "8. 检查环境文件..."
    if [ ! -f ".env" ]; then
      cp .env.example .env
      echo "已创建 .env 文件，请记得更新配置"
    fi
    
    echo "9. 生成应用密钥..."
    php artisan key:generate
    
    echo "10. 创建存储链接..."
    php artisan storage:link
    
    echo "11. 清除缓存..."
    php artisan config:clear
    php artisan cache:clear
    php artisan view:clear
    
    # 最后再次确保权限
    echo "12. 最终确认权限..."
    find storage -type d -exec chmod 777 {} \;
    find storage -type f -exec chmod 666 {} \;
    chmod -R 777 bootstrap/cache
    
    echo "初始化部署完成。"
    echo "请确保："
    echo "1. 配置 .env 文件"
    echo "2. 配置数据库并运行迁移"
    echo "3. 配置 Web 服务器（Nginx/Apache）"
    ;;
  "install")
    composer install --no-dev --no-interaction
    echo "composer install --no-dev 已完成。"
    ;;
  "build")
    # 更新前端依赖并执行构建
    echo "执行 yarn install 以更新前端依赖..."
    # 先生成和更新所有模块的 package.json
    ./prod.sh modules
    
    # 设置 node_modules 权限
    echo "设置 node_modules 权限..."
    chmod -R 755 node_modules
    find node_modules/.bin -type f -exec chmod +x {} \;
    
    # 安装所有依赖
    yarn
    
    # 再次确保执行权限
    echo "确保 node_modules/.bin 执行权限..."
    chmod -R 755 node_modules/.bin
    
    echo "后台前端依赖更新完成，开始执行 yarn build..."
    NODE_OPTIONS="--max-old-space-size=8192" yarn build
    echo "yarn build 已完成。"
    ;;
  "frontend")
    # 保存当前目录并切换到 resources/frontend 目录执行 yarn build，然后返回原目录
    echo "切换到 resources/frontend 目录，开始执行 yarn build..."
    pushd resources/frontend/ || { echo "未能找到 resources/frontend 目录"; exit 1; }
    yarn
    echo "前台前端依赖更新完成，开始执行 yarn build..."
    NODE_OPTIONS="--max-old-space-size=8192" yarn build
    echo "yarn build 已完成。"
    popd > /dev/null || exit
    ;;
  "asset")
    # 检查是否提供了模块名称
    if [ -z "$2" ]; then
      echo "请提供模块名称。示例：./prod.sh asset <module_name>"
      exit 1
    fi
    # 执行资源迁移命令并指定模块名称
    MODULE_NAME=$2
    echo "执行资源迁移命令 php -d memory_limit=-1 artisan bingo:asset:migrate $MODULE_NAME..."
    php -d memory_limit=-1 artisan bingo:asset:migrate "$MODULE_NAME"
    echo "资源迁移完成。"
    ;;
  "routelist")
    # 执行路由列表命令
    echo "列出所有路由信息..."
    php -d memory_limit=-1 artisan route:list
    echo "路由列表生成完成。"
    ;;
  "modules")
    # 生成所有模块的package.json
    generate_all_modules
    ;;
  "module")
    # 检查是否提供了模块名称
    if [ -z "$2" ]; then
      echo "请提供模块名称。示例：./prod.sh module <module_name>"
      exit 1
    fi
    generate_module_package "$2"
    ;;
  *)
    # 默认操作是 composer update
    composer update --no-dev --no-interaction
    echo "composer update --no-dev 已完成。（默认操作）"

    echo '迁移模块资产文件到公共目录'
    php artisan bingo:asset:migrate
    
    echo '迁移数据表'
    php artisan bingo:migrate

    echo "重新加载工作进程..."
    php artisan octane:reload

    echo "清除缓存..."
    php artisan config:clear
    php artisan cache:clear
    php artisan view:clear
    
    ;;
esac
