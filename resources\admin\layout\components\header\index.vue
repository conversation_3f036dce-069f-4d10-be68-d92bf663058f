<template>
  <div class="header-box">
    <div class="header-content">
      <div class="flex items-center header-content-left">
        <!-- 左侧 Logo -->
        <div class="logo-container" @click="goHome">
          <img :src="$asset('Dashboard/Asset/logos.png')" alt="Logo" />
        </div>
      </div>
      
      <!-- 右侧功能区 -->
      <div class="flex items-center">
        <!-- 消息通知 -->
        <MessageBox />

        <!-- 设置下拉菜单 -->
        <el-dropdown trigger="click" style="margin-left: 16px;">
          <div class="flex justify-center items-center w-10 h-10 rounded-full cursor-pointer hover:bg-gray-100">
            <font-awesome-icon :icon="['fas', 'user']" class="text-gray-600" />
          </div>

          <template #dropdown>
            <el-dropdown-menu class="user-dropdown">
              <el-dropdown-item @click="openProfileDrawer">
                {{ $t('system.profile') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 用户资料抽屉 -->
    <div class="drawer-box" :class="{ open: showProfileDrawer }">
      <el-icon size="22" color="#202020" class="close-icon" @click="showProfileDrawer = false">
        <Close />
      </el-icon>
      <Profile @refresh-dashboard="refreshDashboard"></Profile>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import useEventBus from '/admin/support/eventBus'
import MessageBox from './message.vue'
import Profile from './profile.vue'

const { t } = useI18n()
const router = useRouter()
const showProfileDrawer = ref(false)
const { $emit } = useEventBus()

const openProfileDrawer = () => {
  showProfileDrawer.value = true
}

const goHome = () => {
  router.push('/dashboard')
}

// 添加 refreshDashboard 方法
const refreshDashboard = () => {
  // 发送事件通知 dashboard 组件刷新数据
  $emit('refresh-dashboard')
}
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  width: 106px;
  height: 50px;
  cursor: pointer;
  flex-shrink: 0;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.header-content-left {
  flex: 1;
}

.search-container {
  width: 45%;
  max-width: 596px;
  height: 45px;
  flex-shrink: 0;
  background: #F2F5F8;
  border-radius: 10px;
  opacity: 1;
  margin-left: 16%;
}

.drawer-box {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 99;
  transform: translateX(100%);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  border-radius: 30px 0 0 0;
  padding: 30px;
  background-color: #fff;
  height: calc(100vh - 100px);
  max-width: 375px;
  width: 100%;
  transition: transform 0.35s ease-in-out;

  &.open {
    transform: translateX(0);
  }

  .close-icon {
    position: absolute;
    right: 30px;
    top: 30px;
    cursor: pointer;
    transform: rotate(0);
    transition: transform 0.35s ease-in-out;

    &:hover {
      transform: rotate(180deg);
    }
  }
}
</style>
