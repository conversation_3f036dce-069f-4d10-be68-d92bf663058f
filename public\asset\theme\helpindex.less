@import "./variable.less";

.bwms-page {
  .qiuck-nav {
    margin-top: 5px;

    .list {
      .item {
        --bs-gutter-x: 20px;
        --bs-gutter-y: 20px;

        .box {
          .df(center);

          box-shadow: 0 0 20px rgba(0, 0, 0, .05);
          border-radius: 10px;
          padding: 10px;
          background-color: #fff;

          .pic {
            max-width: 40px;
          }

          .text-box {
            padding-left: 20px;
            flex-grow: 1;

            h6 {
              color: @bodyColor;
              font-size: 20px;
              line-height: 1.5;
              font-weight: normal;
            }

            p {
              .vertical(1);
              color: #c4cfdb;
              font-size: @bodySize;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }

  .link-page {
    margin-top: 5px;

    .list {
      .item {
        --bs-gutter-x: 20px;
        --bs-gutter-y: 20px;

        .box {
          box-shadow: 0 0 20px rgba(0, 0, 0, .05);
          border-radius: 10px;
          padding: 10px;
          background-color: #fff;
          height: 100%;

          .tit {
            padding: 10px;
            font-size: 20px;
            color: @bodyColor;
          }

          .link-list {
            .df();
            flex-wrap: wrap;

            .link-item {
              margin: 5px;
              box-shadow: 0 0 20px rgba(0, 0, 0, .05);
              border-radius: 5px;
              background-color: #fff;
              width: calc(50% - 10px);
              font-size: @bodySize;
              line-height: 2;
              transition: background-color .35s ease-in-out;
              display: block;
              color: @bodyColor;
              text-align: center;

              &:hover {
                background-color: #eee;
              }
            }
          }
        }
      }
    }
  }
}