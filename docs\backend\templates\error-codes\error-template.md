# 错误码模板

## 概述

错误码是系统中用于标识不同错误类型的唯一标识符。本文档提供了错误码的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Enums;

use Modules\Core\Enums\BaseEnum;

/**
 * 错误码枚举
 */
enum YourErrorCode: int
{
    /**
     * 通用错误码
     */
    case UNKNOWN_ERROR = 10000;
    case INVALID_PARAMS = 10001;
    case UNAUTHORIZED = 10002;
    case FORBIDDEN = 10003;
    case NOT_FOUND = 10004;
    case METHOD_NOT_ALLOWED = 10005;
    case VALIDATION_ERROR = 10006;
    case SERVICE_UNAVAILABLE = 10007;

    /**
     * 业务错误码 (11000-11999)
     */
    case BUSINESS_ERROR = 11000;
    case DATA_NOT_FOUND = 11001;
    case DATA_ALREADY_EXISTS = 11002;
    case DATA_VALIDATION_FAILED = 11003;
    case OPERATION_FAILED = 11004;
    case STATUS_INVALID = 11005;
    case PERMISSION_DENIED = 11006;

    /**
     * 用户相关错误码 (12000-12999)
     */
    case USER_NOT_FOUND = 12000;
    case USER_ALREADY_EXISTS = 12001;
    case USER_PASSWORD_INVALID = 12002;
    case USER_STATUS_INVALID = 12003;
    case USER_LOGIN_FAILED = 12004;
    case USER_LOGOUT_FAILED = 12005;
    case USER_TOKEN_INVALID = 12006;

    /**
     * 订单相关错误码 (13000-13999)
     */
    case ORDER_NOT_FOUND = 13000;
    case ORDER_ALREADY_EXISTS = 13001;
    case ORDER_STATUS_INVALID = 13002;
    case ORDER_AMOUNT_INVALID = 13003;
    case ORDER_PAYMENT_FAILED = 13004;
    case ORDER_REFUND_FAILED = 13005;
    case ORDER_CANCEL_FAILED = 13006;

    /**
     * 支付相关错误码 (14000-14999)
     */
    case PAYMENT_NOT_FOUND = 14000;
    case PAYMENT_ALREADY_EXISTS = 14001;
    case PAYMENT_STATUS_INVALID = 14002;
    case PAYMENT_AMOUNT_INVALID = 14003;
    case PAYMENT_METHOD_INVALID = 14004;
    case PAYMENT_PROCESS_FAILED = 14005;
    case PAYMENT_REFUND_FAILED = 14006;

    /**
     * 获取错误信息
     *
     * @return string
     */
    public function message(): string
    {
        return match ($this) {
            // 通用错误码
            self::UNKNOWN_ERROR => '未知错误',
            self::INVALID_PARAMS => '无效的参数',
            self::UNAUTHORIZED => '未授权',
            self::FORBIDDEN => '禁止访问',
            self::NOT_FOUND => '资源不存在',
            self::METHOD_NOT_ALLOWED => '方法不允许',
            self::VALIDATION_ERROR => '验证错误',
            self::SERVICE_UNAVAILABLE => '服务不可用',

            // 业务错误码
            self::BUSINESS_ERROR => '业务错误',
            self::DATA_NOT_FOUND => '数据不存在',
            self::DATA_ALREADY_EXISTS => '数据已存在',
            self::DATA_VALIDATION_FAILED => '数据验证失败',
            self::OPERATION_FAILED => '操作失败',
            self::STATUS_INVALID => '状态无效',
            self::PERMISSION_DENIED => '权限不足',

            // 用户相关错误码
            self::USER_NOT_FOUND => '用户不存在',
            self::USER_ALREADY_EXISTS => '用户已存在',
            self::USER_PASSWORD_INVALID => '密码无效',
            self::USER_STATUS_INVALID => '用户状态无效',
            self::USER_LOGIN_FAILED => '登录失败',
            self::USER_LOGOUT_FAILED => '登出失败',
            self::USER_TOKEN_INVALID => '用户令牌无效',

            // 订单相关错误码
            self::ORDER_NOT_FOUND => '订单不存在',
            self::ORDER_ALREADY_EXISTS => '订单已存在',
            self::ORDER_STATUS_INVALID => '订单状态无效',
            self::ORDER_AMOUNT_INVALID => '订单金额无效',
            self::ORDER_PAYMENT_FAILED => '订单支付失败',
            self::ORDER_REFUND_FAILED => '订单��款失败',
            self::ORDER_CANCEL_FAILED => '订单取消失败',

            // 支付相关错误码
            self::PAYMENT_NOT_FOUND => '支付记录不存在',
            self::PAYMENT_ALREADY_EXISTS => '支付记录已存在',
            self::PAYMENT_STATUS_INVALID => '支付状态无效',
            self::PAYMENT_AMOUNT_INVALID => '支付金额无效',
            self::PAYMENT_METHOD_INVALID => '支付方式无效',
            self::PAYMENT_PROCESS_FAILED => '支付处理失败',
            self::PAYMENT_REFUND_FAILED => '支付退款失败',
        };
    }

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::INVALID_PARAMS, self::VALIDATION_ERROR => 400,
            self::UNAUTHORIZED => 401,
            self::FORBIDDEN, self::PERMISSION_DENIED => 403,
            self::NOT_FOUND, self::DATA_NOT_FOUND, 
            self::USER_NOT_FOUND, self::ORDER_NOT_FOUND, 
            self::PAYMENT_NOT_FOUND => 404,
            self::METHOD_NOT_ALLOWED => 405,
            self::SERVICE_UNAVAILABLE => 503,
            default => 400,
        };
    }
}
```

## 使用示例

### 1. 抛出异常

```php
use Modules\YourModule\Enums\YourErrorCode;
use Modules\Core\Exceptions\BusinessException;

class YourService
{
    public function process(): void
    {
        if (!$this->validate()) {
            BizException::throws(YourErrorCode::VALIDATION_ERROR);
        }

        if (!$this->checkPermission()) {
            BizException::throws(YourErrorCode::PERMISSION_DENIED);
        }

        try {
            // 业务逻辑
        } catch (\Exception $e) {
            BizException::throws(YourErrorCode::OPERATION_FAILED);
        }
    }
}
```

### 2. 异常处理

```php
use Modules\YourModule\Enums\YourErrorCode;
use Modules\Core\Exceptions\BusinessException;

class ExceptionHandler extends Handler
{
    public function render($request, Throwable $e)
    {
        if ($e instanceof BusinessException) {
            return response()->json([
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], $e->getHttpCode());
        }

        return parent::render($request, $e);
    }
}
```

### 3. 错误响应

```php
use Modules\YourModule\Enums\YourErrorCode;

class YourController
{
    public function show($id)
    {
        $data = $this->service->find($id);
        
        if (!$data) {
            return response()->json([
                'code' => YourErrorCode::DATA_NOT_FOUND->value,
                'message' => YourErrorCode::DATA_NOT_FOUND->message()
            ], YourErrorCode::DATA_NOT_FOUND->httpCode());
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data
        ]);
    }
}
```

## 规范要求

1. 错误码定义
   - 使用枚举类型
   - 错误码范围划分
   - 错误信息明确
   - HTTP 状态码映射

2. 错误码分类
   - 通用错误码
   - 业务错误码
   - 模块错误码
   - 系统错误码

3. 错误码格式
   - 5位数字
   - 模块前缀
   - 错误类型
   - 具体错误

4. 错误信息
   - 简洁明了
   - 提供解决方案
   - 支持多语言
   - 避免敏感信息

## 最佳实践

1. 错误码定义
```php
// 好的实践 - 清晰的错误码结构
enum OrderErrorCode: int
{
    case ORDER_NOT_FOUND = 13000;
    case ORDER_STATUS_INVALID = 13001;
}

// 不好的实践 - 混乱的错误码结构
const ERROR_ORDER_NOT_FOUND = 1;
const ERROR_ORDER_STATUS_INVALID = 2;
```

2. 错误信息
```php
// 好的实践 - 明确的错误信息
public function message(): string
{
    return match ($this) {
        self::ORDER_NOT_FOUND => '订单不存在，请检查订单号',
        self::ORDER_STATUS_INVALID => '订单状态无效，只能是待支付、已支付状态',
    };
}

// 不好的实践 - 模糊的错误信息
public function message(): string
{
    return match ($this) {
        self::ORDER_NOT_FOUND => '订单错误',
        self::ORDER_STATUS_INVALID => '状态错误',
    };
}
```

## 注意事项

1. 错误码要唯一
2. 错误信息要明确
3. 合理使用 HTTP 状态码
4. 避免暴露敏感信息
5. 保持向后兼容
6. 完善错误文档
7. 统��异常处理
8. 考虑国际化需求
9. 记录错误日志
10. 定期维护更新
