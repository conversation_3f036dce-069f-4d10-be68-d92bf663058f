# [项目名称] 项目规则

## 项目概述

* **类型:** cursor_project_rules
* **描述:** [项目简要描述，包括主要目标用户、核心功能和价值主张]
* **主要目标:** [项目的核心目标，包括解决的问题和提供的价值]

> 示例：
> * **描述:** 构建一个面向小型企业主的基于Web的平台，用于跟踪库存和销售。该应用程序简化日常运营并提供可行的洞察，帮助用户发展业务。它具有安全的用户账户（包含管理员和用户角色）、实时销售分析仪表盘、通过电子邮件和短信发送的自动库存不足警报、内置报告工具（支持CSV导出和调度）、与流行支付网关（如Stripe）的集成，以及可选的AI驱动产品推荐功能。
> * **主要目标:** 提供一个统一的SaaS平台，实现库存管理和销售跟踪的自动化，并提供实时分析，确保安全的用户访问和简化的业务运营，使中小型企业主能够减少手动任务并推动增长。

## 项目结构

### 框架特定路由

* **目录规则:**
  * **[框架1]:** [框架1的目录结构和路由约定]
  * **[框架2]:** [框架2的目录结构和路由约定]
  * **[框架3]:** [框架3的目录结构和路由约定]

> 示例：
> * **目录规则:**
>   * **React Router 6:** 使用`src/routes/`与`createBrowserRouter`定义嵌套的基于组件的路由。
>   * **Next.js 14 (App Router):** `app/[route]/page.tsx`约定（仅供参考）
>   * **Next.js (Pages Router):** `pages/[route].tsx`模式（仅供参考）

### 核心目录

* **版本化结构:**
  * **[前端目录1]:** [前端目录1的用途和内容]
  * **[前端目录2]:** [前端目录2的用途和内容]
  * **[后端目录]:** [后端目录的用途和内容]

> 示例：
> * **版本化结构:**
>   * **src/components:** 存放特定于UI的可复用React组件。
>   * **src/views:** 包含视图级组件，如仪表盘和报告页面，结构反映模块化设计。
>   * **server/api:** 包含使用Node.js构建的后端API端点，与MongoDB交互。

### 关键文件

* **技术栈版本化模式:**
  * **[前端入口文件]:** [前端入口文件的用途和功能]
  * **[后端入口文件]:** [后端入口文件的用途和功能]

> 示例：
> * **技术栈版本化模式:**
>   * **src/App.js:** React应用程序的入口点，设置路由和全局状态。
>   * **server/index.js:** 主服务器文件，初始化Node.js后端并连接到MongoDB。

## 技术栈规则

* **版本强制:**
  * **[前端框架@版本]:** [前端框架的最佳实践和规范]
  * **[后端框架@版本]:** [后端框架的最佳实践和规范]
  * **[数据库@版本]:** [数据库的最佳实践和规范]

> 示例：
> * **版本强制:**
>   * **react@latest:** 强制使用带钩子的函数组件和基于组件的架构。
>   * **node@16+:** 确保异步编程模式和安全的API端点实现。
>   * **mongodb@4+:** 利用模式设计和索引最佳实践，以获得最佳的NoSQL性能。

## PRD合规性

* **不可协商:**
  * [项目需求文档中的关键需求和规范]

> 示例：
> * **不可协商:**
>   * "InventoryTracker Pro是一个专为需要高效跟踪库存和销售的中小型零售企业和在线商店所有者设计的基于Web的平台。" - 这要求安全账户、实时分析、自动警报、灵活报告和无缝支付集成。

## 应用流程集成

* **技术栈对齐流程:**
  * [应用流程与技术栈对齐的示例]

> 示例：
> * **技术栈对齐流程:**
>   * "InventoryTracker Pro应用流程 → 使用`src/routes/auth`处理认证屏幕，使用`src/routes/dashboard`处理主分析仪表板，使用React Router 6集成安全登录、引导和用户旅程转换。"

## 最佳实践

* **[前端框架]**
  * [前端框架的最佳实践1]
  * [前端框架的最佳实践2]
  * [前端框架的最佳实践3]

* **[后端框架]**
  * [后端框架的最佳实践1]
  * [后端框架的最佳实践2]
  * [后端框架的最佳实践3]

* **[数据库]**
  * [数据库的最佳实践1]
  * [数据库的最佳实践2]
  * [数据库的最佳实践3]

* **[认证方式1]**
  * [认证方式1的最佳实践1]
  * [认证方式1的最佳实践2]
  * [认证方式1的最佳实践3]

* **[认证方式2]**
  * [认证方式2的最佳实践1]
  * [认证方式2的最佳实践2]
  * [认证方式2的最佳实践3]

* **[认证方式3]**
  * [认证方式3的最佳实践1]
  * [认证方式3的最佳实践2]
  * [认证方式3的最佳实践3]

* **[第三方服务1]**
  * [第三方服务1的最佳实践1]
  * [第三方服务1的最佳实践2]
  * [第三方服务1的最佳实践3]

* **[第三方服务2]**
  * [第三方服务2的最佳实践1]
  * [第三方服务2的最佳实践2]
  * [第三方服务2的最佳实践3]

* **[第三方服务3]**
  * [第三方服务3的最佳实践1]
  * [第三方服务3的最佳实践2]
  * [第三方服务3的最佳实践3]

* **[第三方服务4]**
  * [第三方服务4的最佳实践1]
  * [第三方服务4的最佳实践2]
  * [第三方服务4的最佳实践3]

> 示例：
> * **React**
>   * 使用函数组件和钩子进行状态管理。
>   * 利用组件可重用性和模块化。
>   * 在适用的地方使用记忆化和懒加载优化性能。
> 
> * **Node.js**
>   * 实现具有适当错误处理的异步、非阻塞代码。
>   * 使用强大的中间件保障安全（如速率限制、CORS处理）。
>   * 在API端点中保持关注点清晰分离。
> 
> * **MongoDB**
>   * 根据数据使用模式设计模式并应用适当的索引。
>   * 严格验证数据以防止注入攻击并维护数据完整性。
>   * 在需要复杂查询的地方使用聚合管道。
> 
> * **本地认证**
>   * 使用强哈希算法安全存储密码。
>   * 强制实施强密码策略和登录尝试的速率限制。
>   * 利用安全的会话管理实践。 