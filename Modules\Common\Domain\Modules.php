<?php

namespace Modules\Common\Domain;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Bingo\Module\ModuleManager;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class Modules
{
    public function getAllEnabledModules(): array
    {
        $moduleList = [];
        try {
            $modules = ModuleManager::listAllEnabledModules();
        } catch (NotFoundExceptionInterface|ContainerExceptionInterface) {
            return $moduleList;
        }
        foreach ($modules as $module => $_) {
            try {
                $info = ModuleManager::getModuleBasic($module);
                if (! isset($info['title'])) {
                    continue;
                }
                $moduleList[] = [
                    'label' => $info['title'],
                    'value' => $module
                ];
            } catch (BizException) {
                return $moduleList;
            }

        }
        return $moduleList;
    }

    public function getAllEnabledModulesConfig(): array
    {
        try {
            return ModuleManager::listAllEnabledModules();
        } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }
    }

}
