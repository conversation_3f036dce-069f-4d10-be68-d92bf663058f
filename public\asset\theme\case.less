@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .pane-list {
    padding-bottom: 80px;
    position: relative;

    .tab-pane {
      width: 100%;

      .list {
        @space: 15px;
        margin-left: -@space;
        margin-right: -@space;
        margin-bottom: calc(-@space * 2);

        .item {
          padding-left: @space;
          padding-right: @space;
          padding-bottom: calc(@space * 2);

          .box {
            @padding: 25px;
            position: relative;

            .mask {
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              z-index: 3;

              padding: @padding;
              background: url(../image/case/bg.png) no-repeat center center / cover;
              color: #fff;
              opacity: 0;
              transform: scale(1);
              transition: transform .35s ease-in-out, opacity .35s ease-in-out;

              h2 {
                margin-bottom: 10px;
                padding-top: 20px;
                font-size: 20px;
              }

              p {
                font-size: 14px;
                line-height: 1.71;
              }

              .btn-box {
                padding-bottom: 10px;
                font-size: 14px;

                position: absolute;
                bottom: @padding;
                right: @padding;
              }
            }

            .text-box {
              padding: @padding;

              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
              z-index: 1;
              background-image: linear-gradient(to bottom,rgba(0,0,0,.2) 65%,rgba(0,0,0,.8) 100%);

              .df(stretch, flex-start, column-reverse);

              p {
                margin-bottom: 5px;
                color: #fff;
                font-size: 16px;
              }
            }

            &:hover {
              .mask {
                opacity: 1;
                transform: scale(1.1);
              }
            }
          }
        }
      }
    }
  }
}
