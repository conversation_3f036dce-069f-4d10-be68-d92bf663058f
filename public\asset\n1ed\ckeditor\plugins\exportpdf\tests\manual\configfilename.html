<div id="editor1">
	<p>My filename should be 'ckeditor4-export-pdf.pdf'.</p>
</div>

<div id="editor2">
	<p>And mine - 'different-name.pdf'.</p>
</div>

<div id="editor3">
	<h1>Beautiful title</h1>
</div>

<script>
	exportPdfUtils.initManualTest();

	CKEDITOR.replace( 'editor1', exportPdfUtils.getDefaultConfig( 'manual' ) );

	CKEDITOR.replace( 'editor2', exportPdfUtils.getDefaultConfig( 'manual', {
		exportPdf_fileName: 'different-name.pdf',
	} ) );

	var editor3 = CKEDITOR.replace( 'editor3', exportPdfUtils.getDefaultConfig( 'manual', {
		exportPdf_fileName: function() {
			return editor3.editable().findOne( 'h1' ).getText() + '.pdf';
		}
	} ) );
</script>
