* {
  margin: 0;
  outline: none;
  border: none;
  padding: 0;
  list-style: none;
  background-color: transparent;
  text-decoration: none;
  letter-spacing: 0;
  line-height: 1;
  font-family: "Inter", "Microsoft JhengHei";
  box-sizing: border-box;
}
.bwms-page .page-content {
  flex-grow: 1;
  min-height: 10%;
}
.bwms-page .page-content .banner {
  position: relative;
}
.bwms-page .page-content .banner .form {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.bwms-page .page-content .banner .form h1 {
  margin-bottom: 20px;
  color: #34495e;
  font-size: 30px;
  text-align: center;
  font-weight: normal;
}
.bwms-page .page-content .banner .form .inp-box {
  border-radius: 50px;
  border: 2px solid #4160F0;
  line-height: 48px;
  overflow: hidden;
  background-color: #fff;
  width: 500px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .page-content .banner .form .inp-box input {
  padding: 0 20px;
  flex-grow: 1;
}
.bwms-page .page-content .banner .form .inp-box button {
  border-radius: 50px;
  padding: 0 20px;
  background-color: #4160F0;
  color: #fff;
  font-size: 16px;
  line-height: 48px;
}
.bwms-page .logo,
.bwms-page .pic {
  font-size: 0;
  line-height: 0;
}
.bwms-page .logo img,
.bwms-page .pic img,
.bwms-page .logo video,
.bwms-page .pic video {
  width: 100%;
}
.bwms-page .breadcrumbs ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .breadcrumbs ul li {
  margin-right: 10px;
  color: #888888;
  font-size: 14px;
  line-height: 5;
}
.bwms-page .breadcrumbs ul li.icon-arrow-right {
  font-size: 8px;
}
.bwms-page .swiper-container + .swiper-pagination .swiper-pagination-bullet {
  margin-left: 5px;
  margin-right: 5px;
  border-radius: 5px;
  background-color: #d3d3d3;
  opacity: 1;
  transition: width 0.35s ease-in-out, background-color 0.35s ease-in-out;
}
.bwms-page .swiper-container + .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 16px;
  background-color: #000;
}
.bwms-page .bwms-module.p-tb {
  padding-top: 80px;
  padding-bottom: 80px;
}
.bwms-page .bwms-module .module-tit {
  margin-bottom: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .bwms-module .module-tit h3 {
  font-size: 30px;
  position: relative;
  font-weight: 600;
  color: #333;
}
.bwms-page .bwms-module .module-tit h3:before {
  background: #FFD100;
  content: '';
  display: block;
  position: absolute;
  width: 30px;
  height: 3px;
  bottom: -15px;
  left: 50%;
  margin-left: -15px;
}
.bwms-page .bwms-module .module-tit h3.white {
  color: #fff;
}
.bwms-page .banner-box {
  position: relative;
}
.bwms-page .banner-box .pic {
  width: 100%;
}
.bwms-page .banner-box .text-box {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.bwms-page .banner-box .text-box h2 {
  font-size: 40px;
  font-weight: 600;
  position: relative;
  margin-bottom: 30px;
  text-align: center;
  color: #fff;
}
.bwms-page .banner-box .text-box h2:not(.en-text)::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: #ff9600;
}
.bwms-page .banner-box .text-box h2.en-text {
  font-size: 28px;
}
.bwms-page .nav-x {
  background-color: #fff;
}
.bwms-page .nav-x .container .tab-list {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
}
.bwms-page .nav-x .container .tab-list .tab-item {
  width: 10%;
}
.bwms-page .nav-x .container .tab-list .tab-item a {
  padding: 0;
  font-size: 16px;
  line-height: 4.375;
  color: #6e6e6e;
  text-align: center;
  cursor: pointer;
  transition: color 0.35s ease-in-out;
  display: block;
}
.bwms-page .nav-x .container .tab-list .tab-item:hover a {
  color: #ff9600;
}
.bwms-page .nav-x .container .tab-list .tab-item.active a {
  background: linear-gradient(-45deg, #FFD100, #ff9600);
  color: #fff;
}
.bwms-page .tips-empty {
  margin: 20px 0;
  color: #888;
  font-size: 14px;
  text-align: center;
}
.bwms-page .search-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-100%);
  transition: transform 0.35s ease-in-out;
}
.bwms-page .search-mask.open {
  transform: translateY(0);
}
.bwms-page .search-mask .icon-close {
  position: absolute;
  font-size: 22px;
  color: #f2f2f2;
  top: 5%;
  right: 5%;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.35s ease-in-out;
}
.bwms-page .search-mask .icon-close:hover {
  color: #ff9600;
}
.bwms-page .search-mask .mask-container {
  position: absolute;
  left: 50%;
  top: 40%;
  width: 70%;
  max-width: 720px;
  transform: translateX(-50%);
}
.bwms-page .search-mask .mask-container .inp-box {
  border-bottom: 2px solid #f2f2f2;
  padding-right: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .search-mask .mask-container .inp-box input {
  margin: 0;
  border: 0;
  padding: 0;
  line-height: 3.75;
  font-size: 16px;
  background-color: transparent;
  flex-grow: 1;
  color: #f2f2f2;
}
.bwms-page .search-mask .mask-container .inp-box .icon-search {
  font-size: 24px;
  color: #f2f2f2;
  flex-shrink: 0;
  cursor: pointer;
}
.bwms-page .paginate-box {
  margin-top: 20px;
  margin-bottom: 80px;
  width: 100%;
}
.bwms-page .paginate-box .ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.bwms-page .paginate-box .ul .li {
  margin: 0 5px;
}
.bwms-page .paginate-box .ul .li a {
  border-radius: 4px;
  padding: 12px 16px;
  color: #666;
  background-color: #fff;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: 1px solid #ff9600;
  display: block;
}
.bwms-page .paginate-box .ul .li a .iconfont + span {
  margin-left: 5px;
}
.bwms-page .paginate-box .ul .li a span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .paginate-box .ul .li a span {
  display: block;
  line-height: 1.5;
}
.bwms-page .paginate-box .ul .li a:hover {
  background-color: #ff9600;
  color: #fff;
}
.bwms-page .paginate-box .ul .li.active a {
  background-color: #ff9600;
  color: #fff;
}
.bwms-page .paginate-box .ul .more {
  padding: 4px 12px;
  color: #666;
  font-size: 16px;
}
