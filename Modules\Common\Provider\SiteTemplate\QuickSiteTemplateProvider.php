<?php

namespace Modules\Common\Provider\SiteTemplate;

class QuickSiteTemplateProvider extends AbstractSiteTemplateProvider
{
    private string $name;
    private string $title;
    private string $root;

    public static function make($name, $title, $root = null): static
    {
        $ins = new static();
        $ins->name = $name;
        $ins->title = $title;
        $ins->root = $root;
        return $ins;
    }

    public function title(): string
    {
        return $this->title;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function root(): null
    {
        return $this->root;
    }


}
