<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 角色模型
 *
 * @property int $role_id 角色ID
 * @property string $role_name 角色名称
 * @property string $role_code 角色代码
 * @property string|null $description 角色描述
 * @property bool $is_system 是否为系统预设角色
 * @property int $status 状态: 0-禁用, 1-启用
 * @property int $sort_order 排序顺序
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class Role extends Model
{
    use SoftDeletes;

    protected $table = 'roles';
    protected $primaryKey = 'role_id';

    protected $fillable = [
        'role_name',
        'role_code',
        'description',
        'is_system',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'status' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 获取角色下的所有管理员
     *
     * @return BelongsToMany
     */
    public function admins(): BelongsToMany
    {
        return $this->belongsToMany(Admin::class, 'admin_roles', 'role_id', 'admin_id')
                    ->withPivot(['is_primary', 'assigned_by', 'assigned_at', 'expires_at'])
                    ->withTimestamps();
    }

    /**
     * 获取角色的所有权限
     *
     * @return BelongsToMany
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_permissions', 'role_id', 'permission_id')
                    ->withPivot(['granted_by', 'granted_at', 'expires_at'])
                    ->withTimestamps();
    }

    /**
     * 获取角色的所有菜单
     *
     * @return BelongsToMany
     */
    public function menus(): BelongsToMany
    {
        return $this->belongsToMany(Menu::class, 'role_menus', 'role_id', 'menu_id')
                    ->withPivot(['granted_by', 'granted_at'])
                    ->withTimestamps();
    }

    /**
     * 获取角色的数据权限
     *
     * @return HasMany
     */
    public function dataPermissions(): HasMany
    {
        return $this->hasMany(DataPermission::class, 'role_id', 'role_id');
    }

    /**
     * 检查角色是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查是否为系统角色
     *
     * @return bool
     */
    public function isSystemRole(): bool
    {
        return $this->is_system === true;
    }

    /**
     * 获取角色下的管理员数量
     *
     * @return int
     */
    public function getAdminCountAttribute(): int
    {
        return $this->admins()->count();
    }

    /**
     * 获取角色的权限数量
     *
     * @return int
     */
    public function getPermissionCountAttribute(): int
    {
        return $this->permissions()->count();
    }

    /**
     * 作用域：启用的角色
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：系统角色
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * 作用域：非系统角色
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * 作用域：按角色代码搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('role_code', $code);
    }

    /**
     * 作用域：按角色名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('role_name', 'like', "%{$name}%");
    }
}
