export const chickthroughPageTemplate = `
<div class="marketing-landing-page responsive-block">
    <!-- 导航栏 -->
    <nav data-bs-component="navbar" class="navbar-section responsive-block">
      <div class="container">
        <div class="navbar-wrapper">
          <div class="navbar-links">
            <a class="nav-link" href="#">首页</a>
            <a class="nav-link" href="#">功能</a>
            <a class="nav-link" href="#">价格</a>
            <a class="nav-link" href="#">关于</a>
            <button class="btn btn-outline-primary">登录</button>
            <button class="btn btn-primary">立即开始</button>
          </div>
        </div>
      </div>
      <style>
        .navbar-section {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          box-shadow: 0 1px 0 rgba(0,0,0,0.05);
          z-index: 1030;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
    
        .navbar-section .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 1.5rem;
        }
    
        .navbar-section .navbar-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 70px;
        }
    
        .navbar-section .navbar-brand {
          padding: 0;
          margin-right: 2rem;
        }
    
        .navbar-section .navbar-links {
          display: flex;
          align-items: center;
          gap: 1.5rem;
        }
    
        .navbar-section .nav-link {
          color: #4B5563;
          font-weight: 500;
          font-size: 0.95rem;
          text-decoration: none;
          transition: all 0.2s ease;
          padding: 0.5rem 0;
        }
    
        .navbar-section .nav-link:hover {
          color: #2563EB;
        }
    
        .navbar-section .btn {
          padding: 0.5rem 1.25rem;
          font-weight: 500;
          font-size: 0.95rem;
          transition: all 0.2s ease;
          border-radius: 6px;
        }
    
        .navbar-section .btn-outline-primary {
          color: #2563EB;
          border: 1.5px solid #2563EB;
          background: transparent;
        }
    
        .navbar-section .btn-outline-primary:hover {
          color: #fff;
          background: #2563EB;
        }
    
        .navbar-section .btn-primary {
          color: #fff;
          background: #2563EB;
          border: none;
        }
    
        .navbar-section .btn-primary:hover {
          background: #1D4ED8;
          box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
        }
    
        @media (max-width: 768px) {
          .navbar-section .navbar-wrapper {
            height: auto;
            padding: 1rem 0;
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }
    
          .navbar-section .navbar-brand {
            margin-right: 0;
          }
    
          .navbar-section .navbar-links {
            width: 100%;
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }
    
          .navbar-section .btn {
            width: 100%;
          }
        }
      </style>
    </nav>

    <!-- 主要英雄区域 -->
    <div class="py-5 hero-section bg-dark" data-bs-component="hero">
        <div class="container text-center text-white">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div data-bs-component="bootstrap-heading">
                        <h1 class="mb-3 display-4 fw-bold">Modern marketing made for a modern world</h1>
                        <p class="mb-4">Meet the new standard for modern SaaS digital marketing</p>
                    </div>
                    <div class="gap-2 mt-4 d-flex justify-content-center">
                        <div data-bs-component="bootstrap-button">
                            <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Try free demo</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 dashboard-preview">
                <div class="browser-header">
                    <div class="browser-dots">
                        <span class="dot dot-red"></span>
                        <span class="dot dot-yellow"></span>
                        <span class="dot dot-green"></span>
                    </div>
                    <div class="browser-address">
                        <span class="browser-url">https://marketing-dashboard.example.com</span>
                    </div>
                    <div class="browser-actions">
                        <span class="browser-action"></span>
                    </div>
                </div>
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/marketing-dashboard.webp" class="img-fluid" alt="Marketing dashboard">
            </div>
        </div>
    </div>

    <!-- 合作伙伴 -->
    <div data-bs-component="partners" class="py-4 partners-section">
        <div class="container">
            <div class="mb-4 text-center">
                <span class="partners-label">Trusted by leading companies</span>
            </div>
            <div class="row align-items-center justify-content-center">
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-hexagonix.webp" class="img-fluid" alt="Hexagonix logo">
                </div>
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-revilufy.webp" class="img-fluid" alt="Revilufy logo">
                </div>
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-zyntellx.webp" class="img-fluid" alt="Zyntellx logo">
                </div>
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-crypturon.webp" class="img-fluid" alt="Crypturon logo">
                </div>
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-spectroxium.webp" class="img-fluid" alt="Spectroxium logo">
                </div>
                <div class="col partner-logo">
                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/partner-logo-lyraxonics.webp" class="img-fluid" alt="Lyraxonics logo">
                </div>
            </div>
        </div>
    </div>

    <!-- 业务自动化部分 -->
    <div data-bs-component="info-section" class="py-5 automation-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="mb-4 col-lg-6 mb-lg-0">
                    <div data-bs-component="richTextBlock">
                        <h2 class="mb-3 fw-bold">Elevate your business with automation made easy</h2>
                        <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="position-relative automation-visual">
                        <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/automation-dashboard.webp" class="img-fluid rounded-4" alt="Automation dashboard">
                    </div>
                </div>
            </div>
        </div>
    </div>

  

    <!-- 客户统计部分 -->
    <div data-bs-component="customer-stats" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <div class="p-3 mb-4 customer-stat-card rounded-4 bg-light">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-2 text-danger">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h6 class="mb-0">Happy customers</h6>
                        </div>
                        <h3 class="mt-2 mb-0">25,317</h3>
                        <div class="mt-2 d-flex">
                            <img src="https://new-bwms.bingo-test.com/tiptap/avatar-1.webp" class="rounded-circle me-1" width="24" height="24" alt="User">
                            <img src="https://new-bwms.bingo-test.com/tiptap/avatar-2.webp" class="rounded-circle me-1" width="24" height="24" alt="User">
                            <img src="https://new-bwms.bingo-test.com/tiptap/avatar-3.webp" class="rounded-circle" width="24" height="24" alt="User">
                        </div>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="row">
                        <div class="mb-4 col-lg-4">
                            <div class="p-3 bg-white shadow-sm marketing-tools-card rounded-4 h-100">
                                <div class="mb-3 d-flex align-items-center">
                                    <div class="brand-icon me-2 rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                        <i class="fab fa-google text-primary"></i>
                                    </div>
                                    <h6 class="mb-0">Google</h6>
                                </div>
                                <div>
                                    <span class="small text-muted">Total traffic</span>
                                    <h5 class="mb-2">15x Search traffic</h5>
                                    <span class="badge bg-success small">+8.4% growth</span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 col-lg-8">
                            <div class="p-3 bg-white shadow-sm marketing-tools-card rounded-4 h-100 d-flex">
                                <div class="d-flex flex-column justify-content-center pe-3">
                                    <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/marketing-person.webp" class="img-fluid" style="max-height: 180px;" alt="Marketing person">
                                </div>
                                <div class="marketing-content">
                                    <div class="mb-2 d-flex align-items-center">
                                        <span class="badge bg-primary small">Information dashboard</span>
                                    </div>
                                    <div data-bs-component="richTextBlock">
                                        <h2 class="mb-3 fw-bold">Marketing tools that help you understand every customer</h2>
                                        <p class="text-muted small">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
   <div data-bs-component="bootstrap-image">
   <img src="https://new-bwms.bingo-test.com/tiptap/marketing-dashboard.webp" alt="">
   </div>
    <!-- 客户推荐 -->
    <div data-bs-component="testimonial" class="py-5 bg-light">
        <div class="container">
            <div class="testimonial-slider position-relative">
                <div class="row">
                    <div class="mb-3 col-md-3 mb-md-0">
                        <div class="overflow-hidden testimonial-image-wrapper rounded-4 bg-primary">
                            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" class="img-fluid" alt="Testimonial user">
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div data-bs-component="richTextBlock">
                            <p class="mb-4 lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
                            <h5 class="mb-1">Neel Kumar</h5>
                            <p class="text-muted">VP of Marketing @ Lyraxonics</p>
                        </div>
                        <div data-bs-component="bootstrap-button">
                            <a href="#" class="text-decoration-none">Read case study →</a>
                        </div>
                    </div>
                </div>
                <div class="mt-3 testimonial-nav">
                    <button class="btn btn-sm btn-outline-secondary me-2">&lt;</button>
                    <button class="btn btn-sm btn-outline-secondary">&gt;</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 行动号召 -->
    <div data-bs-component="bootstrap-cta" class="py-5 text-white bg-dark">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div data-bs-component="richTextBlock">
                        <h2 class="mb-4 display-6 fw-bold">Join Elevate today and start elevating your marketing game</h2>
                        <p class="mb-4">Be a part of a community of 10,000+ members who use Elevate and revolutionize your marketing efforts for a scalable future.</p>
                    </div>
                    <div data-bs-component="bootstrap-button">
                        <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Try free demo</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div data-bs-component="footer" class="py-4 text-white bg-dark">
        <div class="container">
            <div class="text-center">
                <div data-bs-component="richTextBlock">
                    <p class="mb-2">© 2023. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    /* 基础样式 */
    .marketing-landing-page {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    
    /* 暗色背景 */
    .dark-bg {
      background-color: #0f172a;
      color: white;
    }
    
    /* 英雄区域 */
    .hero-section {
      padding: 120px 0 60px;
    }

    /* 浏览器窗口样式 */
    .browser-header {
      background-color: #f0f0f0;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      padding: 8px 12px;
      display: flex;
      align-items: center;
    }

    .browser-dots {
      display: flex;
      gap: 6px;
    }

    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .dot-red {
      background-color: #ff5f57;
    }

    .dot-yellow {
      background-color: #ffbd2e;
    }

    .dot-green {
      background-color: #28c941;
    }

    .browser-address {
      margin-left: 12px;
      flex-grow: 1;
      background-color: #e0e0e0;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      color: #777;
    }

    .browser-actions {
      margin-left: 12px;
    }
    
    .dashboard-preview {
      position: relative;
      z-index: 1;
      box-shadow: 0 20px 40px rgba(0,0,0,0.2);
      border-radius: 8px;
      overflow: hidden;
      background-color: #0f172a;
    }
    
    .dashboard-preview img {
      width: 100%;
      vertical-align: bottom;
    }
    
    /* 合作伙伴区域 */
    .partners-section {
      background-color: #fff;
      border-bottom: 1px solid #eee;
      position: relative;
    }
    
    .partners-section:before {
      content: '';
      position: absolute;
      top: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #3b82f6, #6366f1);
    }
    
    .partners-label {
      position: relative;
      color: #64748b;
      font-size: 0.9rem;
    }
    
    .partner-logo {
      text-align: center;
      padding: 0 15px;
    }
    
    .partner-logo img {
      max-height: 25px;
      opacity: 0.8;
      transition: opacity 0.3s ease;
    }
    
    .partner-logo img:hover {
      opacity: 1;
    }
    
    /* 业务自动化部分 */
    .automation-section {
      padding: 80px 0;
    }
    
    .automation-visual {
      position: relative;
    }
    
    .automation-visual:before {
      content: '';
      position: absolute;
      top: -30px;
      right: -30px;
      width: 60px;
      height: 60px;
      background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9IiM4QkU3OEIiLz48cmVjdCB4PSIyNCIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSIjOEJFNzhCIi8+PHJlY3QgeD0iNDgiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgZmlsbD0iIzhCRTc4QiIvPjxyZWN0IHk9IjI0IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9IiM4QkU3OEIiLz48cmVjdCB4PSIyNCIgeT0iMjQiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgZmlsbD0iIzhCRTc4QiIvPjxyZWN0IHg9IjQ4IiB5PSIyNCIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSIjOEJFNzhCIi8+PHJlY3QgeT0iNDgiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgZmlsbD0iIzhCRTc4QiIvPjxyZWN0IHg9IjI0IiB5PSI0OCIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSIjOEJFNzhCIi8+PHJlY3QgeD0iNDgiIHk9IjQ4IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9IiM4QkU3OEIiLz48L3N2Zz4=');
      background-size: contain;
      z-index: -1;
    }
    
    /* 工作流程卡片 */
    .workflow-section {
      background-color: #f8fafc;
      padding: 80px 0;
    }
    
    .workflow-card {
      overflow: hidden;
      border: none;
    }
    
    .actions-panel, .workflow-panel, .result-panel {
      background-color: #f8fafc;
    }
    
    .action-item {
      transition: all 0.3s ease;
      cursor: pointer;
      background-color: white;
    }
    
    .action-item.selected {
      background-color: #f0f9ff;
      border-left: 3px solid #3b82f6;
    }
    
    .action-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }
    
    .workflow-content, .workflow-condition, .workflow-action {
      transition: all 0.3s ease;
    }
    
    .workflow-avatar {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
    
    .result-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    
    /* 客户统计卡片 */
    .customer-stat-card {
      transition: all 0.3s ease;
    }
    
    .customer-stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0,0,0,0.05);
    }
    
    /* 营销工具卡片 */
    .marketing-tools-card {
      transition: all 0.3s ease;
    }
    
    .marketing-tools-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0,0,0,0.08);
    }
    
    /* 推荐卡片 */
    .testimonial-image-wrapper {
      height: 100%;
      min-height: 200px;
      transition: all 0.3s ease;
      background-color: #7c3aed;
    }
    
    .testimonial-image-wrapper img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .testimonial-nav {
      position: absolute;
      bottom: 0;
      right: 0;
    }
    
    /* 按钮样式 */
    .btn {
      transition: all 0.3s ease;
    }
    
    .btn:hover {
      transform: translateY(-2px);
    }
    
    .btn-primary {
      background-color: #3b82f6;
      border-color: #3b82f6;
    }
    
    .btn-primary:hover {
      background-color: #2563eb;
      border-color: #2563eb;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }
    
    /* 响应式调整 */
    @media (max-width: 991.98px) {
      .hero-section {
        padding: 100px 0 40px;
      }
      
      .automation-section {
        padding: 60px 0;
      }
      
      .workflow-section {
        padding: 60px 0;
      }
    }
    
    @media (max-width: 767.98px) {
      .hero-section {
        padding: 80px 0 30px;
      }
      
      .testimonial-image-wrapper {
        min-height: 150px;
      }
      
      .automation-section {
        padding: 40px 0;
      }
      
      .workflow-section {
        padding: 40px 0;
      }
    }
    </style>
    
    <script>
    document.addEventListener("DOMContentLoaded", function() {
      // 滚动动画效果
      const animateOnScroll = () => {
        const elements = document.querySelectorAll('.marketing-tools-card, .workflow-card, .customer-stat-card, .automation-visual, .testimonial-slider');
        
        elements.forEach(element => {
          const elementPosition = element.getBoundingClientRect().top;
          const screenPosition = window.innerHeight / 1.3;
          
          if (elementPosition < screenPosition) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
          }
        });
      };
      
      // 初始化元素状态
      const initElements = () => {
        const elements = document.querySelectorAll('.marketing-tools-card, .workflow-card, .customer-stat-card, .automation-visual, .testimonial-slider');
        
        elements.forEach(element => {
          element.style.opacity = '0';
          element.style.transform = 'translateY(20px)';
          element.style.transition = 'all 0.6s ease';
        });
      };
      
      // 初始化
      initElements();
      animateOnScroll();
      
      // 监听滚动事件
      window.addEventListener('scroll', animateOnScroll);
    });
    </script>
</div>`

