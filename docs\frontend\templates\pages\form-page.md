# 表单页面模板

## 概述

表单页面是后台管理系统中常见的数据录入和编辑页面。本文档提供了表单页面的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <div class="form-page">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑' : '新增' }}</span>
          <el-button @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="form-content"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            提交
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useFormStore } from '@/stores/form'

// 路由
const route = useRoute()
const router = useRouter()
const formStore = useFormStore()

// 表单引用
const formRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const isEdit = computed(() => !!route.params.id)

// 表单数据
interface FormData {
  name: string
  status: number
  remark: string
}

const formData = reactive<FormData>({
  name: '',
  status: 1,
  remark: ''
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

// 获取详情
const getDetail = async () => {
  try {
    loading.value = true
    const data = await formStore.getDetail(route.params.id as string)
    Object.assign(formData, data)
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value) {
      await formStore.updateItem({
        id: route.params.id,
        ...formData
      })
      ElMessage.success('更新成功')
    } else {
      await formStore.createItem(formData)
      ElMessage.success('创建成功')
    }
    
    goBack()
  } catch (error) {
    console.error(error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    getDetail()
  }
})
</script>

<style lang="scss" scoped>
.form-page {
  padding: 20px;
  
  .form-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .form-content {
      max-width: 800px;
      margin: 0 auto;
    }
  }
}
</style>
```

## 规范要求

1. 页面结构
   - 标题区域
   - 表单区域
   - 操作按钮
   - 返回功能

2. 数据管理
   - 使用 Pinia Store
   - 响应式数据
   - 表单验证
   - 类型定义

3. 功能实现
   - 新增/编辑
   - 表单验证
   - 数据提交
   - 重置功能

4. 交互处理
   - 加载状态
   - 错误处理
   - 成功提示
   - 表单校验

## 最佳实践

1. Store 定义
```typescript
import { defineStore } from 'pinia'
import { createItem, updateItem, getDetail } from '@/api/form'

export const useFormStore = defineStore('form', {
  actions: {
    async createItem(data: any) {
      return await createItem(data)
    },
    
    async updateItem(data: any) {
      return await updateItem(data)
    },
    
    async getDetail(id: string) {
      return await getDetail(id)
    }
  }
})
```

2. API 定义
```typescript
import request from '@/utils/request'

export const createItem = (data: any) => {
  return request({
    url: '/api/items',
    method: 'post',
    data
  })
}

export const updateItem = (data: any) => {
  return request({
    url: `/api/items/${data.id}`,
    method: 'put',
    data
  })
}

export const getDetail = (id: string) => {
  return request({
    url: `/api/items/${id}`,
    method: 'get'
  })
}
```

## 注意事项

1. 表单验证要完整
2. 错误提示要友好
3. 重置功能要彻底
4. 提交防重复
5. 样式要规范
6. 考虑表单性能
7. 注意数据类型
8. 处理异常情况
9. 保持代码整洁
10. 组件要可复用
</rewritten_file>
