/**!
 * jQuery Progress Timer - v1.0.5 - 6/8/2015
 * http://www.thomasnorberg.com
 * Copyright (c) 2015 <PERSON>;
 * Licensed MIT
 */

if("undefined"==typeof jQuery)throw new Error("jQuery progress timer requires jQuery");!function(a,b,c,d){"use strict";var e="progressTimer",f={timeLimit:60,warningThreshold:5,onFinish:function(){},baseStyle:"",warningStyle:"progress-bar-danger",completeStyle:"progress-bar-success",showHtmlSpan:!0,errorText:"ERROR!",successText:"100%"},g=function(b,c){this.element=b,this.$elem=a(b),this.options=a.extend({},f,c),this._defaults=f,this._name=e,this.metadata=this.$elem.data("plugin-options"),this.init()};g.prototype.constructor=g,g.prototype.init=function(){var c=this;return a(c.element).empty(),c.span=a("<span/>"),c.barContainer=a("<div>").addClass("progress"),c.bar=a("<div>").addClass("progress-bar active").addClass(c.options.baseStyle).attr("role","progressbar").attr("aria-valuenow","0").attr("aria-valuemin","0").attr("aria-valuemax",c.options.timeLimit),c.span.appendTo(c.bar),c.options.showHtmlSpan||c.span.addClass("sr-only"),c.bar.appendTo(c.barContainer),c.barContainer.appendTo(c.element),c.start=new Date,c.limit=1e3*c.options.timeLimit,c.warningThreshold=1e3*c.options.warningThreshold,c.interval=b.setInterval(function(){c._run.call(c)},250),c.bar.data("progress-interval",c.interval),!0},g.prototype.destroy=function(){this.$elem.removeData()},g.prototype._run=function(){var a=this,b=new Date-a.start,c=b/a.limit*100;a.bar.attr("aria-valuenow",c),a.bar.width(c+"%");var d=c.toFixed(2);return d>=100&&(d=100),a.options.showHtmlSpan&&a.span.html(d+"%"),b>=a.warningThreshold&&a.bar.removeClass(this.options.baseStyle).removeClass(this.options.completeStyle).addClass(this.options.warningStyle),b>=a.limit&&a.complete.call(a),!0},g.prototype.removeInterval=function(){var c=this,d=a(".progress-bar",c.element);if("undefined"!=typeof d.data("progress-interval")){var e=d.data("progress-interval");b.clearInterval(e)}return d},g.prototype.complete=function(){var b=this,c=b.removeInterval.call(b),d=arguments;0!==d.length&&"object"==typeof d[0]&&(b.options=a.extend({},b.options,d[0])),c.removeClass(b.options.baseStyle).removeClass(b.options.warningStyle).addClass(b.options.completeStyle),c.width("100%"),b.options.showHtmlSpan&&a("span",c).html(b.options.successText),c.attr("aria-valuenow",100),setTimeout(function(){b.options.onFinish.call(c)},500),b.destroy.call(b)},g.prototype.error=function(){var b=this,c=b.removeInterval.call(b),d=arguments;0!==d.length&&"object"==typeof d[0]&&(b.options=a.extend({},b.options,d[0])),c.removeClass(b.options.baseStyle).addClass(b.options.warningStyle),c.width("100%"),b.options.showHtmlSpan&&a("span",c).html(b.options.errorText),c.attr("aria-valuenow",100),setTimeout(function(){b.options.onFinish.call(c)},500),b.destroy.call(b)},a.fn[e]=function(b){var c=arguments;if(b===d||"object"==typeof b)return this.each(function(){a.data(this,"plugin_"+e)||a.data(this,"plugin_"+e,new g(this,b))});if("string"==typeof b&&"_"!==b[0]&&"init"!==b){if(0===Array.prototype.slice.call(c,1).length&&-1!==a.inArray(b,a.fn[e].getters)){var f=a.data(this[0],"plugin_"+e);return f[b].apply(f,Array.prototype.slice.call(c,1))}return this.each(function(){var d=a.data(this,"plugin_"+e);d instanceof g&&"function"==typeof d[b]&&d[b].apply(d,Array.prototype.slice.call(c,1))})}},a.fn[e].getters=["complete","error"]}(jQuery,window,document,void 0);