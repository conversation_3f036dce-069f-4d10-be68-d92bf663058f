# 数据库表结构生成模板

## 标准SQL模板

```sql
DROP TABLE IF EXISTS `模块名_表名`;
CREATE TABLE `模块名_表名` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  -- 业务字段（根据业务需求添加）
  `field1` varchar(255) NOT NULL COMMENT '字段1说明',
  `field2` text NULL DEFAULT NULL COMMENT '字段2说明',
  `field3` int NOT NULL DEFAULT 0 COMMENT '字段3说明',
  `related_table_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联表ID',
  -- 状态字段
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
  -- 多语言支持（如需要）
  `lang` varchar(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言（zh-CN:简体中文, zh-HK:繁体中文）',
  -- 基础字段（必需）
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  -- 创建和更新人（审计字段）
  `created_by` bigint UNSIGNED NOT NULL COMMENT '创建人',
  `updated_by` bigint UNSIGNED NOT NULL COMMENT '更新人',
  -- 索引
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE,
  INDEX `idx_related_table` (`related_table_id`) USING BTREE,
  -- 其他所需索引（根据业务查询需求添加）
  INDEX `idx_field1` (`field1`) USING BTREE,
  INDEX `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT '表说明';
```

## 表结构设计原则

### 1. 命名规范
- **表名**：使用`模块名_表名`格式，如`course_courses`、`member_users`
- **字段名**：使用snake_case命名法，如`user_name`、`course_title`
- **主键**：统一使用`id`
- **外键**：使用`关联表名_id`格式，如`course_id`、`user_id`
- **索引名**：使用`idx_字段名`格式，如`idx_status`、`idx_user_id`
- **所有表和字段**：必须添加明确的注释

### 2. 基础字段要求
- **标识字段**：所有表必须包含自增主键`id`
- **时间字段**：必须包含`created_at`、`updated_at`、`deleted_at`（支持软删除）
- **审计字段**：需包含`created_by`、`updated_by`（记录操作人）
- **状态字段**：业务表通常需要`status`字段（0:禁用, 1:启用）
- **多语言**：多语言支持的表需包含`lang`字段

### 3. 索引设计
- 所有经常作为查询条件的字段应创建适当索引
- 避免过度索引，选择主要查询字段优先创建索引
- 复合索引设计遵循最左前缀原则
- 常见索引字段：`status`、外键字段、创建时间、业务主要查询字段

### 4. 字段类型选择
- **整数类型**：
  - 主键和外键使用`bigint UNSIGNED`
  - 状态标识使用`tinyint`
  - 计数、排序等使用`int`

- **字符串类型**：
  - 短文本使用`varchar(长度)`，如`varchar(255)`
  - 长文本使用`text`类型
  - 多语言标识使用`varchar(10)`

- **时间类型**：
  - 创建、更新、删除时间使用`timestamp`
  - 需要精确记录日期的使用`datetime`
  - 仅需日期的使用`date`

- **数值类型**：
  - 金额统一使用`decimal(精度,小数位)`，如`decimal(10,2)`
  - 避免使用`float`存储精确数值

- **布尔类型**：
  - 使用`tinyint(1)`表示布尔值，0为false，1为true

### 5. 其他注意事项
- 所有表默认使用InnoDB引擎
- 字符集统一使用utf8mb4，排序规则使用utf8mb4_unicode_ci
- 禁止使用外键约束（通过应用层逻辑实现关联关系）
- 敏感数据应考虑加密存储
- 大字段应考虑单独建表存储，提高查询性能
- 表创建后应添加适当的表注释和字段注释
- 尽量避免使用DEFAULT CURRENT_TIMESTAMP等自动更新时间的设置

## 示例表

### 用户表示例
```sql
DROP TABLE IF EXISTS `member_users`;
CREATE TABLE `member_users` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码（加密）',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `mobile` varchar(20) NULL DEFAULT NULL COMMENT '手机号',
  `nickname` varchar(50) NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT 0 COMMENT '性别（0:未知, 1:男, 2:女）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态（0:禁用, 1:启用）',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) NULL DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint UNSIGNED NOT NULL COMMENT '创建人',
  `updated_by` bigint UNSIGNED NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username` (`username`) USING BTREE,
  UNIQUE INDEX `uk_email` (`email`) USING BTREE,
  UNIQUE INDEX `uk_mobile` (`mobile`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE,
  INDEX `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT '会员用户表';
```
