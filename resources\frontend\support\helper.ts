/**
 * Helper 助教函数集合
 */

// 移除这两行导入，因为它们指向 admin 目录
// import Cache from '/admin/support/cache'
// import i18n from '/admin/i18n'

// 替换为前端的 Cache
import Cache from './cache'
import i18n from '../i18n'
const IAM_TOKEN = 'iam_token'

/**
 * env get
 *
 * @param key
 */
export function env(key: string): any {
  const envVars = import.meta.env as any
  return envVars[key]
}

/**
 * remember token
 *
 * @param token
 */
export function rememberAuthToken(token: string): void {
  Cache.set(IAM_TOKEN, token)
}

/**
 * remove auth token
 */
export function removeAuthToken(): void {
  localStorage.removeItem('iam_token')
}

/**
 * get auth token
 *
 */
export function getAuthToken(): string | null {
  return localStorage.getItem('iam_token')
}

/**
 * 是否是小屏幕
 * @return
 */
export function isMiniScreen(): boolean {
  return window.document.body.clientWidth < 500
}

/**
 * translate
 *
 * @param translate
 * @returns
 */
export function t(translate: string) {
  return i18n.global.t(translate)
}

/**
 * is undefined
 *
 * @param value
 * @returns
 */
export function isUndefined(value: any): boolean {
  return value === undefined
}

/**
 * set page title
 *
 * @param title
 */
export function setPageTitle(title: string) {
  document.title = title + '-' + env('VITE_APP_NAME')
}

/**
 * is function?
 *
 * @param value
 */
export function isFunction(value: any) {
  return typeof value === 'function'
}

export function _window(key: string) {
  if (window.hasOwnProperty('admin_config')) {
    //@ts-ignore
    return window.admin_config[key]
  }

  return null
}

export function getBaseUrl() {
  const baseUrl = env('VITE_API_BASE_URL')
  return baseUrl || ''
}
