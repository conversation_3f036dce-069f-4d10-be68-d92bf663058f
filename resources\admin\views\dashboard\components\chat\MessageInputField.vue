<template>
  <div class="message-content">
    <div class="message-body" v-html="formattedContent"></div>
    
    <!-- 输入框区域 -->
    <div class="input-field-container">
      <el-input
        v-if="inputField.type === 'text'"
        v-model="inputValue"
        :placeholder="inputField.placeholder || '请输入...'"
        :maxlength="inputField.maxLength || 200"
        show-word-limit
        @keyup.enter="handleSubmit"
        :disabled="isSubmitting"
        clearable
      />
      
      <el-input
        v-else-if="inputField.type === 'textarea'"
        v-model="inputValue"
        type="textarea"
        :placeholder="inputField.placeholder || '请输入...'"
        :maxlength="inputField.maxLength || 500"
        :rows="inputField.rows || 3"
        show-word-limit
        @keyup.ctrl.enter="handleSubmit"
        :disabled="isSubmitting"
      />
      
      <el-input-number
        v-else-if="inputField.type === 'number'"
        v-model="inputValue"
        :placeholder="inputField.placeholder || '请输入数字...'"
        :disabled="isSubmitting"
        style="width: 100%"
      />
      
      <!-- 操作按钮 -->
      <div class="input-actions">
        <el-button
          type="primary"
          @click="handleSubmit"
          :disabled="isSubmitting || (!inputValue && inputField.required)"
          :loading="isSubmitting"
        >
          {{ inputField.submitText || '提交' }}
        </el-button>
        
        <el-button
          v-if="inputField.cancelText"
          @click="handleCancel"
          :disabled="isSubmitting"
        >
          {{ inputField.cancelText }}
        </el-button>
      </div>
    </div>
    
    <!-- 快捷操作按钮 -->
    <div v-if="message.quickReplies && message.quickReplies.length > 0" class="quick-replies">
      <el-button
        v-for="reply in message.quickReplies"
        :key="reply.id"
        size="small"
        :class="{
          'primary-button': reply.style === 'primary',
          'secondary-button': reply.style === 'secondary',
          'danger-button': reply.style === 'danger'
        }"
        @click="handleQuickReply(reply)"
        :disabled="isSubmitting"
      >
        {{ reply.text }}
      </el-button>
    </div>
    
    <div class="message-time">{{ message.time }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { InputFieldMessage, QuickReply } from './types'

const props = defineProps<{
  message: InputFieldMessage
}>()

const emit = defineEmits<{
  (e: 'submit', value: string): void
  (e: 'cancel'): void
  (e: 'quickReply', reply: QuickReply): void
}>()

const inputValue = ref('')
const isSubmitting = ref(false)

// 获取输入框配置
const inputField = computed(() => props.message.inputField)

// 格式化消息内容
const formattedContent = computed(() => {
  if (!props.message.content) return ''
  
  return props.message.content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/<br><br>/g, '<br>')
})

// 处理提交
const handleSubmit = async () => {
  if (!inputValue.value && inputField.value.required) {
    return
  }
  
  isSubmitting.value = true
  
  try {
    emit('submit', inputValue.value)
    // 清空输入框
    inputValue.value = ''
  } finally {
    isSubmitting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  inputValue.value = ''
}

// 处理快捷回复
const handleQuickReply = (reply: QuickReply) => {
  emit('quickReply', reply)
}
</script>

<script lang="ts">
export default {
  name: 'MessageInputField'
}
</script>

<style scoped lang="scss">
.message-content {
  max-width: 85%;
  width: 100%;
}

.message-body {
  background: #e8f4ff;
  color: #333;
  padding: 12px 16px;
  border-radius: 0 12px 12px 12px;
  margin-bottom: 12px;
  word-wrap: break-word;
  line-height: 1.5;
}

.input-field-container {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 12px;
}

.input-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  justify-content: flex-end;
}

.quick-replies {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.quick-replies .el-button {
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 13px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  transition: all 0.2s ease;
}

.quick-replies .el-button:hover {
  background: #f0f9ff;
  border-color: #6BBAD2;
  color: #6BBAD2;
}

.quick-replies .primary-button {
  background: #6BBAD2;
  border-color: #6BBAD2;
  color: white;
}

.quick-replies .primary-button:hover {
  background: #5aa5bf;
  border-color: #5aa5bf;
}

.quick-replies .secondary-button {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

.quick-replies .secondary-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.quick-replies .danger-button {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.quick-replies .danger-button:hover {
  background: #c82333;
  border-color: #bd2130;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 响应式适配 */
@media screen and (min-width: 1680px) {
  .message-body {
    padding: 14px 18px;
    font-size: 16px;
  }
  
  .input-field-container {
    padding: 18px;
  }
  
  .quick-replies .el-button {
    padding: 8px 18px;
    font-size: 14px;
  }
  
  .message-time {
    font-size: 13px;
  }
}

@media screen and (min-width: 1920px) {
  .message-body {
    padding: 16px 20px;
    font-size: 17px;
  }
  
  .input-field-container {
    padding: 20px;
  }
  
  .quick-replies .el-button {
    padding: 10px 20px;
    font-size: 15px;
  }
  
  .message-time {
    font-size: 14px;
  }
}

@media screen and (min-width: 2560px) {
  .message-body {
    padding: 18px 22px;
    font-size: 18px;
  }
  
  .input-field-container {
    padding: 22px;
  }
  
  .quick-replies .el-button {
    padding: 12px 22px;
    font-size: 16px;
  }
  
  .message-time {
    font-size: 15px;
  }
}
</style> 