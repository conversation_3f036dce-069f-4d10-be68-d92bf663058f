<template>
  <div class="block-width-control">
    <div class="width-options">
      <div 
        class="width-option" 
        :class="{ active: modelValue === 'col-12' }"
        @click="updateWidth('col-12')"
        title="全宽"
      >
        <span class="el-icon-full-screen">全</span>
      </div>
      <div 
        class="width-option" 
        :class="{ active: modelValue === 'col-12 col-md-10 col-lg-8' }"
        @click="updateWidth('col-12 col-md-10 col-lg-8')"
        title="中等宽度"
      >
        <span class="el-icon-crop">中</span>
      </div>
      <div 
        class="width-option" 
        :class="{ active: modelValue === 'col-12 col-md-8 col-lg-6' }"
        @click="updateWidth('col-12 col-md-8 col-lg-6')"
        title="窄宽度"
      >
        <span class="el-icon-crop">窄</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 定义属性
const props = defineProps<{
  modelValue: string
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>();

// 更新宽度
const updateWidth = (width: string) => {
  emit('update:modelValue', width);
};
</script>

<style lang="scss" scoped>
.block-width-control {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.width-options {
  display: flex;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 20px;
  padding: 3px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.width-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 2px;
  font-size: 12px;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.active {
    background-color: #409eff;
    color: white;
  }
}
</style> 