_.templateSettings={evaluate:/\{\{(.+?)\}\}/g,interpolate:/\{\{=(.+?)\}\}/g,escape:/\{\{-(.+?)\}\}/g};var gridEl,grid,charts;$(document).ready(function(){d3.json(options.endPoint).then(function(data){gridEl=$(".grid-stack");grid=GridStack.init({cellHeight:90,acceptWidgets:true});charts=[];var color1=tabler.getColor("primary");var color2=tabler.getColor("purple");var color3=tabler.getColor("yellow");var color4=tabler.getColor("green");var color5=tabler.getColor("red");var color6=tabler.getColor("primary",.8);var color7=tabler.getColor("gray-300");var colors=[color1,color2,color3,color4,color5,color6,color7,color1,color2,color3,color4,color5,color6,color7];dc.config.defaultColors(colors);var widgetResized=false;data.forEach(function(d){var milliseconds=d.created_at*1e3;d.date=new Date(milliseconds)});var facts=crossfilter(data);var all=facts.groupAll();var dataCount=dc.dataCount(".data-count").crossfilter(facts).groupAll(all);dataCount.render();var dateDim=facts.dimension(function(d){return d.date});$("#reset-all").on("click",function(e){e.preventDefault();dateDim.filter(null);dc.filterAll(null);dc.renderAll()});$("#report-filter").on("submit",function(e){e.preventDefault();var fromDate=$("#from-date-disp").val();var toDate=$("#to-date-disp").val();if(fromDate.length>0&&toDate.length>0){dateDim.filter(dc.filters.RangedFilter(new Date(fromDate),new Date(toDate)));dc.redrawAll()}else if(fromDate.length===0&&toDate.length===0){dateDim.filter(null);dc.redrawAll()}});var addWidget=function(title,type,name,label,x,y,w,h){var widget=$("#widget").html();var widgetContent=_.template(widget)({title:title,type:type,name:name,label:label});grid.addWidget({x:x,y:y,w:w,h:h,content:widgetContent})};var drawChart=function(chartName,chartType){var tmpDim=facts.dimension(function(d){return d[chartName]});var tmpGroup=tmpDim.group();var widget=$("body").find("[data-name='"+chartName+"']").closest(".grid-stack-item");var width=widget.width();var height=widget.height();if(chartType==="pie"){charts[chartName]=dc.pieChart("#"+chartName);charts[chartName].width(width-80).height(height-80).slicesCap(4).renderTitle(true).legend(dc.legend()).renderLabel(false).dimension(tmpDim).group(tmpGroup)}else if(chartType==="donut"){charts[chartName]=dc.pieChart("#"+chartName);charts[chartName].width(width-80).height(height-80).dimension(tmpDim).group(tmpGroup).innerRadius(Math.round((height-80)*20/100)).renderLabel(true).renderTitle(true)}else if(chartType==="row"){charts[chartName]=dc.rowChart("#"+chartName);charts[chartName].width(width-40).height(height-80).dimension(tmpDim).group(tmpGroup).elasticX(true)}else if(chartType==="bar"){charts[chartName]=dc.barChart("#"+chartName);charts[chartName].width(width-10).height(height-80).x(d3.scaleBand()).xUnits(dc.units.ordinal).brushOn(false).dimension(tmpDim).barPadding(.1).outerPadding(.05).on("renderlet",function(chart){chart.selectAll("rect").attr("fill",function(d,i){return colors[i]})}).group(tmpGroup)}charts[chartName].render()};var showMessage=function(container,txt,type){var message='<div class="alert alert-'+type+' alert-dismissible" role="alert">';message+='    <div class="d-flex">';message+="        <div>";message+='            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n'+'                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n'+'                <circle cx="12" cy="12" r="9"></circle>\n'+'                <line x1="12" y1="8" x2="12" y2="12"></line>\n'+'                <line x1="12" y1="16" x2="12.01" y2="16"></line>\n'+"            </svg>";message+="        </div>";message+="        <div>";message+='            <p class="alert-title">'+txt+"</p>";message+="        </div>";message+="    </div>";message+='    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>';message+="</div>";$(container).append(message)};var hideMessage=function(container){$(container).empty()};_.each(options.charts,function(chart){addWidget(chart.title,chart.type,chart.name,chart.label,chart.gsX,chart.gsY,chart.gsW,chart.gsH);drawChart(chart.name,chart.type)});grid.disable();grid.on("resize",function(event,el){widgetResized=true});grid.on("change",function(e,items){if(widgetResized){var widget=$(_.first(items).el);var chartName=widget.find(".chart-container").data("name");var chartType=widget.find(".chart-container").data("type");var width=widget.width();var height=widget.height();if(chartType==="bar"){charts[chartName].width(width-10).height(height-80).rescale().redraw()}else if(chartType==="row"){charts[chartName].width(width-40).height(height-80).redraw()}else if(chartType==="donut"){charts[chartName].width(width-80).height(height-80).innerRadius(Math.round((height-80)*20/100)).redraw()}else{charts[chartName].width(width-80).height(height-80).redraw()}}widgetResized=false});window.deleteChart=function(el){var name=$(el).data("name");var widget=$("body").find("[data-name='"+name+"']").parent().parent();widget.remove();grid.removeWidget(widget,false)};$("#enable").click(function(e){e.preventDefault();grid.enable();gridEl.addClass("grid-editable");$(".btn-for-toggle").toggle()});$("#disable").click(function(e){e.preventDefault();grid.disable();gridEl.removeClass("grid-editable");$(".btn-for-toggle").toggle()});$("#reset").click(function(e){e.preventDefault();grid.removeAll()});$("#formModal").on("show.bs.modal",function(event){var target=$(event.relatedTarget);var title=target.data("title");var modal=$(this);if(typeof title==="undefined"){modal.find("#chartTitle").val("");modal.find("#chartType option:eq(0)").prop("selected",true);modal.find("#field option:eq(0)").prop("selected",true);modal.find("#field").prop("disabled",false)}else{var name=target.data("name");var label=target.data("label");var type=target.data("type");modal.find("#chartTitle").val(title);modal.find('#chartType option[value="'+type+'"]').prop("selected",true);modal.find('#field option[value="'+name+'"]').prop("selected",true);modal.find("#field").prop("disabled",true)}});var saveChart=function(e){e.preventDefault();var chartTitle=$("#chartTitle").val();var chartType=$("#chartType").val();var fieldEl=$("#field");var chartName=fieldEl.val();var chartLabel=fieldEl.children(":selected").text();if(chartTitle.length===0){hideMessage("#modal-messages");showMessage("#modal-messages","<strong>"+options.i18n.error+"</strong> "+options.i18n.errorMessage,"danger");return false}if($("#"+chartName).length){var widget=$("body").find("[data-name='"+chartName+"']").closest(".grid-stack-item");var x=widget.attr("gs-x");var y=widget.attr("gs-y");var w=widget.attr("gs-w");var h=widget.attr("gs-h");widget.remove();grid.removeWidget(widget,false);addWidget(chartTitle,chartType,chartName,chartLabel,x,y,w,h);drawChart(chartName,chartType);hideMessage("#modal-messages")}else{addWidget(chartTitle,chartType,chartName,chartLabel,0,0,4,4);drawChart(chartName,chartType)}const formModal=document.querySelector("#formModal");const modal=bootstrap.Modal.getInstance(formModal);modal.hide()};$("#saveChart").on("click",saveChart);$("#formChart").submit(saveChart);$("#saveReport").click(function(e){e.preventDefault();var report=_.map($(".grid-stack .grid-stack-item:visible"),function(el){var itemEl=$(el);var chartContainerEl=itemEl.find(".chart-container");return{name:chartContainerEl.attr("data-name"),label:chartContainerEl.attr("data-label"),title:chartContainerEl.attr("data-title"),type:chartContainerEl.attr("data-type"),width:120,height:120,gsX:itemEl.attr("gs-x"),gsY:itemEl.attr("gs-y"),gsW:itemEl.attr("gs-w"),gsH:itemEl.attr("gs-h")}});var data={report:JSON.stringify(report),_csrf:options._csrf};var jqxhr=$.ajax({method:"POST",url:options.endPoint,dataType:"json",data:data}).done(function(data){if(data.success&&data.id>0){hideMessage("#messages");showMessage("#messages","<strong>"+options.i18n.success+"</strong> "+options.i18n.updatedMessage,"success")}else{hideMessage("#messages");showMessage("#messages","<strong>"+options.i18n.error+"</strong> "+options.i18n.errorOnUpdate,"danger")}}).fail(function(msg){hideMessage("#messages");showMessage("#messages","<strong>"+options.i18n.success+"</strong>"+options.i18n.errorOnUpdate,"danger");console.error(msg)})})})});