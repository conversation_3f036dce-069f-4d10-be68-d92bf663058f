/**
 * 轮播图模板
 * 增加媒体查询相关的响应式类，适配不同设备显示效果
 */

export const carouselTemplate = `
<div data-bs-component="carousel" class="carousel-block responsive-block">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12">
        <div id="carouselExampleIndicators" class="carousel slide" data-bs-ride="carousel">
          <div class="carousel-indicators">
            <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
            <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="1" aria-label="Slide 2"></button>
            <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="2" aria-label="Slide 3"></button>
          </div>
          <div class="carousel-inner">
            <div class="carousel-item active">
              <!-- 响应式图片 - 移动端 -->
              <img src="https://via.placeholder.com/600x300?text=第一张幻灯片" class="d-block w-100 d-md-none" alt="第一张幻灯片">
              <!-- 响应式图片 - 平板端 -->
              <img src="https://via.placeholder.com/900x350?text=第一张幻灯片" class="d-none d-md-block d-lg-none w-100" alt="第一张幻灯片">
              <!-- 响应式图片 - 桌面端 -->
              <img src="https://via.placeholder.com/1200x400?text=第一张幻灯片" class="d-none d-lg-block w-100" alt="第一张幻灯片">
            </div>
            <div class="carousel-item">
              <!-- 响应式图片 - 移动端 -->
              <img src="https://via.placeholder.com/600x300?text=第二张幻灯片" class="d-block w-100 d-md-none" alt="第二张幻灯片">
              <!-- 响应式图片 - 平板端 -->
              <img src="https://via.placeholder.com/900x350?text=第二张幻灯片" class="d-none d-md-block d-lg-none w-100" alt="第二张幻灯片">
              <!-- 响应式图片 - 桌面端 -->
              <img src="https://via.placeholder.com/1200x400?text=第二张幻灯片" class="d-none d-lg-block w-100" alt="第二张幻灯片">
            </div>
            <div class="carousel-item">
              <!-- 响应式图片 - 移动端 -->
              <img src="https://via.placeholder.com/600x300?text=第三张幻灯片" class="d-block w-100 d-md-none" alt="第三张幻灯片">
              <!-- 响应式图片 - 平板端 -->
              <img src="https://via.placeholder.com/900x350?text=第三张幻灯片" class="d-none d-md-block d-lg-none w-100" alt="第三张幻灯片">
              <!-- 响应式图片 - 桌面端 -->
              <img src="https://via.placeholder.com/1200x400?text=第三张幻灯片" class="d-none d-lg-block w-100" alt="第三张幻灯片">
            </div>
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">上一张</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">下一张</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

export default carouselTemplate; 