import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { richTextTemplate } from '../templates/richText.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    richTextBlock: {
      insertRichTextBlock: () => ReturnType
    }
  }
}

export const RichTextBlock = Node.create({
  name: 'richTextBlock',
  
  group: 'block',
  
  content: 'inline*',
  
  draggable: true,
  
  isolating: true,

  parseHTML() {
    return [
      {
        tag: 'h1[style], h2[style], h3[style], h4[style], h5[style], h6[style], p[style], div[style], span[style], ul[style], ol[style], li[style], blockquote[style]',
      },
      {
        tag: '.rich-content, .styled-content'
      },
      {
        tag: 'div.rich-text-block',
      },
      {
        tag: 'div[data-bs-component="rich-text"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'rich-text',
      'class': 'rich-text-block'
    }), 0]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertRichTextBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(richTextTemplate)
        },
    }
  },
}) 