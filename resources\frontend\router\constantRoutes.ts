import { RouteRecordRaw } from 'vue-router'

// 假设所有模块默认导出一个RouteRecordRaw[]类型的数组
export function getModuleRoutes() {
  // 动态导入路由模块
  const modules = import.meta.glob('@/module/**/FrontendView/router.ts', { eager: true })
  let moduleRoutes: RouteRecordRaw[] = []

  Object.values(modules).forEach((module: any) => {
    const routes: RouteRecordRaw[] = module.default
    if (routes) {
      const routesWithQuery = routes.map(route => addQueryToRoute(route))
      moduleRoutes = moduleRoutes.concat(routesWithQuery)
    }
  })

  return moduleRoutes
}

function addQueryToRoute(route: RouteRecordRaw): RouteRecordRaw {
  const newRoute: any = { ...route }

  newRoute.query = { ...newRoute.query, app_code: 'frontend' }

  if (newRoute.children) {
    newRoute.children = newRoute.children.map((childRoute: any) => addQueryToRoute(childRoute))
  }

  return newRoute
}

export function getModuleViewComponents() {
  return import.meta.glob(['@/module/**/views/**/*.vue', '@/module/!User/views/**/*.vue', '@/module/!Develop/views/**/*.vue', '@/module/!Common/views/**/*.vue'])
}
