# 表格组件模板

## 概述

表格组件是管理后台中最常用的数据展示组件。本文档提供了表格组件的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <div class="table-container">
    <!-- 搜索区域 -->
    <el-form :model="searchForm" inline class="search-form">
      <el-form-item label="关键词">
        <el-input
          v-model="searchForm.keyword"
          placeholder="请输入关键词"
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">
        批量删除
      </el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="名称" min-width="120" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'danger'">
            {{ row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { TableInstance } from 'element-plus'

// 类型定义
interface SearchForm {
  keyword: string
  status: number | null
}

interface TableItem {
  id: number
  name: string
  status: number
  createTime: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

// 搜索表单
const searchForm = reactive<SearchForm>({
  keyword: '',
  status: null
})

// 表格数据
const loading = ref(false)
const tableData = ref<TableItem[]>([])
const selectedRows = ref<TableItem[]>([])

// 分页数据
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true
  try {
    // TODO: 调用 API 获取数据
    // const { data, total } = await api.getList({
    //   page: pagination.currentPage,
    //   pageSize: pagination.pageSize,
    //   ...searchForm
    // })
    // tableData.value = data
    // pagination.total = total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchTableData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = null
  handleSearch()
}

// 新增
const handleAdd = () => {
  // TODO: 实现新增逻辑
}

// 编辑
const handleEdit = (row: TableItem) => {
  // TODO: 实现编辑逻辑
}

// 删除
const handleDelete = async (row: TableItem) => {
  try {
    await ElMessageBox.confirm('确认删除该记录吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用删除 API
    ElMessage.success('删除成功')
    fetchTableData()
  } catch {
    // 用户取消删除
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '提示',
      { type: 'warning' }
    )
    // TODO: 调用批量删除 API
    ElMessage.success('批量删除成功')
    fetchTableData()
  } catch {
    // 用户取消删除
  }
}

// 选择行变化
const handleSelectionChange = (rows: TableItem[]) => {
  selectedRows.value = rows
}

// 每页条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchTableData()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  fetchTableData()
}

// 初始化
fetchTableData()
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .table-operations {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
```

## 规范要求

1. 组件结构
   - 搜索区域
   - 操作按钮区域
   - 表格主体
   - 分页区域
   - 类型定义完整

2. 功能实现
   - 搜索和重置
   - 新增和编辑
   - 删除和批量删除
   - 分页和排序
   - 数据加载状态

3. 交互体验
   - 操作确认提示
   - 加载状态展示
   - 错误信息提示
   - 表格溢出处理
   - 批量操作控制

4. 样式规范
   - 布局��理
   - 间距统一
   - 响应式适配
   - 主题样式

## 最佳实践

1. 数据加载
```typescript
const fetchTableData = async () => {
  loading.value = true
  try {
    const { data, total } = await api.getList({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...searchForm
    })
    tableData.value = data
    pagination.total = total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}
```

2. 批量操作
```typescript
const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '提示',
      { type: 'warning' }
    )
    const ids = selectedRows.value.map(row => row.id)
    await api.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchTableData()
  } catch {
    // 用户取消删除
  }
}
```

## 注意事项

1. 表格性能优化
   - 合理使用 `v-show` 和 `v-if`
   - 避免不必要的计算属性
   - 大数据量时使用虚拟滚动
   - 按需加载和懒加载

2. 数据处理
   - 数据格式化
   - 空值处理
   - 时间格式化
   - 状态映射

3. 交互优化
   - 批量操作限制
   - 删除二次确认
   - 表单验证
   - 错误处理

4. 代码组织
   - 逻辑分离
   - 类型定义
   - 注释完整
   - 代码复用
</code_block_to_apply_changes_from>
