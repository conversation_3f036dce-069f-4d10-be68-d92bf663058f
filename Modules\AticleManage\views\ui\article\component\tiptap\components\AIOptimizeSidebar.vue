<template>
  <div class="ai-optimize-sidebar" v-show="visible">
    <div class="sidebar-header">
      <div class="header-left">
        <h3 class="sidebar-title">{{$t('Editor.aiOptimizeSidebar.title')}}</h3>
        <div class="current-block-info" v-if="blockTypeName">
          <span class="block-type-badge">{{ blockTypeName }}</span>
        </div>
      </div>
      <button class="close-button" @click="closeSidebar">
        <el-icon :size="20" >
          <Close />
        </el-icon>
      </button>
    </div>

    <div class="sidebar-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{$t('Editor.aiOptimizeSidebar.loading')}}</div>
      </div>
      
      <div v-else class="main-content">
        <!-- 预设优化方案选择区域 -->
        <div class="preset-section" v-if="presetPrompts.length > 0">
          <p class="section-label">
            <span class="section-icon">🪄</span>
            {{$t('Editor.aiOptimizeSidebar.presetSection')}}
          </p>
          <div class="preset-options">
            <div 
              v-for="(preset, index) in presetPrompts" 
              :key="index"
              class="preset-option"
              :class="{ 'selected': selectedPreset === index }"
              @click="selectPreset(index)"
            >
              <div class="preset-header">
                <span class="preset-icon">📝</span>
                <span class="preset-title">{{ preset.title }}</span>
              </div>
              <p class="preset-description">{{ preset.description }}</p>
            </div>
          </div>
        </div>

        <div class="input-section">
          <p class="section-label">
            <span class="section-icon">✏️</span>
            {{$t('Editor.aiOptimizeSidebar.inputSection')}}
            <span class="label-tip" v-if="selectedPreset !== null">{{$t('Editor.aiOptimizeSidebar.inputSectionSelected')}}</span>
          </p>
          <el-input
            v-model="promptText"
            type="textarea"
            :rows="4"
            :placeholder="$t('Editor.aiOptimizeSidebar.inputPlaceholder')"
            resize="none"
            @keydown.enter.ctrl.prevent="generateContent"
          />
          <div class="prompt-tips">
            <p>{{$t('Editor.aiOptimizeSidebar.tip')}} 
              <el-tooltip :content="$t('Editor.aiOptimizeSidebar.tipHowToWrite')" placement="top">
                <span class="tooltip-trigger">{{$t('Editor.aiOptimizeSidebar.tipHowToWrite')}}</span>
              </el-tooltip>
            </p>
            <div class="quick-actions" v-if="selectedPreset === null">
              <span class="quick-action-label">{{$t('Editor.aiOptimizeSidebar.quickFill')}}:</span>
              <el-button 
                v-for="quickAction in quickActions" 
                :key="quickAction.text"
                size="small" 
                text 
                type="primary"
                @click="fillQuickAction(quickAction.prompt)"
              >
                {{ quickAction.text }}
              </el-button>
            </div>
          </div>

          <div class="action-buttons">
            <el-button 
              type="primary" 
              @click="generateContent" 
              :disabled="loading || !promptText.trim()"
              :loading="loading"
            >
              <span class="button-icon">🪄</span>
              {{$t('Editor.aiOptimizeSidebar.generate')}}
            </el-button>
            <el-button @click="resetForm" v-if="promptText || selectedPreset !== null">
              {{$t('Editor.aiOptimizeSidebar.reset')}}
            </el-button>
            <el-button @click="closeSidebar">
              {{$t('Editor.aiOptimizeSidebar.cancel')}}
            </el-button>
          </div>
        </div>

        <div v-if="generatedContent" class="output-section">
          <div class="output-header">
            <span>{{$t('Editor.aiOptimizeSidebar.result')}}</span>
            <div class="output-actions">
              <el-button type="primary" size="small" @click="applyChange">
                {{$t('Editor.aiOptimizeSidebar.apply')}}
              </el-button>
              <el-button size="small" @click="resetContent">
                {{$t('Editor.aiOptimizeSidebar.regenerate')}}
              </el-button>
            </div>
          </div>
          
          <div class="output-preview">
            <el-tabs v-model="activeTab" class="preview-tabs">
              <el-tab-pane :label="$t('Editor.aiOptimizeSidebar.tab.preview')" name="preview">
                <div class="preview-content">
                  <iframe-renderer :content="generatedContent" height="100%" />
                </div>
              </el-tab-pane>
              <el-tab-pane :label="$t('Editor.aiOptimizeSidebar.tab.code')" name="code">
                <div class="code-editor-container">
                  <div ref="monacoEditorContainer" class="monaco-container"></div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch, onMounted, nextTick } from 'vue'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import * as monaco from 'monaco-editor'
import { 
  MagicStick, 
  EditPen, 
  Document, 
  Picture, 
  Operation, 
  Timer, 
  DataAnalysis, 
  Money, 
  Bell, 
  Share, 
  User, 
  Flag, 
  Grid, 
  List, 
  Menu,
  Postcard,
  Star,
  TrendCharts,
  Promotion,
  ChatDotSquare,
  VideoCamera,
  Service,
  Position
} from '@element-plus/icons-vue'

// 解决TypeScript类型问题，添加默认导出
import { defineAsyncComponent, defineOptions } from 'vue'

defineOptions({
  name: 'AIOptimizeSidebar'
})

// 使用本地ContentRenderer替代iframe-renderer
const IframeRenderer = defineAsyncComponent(() => 
  import('./ContentRenderer.vue')
)

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  },
  blockContent: {
    type: String,
    default: ''
  }
})
console.log(props.blockElement, 'blockElement')

const emit = defineEmits(['close', 'apply-change', 'update:visible'])

// 从blockElement中获取实际的data-bs-component值
const actualBlockType = computed(() => {
  console.log('blockElement:', props.blockElement)
  console.log('blockType prop:', props.blockType)
  
  if (props.blockElement) {
    // 检查是否是HTMLElement
    if (props.blockElement instanceof HTMLElement) {
      const dataBsComponent = props.blockElement.getAttribute('data-bs-component')
      console.log('从HTMLElement获取的 data-bs-component:', dataBsComponent)
      return dataBsComponent || props.blockType
    }
    
    // 检查是否有getAttribute方法
    if (typeof props.blockElement.getAttribute === 'function') {
      const dataBsComponent = props.blockElement.getAttribute('data-bs-component')
      console.log('通过getAttribute获取的 data-bs-component:', dataBsComponent)
      return dataBsComponent || props.blockType
    }
    
    // 直接检查data-bs-component属性
    if ('data-bs-component' in props.blockElement) {
      const dataBsComponent = (props.blockElement as any)['data-bs-component']
      console.log('直接访问 data-bs-component 属性:', dataBsComponent)
      return dataBsComponent || props.blockType
    }
    
    // 检查dataset
    if ('dataset' in props.blockElement && (props.blockElement as any).dataset) {
      const dataBsComponent = (props.blockElement as any).dataset.bsComponent || (props.blockElement as any).dataset['bs-component']
      console.log('从dataset获取的 bsComponent:', dataBsComponent)
      return dataBsComponent || props.blockType
    }
  }
  
  console.log('无法从blockElement获取类型，使用props.blockType:', props.blockType)
  return props.blockType
})

// data-bs-component 到内部blockType的映射
const componentTypeMapping: Record<string, string> = {
  // 直接映射的组件类型 - data-bs-component值 到 内部类型
  'rich-text': 'richTextBlock',
  'social-flow': 'socialFlowBlock', 
  'stats-card': 'statsCardBlock',
  'feature-cards': 'featureCardsBlock',
  'button': 'bootstrap-button',
  'heading': 'bootstrap-heading',
  'alert': 'bootstrap-alert',
  'card': 'bootstrap-card',
  'carousel': 'bootstrap-carousel',
  'accordion': 'bootstrap-accordion',
  'nav': 'bootstrap-nav',
  'form': 'bootstrap-form',
  'table': 'bootstrap-table',
  'image': 'bootstrap-image',
  'countdown': 'bootstrap-countdown',
  'feature-list': 'bootstrap-feature-list',
  'metrics': 'bootstrap-metrics',
  'pricing': 'bootstrap-pricing',
  'cta': 'bootstrap-cta',
  'testimonial-slider': 'testimonialSliderBlock',
  'timeline': 'timelineBlock',
  'navbar': 'navbarBlock',
  'hero': 'heroBlock',
  'info-section': 'infoSectionBlock',
  'footer': 'footerBlock',
  'divider': 'bootstrap-divider',
  'team': 'team-block',
  'partners': 'partners',
  'layout': 'bootstrap-layout',
  
  // 可能的别名或变体映射
  'richtext': 'richTextBlock',
  'rich_text': 'richTextBlock',
  'text': 'richTextBlock',
  'social': 'socialFlowBlock',
  'stats': 'statsCardBlock',
  'features': 'featureCardsBlock',
  'testimonial': 'testimonialSliderBlock',
  'testimonials': 'testimonialSliderBlock',
  'hero-section': 'heroBlock',
  'hero_section': 'heroBlock',
  'info': 'infoSectionBlock',
  'info_section': 'infoSectionBlock',
  
  // 布局相关
  'layout-single': 'layout-single',
  'layout-two-column': 'layout-two-column', 
  'layout-three-column': 'layout-three-column',
  'layout-four-column': 'layout-four-column',
  'layout-1-3-2-3': 'layout-1-3-2-3',
  'layout-2-3-1-3': 'layout-2-3-1-3',
  
  // Bootstrap组件的可能变体
  'btn': 'bootstrap-button',
  'buttons': 'bootstrap-button',
  'img': 'bootstrap-image',
  'picture': 'bootstrap-image',
  'photo': 'bootstrap-image',
  
  // 其他可能的Bootstrap组件映射
  'tab': 'bootstrap-tab',
  'tabs': 'bootstrap-tab',
  'modal': 'bootstrap-modal',
  'popover': 'bootstrap-popover',
  'tooltip': 'bootstrap-tooltip',
  'progress': 'bootstrap-progress',
  'spinner': 'bootstrap-spinner',
  'badge': 'bootstrap-badge',
  'breadcrumb': 'bootstrap-breadcrumb',
  'dropdown': 'bootstrap-dropdown',
  'pagination': 'bootstrap-pagination',
  'list-group': 'bootstrap-list-group',
  'toast': 'bootstrap-toast',
  'jumbotron': 'bootstrap-jumbotron',
  'media': 'bootstrap-media'
}

// 获取映射后的模块类型
const mappedBlockType = computed(() => {
  const actualType = actualBlockType.value
  console.log('原始组件类型:', actualType)
  
  // 先检查是否为内部类型（这些类型不应该被修改）
  const internalTypes = [
    'richTextBlock', 'socialFlowBlock', 'statsCardBlock', 'featureCardsBlock',
    'testimonialSliderBlock', 'timelineBlock', 'navbarBlock', 'heroBlock',
    'infoSectionBlock', 'footerBlock', 'team-block', 'partners',
    // 布局类型也是内部类型
    'layout-single', 'layout-two-column', 'layout-three-column', 'layout-four-column',
    'layout-1-3-2-3', 'layout-2-3-1-3'
  ]
  
  if (internalTypes.includes(actualType)) {
    console.log('识别为内部类型，直接返回:', actualType)
    return actualType
  }
  
  // 然后检查映射表
  if (componentTypeMapping[actualType]) {
    console.log('找到映射:', actualType, '->', componentTypeMapping[actualType])
    return componentTypeMapping[actualType]
  }
  
  // 如果没有映射但是actualType存在且不是空字符串，添加bootstrap-前缀
  if (actualType && actualType !== 'unknown' && actualType !== '' && actualType !== 'default') {
    const bootstrapType = actualType.startsWith('bootstrap-') ? actualType : `bootstrap-${actualType}`
    console.log('添加bootstrap前缀:', actualType, '->', bootstrapType)
    return bootstrapType
  }
  
  // 兜底使用传入的blockType或default
  const fallbackType = props.blockType || 'default'
  console.log('使用兜底类型:', fallbackType)
  return fallbackType
})

console.log('实际组件类型:', actualBlockType.value, '映射后类型:', mappedBlockType.value)

// 提示文本和占位符
const promptText = ref('')
const loading = ref(false)
const generatedContent = ref('')

// 标签页相关
const activeTab = ref('preview')
const monacoEditorContainer = ref<HTMLElement | null>(null)
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 根据块类型提供不同的占位符文本
const placeholderText = computed(() => {
  const blockType = mappedBlockType.value
  const blockTypeMap: Record<string, string> = {
    'bootstrap-button': '例: 优化按钮文案，使用更吸引人的文字，突出行动号召性',
    'bootstrap-card': '例: 调整卡片内容，增加更多细节，使用更专业的语言',
    'heroBlock': '例: 重新设计英雄区域，使其更有冲击力，修改标题和副标题',
    'navbarBlock': '例: 优化导航结构，改进菜单命名，增强品牌展示',
    'richTextBlock': '例: 优化文本内容，改进语法和表达，使其更专业',
    'bootstrap-metrics': '例: 突出关键数据指标，增加对比效果，提升说服力',
    'bootstrap-pricing': '例: 优化价格策略展示，增强价值感知',
    'featureCardsBlock': '例: 突出核心特性，增加应用场景说明',
    'default': '请描述您希望如何优化此模块，例如改进文案、调整样式或添加更多细节'
  }
  
  return blockTypeMap[blockType] || blockTypeMap['default']
})

// 预设提示词类型定义
interface PresetPrompt {
  title: string
  description: string
  prompt: string
  icon: string
}

interface QuickAction {
  text: string
  prompt: string
}

// 预设提示词配置
const presetPromptsConfig: Record<string, PresetPrompt[]> = {
  // 富文本相关
  'richTextBlock': [
    {
      title: '优化文案',
      description: '改进语言表达，使内容更专业、流畅、有吸引力',
      prompt: '优化这段文本内容，使其更专业、更有吸引力。要求：1）改进语言表达，使其更流畅自然；2）增强说服力和可读性；3）保持原意不变的前提下提升文案质量；4）适当增加感情色彩',
      icon: 'EditPen'
    },
    {
      title: '增加细节',
      description: '补充更多有价值的信息和具体细节',
      prompt: '为这段内容添加更多细节和具体信息。要求：1）补充相关的具体数据或案例；2）增加更多有价值的信息点；3）使内容更加丰富和完整；4）保持逻辑清晰',
      icon: 'List'
    },
    {
      title: '调整语调',
      description: '改变文本语调，使其更符合目标受众',
      prompt: '调整这段文本的语调和风格。要求：1）使语调更符合商业/专业场景；2）增强信任感和权威性；3）优化用词选择；4）提升整体品牌形象',
      icon: 'ChatDotSquare'
    }
  ],
  // 按钮相关
  'bootstrap-button': [
    {
      title: '强化号召力',
      description: '优化按钮文案，增强行动号召性',
      prompt: '优化这个按钮的文案，使其更有号召力和吸引力。要求：1）使用动作导向的词汇；2）创造紧迫感；3）突出价值主张；4）保持简洁有力',
      icon: 'Bell'
    },
    {
      title: '多样化选择',
      description: '创建多个按钮选项，满足不同用户需求',
      prompt: '为这个按钮创建多个版本的文案和样式，包括：1）主要行动按钮（强号召力）；2）次要选择按钮（温和引导）；3）了解更多按钮（信息获取）',
      icon: 'Operation'
    },
    {
      title: '添加视觉效果',
      description: '为按钮添加图标、动效等视觉增强',
      prompt: '为这个按钮添加视觉增强效果。要求：1）选择合适的图标；2）优化颜色搭配；3）考虑悬停效果；4）提升视觉吸引力',
      icon: 'Star'
    }
  ],
  // 卡片相关
  'bootstrap-card': [
    {
      title: '完善内容结构',
      description: '优化卡片的标题、内容和行动按钮',
      prompt: '优化这个卡片的整体内容结构。要求：1）创建吸引人的标题；2）丰富描述内容，突出核心价值；3）优化行动按钮文案；4）确保信息层次清晰',
      icon: 'Postcard'
    },
    {
      title: '增加信任元素',
      description: '添加证明、数据等增强可信度的元素',
      prompt: '为这个卡片添加信任元素和社会证明。要求：1）加入相关数据或统计信息；2）添加客户评价或认证标识；3）突出独特卖点；4）增强专业性',
      icon: 'Service'
    },
    {
      title: '优化视觉布局',
      description: '改进卡片的视觉设计和布局结构',
      prompt: '优化这个卡片的视觉设计和布局。要求：1）改进色彩搭配；2）优化间距和对齐；3）增加视觉层次；4）提升整体美观度',
      icon: 'Picture'
    }
  ],
  // 英雄区域相关
  'heroBlock': [
    {
      title: '强化主标题',
      description: '创建更有冲击力的主标题和副标题',
      prompt: '优化英雄区域的主标题和副标题。要求：1）创建有冲击力的主标题，突出核心价值主张；2）编写支撑性的副标题；3）确保信息清晰传达；4）增强视觉冲击力',
      icon: 'Flag'
    },
    {
      title: '优化行动引导',
      description: '完善行动按钮和引导流程',
      prompt: '优化英雄区域的行动引导元素。要求：1）设计主要和次要行动按钮；2）创建清晰的价值层次；3）优化按钮文案和布局；4）引导用户进行期望的行动',
      icon: 'Position'
    },
    {
      title: '增加信任背书',
      description: '添加客户标志、数据等信任元素',
      prompt: '为英雄区域添加信任背书元素。要求：1）加入客户案例或合作伙伴标志；2）展示关键数据或成就；3）添加社会证明元素；4）增强可信度',
      icon: 'TrendCharts'
    }
  ],
  // 导航栏相关
  'navbarBlock': [
    {
      title: '优化导航结构',
      description: '改进导航菜单的结构和命名',
      prompt: '优化导航栏的菜单结构和命名。要求：1）简化导航层次；2）使用清晰的菜单命名；3）优化用户体验流程；4）确保导航逻辑清晰',
      icon: 'Menu'
    },
    {
      title: '增强品牌展示',
      description: '突出品牌标识和核心信息',
      prompt: '增强导航栏的品牌展示效果。要求：1）优化品牌标识的展示；2）突出核心业务信息；3）增加品牌认知度；4）提升专业形象',
      icon: 'Star'
    },
    {
      title: '添加功能元素',
      description: '加入搜索、语言切换等功能元素',
      prompt: '为导航栏添加实用的功能元素。要求：1）考虑搜索功能；2）添加语言/地区切换；3）包含用户登录入口；4）优化移动端体验',
      icon: 'Service'
    }
  ],
  // 数据统计相关
  'bootstrap-metrics': [
    {
      title: '突出关键数据',
      description: '选择和展示最有说服力的数据指标',
      prompt: '优化数据统计模块的内容展示。要求：1）选择最有说服力的关键指标；2）使用清晰的数据可视化；3）添加数据说明和背景；4）突出业务成就',
      icon: 'DataAnalysis'
    },
    {
      title: '增加对比效果',
      description: '添加时间对比、行业对比等元素',
      prompt: '为数据统计添加对比和趋势元素。要求：1）显示增长趋势；2）加入行业对比数据；3）突出改进成果；4）增强说服力',
      icon: 'TrendCharts'
    }
  ],
  // 定价卡片相关
  'bootstrap-pricing': [
    {
      title: '优化价格策略',
      description: '改进定价展示和价值主张',
      prompt: '优化定价卡片的价格策略展示。要求：1）突出最受欢迎的套餐；2）明确各套餐的价值差异；3）添加价格锚点效应；4）优化价格展示方式',
      icon: 'Money'
    },
    {
      title: '增强价值感知',
      description: '突出每个套餐的独特价值和优势',
      prompt: '增强定价卡片的价值感知。要求：1）详细说明每个功能的价值；2）添加"最受欢迎"等标签；3）突出性价比优势；4）减少价格敏感性',
      icon: 'Promotion'
    }
  ],
  // 特性卡片相关
  'featureCardsBlock': [
    {
      title: '突出核心特性',
      description: '重点展示产品或服务的核心优势',
      prompt: '优化特性卡片的内容展示。要求：1）突出3-4个核心特性；2）使用有吸引力的标题；3）提供具体的价值说明；4）增加视觉图标支持',
      icon: 'Grid'
    },
    {
      title: '增加应用场景',
      description: '为每个特性添加具体的应用场景',
      prompt: '为特性卡片添加具体的应用场景。要求：1）描述每个特性的实际应用；2）提供具体的使用案例；3）帮助用户理解价值；4）增强代入感',
      icon: 'VideoCamera'
    }
  ],
  // 统计卡片
  'statsCardBlock': [
    {
      title: '数据可视化',
      description: '优化数据展示方式，增强视觉冲击力',
      prompt: '优化统计卡片的数据展示。要求：1）使用更有冲击力的数字展示；2）添加增长趋势指示；3）配色突出重要数据；4）增加图表元素',
      icon: 'DataAnalysis'
    },
    {
      title: '添加解释说明',
      description: '为统计数据添加背景说明和意义解释',
      prompt: '为统计数据添加背景说明。要求：1）解释数据的来源和意义；2）说明数据背后的成就；3）增加时间维度对比；4）提升数据可信度',
      icon: 'Document'
    }
  ],
  // 社交媒体相关
  'socialFlowBlock': [
    {
      title: '优化社交元素',
      description: '改进社交媒体图标和链接的展示',
      prompt: '优化社交媒体模块的展示效果。要求：1）选择最相关的社交平台；2）使用统一的视觉风格；3）添加关注提示；4）增强互动性',
      icon: 'Share'
    }
  ],
  // 客户评价相关
  'testimonialSliderBlock': [
    {
      title: '优化评价内容',
      description: '改进客户评价的内容和展示方式',
      prompt: '优化客户评价模块的内容。要求：1）选择最有说服力的评价；2）添加客户身份信息；3）突出关键成果；4）增强可信度',
      icon: 'User'
    }
  ],
  // 时间线相关
  'timelineBlock': [
    {
      title: '优化时间节点',
      description: '改进时间线的关键节点和描述',
      prompt: '优化时间线模块的内容展示。要求：1）突出关键时间节点；2）丰富每个阶段的描述；3）添加具体成果；4）增强时间感',
      icon: 'Timer'
    }
  ],
  // 信息区块相关
  'infoSectionBlock': [
    {
      title: '内容重组',
      description: '重新组织信息的结构和层次',
      prompt: '重新组织信息区块的内容结构。要求：1）优化信息层次；2）突出核心信息；3）增加视觉分割；4）提升可读性',
      icon: 'List'
    }
  ],
  // 页脚相关
  'footerBlock': [
    {
      title: '完善页脚信息',
      description: '优化页脚的信息结构和联系方式',
      prompt: '优化页脚的信息展示。要求：1）整理联系信息；2）添加重要链接；3）包含版权信息；4）保持简洁专业',
      icon: 'Document'
    }
  ],
  // 布局相关
  'layout-single': [
    {
      title: '优化单列布局',
      description: '改进单列布局的内容结构和视觉效果',
      prompt: '优化单列布局的设计和内容结构。要求：1）改进内容的视觉层次；2）优化间距和对齐；3）增强可读性；4）提升整体美观度',
      icon: 'Document'
    }
  ],
  'layout-two-column': [
    {
      title: '平衡双列内容',
      description: '优化双列布局的内容分配和视觉平衡',
      prompt: '优化双列布局的内容分配。要求：1）平衡左右列的内容重量；2）确保视觉协调；3）优化内容层次；4）提升整体布局效果',
      icon: 'Document'
    }
  ],
  'layout-three-column': [
    {
      title: '优化三列布局',
      description: '改进三列布局的内容分配和视觉效果',
      prompt: '优化三列布局的内容分配。要求：1）平衡三列的内容重量；2）确保视觉协调；3）优化内容层次；4）提升整体布局效果',
      icon: 'Document'
    }
  ],
  'layout-four-column': [
    {
      title: '优化四列布局',
      description: '改进四列布局的内容分配和视觉效果',
      prompt: '优化四列布局的内容分配。要求：1）平衡四列的内容重量；2）确保视觉协调；3）优化内容层次；4）提升整体布局效果',
      icon: 'Document'
    }
  ],
  'layout-1-3-2-3': [
    {
      title: '优化不等分布局',
      description: '改进左1/3右2/3布局的内容分配',
      prompt: '优化左1/3右2/3布局的内容分配。要求：1）合理利用窄列空间；2）充分利用宽列展示主要内容；3）确保视觉平衡；4）提升布局效果',
      icon: 'Document'
    }
  ],
  'layout-2-3-1-3': [
    {
      title: '优化不等分布局',
      description: '改进左2/3右1/3布局的内容分配',
      prompt: '优化左2/3右1/3布局的内容分配。要求：1）充分利用宽列展示主要内容；2）合理利用窄列空间；3）确保视觉平衡；4）提升布局效果',
      icon: 'Document'
    }
  ],
  // 其他Bootstrap组件
  'bootstrap-heading': [
    {
      title: '优化标题内容',
      description: '改进标题的文案和视觉层次',
      prompt: '优化标题的内容和展示效果。要求：1）创建吸引人的标题文案；2）确保信息清晰传达；3）增强视觉冲击力；4）符合内容主题',
      icon: 'Flag'
    }
  ],
  'bootstrap-alert': [
    {
      title: '优化提示信息',
      description: '改进提示框的内容和样式',
      prompt: '优化提示框的内容和展示。要求：1）使用清晰的提示语言；2）选择合适的提示类型；3）突出重要信息；4）提升用户体验',
      icon: 'Bell'
    }
  ],
  'bootstrap-accordion': [
    {
      title: '优化折叠内容',
      description: '改进手风琴的标题和内容结构',
      prompt: '优化手风琴组件的内容结构。要求：1）创建清晰的折叠标题；2）丰富内容描述；3）优化信息层次；4）提升交互体验',
      icon: 'Menu'
    }
  ],
  'bootstrap-countdown': [
    {
      title: '优化倒计时设计',
      description: '改进倒计时的视觉效果和文案',
      prompt: '优化倒计时组件的设计。要求：1）选择合适的时间格式；2）添加活动说明；3）增强紧迫感；4）提升视觉吸引力',
      icon: 'Timer'
    }
  ],
  'bootstrap-feature-list': [
    {
      title: '优化功能列表',
      description: '改进功能列表的内容和展示方式',
      prompt: '优化功能列表的内容展示。要求：1）突出核心功能；2）使用清晰的描述；3）添加图标支持；4）提升视觉效果',
      icon: 'List'
    }
  ],
  'bootstrap-cta': [
    {
      title: '强化号召行动',
      description: '优化CTA的文案和视觉设计',
      prompt: '优化号召行动模块的效果。要求：1）创建有力的行动文案；2）突出价值主张；3）增强视觉冲击力；4）提升转化率',
      icon: 'Bell'
    }
  ],
  'bootstrap-image': [
    {
      title: '优化图片展示',
      description: '改进图片的说明文字和展示效果',
      prompt: '优化图片模块的展示效果。要求：1）添加有意义的图片说明；2）优化图片布局；3）增加视觉层次；4）提升整体美观度',
      icon: 'Picture'
    }
  ],
  'team-block': [
    {
      title: '优化团队介绍',
      description: '改进团队成员的介绍和展示方式',
      prompt: '优化团队展示模块的内容。要求：1）完善成员介绍；2）突出专业能力；3）增加个人亮点；4）提升团队形象',
      icon: 'User'
    }
  ],
  'partners': [
    {
      title: '优化合作伙伴展示',
      description: '改进合作伙伴的介绍和布局',
      prompt: '优化合作伙伴展示模块。要求：1）突出重要合作伙伴；2）添加合作成果；3）优化视觉布局；4）增强可信度',
      icon: 'Share'
    }
  ],
  // 默认通用
  'default': [
    {
      title: '内容优化',
      description: '全面优化模块的内容质量和表达效果',
      prompt: '全面优化这个模块的内容。要求：1）改进文案表达；2）增强视觉效果；3）提升用户体验；4）确保信息传达清晰有效',
      icon: 'MagicStick'
    },
    {
      title: '设计增强',
      description: '改进模块的视觉设计和用户体验',
      prompt: '改进这个模块的视觉设计。要求：1）优化色彩搭配；2）改进布局结构；3）增加视觉层次；4）提升整体美观度和专业性',
      icon: 'Picture'
    }
  ]
}

// 快速操作配置
const quickActionsConfig: Record<string, QuickAction[]> = {
  'default': [
    { text: '优化文案', prompt: '优化这个模块的文案，使其更专业、更有吸引力' },
    { text: '增加细节', prompt: '为这个模块添加更多细节和具体信息' },
    { text: '改进设计', prompt: '改进这个模块的视觉设计和布局' },
    { text: '增强互动', prompt: '为这个模块增加更多的用户互动元素' }
  ]
}

// 选中的预设方案索引
const selectedPreset = ref<number | null>(null)

// 计算当前模块类型的预设提示词
const presetPrompts = computed(() => {
  const blockType = mappedBlockType.value
  return presetPromptsConfig[blockType] || presetPromptsConfig['default']
})

// 计算当前模块类型的快速操作
const quickActions = computed(() => {
  const blockType = mappedBlockType.value
  return quickActionsConfig[blockType] || quickActionsConfig['default']
})

// 计算当前占位符文本
const currentPlaceholder = computed(() => {
  if (selectedPreset.value !== null && presetPrompts.value[selectedPreset.value]) {
    return `已选择: ${presetPrompts.value[selectedPreset.value].title} - 您可以直接生成或修改下面的指令`
  }
  return placeholderText.value
})

// 计算提示内容
const tipContent = computed(() => {
  if (selectedPreset.value !== null) {
    return '您已选择预设方案，可以直接生成内容，或者修改指令后再生成'
  }
  return '描述你希望如何优化这个模块，比如"改进文案""调整样式""添加更多细节"等'
})

// 计算模块类型显示名称
const blockTypeName = computed(() => {
  const typeNames: Record<string, string> = {
    'richTextBlock': '富文本',
    'bootstrap-button': '按钮',
    'bootstrap-card': '卡片',
    'heroBlock': '英雄区域',
    'navbarBlock': '导航栏',
    'bootstrap-metrics': '数据统计',
    'bootstrap-pricing': '定价卡片',
    'featureCardsBlock': '特性卡片',
    'statsCardBlock': '数据卡片',
    'socialFlowBlock': '社交媒体',
    'testimonialSliderBlock': '客户评价',
    'timelineBlock': '时间线',
    'infoSectionBlock': '信息区块',
    'footerBlock': '页脚',
    'layout-single': '单列布局',
    'layout-two-column': '双列布局',
    'layout-three-column': '三列布局',
    'layout-four-column': '四列布局',
    'layout-1-3-2-3': '左1/3右2/3布局',
    'layout-2-3-1-3': '左2/3右1/3布局',
    'bootstrap-heading': '大标题',
    'bootstrap-alert': '提示框',
    'bootstrap-accordion': '手风琴',
    'bootstrap-divider': '间隔线',
    'bootstrap-countdown': '倒计时',
    'bootstrap-feature-list': '功能列表',
    'bootstrap-cta': '号召行动',
    'bootstrap-image': '图片',
    'bootstrap-carousel': '轮播图',
    'bootstrap-nav': '导航菜单',
    'bootstrap-form': '表单',
    'bootstrap-table': '表格',
    'bootstrap-layout': '布局',
    'team-block': '团队展示',
    'partners': '合作伙伴'
  }
  return typeNames[mappedBlockType.value] || '未知模块'
})

// 添加映射调试信息监听器 - 确保在所有computed属性定义之后执行
watch([actualBlockType, mappedBlockType], ([actualType, mappedType]) => {
  console.log('===== AI优化侧边栏模块识别 =====')
  console.log('props.blockType:', props.blockType)
  console.log('从元素获取的类型:', actualType)
  console.log('最终映射类型:', mappedType)
  console.log('显示名称:', blockTypeName.value)
  console.log('可用预设数量:', presetPrompts.value.length)
  console.log('================================')
}, { immediate: true })

// 选择预设方案
const selectPreset = (index: number) => {
  if (selectedPreset.value === index) {
    // 如果点击已选中的预设，取消选择
    selectedPreset.value = null
    promptText.value = ''
  } else {
    selectedPreset.value = index
    promptText.value = presetPrompts.value[index].prompt
  }
}

// 填充快速操作
const fillQuickAction = (prompt: string) => {
  promptText.value = prompt
}

// 重置表单时也重置预设选择
const resetForm = () => {
  promptText.value = ''
  generatedContent.value = ''
  loading.value = false
  activeTab.value = 'preview'
  selectedPreset.value = null
}

// 监听可见性变化，重置状态
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 侧边栏打开时的处理
    nextTick(() => {
      if (generatedContent.value && activeTab.value === 'code') {
        initMonacoEditor()
      }
    })
  } else {
    // 侧边栏关闭时重置状态
    resetForm()
    disposeEditor()
  }
})

// 监听标签页切换
watch(() => activeTab.value, (newValue) => {
  if (newValue === 'code' && generatedContent.value) {
    nextTick(() => {
      initMonacoEditor()
    })
  }
})

// 监听生成内容变化
watch(() => generatedContent.value, (newValue) => {
  if (newValue && activeTab.value === 'code') {
    nextTick(() => {
      initMonacoEditor()
    })
  }
})

// 初始化Monaco编辑器
const initMonacoEditor = () => {
  if (!monacoEditorContainer.value) return
  
  // 如果已经有编辑器实例，先销毁
  if (editor) {
    editor.dispose()
  }
  
  // 创建编辑器
  editor = monaco.editor.create(monacoEditorContainer.value, {
    value: generatedContent.value,
    language: 'html',
    theme: 'vs',
    automaticLayout: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    lineNumbers: 'on',
    readOnly: false, // 可以设置为true如果只想查看代码
    wordWrap: 'on'
  })
  
  // 监听编辑器内容变化
  editor.onDidChangeModelContent(() => {
    if (editor) {
      const newContent = editor.getValue()
      generatedContent.value = newContent
    }
  })
}

// 销毁编辑器
const disposeEditor = () => {
  if (editor) {
    editor.dispose()
    editor = null
  }
}

// 关闭侧边栏
const closeSidebar = () => {
  emit('update:visible', false)
  emit('close')
}

// 生成优化内容
const generateContent = async () => {
  if (!promptText.value.trim()) {
    ElMessage.warning(t('Editor.aiOptimizeSidebar.inputError'))
    return
  }
  
  try {
    loading.value = true
    
    // 准备发送到API的数据
    const requestData = {
      block_type: mappedBlockType.value,
      original_content: props.blockContent,
      instruction: promptText.value.trim(),
    }
    
    // 模拟API调用
    // 实际项目中，这里应该调用真实的API
    const response = await mockApiCall(requestData)
    
    if (response.success) {
      generatedContent.value = response.content
      ElMessage.success(t('Editor.aiOptimizeSidebar.generateSuccess'))
      
      // 如果当前在代码标签页，初始化编辑器
      if (activeTab.value === 'code') {
        nextTick(() => {
          initMonacoEditor()
        })
      }
    } else {
      ElMessage.error(response.message || t('Editor.aiOptimizeSidebar.generateError'))
    }
  } catch (error) {
    console.error('生成内容时出错:', error)
    ElMessage.error(t('Editor.aiOptimizeSidebar.generateError'))
  } finally {
    loading.value = false
  }
}

// 模拟API调用函数（实际项目中应替换为真实API调用）
const mockApiCall = async (data: any) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 根据不同的块类型返回不同的模拟内容
  let mockContent = ''
  
  if (data.block_type.includes('button')) {
    mockContent = `<button class="btn btn-primary btn-lg">立即行动，获取专属优惠！</button>`
  } else if (data.block_type.includes('card')) {
    mockContent = `
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">专业解决方案</h5>
          <p class="card-text">我们提供行业领先的专业服务，帮助您实现业务目标，提升企业价值。通过定制化方案满足您的独特需求。</p>
          <a href="#" class="btn btn-primary">了解更多</a>
        </div>
      </div>
    `
  } else if (data.block_type.includes('hero')) {
    mockContent = `
      <div class="p-5 text-center text-white hero-container bg-primary">
        <h1 class="display-4 fw-bold">引领行业创新，成就卓越未来</h1>
        <p class="lead">借助我们的专业知识和丰富经验，为您的业务注入新的活力和发展动力</p>
        <div class="gap-2 d-grid d-sm-flex justify-content-sm-center">
          <button type="button" class="gap-3 px-4 btn btn-light btn-lg">立即开始</button>
          <button type="button" class="px-4 btn btn-outline-light btn-lg">了解更多</button>
        </div>
      </div>
    `
  } else {
    // 默认返回一些优化后的富文本内容
    mockContent = `
      <div>
        <h2>专业解决方案，满足您的需求</h2>
        <p>我们提供全方位的专业服务，帮助您解决业务挑战，实现可持续发展。通过深入了解您的需求，我们定制最适合您的解决方案。</p>
        <ul>
          <li>专业的团队支持</li>
          <li>定制化的解决方案</li>
          <li>全天候的技术支持</li>
          <li>行业领先的技术应用</li>
        </ul>
        <p>立即与我们联系，开启成功之旅！</p>
      </div>
    `
  }
  
  // 声明正确的返回类型包含message字段
  return {
    success: true,
    content: mockContent,
    message: '' // 添加message字段以满足类型检查
  }
}

// 应用生成的内容
const applyChange = () => {
  if (!generatedContent.value) {
    ElMessage.warning(t('Editor.aiOptimizeSidebar.applyNoContent'))
    return
  }
  
  // 如果在代码标签页并且编辑器存在，确保使用最新的编辑器内容
  if (activeTab.value === 'code' && editor) {
    generatedContent.value = editor.getValue()
  }
  
  emit('apply-change', generatedContent.value)
  ElMessage.success(t('Editor.aiOptimizeSidebar.applySuccess'))
  
  // 应用后关闭侧边栏
  setTimeout(() => {
    closeSidebar()
  }, 500)
}

// 重置表单
const resetContent = () => {
  generatedContent.value = ''
  if (editor) {
    editor.setValue('')
  }
}

// 组件卸载时清理资源
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

const handleResize = () => {
  if (editor) {
    editor.layout()
  }
}
</script>

<style lang="scss" scoped>
.ai-optimize-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 450px; /* 增加宽度以容纳更多内容 */
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.current-block-info {
  display: flex;
  align-items: center;
}

.block-type-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  width: 30px;
  height: 30px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 4px;
  
  border-radius: 50%;
  transition: all 0.3s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  .loading-text {
    color: #666;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 预设方案区域样式 */
.preset-section {
  margin-bottom: 20px;
}

.section-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  
  .section-icon {
    font-size: 16px;
    color: #409eff;
  }
  
  .label-tip {
    font-size: 12px;
    color: #67c23a;
    font-weight: 400;
    background: rgba(103, 194, 58, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
  }
}

.preset-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preset-option {
  border: 2px solid #e6e6e6;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
  
  &:hover {
    border-color: #409eff;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  }
  
  &.selected {
    border-color: #409eff;
    background: linear-gradient(135deg, #ecf5ff 0%, #e6f7ff 100%);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
    
    .preset-header {
      .preset-title {
        color: #409eff;
        font-weight: 600;
      }
      
      .preset-icon {
        color: #409eff;
      }
    }
  }
}

.preset-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.preset-icon {
  font-size: 16px;
  color: #666;
  transition: color 0.3s;
}

.preset-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0;
  transition: all 0.3s;
}

.preset-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.prompt-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  
  .tooltip-trigger {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
  }
}

.quick-actions {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  
  .quick-action-label {
    font-size: 12px;
    color: #666;
    margin-right: 8px;
    font-weight: 500;
  }
  
  .el-button {
    margin: 2px 4px 2px 0;
    height: 24px;
    padding: 0 8px;
    font-size: 11px;
    
    &:hover {
      background: #409eff;
      color: white;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    &[type="primary"] {
      background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #66b1ff 0%, #4dabf7 100%);
      }
      
      &:disabled {
        background: #c0c4cc;
      }
    }
  }
}

.output-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  
  span {
    font-weight: 500;
    font-size: 14px;
  }
  
  .output-actions {
    display: flex;
    gap: 8px;
  }
}

.output-preview {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 10px;
  min-height: 200px;
  background: #f8f9fa;
  overflow: auto;
  
  .preview-content {
    width: 100%;
    height: 100%;
    min-height: 180px;
  }
}

.preview-tabs {
  width: 100%;
  height: 100%;
  
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    overflow: auto;
  }
  
  :deep(.el-tab-pane) {
    height: 100%;
  }
  
  :deep(.el-tabs__header) {
    margin: 0 0 10px 0;
  }
  
  :deep(.el-tabs__nav-wrap) {
    background: white;
    border-radius: 4px;
  }
  
  :deep(.el-tabs__active-bar) {
    background-color: #409eff;
  }
  
  :deep(.el-tabs__item.is-active) {
    color: #409eff;
  }
}

.code-editor-container {
  width: 100%;
  height: 300px;
  overflow: hidden;
  
  .monaco-container {
    width: 100%;
    height: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ai-optimize-sidebar {
    width: 400px;
  }
}

@media (max-width: 768px) {
  .ai-optimize-sidebar {
    width: 100%;
    right: 0;
  }
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
  
  &:hover {
    background: #909399;
  }
}

/* 动画效果 */
.preset-option {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框样式增强 */
.input-section {
  :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: 2px solid #e6e6e6;
    transition: all 0.3s;
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

/* 成功状态指示 */
.label-tip {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style> 