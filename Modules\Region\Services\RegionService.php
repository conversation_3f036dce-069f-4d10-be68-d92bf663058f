<?php

namespace Modules\Region\Services;

use Modules\Region\Models\Region;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * 区域管理服务类
 */
class RegionService
{
    /**
     * 获取区域列表（包含频道数量）
     * @param array $params
     * @return array
     */
    public function getRegionListWithChannelCount(array $params): array
    {
        $query = Region::query();

        // 按名称搜索
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        // 按状态筛选
        if (isset($params['status']) && !empty($params['status'])) {
            if ($params['status'] == 1) {
                $query->enabled();
            } else {
                $query->disabled();
            }
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        $regions = $query->paginate($perPage);

        // 添加频道数量
        $items = $regions->getCollection()->map(function ($region) {
            $regionData = $region->toArray();
            $regionData['channel_num'] = $region->channels()->count();
            return $regionData;
        })->toArray();

        return [
            'total' => $regions->total(),
            'page' => $regions->currentPage(),
            'limit' => $regions->perPage(),
            'items' => $items
        ];
    }

    /**
     * 获取区域列表
     * @param array $params
     * @return LengthAwarePaginator
     */
    public function getRegionList(array $params): LengthAwarePaginator
    {
        $query = Region::query();

        // 按名称搜索
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        // 按状态筛选
        if (isset($params['status']) && !empty($params['status'])) {
            if ($params['status'] == 1) {
                $query->enabled();
            } else {
                $query->disabled();
            }
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * 根据ID获取区域
     * @param int $id
     * @return Region|null
     */
    public function getRegionById(int $id): ?Region
    {
        return Region::find($id);
    }

    /**
     * 创建区域
     * @param array $params
     * @return Region
     */
    public function createRegion(array $params): Region
    {
        return Region::create($params);
    }

    /**
     * 更新区域
     * @param int $id
     * @param array $params
     * @return Region|null
     */
    public function updateRegion(int $id, array $params): ?Region
    {
        $region = Region::find($id);
        if (!$region) {
            return null;
        }

        $region->update($params);
        return $region->fresh();
    }

    /**
     * 删除区域
     * @param int $id
     * @return bool
     */
    public function deleteRegion(int $id): bool
    {
        $region = Region::find($id);
        if (!$region) {
            return false;
        }

        return $region->delete();
    }

    /**
     * 更新区域状态
     * @param int $id
     * @param int $status
     * @param int $updatedBy
     * @return bool
     */
    public function updateRegionStatus(int $id, int $status, int $updatedBy): bool
    {
        $region = Region::find($id);
        if (!$region) {
            return false;
        }

        return $region->update([
            'status' => $status,
            'updated_by' => $updatedBy
        ]);
    }

    /**
     * 批量操作
     * @param string $action
     * @param array $ids
     * @param int $operatorId
     * @return array
     */
    public function batchAction(string $action, array $ids, int $operatorId): array
    {
        $result = [
            'success_count' => 0,
            'failed_count' => 0,
            'failed_ids' => []
        ];

        foreach ($ids as $id) {
            $region = Region::find($id);
            if (!$region) {
                $result['failed_count']++;
                $result['failed_ids'][] = $id;
                continue;
            }

            $success = false;
            switch ($action) {
                case 'delete':
                    $success = $region->delete();
                    break;
                case 'enable':
                    $success = $region->update([
                        'status' => 1,
                        'updated_by' => $operatorId
                    ]);
                    break;
                case 'disable':
                    $success = $region->update([
                        'status' => 0,
                        'updated_by' => $operatorId
                    ]);
                    break;
                case 'update_sort':
                    // update_sort 操作需要额外的数据，这里只返回成功
                    $result['success_count']++;
                    break;
            }

            if ($success) {
                $result['success_count']++;
            } else {
                $result['failed_count']++;
                $result['failed_ids'][] = $id;
            }
        }

        return $result;
    }

    /**
     * 获取所有启用的区域选项
     * @return array
     */
    public function getRegionOptions(): array
    {
        return Region::enabled()
            ->select(['id', 'name'])
            ->orderBy('name')
            ->get()
            ->toArray();
    }
} 