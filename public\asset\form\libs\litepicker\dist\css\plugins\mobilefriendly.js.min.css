/*!
 * 
 * ../css/plugins/mobilefriendly.js.css
 * Litepicker v2.0.12 (https://github.com/wakirin/Litepicker)
 * Package: litepicker (https://www.npmjs.com/package/litepicker)
 * License: MIT (https://github.com/wakirin/Litepicker/blob/master/LICENCE.md)
 * Copyright 2019-2021 Rinat G.
 *     
 * Hash: fc3887e0bb19d54c36db
 * 
 */
@-webkit-keyframes lp-bounce-target-next{0%{-webkit-transform:translateX(100px) scale(.5);transform:translateX(100px) scale(.5)}to{-webkit-transform:translateX(0) scale(1);transform:translateX(0) scale(1)}}@keyframes lp-bounce-target-next{0%{-webkit-transform:translateX(100px) scale(.5);transform:translateX(100px) scale(.5)}to{-webkit-transform:translateX(0) scale(1);transform:translateX(0) scale(1)}}@-webkit-keyframes lp-bounce-target-prev{0%{-webkit-transform:translateX(-100px) scale(.5);transform:translateX(-100px) scale(.5)}to{-webkit-transform:translateX(0) scale(1);transform:translateX(0) scale(1)}}@keyframes lp-bounce-target-prev{0%{-webkit-transform:translateX(-100px) scale(.5);transform:translateX(-100px) scale(.5)}to{-webkit-transform:translateX(0) scale(1);transform:translateX(0) scale(1)}}:root{--litepicker-mobilefriendly-backdrop-color-bg:#000}.litepicker-backdrop{display:none;background-color:var(--litepicker-mobilefriendly-backdrop-color-bg);opacity:.3;position:fixed;top:0;right:0;bottom:0;left:0}.litepicker.mobilefriendly[data-plugins*=mobilefriendly]{-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);font-size:1.1rem;--litepicker-container-months-box-shadow-color:#616161}.litepicker.mobilefriendly-portrait{--litepicker-day-width:13.5vw;--litepicker-month-width:calc(var(--litepicker-day-width) * 7)}.litepicker.mobilefriendly-landscape{--litepicker-day-width:5.5vw;--litepicker-month-width:calc(var(--litepicker-day-width) * 7)}.litepicker-open,.litepicker[data-plugins*=mobilefriendly] .container__months{overflow:hidden}.litepicker.mobilefriendly[data-plugins*=mobilefriendly] .container__months .month-item-header{height:var(--litepicker-day-width)}.litepicker.mobilefriendly[data-plugins*=mobilefriendly] .container__days>div{height:var(--litepicker-day-width);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.litepicker[data-plugins*=mobilefriendly] .container__months .month-item{-webkit-transform-origin:center;transform-origin:center}.litepicker[data-plugins*=mobilefriendly] .container__months .month-item.touch-target-next,.litepicker[data-plugins*=mobilefriendly] .container__months .month-item.touch-target-prev{-webkit-animation-name:lp-bounce-target-next;animation-name:lp-bounce-target-next;-webkit-animation-duration:.5s;animation-duration:.5s;-webkit-animation-timing-function:ease;animation-timing-function:ease}.litepicker[data-plugins*=mobilefriendly] .container__months .month-item.touch-target-prev{-webkit-animation-name:lp-bounce-target-prev;animation-name:lp-bounce-target-prev}