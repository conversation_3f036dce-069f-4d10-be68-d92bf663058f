

jQuery(function($) {

    var $el = jQuery('#w0 .kv-hint-special')
    if ($el.length) {
        $el.each(function() {
            $(this).activeFieldHint()
        })
    }
    kvBs4InitForm()
    jQuery && jQuery.pjax && (jQuery.pjax.defaults.maxCacheLength = 0)
    kvInitHtml5('#theme-color', '#theme-color-source')
    if (jQuery('#theme-color').data('spectrum')) {
        jQuery('#theme-color').spectrum('destroy')
    }

    jQuery.when(jQuery('#theme-color-source').spectrum(spectrum_81510b99)).done(function() {
        jQuery('#theme-color-source').spectrum('set', jQuery('#theme-color').val())
        jQuery('#theme-color-cont').removeClass('kv-center-loading')
    })

    if (jQuery('#w1').data('select2')) {
        jQuery('#w1').select2('destroy')
    }
    jQuery.when(jQuery('#w1').select2(select2_b1e179e0)).done(initS2Loading('w1', 's2options_fd268a17'))
    jQuery('#w1').on('select2:select', previewSelected)
    jQuery('#w1').on('select2:unselect', previewUnselected)

    new TomSelect('#theme-created_by', {
        'openOnFocus': false, 'create': false, 'multiple': false, 'selectOnTab': true, 'onType':
            function(str) {
                if (str.length < 3) {
                    $(this.dropdown).hide()
                } else {
                    $(this.dropdown).show()
                }
            }, 'valueField': 'id', 'labelField': 'username', 'searchField': 'username', 'load':
            function(query, callback) {
                var url = options.hasPrettyUrls
                    ? options.userListUrl + '?q=' + encodeURIComponent(query)
                    : options.userListUrl + '&q=' + encodeURIComponent(query)
                fetch(url)
                    .then(function(response) {
                        return response.json()
                    })
                    .then(function(json) {
                        callback(json.items)
                    })
                    .catch(() => {
                        callback()
                    })
            },
    })
    new TomSelect('#theme-users', {
        'openOnFocus': false, 'create': false, 'multiple': true, 'selectOnTab': true, 'onType':
            function(str) {
                if (str.length < 3) {
                    $(this.dropdown).hide()
                } else {
                    $(this.dropdown).show()
                }
            }, 'valueField': 'id', 'labelField': 'username', 'searchField': 'username', 'load':
            function(query, callback) {
                var url = '/ajax/user-list?e=1&q=' + encodeURIComponent(query)
                var url = options.hasPrettyUrls
                    ? options.userListUrl + '?q=' + encodeURIComponent(query) + '&e=' + '1'
                    : options.userListUrl + '&q=' + encodeURIComponent(query) + '&e=' + '1'
                fetch(url)
                    .then(function(response) {
                        return response.json()
                    })
                    .then(function(json) {
                        callback(json.items)
                    })
                    .catch(() => {
                        callback()
                    })
            },
    })
    jQuery('#w0').yiiActiveForm([{
        'id': 'theme-name',
        'name': 'name',
        'container': '.field-theme-name',
        'input': '#theme-name',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '名字不能为空。' })
            yii.validation.string(value, messages, {
                'message': '名字必须是一条字符串。',
                'max': 255,
                'tooLong': '名字只能包含至多255个字符。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'theme-description',
        'name': 'description',
        'container': '.field-theme-description',
        'input': '#theme-description',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.string(value, messages, {
                'message': '描述必须是一条字符串。',
                'max': 510,
                'tooLong': '描述只能包含至多510个字符。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'theme-color',
        'name': 'color',
        'container': '.field-theme-color',
        'input': '#theme-color',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.string(value, messages, {
                'message': '主要颜色必须是一条字符串。',
                'max': 255,
                'tooLong': '主要颜色只能包含至多255个字符。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'theme-css',
        'name': 'css',
        'container': '.field-theme-css',
        'input': '#theme-css',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': 'Css不能为空。' })
            yii.validation.string(value, messages, { 'message': 'Css必须是一条字符串。', 'skipOnEmpty': 1 })
        },
    }, {
        'id': 'theme-created_by',
        'name': 'created_by',
        'container': '.field-theme-created_by',
        'input': '#theme-created_by',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': '创建必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }, {
        'id': 'theme-shared',
        'name': 'shared',
        'container': '.field-theme-shared',
        'input': '#theme-shared',
        'error': '.invalid-feedback',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.number(value, messages, {
                'pattern': /^[+-]?\d+$/,
                'message': 'Shared With必须是整数。',
                'skipOnEmpty': 1,
            })
        },
    }], [])
})
$('#actions').find('.saveForm').click(function( e ){
    // Do not perform default action when button is clicked
    e.preventDefault();
    $('#actions button').attr("disabled", true); // Disable submit buttons

    // Prepare FormBuilder to POST as JSON
    var data = {};
    $.each($('#w0').serializeArray(), function() {
        data[this.name]= this.value;
    });
    // Send Form Data
    $.ajax({
        method: "POST",
        url: options.endPoint, // From external file configuration
        dataType: 'json',
        data: data
    }).done(function(data) {
        data = data.data;
        $('#actions button').removeAttr("disabled"); // Enable submit buttons

        if (data.success && data.id > 0) {
            // Redirect to another page
            if (options.afterSave === 'redirect' ) {
                window.parent.location.href = options.url;
            }
        } else {

            // Show error message
            $(document).trigger("add-alerts", [
                {
                    'message': "<strong>alert.warning</strong> " + data.message,
                    'priority': 'warning'
                }
            ]);

        }
    }).fail(function(msg){

        // Show error message
        $(document).trigger("add-alerts", [
            {
                'message': "<strong>" + polyglot.t('alert.warning') + "</strong> " + polyglot.t('alert.errorSavingData'),
                'priority': 'warning'
            }
        ]);

    }).always(function(){
    });

});

