<?php

namespace Modules\Common\Provider\Schedule;

use Closure;

/**
 * Class ScheduleProvider
 * @package Modules\Common\Provider\Schedule
 */
class ScheduleBiz
{
    /**
     * @var AbstractScheduleBiz[]
     */
    private static array $instances = [
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractScheduleBiz[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }
}
