<template>
  <div class="menu-box" v-loading="loading">
    <!--  :style="{ width: isCollapse ? '76px' : '100%' }"
    <div class="collapse-btn" :class="{ 'collapse-btn--collapsed': isCollapse }" @click="isCollapse = !isCollapse">
      <el-icon :class="{ 'rotate-180': isCollapse }"><Back /></el-icon>
    </div> -->

    <!-- 菜单区域 -->
    <div class="menu-container" :class="{ collapsed: isCollapse }">
      <el-menu :default-active="activeMenu.key" :default-openeds="[activeMenu.activeParentKey]" :collapse="isCollapse" :collapse-transition="true" class="menu-list" unique-opened>
        <template v-for="navItem in navList" :key="navItem.key">
          <el-menu-item-group>
            <template #title>
              <span class="group-title">{{ navItem.nav_name }}</span>
            </template>
            <template v-if="navItem.children && navItem.children.length">
              <MenuItem :isCollapse="isCollapse" :navItem="navItem.children" :isSubMenu="false" />
            </template>
            <template v-else>
              <el-menu-item :index="navItem.key" @click="goPage(navItem)">
                <el-icon><component :is="navItem.icon" /></el-icon>
                <span>{{ navItem.nav_name }}</span>
              </el-menu-item>
            </template>
          </el-menu-item-group>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, watchEffect, onUnmounted } from 'vue'
import Cache from '/admin/support/cache'
import http from '/admin/support/http'
import { useAppStore } from '/admin/stores/modules/app'
import { useRouter, useRoute } from 'vue-router'

import MenuItem from './menuItem.vue'

// import LogoImg from '@/public/asset/image/logo.png'
// import LogoImg_B from '@/public/asset/image/logo_b.png'

import useEventBus from '/admin/support/eventBus'
const { $on, $off } = useEventBus()

const api = 'nav'
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

interface NavItem {
  children?: NavItem[]
  icon: string
  key: string
  nav_name: string
  parent?: string
  path: string
  order: number
  activeParentKey?: string
}

const navList = ref<NavItem[]>([])
const activeMenu = ref({} as NavItem)
const isCollapse = ref(false)
const COLLAPSE_WIDTH = 768 // 设置折叠阈值，小于这个宽度时菜单将自动折叠
const loading = ref(true)

// 静态菜单数据 - 根据图片内容，调整为MenuItem组件兼容格式
const staticMenuData: NavItem[] = [
  {
    key: 'home-group',
    nav_name: '首页',
    icon: 'HomeFilled',
    path: '/dashboard',
    order: 1,
    children: [
      {
        key: 'home',
        nav_name: '控制台',
        icon: 'HomeFilled',
        path: '/dashboard',
        order: 1
      }
    ]
  },
  {
    key: 'content-group',
    nav_name: '内容管理',
    icon: 'Document',
    path: '/admin/content',
    order: 2,
    children: [
      {
        key: 'article-editor',
        nav_name: '文章编辑器',
        icon: 'EditPen',
        path: '/content/editor',
        order: 1
      },
      {
        key: 'article-list',
        nav_name: '文章列表',
        icon: 'List',
        path: '/content/list',
        order: 2
      },
      {
        key: 'tags-group',
        nav_name: '标签管理',
        icon: 'PriceTag',
        path: '/tags/list',
        order: 3,
      },
      {
        key: 'live-channel-group',
        nav_name: '直播管理',
        icon: 'VideoPlay',
        path: '/live-channel/list',
        order: 5,
      },
      {
        key: 'region-group',
        nav_name: '區域內容管理',
        icon: 'Location',
        path: '/region/list',
        order: 6,
        children: [
          {
            key: 'region-list',
            nav_name: '區域管理',
            icon: 'MapLocation',
            path: '/region/list',
            order: 1
          },
          {
            key: 'channel-list',
            nav_name: '頻道管理',
            icon: 'Headset',
            path: '/region/channel-list',
            order: 2
          }
        ]
      },
      {
        key: 'faq-group',
        nav_name: 'FAQ管理',
        icon: 'QuestionFilled',
        path: '/faq',
        order: 4,
        children: [
          {
            key: 'faq',
            nav_name: 'FAQ列表',
            icon: 'List',
            path: '/faq/list',
            order: 1
          },
          {
            key: 'faq-category',
            nav_name: 'FAQ分類管理',
            icon: 'Folder',
            path: '/faq/category',
            order: 2
          }
        ]
      }
    ]
  }

]

function getNavList() {
  loading.value = true
  
  // 使用静态数据替代 HTTP 请求
  setTimeout(() => {
    navList.value = staticMenuData
    nextTick(() => {
      const fullPath = route.query.model_id ? `${appStore.getActiveMenu}?model_id=${route.query.model_id}` : appStore.getActiveMenu
      const found = findNavItemByPath(navList.value, fullPath)
    
      if (found) {
        activeMenu.value = found
        appStore.setPageName(found.nav_name)
      } else {
        activeMenu.value = navList.value[0]
        appStore.setPageName(navList.value[0].nav_name)
      }
      loading.value = false
      
      // 通过事件总线向面包屑组件发送导航数据
      useEventBus().$emit('navList:ready', navList.value)
    })
  }, 100) // 模拟一个短暂的加载时间
}

function findNavItemByPath(navItems: NavItem[], path: string): NavItem | null {
  const matchItem = (item: NavItem, targetPath: string) => {
    const [itemBasePath, itemQuery] = item.path.split('?')
    const [targetBasePath, targetQuery] = targetPath.split('?')

    // 如果基础路径不同，直接返回 false
    if (itemBasePath !== targetBasePath) {
      return false
    }

    // 如果有查询参数，需要进一步匹配 model_id
    if (itemQuery || targetQuery) {
      const itemModelId = itemQuery ? new URLSearchParams(itemQuery).get('model_id') : null
      const targetModelId = targetQuery ? new URLSearchParams(targetQuery).get('model_id') : null
      return itemModelId === targetModelId
    }

    return true
  }

  let activeParentKey = ''

  // 递归查找匹配项
  const findItem = (items: NavItem[]): NavItem | null => {
    for (const item of items) {
      if (item.children?.length) {
        const found = findItem(item.children)
        if (found) {
          activeParentKey = item.key
          return found
        }
      }

      if (matchItem(item, path)) {
        return item
      }
    }
    return null
  }

  const found = findItem(navItems)
  if (found) {
    // 使用类型断言来避免 TypeScript 错误
    ;(found as NavItem & { activeParentKey: string }).activeParentKey = activeParentKey
  }
  return found
}

function goPage(navItem: NavItem) {
  appStore.setPageName(navItem.nav_name)
  router.push(navItem.path)
}

// 添加窗口宽度监听函数
const handleResize = () => {
  isCollapse.value = window.innerWidth <= COLLAPSE_WIDTH
}

// 在组件挂载时添加监听器
onMounted(() => {
  getNavList()
  $on('update:upMenu', getNavList)

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
  // 初始化时执行一次检查
  handleResize()
})

// 在组件卸载时移除监听器
onUnmounted(() => {
  $off('update:upMenu', getNavList)
  window.removeEventListener('resize', handleResize)
})

watchEffect(() => {
  const path = appStore.getActiveMenu
  if (navList.value.length > 0) {
    const fullPath = route.query.model_id ? `${path}?model_id=${route.query.model_id}` : path
    const found = findNavItemByPath(navList.value, fullPath)
    if (found) {
      activeMenu.value = found
      appStore.setPageName(found.nav_name)
    }
  }
})
</script>

<style scoped lang="scss">
.menu-box {
  padding: 40px 20px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .menu-container {
    padding-bottom: 40px;

    :deep(.el-menu) {
      border-right: none;

      .el-menu-item-group {
        margin-bottom: 20px;

        .el-menu-item-group__title {
          padding-left: 0;
          color: #7f7f7f;
          font-size: 16px;
          line-height: 1.3125;
        }

        ul {
          .el-menu-item {
            border-radius: 50px;
            padding: 0 12px;
            line-height: 42px;
            height: 42px;
            font-size: 16px;
            font-weight: 500;

            .el-icon {
              text-align: left;
              justify-content: flex-start;
              font-size: 16px;
              width: 16px;
              color: #232323;

              &.el-sub-menu__icon-arrow {
                font-size: 12px;
                width: 12px;
              }
            }
          }

          .el-sub-menu {
            .el-sub-menu__title {
              border-radius: 50px;
              padding-left: 12px;
              line-height: 42px;
              height: 42px;
              font-size: 16px;
              font-weight: 500;

              .el-icon {
                text-align: left;
                justify-content: flex-start;
                font-size: 16px;
                width: 16px;
                color: #232323;

                &.el-sub-menu__icon-arrow {
                  font-size: 12px;
                  width: 12px;
                }
              }
            }

            .el-menu {
              padding: 0 12px;

              .el-menu-item {
                font-size: 14px;
                padding: 0 12px;
                line-height: 40px;
                height: 40px;
                font-size: 14px;
                font-weight: 500;

                .el-icon {
                  font-size: 14px;
                  width: 14px;
                  color: #232323;

                  &.el-sub-menu__icon-arrow {
                    font-size: 12px;
                    width: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
