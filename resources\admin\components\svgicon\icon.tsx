import { App } from 'vue'

let React = window.amisRequire('react')
let { useRef, useEffect } = React

function svgIcon(props: any) {
  const containerRef = useRef(null)
  const icons = props.data.icon

  useEffect(() => {
    let vueApp: App<Element>
    import('vue').then(({ createApp }) => {
      import('./CustomIcon.vue').then((CustomIcon) => {
        vueApp = createApp(CustomIcon.default, { icon: icons, className: 'text-2xl' })
        vueApp.mount(containerRef.current)
      })
    })

    // 当组件卸载时，卸载 Vue 应用
    return () => {
      if (vueApp) {
        vueApp.unmount()
      }
    }
  }, [icons])  // 添加 icons 到依赖数组中

  return React.createElement('div', { ref: containerRef })
}

export default svgIcon
