.bwms-page .page-content {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .page-content .row {
  padding-bottom: 10px;
  height: 100%;
  width: 100%;
}
.bwms-page .page-content .side {
  margin-right: 10px;
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
  width: 25%;
  height: 100%;
  overflow-y: auto;
}
.bwms-page .page-content .side::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #B3B3B3;
}
.bwms-page .page-content .side::-webkit-scrollbar-track {
  background-color: transparent;
  border: none;
}
.bwms-page .page-content .side::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
}
.bwms-page .page-content .side ul li {
  font-size: 14px;
  color: #34495e;
  line-height: 2.85;
  transition: 0.35s ease-in-out;
}
.bwms-page .page-content .side ul li .li {
  padding: 10px 30px;
  font-size: 14px;
  color: #34495e;
  line-height: 2.85;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  transition: 0.35s ease-in-out;
  position: relative;
}
.bwms-page .page-content .side ul li .li span {
  font-size: 14px;
  color: #34495e;
  line-height: 2.85;
  transition: 0.35s ease-in-out;
}
.bwms-page .page-content .side ul li .li .iconfont {
  font-size: 12px;
  transition: 0.35s ease-in-out;
}
.bwms-page .page-content .side ul li .li::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  z-index: 1;
  background-color: #4160F0;
  width: 4px;
  opacity: 0;
  transition: 0.35s ease-in-out;
}
.bwms-page .page-content .side ul li .li:hover {
  background-color: #eceffa;
  color: #4160F0;
}
.bwms-page .page-content .side ul li .li:hover span {
  color: #4160F0;
}
.bwms-page .page-content .side ul li .li:hover::after {
  opacity: 1;
}
.bwms-page .page-content .side ul li .li.open .iconfont {
  transform: rotate(90deg);
}
.bwms-page .page-content .side ul li.active {
  color: #4160F0;
}
.bwms-page .page-content .side ul li.active > .li {
  background-color: #eceffa;
  color: #4160F0;
}
.bwms-page .page-content .side ul li.active > .li span {
  color: #4160F0;
}
.bwms-page .page-content .side ul li.active > .li::after {
  opacity: 1;
}
.bwms-page .page-content .side ul li .sub-li {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease-in-out;
}
.bwms-page .page-content .side ul li .sub-li ul li {
  line-height: 1.4;
}
.bwms-page .page-content .side ul li .sub-li ul li .li {
  padding-left: 38px;
  line-height: 1.4;
}
.bwms-page .page-content .main-content {
  width: calc(75% - 10px);
  height: 100%;
}
.bwms-page .page-content .main-content .white-box {
  margin-bottom: 10px;
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
  overflow-y: auto;
}
.bwms-page .page-content .main-content .breadcrumbs {
  padding: 20px 10px;
}
.bwms-page .page-content .main-content .breadcrumbs ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .page-content .main-content .breadcrumbs ul li {
  font-size: 14px;
  color: #34495e;
}
.bwms-page .page-content .main-content .breadcrumbs ul li.iconfont {
  margin: 0 10px;
  font-size: 10px;
}
.bwms-page .page-content .main-content .breadcrumbs ul li:last-child {
  color: #4160F0;
}
.bwms-page .page-content .main-content .document-list .document-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 20px;
}
.bwms-page .page-content .main-content .document-list .document-item .round {
  margin-right: 10px;
  width: 4px;
  height: 4px;
  background-color: #4160F0;
}
.bwms-page .page-content .main-content .document-list .document-item .iconfont {
  margin-right: 10px;
  font-size: 16px;
  color: #34495e;
}
.bwms-page .page-content .main-content .document-list .document-item .document-tit {
  font-size: 16px;
  color: #34495e;
  line-height: 2;
}
.bwms-page .page-content .main-content .document-list .document-item .document-tit a {
  font-size: 16px;
  color: #34495e;
  line-height: 2;
}
.bwms-page .page-content .main-content .tac {
  text-align: center !important;
}
.bwms-page .page-content .main-content .mb10 {
  margin-bottom: 10px !important;
  line-height: 2.14;
}
.bwms-page .page-content .main-content .tit {
  margin-bottom: 10px;
  color: #34495e;
  font-size: 20px;
  font-weight: bold;
}
.bwms-page .page-content .main-content h1.tit {
  border-bottom: 1px solid #eee;
  padding: 30px 0 20px;
  font-size: 30px;
}
.bwms-page .page-content .main-content h3 {
  margin-bottom: 10px;
  margin-top: 20px;
}
.bwms-page .page-content .main-content p {
  color: #34495e;
  font-size: 14px;
  line-height: 1.4;
}
.bwms-page .page-content .main-content .more-a {
  margin-bottom: 10px;
  padding: 0 10px;
  line-height: 1;
}
.bwms-page .page-content .main-content .more-a a {
  color: #34495e;
}
.bwms-page .page-content .main-content .more-a:first-child {
  margin-top: 10px;
}
.bwms-page .page-content .main-content .qr-code {
  margin: 0 auto;
  width: 120px;
}
.bwms-page .page-content .main-content .sort-ul {
  margin: 20px 0;
}
.bwms-page .page-content .main-content .sort-ul li {
  margin-top: 10px;
  color: #34495e;
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .page-content .main-content .sort-ul li::before {
  margin-right: 8px;
  content: "";
  display: block;
  width: 2px;
  height: 2px;
  background-color: #34495e;
}
