<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.countdownEditor.tab.content')" name="content">
        <el-form label-position="top" size="small">
          <el-form-item :label="$t('Editor.countdownEditor.form.title')">
            <el-input v-model="title" @input="markAsChanged" />
          </el-form-item>
          
          <el-form-item :label="$t('Editor.countdownEditor.form.endDate')">
            <div class="date-time-selector">
              <el-date-picker
                v-model="endDate"
                type="date"
                :placeholder="$t('Editor.countdownEditor.form.endDatePlaceholder')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                @change="markAsChanged"
                class="date-picker"
                size="small"
              />
              <el-time-picker
                v-model="endTime"
                :placeholder="$t('Editor.countdownEditor.form.endTimePlaceholder')"
                format="HH:mm"
                value-format="HH:mm"
                @change="markAsChanged"
                class="time-picker"
                size="small"
              />
            </div>
            <div class="date-description">
              {{$t('Editor.countdownEditor.form.endDateDesc')}}
            </div>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.countdownEditor.form.message')">
            <el-input
              v-model="message"
              type="textarea"
              :rows="2"
              :placeholder="$t('Editor.countdownEditor.form.messagePlaceholder')"
              @input="markAsChanged"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane :label="$t('Editor.countdownEditor.tab.style')" name="style">
        <el-form label-position="top" size="small">
          <el-form-item :label="$t('Editor.countdownEditor.style.counter')">
            <div class="sub-field-group">
              <div class="sub-field-label">{{$t('Editor.countdownEditor.style.bg')}}</div>
              <el-radio-group v-model="hasFill" @change="markAsChanged" size="small">
                <el-radio :label="true">{{$t('Editor.countdownEditor.style.fill')}}</el-radio>
                <el-radio :label="false">{{$t('Editor.countdownEditor.style.noFill')}}</el-radio>
              </el-radio-group>
            </div>
            
            <div class="sub-field-group" v-if="hasFill">
              <div class="sub-field-label">{{$t('Editor.countdownEditor.style.border')}}</div>
              <div class="border-radii-wrapper">
                <div class="border-size">
                  <span class="border-label">{{$t('Editor.countdownEditor.style.borderWidth')}}</span>
                  <el-select v-model="borderSize" @change="markAsChanged" class="border-select" size="small">
                    <el-option :label="$t('Editor.countdownEditor.style.noBorder')" value="0px" />
                    <el-option label="1px" value="1px" />
                    <el-option label="2px" value="2px" />
                    <el-option label="3px" value="3px" />
                    <el-option label="4px" value="4px" />
                    <el-option label="5px" value="5px" />
                  </el-select>
                </div>
                <div class="border-radius">
                  <span class="border-label">{{$t('Editor.countdownEditor.style.borderRadius')}}</span>
                  <el-slider
                    v-model="borderRadius"
                    :min="0"
                    :max="50"
                    @change="markAsChanged"
                    size="small"
                  />
                </div>
              </div>
            </div>
            
            <div class="sub-field-group" v-if="hasFill">
              <div class="sub-field-label">{{$t('Editor.countdownEditor.style.fillColor')}}</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="fillColor"
                  :placeholder="$t('Editor.countdownEditor.style.fillColorPlaceholder')"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + fillColor }"
                ></div>
                <el-color-picker 
                  v-model="fillColorPicker" 
                  show-alpha 
                  @change="updateFillColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
            
            <div class="sub-field-group">
              <div class="sub-field-label">{{$t('Editor.countdownEditor.style.textColor')}}</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="textColor"
                  :placeholder="$t('Editor.countdownEditor.style.textColorPlaceholder')"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + textColor }"
                ></div>
                <el-color-picker 
                  v-model="textColorPicker" 
                  show-alpha 
                  @change="updateTextColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.countdownEditor.style.label')">
            <div class="sub-field-group">
              <div class="sub-field-label">{{$t('Editor.countdownEditor.style.labelColor')}}</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="labelColor"
                  :placeholder="$t('Editor.countdownEditor.style.labelColorPlaceholder')"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + labelColor }"
                ></div>
                <el-color-picker 
                  v-model="labelColorPicker" 
                  show-alpha 
                  @change="updateLabelColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item :label="$t('Editor.countdownEditor.style.alignment')">
            <el-radio-group v-model="alignment" @change="markAsChanged" size="small">
              <el-radio-button label="left">
                {{$t('Editor.countdownEditor.style.alignLeft')}}
              </el-radio-button>
              <el-radio-button label="center">
                {{$t('Editor.countdownEditor.style.alignCenter')}}
              </el-radio-button>
              <el-radio-button label="right">
                {{$t('Editor.countdownEditor.style.alignRight')}}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 應用按鈕，只在有更改時顯示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.countdownEditor.button.apply')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 定義組件名稱
defineOptions({
  name: 'CountdownEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

const { t } = useI18n()

// 當前激活的標籤
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 內容屬性
const title = ref('倒數計時')
const endDate = ref('')
const endTime = ref('12:00')
const message = ref('活動即將開始，敬請期待！')

// 樣式屬性
const hasFill = ref(true)
const borderSize = ref('0px')
const borderRadius = ref(8)
const fillColor = ref('F4F2FF')
const textColor = ref('4F38E0')
const labelColor = ref('09152B')
const alignment = ref('center')

// 顏色選擇器的值
const fillColorPicker = ref('#' + fillColor.value)
const textColorPicker = ref('#' + textColor.value)
const labelColorPicker = ref('#' + labelColor.value)

// 更新填充顏色
const updateFillColor = (value: string) => {
  if (value) {
    // 從 '#RRGGBB' 或 '#RRGGBBAA' 格式轉換為 'RRGGBB'
    fillColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新文字顏色
const updateTextColor = (value: string) => {
  if (value) {
    textColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新標籤顏色
const updateLabelColor = (value: string) => {
  if (value) {
    labelColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 監聽文本輸入，同步更新顏色選擇器
watch(fillColor, (newValue) => {
  fillColorPicker.value = '#' + newValue
})

watch(textColor, (newValue) => {
  textColorPicker.value = '#' + newValue
})

watch(labelColor, (newValue) => {
  labelColorPicker.value = '#' + newValue
})

/**
 * 從塊元素中提取倒數計時數據
 */
const extractCountdownData = () => {
  if (!props.blockElement) return false
  
  try {
    // 提取標題
    const titleEl = props.blockElement.querySelector('.countdown-title')
    if (titleEl) {
      title.value = titleEl.textContent?.trim() || '倒數計時'
    }

    // 提取訊息
    const messageEl = props.blockElement.querySelector('.countdown-message')
    if (messageEl) {
      message.value = messageEl.textContent?.trim() || '活動即將開始，敬請期待！'
    }

    // 提取日期時間
    const dateAttr = props.blockElement.getAttribute('data-target-date')
    if (dateAttr) {
      try {
        const date = new Date(dateAttr)
        // 設置日期
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        endDate.value = `${year}/${month}/${day}`

        // 設置時間
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        endTime.value = `${hours}:${minutes}`
      } catch (e) {
        console.error('解析日期時出錯:', e)
        setDefaultDate()
      }
    } else {
      setDefaultDate()
    }

    // 提取樣式屬性
    const counterEl = props.blockElement.querySelector('.countdown-value')
    if (counterEl) {
      // 檢查是否有背景色
      const bgColorClass = Array.from(counterEl.classList).find(cls => cls.startsWith('bg-'))
      hasFill.value = !!bgColorClass

      // 獲取邊框樣式
      const borderProperty = window.getComputedStyle(counterEl).borderWidth
      borderSize.value = borderProperty || '0px'

      // 獲取圓角樣式
      const borderRadiusProperty = window.getComputedStyle(counterEl).borderRadius
      const radiusValue = parseInt(borderRadiusProperty)
      if (!isNaN(radiusValue)) {
        borderRadius.value = radiusValue
      }

      // 獲取填充顏色
      const bgColor = window.getComputedStyle(counterEl).backgroundColor
      if (bgColor && bgColor !== 'transparent') {
        const rgbMatch = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          fillColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }

      // 獲取文字顏色
      const textColorValue = window.getComputedStyle(counterEl).color
      if (textColorValue) {
        const rgbMatch = textColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          textColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }
    }

    // 獲取標籤顏色
    const labelEl = props.blockElement.querySelector('.countdown-label')
    if (labelEl) {
      const labelColorValue = window.getComputedStyle(labelEl).color
      if (labelColorValue) {
        const rgbMatch = labelColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          labelColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }
    }

    // 獲取對齊方式
    const containerEl = props.blockElement.querySelector('.countdown-container')
    if (containerEl) {
      const textAlignValue = window.getComputedStyle(containerEl).textAlign
      if (textAlignValue) {
        if (textAlignValue === 'center' || textAlignValue === 'right') {
          alignment.value = textAlignValue
        } else {
          alignment.value = 'left'
        }
      }
    }

    return true
  } catch (error) {
    console.error('提取倒數計時數據時出錯:', error)
    return false
  }
}

/**
 * 設置默認日期（當前日期加7天）
 */
const setDefaultDate = () => {
    const defaultDate = new Date()
    defaultDate.setDate(defaultDate.getDate() + 7)
    
    const year = defaultDate.getFullYear()
    const month = String(defaultDate.getMonth() + 1).padStart(2, '0')
    const day = String(defaultDate.getDate()).padStart(2, '0')
    endDate.value = `${year}/${month}/${day}`
  }
  
// 監聽 blockElement 的變化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改狀態
    isChanged.value = false
    
    // 重新提取數據
    const extracted = extractCountdownData()
    
    if (!extracted) {
      // 如果無法提取數據，設置默認值
      title.value = '倒數計時'
      message.value = '活動即將開始，敬請期待！'
      setDefaultDate()
      endTime.value = '12:00'
      hasFill.value = true
      borderSize.value = '0px'
      borderRadius.value = 8
      fillColor.value = 'F4F2FF'
      textColor.value = '4F38E0'
      labelColor.value = '09152B'
      alignment.value = 'center'
    }
  }
}, { immediate: true, deep: true })

// 組件掛載時初始化
onMounted(() => {
  // 移除原有的提取邏輯，因為已經由 watch 處理
  isChanged.value = false
})

// 標記為已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 準備目標日期
const prepareTargetDate = (): string => {
  if (!endDate.value) {
    ElMessage.warning(t('Editor.countdownEditor.message.selectEndDate'))
    return ''
  }

  try {
    // 使用選擇的日期和時間創建目標日期
    const [year, month, day] = endDate.value.split('/').map(Number)
    const [hours, minutes] = endTime.value ? endTime.value.split(':').map(Number) : [0, 0]
    
    // 創建日期對象 (月份要減1，因為JavaScript中月份從0開始)
    const targetDate = new Date(year, month - 1, day, hours, minutes)
    
    // 如果日期無效，拋出錯誤
    if (isNaN(targetDate.getTime())) {
      throw new Error('無效的日期')
    }
    
    return targetDate.toISOString()
  } catch (e) {
    console.error('創建目標日期時出錯:', e)
    ElMessage.error(t('Editor.countdownEditor.message.invalidDate'))
    return ''
  }
}

// 準備倒數計時HTML
const prepareCountdownHTML = (): string => {
  const targetDate = prepareTargetDate()
  if (!targetDate) return ''
  
  // 構建基本樣式
  const counterStyle: string[] = []
  const labelStyle: string[] = []
  
  // 填充樣式
  if (hasFill.value) {
    counterStyle.push(`background-color: #${fillColor.value}`)
  } else {
    counterStyle.push('background-color: transparent')
  }
  
  // 邊框樣式
  if (borderSize.value !== '0px') {
    counterStyle.push(`border: ${borderSize.value} solid #${fillColor.value}`)
  }
  
  // 圓角樣式
  counterStyle.push(`border-radius: ${borderRadius.value}px`)
  
  // 文字顏色
  counterStyle.push(`color: #${textColor.value}`)
  
  // 標籤文字顏色
  labelStyle.push(`color: #${labelColor.value}`)
  
  // 創建倒數計時腳本
  const countdownScript = `
    (function() {
      // 找到當前倒數計時組件
      const countdown = document.currentScript.parentElement;
      if (!countdown) return;
      
      // 提取目標日期
      const targetDateStr = countdown.getAttribute('data-target-date');
      const targetDate = targetDateStr ? new Date(targetDateStr) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      
      // 獲取顯示元素
      const daysEl = countdown.querySelector('.days-item .countdown-value');
      const hoursEl = countdown.querySelector('.hours-item .countdown-value');
      const minutesEl = countdown.querySelector('.minutes-item .countdown-value');
      const secondsEl = countdown.querySelector('.seconds-item .countdown-value');
      
      if (!daysEl || !hoursEl || !minutesEl || !secondsEl) return;
      
      // 更新倒數計時函數
      function updateCountdown() {
        const now = new Date();
        const diff = targetDate.getTime() - now.getTime();
        
        if (diff <= 0) {
          // 倒數計時結束
          daysEl.textContent = '00';
          hoursEl.textContent = '00';
          minutesEl.textContent = '00';
          secondsEl.textContent = '00';
          return;
        }
        
        // 計算時間差
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        // 格式化顯示
        daysEl.textContent = String(days).padStart(2, '0');
        hoursEl.textContent = String(hours).padStart(2, '0');
        minutesEl.textContent = String(minutes).padStart(2, '0');
        secondsEl.textContent = String(seconds).padStart(2, '0');
      }
      
      // 初始更新
      updateCountdown();
      
      // 每秒更新
      const intervalId = setInterval(updateCountdown, 1000);
      
      // 清理函數
      function cleanup() {
        clearInterval(intervalId);
        document.removeEventListener('visibilitychange', visibilityHandler);
      }
      
      // 處理頁面可見性變化，優化性能
      function visibilityHandler() {
        if (document.hidden) {
          clearInterval(intervalId);
        } else {
          updateCountdown();
          setInterval(updateCountdown, 1000);
        }
      }
      
      document.addEventListener('visibilitychange', visibilityHandler);
      
      // 頁面卸載時清理
      window.addEventListener('beforeunload', cleanup);
    })();
  `
  
  // 構建HTML - 避免使用嵌套模板字符串
  return `
<div data-bs-component="countdown" class="my-4 bootstrap-countdown" data-target-date="${targetDate}">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="countdown-container text-${alignment.value}">
          <h3 class="mb-4 countdown-title">${title.value}</h3>
          <div class="countdown-wrapper d-flex justify-content-${alignment.value}">
            <div class="mx-2 countdown-item days-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">天</div>
            </div>
            <div class="mx-2 countdown-item hours-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">時</div>
            </div>
            <div class="mx-2 countdown-item minutes-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">分</div>
            </div>
            <div class="mx-2 countdown-item seconds-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">秒</div>
            </div>
          </div>
          <div class="mt-4 countdown-message">${message.value}</div>
        </div>
      </div>
    </div>
  </div>
  <script>${countdownScript}<\/script>
</div>`.trim()
}

// 應用更改
const applyChanges = () => {
  try {
    const html = prepareCountdownHTML()
    if (!html) return
    
    // 發出更新事件
    emit('update-block', { html })
    
    // 重置更改狀態
    isChanged.value = false
    
    ElMessage.success(t('Editor.countdownEditor.message.updateSuccess'))
  } catch (error) {
    console.error('應用倒數計時更改時出錯:', error)
    ElMessage.error(t('Editor.countdownEditor.message.updateFail'))
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-tabs__nav) {
  padding: 0 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-radio-button__inner) {
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-time-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 5px;
  flex-wrap: wrap;
  
  .date-picker {
    flex: 1;
    min-width: 120px;
  }
  
  .time-picker {
    flex: 0 0 100px;
  }
}

.date-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.sub-field-group {
  margin-bottom: 12px;
  
  .sub-field-label {
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
  }
}

.border-radii-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  
  .border-size,
  .border-radius {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .border-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .color-input {
    flex: 1;
  }
  
  .color-preview {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    flex-shrink: 0;
    cursor: pointer;
  }
  
  .color-picker-button {
    height: 24px;
  }
}

.border-select {
  width: 100%;
}

:deep(.el-input-group__prepend) {
  padding: 0 8px;
}

:deep(.el-form-item__label) {
  padding-bottom: 4px;
  line-height: 1.4;
  font-size: 13px;
}

:deep(.el-slider) {
  margin-top: 6px;
  margin-bottom: 6px;
}
</style> 

