.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .download-details {
  margin-bottom: 52px;
}
.bwms-page .download-details .container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content {
  padding: 30px 50px;
  background-color: #fff;
  flex-grow: 1;
}
.bwms-page .download-details .container .download-content h3 {
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 28px;
  color: #333;
}
.bwms-page .download-details .container .download-content .download-info {
  margin-bottom: 20px;
  border-bottom: 1px solid #F7F7F7;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content .download-info span {
  margin-right: 10px;
  display: block;
  font-size: 14px;
  color: #888;
}
.bwms-page .download-details .container .download-content .rich-text {
  margin-bottom: 58px;
}
.bwms-page .download-details .container .download-content .rich-text p {
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 1.75;
}
.bwms-page .download-details .container .download-content .collection-btn {
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .download-details .container .download-content .collection-btn .btn-box {
  border-radius: 50px;
  padding: 10px 16px;
  color: #383838;
  background-color: #F7F7F7;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .download-details .container .download-content .collection-btn .btn-box .iconfont + span {
  margin-left: 5px;
}
.bwms-page .download-details .container .download-content .collection-btn .btn-box span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .download-details .container .download-content .collection-btn .btn-box span {
  display: block;
  line-height: 1.5;
}
.bwms-page .download-details .container .download-content .collection-btn .btn-box:hover {
  background-color: #aaa;
  color: #fff;
}
.bwms-page .download-details .container .download-content .tag-list {
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content .tag-list .tag-text {
  color: #333;
  font-size: 14px;
}
.bwms-page .download-details .container .download-content .tag-list .tag-item {
  margin-left: 10px;
  border: 1px solid #ff9600;
  border-radius: 50px;
  padding: 5px 14px;
  color: #ff9600;
  background-color: transparent;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .download-details .container .download-content .tag-list .tag-item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .download-details .container .download-content .tag-list .tag-item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .download-details .container .download-content .tag-list .tag-item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .download-details .container .download-content .tag-list .tag-item:hover {
  background-color: #ff9600;
  color: #fff;
}
.bwms-page .download-details .container .download-content .more {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content .more .more-news {
  flex-grow: 1;
}
.bwms-page .download-details .container .download-content .more .more-news .prev-news {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
  line-height: 1.71;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content .more .more-news .prev-news a {
  color: #333;
  line-height: 1.71;
}
.bwms-page .download-details .container .download-content .more .more-news .next-news {
  font-size: 14px;
  color: #333;
  line-height: 1.71;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .download-content .more .more-news .next-news span {
  color: #888;
  line-height: 1.71;
}
.bwms-page .download-details .container .download-content .more .back-list .btn-box {
  border-radius: 50px;
  padding: 12px 14px;
  color: #fff;
  background-color: #ff9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .download-details .container .download-content .more .back-list .btn-box .iconfont + span {
  margin-left: 5px;
}
.bwms-page .download-details .container .download-content .more .back-list .btn-box span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .download-details .container .download-content .more .back-list .btn-box span {
  display: block;
  line-height: 1.5;
}
.bwms-page .download-details .container .download-content .more .back-list .btn-box:hover {
  background-color: #FCB319;
  color: #fff;
}
.bwms-page .download-details .container .side {
  padding-left: 30px;
  width: calc(25% + 30px);
  flex-shrink: 0;
}
.bwms-page .download-details .container .side .tit {
  margin-bottom: 10px;
  line-height: 1.11;
  color: #333;
  font-size: 18px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .download-details .container .side .tit::before {
  margin-right: 15px;
  content: '';
  display: block;
  width: 5px;
  background-color: #ff9600;
}
.bwms-page .download-details .container .side .download-address {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
}
.bwms-page .download-details .container .side .download-address .address-list .download-item {
  justify-content: flex-start;
  border-radius: 8px;
  padding: 15px 25px;
  color: #fff;
  background-color: #ff9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  cursor: pointer;
}
.bwms-page .download-details .container .side .download-address .address-list .download-item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .download-details .container .side .download-address .address-list .download-item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .download-details .container .side .download-address .address-list .download-item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .download-details .container .side .download-address .address-list .download-item:hover {
  background-color: #FCB319;
  color: #fff;
}
.bwms-page .download-details .container .side .download-address .download-num {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .download-details .container .side .download-address .download-num .item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  font-size: 14px;
  color: #888;
}
.bwms-page .download-details .container .side .download-address .download-num .item .iconfont {
  margin-right: 5px;
  display: block;
}
.bwms-page .download-details .container .side .tag {
  padding: 20px;
  background-color: #fff;
}
.bwms-page .download-details .container .side .tag .tag-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.bwms-page .download-details .container .side .tag .tag-list .tag-item {
  margin-bottom: 10px;
  margin-right: 10px;
  border: 1px solid #ff9600;
  border-radius: 50px;
  padding: 5px 12px;
  color: #ff9600;
  background-color: transparent;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.bwms-page .download-details .container .side .tag .tag-list .tag-item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .download-details .container .side .tag .tag-list .tag-item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .download-details .container .side .tag .tag-list .tag-item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .download-details .container .side .tag .tag-list .tag-item:hover {
  background-color: #ff9600;
  color: #fff;
}
