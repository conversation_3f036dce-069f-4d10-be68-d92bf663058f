# API 控制器模板

## 概述

API 控制器用于处理 API 请求，返回 JSON 格式的响应。本文档提供了 API 控制器的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Api\Controllers;

use Bingo\Module\ApiBaseController;
use Modules\YourModule\Api\Requests\ListRequest;
use Modules\YourModule\Api\Requests\CreateRequest;
use Modules\YourModule\Api\Requests\UpdateRequest;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

final class YourController extends ApiBaseController 
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    public function index(ListRequest $request): array
    {
        return $this->service->getList($request->validated());
    }

    public function show(int $id): array
    {
        return $this->service->getDetail($id);
    }

    public function store(CreateRequest $request): array
    {
        return $this->service->create($request->validated());
    }

    public function update(int $id, UpdateRequest $request): array
    {
        return $this->service->update($id, $request->validated());
    }

    public function destroy(int $id): array
    {
        return $this->service->delete($id);
    }
}
```

## 请求验证类示例

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'status' => ['required', 'string', 'in:draft,published'],
            'category_id' => ['required', 'integer', 'exists:categories,id'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['integer', 'exists:tags,id'],
            'publish_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => T('YourModule::validation.title.required'),
            'title.string' => T('YourModule::validation.title.string'),
            'title.max' => T('YourModule::validation.title.max'),
            'content.required' => T('YourModule::validation.content.required'),
            'content.string' => T('YourModule::validation.content.string'),
            'status.required' => T('YourModule::validation.status.required'),
            'status.string' => T('YourModule::validation.status.string'),
            'status.in' => T('YourModule::validation.status.in'),
            'category_id.required' => T('YourModule::validation.category_id.required'),
            'category_id.integer' => T('YourModule::validation.category_id.integer'),
            'category_id.exists' => T('YourModule::validation.category_id.exists'),
            'tags.array' => T('YourModule::validation.tags.array'),
            'tags.*.integer' => T('YourModule::validation.tags.*.integer'),
            'tags.*.exists' => T('YourModule::validation.tags.*.exists'),
            'publish_at.date' => T('YourModule::validation.publish_at.date'),
            'publish_at.after' => T('YourModule::validation.publish_at.after'),
        ];
    }
}
```

## 规范要求

1. 类声明
   - 必须使用 `declare(strict_types=1)`
   - 必须是 `final` 类
   - 必须继承 `ApiBaseController`
   - 使用构造函数注入依赖

2. 方法规范
   - 方法名遵循 RESTful 规范
   - 返回类型必须是 `array`
   - 使用 FormRequest 进行参数验证
   - 适当的错误处理

3. 错误处理
```php
public function show(int $id): array
{
    try {
        return $this->service->getDetail($id);
    } catch (ModelNotFoundException $e) {
        throw BizException::throws(Code::NOT_FOUND);
    } catch (\Exception $e) {
        throw BizException::throws(Code::SYSTEM_ERROR);
    }
}
```

## 最佳实践

1. 保持控制器简洁
   - 只负责请求处理和响应返回
   - 业务逻辑委托给服务层
   - 使用 FormRequest 进行参数验证

2. 请求验证
   - 为每个请求创建专门的 FormRequest 类
   - 在 FormRequest 中定义验证规则
   - 使用语言包进行错误消息国际化
   - 验证失败自动返回错误响应

3. 响应格式
   - 统一使用数组格式
   - 遵循 API 响应规范
   - 使用正确的 HTTP 状态码

4. 依赖注入
   - 使用构造函数注入服务
   - 避免在方法中实例化对象
   - 遵循依赖倒置原则

## 常见问题

1. 复杂验证规则
```php
public function rules(): array
{
    return [
        'email' => [
            'required',
            'email',
            Rule::unique('users')->ignore($this->user()->id),
        ],
        'password' => [
            'required',
            'string',
            'min:8',
            'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
        ],
        'role_ids' => [
            'required',
            'array',
            Rule::exists('roles', 'id')->whereIn('id', $this->input('role_ids', [])),
        ],
    ];
}
```

2. 条件验证规则
```php
public function rules(): array
{
    $rules = [
        'title' => ['required', 'string', 'max:255'],
    ];

    if ($this->isMethod('POST')) {
        $rules['status'] = ['required', 'string', 'in:draft,published'];
    }

    if ($this->input('status') === 'published') {
        $rules['publish_at'] = ['required', 'date', 'after:now'];
    }

    return $rules;
}
```

3. 自定义验证规则
```php
public function rules(): array
{
    return [
        'custom_field' => [
            'required',
            function (string $attribute, mixed $value, Closure $fail) {
                if (!$this->service->validateCustomField($value)) {
                    $fail(T('validation.custom.invalid_value'));
                }
            },
        ],
    ];
}
```

## 注意事项

1. 控制器方法不应超过 50 行
2. 避免在控制器中写业务逻辑
3. 正确使用 HTTP 动词
4. 统一的错误处理方式
5. 适当的日志记录
