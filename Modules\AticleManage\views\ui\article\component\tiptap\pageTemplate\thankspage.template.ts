export const thanksPageTemplate = `
<div class="thanks-page responsive-block">
  <!-- 顶部区域 - 修改为图片布局 -->
  <div data-bs-component="layoutBlock" class="py-5 top-section">
    <div class="container">
      <!-- 电子书封面居中显示 -->
      <div class="mb-5 text-center">
        <div data-bs-component="bootstrap-image">
          <img src="https://new-bwms.bingo-test.com/tiptap/download.webp" alt="Marketing Playbook" class="shadow-lg img-fluid resource-cover" style="max-width: 280px; margin: 0 auto;">
        </div>
      </div>
      
      <!-- 标题和副标题 -->
      <div class="text-center">
        <div data-bs-component="richTextBlock">
          <h1 class="mb-4 display-4 fw-bold text-dark">Thank you for your request</h1>
          <p class="mx-auto text-muted col-md-8">
            We are pleased to share this resource with you. Explore insights that can support your marketing journey. If you like, check out a few other next steps. Enjoy!
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 下载按钮区域 -->
  <div data-bs-component="layoutBlock" class="py-4 download-section">
    <div class="container">
      <div class="text-center">
        <div data-bs-component="bootstrap-button">
          <a href="#" class="px-4 py-2 btn btn-primary btn-lg d-inline-flex align-items-center rounded-pill">
            Download
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download ms-2" viewBox="0 0 16 16">
              <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
              <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 灰色分隔区 -->
  <div data-bs-component="richTextBlock" class="py-5 mt-5 bg-light">
    <div class="container">
      <div class="mb-4 text-center">
        <h2 class="display-6 fw-bold">Next steps</h2>
        <p class="text-muted">Check out a few of our other resources and channels</p>
      </div>
    </div>
  </div>

  <!-- 下一步选项 -->
  <div data-bs-component="featureCardsBlock" class="pb-5 next-steps-section bg-light">
    <div class="container">
      <div class="row g-4">
        <!-- 选项1 -->
        <div class="col-md-4">
          <div class="border-0 shadow-sm card h-100">
            <div class="p-4 text-center card-body">
              <div class="mb-3">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/step-one.webp" alt="Explore content" class="step-icon" style="height: 80px; width: auto;">
              </div>
              <div data-bs-component="richTextBlock">
                <h3 class="mb-3 card-title h5 fw-bold">Explore the content</h3>
                <p class="card-text text-muted">Dive into the digital resource and discover valuable insights</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 选项2 -->
        <div class="col-md-4">
          <div class="border-0 shadow-sm card h-100">
            <div class="p-4 text-center card-body">
              <div class="mb-3">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/step-two.webp" alt="Apply strategies" class="step-icon" style="height: 80px; width: auto;">
              </div>
              <div data-bs-component="richTextBlock">
                <h3 class="mb-3 card-title h5 fw-bold">Apply the strategies</h3>
                <p class="card-text text-muted">Put the learnings into action to see real results in your work or life</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 选项3 -->
        <div class="col-md-4">
          <div class="border-0 shadow-sm card h-100">
            <div class="p-4 text-center card-body">
              <div class="mb-3">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/step-three.webp" alt="Stay engaged" class="step-icon" style="height: 80px; width: auto;">
              </div>
              <div data-bs-component="richTextBlock">
                <h3 class="mb-3 card-title h5 fw-bold">Stay engaged</h3>
                <p class="card-text text-muted">Follow us on social media and join our community to stay updated</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <div data-bs-component="footerBlock" class="py-3 text-white footer-section bg-dark">
    <div class="container">
      <div class="text-center">
        <div data-bs-component="richTextBlock">
          <p class="mb-0 small">© 2025. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>

<style>
/* 基础样式 */
.thanks-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

/* 资源封面样式 */
.resource-cover {
  border-radius: 8px;
  transition: transform 0.3s ease;
  box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.2);
  margin-bottom: 1.5rem;
}

.resource-cover:hover {
  transform: translateY(-5px);
}

/* 下载按钮样式 */
.btn-primary {
  transition: all 0.3s ease;
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: #5a32a3;
  border-color: #5a32a3;
}

/* 步骤卡片样式 */
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.step-icon {
  transition: transform 0.3s ease;
}

.card:hover .step-icon {
  transform: scale(1.1);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
  .resource-section .col-md-4 {
    margin-bottom: 2rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
  
  .resource-cover {
    max-width: 200px !important;
  }
  
  h1.display-4 {
    font-size: 2.5rem;
  }
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // 获取页面元素
  const cards = document.querySelectorAll('.card');
  const resourceCover = document.querySelector('.resource-cover');
  
  // 为卡片添加悬停效果
  cards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
      this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.05)';
    });
  });
  
  // 为资源封面添加动画效果
  if (resourceCover) {
    resourceCover.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
    });
    
    resourceCover.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  }
  
  // 为下载按钮添加点击效果
  const downloadBtn = document.querySelector('.btn-primary');
  if (downloadBtn) {
    downloadBtn.addEventListener('click', function(e) {
      // 这里可以添加下载功能
      console.log('Download button clicked');
    });
  }
});
</script>
`
