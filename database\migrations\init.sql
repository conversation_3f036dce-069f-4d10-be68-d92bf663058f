-- ========================================
-- 初始化系统角色
-- ========================================
INSERT INTO `roles` (`role_name`, `role_code`, `description`, `is_system`, `sort_order`) VALUES
('超级管理员', 'super_admin', '系统最高权限管理员，拥有所有功能权限', TRUE, 1),
('内容总监', 'content_director', '负责内容审核、发布管理、编辑团队管理', TRUE, 2),
('高级编辑', 'senior_editor', '负责内容编辑、审核、发布，可管理其他编辑', TRUE, 3),
('编辑', 'editor', '负责内容创建、编辑、基础审核', TRUE, 4),
('记者', 'reporter', '负责新闻采写、内容创建', TRUE, 5),
('运营专员', 'operation_specialist', '负责内容运营、数据分析、用户管理', TRUE, 6),
('技术管理员', 'tech_admin', '负责系统配置、技术维护', TRUE, 7),
('审核员', 'reviewer', '负责内容审核、质量控制', TRUE, 8);

-- ========================================
-- 初始化系统权限
-- ========================================

-- 用户管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('用户查看', 'user:view', 'user', 'user', 'view', '查看用户列表和详情', TRUE, 1),
('用户创建', 'user:create', 'user', 'user', 'create', '创建新用户', TRUE, 2),
('用户编辑', 'user:edit', 'user', 'user', 'edit', '编辑用户信息', TRUE, 3),
('用户删除', 'user:delete', 'user', 'user', 'delete', '删除用户', TRUE, 4),
('用户状态管理', 'user:status', 'user', 'user', 'status', '启用/禁用用户', TRUE, 5),
('用户密码重置', 'user:password', 'user', 'user', 'password', '重置用户密码', TRUE, 6);

-- 角色管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `role', `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('角色查看', 'role:view', 'role', 'role', 'view', '查看角色列表和详情', TRUE, 7),
('角色创建', 'role:create', 'role', 'role', 'create', '创建新角色', TRUE, 8),
('角色编辑', 'role:edit', 'role', 'role', 'edit', '编辑角色信息', TRUE, 9),
('角色删除', 'role:delete', 'role', 'role', 'delete', '删除角色', TRUE, 10),
('角色权限分配', 'role:permission', 'role', 'role', 'permission', '分配角色权限', TRUE, 11);

-- 内容管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('文章查看', 'article:view', 'content', 'article', 'view', '查看文章列表和详情', TRUE, 12),
('文章创建', 'article:create', 'content', 'article', 'create', '创建新文章', TRUE, 13),
('文章编辑', 'article:edit', 'content', 'article', 'edit', '编辑文章内容', TRUE, 14),
('文章发布', 'article:publish', 'content', 'article', 'publish', '发布文章', TRUE, 15),
('文章审核', 'article:review', 'content', 'article', 'review', '审核文章', TRUE, 16),
('文章删除', 'article:delete', 'content', 'article', 'delete', '删除文章', TRUE, 17),
('文章置顶', 'article:pin', 'content', 'article', 'pin', '置顶文章', TRUE, 18),
('文章归档', 'article:archive', 'content', 'article', 'archive', '归档文章', TRUE, 19);

-- 视频管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('视频查看', 'video:view', 'content', 'video', 'view', '查看视频列表和详情', TRUE, 20),
('视频上传', 'video:upload', 'content', 'video', 'upload', '上传视频', TRUE, 21),
('视频编辑', 'video:edit', 'content', 'video', 'edit', '编辑视频信息', TRUE, 22),
('视频发布', 'video:publish', 'content', 'video', 'publish', '发布视频', TRUE, 23),
('视频删除', 'video:delete', 'content', 'video', 'delete', '删除视频', TRUE, 24);

-- 分类管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('分类查看', 'category:view', 'content', 'category', 'view', '查看分类列表', TRUE, 25),
('分类创建', 'category:create', 'content', 'category', 'create', '创建新分类', TRUE, 26),
('分类编辑', 'category:edit', 'content', 'category', 'edit', '编辑分类信息', TRUE, 27),
('分类删除', 'category:delete', 'content', 'category', 'delete', '删除分类', TRUE, 28);

-- 标签管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('标签查看', 'tag:view', 'content', 'tag', 'view', '查看标签列表', TRUE, 29),
('标签创建', 'tag:create', 'content', 'tag', 'create', '创建新标签', TRUE, 30),
('标签编辑', 'tag:edit', 'content', 'tag', 'edit', '编辑标签信息', TRUE, 31),
('标签删除', 'tag:delete', 'content', 'tag', 'delete', '删除标签', TRUE, 32);

-- 笔名管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('笔名查看', 'pen_name:view', 'content', 'pen_name', 'view', '查看笔名列表', TRUE, 33),
('笔名创建', 'pen_name:create', 'content', 'pen_name', 'create', '创建新笔名', TRUE, 34),
('笔名编辑', 'pen_name:edit', 'content', 'pen_name', 'edit', '编辑笔名信息', TRUE, 35),
('笔名删除', 'pen_name:delete', 'content', 'pen_name', 'delete', '删除笔名', TRUE, 36);

-- 菜单管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('菜单查看', 'menu:view', 'system', 'menu', 'view', '查看菜单列表', TRUE, 37),
('菜单创建', 'menu:create', 'system', 'menu', 'create', '创建新菜单', TRUE, 38),
('菜单编辑', 'menu:edit', 'system', 'menu', 'edit', '编辑菜单信息', TRUE, 39),
('菜单删除', 'menu:delete', 'system', 'menu', 'delete', '删除菜单', TRUE, 40),
('菜单排序', 'menu:sort', 'system', 'menu', 'sort', '调整菜单顺序', TRUE, 41);

-- 系统配置权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('系统配置查看', 'config:view', 'system', 'config', 'view', '查看系统配置', TRUE, 42),
('系统配置编辑', 'config:edit', 'system', 'config', 'edit', '编辑系统配置', TRUE, 43),
('日志查看', 'log:view', 'system', 'log', 'view', '查看操作日志', TRUE, 44);

-- 推送管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('推送查看', 'push:view', 'push', 'push', 'view', '查看推送列表', TRUE, 45),
('推送创建', 'push:create', 'push', 'push', 'create', '创建推送消息', TRUE, 46),
('推送发送', 'push:send', 'push', 'push', 'send', '发送推送消息', TRUE, 47),
('推送统计', 'push:stats', 'push', 'push', 'stats', '查看推送统计', TRUE, 48);

-- 投票管理权限
INSERT INTO `permissions` (`permission_name`, `permission_code`, `module`, `resource`, `action`, `description`, `is_system`, `sort_order`) VALUES
('投票查看', 'voting:view', 'voting', 'voting', 'view', '查看投票列表', TRUE, 49),
('投票创建', 'voting:create', 'voting', 'voting', 'create', '创建投票活动', TRUE, 50),
('投票编辑', 'voting:edit', 'voting', 'voting', 'edit', '编辑投票信息', TRUE, 51),
('投票删除', 'voting:delete', 'voting', 'voting', 'delete', '删除投票', TRUE, 52),
('投票统计', 'voting:stats', 'voting', 'voting', 'stats', '查看投票统计', TRUE, 53);

-- ========================================
-- 初始化系统菜单
-- ========================================
INSERT INTO `menus` (`parent_id`, `menu_name`, `menu_code`, `menu_type`, `route_path`, `component_path`, `icon`, `sort_order`, `is_system`) VALUES
-- 主菜单
(0, '仪表板', 'dashboard', 1, '/dashboard', 'Dashboard', 'dashboard', 1, TRUE),
(0, '内容管理', 'content', 1, '/content', 'Content', 'content', 2, TRUE),
(0, '用户管理', 'user', 1, '/user', 'User', 'user', 3, TRUE),
(0, '系统管理', 'system', 1, '/system', 'System', 'system', 4, TRUE),
(0, '数据统计', 'statistics', 1, '/statistics', 'Statistics', 'chart', 5, TRUE),

-- 内容管理子菜单
(2, '文章管理', 'content.article', 2, '/content/article', 'Content/Article', 'article', 1, TRUE),
(2, '视频管理', 'content.video', 2, '/content/video', 'Content/Video', 'video', 2, TRUE),
(2, '分类管理', 'content.category', 2, '/content/category', 'Content/Category', 'category', 3, TRUE),
(2, '标签管理', 'content.tag', 2, '/content/tag', 'Content/Tag', 'tag', 4, TRUE),
(2, '笔名管理', 'content.pen_name', 2, '/content/pen-name', 'Content/PenName', 'pen', 5, TRUE),
(2, '置顶管理', 'content.pin', 2, '/content/pin', 'Content/Pin', 'pin', 6, TRUE),

-- 用户管理子菜单
(3, '用户列表', 'user.list', 2, '/user/list', 'User/List', 'user-list', 1, TRUE),
(3, '角色管理', 'user.role', 2, '/user/role', 'User/Role', 'role', 2, TRUE),
(3, '权限管理', 'user.permission', 2, '/user/permission', 'User/Permission', 'permission', 3, TRUE),

-- 系统管理子菜单
(4, '菜单管理', 'system.menu', 2, '/system/menu', 'System/Menu', 'menu', 1, TRUE),
(4, '系统配置', 'system.config', 2, '/system/config', 'System/Config', 'config', 2, TRUE),
(4, '操作日志', 'system.log', 2, '/system/log', 'System/Log', 'log', 3, TRUE),
(4, '推送管理', 'system.push', 2, '/system/push', 'System/Push', 'push', 4, TRUE),
(4, '投票管理', 'system.voting', 2, '/system/voting', 'System/Voting', 'voting', 5, TRUE);

-- ========================================
-- 角色权限分配
-- ========================================

-- 超级管理员拥有所有权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 1, `permission_id`, 1 FROM `permissions`;

-- 内容总监权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 2, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:create', 'article:edit', 'article:publish', 'article:review', 'article:pin', 'article:archive',
    'video:view', 'video:upload', 'video:edit', 'video:publish',
    'category:view', 'category:create', 'category:edit',
    'tag:view', 'tag:create', 'tag:edit',
    'pen_name:view', 'pen_name:create', 'pen_name:edit',
    'user:view', 'user:edit', 'user:status',
    'push:view', 'push:create', 'push:send', 'push:stats',
    'voting:view', 'voting:create', 'voting:edit', 'voting:stats',
    'log:view'
);

-- 高级编辑权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 3, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:create', 'article:edit', 'article:publish', 'article:review', 'article:pin',
    'video:view', 'video:upload', 'video:edit', 'video:publish',
    'category:view',
    'tag:view', 'tag:create', 'tag:edit',
    'pen_name:view', 'pen_name:create', 'pen_name:edit',
    'push:view', 'push:create', 'push:send',
    'voting:view', 'voting:create', 'voting:edit'
);

-- 编辑权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 4, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:create', 'article:edit',
    'video:view', 'video:upload', 'video:edit',
    'category:view',
    'tag:view', 'tag:create',
    'pen_name:view', 'pen_name:create',
    'voting:view'
);

-- 记者权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 5, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:create', 'article:edit',
    'video:view', 'video:upload', 'video:edit',
    'category:view',
    'tag:view', 'tag:create',
    'pen_name:view', 'pen_name:create'
);

-- 运营专员权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 6, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:pin', 'article:archive',
    'video:view',
    'category:view',
    'tag:view',
    'pen_name:view',
    'push:view', 'push:create', 'push:send', 'push:stats',
    'voting:view', 'voting:create', 'voting:edit', 'voting:stats',
    'log:view'
);

-- 技术管理员权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 7, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'user:view', 'user:create', 'user:edit', 'user:status',
    'role:view', 'role:create', 'role:edit', 'role:permission',
    'menu:view', 'menu:create', 'menu:edit', 'menu:sort',
    'config:view', 'config:edit',
    'log:view'
);

-- 审核员权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `granted_by`)
SELECT 8, `permission_id`, 1 FROM `permissions` 
WHERE `permission_code` IN (
    'article:view', 'article:review', 'article:edit',
    'video:view', 'video:edit',
    'category:view',
    'tag:view',
    'pen_name:view'
);

-- ========================================
-- 角色菜单分配
-- ========================================

-- 超级管理员拥有所有菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 1, `menu_id`, 1 FROM `menus`;

-- 内容总监菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 2, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name', 'content.pin', 'user.list', 'system.push', 
    'system.voting', 'system.log'
);

-- 高级编辑菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 3, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name', 'system.push', 'system.voting'
);

-- 编辑菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 4, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name', 'system.voting'
);

-- 记者菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 5, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name'
);

-- 运营专员菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 6, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name', 'content.pin', 'system.push', 'system.voting', 
    'system.log', 'statistics'
);

-- 技术管理员菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 7, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'user', 'user.list', 'user.role', 'user.permission', 'system', 
    'system.menu', 'system.config', 'system.log'
);

-- 审核员菜单
INSERT INTO `role_menus` (`role_id`, `menu_id`, `granted_by`)
SELECT 8, `menu_id`, 1 FROM `menus` 
WHERE `menu_code` IN (
    'dashboard', 'content', 'content.article', 'content.video', 'content.category', 
    'content.tag', 'content.pen_name'
);