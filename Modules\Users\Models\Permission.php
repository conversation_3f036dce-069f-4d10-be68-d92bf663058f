<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 权限模型
 *
 * @property int $permission_id 权限ID
 * @property string $permission_name 权限名称
 * @property string $permission_code 权限代码
 * @property string $module 所属模块
 * @property string $resource 资源类型
 * @property string $action 操作类型
 * @property string|null $description 权限描述
 * @property bool $is_system 是否为系统预设权限
 * @property int $status 状态: 0-禁用, 1-启用
 * @property int $sort_order 排序顺序
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class Permission extends Model
{
    use SoftDeletes;

    protected $table = 'permissions';
    protected $primaryKey = 'permission_id';

    protected $fillable = [
        'permission_name',
        'permission_code',
        'module',
        'resource',
        'action',
        'description',
        'is_system',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'status' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 获取拥有此权限的所有角色
     *
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions', 'permission_id', 'role_id')
                    ->withPivot(['granted_by', 'granted_at', 'expires_at'])
                    ->withTimestamps();
    }

    /**
     * 检查权限是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查是否为系统权限
     *
     * @return bool
     */
    public function isSystemPermission(): bool
    {
        return $this->is_system === true;
    }

    /**
     * 获取完整的权限代码
     *
     * @return string
     */
    public function getFullCodeAttribute(): string
    {
        return "{$this->module}:{$this->resource}:{$this->action}";
    }

    /**
     * 获取权限类型文本
     *
     * @return string
     */
    public function getActionTextAttribute(): string
    {
        $actions = [
            'view' => '查看',
            'create' => '创建',
            'edit' => '编辑',
            'delete' => '删除',
            'publish' => '发布',
            'review' => '审核',
            'pin' => '置顶',
            'archive' => '归档',
            'upload' => '上传',
            'download' => '下载',
            'export' => '导出',
            'import' => '导入',
            'status' => '状态管理',
            'password' => '密码管理',
            'permission' => '权限分配',
            'sort' => '排序',
            'send' => '发送',
            'stats' => '统计'
        ];

        return $actions[$this->action] ?? $this->action;
    }

    /**
     * 作用域：启用的权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：按模块筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $module
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * 作用域：按资源类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $resource
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByResource($query, string $resource)
    {
        return $query->where('resource', $resource);
    }

    /**
     * 作用域：按操作类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 作用域：系统权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * 作用域：自定义权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * 作用域：按权限代码搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('permission_code', 'like', "%{$code}%");
    }

    /**
     * 作用域：按权限名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('permission_name', 'like', "%{$name}%");
    }
}
