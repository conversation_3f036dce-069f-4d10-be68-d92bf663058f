.bwms-page .banner {
  max-height: 255px;
  overflow: hidden;
}
.bwms-page .search-result {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #fff;
}
.bwms-page .search-result .search-keyword {
  color: #34495e;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .search-result .search-keyword .keyword,
.bwms-page .search-result .search-keyword .num {
  margin: 0 8px;
  display: block;
  color: #4160F0;
  font-weight: bold;
}
.bwms-page .result-list {
  padding: 10px;
  background-color: #fff;
}
.bwms-page .result-list .red {
  color: #ff0000 !important;
}
.bwms-page .result-list .tit {
  margin-bottom: 10px;
  font-size: 14px;
  color: #34495e;
}
.bwms-page .result-list .list .item {
  margin-top: 10px;
  border-bottom: 1px dashed #E5E9EE;
  padding: 10px 0;
}
.bwms-page .result-list .list .item .item-tit {
  color: #34495e;
  font-size: 20px;
  display: block;
}
.bwms-page .result-list .list .item .desc {
  margin-top: 5px;
  font-size: 14px;
  color: #34495e;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .result-list .list .item .classification {
  margin-top: 10px;
  color: #c4cfdb;
  font-size: 13px;
}
.bwms-page .result-list .list .item .classification a {
  color: #c4cfdb;
  font-size: 13px;
}
.bwms-page .pagination-box {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .pagination-box a {
  margin: 5px;
  border-radius: 5px;
  color: #666;
  font-size: 13px;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .pagination-box a.active {
  background-color: #3555CC;
  color: #fff;
}
.bwms-page .pagination-box a:hover {
  color: #3555CC;
}
.bwms-page .pagination-box .more {
  color: #666;
  font-size: 13px;
}
