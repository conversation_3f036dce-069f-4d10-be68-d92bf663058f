<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.accordionEditor.tab.content')" name="content">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.accordionEditor.form.items')">
            <div v-for="(item, index) in accordionItems" :key="index" class="accordion-item-edit">
              <div class="accordion-item-header">
                <span>{{$t('Editor.accordionEditor.form.item')}} #{{ index + 1 }}</span>
                <div class="accordion-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    :title="$t('Editor.accordionEditor.button.edit')"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="accordionItems.length > 1"
                    :title="$t('Editor.accordionEditor.button.delete')"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="accordion-item-content">
                <el-form-item :label="$t('Editor.accordionEditor.form.title')">
                  <el-input v-model="accordionItems[index].title" @input="markAsChanged" />
                </el-form-item>
                <el-form-item :label="$t('Editor.accordionEditor.form.content')">
                  <el-input 
                    v-model="accordionItems[index].content" 
                    type="textarea" 
                    :rows="3" 
                    @input="markAsChanged"
                  />
                </el-form-item>
                <el-form-item :label="$t('Editor.accordionEditor.form.expanded')">
                  <el-switch v-model="accordionItems[index].expanded" @change="markAsChanged" />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">{{$t('Editor.accordionEditor.button.addItem')}}</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('Editor.accordionEditor.tab.style')" name="style">
        <el-form label-position="top">
          <el-form-item :label="$t('Editor.accordionEditor.style.borderStyle')">
            <el-radio-group v-model="accordionBorderStyle" @change="markAsChanged">
              <el-radio label="default">{{$t('Editor.accordionEditor.style.borderDefault')}}</el-radio>
              <el-radio label="flush">{{$t('Editor.accordionEditor.style.borderFlush')}}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.accordionEditor.style.theme')">
            <el-select v-model="accordionTheme" @change="markAsChanged" style="width: 100%">
              <el-option :label="$t('Editor.accordionEditor.style.themeDefault')" value="default" />
              <el-option :label="$t('Editor.accordionEditor.style.themePrimary')" value="primary" />
              <el-option :label="$t('Editor.accordionEditor.style.themeSuccess')" value="success" />
              <el-option :label="$t('Editor.accordionEditor.style.themeInfo')" value="info" />
              <el-option :label="$t('Editor.accordionEditor.style.themeWarning')" value="warning" />
              <el-option :label="$t('Editor.accordionEditor.style.themeDanger')" value="danger" />
            </el-select>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.accordionEditor.style.gapSize')">
            <el-select v-model="gapSize" @change="markAsChanged" style="width: 100%">
              <el-option :label="$t('Editor.accordionEditor.style.gapNone')" value="none" />
              <el-option :label="$t('Editor.accordionEditor.style.gapSmall')" value="small" />
              <el-option :label="$t('Editor.accordionEditor.style.gapMedium')" value="medium" />
              <el-option :label="$t('Editor.accordionEditor.style.gapLarge')" value="large" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 應用按鈕，只在有更改時顯示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.accordionEditor.button.apply')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineProps, defineEmits, defineOptions, computed } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

// 定義組件名稱
defineOptions({
  name: 'AccordionEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

const { t } = useI18n()

// 當前激活的標籤
const activeTab = ref('content')

// 編輯中的手風琴項目索引
const editingIndex = ref(0)

// 是否有未保存的更改
const isChanged = ref(false)

// 手風琴項目
interface AccordionItem {
  title: string
  content: string
  expanded: boolean
  id?: string
  headingId?: string
}

// 手風琴數據和配置
const accordionId = ref('')
const originalHtml = ref('')
const originalStructure = ref<{
  rootElement: HTMLElement | null,
  outerClasses: string[],
  containerClasses: string[],
  rowClasses: string[],
  colClasses: string[],
  customAttributes: Record<string, string>
}>({
  rootElement: null,
  outerClasses: [],
  containerClasses: [],
  rowClasses: [],
  colClasses: [],
  customAttributes: {}
})

// 手風琴數據
const accordionItems = ref<AccordionItem[]>([
  {
    title: '手风琴项目 #1',
    content: '这是第一个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。',
    expanded: true
  }
])

// 樣式選項
const accordionBorderStyle = ref('default')
const accordionTheme = ref('default')
const gapSize = ref('none')

/**
 * 重置編輯器狀態
 */
const resetEditorState = () => {
  accordionId.value = ''
  originalHtml.value = ''
  originalStructure.value = {
    rootElement: null,
    outerClasses: [],
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    customAttributes: {}
  }
  accordionItems.value = [{
    title: '手風琴項目 #1',
    content: '這是第一個手風琴項目的內容。您可以在這裡放置任何文本或HTML內容。',
    expanded: true
  }]
  accordionBorderStyle.value = 'default'
  accordionTheme.value = 'default'
  gapSize.value = 'none'
  editingIndex.value = 0
  isChanged.value = false
}

/**
 * 初始化編輯器
 */
const initializeEditor = (element: HTMLElement | null) => {
  try {
    if (!element) {
      resetEditorState()
      return
    }

    // 存儲原始HTML
    originalHtml.value = element.outerHTML
    
    // 解析結構
    const structureParsed = parseAccordionStructure(element)
    
    // 提取項目內容
    const itemsExtracted = extractAccordionItems(element)
    
    // 如果解析失敗，使用默認值
    if (!structureParsed || !itemsExtracted || accordionItems.value.length === 0) {
      accordionItems.value = [{
        title: '手風琴項目 #1',
        content: '這是第一個手風琴項目的內容。您可以在這裡放置任何文本或HTML內容。',
        expanded: true
      }]
    }
    
    // 重置更改狀態
    isChanged.value = false
  } catch (error) {
    console.error('初始化編輯器時出錯:', error)
    resetEditorState()
  }
}

// 監聽 blockElement 的變化
watch(() => props.blockElement, (newElement) => {
  if (newElement) {
    initializeEditor(newElement as HTMLElement)
  } else {
    resetEditorState()
  }
}, { deep: true, immediate: true })

// 解析HTML結構，提取原始結構信息
const parseAccordionStructure = (element: HTMLElement) => {
  try {
    // 存儲原始元素用於參考
    originalStructure.value.rootElement = element
    
    // 獲取根元素的類名和屬性
    originalStructure.value.outerClasses = Array.from(element.classList)
    
    // 提取ID
    const id = element.id || `accordion-${Date.now()}`
    accordionId.value = id
    
    // 提取自定義屬性
    const customAttributes: Record<string, string> = {}
    Array.from(element.attributes).forEach(attr => {
      if (attr.name !== 'class' && attr.name !== 'id' && attr.name !== 'style') {
        customAttributes[attr.name] = attr.value
      }
    })
    originalStructure.value.customAttributes = customAttributes
    
    // 查找容器結構
    const container = element.querySelector('.container-fluid, .container')
    if (container) {
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
        }
      }
    }
    
    // 提取樣式信息
    // 邊框樣式
    if (element.classList.contains('accordion-flush')) {
      accordionBorderStyle.value = 'flush'
    } else {
      accordionBorderStyle.value = 'default'
    }
    
    // 主題色
    const themeMatches = Array.from(element.classList).find(cls => {
      return cls.match(/text-(primary|secondary|success|info|warning|danger)/)
    })
    
    if (themeMatches) {
      const theme = themeMatches.replace('text-', '')
      if (['primary', 'secondary', 'success', 'info', 'warning', 'danger'].includes(theme)) {
        accordionTheme.value = theme
      }
    } else {
      accordionTheme.value = 'default'
    }
    
    // 間距
    if (element.classList.contains('accordion-gap-small')) {
      gapSize.value = 'small'
    } else if (element.classList.contains('accordion-gap-medium')) {
      gapSize.value = 'medium'
    } else if (element.classList.contains('accordion-gap-large')) {
      gapSize.value = 'large'
    } else {
      gapSize.value = 'none'
    }
    
    return true
  } catch (error) {
    console.error('解析手風琴結構時出錯:', error)
    return false
  }
}

// 提取手風琴項目內容
const extractAccordionItems = (element: HTMLElement) => {
  try {
    // 提取各個手風琴項
    const items = element.querySelectorAll('.accordion-item')
    
    if (items.length > 0) {
      const newItems: AccordionItem[] = []
      
      items.forEach((item, index) => {
        const button = item.querySelector('.accordion-button')
        const collapse = item.querySelector('.accordion-collapse')
        const body = item.querySelector('.accordion-body')
        
        // 獲取相關ID
        const headingId = button?.closest('.accordion-header')?.id || `heading-${index}`
        const itemId = collapse?.id || `collapse-${index}`
        
        // 是否展開
        const isExpanded = collapse?.classList.contains('show') || 
                          button?.getAttribute('aria-expanded') === 'true' || 
                          false
        
        newItems.push({
          title: button?.textContent?.trim() || `手風琴項目 #${index + 1}`,
          content: body?.innerHTML?.trim() || '這是手風琴項目的內容。',
          expanded: isExpanded,
          id: itemId,
          headingId: headingId
        })
      })
      
      if (newItems.length > 0) {
        accordionItems.value = newItems
      }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取手風琴項目時出錯:', error)
    return false
  }
}

// 標記為已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加手風琴項目
const addItem = () => {
  accordionItems.value.push({
    title: `手風琴項目 #${accordionItems.value.length + 1}`,
    content: '這是新手風琴項目的內容。',
    expanded: false
  })
  editingIndex.value = accordionItems.value.length - 1
  markAsChanged()
}

// 編輯手風琴項目
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 刪除手風琴項目
const removeItem = (index: number) => {
  if (accordionItems.value.length > 1) {
    accordionItems.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

// 保留原始ID或生成新ID
const getAccordionId = () => {
  return accordionId.value || `accordion-${Date.now()}`
}

/**
 * 準備手風琴HTML，保留原有結構和屬性
 */
const prepareAccordionHTML = (): string => {
  // 使用原始ID或生成新ID
  const currentAccordionId = getAccordionId()
  
  // 構建類名列表，確保保留原始類名
  let accordionClasses = ['accordion']
  
  // 過濾掉核心功能類，保留其他自定義類
  if (originalStructure.value.outerClasses.length > 0) {
    originalStructure.value.outerClasses.forEach(cls => {
      if (!['accordion', 'accordion-flush', 'responsive-block'].includes(cls) && 
          !cls.startsWith('accordion-gap-') && 
          !cls.startsWith('text-')) {
        accordionClasses.push(cls)
      }
    })
  }
  
  // 添加響應式塊類
  if (!accordionClasses.includes('responsive-block')) {
    accordionClasses.push('responsive-block')
  }
  
  // flush風格
  if (accordionBorderStyle.value === 'flush') {
    accordionClasses.push('accordion-flush')
  }
  
  // 主題色
  if (accordionTheme.value !== 'default') {
    if (["primary","secondary","success","danger","info","warning"].includes(accordionTheme.value)) {
      accordionClasses.push(`text-${accordionTheme.value}`)
    }
  }
  
  // 間距類
  if (gapSize.value !== 'none') {
    accordionClasses.push(`accordion-gap-${gapSize.value}`)
  }
  
  // 計算項目間距樣式 - 使用CSS類而不是內聯樣式
  let marginClass = ''
  if (gapSize.value === 'small') {
    marginClass = 'mb-2' // 0.5rem
  } else if (gapSize.value === 'medium') {
    marginClass = 'mb-3' // 1rem
  } else if (gapSize.value === 'large') {
    marginClass = 'mb-4' // 1.5rem
  }

  // 生成自定義屬性字符串
  let customAttrsStr = ''
  Object.entries(originalStructure.value.customAttributes).forEach(([key, value]) => {
    // 確保特殊字符被正確轉義
    const escapedValue = value.replace(/"/g, '&quot;')
    customAttrsStr += ` ${key}="${escapedValue}"`
  })
  
  // 使用原始容器類或默認類
  const containerClasses = originalStructure.value.containerClasses.length > 0 
    ? originalStructure.value.containerClasses.join(' ')
    : 'container-fluid p-0'
  
  // 使用原始行類或默認類
  const rowClasses = originalStructure.value.rowClasses.length > 0
    ? originalStructure.value.rowClasses.join(' ')
    : 'row justify-content-center'
  
  // 使用原始列類或默認類
  const colClasses = originalStructure.value.colClasses.length > 0
    ? originalStructure.value.colClasses.join(' ')
    : 'col-12 col-md-10 col-lg-8'

  // 生成手風琴項目HTML
  let itemsHtml = ''
  accordionItems.value.forEach((item, idx) => {
    // 使用原有ID或生成新ID
    const itemId = item.id || `${currentAccordionId}-collapse-${idx}`
    const headingId = item.headingId || `${currentAccordionId}-heading-${idx}`
    
    // 添加按鈕樣式
    const buttonClass = [
      'accordion-button',
      !item.expanded ? 'collapsed' : '',
      accordionTheme.value !== 'default' ? `text-${accordionTheme.value}` : ''
    ].filter(Boolean).join(' ')
    
    // 安全處理HTML內容 - 確保特殊字符處理
    const safeTitle = item.title || '標題'
    const safeContent = item.content || '內容'
    
    // 添加外部CSS類處理間距，避免內聯樣式
    itemsHtml += `
      <div class="accordion-item ${marginClass}">
        <h2 class="accordion-header" id="${headingId}">
          <button class="${buttonClass}" 
            type="button" 
            data-bs-toggle="collapse" 
            data-bs-target="#${itemId}" 
            aria-expanded="${item.expanded ? 'true' : 'false'}" 
            aria-controls="${itemId}">
            ${safeTitle}
          </button>
        </h2>
        <div id="${itemId}" 
          class="accordion-collapse collapse${item.expanded ? ' show' : ''}" 
          aria-labelledby="${headingId}" 
          data-bs-parent="#${currentAccordionId}">
          <div class="accordion-body">
            ${safeContent}
          </div>
        </div>
      </div>
    `
  })

  // 返回完整手風琴HTML，保留原有結構
  return `
    <div data-bs-component="accordion" class="${accordionClasses.join(' ')}" id="${currentAccordionId}"${customAttrsStr}>
      <div class="${containerClasses}">
        <div class="${rowClasses}">
          <div class="${colClasses}">
            ${itemsHtml}
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

// 應用更改
const applyChanges = () => {
  try {
    const html = prepareAccordionHTML()
    
    // 發出更新事件，包含完整的HTML結構
    emit('update-block', { html })
    
    // 重置更改狀態
    isChanged.value = false
  } catch (error) {
    console.error('應用手風琴更改時出錯:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.accordion-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.accordion-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.accordion-item-actions {
  display: flex;
  gap: 5px;
}

.accordion-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 