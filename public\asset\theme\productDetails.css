.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .product-info {
  margin-bottom: 50px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .product-info .pic {
  width: 40%;
}
.bwms-page .product-info .name-price {
  padding-left: 60px;
  padding-top: 30px;
  width: 60%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.bwms-page .product-info .name-price .price .product-name {
  margin-bottom: 40px;
  font-size: 28px;
  font-weight: bold;
  line-height: 1.4;
  color: #333;
}
.bwms-page .product-info .name-price .price .product-price {
  color: #ff9600;
  font-size: 24px;
  line-height: 1.3;
}
.bwms-page .product-info .name-price .buy-btn {
  margin-bottom: 20px;
  border-radius: 0;
  padding: 20px 40px;
  color: #fff;
  background-color: #FF9600;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .product-info .name-price .buy-btn .iconfont + span {
  margin-left: 5px;
}
.bwms-page .product-info .name-price .buy-btn span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .product-info .name-price .buy-btn span {
  display: block;
  line-height: 1.5;
}
.bwms-page .product-info .name-price .buy-btn:hover {
  background-color: #FCB319;
  color: #fff;
}
.bwms-page .product-nav {
  border-bottom: 1px solid #EFEFEF;
  background-color: #fff;
}
.bwms-page .product-nav .tab-nav {
  margin-bottom: -1px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .product-nav .tab-nav li {
  border-bottom: 3px solid #ff9600;
  padding: 20px 0;
  cursor: pointer;
  color: #ff9600;
  font-size: 18px;
  line-height: 1.33;
}
.bwms-page .product-details {
  padding: 30px 0 50px;
  background-color: #fff;
}
.bwms-page .product-list {
  padding-top: 50px;
  padding-bottom: 90px;
  position: relative;
}
.bwms-page .product-list .tit {
  margin-bottom: 30px;
  line-height: 1.11;
  color: #333;
  font-size: 18px;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .product-list .tit::before {
  margin-right: 15px;
  content: '';
  display: block;
  width: 5px;
  background-color: #ff9600;
}
.bwms-page .product-list .swiper-container .swiper-slide {
  padding: 30px;
  background-color: #fff;
}
.bwms-page .product-list .swiper-container .swiper-slide:hover {
  box-shadow: 0 5px 20px 5px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease-in-out;
}
.bwms-page .product-list .swiper-container .swiper-slide:hover .text-box h6 {
  color: #ff9600;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box {
  margin-top: 14px;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box h6 {
  font-size: 16px;
  line-height: 1.3;
  color: #6E6E6E;
  transition: color 0.35s ease-in-out;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box .price-views {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box .price-views .price {
  color: #ff9600;
  font-size: 20px;
  font-weight: bold;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box .price-views .views-num {
  font-size: 14px;
  color: #888;
}
.bwms-page .product-list .swiper-container .swiper-slide .text-box .price-views .views-num .iconfont {
  margin-right: 5px;
  font-size: 16px;
}
.bwms-page .product-list .swiper-pagination {
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
}

/* 调整按钮大小和内边距 */
.el-button {
    padding: 10px 40px; /* 按钮内边距 */
    font-size: 16px;    /* 文字大小 */
    border-radius: 0;   /* 边框圆角 */
}

/* 调整Element Plus的主要按钮颜色和悬停颜色 */
.el-button--primary {
    background-color: #FF9600; /* 按钮背景颜色 */
    border-color: #FF9600;     /* 按钮边框颜色 */
    color: #fff;               /* 文字颜色 */
}

.el-button--primary:hover {
    background-color: #FCB319; /* 按钮悬停时的背景颜色 */
    border-color: #FCB319;     /* 按钮悬停时的边框颜色 */
}

/* 按钮内部的图标和文本间隔 */
.el-button .iconfont + span {
    margin-left: 5px; /* 图标和文本之间的间隔 */
}

.el-button span + .iconfont {
    margin-right: 5px; /* 文本和图标之间的间隔 */
}
