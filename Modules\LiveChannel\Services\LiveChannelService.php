<?php

declare(strict_types=1);

namespace Modules\LiveChannel\Services;

use Modules\LiveChannel\Models\LiveChannel;
use Modules\LiveChannel\Enums\LiveChannelErrorCode;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class LiveChannelService
{
    /**
     * 获取频道列表
     */
    public function getList(array $params = []): LengthAwarePaginator
    {
        try {
            $query = LiveChannel::query();

            // 搜索
            if (!empty($params['keyword'])) {
                $query->search($params['keyword']);
            }

            // 状态筛选
            if (isset($params['status']) && $params['status'] !== null && $params['status'] !== '') {
                $query->where('status', $params['status']);
            }

            // 直播状态筛选
            if (isset($params['live_status']) && $params['live_status'] !== null && $params['live_status'] !== '') {
                $query->where('live_status', $params['live_status']);
            }

            // 时间范围筛选
            if (!empty($params['start_date'])) {
                $query->timeRange($params['start_date'], $params['end_date'] ?? null);
            }

            // 排序
            $sortField = $params['sort_field'] ?? 'sort';
            $sortOrder = $params['sort_order'] ?? 'asc';
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $perPage = $params['per_page'] ?? 15;
            
            return $query->paginate($perPage);
        } catch (Exception $e) {
            Log::error('获取频道列表失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            throw new Exception(LiveChannelErrorCode::CHANNEL_LIST_FAILED->getMessage(), LiveChannelErrorCode::CHANNEL_LIST_FAILED->value);
        }
    }

    /**
     * 获取频道详情
     */
    public function getDetail(int $id): LiveChannel
    {
        try {
            $channel = LiveChannel::find($id);
            
            if (!$channel) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            return $channel;
        } catch (Exception $e) {
            Log::error('获取频道详情失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 创建频道
     */
    public function create(array $data): LiveChannel
    {
        try {
            DB::beginTransaction();

            // 验证频道名称
            if (LiveChannel::isNameExists($data['name'])) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NAME_EXISTS->getMessage(), LiveChannelErrorCode::CHANNEL_NAME_EXISTS->value);
            }

            // 自动生成频道编号
            $data['channel_num'] = LiveChannel::generateChannelNum();

            // 创建频道
            $channel = LiveChannel::create($data);

            DB::commit();
            return $channel;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('创建频道失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新频道
     */
    public function update(int $id, array $data): LiveChannel
    {
        try {
            DB::beginTransaction();

            $channel = LiveChannel::find($id);
            if (!$channel) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            // 验证频道名称
            if (isset($data['name']) && LiveChannel::isNameExists($data['name'], $id)) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NAME_EXISTS->getMessage(), LiveChannelErrorCode::CHANNEL_NAME_EXISTS->value);
            }

            // 验证频道编号
            if (isset($data['channel_num']) && LiveChannel::isChannelNumExists($data['channel_num'], $id)) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NUM_EXISTS->getMessage(), LiveChannelErrorCode::CHANNEL_NUM_EXISTS->value);
            }

            // 更新频道
            $channel->update($data);

            DB::commit();
            return $channel->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('更新频道失败', [
                'id' => $id,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 删除频道
     */
    public function delete(int $id): bool
    {
        try {
            $channel = LiveChannel::find($id);
            if (!$channel) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            return $channel->delete();
        } catch (Exception $e) {
            Log::error('删除频道失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 批量删除
     */
    public function batchDelete(array $ids): bool
    {
        try {
            DB::beginTransaction();

            $channels = LiveChannel::whereIn('id', $ids)->get();
            
            if ($channels->count() !== count($ids)) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            $result = LiveChannel::whereIn('id', $ids)->delete();

            DB::commit();
            return $result > 0;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('批量删除频道失败', [
                'ids' => $ids,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新直播状态
     */
    public function updateLiveStatus(int $id, int $status): LiveChannel
    {
        try {
            $channel = LiveChannel::find($id);
            if (!$channel) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            $channel->update(['live_status' => $status]);

            return $channel->fresh();
        } catch (Exception $e) {
            Log::error('更新直播状态失败', [
                'id' => $id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 批量更新状态
     */
    public function batchUpdateStatus(array $ids, int $status): bool
    {
        try {
            DB::beginTransaction();

            $result = LiveChannel::whereIn('id', $ids)->update(['status' => $status]);

            DB::commit();
            return $result > 0;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('批量更新状态失败', [
                'ids' => $ids,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取直播中的频道
     */
    public function getLiveChannels(): Collection
    {
        try {
            return LiveChannel::enabled()
                ->live()
                ->ordered()
                ->get();
        } catch (Exception $e) {
            Log::error('获取直播中频道失败', [
                'error' => $e->getMessage()
            ]);
            throw new Exception(LiveChannelErrorCode::CHANNEL_LIST_FAILED->getMessage(), LiveChannelErrorCode::CHANNEL_LIST_FAILED->value);
        }
    }

    /**
     * 获取频道统计
     */
    public function getStatistics(): array
    {
        try {
            return [
                'total' => LiveChannel::count(),
                'enabled' => LiveChannel::enabled()->count(),
                'live' => LiveChannel::live()->count(),
                'disabled' => LiveChannel::where('status', 0)->count(),
            ];
        } catch (Exception $e) {
            Log::error('获取频道统计失败', [
                'error' => $e->getMessage()
            ]);
            throw new Exception(LiveChannelErrorCode::CHANNEL_LIST_FAILED->getMessage(), LiveChannelErrorCode::CHANNEL_LIST_FAILED->value);
        }
    }

    /**
     * 绑定直播配置
     */
    public function bindStreamConfig(int $id, string $streamUrl, string $streamKey): LiveChannel
    {
        try {
            DB::beginTransaction();

            $channel = LiveChannel::find($id);
            if (!$channel) {
                throw new Exception(LiveChannelErrorCode::CHANNEL_NOT_FOUND->getMessage(), LiveChannelErrorCode::CHANNEL_NOT_FOUND->value);
            }

            // 更新直播配置
            $channel->update([
                'stream_url' => $streamUrl,
                'stream_key' => $streamKey,
                'updated_by' => 1, // 这里应该从认证用户中获取
            ]);

            DB::commit();
            return $channel->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('绑定直播配置失败', [
                'id' => $id,
                'stream_url' => $streamUrl,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取频道下拉选项
     */
    public function getChannelOptions(string $keyword = '', int $limit = 20): array
    {
        try {
            $query = LiveChannel::query()
                ->select(['id', 'channel_num', 'name', 'name_hk', 'stream_url', 'stream_key']);
                // ->where('status', 1); // 只获取启用状态的频道，暂时注释掉状态限制

            // 搜索功能
            if (!empty($keyword)) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('name_hk', 'like', "%{$keyword}%")
                      ->orWhere('channel_num', 'like', "%{$keyword}%");
                });
            }

            $channels = $query->orderBy('sort', 'asc')
                ->orderBy('id', 'desc')
                ->limit($limit)
                ->get();

            // 调试信息
            Log::info('Channel options query', [
                'keyword' => $keyword,
                'limit' => $limit,
                'count' => $channels->count(),
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            return $channels->map(function ($channel) {
                return [
                    'id' => $channel->id,
                    'channel_num' => $channel->channel_num,
                    'name' => $channel->name,
                    'name_hk' => $channel->name_hk,
                    'stream_url' => $channel->stream_url,
                    'stream_key' => $channel->stream_key,
                    'label' => "{$channel->channel_num} - {$channel->name}", // 下拉选项显示文本
                    'value' => $channel->id // 下拉选项值
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('获取频道选项失败', [
                'keyword' => $keyword,
                'limit' => $limit,
                'error' => $e->getMessage()
            ]);
            throw new Exception(LiveChannelErrorCode::CHANNEL_LIST_FAILED->getMessage(), LiveChannelErrorCode::CHANNEL_LIST_FAILED->value);
        }
    }
} 