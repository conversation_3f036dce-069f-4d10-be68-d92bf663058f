{{ if (field.containerClass) { }}<!-- Signature -->
<div class="{{= field.containerClass }}">
    <div class="form-group{{ if(field.required) { }} required-control{{ } }}">
        {{ if (field.label) { }}<label {{ if (field.labelClass) { }} class="{{= field.labelClass }}"{{ } }} for="{{= field.id }}">{{= field.label }}</label>{{ } }}{{ if (field.helpText && field.helpTextPlacement === "above") { }}
        <p class="form-text">{{= field.helpText }}</p>{{ } }}
        <div class="signature-pad">
            <canvas id="{{= field.id }}" width="{{= field.width }}" height="{{= field.height }}" data-color="{{= field.color }}" {{ if (field.cssClass) { }} class="{{= field.cssClass }}"{{ } }}></canvas>
        </div>
        {{ if (field.helpText && field.helpTextPlacement === "below") { }}<p class="form-text">{{= field.helpText }}</p>{{ } }}
        <div class="signature-pad-actions">
            {{ if (field.clear) { }}<button type="button" id="clear_{{= field.id }}" name="clear_{{= field.id }}" class="btn btn-sm btn-default btn-clear" data-exclude="true">{{= field.clearText }}</button>{{ } }}
            {{ if (field.undo) { }}<button type="button" id="undo_{{= field.id }}" name="undo_{{= field.id }}" class="btn btn-sm btn-default btn-undo" data-exclude="true">{{= field.undoText }}</button>{{ } }}
        </div>
        <input type="text" style="display: none" name="hidden_{{= field.id }}" id="hidden_{{= field.id }}" value="" data-alias="{{= field.alias }}" data-label="{{= field.label }}"{{ if(field.required) { }} required{{ } }}>
    </div>
</div>
{{ } else { }}<!-- Signature -->
<div class="form-group{{ if(field.required) { }} required-control{{ } }}">
    {{ if (field.label) { }}<label {{ if (field.labelClass) { }} class="{{= field.labelClass }}"{{ } }} for="{{= field.id }}">{{= field.label }}</label>{{ } }}{{ if (field.helpText && field.helpTextPlacement === "above") { }}
    <p class="form-text">{{= field.helpText }}</p>{{ } }}
    <div class="signature-pad">
        <canvas id="{{= field.id }}" width="{{= field.width }}" height="{{= field.height }}" data-color="{{= field.color }}" {{ if (field.cssClass) { }} class="{{= field.cssClass }}"{{ } }}></canvas>
    </div>
    {{ if (field.helpText && field.helpTextPlacement === "below") { }}<p class="form-text">{{= field.helpText }}</p>{{ } }}
    <div class="signature-pad-actions">
        {{ if (field.clear) { }}<button type="button" id="clear_{{= field.id }}" name="clear_{{= field.id }}" class="btn btn-sm btn-default btn-clear" data-exclude="true">{{= field.clearText }}</button>{{ } }}
        {{ if (field.undo) { }}<button type="button" id="undo_{{= field.id }}" name="undo_{{= field.id }}" class="btn btn-sm btn-default btn-undo" data-exclude="true">{{= field.undoText }}</button>{{ } }}
    </div>
    <input type="text" style="display: none" name="hidden_{{= field.id }}" id="hidden_{{= field.id }}" value="" data-alias="{{= field.alias }}" data-label="{{= field.label }}"{{ if(field.required) { }} required{{ } }}>
</div>
{{ } }}