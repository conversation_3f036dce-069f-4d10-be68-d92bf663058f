<?php

declare(strict_types=1);

namespace Modules\Region\Enums;

use Modules\Core\Enums\BaseEnum;

/**
 * 频道模块错误码枚举
 */
enum ChannelErrorCode: int
{
    /**
     * 通用错误 (16000-16099)
     */
    case CHANNEL_NOT_FOUND = 16000;
    case CHANNEL_ALREADY_EXISTS = 16001;
    case CHANNEL_DISABLED = 16002;

    /**
     * 创建相关错误 (16100-16199)
     */
    case CHANNEL_CREATE_FAILED = 16100;
    case CHANNEL_IMPORT_FAILED = 16101;
    case CHANNEL_BATCH_CREATE_FAILED = 16102;

    /**
     * 更新相关错误 (16200-16299)
     */
    case CHANNEL_UPDATE_FAILED = 16200;
    case CHANNEL_STATUS_UPDATE_FAILED = 16201;
    case CHANNEL_SORT_UPDATE_FAILED = 16202;

    /**
     * 删除相关错误 (16300-16399)
     */
    case CHANNEL_DELETE_FAILED = 16300;
    case CHANNEL_BATCH_DELETE_FAILED = 16301;
    case CHANNEL_SOFT_DELETE_FAILED = 16302;

    /**
     * 查询相关错误 (16400-16499)
     */
    case CHANNEL_QUERY_FAILED = 16400;
    case CHANNEL_SEARCH_FAILED = 16401;
    case CHANNEL_LIST_FAILED = 16402;

    /**
     * 状态相关错误 (16500-16599)
     */
    case CHANNEL_STATUS_INVALID = 16500;
    case CHANNEL_STATE_TRANSITION_FAILED = 16501;
    case CHANNEL_STATUS_CHANGE_FAILED = 16502;

    /**
     * 验证相关错误 (16600-16699)
     */
    case CHANNEL_VALIDATION_FAILED = 16600;
    case CHANNEL_NAME_VALIDATION_FAILED = 16601;
    case CHANNEL_SORT_VALIDATION_FAILED = 16602;

    /**
     * 处理相关错误 (16700-16799)
     */
    case CHANNEL_PROCESS_FAILED = 16700;
    case CHANNEL_SYNC_FAILED = 16701;
    case CHANNEL_IMPORT_PROCESS_FAILED = 16702;

    /**
     * 业务相关错误 (16800-16899)
     */
    case CHANNEL_BUSINESS_RULE_VIOLATED = 16800;
    case CHANNEL_PERMISSION_DENIED = 16801;
    case CHANNEL_QUOTA_EXCEEDED = 16802;

    /**
     * 系统相关错误 (16900-16999)
     */
    case CHANNEL_SYSTEM_ERROR = 16900;
    case CHANNEL_DATABASE_ERROR = 16901;
    case CHANNEL_NETWORK_ERROR = 16902;

    /**
     * 获取错误信息
     *
     * @return string
     */
    public function message(): string
    {
        return match ($this) {
            // 通用错误
            self::CHANNEL_NOT_FOUND => '频道不存在',
            self::CHANNEL_ALREADY_EXISTS => '频道已存在',
            self::CHANNEL_DISABLED => '频道已禁用',

            // 创建相关错误
            self::CHANNEL_CREATE_FAILED => '创建频道失败',
            self::CHANNEL_IMPORT_FAILED => '频道导入失败',
            self::CHANNEL_BATCH_CREATE_FAILED => '批量创建频道失败',

            // 更新相关错误
            self::CHANNEL_UPDATE_FAILED => '更新频道失败',
            self::CHANNEL_STATUS_UPDATE_FAILED => '更新频道状态失败',
            self::CHANNEL_SORT_UPDATE_FAILED => '更新频道排序失败',

            // 删除相关错误
            self::CHANNEL_DELETE_FAILED => '删除频道失败',
            self::CHANNEL_BATCH_DELETE_FAILED => '批量删除频道失败',
            self::CHANNEL_SOFT_DELETE_FAILED => '软删除频道失败',

            // 查询相关错误
            self::CHANNEL_QUERY_FAILED => '查询频道失败',
            self::CHANNEL_SEARCH_FAILED => '搜索频道失败',
            self::CHANNEL_LIST_FAILED => '获取频道列表失败',

            // 状态相关错误
            self::CHANNEL_STATUS_INVALID => '频道状态无效',
            self::CHANNEL_STATE_TRANSITION_FAILED => '频道状态转换失败',
            self::CHANNEL_STATUS_CHANGE_FAILED => '更新频道状态失败',

            // 验证相关错误
            self::CHANNEL_VALIDATION_FAILED => '频道验证失败',
            self::CHANNEL_NAME_VALIDATION_FAILED => '频道名称验证失败',
            self::CHANNEL_SORT_VALIDATION_FAILED => '频道排序验证失败',

            // 处理相关错误
            self::CHANNEL_PROCESS_FAILED => '处理频道数据失败',
            self::CHANNEL_SYNC_FAILED => '同步频道数据失败',
            self::CHANNEL_IMPORT_PROCESS_FAILED => '处理频道导入数据失败',

            // 业务相关错误
            self::CHANNEL_BUSINESS_RULE_VIOLATED => '违反频道业务规则',
            self::CHANNEL_PERMISSION_DENIED => '频道权限不足',
            self::CHANNEL_QUOTA_EXCEEDED => '超出频道配额限制',

            // 系统相关错误
            self::CHANNEL_SYSTEM_ERROR => '频道系统错误',
            self::CHANNEL_DATABASE_ERROR => '频道数据库错误',
            self::CHANNEL_NETWORK_ERROR => '频道网络错误',
        };
    }

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::CHANNEL_VALIDATION_FAILED, self::CHANNEL_NAME_VALIDATION_FAILED, 
            self::CHANNEL_SORT_VALIDATION_FAILED => 400,
            self::CHANNEL_PERMISSION_DENIED => 403,
            self::CHANNEL_NOT_FOUND, self::CHANNEL_QUERY_FAILED, 
            self::CHANNEL_SEARCH_FAILED, self::CHANNEL_LIST_FAILED => 404,
            self::CHANNEL_SYSTEM_ERROR, self::CHANNEL_DATABASE_ERROR, 
            self::CHANNEL_NETWORK_ERROR => 503,
            default => 400,
        };
    }
} 