# 管理后台控制器模板

## 概述

本文档提供了管理后台控制器的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Admin\Controllers;

use Bingo\Base\BingoController as Controller;
use Bingo\Exceptions\BizException;
use Modules\YourModule\Admin\Requests\ListRequest;
use Modules\YourModule\Admin\Requests\CreateRequest;
use Modules\YourModule\Admin\Requests\UpdateRequest;
use Modules\YourModule\Admin\Requests\BatchRequest;
use Modules\YourModule\Admin\Requests\ExportRequest;
use Modules\YourModule\Admin\Requests\ImportRequest;
use Modules\YourModule\Services\YourService;
use Modules\YourModule\Enums\YourModuleErrorCode;

final class YourController extends Controller
{
    public function __construct(
        private readonly YourService $service
    ) {
    }

    /**
     * 列表页面
     */
    public function index(ListRequest $request): array
    {
        $result = $this->service->getList($request->validated());
        
        return [
            'items' => $result->items(),
            'total' => $result->total()
        ];
    }

    /**
     * 详情页面
     */
    public function show(int $id): array
    {
        $item = $this->service->findById($id);
        if (!$item) {
            BizException::throws(YourModuleErrorCode::NOT_FOUND);
        }
        
        return [
            'item' => $item
        ];
    }

    /**
     * 创建记录
     */
    public function store(CreateRequest $request): array
    {
        $item = $this->service->create($request->validated());
        
        return [
            'item' => $item
        ];
    }

    /**
     * 更新记录
     */
    public function update(int $id, UpdateRequest $request): array
    {
        $item = $this->service->update($id, $request->validated());
        
        return [
            'item' => $item
        ];
    }

    /**
     * 删除记录
     */
    public function destroy(int $id): array
    {
        $this->service->delete($id);
        
        return [
            'message' => '删除成功'
        ];
    }

    /**
     * 批量删除
     */
    public function batchDestroy(BatchRequest $request): array
    {
        $this->service->batchDelete($request->validated('ids'));
        
        return [
            'message' => '批量删除成功'
        ];
    }

    /**
     * 修改状态
     */
    public function changeStatus(int $id, UpdateRequest $request): array
    {
        $item = $this->service->changeStatus($id, $request->validated('status'));
        
        return [
            'item' => $item
        ];
    }

    /**
     * 导出数据
     */
    public function export(ExportRequest $request): array
    {
        $filePath = $this->service->export($request->validated());
        
        return [
            'file_path' => $filePath
        ];
    }

    /**
     * 导入数据
     */
    public function import(ImportRequest $request): array
    {
        $result = $this->service->import($request->file('file'));
        
        return [
            'success' => $result['success'],
            'failed' => $result['failed'],
            'errors' => $result['errors']
        ];
    }
}
```

## 请求验证类示例

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'keyword' => ['sometimes', 'string', 'max:100'],
            'status' => ['sometimes', 'string', Rule::in(['active', 'inactive'])],
            'start_date' => ['sometimes', 'date'],
            'end_date' => ['sometimes', 'date', 'after_or_equal:start_date'],
            'sort_field' => ['sometimes', 'string', Rule::in(['id', 'created_at', 'updated_at'])],
            'sort_order' => ['sometimes', 'string', Rule::in(['asc', 'desc'])],
        ];
    }

    public function messages(): array
    {
        return [
            'page.integer' => T('YourModule::validation.page.integer'),
            'page.min' => T('YourModule::validation.page.min'),
            'limit.integer' => T('YourModule::validation.limit.integer'),
            'limit.min' => T('YourModule::validation.limit.min'),
            'limit.max' => T('YourModule::validation.limit.max'),
            'keyword.string' => T('YourModule::validation.keyword.string'),
            'keyword.max' => T('YourModule::validation.keyword.max'),
            'status.string' => T('YourModule::validation.status.string'),
            'status.in' => T('YourModule::validation.status.in'),
            'start_date.date' => T('YourModule::validation.start_date.date'),
            'end_date.date' => T('YourModule::validation.end_date.date'),
            'end_date.after_or_equal' => T('YourModule::validation.end_date.after_or_equal'),
            'sort_field.string' => T('YourModule::validation.sort_field.string'),
            'sort_field.in' => T('YourModule::validation.sort_field.in'),
            'sort_order.string' => T('YourModule::validation.sort_order.string'),
            'sort_order.in' => T('YourModule::validation.sort_order.in'),
        ];
    }
}

class CreateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'status' => ['required', 'string', Rule::in(['active', 'inactive'])],
            'category_id' => ['required', 'integer', Rule::exists('categories', 'id')],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['integer', Rule::exists('tags', 'id')],
            'images' => ['nullable', 'array', 'max:5'],
            'images.*' => ['file', 'image', 'max:2048', 'mimes:jpeg,png,jpg'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => T('YourModule::validation.title.required'),
            'title.string' => T('YourModule::validation.title.string'),
            'title.max' => T('YourModule::validation.title.max'),
            'description.string' => T('YourModule::validation.description.string'),
            'description.max' => T('YourModule::validation.description.max'),
            'status.required' => T('YourModule::validation.status.required'),
            'status.string' => T('YourModule::validation.status.string'),
            'status.in' => T('YourModule::validation.status.in'),
            'category_id.required' => T('YourModule::validation.category_id.required'),
            'category_id.integer' => T('YourModule::validation.category_id.integer'),
            'category_id.exists' => T('YourModule::validation.category_id.exists'),
            'tags.array' => T('YourModule::validation.tags.array'),
            'tags.*.integer' => T('YourModule::validation.tags.*.integer'),
            'tags.*.exists' => T('YourModule::validation.tags.*.exists'),
            'images.array' => T('YourModule::validation.images.array'),
            'images.max' => T('YourModule::validation.images.max'),
            'images.*.file' => T('YourModule::validation.images.*.file'),
            'images.*.image' => T('YourModule::validation.images.*.image'),
            'images.*.max' => T('YourModule::validation.images.*.max'),
            'images.*.mimes' => T('YourModule::validation.images.*.mimes'),
        ];
    }
}
```

## 规范要求

1. 命名规范
   - 类名：使用大驼峰命名法
   - 方法名：使用小驼峰命名法
   - 变量名：使用小驼峰命名法
   - 常量名：使用大写下划线

2. 方法定义
   - 访问修饰符
   - 返回类型声明
   - 参数类型声明
   - 方法注释

3. 错误处理
   - 使用业务异常
   - 统一错误码
   - 友好错误信息
   - 日志记录

4. 输入处理
   - 使用 FormRequest 进行参数验证
   - 类型转换
   - 默认值处理
   - 安全过滤

## 最佳实践

1. 请求验证
```php
class UpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'status' => [
                'sometimes', 
                'required', 
                'string',
                Rule::in(['active', 'inactive']),
                function (string $attribute, mixed $value, Closure $fail) {
                    if ($value === 'inactive' && !$this->user()->can('deactivate_items')) {
                        $fail(T('YourModule::validation.status.permission_denied'));
                    }
                },
            ],
        ];
    }
}
```

2. 批量操作
```php
class BatchRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array', 'min:1', 'max:100'],
            'ids.*' => ['integer', Rule::exists('your_table', 'id')],
            'action' => ['required', 'string', Rule::in(['delete', 'activate', 'deactivate'])],
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->has('ids') && is_string($this->input('ids'))) {
            $this->merge([
                'ids' => explode(',', $this->input('ids'))
            ]);
        }
    }
}
```

3. 导入验证
```php
class ImportRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:xlsx,csv',
                'max:10240',
                function (string $attribute, mixed $value, Closure $fail) {
                    if ($value->getSize() > 10 * 1024 * 1024) {
                        $fail(T('YourModule::validation.file.size_exceeded'));
                    }

                    // 检查文件行数
                    $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                    $spreadsheet = $reader->load($value->getPathname());
                    $rowCount = $spreadsheet->getActiveSheet()->getHighestRow();
                    
                    if ($rowCount > 1000) {
                        $fail(T('YourModule::validation.file.too_many_rows'));
                    }
                },
            ],
        ];
    }
}
```

## 注意事项

1. 控制器职责
   - 参数验证和处理
   - 调用服务层方法
   - 格式化返回数据
   - 错误处理和转换

2. 安全考虑
   - 输入数据过滤
   - 权限检查
   - CSRF防护
   - XSS防护

3. 性能优化
   - 合理分页
   - 缓存使用
   - N+1问题避免
   - 大数据处理

4. 代码质量
   - 单一职责
   - 代码复用
   - 测试覆盖
   - 注释完整
