// https://crossfilter.github.io/crossfilter/ v1.5.4 Copyright 2020 Mike Bostock
!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(r=r||self).crossfilter=e()}(this,(function(){"use strict";let r=o,e=o,n=o,t=f,u=i;function o(r){for(var e=new Array(r),n=-1;++n<r;)e[n]=0;return e}function f(r,e){for(var n=r.length;n<e;)r[n++]=0;return r}function i(r,e){if(e>32)throw new Error("invalid array width!");return r}function a(e){this.length=e,this.subarrays=1,this.width=8,this.masks={0:0},this[0]=r(e)}"undefined"!=typeof Uint8Array&&(r=function(r){return new Uint8Array(r)},e=function(r){return new Uint16Array(r)},n=function(r){return new Uint32Array(r)},t=function(r,e){if(r.length>=e)return r;var n=new r.constructor(e);return n.set(r),n},u=function(r,t){var u;switch(t){case 16:u=e(r.length);break;case 32:u=n(r.length);break;default:throw new Error("invalid array width!")}return u.set(r),u}),a.prototype.lengthen=function(r){var e,n;for(e=0,n=this.subarrays;e<n;++e)this[e]=t(this[e],r);this.length=r},a.prototype.add=function(){var e,n,t,o,f;for(o=0,f=this.subarrays;o<f;++o)if(t=(~(e=this.masks[o])&e+1)>>>0,!((n=this.width-32*o)>=32)||t)return n<32&&t&1<<n&&(this[o]=u(this[o],n<<=1),this.width=32*o+n),this.masks[o]|=t,{offset:o,one:t};return this[this.subarrays]=r(this.length),this.masks[this.subarrays]=1,this.width+=8,{offset:this.subarrays++,one:1}},a.prototype.copy=function(r,e){var n,t;for(n=0,t=this.subarrays;n<t;++n)this[n][r]=this[n][e]},a.prototype.truncate=function(r){var e,n;for(e=0,n=this.subarrays;e<n;++e)for(var t=this.length-1;t>=r;t--)this[e][t]=0;this.length=r},a.prototype.zero=function(r){var e,n;for(e=0,n=this.subarrays;e<n;++e)if(this[e][r])return!1;return!0},a.prototype.zeroExcept=function(r,e,n){var t,u;for(t=0,u=this.subarrays;t<u;++t)if(t===e?this[t][r]&n:this[t][r])return!1;return!0},a.prototype.zeroExceptMask=function(r,e){var n,t;for(n=0,t=this.subarrays;n<t;++n)if(this[n][r]&e[n])return!1;return!0},a.prototype.only=function(r,e,n){var t,u;for(t=0,u=this.subarrays;t<u;++t)if(this[t][r]!=(t===e?n:0))return!1;return!0},a.prototype.onlyExcept=function(r,e,n,t,u){var o,f,i;for(f=0,i=this.subarrays;f<i;++f)if(o=this[f][r],f===e&&(o=(o&n)>>>0),o!=(f===t?u:0))return!1;return!0};var l={array8:o,array16:o,array32:o,arrayLengthen:f,arrayWiden:i,bitarray:a};var s={filterExact:(r,e)=>(function(n){var t=n.length;return[r.left(n,e,0,t),r.right(n,e,0,t)]}),filterRange:(r,e)=>{var n=e[0],t=e[1];return function(e){var u=e.length;return[r.left(e,n,0,u),r.left(e,t,0,u)]}},filterAll:r=>[0,r.length]},c=r=>r,h=()=>null,v=()=>0;function p(r){function e(r,e,t){for(var u=t-e,o=1+(u>>>1);--o>0;)n(r,o,u,e);return r}function n(e,n,t,u){for(var o,f=e[--u+n],i=r(f);(o=n<<1)<=t&&(o<t&&r(e[u+o])>r(e[u+o+1])&&o++,!(i<=r(e[u+o])));)e[u+n]=e[u+o],n=o;e[u+n]=f}return e.sort=function(r,e,t){for(var u,o=t-e;--o>0;)u=r[e],r[e]=r[e+o],r[e+o]=u,n(r,1,o,e);return r},e}const d=p(c);function g(r){var e=d.by(r);return function(n,t,u,o){var f,i,a,l=new Array(o=Math.min(u-t,o));for(i=0;i<o;++i)l[i]=n[t++];if(e(l,0,o),t<u){f=r(l[0]);do{r(a=n[t])>f&&(l[0]=a,f=r(e(l,0,o)[0]))}while(++t<u)}return l}}d.by=p;const y=g(c);function x(r){function e(e,n,t,u){for(;t<u;){var o=t+u>>>1;n<r(e[o])?u=o:t=o+1}return t}return e.right=e,e.left=function(e,n,t,u){for(;t<u;){var o=t+u>>>1;r(e[o])<n?t=o+1:u=o}return t},e}y.by=g;const b=x(c);b.by=x;var m=(r,e,n)=>{for(var t=0,u=e.length,o=n?JSON.parse(JSON.stringify(r)):new Array(u);t<u;++t)o[t]=r[e[t]];return o};var E={reduceIncrement:r=>r+1,reduceDecrement:r=>r-1,reduceAdd:r=>(function(e,n){return e+ +r(n)}),reduceSubtract:r=>(function(e,n){return e-r(n)})};const w=(r,e)=>{const n=r[e];return"function"==typeof n?n.call(r):n},A=/\[([\w\d]+)\]/g;var z=(r,e)=>(function(r,e,n,t,u){for(u in t=(n=n.split(".")).splice(-1,1),n)e=e[n[u]]=e[n[u]]||{};return r(e,t)})(w,r,e.replace(A,".$1")),k=-1;function O(){var r,e={add:a,remove:function(e){for(var o=new Array(t),i=[],a="function"==typeof e,l=0,s=0;l<t;++l)c=l,(a?e(n[c],c):r.zero(c))?(i.push(l),o[l]=k):o[l]=s++;var c;u.forEach((function(r){r(-1,-1,[],i,!0)})),f.forEach((function(r){r(o)}));for(var h=0,v=0;h<t;++h)o[h]!==k&&(h!==v&&(r.copy(v,h),n[v]=n[h]),++v);n.length=t=v,r.truncate(v),g("dataRemoved")},dimension:function(e,i){if("string"==typeof e){var a=e;e=function(r){return z(r,a)}}var p,x,w,A,O,F,N,R,U,D,I,L,W,J,j={filter:function(r){return null==r?er():Array.isArray(r)?rr(r):"function"==typeof r?nr(r):_(r)},filterExact:_,filterRange:rr,filterFunction:nr,filterAll:er,currentFilter:function(){return L},hasCurrentFilter:function(){return W},top:function(e,t){var u,o=[],f=P,a=0;t&&t>0&&(a=t);for(;--f>=K&&e>0;)r.zero(u=F[f])&&(a>0?--a:(o.push(n[u]),--e));if(i)for(f=0;f<$.length&&e>0;f++)r.zero(u=$[f])&&(a>0?--a:(o.push(n[u]),--e));return o},bottom:function(e,t){var u,o,f=[],a=0;t&&t>0&&(a=t);if(i)for(u=0;u<$.length&&e>0;u++)r.zero(o=$[u])&&(a>0?--a:(f.push(n[o]),--e));u=K;for(;u<P&&e>0;)r.zero(o=F[u])&&(a>0?--a:(f.push(n[o]),--e)),u++;return f},group:ur,groupAll:function(){var r=ur(h),e=r.all;return delete r.all,delete r.top,delete r.order,delete r.orderNatural,delete r.size,r.value=function(){return e()[0].value},r},dispose:or,remove:or,accessor:e,id:function(){return A}},$=[],q=function(r){return S(r).sort((function(r,e){var n=N[r],t=N[e];return n<t?-1:n>t?1:r-e}))},B=s.filterAll,G=[],H=[],K=0,P=0,Q=0;o.unshift(V),o.push(X),f.push(Y);var T=r.add();function V(n,u,o){var f,a;if(i){Q=0,G=0,J=[];for(var s=0;s<n.length;s++)for(G=0,J=e(n[s]);G<J.length;G++)Q++;N=[],f=S(n.length),a=M(Q,1);for(var c=S(Q),h=0,v=0;v<n.length;v++)if((J=e(n[v])).length)for(f[v]=J.length,G=0;G<J.length;G++)N.push(J[G]),c[h]=v,h++;else f[v]=0,$.push(v+u);var d=q(Q);N=m(N,d),R=m(c,d)}else N=n.map(e),R=q(o),N=m(N,R);var g,y,x,b=B(N),E=b[0],A=b[1];if(i)if(o=Q,I)for(g=0;g<o;++g)I(N[g],g)||(0==--f[R[g]]&&(r[w][R[g]+u]|=p),a[g]=1);else{for(y=0;y<E;++y)0==--f[R[y]]&&(r[w][R[y]+u]|=p),a[y]=1;for(x=A;x<o;++x)0==--f[R[x]]&&(r[w][R[x]+u]|=p),a[x]=1}else if(I)for(g=0;g<o;++g)I(N[g],g)||(r[w][R[g]+u]|=p);else{for(y=0;y<E;++y)r[w][R[y]+u]|=p;for(x=A;x<o;++x)r[w][R[x]+u]|=p}if(!u)return O=N,F=R,U=f,D=a,K=E,void(P=A);var z,k=O,C=F,L=D,W=0;if(s=0,i&&(z=u,u=k.length,o=Q),O=new Array(i?u+o:t),F=i?new Array(u+o):M(t,t),i&&(D=M(u+o,1)),i){var j=U.length;U=l.arrayLengthen(U,t);for(var G=0;G+j<t;G++)U[G+j]=f[G]}for(var H=0;s<u&&W<o;++H)k[s]<N[W]?(O[H]=k[s],i&&(D[H]=L[s]),F[H]=C[s++]):(O[H]=N[W],i&&(D[H]=a[W]),F[H]=R[W++]+(i?z:u));for(;s<u;++s,++H)O[H]=k[s],i&&(D[H]=L[s]),F[H]=C[s];for(;W<o;++W,++H)O[H]=N[W],i&&(D[H]=a[W]),F[H]=R[W]+(i?z:u);b=B(O),K=b[0],P=b[1]}function X(r,e,n){G.forEach((function(r){r(N,R,e,n)})),N=R=null}function Y(r){if(i){for(var e=0,n=0;e<$.length;e++)r[$[e]]!==k&&($[n]=r[$[e]],n++);for($.length=n,e=0,n=0;e<t;e++)r[e]!==k&&(n!==e&&(U[n]=U[e]),n++);U=U.slice(0,n)}for(var u,o=O.length,f=0,a=0;f<o;++f)r[u=F[f]]!==k&&(f!==a&&(O[a]=O[f]),F[a]=r[u],i&&(D[a]=D[f]),++a);for(O.length=a,i&&(D=D.slice(0,a));a<o;)F[a++]=0;var l=B(O);K=l[0],P=l[1]}function Z(e){var n=e[0],t=e[1];if(I)return I=null,tr((function(r,e){return n<=e&&e<t}),0===e[0]&&e[1]===O.length),K=n,P=t,j;var o,f,a,l=[],c=[],h=[],v=[];if(n<K)for(o=n,f=Math.min(K,t);o<f;++o)l.push(F[o]),h.push(o);else if(n>K)for(o=K,f=Math.min(n,P);o<f;++o)c.push(F[o]),v.push(o);if(t>P)for(o=Math.max(n,P),f=t;o<f;++o)l.push(F[o]),h.push(o);else if(t<P)for(o=Math.max(K,t),f=P;o<f;++o)c.push(F[o]),v.push(o);if(i){var d=[],y=[];for(o=0;o<l.length;o++)U[l[o]]++,D[h[o]]=0,1===U[l[o]]&&(r[w][l[o]]^=p,d.push(l[o]));for(o=0;o<c.length;o++)U[c[o]]--,D[v[o]]=1,0===U[c[o]]&&(r[w][c[o]]^=p,y.push(c[o]));if(l=d,c=y,B===s.filterAll)for(o=0;o<$.length;o++)r[w][a=$[o]]&p&&(r[w][a]^=p,l.push(a));else for(o=0;o<$.length;o++)r[w][a=$[o]]&p||(r[w][a]^=p,c.push(a))}else{for(o=0;o<l.length;o++)r[w][l[o]]^=p;for(o=0;o<c.length;o++)r[w][c[o]]^=p}return K=n,P=t,u.forEach((function(r){r(p,w,l,c)})),g("filtered"),j}function _(r){return L=r,W=!0,Z((B=s.filterExact(b,r))(O))}function rr(r){return L=r,W=!0,Z((B=s.filterRange(b,r))(O))}function er(){return L=void 0,W=!1,Z((B=s.filterAll)(O))}function nr(r){L=r,W=!0,I=r,B=s.filterAll,tr(r,!1);var e=B(O);return K=e[0],P=e[1],j}function tr(e,n){var t,o,f,a=[],l=[],s=[],c=[],h=O.length;if(!i)for(t=0;t<h;++t)!(r[w][o=F[t]]&p)^!!(f=e(O[t],t))&&(f?a.push(o):l.push(o));if(i)for(t=0;t<h;++t)e(O[t],t)?(a.push(F[t]),s.push(t)):(l.push(F[t]),c.push(t));if(i){var v=[],d=[];for(t=0;t<a.length;t++)1===D[s[t]]&&(U[a[t]]++,D[s[t]]=0,1===U[a[t]]&&(r[w][a[t]]^=p,v.push(a[t])));for(t=0;t<l.length;t++)0===D[c[t]]&&(U[l[t]]--,D[c[t]]=1,0===U[l[t]]&&(r[w][l[t]]^=p,d.push(l[t])));if(a=v,l=d,n)for(t=0;t<$.length;t++)r[w][o=$[t]]&p&&(r[w][o]^=p,a.push(o));else for(t=0;t<$.length;t++)r[w][o=$[t]]&p||(r[w][o]^=p,l.push(o))}else{for(t=0;t<a.length;t++)r[w][a[t]]&p&&(r[w][a[t]]&=x);for(t=0;t<l.length;t++)r[w][l[t]]&p||(r[w][l[t]]|=p)}u.forEach((function(r){r(p,w,a,l)})),g("filtered")}function ur(e){var o={top:function(r){var e=g(P(),0,a.length,r);return b.sort(e,0,e.length)},all:P,reduce:Q,reduceCount:T,reduceSum:function(r){return Q(E.reduceAdd(r),E.reduceSubtract(r),v)},order:V,orderNatural:function(){return V(c)},size:function(){return U},dispose:X,remove:X};H.push(o);var a,s,g,b,m,A,z,S,N=8,R=C(N),U=0,D=h,I=h,L=!0,W=e===h;function J(o,f,c,v){i&&(S=c,c=O.length-o.length,v=o.length);var p,d,g,y,b,E,k=a,F=i?[]:M(U,R),J=m,j=A,G=z,H=U,P=0,Q=0;for(L&&(J=G=h),L&&(j=G=h),a=new Array(U),U=0,s=i?H?s:[]:H>1?l.arrayLengthen(s,t):M(t,R),H&&(g=(d=k[0]).key);Q<v&&!((y=e(o[Q]))>=y);)++Q;for(;Q<v;){for(d&&g<=y?(b=d,E=g,F[P]=U,(d=k[++P])&&(g=d.key)):(b={key:y,value:G()},E=y),a[U]=b;y<=E&&(p=f[Q]+(i?S:c),i?s[p]?s[p].push(U):s[p]=[U]:s[p]=U,b.value=J(b.value,n[p],!0),r.zeroExcept(p,w,x)||(b.value=j(b.value,n[p],!1)),!(++Q>=v));)y=e(o[Q]);V()}for(;P<H;)a[F[P]=U]=k[P++],V();if(i)for(var T=0;T<t;T++)s[T]||(s[T]=[]);if(U>P)if(i)for(P=0;P<S;++P)for(T=0;T<s[P].length;T++)s[P][T]=F[s[P][T]];else for(P=0;P<c;++P)s[P]=F[s[P]];function V(){i?U++:++U===R&&(F=l.arrayWiden(F,N<<=1),s=l.arrayWiden(s,N),R=C(N))}p=u.indexOf(D),U>1||i?(D=$,I=B):(!U&&W&&(U=1,a=[{key:null,value:G()}]),1===U?(D=q,I=K):(D=h,I=h),s=null),u[p]=D}function j(r){if(U>1||i){var e,n,o,f=U,l=a,c=M(f,f);if(i){for(e=0,o=0;e<t;++e)if(r[e]!==k){for(s[o]=s[e],n=0;n<s[o].length;n++)c[s[o][n]]=1;++o}s=s.slice(0,o)}else for(e=0,o=0;e<t;++e)r[e]!==k&&(c[s[o]=s[e]]=1,++o);for(a=[],U=0,e=0;e<f;++e)c[e]&&(c[e]=U++,a.push(l[e]));if(U>1||i)if(i)for(e=0;e<o;++e)for(n=0;n<s[e].length;++n)s[e][n]=c[s[e][n]];else for(e=0;e<o;++e)s[e]=c[s[e]];else s=null;u[u.indexOf(D)]=U>1||i?(I=B,D=$):1===U?(I=K,D=q):I=D=h}else if(1===U){if(W)return;for(var v=0;v<t;++v)if(r[v]!==k)return;a=[],U=0,u[u.indexOf(D)]=D=I=h}}function $(e,t,u,o,f){var l,c,h,v,d;if(!(e===p&&t===w||L))if(i){for(l=0,v=u.length;l<v;++l)if(r.zeroExcept(h=u[l],w,x))for(c=0;c<s[h].length;c++)(d=a[s[h][c]]).value=m(d.value,n[h],!1,c);for(l=0,v=o.length;l<v;++l)if(r.onlyExcept(h=o[l],w,x,t,e))for(c=0;c<s[h].length;c++)(d=a[s[h][c]]).value=A(d.value,n[h],f,c)}else{for(l=0,v=u.length;l<v;++l)r.zeroExcept(h=u[l],w,x)&&((d=a[s[h]]).value=m(d.value,n[h],!1));for(l=0,v=o.length;l<v;++l)r.onlyExcept(h=o[l],w,x,t,e)&&((d=a[s[h]]).value=A(d.value,n[h],f))}}function q(e,t,u,o,f){if(!(e===p&&t===w||L)){var i,l,s,c=a[0];for(i=0,s=u.length;i<s;++i)r.zeroExcept(l=u[i],w,x)&&(c.value=m(c.value,n[l],!1));for(i=0,s=o.length;i<s;++i)r.onlyExcept(l=o[i],w,x,t,e)&&(c.value=A(c.value,n[l],f))}}function B(){var e,u,o;for(e=0;e<U;++e)a[e].value=z();if(i){for(e=0;e<t;++e)for(u=0;u<s[e].length;u++)(o=a[s[e][u]]).value=m(o.value,n[e],!0,u);for(e=0;e<t;++e)if(!r.zeroExcept(e,w,x))for(u=0;u<s[e].length;u++)(o=a[s[e][u]]).value=A(o.value,n[e],!1,u)}else{for(e=0;e<t;++e)(o=a[s[e]]).value=m(o.value,n[e],!0);for(e=0;e<t;++e)r.zeroExcept(e,w,x)||((o=a[s[e]]).value=A(o.value,n[e],!1))}}function K(){var e,u=a[0];for(u.value=z(),e=0;e<t;++e)u.value=m(u.value,n[e],!0);for(e=0;e<t;++e)r.zeroExcept(e,w,x)||(u.value=A(u.value,n[e],!1))}function P(){return L&&(I(),L=!1),a}function Q(r,e,n){return m=r,A=e,z=n,L=!0,o}function T(){return Q(E.reduceIncrement,E.reduceDecrement,v)}function V(r){function e(e){return r(e.value)}return g=y.by(e),b=d.by(e),o}function X(){var r=u.indexOf(D);return r>=0&&u.splice(r,1),(r=G.indexOf(J))>=0&&G.splice(r,1),(r=f.indexOf(j))>=0&&f.splice(r,1),(r=H.indexOf(o))>=0&&H.splice(r,1),o}return arguments.length<1&&(e=c),u.push(D),G.push(J),f.push(j),J(O,F,0,t),T().orderNatural()}function or(){H.forEach((function(r){r.dispose()}));var e=o.indexOf(V);return e>=0&&o.splice(e,1),(e=o.indexOf(X))>=0&&o.splice(e,1),(e=f.indexOf(Y))>=0&&f.splice(e,1),r.masks[w]&=x,er()}return w=T.offset,p=T.one,x=~p,A=w<<7|Math.log(p)/Math.log(2),V(n,0,t),X(n,0,t),j},groupAll:function(){var e,f,i,a,l={reduce:p,reduceCount:d,reduceSum:function(r){return p(E.reduceAdd(r),E.reduceSubtract(r),v)},value:function(){s&&(function(){var u;for(e=a(),u=0;u<t;++u)e=f(e,n[u],!0),r.zero(u)||(e=i(e,n[u],!1))}(),s=!1);return e},dispose:g,remove:g},s=!0;function c(u,o){var a;if(!s)for(a=o;a<t;++a)e=f(e,n[a],!0),r.zero(a)||(e=i(e,n[a],!1))}function h(t,u,o,a,l){var c,h,v;if(!s){for(c=0,v=o.length;c<v;++c)r.zero(h=o[c])&&(e=f(e,n[h],l));for(c=0,v=a.length;c<v;++c)r.only(h=a[c],u,t)&&(e=i(e,n[h],l))}}function p(r,e,n){return f=r,i=e,a=n,s=!0,l}function d(){return p(E.reduceIncrement,E.reduceDecrement,v)}function g(){var r=u.indexOf(h);return r>=0&&u.splice(r,1),(r=o.indexOf(c))>=0&&o.splice(r,1),l}return(u.push(h),o.push(c),c(n,0),d())},size:function(){return t},all:function(){return n},allFiltered:function(e){var u=[],o=0,f=p(e||[]);for(o=0;o<t;o++)r.zeroExceptMask(o,f)&&u.push(n[o]);return u},onChange:function(r){if("function"!=typeof r)return void console.warn("onChange callback parameter must be a function!");return i.push(r),function(){i.splice(i.indexOf(r),1)}},isElementFiltered:function(e,n){var t=p(n||[]);return r.zeroExceptMask(e,t)}},n=[],t=0,u=[],o=[],f=[],i=[];function a(u){var f=t,i=u.length;return i&&(n=n.concat(u),r.lengthen(t+=i),o.forEach((function(r){r(u,f,i)})),g("dataAdded")),e}function p(e){var n,t,u,o,f=Array(r.subarrays);for(n=0;n<r.subarrays;n++)f[n]=-1;for(t=0,u=e.length;t<u;t++)f[(o=e[t].id())>>7]&=~(1<<(63&o));return f}function g(r){for(var e=0;e<i.length;e++)i[e](r)}return r=new l.bitarray(0),arguments.length?a(arguments[0]):e}function M(r,e){return(e<257?l.array8:e<65537?l.array16:l.array32)(r)}function S(r){for(var e=M(r,r),n=-1;++n<r;)e[n]=n;return e}function C(r){return 8===r?256:16===r?65536:4294967296}O.heap=d,O.heapselect=y,O.bisect=b,O.permute=m;return O.version="1.5.4",O}));