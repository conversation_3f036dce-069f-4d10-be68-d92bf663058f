import { Node, mergeAttributes } from '@tiptap/core'
import { pricingTemplate } from '../templates/pricing.template'

export const PricingBlock = Node.create({
  name: 'pricingBlock',
  
  group: 'block',
  
  content: 'block*',
  
  draggable: true,

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="pricing"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const container = document.createElement('div')
    container.innerHTML = pricingTemplate

    // 合并任何自定义属性
    const mergedAttrs = mergeAttributes(HTMLAttributes, {
      'data-bs-component': 'pricing',
    })

    // 将模板内容包装在具有合并属性的div中
    const wrapper = document.createElement('div')
    Object.entries(mergedAttrs).forEach(([key, value]) => {
      wrapper.setAttribute(key, value as string)
    })
    wrapper.innerHTML = container.innerHTML

    return wrapper
  },

  addCommands() {
    return {
      insertPricingBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(pricingTemplate)
        },
    }
  },
}) 