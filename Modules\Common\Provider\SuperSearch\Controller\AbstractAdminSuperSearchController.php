<?php

namespace Modules\Common\Provider\SuperSearch\Controller;

use Bingo\Core\Input\InputPackage;
use Bingo\Core\Input\Request;
use Bingo\Core\Input\Response;
use Bingo\Exceptions\BizException;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Application;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\Common\Provider\SuperSearch\AbstractSuperSearchProvider;
use Modules\Common\Provider\SuperSearch\SuperSearchBiz;

abstract class AbstractAdminSuperSearchController extends Controller
{
    /**
     * @param AbstractSuperSearchProvider $provider
     * @param array $bizList
     * @return array|Factory|Application|View
     * @throws BizException
     */
    public function renderIndex(AbstractSuperSearchProvider $provider, array $bizList): Application|Factory|array|View
    {
        $bizList = array_map(function ($biz) {
            return SuperSearchBiz::get($biz);
        }, $bizList);
        $bizList = array_filter($bizList);
        if (Request::isPost()) {
            $input = InputPackage::buildFromInput();
            $bucket = $input->getTrimString('bucket');
            $action = $input->getTrimString('action');
            $biz = SuperSearchBiz::get($bucket);
            BizException::throwsIfEmpty($biz, \Bingo\Enums\Code::FAILED, 'Bucket错误');
            switch ($action) {
                case 'refresh':
                    return Response::generateSuccessData([
                        'count' => $provider->bucketCount($bucket),
                    ]);
                case 'sync':
                    $nextId = $input->getInteger('nextId');
                    if (0 === $nextId) {
                        $provider->bucketDelete($bucket);
                        $provider->bucketCreate($bucket, $biz->fields());
                    }
                    $ret = $biz->syncBatch($nextId);
                    $data = [];
                    $data['count'] = $ret['count'];
                    $data['nextId'] = $ret['nextId'];
                    return Response::generateSuccessData($data);
            }
        }
        return view('module::Common.View.superSearch.admin.index', [
            'provider' => $provider,
            'bizList' => $bizList,
        ]);
    }
}
