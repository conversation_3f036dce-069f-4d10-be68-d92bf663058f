# 查询作用域模板

## 概述

查询作用域允许我们定义通用的约束集合，以便在应用程序中重复使用。本文档提供了查询作用域的标准模板和最佳实践。

## 基本结构

### 本地作用域

```php
<?php

namespace Modules\YourModule\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class YourModel extends Model
{
    /**
     * 按状态查询
     *
     * @param Builder $query
     * @param int $status
     * @return Builder
     */
    public function scopeStatus(Builder $query, int $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 按类型查询
     *
     * @param Builder $query
     * @param string $type
     * @return Builder
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * 按日期范围查询
     *
     * @param Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return Builder
     */
    public function scopeDateBetween(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 按关键词搜索
     *
     * @param Builder $query
     * @param string $keyword
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $keyword): Builder
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
    }

    /**
     * 按多个字段排序
     *
     * @param Builder $query
     * @param array $sorts
     * @return Builder
     */
    public function scopeMultiSort(Builder $query, array $sorts): Builder
    {
        foreach ($sorts as $column => $direction) {
            $query->orderBy($column, $direction);
        }
        return $query;
    }

    /**
     * 包含软删除记录
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeWithTrashed(Builder $query): Builder
    {
        return $query->withTrashed();
    }

    /**
     * 按关联关系过滤
     *
     * @param Builder $query
     * @param int $relationId
     * @return Builder
     */
    public function scopeHasRelation(Builder $query, int $relationId): Builder
    {
        return $query->whereHas('relation', function ($q) use ($relationId) {
            $q->where('id', $relationId);
        });
    }
}
```

### 全局作用域

```php
<?php

namespace Modules\YourModule\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class ActiveScope implements Scope
{
    /**
     * 应用作用域
     *
     * @param Builder $builder
     * @param Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model): void
    {
        $builder->where('is_active', true);
    }
}

// 在模型中使用全局作用域
class YourModel extends Model
{
    protected static function booted(): void
    {
        static::addGlobalScope(new ActiveScope);
    }
}
```

## 使用示例

### 本地作用域使用

```php
// 单个作用域
$activeUsers = User::status(1)->get();

// 链式调用
$results = YourModel::status(1)
    ->ofType('premium')
    ->dateBetween('2024-01-01', '2024-12-31')
    ->search('keyword')
    ->get();

// 动态参数
$query = YourModel::query();
if ($status) {
    $query->status($status);
}
if ($type) {
    $query->ofType($type);
}
$results = $query->get();
```

### 全局作用域使��

```php
// 移除全局作用域
$allRecords = YourModel::withoutGlobalScope(ActiveScope::class)->get();

// 移除多个全局作用域
$records = YourModel::withoutGlobalScopes([
    ActiveScope::class,
    AnotherScope::class
])->get();

// 移除所有全局作用域
$allRecords = YourModel::withoutGlobalScopes()->get();
```

## 最佳实践

1. 命名规范
```php
// 好的实践 - 清晰的作用域名称
public function scopeActive($query)
{
    return $query->where('status', 1);
}

// 不好的实践 - 模糊的作用域名称
public function scopeFilter($query)
{
    return $query->where('status', 1);
}
```

2. 参数验证
```php
// 好的实践 - 参数验证
public function scopeDateRange($query, $start, $end)
{
    if (!$start || !$end) {
        return $query;
    }
    return $query->whereBetween('created_at', [$start, $end]);
}

// 不好的实践 - 没有参数验证
public function scopeDateRange($query, $start, $end)
{
    return $query->whereBetween('created_at', [$start, $end]);
}
```

## 常见问题

1. 作用域组合
```php
// 好的实践 - 合理组合作用域
public function scopePublished($query)
{
    return $query->where('status', 'published')
                 ->where('published_at', '<=', now());
}

// 不好的实践 - 重复的查询逻辑
public function scopePublishedStatus($query)
{
    return $query->where('status', 'published');
}
public function scopePublishedDate($query)
{
    return $query->where('published_at', '<=', now());
}
```

2. 作用域复用
```php
// 好的实践 - 作用域复用
public function scopeActive($query)
{
    return $query->where('status', 1);
}

public function scopePublished($query)
{
    return $query->active()->where('published_at', '<=', now());
}

// 不好的实践 - 重复的查询条件
public function scopePublished($query)
{
    return $query->where('status', 1)
                 ->where('published_at', '<=', now());
}
```

## 注意事项

1. 作用域命名要清晰
2. 参数类型要明确
3. 避免过度使用全局作用域
4. 注意作用域的组合使用
5. 合理处理参数验证
6. 考虑查询性能优化
