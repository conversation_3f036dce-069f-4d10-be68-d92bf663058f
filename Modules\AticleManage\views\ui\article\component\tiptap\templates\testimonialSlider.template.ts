export const testimonialSliderTemplate = `<div data-bs-component="testimonial-slider" class="testimonial-slider-block responsive-block">
  <div class="testimonial-slider-container">
    <div class="testimonial-slider">
      <button class="nav-button prev-button"><i class="fas fa-chevron-left"></i></button>
      <div class="testimonial-slide active">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5">
            <div class="testimonial-image">
              <img src="https://via.placeholder.com/500x600" alt="Product image" class="rounded img-fluid">
            </div>
          </div>
          <div class="col-12 col-md-7">
            <div class="text-center testimonial-content text-md-start">
              <div class="mb-4 quote-icon">
                <i class="fas fa-quote-left"></i>
              </div>
              <div class="testimonial-quote">
                <p class="quote-text">Add a testimonial quote #1 here. Keep it concise and impactful to enhance credibility with your business</p>
              </div>
              <div class="mt-4 testimonial-author">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="author-image">
                    <img src="https://via.placeholder.com/60x60" alt="Customer name one">
                  </div>
                  <div class="author-info ms-3">
                    <h4 class="author-name">Customer name one</h4>
                    <p class="author-role">Customer role one</p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-center testimonial-cta text-md-start">
                <a href="#" class="read-more-link">Read case study <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-slide">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5">
            <div class="testimonial-image">
              <img src="https://via.placeholder.com/500x600" alt="Product image" class="rounded img-fluid">
            </div>
          </div>
          <div class="col-12 col-md-7">
            <div class="text-center testimonial-content text-md-start">
              <div class="mb-4 quote-icon">
                <i class="fas fa-quote-left"></i>
              </div>
              <div class="testimonial-quote">
                <p class="quote-text">Add a testimonial quote #2 here. Keep it concise and impactful to enhance credibility with your business</p>
              </div>
              <div class="mt-4 testimonial-author">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="author-image">
                    <img src="https://via.placeholder.com/60x60" alt="Customer name two">
                  </div>
                  <div class="author-info ms-3">
                    <h4 class="author-name">Customer name two</h4>
                    <p class="author-role">Customer role two</p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-center testimonial-cta text-md-start">
                <a href="#" class="read-more-link">Read case study <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-slide">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5">
            <div class="testimonial-image">
              <img src="https://via.placeholder.com/500x600" alt="Product image" class="rounded img-fluid">
            </div>
          </div>
          <div class="col-12 col-md-7">
            <div class="text-center testimonial-content text-md-start">
              <div class="mb-4 quote-icon">
                <i class="fas fa-quote-left"></i>
              </div>
              <div class="testimonial-quote">
                <p class="quote-text">Add a testimonial quote #3 here. Keep it concise and impactful to enhance credibility with your business</p>
              </div>
              <div class="mt-4 testimonial-author">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="author-image">
                    <img src="https://via.placeholder.com/60x60" alt="Customer name three">
                  </div>
                  <div class="author-info ms-3">
                    <h4 class="author-name">Customer name three</h4>
                    <p class="author-role">Customer role three</p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-center testimonial-cta text-md-start">
                <a href="#" class="read-more-link">Read case study <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-slide">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5">
            <div class="testimonial-image">
              <img src="https://via.placeholder.com/500x600" alt="Product image" class="rounded img-fluid">
            </div>
          </div>
          <div class="col-12 col-md-7">
            <div class="text-center testimonial-content text-md-start">
              <div class="mb-4 quote-icon">
                <i class="fas fa-quote-left"></i>
              </div>
              <div class="testimonial-quote">
                <p class="quote-text">Add a testimonial quote #4 here. Keep it concise and impactful to enhance credibility with your business</p>
              </div>
              <div class="mt-4 testimonial-author">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="author-image">
                    <img src="https://via.placeholder.com/60x60" alt="Customer name four">
                  </div>
                  <div class="author-info ms-3">
                    <h4 class="author-name">Customer name four</h4>
                    <p class="author-role">Customer role four</p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-center testimonial-cta text-md-start">
                <a href="#" class="read-more-link">Read case study <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-slide">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5">
            <div class="testimonial-image">
              <img src="https://via.placeholder.com/500x600" alt="Product image" class="rounded img-fluid">
            </div>
          </div>
          <div class="col-12 col-md-7">
            <div class="text-center testimonial-content text-md-start">
              <div class="mb-4 quote-icon">
                <i class="fas fa-quote-left"></i>
              </div>
              <div class="testimonial-quote">
                <p class="quote-text">Add a testimonial quote #5 here. Keep it concise and impactful to enhance credibility with your business</p>
              </div>
              <div class="mt-4 testimonial-author">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="author-image">
                    <img src="https://via.placeholder.com/60x60" alt="Customer name five">
                  </div>
                  <div class="author-info ms-3">
                    <h4 class="author-name">Customer name five</h4>
                    <p class="author-role">Customer role five</p>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-center testimonial-cta text-md-start">
                <a href="#" class="read-more-link">Read case study <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button class="nav-button next-button"><i class="fas fa-chevron-right"></i></button>
    </div>
    <div class="slider-dots">
      <span class="dot active" data-slide="0"></span>
      <span class="dot" data-slide="1"></span>
      <span class="dot" data-slide="2"></span>
      <span class="dot" data-slide="3"></span>
      <span class="dot" data-slide="4"></span>
    </div>
  </div>
  
  <style>
  .testimonial-slider-block {
    position: relative;
    padding: 40px 0;
    width: 100%;
    background-color: #f8f9fa;
  }
  
  .testimonial-slider-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    overflow: hidden;
  }
  
  .testimonial-slider {
    position: relative;
    width: 100%;
    min-height: 400px;
    display: flex;
    align-items: center;
  }
  
  .testimonial-slide {
    display: none;
    width: 100%;
    padding: 30px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
  }
  
  .testimonial-slide.active {
    display: block;
  }
  
  .testimonial-image {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
  }
  
  .testimonial-image img {
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    transition: transform 0.3s ease;
  }
  
  .testimonial-content {
    display: flex;
    flex-direction: column;
    padding: 20px;
  }
  
  .quote-icon {
    font-size: 30px;
    color: #6c5ce7;
    opacity: 0.5;
  }
  
  .testimonial-quote {
    position: relative;
  }
  
  .quote-text {
    font-size: 22px;
    line-height: 1.5;
    color: #333;
    font-style: italic;
  }
  
  .testimonial-author {
    margin-top: 20px;
  }
  
  .author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  
  .author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .author-info {
    width: 100%;
  }
  
  .author-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .author-role {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
  
  .testimonial-cta {
    margin-top: 20px;
  }
  
  .read-more-link {
    display: inline-flex;
    align-items: center;
    color: #6c5ce7;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
  }
  
  .read-more-link:hover {
    color: #5649c0;
    transform: translateX(5px);
  }
  
  .read-more-link i {
    margin-left: 5px;
    font-size: 12px;
    transition: transform 0.3s ease;
  }
  
  .read-more-link:hover i {
    transform: translateX(3px);
  }
  
  .nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: #fff;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .prev-button {
    left: -20px;
  }
  
  .next-button {
    right: -20px;
  }
  
  .nav-button:hover {
    background-color: #6c5ce7;
    color: white;
    box-shadow: 0 4px 12px rgba(108,92,231,0.3);
    transform: translateY(-50%) scale(1.1);
  }
  
  .slider-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
  }
  
  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #d1d1d1;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .dot.active {
    background-color: #6c5ce7;
    transform: scale(1.2);
  }

  /* 移动端样式 */
  @media (max-width: 767.98px) {
    .testimonial-slider-block {
      padding: 20px 0;
    }

    .testimonial-slider-container {
      padding: 10px;
    }

    .testimonial-slide {
      padding: 15px;
    }

    .testimonial-content {
      padding: 15px 10px;
    }

    .quote-icon {
      font-size: 24px;
    }

    .quote-text {
      font-size: 18px;
      line-height: 1.4;
    }

    .author-image {
      width: 50px;
      height: 50px;
    }

    .author-name {
      font-size: 16px;
    }

    .author-role {
      font-size: 12px;
    }

    .nav-button {
      width: 36px;
      height: 36px;
      background-color: rgba(255,255,255,0.9);
    }

    .prev-button {
      left: 5px;
    }

    .next-button {
      right: 5px;
    }
  }

  /* 平板端样式 */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .testimonial-slider-block {
      padding: 30px 0;
    }

    .testimonial-slide {
      padding: 20px;
    }

    .quote-text {
      font-size: 20px;
    }
  }

  /* 桌面端样式 */
  @media (min-width: 992px) {
    .testimonial-slider-block {
      padding: 40px 0;
    }

    .testimonial-slide {
      padding: 30px;
    }

    .testimonial-image img:hover {
      transform: scale(1.02);
    }
  }

  /* 移动端预览模式样式 */
  .mobile-preview .testimonial-slider-block {
    padding: 20px 0;
  }

  .mobile-preview [class*="col-"] {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }

  .mobile-preview .testimonial-slide {
    padding: 15px;
  }

  .mobile-preview .testimonial-content {
    padding: 15px 10px;
    text-align: center;
  }

  .mobile-preview .quote-text {
    font-size: 18px;
  }

  .mobile-preview .testimonial-author {
    justify-content: center;
  }

  .mobile-preview .nav-button {
    width: 36px;
    height: 36px;
  }

  .mobile-preview .prev-button {
    left: 5px;
  }

  .mobile-preview .next-button {
    right: 5px;
  }

  /* 桌面预览模式样式 */
  .desktop-preview .testimonial-slider-block {
    padding: 40px 0;
  }

  .desktop-preview .testimonial-slide {
    padding: 30px;
  }

  .desktop-preview .testimonial-content {
    text-align: left;
  }

  .desktop-preview .testimonial-author {
    justify-content: flex-start;
  }

  /* 容器大小响应式 */
  .container-sm .testimonial-slider-container {
    max-width: 540px;
  }

  .container-md .testimonial-slider-container {
    max-width: 720px;
  }

  .container-lg .testimonial-slider-container {
    max-width: 960px;
  }

  .container-xl .testimonial-slider-container {
    max-width: 1140px;
  }

  .container-xxl .testimonial-slider-container {
    max-width: 1320px;
  }
  </style>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <script>
  document.addEventListener("DOMContentLoaded", function() {
    const slider = document.querySelector('.testimonial-slider');
    const slides = document.querySelectorAll('.testimonial-slide');
    const dots = document.querySelectorAll('.dot');
    const prevButton = document.querySelector('.prev-button');
    const nextButton = document.querySelector('.next-button');
    let currentSlide = 0;
    
    function showSlide(index) {
      slides.forEach(slide => slide.classList.remove('active'));
      dots.forEach(dot => dot.classList.remove('active'));
      
      slides[index].classList.add('active');
      dots[index].classList.add('active');
      currentSlide = index;
    }
    
    function nextSlide() {
      currentSlide = (currentSlide + 1) % slides.length;
      showSlide(currentSlide);
    }
    
    function prevSlide() {
      currentSlide = (currentSlide - 1 + slides.length) % slides.length;
      showSlide(currentSlide);
    }
    
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => showSlide(index));
    });
    
    prevButton.addEventListener('click', prevSlide);
    nextButton.addEventListener('click', nextSlide);
    
    const autoSlide = setInterval(nextSlide, 5000);
    
    [prevButton, nextButton, ...dots].forEach(el => {
      el.addEventListener('click', () => {
        clearInterval(autoSlide);
      });
    });
  });
  </script>
</div>`; 