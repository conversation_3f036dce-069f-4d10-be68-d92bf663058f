<template>
     <div v-if="!isOpen" @click="toggleChat" class="chat-bot-trigger">
      <img :src="$asset('Dashboard/Asset/chat.png')" alt="">
    </div>

  <div class="chat-bot-container">
    <!-- 聊天图标按钮 -->
   
    <!-- 聊天窗口 -->
    <Transition name="slide-fade">
      <div v-if="isOpen" class="chat-window-wrapper">
        <div class="chat-bot-window">
          <!-- 聊天窗口头部 -->
          <div class="chat-header">
            <div class="header-left">
              <img :src="$asset('Dashboard/Asset/chatbot.png')" class="bot-avata-head" alt="ChatBot">
              <span class="bot-name">CMS智能助手</span>
            </div>
           
          </div>

          <!-- 聊天消息区域 -->
          <div class="chat-messages" ref="messageContainer">
            <div v-for="message in chatMessages" 
                 :key="message.id" 
                 :class="['message', message.type]">
              <div class="message-avatar bot-avatar" v-if="message.type === 'bot'">
                <img :src="$asset('Dashboard/Asset/Jacky.png')" alt="Bot Avatar">
              </div>
              
              <!-- 使用条件渲染替代动态组件 -->
              <template v-if="message.messageType === MessageType.TEXT">
                <MessageText :message="message" />
              </template>

              <template v-else-if="message.messageType === MessageType.QUICK_REPLIES">
                <MessageQuickReplies 
                  :message="message"
                  @select="handleQuickReply"
                />
              </template>

              <template v-else-if="message.messageType === MessageType.IMAGE">
                <MessageImage 
                  :message="message"
                  @action="handleMessageAction"
                />
              </template>

              <template v-else-if="message.messageType === MessageType.CARD">
                <MessageCard 
                  :message="message"
                  @action="handleMessageAction"
                />
              </template>

              <template v-else-if="message.messageType === MessageType.FILE">
                <MessageFile 
                  :message="message"
                  @action="handleMessageAction"
                />
              </template>

              <template v-else-if="message.messageType === MessageType.INPUT_FIELD">
                <MessageInputField 
                  :message="message"
                  @submit="handleInputSubmit"
                  @cancel="handleInputCancel"
                  @quickReply="handleQuickReply"
                />
              </template>
            </div>

            <!-- 简单加载提示 -->
            <div v-if="isLoading" class="message bot">
              <div class="message-avatar bot-avatar">
                <img :src="$asset('Dashboard/Asset/Jacky.png')" alt="Bot Avatar">
              </div>
              <div class="message-content">
                <div class="message-body">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入框区域 -->
          <div class="chat-input">
            <el-input
              v-model="inputMessage"
              :placeholder="inputPlaceholder"
              :maxlength="150"
              show-word-limit
              @keyup.enter="sendMessage"
              :disabled="isLoading"
            >
            </el-input>
            <div class="send-button" @click="sendMessage" :class="{ disabled: isLoading }">
                  <img style="width: 25px !important; height: 20px !important;" :src="$asset('Dashboard/Asset/Group 5990.png')" alt="">
                </div>
          </div>
        </div>
        <!-- 右下角关闭按钮 -->
        <div class="close-button-wrapper">
          <el-button 
            class="close-button" 
            @click="closeChat"
            circle
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Close } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import http from '/admin/support/http'
import { MessageType, type ChatMessage, type QuickReply } from './chat/types'
import { ChatActions } from './chat/constants/ChatActions'
import MessageText from './chat/MessageText.vue'
import MessageQuickReplies from './chat/MessageQuickReplies.vue'
import MessageImage from './chat/MessageImage.vue'
import MessageCard from './chat/MessageCard.vue'
import MessageFile from './chat/MessageFile.vue'
import MessageInputField from './chat/MessageInputField.vue'

// 基础状态管理
const isOpen = ref(false)
const inputMessage = ref('')
const inputPlaceholder = ref('请输入您的问题...')
const isLoading = ref(false)
const chatMessages = ref<ChatMessage[]>([])
const messageContainer = ref<HTMLElement | null>(null)

// 会话管理
const sessionId = ref<string>('')
const chatContext = ref<any>({})

// 工具状态管理
const currentTool = ref<string>('')
const hasExitedTool = ref<boolean>(false)

// 进度条管理
const activeProgressId = ref<string>('')
const progressTimer = ref<any>(null)

// 生成会话ID
const generateSessionId = (): string => {
  return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 发送消息到后端
const sendChatMessage = async (content: string, action?: string, payload?: any): Promise<any> => {
  try {
    // 确保content始终是字符串
    const messageContent = content || ''
    
    const requestData = {
      messages: [
        {
          role: 'user',
          content: messageContent,
          timestamp: Date.now()
        }
      ],
      sessionId: sessionId.value,
      action: action || ChatActions.TEXT_MESSAGE,
      payload: payload || {},
      context: chatContext.value
    }

    const response = await http.post('/ai/chat', requestData)
    
    // 获取实际的聊天数据
    const chatData = response.data.data
    
    // 更新会话上下文
    if (chatData.context) {
      chatContext.value = chatData.context
    }
    
    // 更新会话ID
    if (chatData.sessionId) {
      sessionId.value = chatData.sessionId
    }
    
    // 更新输入框提示
    if (chatData.input_placeholder) {
      inputPlaceholder.value = chatData.input_placeholder
    }
    
    return chatData
  } catch (error) {
    console.error('发送消息失败:', error)
    throw error
  }
}

// 处理快捷回复
const handleQuickReply = async (reply: QuickReply) => {
  if (isLoading.value) return
  
  console.log('点击快捷回复:', reply)
  
  // 添加用户选择的回复
  chatMessages.value.push({
    id: Date.now().toString(),
    type: 'user',
    time: dayjs().format('HH:mm'),
    messageType: MessageType.TEXT,
    content: reply.text
  })
  
  isLoading.value = true
  
  // 显示加载动画后立即滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  try {
    // 简化逻辑：根据reply.action确定当前工具
    let toolAction: string
    
    // 根据具体操作判断当前所在的工具
    if ([
      ChatActions.SELECT_TEXT_REPLACER
    ].includes(reply.action as any)) {
      toolAction = ChatActions.SELECT_TEXT_REPLACER
      currentTool.value = ChatActions.SELECT_TEXT_REPLACER
      hasExitedTool.value = false
    } else if ([
      ChatActions.SELECT_SEO_ANALYZER
    ].includes(reply.action as any)) {
      toolAction = ChatActions.SELECT_SEO_ANALYZER
      currentTool.value = ChatActions.SELECT_SEO_ANALYZER
      hasExitedTool.value = false
    } else if ([
      ChatActions.SELECT_ARTICLE_REPLACER,
      ChatActions.QUICK_REPLACE,
      ChatActions.BATCH_REPLACE_ALL,
      ChatActions.SELECT_INDIVIDUAL_ARTICLES,
      ChatActions.BACK_TO_SEARCH,
      ChatActions.CONFIRM_EXECUTE,
      ChatActions.CANCEL_OPERATION,
      ChatActions.SELECT_ARTICLE,
      ChatActions.SHOW_MORE_ARTICLES,
      ChatActions.SEARCH_SPECIFIC_ARTICLE,
      ChatActions.BACK_TO_ARTICLE_LIST,
      'NEW_REPLACEMENT' // 添加新的替换操作
    ].includes(reply.action as any)) {
      toolAction = ChatActions.SELECT_ARTICLE_REPLACER
      currentTool.value = ChatActions.SELECT_ARTICLE_REPLACER
      hasExitedTool.value = false
    } else if (reply.action === ChatActions.EXIT_TOOL) {
      // 退出工具，清除工具状态
      currentTool.value = ''
      chatContext.value = {}
      hasExitedTool.value = true
      
      // 清空聊天记录，确保不会从历史消息中重新检测工具状态
      chatMessages.value = []
      
      // 重新初始化聊天
      initChat()
      return
    } else {
      // 默认使用快捷回复
      toolAction = ChatActions.QUICK_REPLY
    }
    
    console.log('发送请求到后端:', {
      action: toolAction,
      reply_action: reply.action,
      reply_text: reply.text,
      reply_data: (reply as any).data || {}
    })
    
    // 发送快捷回复到后端，使用明确的action类型
    // 将快捷回复的文本内容作为消息内容发送，保持前后端一致性
    const response = await sendChatMessage(reply.text, toolAction, {
      reply_id: reply.id,
      reply_action: reply.action,
      reply_text: reply.text,
      reply_data: (reply as any).data || {}
    })
    
    console.log('后端响应:', response)
    
    // 处理后端响应
    handleBotResponse(response)
  } catch (error) {
    showErrorMessage('处理您的请求时发生错误，请稍后重试')
  } finally {
    isLoading.value = false
  }
  
  scrollToBottom()
}

// 判断当前所在的工具
const getCurrentTool = (): string => {
  // 如果明确退出了工具，直接返回TEXT_MESSAGE
  if (hasExitedTool.value) {
    return ChatActions.TEXT_MESSAGE
  }
  
  // 优先使用当前工具状态
  if (currentTool.value) {
    return currentTool.value
  }
  
  // 检查上下文中的工具信息
  if (chatContext.value.currentTool) {
    currentTool.value = chatContext.value.currentTool
    return chatContext.value.currentTool
  }
  
  // 通过最近的消息判断当前工具（仅在未明确退出工具时）
  const recentBotMessages = chatMessages.value
    .filter(msg => msg.type === 'bot')
    .slice(-3) // 检查最近3条机器人消息
  
  for (const message of recentBotMessages.reverse()) {
    if ((message as any).quickReplies) {
      const quickReplies = (message as any).quickReplies
      for (const reply of quickReplies) {
        // 根据快捷回复的action判断当前工具
        if ([
          ChatActions.SELECT_ARTICLE_REPLACER,
          ChatActions.QUICK_REPLACE,
          ChatActions.BATCH_REPLACE_ALL,
          ChatActions.SELECT_INDIVIDUAL_ARTICLES,
          ChatActions.BACK_TO_SEARCH,
          ChatActions.CONFIRM_EXECUTE,
          ChatActions.CANCEL_OPERATION,
          ChatActions.SELECT_ARTICLE,
          ChatActions.SHOW_MORE_ARTICLES,
          ChatActions.SEARCH_SPECIFIC_ARTICLE,
          ChatActions.BACK_TO_ARTICLE_LIST,
          'NEW_REPLACEMENT'
        ].includes(reply.action)) {
          currentTool.value = ChatActions.SELECT_ARTICLE_REPLACER
          return ChatActions.SELECT_ARTICLE_REPLACER
        } else if ([ChatActions.SELECT_TEXT_REPLACER].includes(reply.action)) {
          currentTool.value = ChatActions.SELECT_TEXT_REPLACER
          return ChatActions.SELECT_TEXT_REPLACER
        } else if ([ChatActions.SELECT_SEO_ANALYZER].includes(reply.action)) {
          currentTool.value = ChatActions.SELECT_SEO_ANALYZER
          return ChatActions.SELECT_SEO_ANALYZER
        }
      }
    }
  }
  
  // 默认返回文本消息
  return ChatActions.TEXT_MESSAGE
}

// 判断当前的输入动作
const getCurrentAction = (): string => {
  // 检查最后一条机器人消息是否有输入框
  const lastBotMessage = chatMessages.value
    .slice()
    .reverse()
    .find(msg => msg.type === 'bot')
  
  if (lastBotMessage && (lastBotMessage as any).inputField) {
    // 如果有输入框，说明是在搜索或输入特定内容
    const inputField = (lastBotMessage as any).inputField
    if (inputField.type === 'text') {
      // 通过placeholder或其他属性判断具体的输入类型
      if (inputField.placeholder && inputField.placeholder.includes('搜索')) {
        return 'EXECUTE_SEARCH'
      }
    }
  }
  
  // 检查是否在工具上下文中
  if (chatContext.value.searchContext) {
    return 'EXECUTE_SEARCH'
  }
  
  // 默认是文本消息
  return ChatActions.TEXT_MESSAGE
}

// 发送文本消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return
  
  const message = inputMessage.value.trim()
  inputMessage.value = ''
  
  // 添加用户消息
  chatMessages.value.push({
    id: Date.now().toString(),
    type: 'user',
    time: dayjs().format('HH:mm'),
    messageType: MessageType.TEXT,
    content: message
  })
  
  isLoading.value = true
  
  // 显示加载动画后立即滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  try {
    // 判断当前所在的工具和上下文
    const toolAction = getCurrentTool()
    const currentAction = getCurrentAction()
    
    console.log('发送文本消息:', {
      message,
      currentTool: toolAction,
      currentAction,
      context: chatContext.value
    })
    
    // 检查是否选择了工具
    if (toolAction === ChatActions.TEXT_MESSAGE && !chatContext.value.currentTool) {
      // 没有选择工具，提示用户先选择工具
      showErrorMessage('请先选择要使用的工具，然后再发送消息')
      return
    }
    
    // 发送消息到后端，使用正确的工具作为action
    const response = await sendChatMessage(message, toolAction, {
      reply_action: currentAction,
      reply_text: message,
      reply_data: {}
    })
    
    // 处理后端响应
    handleBotResponse(response)
  } catch (error) {
    showErrorMessage('发送消息失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
  
  scrollToBottom()
}

// 处理机器人响应
const handleBotResponse = (response: any) => {
  if (!response) return
  
  // 检查是否包含start_replacement命令
  const hasStartReplacementCommand = response.commands && 
    response.commands.some((cmd: any) => cmd.type === 'start_replacement')
  
  // 如果包含start_replacement命令，跳过content处理，只处理commands
  if (!hasStartReplacementCommand) {
    // 处理单个消息
    if (response.content && response.content.trim()) {
      // 检查是否有输入框配置
      const hasInputField = response.inputField && response.inputField.type
      // 检查是否有快捷操作（后端返回的是quickActions）
      const hasQuickActions = response.quickActions && response.quickActions.length > 0
      
      let messageType: MessageType
      if (hasInputField) {
        messageType = MessageType.INPUT_FIELD
      } else if (hasQuickActions) {
        messageType = MessageType.QUICK_REPLIES
      } else {
        messageType = MessageType.TEXT
      }
      
      const message: any = {
        id: Date.now().toString(),
        type: 'bot',
        time: dayjs().format('HH:mm'),
        messageType: messageType,
        content: response.content,
        quickReplies: response.quickActions || [] // 使用quickActions而不是quick_replies
      }
      
      // 如果有输入框配置，添加到消息中
      if (hasInputField) {
        message.inputField = response.inputField
      }
      
      chatMessages.value.push(message)
    }
    
    // 处理多个消息
    if (response.messages && Array.isArray(response.messages)) {
      response.messages.forEach((msg: any, index: number) => {
        // 只处理有内容的消息
        if (!msg.content || !msg.content.trim()) return
        
        setTimeout(() => {
          const hasQuickActions = msg.quickActions && msg.quickActions.length > 0
          const messageType = hasQuickActions ? MessageType.QUICK_REPLIES : MessageType.TEXT
          
          chatMessages.value.push({
            id: Date.now().toString() + '_' + index,
            type: 'bot',
            time: dayjs().format('HH:mm'),
            messageType: messageType,
            content: msg.content,
            quickReplies: msg.quickActions || [] // 使用quickActions而不是quick_replies
          })
          
          scrollToBottom()
        }, index * 500) // 逐个显示消息
      })
    }
  }
  
  // 处理特殊指令
  if (response.commands) {
    response.commands.forEach((command: any) => {
      switch (command.type) {
        case 'start_replacement':
          // 防止重复创建进度条
          if (!activeProgressId.value) {
            handleReplacementProgress(command.data)
          }
          break
        case 'update_progress':
          // 这个命令现在不需要了，因为我们直接更新消息内容
          break
        case 'scroll_to_bottom':
          scrollToBottom()
          break
        case 'clear_input':
          inputMessage.value = ''
          break
        // 可以添加更多指令类型
      }
    })
  }
  
  // 更新上下文信息
  if (response.context) {
    chatContext.value = response.context
    // 如果上下文中包含工具信息，同步更新当前工具状态
    if (response.context.currentTool) {
      currentTool.value = response.context.currentTool
    }
  }
  
  // 检查是否需要清除工具状态
  if (response.releaseControl) {
    currentTool.value = ''
    chatContext.value = {}
    hasExitedTool.value = true
  }
  
  // 更新输入框提示
  if (response.input_placeholder) {
    inputPlaceholder.value = response.input_placeholder
  }
  
  // 检查是否在等待输入状态
  if (response.toolData && response.toolData.waitingForInput) {
    inputPlaceholder.value = '请输入文章标题关键词或文章ID...'
  }
  
  // 确保滚动到底部
  scrollToBottom()
}

// 处理替换进度
const handleReplacementProgress = async (data: any) => {
  const { articleIds, sourceText, targetText, replaceType, totalArticles } = data
  const isSingleReplace = replaceType === 'single'
  
  // 清除之前的进度条定时器
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
  
  // 先添加进度条消息
  const progressMessageId = Date.now().toString()
  activeProgressId.value = progressMessageId
  
  const progressMessage: ChatMessage = {
    id: progressMessageId,
    type: 'bot',
    time: dayjs().format('HH:mm'),
    messageType: MessageType.TEXT,
    content: '', // 初始为空，后面会更新
    progressData: {
      percentage: 0,
      text: '开始处理...',
      isActive: true,
      isSingleReplace
    }
  } as any
  
  chatMessages.value.push(progressMessage)
  scrollToBottom()
  
  // 模拟进度更新
  const updateInterval = isSingleReplace ? 150 : 300
  let currentProgress = 0
  
  progressTimer.value = setInterval(() => {
    currentProgress += isSingleReplace ? 8 : 4
    
    if (currentProgress >= 100) {
      currentProgress = 100
      clearInterval(progressTimer.value!)
      progressTimer.value = null
      
      // 最终状态
      updateProgressInMessage(progressMessageId, {
        percentage: 100,
        text: isSingleReplace ? 
          '✅ 替换完成！' : 
          `✅ 已完成: ${totalArticles}/${totalArticles} 篇文章`,
        isActive: false
      })
      
      // 进度完成后，获取最终结果
      setTimeout(() => {
        fetchReplacementResult(articleIds, sourceText, targetText, replaceType)
        // 清除活动进度条ID
        activeProgressId.value = ''
      }, 800)
    } else {
      // 更新进度条
      updateProgressInMessage(progressMessageId, {
        percentage: currentProgress,
        text: isSingleReplace ? 
          `正在替换... ${currentProgress}%` : 
          `已处理: ${Math.floor(currentProgress / 100 * totalArticles)}/${totalArticles} 篇文章`,
        isActive: true
      })
    }
  }, updateInterval)
}

// 获取替换结果
const fetchReplacementResult = async (articleIds: any[], sourceText: string, targetText: string, replaceType: string) => {
  try {
    // 模拟获取最终结果
    const requestData = {
      messages: [
        {
          role: 'user',
          content: 'get_replacement_result',
          timestamp: Date.now()
        }
      ],
      sessionId: sessionId.value,
      action: 'GET_REPLACEMENT_RESULT',
      payload: {
        articleIds,
        sourceText,
        targetText,
        replaceType
      },
      context: chatContext.value
    }
    
    const response = await http.post('/ai/chat', requestData)
    const chatData = response.data.data
    
    // 添加新的结果消息，而不是替换进度条消息
    const hasQuickActions = chatData.quickActions && chatData.quickActions.length > 0
    const messageType = hasQuickActions ? MessageType.QUICK_REPLIES : MessageType.TEXT
    
    chatMessages.value.push({
      id: Date.now().toString(),
      type: 'bot',
      time: dayjs().format('HH:mm'),
      messageType: messageType,
      content: chatData.content,
      quickReplies: chatData.quickActions || []
    } as any)
    
    scrollToBottom()
  } catch (error) {
    console.error('获取替换结果失败:', error)
    // 添加错误消息
    chatMessages.value.push({
      id: Date.now().toString(),
      type: 'bot',
      time: dayjs().format('HH:mm'),
      messageType: MessageType.TEXT,
      content: '❌ 获取替换结果失败，请稍后重试'
    })
    scrollToBottom()
  }
}

// 更新进度条 - 在指定消息中更新进度
const updateProgressInMessage = (messageId: string, data: any) => {
  const messageIndex = chatMessages.value.findIndex(msg => msg.id === messageId)
  if (messageIndex === -1) return
  
  const message = chatMessages.value[messageIndex] as any
  if (!message.progressData) return
  
  // 更新进度数据
  message.progressData.percentage = data.percentage
  message.progressData.text = data.text
  message.progressData.isActive = data.isActive ?? true
  
  // 强制响应式更新
  chatMessages.value[messageIndex] = { ...message }
  
  nextTick(() => {
    scrollToBottom()
  })
}

// 显示错误消息
const showErrorMessage = (message: string) => {
  chatMessages.value.push({
    id: Date.now().toString(),
    type: 'bot',
    time: dayjs().format('HH:mm'),
    messageType: MessageType.TEXT,
    content: `❌ ${message}`
  })
  scrollToBottom()
}

// 处理其他消息动作
const handleMessageAction = (action: string, payload?: any) => {
  switch (action) {
    case 'OPEN_IMAGE':
      // 处理图片预览
      break
    case 'DOWNLOAD_FILE':
      // 处理文件下载
      break
    case 'CARD_ACTION':
      // 处理卡片动作
      break
    default:
      console.log('Unknown action:', action, payload)
  }
}

// 处理输入框提交
const handleInputSubmit = async (value: string) => {
  if (!value.trim()) return
  
  // 添加用户输入的消息
  chatMessages.value.push({
    id: Date.now().toString(),
    type: 'user',
    time: dayjs().format('HH:mm'),
    messageType: MessageType.TEXT,
    content: value
  })
  
  isLoading.value = true
  
  // 显示加载动画后立即滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  try {
    // 使用统一的工具和动作判断逻辑
    const toolAction = getCurrentTool()
    const currentAction = getCurrentAction()
    
    console.log('处理输入框提交:', {
      value,
      currentTool: toolAction,
      currentAction,
      context: chatContext.value
    })
    
    // 检查是否选择了工具
    if (toolAction === ChatActions.TEXT_MESSAGE && !chatContext.value.currentTool) {
      // 没有选择工具，提示用户先选择工具
      showErrorMessage('请先选择要使用的工具，然后再发送消息')
      return
    }
    
    // 发送消息到后端，使用正确的工具作为action
    const response = await sendChatMessage(value, toolAction, {
      reply_action: currentAction,
      reply_text: value,
      reply_data: currentAction === 'EXECUTE_SEARCH' ? { searchKeyword: value } : {}
    })
    
    handleBotResponse(response)
  } catch (error) {
    showErrorMessage('发送消息失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
  
  scrollToBottom()
}

// 处理输入框取消
const handleInputCancel = () => {
  // 可以在这里处理取消逻辑，比如发送取消操作到后端
  console.log('输入框取消操作')
}

// 初始化聊天
const initChat = async () => {
  sessionId.value = generateSessionId()
  isLoading.value = true
  
  // 显示加载动画后立即滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  try {
    // 发送初始化消息，明确传入空字符串
    const response = await sendChatMessage('', ChatActions.INIT_CHAT)
    handleBotResponse(response)
  } catch (error) {
    console.error('初始化聊天失败:', error)
    showErrorMessage('初始化聊天失败，请刷新页面重试')
  } finally {
    isLoading.value = false
  }
}

// 切换聊天窗口
const toggleChat = () => {
  isOpen.value = !isOpen.value
  
  // 每次打开时都清空聊天记录并重新初始化
  if (isOpen.value) {
    // 清空聊天记录
    chatMessages.value = []
    // 重置会话状态
    sessionId.value = ''
    chatContext.value = {}
    currentTool.value = ''
    hasExitedTool.value = false
    // 重新初始化聊天
    initChat()
  }
}

// 关闭聊天窗口
const closeChat = () => {
  isOpen.value = false
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

// 组件挂载
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
})
</script>

<style scoped lang="scss">
.chat-bot-container {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 1000;
}

.chat-bot-trigger {
  cursor: pointer;
  position: fixed;
  right: 15px;
  bottom: 50px;
  img{
    width: 40px;
    height: 40px;
    
  }
}

.chat-window-wrapper {
  position: relative;
}

.chat-bot-window {
  position: relative;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 15px;
  background: linear-gradient(to right, #6BBAD2, #559CD7);
  color: white;
  height: 49px;
  width: 96%;
  border-radius: 10px;
  margin: 10px auto 0;
  display: flex;
 
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
  .bot-avata-head{
    width: 42px;
  height: 63px;

  }
}

.bot-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-top: 20px;
}

.bot-name {
  font-weight: 500;
}

.chat-messages {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
  background:white;
}

.message {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 0 10px;
}

.message-avatar {
  width: 30px;
  height: 30px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message.bot .message-body {
  background: #e8f4ff;
  border-radius: 0 12px 12px 12px;
}

.message.user {
  flex-direction: row-reverse;
}

.message.user .message-body {
  background: #007bff;
  color: white;
  border-radius: 12px 0 12px 12px;
}

/* 快捷回复样式由子组件MessageQuickReplies控制 */

.chat-input {
  padding: 15px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .send-button{
    margin-left: 10px;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: #EBFCFF;
    padding: 5px 8px;
    color: #67B1E7;
    font-size: 18px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.3s ease;
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

:deep(.el-input-group__append) {
  padding: 0;
  width: 30px;
}

:deep(.el-input__wrapper) {
  padding-right: 8px;
}

/* 底部关闭按钮样式 */
.close-button-wrapper {
  position: absolute;
  bottom: -45px;
  right: 0px;
  z-index: 2;
}

.close-button {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button :deep(.el-icon) {
  font-size: 20px;
  color: #2B4B6B;
}

/* 动画效果 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* 优化滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-track {
  background-color: transparent;
}

  .user-avatar {
    margin-top: 4px;
  }

  /* 简单加载动画 */
  .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 8px 0;
  }

  .typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #6BBAD2;
    animation: typing 1.4s infinite ease-in-out;
  }

  .typing-dots span:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.4;
    }
    30% {
      transform: translateY(-10px);
      opacity: 1;
    }
  }

  /* 响应式样式调整 */
@media screen and (min-width: 1680px) {
  .chat-bot-window {
    width: 420px;  // 增加窗口宽度
    height: 600px; // 增加窗口高度
  }

  .chat-header {
    padding: 20px;
    height: 60px;

    .bot-avata-head {
      width: 35px;
      height: 35px;
    }

    .bot-name {
      font-size: 18px;
    }
  }

  .message {
    margin-bottom: 25px;
    padding: 0 15px;
  }

  .message-avatar {
    width: 35px;
    height: 35px;
  }

  .message-content {
    max-width: 75%;
  }

  :deep(.message-body) {
    font-size: 16px;
    padding: 12px 16px;
  }

  /* 快捷回复响应式样式由子组件控制 */

  .chat-input {
    padding: 20px;

    :deep(.el-input__wrapper) {
      padding: 4px 15px;
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      height: 42px;
    }

    .el-button {
      width: 45px;
      height: 45px;
      font-size: 20px;
    }
  }

  .close-button-wrapper {
    bottom: -50px;

    .close-button {
      width: 45px;
      height: 45px;
    }
  }

  .chat-bot-trigger {
    .el-button {
      width: 50px;
      height: 50px;
      
      :deep(.el-icon) {
        font-size: 24px;
      }
    }
  }

  /* 时间戳样式 */
  :deep(.message-time) {
    font-size: 13px;
  }

  /* 滚动条调整 */
  .chat-messages::-webkit-scrollbar {
    width: 8px;
  }
}

/* 更大屏幕的适配 */
@media screen and (min-width: 1920px) {
  .chat-bot-window {
    width: 480px;
    height: 680px;
  }

  .chat-header {
    padding: 25px;
    height: 70px;

    .bot-avata-head {
      width: 40px;
      height: 40px;
    }

    .bot-name {
      font-size: 20px;
    }
  }

  :deep(.message-body) {
    font-size: 17px;
    padding: 14px 18px;
  }

  .chat-input {
    :deep(.el-input__inner) {
      font-size: 17px;
      height: 45px;
    }

    .el-button {
      width: 50px;
      height: 50px;
      font-size: 22px;
    }
  }
}

/* 在现有的媒体查询后面添加 2560px 的样式 */
@media screen and (min-width: 2560px) {
  .chat-bot-window {
    width: 580px;
    height: 780px;
  }

  .chat-header {
    padding: 30px;
    height: 80px;

    .bot-avata-head {
      width: 45px;
      height: 45px;
    }

    .bot-name {
      font-size: 22px;
    }
  }

  .message {
    margin-bottom: 30px;
    padding: 0 20px;
  }

  .message-avatar {
    width: 40px;
    height: 40px;
  }

  .message-content {
    max-width: 80%;
  }

  :deep(.message-body) {
    font-size: 18px;
    padding: 16px 20px;
  }

  /* 2560px下的快捷回复样式由子组件控制 */

  .chat-input {
    padding: 25px;

    :deep(.el-input__wrapper) {
      padding: 5px 18px;
    }

    :deep(.el-input__inner) {
      font-size: 18px;
      height: 48px;
    }

    .el-button {
      width: 55px;
      height: 55px;
      font-size: 24px;
      margin-left: 15px;
    }
  }

  .close-button-wrapper {
    bottom: -60px;

    .close-button {
      width: 50px;
      height: 50px;

      :deep(.el-icon) {
        font-size: 24px;
      }
    }
  }

  .chat-bot-trigger {
    .el-button {
      width: 60px;
      height: 60px;
      
      :deep(.el-icon) {
        font-size: 28px;
      }
    }
  }

  /* 时间戳样式 */
  :deep(.message-time) {
    font-size: 14px;
  }

  /* 滚动条调整 */
  .chat-messages::-webkit-scrollbar {
    width: 10px;
  }
}


</style>
