<?php

namespace Modules\Users\Providers;

use Bingo\Providers\BingoModuleServiceProvider;

/**
 * IAM模块服务提供者
 * 负责注册IAM模块的各种服务和功能
 */
class UsersServiceProvider extends BingoModuleServiceProvider
{
    /**
     * 启动模块
     * 注册模块的各种服务和功能
     */
    public function boot(): void
    {
        // 加载语言文件
        $path = dirname(__DIR__, 2) . DIRECTORY_SEPARATOR . 'Users' .DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Users');

        // 注册短代码
        add_shortcode('example', function ($atts, $content = null) {
            return '<div class="example">'.$content.'</div>';
        });

        // 注册导航
        $this->registerNavigation();

        // 加载视图
        $this->loadViewsFrom(dirname(__DIR__, 2) . DIRECTORY_SEPARATOR . 'Users' .DIRECTORY_SEPARATOR.'views', 'Iam');

        // 注册模块权限
        $this->registerModulePermissions();

        // 注册小部件管理器
        $this->registerWidgetManager();

        // 注册API路由
        $this->registerApiRoutes();
    }

    /**
     * 注册API路由
     */
    protected function registerApiRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Api/route.php');
    }

    /**
     * 获取模块名称
     */
    public function moduleName(): string
    {
        return 'Users';
    }

    /**
     * 获取服务提供者提供的服务
     */
    public function provides(): array
    {
        return [];
    }

    /**
     * 注册模块权限
     */
    public function registerPermissions(): array
    {
        $admin = [
            // 管理员权限配置
        ];
        $frontend = [
            // 前端权限配置
        ];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }

    /**
     * 注册模块设置
     */
    public function registerSettings(): array
    {
       return [];
    }
}
