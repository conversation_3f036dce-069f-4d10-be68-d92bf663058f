<?php

namespace Modules\Common\Provider\SearchBox;

class QuickSearchBoxProvider extends AbstractSearchBoxProvider
{
    protected string $name;
    protected string $title;
    protected string $url;
    protected int $order;

    public static function make(string $name, string $title, string $url, int $order = 1000): static
    {
        $o = new static();
        $o->name = $name;
        $o->title = $title;
        $o->url = $url;
        $o->order = $order;
        return $o;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string
    {
        return $this->title;
    }

    public function url(): string
    {
        return $this->url;
    }

    public function order(): int
    {
        return $this->order;
    }

}
