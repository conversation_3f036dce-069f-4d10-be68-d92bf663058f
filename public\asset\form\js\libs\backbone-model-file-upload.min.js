(function(root,factory){if(typeof define==="function"&&define.amd){define(["underscore","backbone"],function(_,Backbone){factory(root,Backbone,_)})}else if(typeof exports!=="undefined"){var _=require("underscore"),Backbone=require("backbone");factory(root,Backbone,_)}else{factory(root,root.Backbone,root._)}})(this,function(root,Backbone,_){"use strict";var _superClass=_.clone(Backbone.Model.prototype);var BackboneModelFileUpload=Backbone.Model.extend({fileAttribute:"file",save:function(key,val,options){var attrs,attributes=this.attributes,that=this;if(key==null||typeof key==="object"){attrs=key;options=val}else{(attrs={})[key]=val}options=_.extend({validate:true},options);if(attrs&&!options.wait){if(!this.set(attrs,options))return false}else{if(!this._validate(attrs,options))return false}var mergedAttrs=_.extend({},attributes,attrs);if(attrs&&options.wait){this.attributes=mergedAttrs}if(options.formData===true||options.formData!==false&&mergedAttrs[this.fileAttribute]&&mergedAttrs[this.fileAttribute]instanceof File||mergedAttrs[this.fileAttribute]instanceof FileList||mergedAttrs[this.fileAttribute]instanceof Blob){var formAttrs=_.clone(mergedAttrs),fileAttr=mergedAttrs[this.fileAttribute];formAttrs=this._flatten(formAttrs);formAttrs[this.fileAttribute]=fileAttr;var formData=new FormData;_.each(formAttrs,function(value,key){if(value instanceof FileList||key===that.fileAttribute&&value instanceof Array){_.each(value,function(file){formData.append(key,file)})}else{formData.append(key,value)}});options.data=formData;options.processData=false;options.contentType=false;if(!options.xhr){options.xhr=function(){var xhr=Backbone.$.ajaxSettings.xhr();xhr.upload.addEventListener("progress",_.bind(that._progressHandler,that),false);return xhr}}}if(attrs&&options.wait)this.attributes=attributes;return _superClass.save.call(this,attrs,options)},_flatten:function flatten(obj){var output={};for(var i in obj){if(!obj.hasOwnProperty(i))continue;if(typeof obj[i]=="object"){var flatObject=flatten(obj[i]);for(var x in flatObject){if(!flatObject.hasOwnProperty(x))continue;output[i+"["+x+"]"]=flatObject[x]}}else{output[i]=obj[i]}}return output},_unflatten:function unflatten(obj,output){var re=/^([^\[\]]+)\[(.+)\]$/g;output=output||{};for(var key in obj){var value=obj[key];if(!key.toString().match(re)){var tempOut={};tempOut[key]=value;_.extend(output,tempOut)}else{var keys=_.compact(key.split(re)),tempOut={};tempOut[keys[1]]=value;output[keys[0]]=unflatten(tempOut,output[keys[0]])}}return output},_progressHandler:function(event){if(event.lengthComputable){var percentComplete=event.loaded/event.total;this.trigger("progress",percentComplete)}}});Backbone.Model=BackboneModelFileUpload});