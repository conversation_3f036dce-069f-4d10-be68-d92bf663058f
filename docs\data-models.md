# 数据模型设计指南

## 目录
- [数据库设计](#数据库设计)
- [模型定义](#模型定义)
- [关联关系](#关联关系)
- [查询构建](#查询构建)
- [最佳实践](#最佳实践)

## 数据库设计

### 1. 命名规范

- 表名：模块名_表名（例如：course_courses）
- 字段名：下划线命名（例如：course_title）
- 主键：id
- 外键：关联表名_id（例如：course_id）
- 时间戳：created_at, updated_at, deleted_at

### 2. 字段类型规范

```sql
CREATE TABLE course_courses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    course_title VARCHAR(255) NOT NULL COMMENT '课程标题',
    course_description TEXT COMMENT '课程描述',
    course_duration INT NOT NULL DEFAULT 0 COMMENT '课程时长(分钟)',
    category_id BIGINT UNSIGNED COMMENT '分类ID',
    creator_id BIGINT UNSIGNED NOT NULL COMMENT '创建者ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id),
    KEY idx_category (category_id),
    KEY idx_creator (creator_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. 索引设计

- 主键索引：id
- 外键索引：xxx_id
- 组合索引：根据查询需求
- 全文索引：适用于搜索功能

## 模型定义

### 1. 基础模型

```php
namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

class Courses extends Model
{
    protected $table = 'course_courses';

    protected $fillable = [
        'course_title',
        'course_description',
        'course_duration',
        'category_id',
        'creator_id',
        'status'
    ];

    protected $casts = [
        'course_duration' => 'integer',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];
}
```

### 2. 关联模型

```php
namespace Modules\Course\Models;

class Courses extends Model
{
    public function category()
    {
        return $this->belongsTo(Categories::class, 'category_id');
    }

    public function chapters()
    {
        return $this->hasMany(Chapters::class, 'course_id');
    }

    public function students()
    {
        return $this->belongsToMany(Students::class, 'course_enrollments');
    }
}
```

## 关联关系

### 1. 一对一关系

```php
// 课程详情
public function detail()
{
    return $this->hasOne(CourseDetail::class, 'course_id');
}
```

### 2. 一对多关系

```php
// 课程章节
public function chapters()
{
    return $this->hasMany(Chapter::class, 'course_id');
}
```

### 3. 多对多关系

```php
// 课程学生
public function students()
{
    return $this->belongsToMany(Student::class, 'course_enrollments')
        ->withPivot(['status', 'score'])
        ->withTimestamps();
}
```

## 查询构建

### 1. 基础查询

```php
// 获取课程列表
$courses = Courses::query()
    ->with(['category', 'creator'])
    ->where('status', 1)
    ->orderBy('created_at', 'desc')
    ->paginate(15);
```

### 2. 高级查询

```php
// 复杂条件查询
$courses = Courses::query()
    ->when($category_id, function ($query, $category_id) {
        return $query->where('category_id', $category_id);
    })
    ->when($keyword, function ($query, $keyword) {
        return $query->where(function ($q) use ($keyword) {
            $q->where('course_title', 'like', "%{$keyword}%")
              ->orWhere('course_description', 'like', "%{$keyword}%");
        });
    })
    ->whereHas('chapters', function ($query) {
        $query->where('status', 1);
    })
    ->get();
```

### 3. 关联查询

```php
// 预加载关联数据
$course = Courses::query()
    ->with([
        'chapters' => function ($query) {
            $query->orderBy('sort_order');
        },
        'chapters.contents',
        'students' => function ($query) {
            $query->where('status', 1);
        }
    ])
    ->findOrFail($id);
```

## 最佳实践

### 1. 模型事件

```php
class Courses extends Model
{
    protected static function booted()
    {
        static::created(function ($course) {
            // 创建后的处理
            event(new CourseCreated($course));
        });

        static::updated(function ($course) {
            // 更新后的处理
            Cache::forget("course:{$course->id}");
        });
    }
}
```

### 2. 模型访问器和修改器

```php
class Courses extends Model
{
    // 访问器
    public function getDurationTextAttribute(): string
    {
        return $this->course_duration . '分钟';
    }

    // 修改器
    public function setCourseTitleAttribute($value)
    {
        $this->attributes['course_title'] = trim($value);
    }
}
```

### 3. 模型作用域

```php
class Courses extends Model
{
    // 全局作用域
    protected static function booted()
    {
        static::addGlobalScope('active', function ($query) {
            $query->where('status', 1);
        });
    }

    // 本地作用域
    public function scopePublished($query)
    {
        return $query->where('status', 1)
            ->where('published_at', '<=', now());
    }
}
```

### 4. 软删除

```php
class Courses extends Model
{
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    // 恢复软删除的记录
    public function restore()
    {
        $this->deleted_at = null;
        return $this->save();
    }
}
```

## 性能优化

1. 查询优化
   - 使用适当的索引
   - 避免 N+1 查询问题
   - 使用 chunk 处理大量数据

2. 缓存优化
   - 缓存频繁访问的数据
   - 使用模型观察者自动更新缓存
   - 合理设置缓存过期时间

3. 数据库优化
   - 定期维护和优化表
   - 合理使用字段类型
   - 适当的分表分库策略 
