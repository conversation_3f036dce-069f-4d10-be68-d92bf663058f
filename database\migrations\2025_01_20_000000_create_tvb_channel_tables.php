<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建频道表
        Schema::create('tvb_channel', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('频道名称');
            $table->text('description')->nullable()->comment('频道描述');
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用 1-启用');
            $table->integer('sort')->nullable()->comment('排序');
            $table->string('url', 500)->nullable()->comment('频道url');
            $table->string('cover_img', 500)->nullable()->comment('封面图');
            $table->unsignedBigInteger('created_by')->comment('创建人');
            $table->unsignedBigInteger('updated_by')->comment('更新人');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index('status', 'idx_status');
            $table->index('name', 'idx_name');
            $table->index('created_at', 'idx_created_at');
        });

        // 创建区域频道关联表
        Schema::create('tvb_regions_channel', function (Blueprint $table) {
            $table->id();
            $table->string('regions_id', 100)->comment('区域ID');
            $table->text('channel_id')->nullable()->comment('频道ID');
            $table->unsignedBigInteger('created_by')->comment('创建人');
            $table->unsignedBigInteger('updated_by')->comment('更新人');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index('created_at', 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tvb_regions_channel');
        Schema::dropIfExists('tvb_channel');
    }
}; 