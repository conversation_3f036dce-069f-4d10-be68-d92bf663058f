<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Cron<PERSON><PERSON> Runner <PERSON>
    |--------------------------------------------------------------------------
    |
    | 用于验证 Web 请求的访问令牌
    |
    */
    'runner_token' => env('CRONJOB_RUNNER_TOKEN', 'your-secret-token'),

    /*
    |--------------------------------------------------------------------------
    | Allowed IPs
    |--------------------------------------------------------------------------
    |
    | Web 请求允许的 IP 地址列表
    |
    */
    'allowed_ips' => [
        '127.0.0.1',
        // 添加其他允许的 IP
    ],

    /*
    |--------------------------------------------------------------------------
    | Lock Settings
    |--------------------------------------------------------------------------
    |
    | 锁定机制相关设置
    |
    */
    'lock' => [
        'timeout' => 3600, // 锁定超时时间(秒)
        'file' => storage_path('cronjob.lock'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Settings
    |--------------------------------------------------------------------------
    |
    | 日志相关设置
    |
    */
    'log' => [
        'file' => storage_path('logs/cronjob-polling.log'),
        'level' => env('CRONJOB_LOG_LEVEL', 'debug'),
    ],
];
