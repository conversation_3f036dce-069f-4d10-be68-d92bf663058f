<template>
  <div class="dashboard-statistics" v-if="showLeftPanel || showRightPanel">
    <el-row :gutter="20">
      <!-- 左侧安全防护工具 (70%) -->
      <el-col :span="showRightPanel ? 17 : 24" v-if="showLeftPanel">
        <div class="security-panel">
          <div class="panel-header">
            <div class="panel-title">
              <span class="title-text">安全防護工具</span>
            </div>
            <el-button type="text" link class="close-btn" @click="closeLeftPanel">
              <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
            </el-button>
          </div>
          
          <div class="traffic-monitor">
            <div class="monitor-header">
              <div class="monitor-title">
                <span class="title-text">請求總數</span>
                <span class="ddos-indicator">
                  <el-icon size="15">
                    <img :src="$asset('Dashboard/Asset/open-shield.png')" alt="" />
                  </el-icon>
                  DDoS攻擊
                </span>
              </div>
            </div>
            
            <div class="monitor-content">
              <!-- 上半部分：左右布局 -->
              <div class="top-section">
                <!-- 左侧主要数据显示 -->
                <div class="left-section">
                  <div class="main-number-section">
                    <div class="primary-stat">
                      <div class="big-number">6.03M</div>
                      <div class="trend-indicator">↑100%</div>
                    </div>
                  </div>
                </div>
                
                <!-- 右侧流量图表和次要统计项 -->
                <div class="right-section">
                  <!-- 流量图表 -->
                  <div class="traffic-chart">
                    <div ref="chartContainer" class="chart-container"></div>
                  </div>
                  
                  <!-- 次要统计项 -->
                  <div class="secondary-stats">
                    <div class="stat-item">
                      <div class="stat-value">
                        <div class="value">0</div>
                        <div class="stat-label">惡意</div>
                      </div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">
                        <div class="value">0</div>
                        <div class="stat-label">
                          <el-icon size="19" color="#9DA3AE">
                            <img :src="$asset('Dashboard/Asset/cloudflare.png')" alt="" />
                          </el-icon>
                          Cloudflare攔截
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 底部统计指标 -->
              <div class="bottom-stats">
                <div class="stat-box">
                  <div class="stat-number">{{ dailyProtectionCount }}</div>
                  <div class="stat-desc">今日防護次數</div>
                </div>
                <div class="stat-box">
                  <div class="stat-number">{{ scanFilesCount }}</div>
                  <div class="stat-desc">正掃碼檔案</div>
                </div>
                <div class="stat-box">
                  <div class="stat-number">0</div>
                  <div class="stat-desc">發現危險</div>
                </div>
                <div class="stat-box">
                  <div class="stat-number">{{ totalProtectionCount }}</div>
                  <div class="stat-desc">總防護次數</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧实用工具 (30%) -->
      <el-col :span="showLeftPanel ? 7 : 24" v-if="showRightPanel">
        <div class="tools-panel">
          <div class="panel-header">
            <div class="panel-title">
              <span class="title-text">實用工具</span>
            </div>
            <el-button type="text" link class="close-btn" @click="closeRightPanel">
              <el-icon size="18" color="#A7A7A7"><Close /></el-icon>
            </el-button>
          </div>
          
          <div class="tools-content" :style="{height: showLeftPanel ? '386px' : '100%'}">
            <el-row :gutter="16">
              <el-col 
                v-for="tool in toolsList" 
                :key="tool.id" 
                :xs="showLeftPanel ? 12 : 4"
                :sm="showLeftPanel ? 12 : 4"
                :md="showLeftPanel ? 12 : 4"
                :lg="showLeftPanel ? 12 : 4"
                class="tool-column"
                :style="{marginBottom: showLeftPanel ? '14px' : '0px'}"
              >
                <div class="tool-card" @click="handleToolClick(tool)">
                  <div class="icon-wrapper">
                    <img :src="$asset(tool.icon)" :alt="tool.title" />
                  </div>
                  <div class="title">{{ tool.title }}</div>
                </div>
              </el-col>

              <!-- 骨架屏 -->
              <template v-if="toolsLoading && toolsList.length === 0">
                <el-col 
                  v-for="index in 6" 
                  :key="'skeleton-' + index"
                  :xs="showLeftPanel ? 12 : 4"
                  :sm="showLeftPanel ? 12 : 4"
                  :md="showLeftPanel ? 12 : 4"
                  :lg="showLeftPanel ? 12 : 4"
                  class="tool-column"
                >
                  <div class="tool-card skeleton">
                    <div class="icon-wrapper"></div>
                    <div class="title"></div>
                  </div>
                </el-col>
              </template>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'
import { Document, Bell, Monitor, Warning, Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'

const { t } = useI18n()
const router = useRouter()

const loading = ref(true)

// 定义emits
const emit = defineEmits(['close'])
const infoValue = ref<any>([])
const chartContainer = ref<HTMLElement>()

// 安全检测动态数据
const securityDetectionCount = ref(3)

// 面板显示控制
const showLeftPanel = ref(true)
const showRightPanel = ref(true)

// 底部统计指标数据
const dailyProtectionCount = ref(8) // 今日防護次數
const scanFilesCount = ref(6) // 正掃碼檔案
const totalProtectionCount = ref(1190) // 總防護次數

// 工具列表数据
const toolsList = ref<any>([])
const toolsLoading = ref(true)

let securityTimer: number | null = null
let scanFilesTimer: number | null = null
let totalProtectionTimer: number | null = null
let chartInstance: echarts.ECharts | null = null
let resizeHandler: (() => void) | null = null

// 处理标题文本
const processTitle = (title: string): string => {
  // 如果标题长度小于等于4个字符，添加换行
  if (title.length <= 4) {
    return title + '\n'
  }
  return title
}

// 获取工具列表数据
const getTools = async () => {
  toolsLoading.value = true
  try {
    const res = await http.get('/dashboard/toolsSection/data')
    // 处理数据，取前6个
    toolsList.value = res.data.data.slice(0, 6).map((tool: any) => ({
      ...tool,
      title: processTitle(tool.title),
      link: tool.path // 保持与原有代码兼容
    }))
  } catch (error) {
  } finally {
    toolsLoading.value = false
  }
}

// 启动安全检测数据动态变化
const startSecurityAnimation = () => {
  securityTimer = setInterval(() => {
    // 生成1-5之间的随机数
    securityDetectionCount.value = Math.floor(Math.random() * 5) + 1
  }, 3000) // 3秒更换一次
}

// 启动扫描文件数据动态变化（每2-3秒随机切换）
const startScanFilesAnimation = () => {
  // 保持固定值，不需要动态变化
  scanFilesTimer = setInterval(() => {
    scanFilesCount.value = Math.floor(Math.random() * 10) + 1
  }, 2500) // 2.5秒更换一次
}

// 启动总防护次数动态变化（每周3次，这里用20分钟模拟）
const startTotalProtectionAnimation = () => {
  // 保持固定值，不需要动态变化
  totalProtectionTimer = setInterval(() => {
    totalProtectionCount.value = Math.floor(Math.random() * 1000) + 1
  }, 1200000) // 20分钟更换一次（模拟一周3次）
}

// 检查是否需要更新今日防护次数（每天随机1次）
const checkDailyProtectionUpdate = () => {
  // 保持固定值，不需要动态变化
  const today = new Date().toDateString()
  const lastUpdateDate = localStorage.getItem('dailyProtectionUpdateDate')
  
  if (lastUpdateDate !== today) {
    // 新的一天，更新数据并记录日期
    dailyProtectionCount.value = Math.floor(Math.random() * 10) + 1
    localStorage.setItem('dailyProtectionUpdateDate', today)
    localStorage.setItem('dailyProtectionCount', dailyProtectionCount.value.toString())
  } else {
    // 同一天，从localStorage获取数据
    const savedCount = localStorage.getItem('dailyProtectionCount')
    if (savedCount) {
      dailyProtectionCount.value = parseInt(savedCount)
    }
  }
}

// 清理定时器
const clearAllTimers = () => {
  if (securityTimer) {
    clearInterval(securityTimer)
    securityTimer = null
  }
  if (scanFilesTimer) {
    clearInterval(scanFilesTimer)
    scanFilesTimer = null
  }
  if (totalProtectionTimer) {
    clearInterval(totalProtectionTimer)
    totalProtectionTimer = null
  }
}

// 初始化图表 
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    grid: {
      left: '-10%',
      right: '-10%',
      top: '0%',
      bottom: '0%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: ['', '', '', '', '', '', ''],
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { 
        show: true,
        lineStyle: {
          color: '#E3E3E3',
          type: 'solid',
          width: 1
        }
      },
      splitNumber: 3,
      min: 0,
      max: 6
    },
    series: [
      {
        data: [1.2, 2.8, 1.5, 2.8, 3.2, 4.0, 5.2],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#007EE5',
          width: 2
        },
        itemStyle: {
          color: '#007EE5',
          borderWidth: 0
        },
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 126, 229, 0.2)' },
            { offset: 0.5, color: 'rgba(0, 126, 229, 0.1)' },
            { offset: 1, color: 'rgba(0, 126, 229, 0.05)' }
          ])
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 2,
            borderColor: '#007EE5',
            scale: 1.2
          }
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex
        const values = ['6.01M', '6.05M', '6.02M', '5.98M', '6.08M', '6.12M', '6.03M']
        return `${values[dataIndex]}`
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#007EE5',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    animation: true,
    animationDuration: 1000
  }
  
  chartInstance.setOption(option)
  
  // 响应式处理
  resizeHandler = () => {
    chartInstance?.resize()
  }
  
  window.addEventListener('resize', resizeHandler)
}

// 关闭面板
const closeLeftPanel = () => {
  showLeftPanel.value = false
  // 如果两个面板都关闭了，则关闭整个组件
  if (!showLeftPanel.value && !showRightPanel.value) {
    emit('close')
  }
}

const closeRightPanel = async () => {
  showRightPanel.value = false
  // 如果两个面板都关闭了，则关闭整个组件
  if (!showLeftPanel.value && !showRightPanel.value) {
    emit('close')
  } else if (showLeftPanel.value && chartInstance) {
    // 如果左侧面板还在显示，需要重新调整图表尺寸
    await nextTick()
    setTimeout(() => {
      chartInstance?.resize()
    }, 200)
  }
}

// 处理工具点击
const handleToolClick = (tool: any) => {
  if (tool.path) {
    if (tool.path.startsWith('http')) {
      window.open(tool.path, '_blank')
    } else {
      router.push(tool.path)
    }
  }
}

// 监听面板状态变化，重新调整图表尺寸
watch(
  [() => showRightPanel.value, () => showLeftPanel.value],
  async (newVal, oldVal) => {
    if (chartInstance && showLeftPanel.value) {
      // 等待DOM更新后重新调整图表尺寸
      await nextTick()
      setTimeout(() => {
        chartInstance?.resize()
      }, 150)
    }
  }
)

onMounted(async () => {
  // 启动所有动态数据更新
  checkDailyProtectionUpdate() // 检查今日防护次数
  startSecurityAnimation() // 安全检测动态数据
  startScanFilesAnimation() // 扫描文件动态数据
  startTotalProtectionAnimation() // 总防护次数动态数据
  
  // 获取工具列表数据
  await getTools()
  
  // 等待DOM更新后初始化图表
  await nextTick()
  initChart()
})

onUnmounted(() => {
  clearAllTimers()
  
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  
  // 移除resize监听器
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler)
    resizeHandler = null
  }
})
</script>

<script lang="ts">
export default {
  name: 'SafeTitle',
}
</script>

<style lang="scss" scoped>
.dashboard-statistics {
  .security-panel,
  .tools-panel {
    height: 100%;
    display: flex;
    flex-direction: column;

    .panel-header {
      margin-top: 25px;
      margin-bottom: 13px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #232323;
      font-weight: bold;
      font-size: 18px;
      line-height: 24px;

      .panel-title {
        .title-text {
          line-height: 24px;
        }
      }
    }
  }

      .security-panel {
      .traffic-monitor {
        height: 386px;
        background-color: #fff;
        box-shadow: 0px 3px 6px #00000029;
        border-radius: 10px;
        display: flex;
        flex-direction: column;

      .monitor-header {
        padding: 29px 22px 25px 23px;

        .monitor-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 10px;

          .title-text {
            font-size: 14px;
            font-weight: normal;
            color: #7E7E7E;
          }

          .ddos-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #474747;
          }
        }
      }

              .monitor-content {
          padding: 0 22px 19px 23px;
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .top-section {
            display: flex;
            gap: 20px;
            flex: 1;
            
            .left-section {
              display: flex;
              flex-direction: column;
              
              .main-number-section {
                .primary-stat {
                  .big-number {
                    font-size: 30px;
                    font-weight: bold;
                    color: #007EE5;
                    line-height: 34px;
                    font-family: Arial;
                  }
                  
                  .trend-indicator {
                    font-size: 14px;
                    font-weight: bold;
                    color: #43956C;
                    margin-top: 5px;
                  }
                }
              }
            }
            
            .right-section {
              flex: 1;
              display: flex;
              flex-direction: column;
              
              .traffic-chart {
                margin-bottom: 36px;
                height: 109px;
                
                .chart-container {
                  width: 100%;
                  height: 100%;
                }
              }
              
              .secondary-stats {
                display: flex;
                justify-content: space-between;
                
                .stat-item {
                  text-align: center;
                  
                  .stat-value {
                    display: flex;
                    gap: 6px;
                    
                    .value {
                      font-size: 30px;
                      font-weight: bold;
                      color: #2A2A2A;
                      line-height: 34px;
                      font-family: Arial;
                    }
                  }
                  
                  .stat-label {
                    font-size: 16px;
                    color: #9DA3AE;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    line-height: 21px;
                  }
                }
              }
            }
          }

          .bottom-stats {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            border: 2px solid #86C0E9;
            border-radius: 5px;
            background: #F4F9FD;
            padding: 14px 20px 13px 17px;

            .stat-box {
              flex: 1;
              text-align: center;
              padding: 0;

              .stat-number {
                font-size: 26px;
                font-weight: bold;
                color: #218EE8;
                margin-bottom: 7px;
                line-height: 31px;
                font-family: Arial;
              }

              .stat-desc {
                font-size: 14px;
                color: #000000;
                line-height: 19px;
                word-break: keep-all;
              }
            }
          }
      }
    }
  }

  .tools-panel {
    .tools-content {
      height: 386px;

      .tool-column {
          height: 119px;
          margin-bottom: 14px;
        }

      .tool-card {
        box-shadow: 0px 3px 6px #00000029;
        border-radius: 10px;
        background-color: #fff;
        transition: transform 0.3s ease;
        cursor: pointer;
        height: 100%;
        
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:hover {
          transform: translateY(-2px);
        }

        .icon-wrapper {
          margin-bottom: 9px;
          width: 39px;
          height: 39px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
          }
        }

        .title {
          font-size: 14px;
          color: #19496a;
          text-align: center;
          line-height: 1.4375;
          white-space: pre-line;
          word-break: break-word;
        }

        &.skeleton {
          .icon-wrapper {
            background-color: #f5f7fa;
            border-radius: 4px;
          }

          .title {
            background-color: #f5f7fa;
            border-radius: 4px;
            width: 70%;
            height: 28px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 1200px) {
    .security-panel,
    .tools-panel {
      margin-bottom: 20px;
    }
  }
}
</style>
