# 后端设计模式指南

## 目录
- [架构模式](#架构模式)
- [设计模式](#设计模式)
- [领域驱动设计](#领域驱动设计)
- [SOLID 原则](#solid-原则)
- [最佳实践](#最佳实践)

## 架构模式

### 1. 分层架构

```
应用层架构
├── Api/                  # API 接口层
├── Web/                  # Web 接口层
├── Admin/                # 管理后台层
├── OpenApi/              # 开放 API 层
├── Domain/               # 领域层
│   ├── Entities/        # 领域实体
│   ├── Interfaces/      # 接口定义
│   ├── Repositories/    # 仓储实现
│   └── Services/        # 领域服务
├── Services/             # 应用服务层
└── Models/               # 数据模型层
```

### 2. DDD 分层架构

- 表现层（Api/Web/Admin/OpenApi）
- 应用层（Services）
- 领域层（Domain）
- 基础设施层（Models/Database）

## 设计模式

### 1. 仓储模式（Repository Pattern）

```php
namespace Modules\Course\Domain\Repositories;

interface CourseRepositoryInterface
{
    public function findById(int $id): ?Course;
    public function save(Course $course): Course;
}

class CourseRepository implements CourseRepositoryInterface
{
    public function findById(int $id): ?Course
    {
        $model = Courses::find($id);
        return $model ? $this->mapToEntity($model) : null;
    }
}
```

### 2. 服务模式（Service Pattern）

```php
namespace Modules\Course\Services;

class CourseService
{
    public function __construct(
        private readonly CourseRepositoryInterface $repository,
        private readonly CalendarEventService $calendarService
    ) {
    }

    public function createCourse(array $data): Course
    {
        // 业务逻辑处理
    }
}
```

### 3. 工厂模式（Factory Pattern）

```php
namespace Modules\Course\Domain\Factories;

class CourseFactory
{
    public static function createFromRequest(array $data): Course
    {
        return new Course(
            title: $data['title'],
            description: $data['description'],
            startDate: new DateTime($data['start_date']),
            endDate: new DateTime($data['end_date'])
        );
    }
}
```

### 4. 策略模式（Strategy Pattern）

```php
namespace Modules\Course\Domain\Strategies;

interface GradingStrategy
{
    public function calculateGrade(array $scores): float;
}

class WeightedGradingStrategy implements GradingStrategy
{
    public function calculateGrade(array $scores): float
    {
        // 实现加权计分策略
    }
}
```

## 领域驱动设计

### 1. 实体（Entities）

```php
namespace Modules\Course\Domain\Entities;

class Course
{
    public function __construct(
        private readonly int $id,
        private string $title,
        private string $description,
        private DateTime $startDate,
        private DateTime $endDate
    ) {
    }

    public function updateTitle(string $title): void
    {
        $this->title = $title;
    }
}
```

### 2. 值对象（Value Objects）

```php
namespace Modules\Course\Domain\ValueObjects;

class Duration
{
    public function __construct(
        private readonly DateTime $startDate,
        private readonly DateTime $endDate
    ) {
        if ($endDate <= $startDate) {
            throw new InvalidArgumentException('结束时间必须晚于开始时间');
        }
    }

    public function getDays(): int
    {
        return $this->endDate->diff($this->startDate)->days;
    }
}
```

### 3. 聚合（Aggregates）

```php
namespace Modules\Course\Domain\Aggregates;

class CourseAggregate
{
    private Course $course;
    private Collection $chapters;
    private Collection $students;

    public function addChapter(Chapter $chapter): void
    {
        $this->chapters->add($chapter);
    }

    public function enrollStudent(Student $student): void
    {
        if ($this->hasReachedCapacity()) {
            throw new DomainException('课程已达到最大容量');
        }
        $this->students->add($student);
    }
}
```

## SOLID 原则

### 1. 单一职责原则（SRP）

```php
// 好的实践
class CourseRepository
{
    public function find() {} // 只负责数据访问
}

class CourseService
{
    public function process() {} // 只负责业务逻辑
}
```

### 2. 开放封闭原则（OCP）

```php
interface NotificationChannel
{
    public function send(string $message): void;
}

class EmailNotification implements NotificationChannel
{
    public function send(string $message): void {}
}

class SMSNotification implements NotificationChannel
{
    public function send(string $message): void {}
}
```

### 3. 依赖倒置原则（DIP）

```php
class CourseController
{
    public function __construct(
        private readonly CourseServiceInterface $service
    ) {}
}
```

## 最佳实践

### 1. 错误处理

```php
namespace Modules\Course\Exceptions;

class CourseException extends BizException
{
    public static function notFound(int $id): self
    {
        return new self(CourseErrorCode::COURSE_NOT_FOUND, "课程 {$id} 不存在");
    }

    public static function invalidDuration(): self
    {
        return new self(CourseErrorCode::INVALID_DURATION, '课程持续时间无效');
    }
}
```

### 2. 事务处理

```php
namespace Modules\Course\Services;

class CourseEnrollmentService
{
    public function enroll(int $courseId, int $studentId): void
    {
        DB::transaction(function () use ($courseId, $studentId) {
            // 1. 检查课程容量
            // 2. 创建选课记录
            // 3. 更新课程统计
            // 4. 发送通知
        });
    }
}
```

### 3. 缓存策略

```php
namespace Modules\Course\Services;

class CourseCache
{
    private const TTL = 3600; // 1小时

    public function getCourse(int $id): ?Course
    {
        return Cache::remember(
            "course:{$id}",
            self::TTL,
            fn() => $this->repository->findById($id)
        );
    }
}
```

### 4. 日志记录

```php
namespace Modules\Course\Services;

class CourseLogger
{
    public function logAccess(int $courseId, int $userId): void
    {
        Log::info('课程访问', [
            'course_id' => $courseId,
            'user_id' => $userId,
            'timestamp' => now(),
            'ip' => request()->ip()
        ]);
    }
}
```

## 性能优化

1. 数据库优化
   - 合理使用索引
   - 查询优化
   - N+1 问题避免

2. 缓存策略
   - 多级缓存
   - 缓存预热
   - 缓存更新策略

3. 代码优化
   - 延迟加载
   - 批量处理
   - 异步处理 
