{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["../admin/*"], "@frontend/*": ["./*"], "/admin": ["../admin/*"], "/frontend": ["./*"], "@public": ["../../public/*"], "@/module": ["../../Modules/*"], "/resources": ["../../resources/*"]}, "skipLibCheck": true, "noImplicitAny": false}, "include": ["**/*.ts", "**/*.d.ts", "**/*.tsx", "**/*.vue"], "exclude": ["node_modules", "../admin"]}