# LiveChannel 模块快速启动指南

## 🚀 5分钟快速开始

### 1. 运行数据库迁移

```bash
# 进入项目根目录
cd /path/to/your/laravel/project

# 运行迁移
php artisan migrate
```

### 2. 注册服务提供者

在 `config/app.php` 的 `providers` 数组中添加：

```php
'providers' => [
    // ... 其他提供者
    Modules\LiveChannel\Providers\LiveChannelServiceProvider::class,
],
```

### 3. 测试API接口

#### 创建测试频道

```bash
curl -X POST http://localhost/api/admin/live-channel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "翡翠台",
    "name_hk": "翡翠台",
    "description": "TVB翡翠台高清直播",
    "description_hk": "TVB翡翠台高清直播",
    "cover_image_url": "https://example.com/cover.jpg",
    "stream_url": "https://example.com/stream.m3u8",
    "start_time": "2024-01-15 14:00:00",
    "end_time": "2024-01-15 18:00:00",
    "is_audio_only": false,
    "is_breaking_news": false,
    "is_hk_only": true
  }'
```

#### 获取频道列表

```bash
curl -X GET http://localhost/api/admin/live-channel \
  -H "Authorization: Bearer your-token"
```

#### 更新直播状态

```bash
curl -X PUT http://localhost/api/admin/live-channel/1/live-status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "live_status": 1
  }'
```

## 📋 核心功能验证

### 1. 基础CRUD操作

- ✅ 创建频道
- ✅ 获取频道列表
- ✅ 获取频道详情
- ✅ 更新频道信息
- ✅ 删除频道

### 2. 状态管理

- ✅ 更新直播状态（关闭/直播中/暂停）
- ✅ 批量更新状态
- ✅ 获取直播中的频道

### 3. 高级功能

- ✅ 搜索和筛选
- ✅ 分页支持
- ✅ 时间冲突检测
- ✅ 自动生成频道编号
- ✅ 统计信息

## 🔧 配置检查

### 1. 检查数据库表

```sql
-- 检查表是否存在
SHOW TABLES LIKE 'tvb_channels';

-- 检查表结构
DESCRIBE tvb_channels;
```

### 2. 检查路由

```bash
# 查看所有路由
php artisan route:list | grep live-channel
```

### 3. 检查服务提供者

```bash
# 检查服务提供者是否注册
php artisan config:cache
```

## 🐛 常见问题

### 1. 迁移失败

**问题**: `Class not found` 错误

**解决方案**:
```bash
# 清除缓存
php artisan config:clear
php artisan cache:clear

# 重新运行迁移
php artisan migrate:fresh
```

### 2. 路由404

**问题**: API路由返回404

**解决方案**:
```bash
# 检查路由文件是否正确加载
php artisan route:list | grep live-channel

# 清除路由缓存
php artisan route:clear
```

### 3. 认证失败

**问题**: 返回401未授权错误

**解决方案**:
```bash
# 确保已登录并获取有效的token
# 在请求头中添加正确的Authorization
Authorization: Bearer your-valid-token
```

### 4. 验证错误

**问题**: 返回422验证失败

**解决方案**:
- 检查必填字段是否提供
- 检查字段格式是否正确
- 检查字段长度限制

## 📊 测试数据

### 创建测试频道

```php
use Modules\LiveChannel\Services\LiveChannelService;

$service = app(LiveChannelService::class);

// 创建翡翠台
$jadeChannel = $service->create([
    'name' => '翡翠台',
    'name_hk' => '翡翠台',
    'description' => 'TVB翡翠台高清直播',
    'description_hk' => 'TVB翡翠台高清直播',
    'cover_image_url' => 'https://example.com/jade.jpg',
    'stream_url' => 'https://example.com/jade.m3u8',
    'start_time' => '2024-01-15 14:00:00',
    'end_time' => '2024-01-15 18:00:00',
    'is_audio_only' => false,
    'is_breaking_news' => false,
    'is_hk_only' => true,
    'live_status' => 1,
    'status' => 1,
    'sort' => 1,
]);

// 创建明珠台
$pearlChannel = $service->create([
    'name' => '明珠台',
    'name_hk' => '明珠台',
    'description' => 'TVB明珠台高清直播',
    'description_hk' => 'TVB明珠台高清直播',
    'cover_image_url' => 'https://example.com/pearl.jpg',
    'stream_url' => 'https://example.com/pearl.m3u8',
    'start_time' => '2024-01-15 16:00:00',
    'end_time' => '2024-01-15 20:00:00',
    'is_audio_only' => false,
    'is_breaking_news' => false,
    'is_hk_only' => true,
    'live_status' => 0,
    'status' => 1,
    'sort' => 2,
]);
```

## 🎯 下一步

1. **前端集成**: 创建Vue.js管理界面
2. **权限控制**: 添加角色和权限管理
3. **实时通知**: 集成WebSocket推送
4. **监控告警**: 添加系统监控
5. **性能优化**: 添加缓存和索引优化

## 📞 技术支持

如果遇到问题，请检查：

1. Laravel版本兼容性
2. PHP版本要求（>=8.1）
3. 数据库连接配置
4. 文件权限设置
5. 日志文件查看

## 📚 相关文档

- [完整API文档](./api-examples.md)
- [模块架构说明](./README.md)
- [最佳实践指南](../Region/docs/best-practices.md) 