define(["jquery","underscore","backbone","models/component","views/tab-component","views/tab-widget"],function($,_,Backbone,ComponentModel,TabComponentView,TabWidgetView){return Backbone.Collection.extend({model:ComponentModel,renderAll:function(){return this.map(function(component){return new TabComponentView({model:component}).render()})},renderAllAsWidgets:function(){return this.map(function(component){return new TabWidgetView({model:component}).render()})}})});