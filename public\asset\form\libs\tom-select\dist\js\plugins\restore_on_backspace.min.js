(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.restore_on_backspace=factory())})(this,function(){"use strict";function plugin(userOptions){const self=this;const options=Object.assign({text:option=>{return option[self.settings.labelField]}},userOptions);self.on("item_remove",function(value){if(!self.isFocused){return}if(self.control_input.value.trim()===""){var option=self.options[value];if(option){self.setTextboxValue(options.text.call(self,option))}}})}return plugin});