import { Extension } from '@tiptap/core'

export const AllowAttributes = Extension.create({
  name: 'allowAttributes',

  addGlobalAttributes() {
    return [
      {
        // 应用于所有的节点
        types: [
          'paragraph',
          'heading',
          'blockquote',
          'bulletList',
          'orderedList',
          'listItem',
          'image',
          'horizontalRule',
          'codeBlock',
          'table',
          'tableRow',
          'tableCell',
          'tableHeader',
          'link',
          'taskList',
          'taskItem',
          // 添加自定义Block类型
          'bootstrapComponent',
          'bootstrapCard',
          'bootstrapAlert',
          'bootstrapBadge',
          'bootstrapButton',
          'bootstrapNavbar',
          'bootstrapModal',
          'bootstrapTab',
          'bootstrapDropdown',
          'bootstrapCarousel',
          'buttonBlock',
          'gridBlock',
          'headingBlock',
          'heroBlock',
          'imageBlock',
          'layoutBlock',
          'richTextBlock',
          'countdownBlock',
          'featureListBlock',
          'ctaBlock',
          'testimonialSliderBlock'
        ],
        attributes: {
          // 允许内联样式
          style: {
            default: null,
            parseHTML: element => element.getAttribute('style'),
            renderHTML: attributes => {
              if (!attributes.style) {
                return {}
              }
              
              return {
                style: attributes.style,
              }
            },
          },
          // 允许类名
          class: {
            default: null,
            parseHTML: element => element.getAttribute('class'),
            renderHTML: attributes => {
              if (!attributes.class) {
                return {}
              }
              
              return {
                class: attributes.class,
              }
            },
          },
          // 允许ID属性
          id: {
            default: null,
            parseHTML: element => element.getAttribute('id'),
            renderHTML: attributes => {
              if (!attributes.id) {
                return {}
              }
              
              return {
                id: attributes.id,
              }
            },
          },
          // 允许链接目标属性
          target: {
            default: null,
            parseHTML: element => element.getAttribute('target'),
            renderHTML: attributes => {
              if (!attributes.target) {
                return {}
              }
              
              return {
                target: attributes.target,
              }
            },
          },
          // 允许链接rel属性
          rel: {
            default: null,
            parseHTML: element => element.getAttribute('rel'),
            renderHTML: attributes => {
              if (!attributes.rel) {
                return {}
              }
              
              return {
                rel: attributes.rel,
              }
            },
          },
          // 允许自定义数据属性
          ...Object.fromEntries(
            Array.from({ length: 10 }, (_, i) => i).map(i => [
              `data-${i}`,
              {
                default: null,
                parseHTML: element => element.getAttribute(`data-${i}`),
                renderHTML: attributes => {
                  const attr = `data-${i}`
                  if (!attributes[attr]) {
                    return {}
                  }
                  
                  return {
                    [attr]: attributes[attr],
                  }
                },
              },
            ]),
          ),
          // 添加Bootstrap特定的数据属性
          'data-bs-component': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-component'),
            renderHTML: attributes => {
              if (!attributes['data-bs-component']) {
                return {}
              }
              return {
                'data-bs-component': attributes['data-bs-component'],
              }
            },
          },
          'data-bs-toggle': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-toggle'),
            renderHTML: attributes => {
              if (!attributes['data-bs-toggle']) {
                return {}
              }
              return {
                'data-bs-toggle': attributes['data-bs-toggle'],
              }
            },
          },
          // 新增Bootstrap数据属性
          'data-bs-target': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-target'),
            renderHTML: attributes => {
              if (!attributes['data-bs-target']) {
                return {}
              }
              return {
                'data-bs-target': attributes['data-bs-target'],
              }
            },
          },
          'data-bs-dismiss': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-dismiss'),
            renderHTML: attributes => {
              if (!attributes['data-bs-dismiss']) {
                return {}
              }
              return {
                'data-bs-dismiss': attributes['data-bs-dismiss'],
              }
            },
          },
          'data-bs-slide': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-slide'),
            renderHTML: attributes => {
              if (!attributes['data-bs-slide']) {
                return {}
              }
              return {
                'data-bs-slide': attributes['data-bs-slide'],
              }
            },
          },
          'data-bs-slide-to': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-slide-to'),
            renderHTML: attributes => {
              if (!attributes['data-bs-slide-to']) {
                return {}
              }
              return {
                'data-bs-slide-to': attributes['data-bs-slide-to'],
              }
            },
          },
          'data-bs-ride': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-ride'),
            renderHTML: attributes => {
              if (!attributes['data-bs-ride']) {
                return {}
              }
              return {
                'data-bs-ride': attributes['data-bs-ride'],
              }
            },
          },
          'data-bs-parent': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-parent'),
            renderHTML: attributes => {
              if (!attributes['data-bs-parent']) {
                return {}
              }
              return {
                'data-bs-parent': attributes['data-bs-parent'],
              }
            },
          },
          'data-bs-spy': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-spy'),
            renderHTML: attributes => {
              if (!attributes['data-bs-spy']) {
                return {}
              }
              return {
                'data-bs-spy': attributes['data-bs-spy'],
              }
            },
          },
          'data-bs-offset': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-offset'),
            renderHTML: attributes => {
              if (!attributes['data-bs-offset']) {
                return {}
              }
              return {
                'data-bs-offset': attributes['data-bs-offset'],
              }
            },
          },
          'data-bs-method': {
            default: null,
            parseHTML: element => element.getAttribute('data-bs-method'),
            renderHTML: attributes => {
              if (!attributes['data-bs-method']) {
                return {}
              }
              return {
                'data-bs-method': attributes['data-bs-method'],
              }
            },
          },
          'aria-expanded': {
            default: null,
            parseHTML: element => element.getAttribute('aria-expanded'),
            renderHTML: attributes => {
              if (!attributes['aria-expanded']) {
                return {}
              }
              return {
                'aria-expanded': attributes['aria-expanded'],
              }
            },
          },
          'aria-controls': {
            default: null,
            parseHTML: element => element.getAttribute('aria-controls'),
            renderHTML: attributes => {
              if (!attributes['aria-controls']) {
                return {}
              }
              return {
                'aria-controls': attributes['aria-controls'],
              }
            },
          },
          'aria-selected': {
            default: null,
            parseHTML: element => element.getAttribute('aria-selected'),
            renderHTML: attributes => {
              if (!attributes['aria-selected']) {
                return {}
              }
              return {
                'aria-selected': attributes['aria-selected'],
              }
            },
          },
          'aria-label': {
            default: null,
            parseHTML: element => element.getAttribute('aria-label'),
            renderHTML: attributes => {
              if (!attributes['aria-label']) {
                return {}
              }
              return {
                'aria-label': attributes['aria-label'],
              }
            },
          },
          'aria-labelledby': {
            default: null,
            parseHTML: element => element.getAttribute('aria-labelledby'),
            renderHTML: attributes => {
              if (!attributes['aria-labelledby']) {
                return {}
              }
              return {
                'aria-labelledby': attributes['aria-labelledby'],
              }
            },
          },
          'aria-hidden': {
            default: null,
            parseHTML: element => element.getAttribute('aria-hidden'),
            renderHTML: attributes => {
              if (!attributes['aria-hidden']) {
                return {}
              }
              return {
                'aria-hidden': attributes['aria-hidden'],
              }
            },
          },
          'role': {
            default: null,
            parseHTML: element => element.getAttribute('role'),
            renderHTML: attributes => {
              if (!attributes['role']) {
                return {}
              }
              return {
                'role': attributes['role'],
              }
            },
          },
          'tabindex': {
            default: null,
            parseHTML: element => element.getAttribute('tabindex'),
            renderHTML: attributes => {
              if (!attributes['tabindex']) {
                return {}
              }
              return {
                'tabindex': attributes['tabindex'],
              }
            },
          },
        },
      },
    ]
  },
}) 

