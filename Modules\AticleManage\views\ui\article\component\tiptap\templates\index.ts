/**
 * Bootstrap 组件模板导出文件
 * 这个文件集中导入并导出所有模板
 */

import alertTemplate from './alert.template';
import cardTemplate from './card.template';
import buttonGroupTemplate from './buttonGroup.template';
import formTemplate from './form.template';
import carouselTemplate from './carousel.template';
import accordionTemplate from './accordion.template';
import aClassTemplate from './a-class.template';
import getDividerTemplate from './divider.template';
import getButtonTemplate from './button.template';
import countdownTemplate from './countdown.template';
import { featureListTemplate } from './featureList.template';
import listTemplate from './list.template';
import metricsTemplate from './metrics.template';
import { pricingTemplate } from './pricing.template'
import { richTextTemplate } from './richText.template'
import { socialFlowTemplate } from './socialFlow.template'
import { testimonialSliderTemplate } from './testimonialSlider.template'
import { timelineTemplate } from './timeline.template'
import { statsCardTemplate } from './statsCard.template'
import { navbarTemplate } from './navbar.template'
import { heroTemplate } from './hero.template'
import { featureCardsTemplate } from './featureCards.template'
import { infoSectionTemplate } from './infoSection.template'
import { footerTemplate } from './footer.template'
import ctaTemplate from './cta.template'
import { imageTemplate } from './image.template'
import { teamTemplate } from './team.template'
import { partnersTemplate } from './partners.template'

// 创建响应式包装函数
export const createResponsiveWrapper = (
  componentType: string,
  content: string, 
  options: {
    className?: string,
    fullWidth?: boolean,
    textAlign?: 'left' | 'center' | 'right',
    extraClasses?: string
  } = {}
) => {
  const { 
    className = '', 
    fullWidth = false, 
    textAlign = 'left',
    extraClasses = ''
  } = options;
  
  // 计算列宽
  const colClasses = fullWidth ? 'col-12' : 'col-12 col-md-10 col-lg-8';
  
  // 文本对齐
  const textAlignClass = textAlign !== 'left' ? `text-${textAlign}` : '';
  
  // 组合所有样式
  const combinedClasses = [
    className,
    'responsive-block',
    extraClasses
  ].filter(Boolean).join(' ');
  
  return `
    <div data-bs-component="${componentType}" class="${combinedClasses}">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="${colClasses} ${textAlignClass}">
            ${content}
          </div>
        </div>
      </div>
    </div>
  `.trim();
};

// 布局模板
const layoutTemplates: Record<string, string> = {
  'layout-single': `
    <div data-bs-component="layout" data-bs-layout="single-column" class="my-4 bootstrap-layout layout-single">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row justify-content-center">
                <div class="p-3 border col-12 bg-light" data-layout-container>
                  <!-- 在此放置内容模块 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  'layout-two-column': `
    <div data-bs-component="layout" data-bs-layout="two-column" class="my-4 bootstrap-layout layout-two-column">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row">
                <div class="p-3 border col-md-6 bg-light" data-layout-container="left">
                  <!-- 左侧内容 -->
                </div>
                <div class="p-3 border col-md-6 bg-light" data-layout-container="right">
                  <!-- 右侧内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  'layout-three-column': `
    <div data-bs-component="layout" data-bs-layout="three-column" class="my-4 bootstrap-layout layout-three-column">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row">
                <div class="p-3 border col-md-4 bg-light" data-layout-container="left">
                  <!-- 左侧内容 -->
                </div>
                <div class="p-3 border col-md-4 bg-light" data-layout-container="middle">
                  <!-- 中间内容 -->
                </div>
                <div class="p-3 border col-md-4 bg-light" data-layout-container="right">
                  <!-- 右侧内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  'layout-1-3-2-3': `
    <div data-bs-component="layout" data-bs-layout="1-3-2-3" class="my-4 bootstrap-layout layout-1-3-2-3">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row">
                <div class="p-3 border col-md-4 bg-light" data-layout-container="left">
                  <!-- 左侧内容(1/3) -->
                </div>
                <div class="p-3 border col-md-8 bg-light" data-layout-container="right">
                  <!-- 右侧内容(2/3) -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  'layout-2-3-1-3': `
    <div data-bs-component="layout" data-bs-layout="2-3-1-3" class="my-4 bootstrap-layout layout-2-3-1-3">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row">
                <div class="p-3 border col-md-8 bg-light" data-layout-container="left">
                  <!-- 左侧内容(2/3) -->
                </div>
                <div class="p-3 border col-md-4 bg-light" data-layout-container="right">
                  <!-- 右侧内容(1/3) -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  'layout-four-column': `
    <div data-bs-component="layout" data-bs-layout="four-column" class="my-4 bootstrap-layout layout-four-column">
      <div class="p-0 container-fluid">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="container">
              <div class="row">
                <div class="p-3 border col-md-3 bg-light" data-layout-container="first">
                  <!-- 第一列内容 -->
                </div>
                <div class="p-3 border col-md-3 bg-light" data-layout-container="second">
                  <!-- 第二列内容 -->
                </div>
                <div class="p-3 border col-md-3 bg-light" data-layout-container="third">
                  <!-- 第三列内容 -->
                </div>
                <div class="p-3 border col-md-3 bg-light" data-layout-container="fourth">
                  <!-- 第四列内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
}

// 导出所有模板
export {
  alertTemplate,
  cardTemplate,
  buttonGroupTemplate,
  formTemplate,
  carouselTemplate,
  accordionTemplate,
  aClassTemplate,
  countdownTemplate,
  featureListTemplate,
  listTemplate,
  metricsTemplate,
  richTextTemplate,
  pricingTemplate,
  socialFlowTemplate,
  testimonialSliderTemplate,
  timelineTemplate,
  statsCardTemplate,
  navbarTemplate,
  heroTemplate,
  featureCardsTemplate,
  infoSectionTemplate,
  footerTemplate,
  ctaTemplate,
  imageTemplate,
  teamTemplate,
  partnersTemplate
};

// 模块列表
export const moduleList = [
  {
    type: 'bootstrap-heading',
    title: '大标题',
    icon: 'title'
  },
  {
    type: 'bootstrap-headline',
    title: '标题段落',
    icon: 'format_size'
  },
  {
    type: 'bootstrap-button',
    title: '按钮',
    icon: 'smart_button'
  },
  {
    type: 'bootstrap-button-group',
    title: '按钮组',
    icon: 'dashboard'
  },
  {
    type: 'bootstrap-alert',
    title: '提示框',
    icon: 'warning'
  },
  {
    type: 'bootstrap-card',
    title: '卡片',
    icon: 'crop_original'
  },
  {
    type: 'bootstrap-accordion',
    title: '手风琴',
    icon: 'expand_more'
  },
  {
    type: 'bootstrap-divider',
    title: '分隔线',
    icon: 'horizontal_rule'
  },
  {
    type: 'bootstrap-form',
    title: '表单',
    icon: 'text_fields'
  },
  {
    type: 'bootstrap-carousel',
    title: '轮播图',
    icon: 'view_carousel'
  },
  {
    type: 'bootstrap-a-class',
    title: 'a标签',
    icon: 'link'
  },
  {
    type: 'bootstrap-layout',
    title: '布局',
    icon: 'grid_view'
  },
  {
    type: 'bootstrap-countdown',
    title: '倒计时',
    icon: 'timer'
  },
  {
    type: 'bootstrap-feature-list',
    title: '功能列表',
    icon: 'list'
  },
  {
    type: 'bootstrap-list',
    title: '列表',
    icon: 'format_list_bulleted'
  },
  {
    type: 'bootstrap-metrics',
    title: '指标',
    icon: 'analytics'
  },
  {
    type: 'bootstrap-pricing',
    title: '定价卡片',
    icon: 'attach_money'
  },
  {
    type: 'bootstrap-cta',
    title: '号召行动',
    icon: 'call_to_action'
  },
  {
    type: 'richTextBlock',
    title: '富文本',
    icon: 'text_format'
  },
  {
    type: 'socialFlowBlock',
    title: '社交媒体',
    icon: 'share'
  },
  {
    type: 'testimonialSliderBlock',
    title: '客户评价',
    icon: 'groups'
  },
  {
    type: 'timelineBlock',
    title: '时间线',
    icon: 'timeline'
  },
  {
    type: 'statsCardBlock',
    title: '数据卡片',
    icon: 'assessment'
  },
  {
    type: 'navbarBlock',
    title: '导航栏',
    icon: 'menu'
  },
  {
    type: 'heroBlock',
    title: '英雄区域',
    icon: 'view_day'
  },
  {
    type: 'featureCardsBlock',
    title: '特性卡片',
    icon: 'view_module'
  },
  {
    type: 'infoSectionBlock',
    title: '信息部分',
    icon: 'info'
  },
  {
    type: 'footerBlock',
    title: '页脚',
    icon: 'horizontal_split'
  },
  {
    type: 'bootstrap-image',
    title: '图片',
    icon: 'image'
  },
  {
    type: 'team-block',
    title: '团队',
    icon: 'group'
  },
  {
    type: 'partners',
    title: '合作伙伴',
    icon: 'share'
  }
]

// 获取模板函数
export const getTemplateByType = (type: string): string | null => {
  if (type.startsWith('layout-')) {
    return layoutTemplates[type] || null
  }

  const templates: Record<string, string> = {
    'bootstrap-alert': alertTemplate,
    'bootstrap-card': cardTemplate,
    'bootstrap-divider': getDividerTemplate(),
    'bootstrap-button': getButtonTemplate(),
    'bootstrap-accordion': accordionTemplate,
    'bootstrap-form': formTemplate,
    'bootstrap-carousel': carouselTemplate,
    'bootstrap-button-group': buttonGroupTemplate,
    'bootstrap-a-class': aClassTemplate,
    'bootstrap-countdown': countdownTemplate,
    'bootstrap-feature-list': featureListTemplate,
    'bootstrap-list': listTemplate,
    'bootstrap-metrics': metricsTemplate,
    'bootstrap-cta': ctaTemplate,
    'list-block': listTemplate,
    'richTextBlock': richTextTemplate,
    'socialFlowBlock': socialFlowTemplate,
    'testimonialSliderBlock': testimonialSliderTemplate,
    'timelineBlock': timelineTemplate,
    'statsCardBlock': statsCardTemplate,
    'navbarBlock': navbarTemplate,
    'heroBlock': heroTemplate,
    'featureCardsBlock': featureCardsTemplate,
    'infoSectionBlock': infoSectionTemplate,
    'footerBlock': footerTemplate,
    'bootstrap-image': imageTemplate,
    'team-block': teamTemplate,
    'partners': partnersTemplate
  }

  switch (type) {
    case 'bootstrap-headline':
      return '<div data-bs-component="headline" class="py-5 bootstrap-headline"><div class="p-0 container-fluid"><div class="row justify-content-center"><div class="col-12 col-md-10 col-lg-8"><h2 class="headline-title">标题</h2><p class="headline-subtitle">副标题内容，添加一些额外的描述信息。</p></div></div></div></div>'
    case 'bootstrap-heading':
      return '<div data-bs-component="heading" class="bootstrap-heading"><div class="p-0 container-fluid"><div class="row justify-content-center"><div class="col-12 col-md-10 col-lg-8"><h1 class="heading-title">清晰醒目的标题</h1></div></div></div></div>'
    case 'bootstrap-pricing':
      return pricingTemplate
    default:
      return templates[type] || null;
  }
}