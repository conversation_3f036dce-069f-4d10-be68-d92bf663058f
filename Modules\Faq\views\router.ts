import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/faq',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '内容管理', icon: 'datareport' },
        children: [
            {
                path: 'list',
                name: '<PERSON>aq<PERSON>ist',
                meta: { title: 'FAQ列表' },
                component: () => import('./ui/list.vue'),
            },
            {
                path: 'create',
                name: 'FaqCreate',
                meta: { title: 'FAQ新增' },
                component: () => import('./ui/create.vue'),
            },
            {
                path: 'edit/:id',
                name: 'FaqEdit',
                meta: { title: 'FAQ編輯' },
                component: () => import('./ui/create.vue'),
            },
            {
                path: 'category',
                name: 'FaqCategory',
                meta: { title: 'FAQ分類管理' },
                component: () => import('./ui/category.vue'),
            }
        ]
    }
]

export default router
