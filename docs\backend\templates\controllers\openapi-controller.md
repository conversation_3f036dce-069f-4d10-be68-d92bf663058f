# OpenApi 控制器模板

## 概述

OpenApi 控制器用于处理开放 API 接口的请求。本文档提供了 OpenApi 控制器的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\OpenApi\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\YourModule\OpenApi\Requests\ListRequest;
use Modules\YourModule\OpenApi\Requests\CreateRequest;
use Modules\YourModule\OpenApi\Requests\UpdateRequest;
use Modules\YourModule\OpenApi\Requests\SyncRequest;
use Modules\YourModule\OpenApi\Resources\YourResource;
use Modules\YourModule\Domain\Services\YourService;
use Modules\YourModule\Domain\Repositories\YourRepository;

final class YourController extends Controller
{
    public function __construct(
        private readonly YourService $service,
        private readonly YourRepository $repository
    ) {
    }

    /**
     * 获取列表
     */
    public function index(ListRequest $request): JsonResponse
    {
        $data = $this->repository->paginate($request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => YourResource::collection($data)
        ]);
    }

    /**
     * 获取详情
     */
    public function show(int $id): JsonResponse
    {
        $data = $this->repository->findById($id);
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => new YourResource($data)
        ]);
    }

    /**
     * 创建记录
     */
    public function store(CreateRequest $request): JsonResponse
    {
        $data = $this->service->create($request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => new YourResource($data)
        ]);
    }

    /**
     * 更新记录
     */
    public function update(int $id, UpdateRequest $request): JsonResponse
    {
        $data = $this->service->update($id, $request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => new YourResource($data)
        ]);
    }

    /**
     * 删除记录
     */
    public function destroy(int $id): JsonResponse
    {
        $this->service->delete($id);
        
        return response()->json([
            'code' => 0,
            'message' => 'success'
        ]);
    }

    /**
     * 查询是否存在
     */
    public function exists(ListRequest $request): JsonResponse
    {
        $exists = $this->repository->exists($request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => ['exists' => $exists]
        ]);
    }

    /**
     * 同步推送
     */
    public function syncPush(SyncRequest $request): JsonResponse
    {
        $result = $this->service->syncPush($request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 同步拉取
     */
    public function syncPull(SyncRequest $request): JsonResponse
    {
        $result = $this->service->syncPull($request->validated());
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $result
        ]);
    }
}
```

## 请求验证类示例

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\OpenApi\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'keyword' => ['sometimes', 'string', 'max:100'],
            'status' => ['sometimes', 'string', Rule::in(['active', 'inactive'])],
            'start_time' => ['sometimes', 'date_format:Y-m-d H:i:s'],
            'end_time' => ['sometimes', 'date_format:Y-m-d H:i:s', 'after_or_equal:start_time'],
            'sort_field' => ['sometimes', 'string', Rule::in(['id', 'created_at', 'updated_at'])],
            'sort_order' => ['sometimes', 'string', Rule::in(['asc', 'desc'])],
            'with' => ['sometimes', 'array'],
            'with.*' => ['string', Rule::in(['creator', 'updater', 'category', 'tags'])],
        ];
    }

    public function messages(): array
    {
        return [
            'page.integer' => T('YourModule::validation.page.integer'),
            'page.min' => T('YourModule::validation.page.min'),
            'limit.integer' => T('YourModule::validation.limit.integer'),
            'limit.min' => T('YourModule::validation.limit.min'),
            'limit.max' => T('YourModule::validation.limit.max'),
            'keyword.string' => T('YourModule::validation.keyword.string'),
            'keyword.max' => T('YourModule::validation.keyword.max'),
            'status.string' => T('YourModule::validation.status.string'),
            'status.in' => T('YourModule::validation.status.in'),
            'start_time.date_format' => T('YourModule::validation.start_time.date_format'),
            'end_time.date_format' => T('YourModule::validation.end_time.date_format'),
            'end_time.after_or_equal' => T('YourModule::validation.end_time.after_or_equal'),
            'sort_field.string' => T('YourModule::validation.sort_field.string'),
            'sort_field.in' => T('YourModule::validation.sort_field.in'),
            'sort_order.string' => T('YourModule::validation.sort_order.string'),
            'sort_order.in' => T('YourModule::validation.sort_order.in'),
            'with.array' => T('YourModule::validation.with.array'),
            'with.*.string' => T('YourModule::validation.with.*.string'),
            'with.*.in' => T('YourModule::validation.with.*.in'),
        ];
    }
}

class CreateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'status' => ['required', 'string', Rule::in(['active', 'inactive'])],
            'category_id' => ['required', 'integer', Rule::exists('categories', 'id')],
            'tags' => ['nullable', 'array', 'max:10'],
            'tags.*' => ['integer', Rule::exists('tags', 'id')],
            'attributes' => ['nullable', 'array'],
            'attributes.*.key' => ['required', 'string', 'max:50'],
            'attributes.*.value' => ['required', 'string', 'max:255'],
            'external_id' => [
                'nullable', 
                'string', 
                'max:100',
                Rule::unique('your_table', 'external_id')->where(function ($query) {
                    return $query->where('tenant_id', $this->user()->tenant_id);
                }),
            ],
            'metadata' => ['nullable', 'array'],
            'metadata.*' => ['string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => T('YourModule::validation.title.required'),
            'title.string' => T('YourModule::validation.title.string'),
            'title.max' => T('YourModule::validation.title.max'),
            'description.string' => T('YourModule::validation.description.string'),
            'description.max' => T('YourModule::validation.description.max'),
            'status.required' => T('YourModule::validation.status.required'),
            'status.string' => T('YourModule::validation.status.string'),
            'status.in' => T('YourModule::validation.status.in'),
            'category_id.required' => T('YourModule::validation.category_id.required'),
            'category_id.integer' => T('YourModule::validation.category_id.integer'),
            'category_id.exists' => T('YourModule::validation.category_id.exists'),
            'tags.array' => T('YourModule::validation.tags.array'),
            'tags.max' => T('YourModule::validation.tags.max'),
            'tags.*.integer' => T('YourModule::validation.tags.*.integer'),
            'tags.*.exists' => T('YourModule::validation.tags.*.exists'),
            'attributes.array' => T('YourModule::validation.attributes.array'),
            'attributes.*.key.required' => T('YourModule::validation.attributes.*.key.required'),
            'attributes.*.key.string' => T('YourModule::validation.attributes.*.key.string'),
            'attributes.*.key.max' => T('YourModule::validation.attributes.*.key.max'),
            'attributes.*.value.required' => T('YourModule::validation.attributes.*.value.required'),
            'attributes.*.value.string' => T('YourModule::validation.attributes.*.value.string'),
            'attributes.*.value.max' => T('YourModule::validation.attributes.*.value.max'),
            'external_id.string' => T('YourModule::validation.external_id.string'),
            'external_id.max' => T('YourModule::validation.external_id.max'),
            'external_id.unique' => T('YourModule::validation.external_id.unique'),
            'metadata.array' => T('YourModule::validation.metadata.array'),
            'metadata.*.string' => T('YourModule::validation.metadata.*.string'),
            'metadata.*.max' => T('YourModule::validation.metadata.*.max'),
        ];
    }
}

class SyncRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'items' => ['required', 'array', 'min:1', 'max:1000'],
            'items.*.id' => ['required', 'integer'],
            'items.*.external_id' => ['required', 'string', 'max:100'],
            'items.*.type' => ['required', 'string', Rule::in(['create', 'update', 'delete'])],
            'items.*.data' => ['required_unless:items.*.type,delete', 'array'],
            'items.*.data.title' => ['required_if:items.*.type,create,update', 'string', 'max:255'],
            'items.*.data.description' => ['nullable', 'string', 'max:1000'],
            'items.*.data.status' => [
                'required_if:items.*.type,create,update',
                'string',
                Rule::in(['active', 'inactive']),
            ],
            'items.*.data.category_id' => [
                'required_if:items.*.type,create,update',
                'integer',
                Rule::exists('categories', 'id'),
            ],
            'items.*.data.tags' => ['nullable', 'array', 'max:10'],
            'items.*.data.tags.*' => ['integer', Rule::exists('tags', 'id')],
            'items.*.data.attributes' => ['nullable', 'array'],
            'items.*.data.attributes.*.key' => ['required', 'string', 'max:50'],
            'items.*.data.attributes.*.value' => ['required', 'string', 'max:255'],
            'items.*.data.metadata' => ['nullable', 'array'],
            'items.*.data.metadata.*' => ['string', 'max:255'],
            'sync_token' => ['required', 'string'],
            'sync_type' => ['required', 'string', Rule::in(['full', 'incremental'])],
        ];
    }

    public function messages(): array
    {
        return [
            'items.required' => T('YourModule::validation.items.required'),
            'items.array' => T('YourModule::validation.items.array'),
            'items.min' => T('YourModule::validation.items.min'),
            'items.max' => T('YourModule::validation.items.max'),
            'items.*.id.required' => T('YourModule::validation.items.*.id.required'),
            'items.*.id.integer' => T('YourModule::validation.items.*.id.integer'),
            'items.*.external_id.required' => T('YourModule::validation.items.*.external_id.required'),
            'items.*.external_id.string' => T('YourModule::validation.items.*.external_id.string'),
            'items.*.external_id.max' => T('YourModule::validation.items.*.external_id.max'),
            'items.*.type.required' => T('YourModule::validation.items.*.type.required'),
            'items.*.type.string' => T('YourModule::validation.items.*.type.string'),
            'items.*.type.in' => T('YourModule::validation.items.*.type.in'),
            'items.*.data.required_unless' => T('YourModule::validation.items.*.data.required_unless'),
            'items.*.data.array' => T('YourModule::validation.items.*.data.array'),
            'items.*.data.title.required_if' => T('YourModule::validation.items.*.data.title.required_if'),
            'items.*.data.title.string' => T('YourModule::validation.items.*.data.title.string'),
            'items.*.data.title.max' => T('YourModule::validation.items.*.data.title.max'),
            'items.*.data.description.string' => T('YourModule::validation.items.*.data.description.string'),
            'items.*.data.description.max' => T('YourModule::validation.items.*.data.description.max'),
            'items.*.data.status.required_if' => T('YourModule::validation.items.*.data.status.required_if'),
            'items.*.data.status.string' => T('YourModule::validation.items.*.data.status.string'),
            'items.*.data.status.in' => T('YourModule::validation.items.*.data.status.in'),
            'items.*.data.category_id.required_if' => T('YourModule::validation.items.*.data.category_id.required_if'),
            'items.*.data.category_id.integer' => T('YourModule::validation.items.*.data.category_id.integer'),
            'items.*.data.category_id.exists' => T('YourModule::validation.items.*.data.category_id.exists'),
            'items.*.data.tags.array' => T('YourModule::validation.items.*.data.tags.array'),
            'items.*.data.tags.max' => T('YourModule::validation.items.*.data.tags.max'),
            'items.*.data.tags.*.integer' => T('YourModule::validation.items.*.data.tags.*.integer'),
            'items.*.data.tags.*.exists' => T('YourModule::validation.items.*.data.tags.*.exists'),
            'items.*.data.attributes.array' => T('YourModule::validation.items.*.data.attributes.array'),
            'items.*.data.attributes.*.key.required' => T('YourModule::validation.items.*.data.attributes.*.key.required'),
            'items.*.data.attributes.*.key.string' => T('YourModule::validation.items.*.data.attributes.*.key.string'),
            'items.*.data.attributes.*.key.max' => T('YourModule::validation.items.*.data.attributes.*.key.max'),
            'items.*.data.attributes.*.value.required' => T('YourModule::validation.items.*.data.attributes.*.value.required'),
            'items.*.data.attributes.*.value.string' => T('YourModule::validation.items.*.data.attributes.*.value.string'),
            'items.*.data.attributes.*.value.max' => T('YourModule::validation.items.*.data.attributes.*.value.max'),
            'items.*.data.metadata.array' => T('YourModule::validation.items.*.data.metadata.array'),
            'items.*.data.metadata.*.string' => T('YourModule::validation.items.*.data.metadata.*.string'),
            'items.*.data.metadata.*.max' => T('YourModule::validation.items.*.data.metadata.*.max'),
            'sync_token.required' => T('YourModule::validation.sync_token.required'),
            'sync_token.string' => T('YourModule::validation.sync_token.string'),
            'sync_type.required' => T('YourModule::validation.sync_type.required'),
            'sync_type.string' => T('YourModule::validation.sync_type.string'),
            'sync_type.in' => T('YourModule::validation.sync_type.in'),
        ];
    }
}
```

## 规范要求

1. 类声明
   - 必须使用 `declare(strict_types=1)`
   - 必须是 `final` 类
   - 使用构造函数注入依赖

2. 方法规范
   - 方法名遵循 RESTful 规范
   - 返回类型必须是 `JsonResponse`
   - 使用 FormRequest 进行参数验证
   - 使用 Resource 进行响应转换

3. 错误处理
   - 使用业务异常
   - 统一错误码
   - 友好错误信息
   - 日志记录

4. 输入处理
   - 使用 FormRequest 进行参数验证
   - 类型转换
   - 默认值处理
   - 安全过滤

## 最佳实践

1. 请求验证
```php
class UpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'status' => [
                'sometimes', 
                'required', 
                'string',
                Rule::in(['active', 'inactive']),
                function (string $attribute, mixed $value, Closure $fail) {
                    if ($value === 'inactive' && !$this->user()->can('deactivate_items')) {
                        $fail(T('YourModule::validation.status.permission_denied'));
                    }
                },
            ],
        ];
    }
}
```

2. 响应转换
```php
class YourResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'status' => $this->status,
            'category' => new CategoryResource($this->whenLoaded('category')),
            'tags' => TagResource::collection($this->whenLoaded('tags')),
            'attributes' => $this->attributes,
            'metadata' => $this->metadata,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
```

3. 批量同步
```php
class SyncController extends Controller
{
    public function push(SyncRequest $request): JsonResponse
    {
        $result = DB::transaction(function () use ($request) {
            $items = collect($request->validated('items'));
            
            // 处理创建
            $createItems = $items->where('type', 'create');
            $this->service->batchCreate($createItems->pluck('data')->all());
            
            // 处理更新
            $updateItems = $items->where('type', 'update');
            $this->service->batchUpdate($updateItems->pluck('id')->all(), $updateItems->pluck('data')->all());
            
            // 处理删除
            $deleteItems = $items->where('type', 'delete');
            $this->service->batchDelete($deleteItems->pluck('id')->all());
            
            return [
                'success' => true,
                'sync_token' => Str::random(32),
            ];
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $result,
        ]);
    }
}
```

## 注意事项

1. 控制器职责
   - 参数验证和处理
   - 调用服务层方法
   - 响应转换
   - 错误处理

2. 安全考虑
   - 输入数据过滤
   - 权限检查
   - API 认证
   - 数据加密

3. 性能优化
   - 合理分页
   - 缓存使用
   - N+1问题避免
   - 大数据处理

4. 代码质量
   - 单一职责
   - 代码复用
   - 测试覆盖
   - 注释完整