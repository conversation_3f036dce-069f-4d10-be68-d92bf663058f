import { mergeAttributes, Node } from '@tiptap/core'

export interface TeamBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    teamBlock: {
      /**
       * Add a team block
       */
      setTeamBlock: (attributes?: { }) => ReturnType
    }
  }
}

export const TeamBlock = Node.create<TeamBlockOptions>({
  name: 'teamBlock',

  group: 'block',

  content: 'block+',

  draggable: true,

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="team-block"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-bs-component': 'team-block' }), 0]
  },

  addCommands() {
    return {
      setTeamBlock:
        attributes => ({ commands }) => {
          return commands.setNode(this.name, attributes)
        },
    }
  },
})

export default TeamBlock 