<?php
// ========================================
// 权限系统路由配置
// ========================================

// routes/api.php

use Modules\Users\Api\Controller\PenNameController;


Route::prefix('admin')->group(function () {
    Route::prefix('pen-names')->group(function () {
        Route::get('/', [PenNameController::class, 'index'])->name('pen-names.index');
        Route::post('/', [PenNameController::class, 'store'])->name('pen-names.store');
        Route::get('/{id}', [PenNameController::class, 'show'])->name('pen-names.show');
        Route::put('/{id}', [PenNameController::class, 'update'])->name('pen-names.update');
        Route::delete('/{id}', [PenNameController::class, 'destroy'])->name('pen-names.destroy');
        Route::post('/{id}/switch', [PenNameController::class, 'switch'])->name('pen-names.switch');
    });
});
