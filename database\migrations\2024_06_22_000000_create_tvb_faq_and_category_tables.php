<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// 创建tvb_faq和tvb_faq_category表的迁移文件
// 本文件由AI自动生成，所有字段均带中文注释
return new class extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        // 创建常见问题表
        Schema::create('faq', function (Blueprint $table) {
            $table->increments('id')->comment('常见问题ID');
            $table->string('code', 255)->comment('问题代码');
            $table->integer('category_id')->comment('分类ID');
            $table->string('title', 255)->comment('问题标题');
            $table->text('content')->comment('问题内容');
            $table->tinyInteger('status')->default(0)->comment('状态: 0-草稿, 1-已发布, 2-已下架');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->integer('created_by')->comment('创建人ID');
            $table->integer('updated_by')->comment('更新人ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间');
            $table->unique(['code', 'deleted_at'], 'uk_code');
        });

        // 创建常见问题分类表
        Schema::create('faq_category', function (Blueprint $table) {
            $table->increments('id')->comment('分类ID');
            $table->string('name', 255)->comment('分类名称');
            $table->tinyInteger('status')->default(1)->comment('状态: 0-禁用, 1-启用');
            $table->integer('parent_id')->comment('父级分类ID');
            $table->integer('root_id')->comment('根分类ID');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间');
        });
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('faq');
        Schema::dropIfExists('faq_category');
    }
}; 