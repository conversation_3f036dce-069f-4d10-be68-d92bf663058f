export const navbarTemplate = `<nav data-bs-component="navbar" class="navbar-section responsive-block">
  <div class="container">
    <div class="navbar-wrapper">
      <a class="navbar-brand" href="#">
        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hub/7528302/hubfs/theme_hubspot/elevate/images/hexagontalxio-dark.png" height="30" alt="Logo">
      </a>
      <div class="navbar-links">
        <a class="nav-link" href="#">首页</a>
        <a class="nav-link" href="#">功能</a>
        <a class="nav-link" href="#">价格</a>
        <a class="nav-link" href="#">关于</a>
        <button class="btn btn-outline-primary">登录</button>
        <button class="btn btn-primary">立即开始</button>
      </div>
    </div>
  </div>

  <style>
    .navbar-section {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      box-shadow: 0 1px 0 rgba(0,0,0,0.05);
      z-index: 1030;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    .navbar-section .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1.5rem;
    }

    .navbar-section .navbar-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 70px;
    }

    .navbar-section .navbar-brand {
      padding: 0;
      margin-right: 2rem;
    }

    .navbar-section .navbar-links {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .navbar-section .nav-link {
      color: #4B5563;
      font-weight: 500;
      font-size: 0.95rem;
      text-decoration: none;
      transition: all 0.2s ease;
      padding: 0.5rem 0;
    }

    .navbar-section .nav-link:hover {
      color: #2563EB;
    }

    .navbar-section .btn {
      padding: 0.5rem 1.25rem;
      font-weight: 500;
      font-size: 0.95rem;
      transition: all 0.2s ease;
      border-radius: 6px;
    }

    .navbar-section .btn-outline-primary {
      color: #2563EB;
      border: 1.5px solid #2563EB;
      background: transparent;
    }

    .navbar-section .btn-outline-primary:hover {
      color: #fff;
      background: #2563EB;
    }

    .navbar-section .btn-primary {
      color: #fff;
      background: #2563EB;
      border: none;
    }

    .navbar-section .btn-primary:hover {
      background: #1D4ED8;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    }

    @media (max-width: 768px) {
      .navbar-section .navbar-wrapper {
        height: auto;
        padding: 1rem 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .navbar-section .navbar-brand {
        margin-right: 0;
      }

      .navbar-section .navbar-links {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .navbar-section .btn {
        width: 100%;
      }
    }
  </style>
</nav>` 