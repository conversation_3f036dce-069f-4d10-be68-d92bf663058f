import { User, SecuritySetting, Application } from '../domain/UserCenterDomain'
import { UserCenterRepository } from '../infrastructure/UserCenterRepository'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'

export class UserCenterService {
  private repository: UserCenterRepository

  constructor() {
    this.repository = new UserCenterRepository()
  }

  async getUserInfo(): Promise<User> {
    try {
      const response = await http.get('/iam/users/get-profile?withCustomData=true&withIdentities=true&withDepartmentIds=true')
      return response.data.data
    } catch (error) {
      throw error
    }
  }

  async updateUserInfo(user: Partial<User>): Promise<void> {
    try {
      const response = await http.put('/iam/users/update-profile', user)
      if (response.data.code === 200) {
        ElMessage.success('个人信息更新成功')
        return response.data
      } else {
        throw new Error(response.data.message || '更新失败')
      }
    } catch (error) {
      throw error
    }
  }

  async sendSmsCode(phoneNumber: string, channel: string, phoneCountryCode: string, appCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/send-sms', {
        phoneNumber,
        channel,
        phoneCountryCode,
        app_code: appCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('验证码已发送')
      } else {
        throw new Error(response.data.message || '发送验证码失败')
      }
    } catch (error) {
      throw error
    }
  }

  async sendEmailCode(email: string, channel: string, appCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/send-email-verify-code', {
        email,
        channel,
        app_code: appCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('验证码已发送到您的邮箱')
      } else {
        throw new Error(response.data.message || '发送验证码失败')
      }
    } catch (error) {
      throw error
    }
  }

  async bindPhone(phoneNumber: string, passCode: string, phoneCountryCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/bind-phone', {
        phoneNumber,
        passCode,
        phoneCountryCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('手机号绑定成功')
      } else {
        throw new Error(response.data.message || '绑定手机号失败')
      }
    } catch (error) {
      throw error
    }
  }

  async bindEmail(email: string, passCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/bind-email', {
        email,
        passCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('邮箱绑定成功')
      } else {
        throw new Error(response.data.message || '绑定邮箱失败')
      }
    } catch (error) {
      throw error
    }
  }

  async unbindPhone(passCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/unbind-phone', {
        passCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('手机号解绑成功')
      } else {
        throw new Error(response.data.message || '解绑手机号失败')
      }
    } catch (error) {
      throw error
    }
  }

  async unbindEmail(passCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/unbind-email', {
        passCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('邮箱解绑成功')
      } else {
        throw new Error(response.data.message || '解绑邮箱失败')
      }
    } catch (error) {
      throw error
    }
  }

  async verifyEmail(email: string, passCode: string): Promise<void> {
    try {
      const response = await http.post('/iam/verify-email', {
        email,
        passCode,
      })
      if (response.data.code === 200) {
        ElMessage.success('邮箱验证成功')
      } else {
        throw new Error(response.data.message || '邮箱验证失败')
      }
    } catch (error) {
      throw error
    }
  }

  async getLoginHistory(data: any): Promise<any> {
    try {
      const response = await http.get('/iam/users/login/log', data)
      return response.data
    } catch (error) {
      throw error
    }
  }

  async updatePassword(oldPassword: string, newPassword: string): Promise<void> {
    try {
      const response = await http.post('/iam/users/update-password', {
        oldPassword,
        newPassword,
        passwordEncryptType: 'none',
        passwordResetToken: '',
      })
      if (response.data.code === 200) {
        ElMessage.success('密码修改成功')
      } else {
        throw new Error(response.data.message || '密码修改失败')
      }
    } catch (error) {
      throw error
    }
  }

  async verifyDeleteAccountRequest(payload: {
    verifyMethod: string
    phonePassCodePayload: {
      phoneNumber: string
      passCode: string
      phoneCountryCode: string
    }
    emailPassCodePayload: {
      email: string
      passCode: string
    }
    passwordPayload: {
      password: string
      passwordEncryptType: string
    }
  }): Promise<void> {
    try {
      const response = await http.post('/iam/users/verify-delete-account-request', payload)
      if (response.data.code === 200) {
        ElMessage.success('账号注销请求已提交')
      } else {
        throw new Error(response.data.message || '账号注销请求失败')
      }
    } catch (error) {
      throw error
    }
  }

  async getSecurityInfo(): Promise<SecuritySetting> {
    try {
      const response = await http.get('/iam/users/get-security-info')
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取安全信息失败')
      }
    } catch (error) {
      throw error
    }
  }

  async getOtpEnrollmentQrCode(data: any): Promise<any> {
    try {
      const response = await http.post('/iam/mfa/send-enroll-factor-request', data)
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取 OTP 二维码失败')
      }
    } catch (error) {}
  }

  async verifyOtpCode(totp: any): Promise<void> {
    try {
      const response = await http.post('/iam/mfa/enroll-factor', totp)
      if (response.data.code === 200) {
        ElMessage.success('OTP 验证成功')
        return response.data.data
      } else {
        throw new Error(response.data.message || 'OTP 验证失败')
      }
    } catch (error) {
      throw error
    }
  }

  async resetFactor(factorId: string): Promise<void> {
    try {
      const response = await http.post('/iam/reset-factor', { factorId })
      if (response.data.code === 200) {
        ElMessage.success('OTP 解绑成功')
        return response.data.data
      } else {
        throw new Error(response.data.message || 'OTP 解绑失败')
      }
    } catch (error) {
      throw error
    }
  }
}
