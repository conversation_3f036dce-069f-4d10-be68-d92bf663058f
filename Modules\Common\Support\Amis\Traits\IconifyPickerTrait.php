<?php

namespace Modules\Common\Support\Amis\Traits;

use Bingo\Amis\Renderers\Button;
use Bingo\Amis\Renderers\Card;
use Bingo\Amis\Renderers\CRUD;
use Bingo\Amis\Renderers\CustomSvgIcon;
use Bingo\Amis\Renderers\Form\AmisForm;
use Bingo\Amis\Renderers\Form\Group;
use Bingo\Amis\Renderers\Form\InputText;
use Bingo\Amis\Renderers\Form\Picker;

/**
 * Trait IconifyPickerTrait
 * @package Modules\Common\Support\Amis\Traits
 * 使用方法
 * $this->iconifyPicker('icon', T('Develop::gen.tabs.router_config.icon.label'))->value('ph:circle')
 */
trait IconifyPickerTrait
{
    /**
     * iconify 图标选择器
     * @param string $name
     * @param string $label
     * @return Picker
     */
    public function iconifyPicker(string $name = '', string $label = ''): Picker
    {
        return Picker::make()->name($name)->label($label)
            ->valueField('icon')->required(true)->labelField('icon')->size('lg')
            ->pickerSchema(
                CRUD::make()->mode('cards')->perPage('40')->loadDataOnce(true)->columnsCount(8)->footerToolbar([
                    "statistics", "pagination"
                ])->api('develop/_iconify_search')->filter(
                    AmisForm::make()->wrapWithPanel(false)->body(
                        [
                            Group::make()->body([
                                InputText::make()->name('query')->placeholder('Search icon name')->size('md')->value('${icon || "home"}')->clearable(true)->required(true),
                                Button::make()->label('Search')->actionType('submit')->icon('fa fa-search')->size('md')->level('primary'),
                                Button::make()->label('Icones')->actionType('url')->icon('fa fa-external-link-alt')->blank(true)->url('https://icones.js.org/collection/all')->className('ml-2'),
                            ])->className('pt-3 pb-3')
                        ]
                    )
                )->card(
                    Card::make()->body([
                        CustomSvgIcon::make()->icon('${icon}')
                    ])
                )
            )->source('develop/_iconify_search');
    }

}
