<template>
  <div class="chat-bot-container">
    <div id="chatbot-container"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { env, getAuthToken } from '/admin/support/helper'
import mitt from 'mitt'

// 在组件初始化之前设置错误拦截
const originalConsoleError = console.error.bind(console)
const originalOnError = window.onerror

// 设置 console.error 拦截
console.error = (...args: any[]) => {
  if (typeof args[0] === 'string' && (args[0].includes('useEffect') || args[0].includes('React') || args[0].includes('Cannot read properties of null') || args[0].includes('Invalid hook call'))) {
    return
  }
  originalConsoleError(...args)
}

// 设置 window.onerror 拦截
window.onerror = function (message, source, lineno, colno, error) {
  if (typeof message === 'string' && (message.includes('React') || message.includes('useEffect') || message.includes('Cannot read properties of null'))) {
    return true
  }
  return originalOnError ? originalOnError.apply(this, arguments as any) : false
}

// 设置未处理的 Promise 错误拦截
window.addEventListener('unhandledrejection', function (event) {
  if (
    event.reason &&
    typeof event.reason.message === 'string' &&
    (event.reason.message.includes('React') || event.reason.message.includes('useEffect') || event.reason.message.includes('Cannot read properties of null'))
  ) {
    event.preventDefault()
  }
})

type Events = {
  'language-change': string
}

const emitter = mitt<Events>()
const tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const BaseUrl = ref<string>(env('VITE_BASE_URL'))
const currentLanguage = ref(localStorage.getItem('bwms_language') || 'zh_HK')

// 监听跨页面的语言变化
const handleStorageChange = (event: StorageEvent) => {
  if (event.key === 'bwms_language' && event.newValue) {
    updateLanguage(event.newValue)
  }
}

// 监听当前页面的语言变化
const handleLocalLanguageChange = () => {
  const originalSetItem = localStorage.setItem.bind(localStorage)
  localStorage.setItem = function (key: string, value: string) {
    if (key === 'bwms_language') {
      updateLanguage(value)
    }
    originalSetItem(key, value)
  }
  return originalSetItem
}

interface ChatWidgetConfig {
  url: string
  token?: string | null
  appCode?: string | null
  userId?: number
  language?: string | null
}

declare global {
  interface Window {
    chatWidgetConfig: ChatWidgetConfig
    React: any
    ReactDOM: any
    dispatchChatWidgetConfigChange?: () => void
    updateLanguage?: (lang: string) => void
  }
}

const loadScript = async (src: string): Promise<void> => {
  if (document.querySelector(`script[src="${src}"]`)) {
    return Promise.resolve()
  }

  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = src
    script.async = true
    script.crossOrigin = 'anonymous'
    script.onload = () => resolve()
    script.onerror = error => reject(error)
    document.head.appendChild(script)
  })
}

const loadCSS = (href: string): Promise<void> => {
  if (document.querySelector(`link[href="${href}"]`)) {
    return Promise.resolve()
  }

  return new Promise(resolve => {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    link.crossOrigin = 'anonymous'
    link.onload = () => resolve()
    document.head.appendChild(link)
  })
}

const configureChatWidget = () => {
  window.chatWidgetConfig = {
    url: BaseUrl.value,
    token: token.value,
    language: currentLanguage.value,
  }

  if (window.dispatchChatWidgetConfigChange) {
    window.dispatchChatWidgetConfigChange()
  }
}

// 定义更新语言的方法
const updateLanguage = (lang: string) => {
  if (lang && lang !== currentLanguage.value) {
    currentLanguage.value = lang
    configureChatWidget()
  }
}

// 在window对象上暴露更新语言的方法
window.updateLanguage = updateLanguage

// 监听语言变化事件
emitter.on('language-change', (lang: string) => {
  updateLanguage(lang)
})

onMounted(async () => {
  try {
    await Promise.all([loadCSS('/ChatBot/frontend-chatbot.css')])

    await loadScript('/ChatBot/frontend-chatbot.umd.js')

    configureChatWidget()

    window.addEventListener('storage', handleStorageChange)
    const originalSetItem = handleLocalLanguageChange()
    ;(window as any).__originalSetItem = originalSetItem
  } catch (error) {
    console.error('Failed to load chat widget:', error)
  }
})

onBeforeUnmount(() => {
  // 恢复原始的错误处理
  console.error = originalConsoleError
  window.onerror = originalOnError

  // 清理事件监听和其他资源
  emitter.all.clear()
  window.removeEventListener('storage', handleStorageChange)
  window.removeEventListener('unhandledrejection', () => {})

  if ((window as any).__originalSetItem) {
    localStorage.setItem = (window as any).__originalSetItem
    delete (window as any).__originalSetItem
  }

  delete window.updateLanguage

  const scripts = document.querySelectorAll('script[src*="unpkg.com"], script[src*="chatbot"]')
  scripts.forEach(script => script.remove())

  const styles = document.querySelectorAll('link[href*="chatbot"]')
  styles.forEach(style => style.remove())

  const root = document.getElementById('chatbot-widget-root')
  if (root) {
    root.remove()
  }
})
</script>