# 详情页面模板

## 概述

详情页面用于展示数据的详细信息。本文档提供了详情页面的标准模板和最佳实践。

## 基本结构

```vue
<template>
  <div class="detail-page">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>详情</span>
          <el-button @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-descriptions
        :column="2"
        border
        class="detail-content"
      >
        <el-descriptions-item label="名称">
          {{ detailData.name }}
        </el-descriptions-item>
        
        <el-descriptions-item label="状态">
          <el-tag :type="detailData.status ? 'success' : 'danger'">
            {{ detailData.status ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(detailData.createTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(detailData.updateTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="备注" :span="2">
          {{ detailData.remark || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="detail-actions">
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDetailStore } from '@/stores/detail'
import { formatDateTime } from '@/utils/date'

// 路由
const route = useRoute()
const router = useRouter()
const detailStore = useDetailStore()

// 状态
const loading = ref(false)

// 详情数据
interface DetailData {
  id: string | number
  name: string
  status: number
  remark: string
  createTime: string
  updateTime: string
}

const detailData = ref<DetailData>({
  id: '',
  name: '',
  status: 1,
  remark: '',
  createTime: '',
  updateTime: ''
})

// 获取详情
const getDetail = async () => {
  try {
    loading.value = true
    const data = await detailStore.getDetail(route.params.id as string)
    detailData.value = data
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/form/${detailData.value.id}`)
}

// 删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确认删除该记录吗？', '提示', {
      type: 'warning'
    })
    await detailStore.deleteItem(detailData.value.id)
    ElMessage.success('删除成功')
    goBack()
  } catch {
    // 用户取消删除
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  getDetail()
})
</script>

<style lang="scss" scoped>
.detail-page {
  padding: 20px;
  
  .detail-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .detail-content {
      margin-bottom: 20px;
    }
    
    .detail-actions {
      display: flex;
      justify-content: center;
      gap: 20px;
    }
  }
}
</style>
```

## 规范要求

1. 页面结构
   - 标题区域
   - 详情内容
   - 操作按钮
   - 返回功能

2. 数据管理
   - 使用 Pinia Store
   - 响应式数据
   - 类型定义
   - 数据格式化

3. 功能实现
   - 数据展示
   - 编辑跳转
   - 删除确认
   - 返回功能

4. 交互处理
   - 加载状态
   - 错误处理
   - 操作确认
   - 结果反馈

## 最佳实践

1. Store 定义
```typescript
import { defineStore } from 'pinia'
import { getDetail, deleteItem } from '@/api/detail'

export const useDetailStore = defineStore('detail', {
  actions: {
    async getDetail(id: string) {
      return await getDetail(id)
    },
    
    async deleteItem(id: string | number) {
      return await deleteItem(id)
    }
  }
})
```

2. API 定义
```typescript
import request from '@/utils/request'

export const getDetail = (id: string) => {
  return request({
    url: `/api/items/${id}`,
    method: 'get'
  })
}

export const deleteItem = (id: string | number) => {
  return request({
    url: `/api/items/${id}`,
    method: 'delete'
  })
}
```

## 注意事项

1. 数据展示要完整
2. 错误提示要友好
3. 删除要二次确认
4. 注意数据类型
5. 处理异常情况
6. 保持代码整洁
7. 样式要规范
8. 考虑页面性能
9. 数据格式化
10. 组件要可复用
