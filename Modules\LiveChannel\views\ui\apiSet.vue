<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </el-button>
    </div>
    
    <div class="module-con">
      <div class="box">
        <el-form 
          :model="apiConfig" 
          :rules="formRules" 
          ref="apiConfigFormRef" 
          label-position="top"
        >
          <el-form-item label="API 端點" prop="endpoint">
            <el-input 
              v-model="apiConfig.endpoint" 
              placeholder="例如: https://api.example.com/livestream"
              style="width: 60%;"
            />
          </el-form-item>
          
          <el-form-item label="API Key" prop="apiKey">
            <el-input 
              v-model="apiConfig.apiKey" 
              placeholder="輸入您的API Key"
              show-password
              style="width: 60%;"
            />
          </el-form-item>
          
          <el-form-item label="直播 ID 前綴" prop="prefix">
            <el-input 
              v-model="apiConfig.prefix" 
              placeholder="例如: live_"
              style="width: 60%;"
            />
          </el-form-item>
          
          <div class="flex justify-center" style="margin-top: 26px;">
            <el-button 
              type="primary" 
              @click="saveConfig" 
              :loading="saving"
            >
              保存配置
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import http from '/admin/support/http'

const router = useRouter()

// API配置数据
const apiConfig = reactive({
  endpoint: '',
  apiKey: '',
  prefix: ''
})

// 表单验证规则
const formRules = {
  endpoint: [
    { required: true, message: '請輸入API端點', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/,
      message: '請輸入有效的URL地址',
      trigger: 'blur'
    }
  ],
  apiKey: [
    { required: true, message: '請輸入API Key', trigger: 'blur' },
    { min: 8, message: 'API Key長度不能少於8位', trigger: 'blur' }
  ],
  prefix: [
    { required: true, message: '請輸入直播ID前綴', trigger: 'blur' },
    { max: 20, message: '前綴長度不能超過20位', trigger: 'blur' }
  ]
}

const apiConfigFormRef = ref()
const saving = ref(false)

// 获取API配置
const getApiConfig = async () => {
  try {
    const response = await http.get('/admin/live-channel/api-config')
    if (response.data && response.data.code === 200) {
      const data = response.data.data
      apiConfig.endpoint = data.endpoint || ''
      apiConfig.apiKey = data.apiKey || ''
      apiConfig.prefix = data.prefix || ''
    }
  } catch (error) {
    console.error('獲取API配置失敗:', error)
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    await apiConfigFormRef.value.validate()
    
    saving.value = true
    const response = await http.post('/admin/live-channel/api-config', apiConfig)
    
    if (response.data && response.data.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(response.data?.message || '保存失敗')
    }
  } catch (error) {
    if (error === false) {
      ElMessage.error('請檢查表單輸入')
    } else {
      ElMessage.error('保存失敗')
    }
  } finally {
    saving.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(() => {
  // getApiConfig()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}




</style>
