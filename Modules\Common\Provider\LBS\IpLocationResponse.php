<?php

namespace Modules\Common\Provider\LBS;

class IpLocationResponse
{
    public static array $keys = [
        'country',
        'province',
        'city',
        'district',
        'isp'
    ];

    public static function fromArray($data): static
    {
        $ret = new static();
        foreach (self::$keys as $key) {
            $ret->{$key} = $data[$key] ?? null;
        }
        return $ret;
    }

    public $country;
    public $province;
    public $city;
    public $district;
    public $isp;
}
