$(document).ready(function(){window.formEl=$(options.name);var progressEl=$("#progress");var barEl=$("#bar");var percentEl=$("#percent");var fieldset=$("fieldset");var current_fs,next_fs,previous_fs;var beganFilling=false;var startTime=0;var fp=null;var lat=null;var lng=null;formEl.attr("role","form");$("<input>").attr({type:"hidden",id:"_csrf",name:"_csrf",value:options._csrf}).appendTo(formEl);$('.form-check-input:required, .form-check-input[data-required="true"]').click(function(e){var field=$(this);var fieldType=field.attr("name").split("_",1).shift();var fieldGroup=fieldType==="matrix"?field.closest("tr"):field.closest(".form-group");if(fieldGroup.find(".form-check-input:checked").length>0){fieldGroup.find(".form-check-input").each(function(){$(this).get(0).setCustomValidity("")});fieldGroup.find(".invalid-feedback").hide()}else{fieldGroup.find(".form-check-input").each(function(){$(this).get(0).setCustomValidity("invalid")});fieldGroup.find(".invalid-feedback").show()}});$("input[type=file]").click(function(){$(this).get(0).setCustomValidity("")});if(options.text_direction==="rtl"){formEl.find(":input").attr("dir","rtl")}if(options.autocomplete){formEl.attr("autocomplete","on")}else{formEl.attr("autocomplete","off")}if(options.novalidate){formEl.attr("novalidate","novalidate")}else{if(formEl.attr("novalidate")!=="novalidate"){formEl.removeAttr("novalidate")}}if(options.resume){formEl.resume({key:"form_app_"+options.id})}if(options.fingerprint){Fingerprint2.get(function(components){fp=Fingerprint2.x64hash128(components.map(function(pair){return pair.value}).join(),31)})}if(options.geolocation&&navigator&&navigator.geolocation){navigator.geolocation.getCurrentPosition(function(position){lat=position.coords.latitude;lng=position.coords.longitude})}if(options.initialStep){var initialStep=parseInt(options.initialStep)-1;if(initialStep>0&&initialStep<fieldset.length){current_fs=formEl.find("fieldset:visible");next_fs=fieldset.eq(initialStep);if(next_fs.is("fieldset")){current_fs.hide();next_fs.show();previous_fs=current_fs}var steps=$(".steps");if(steps.length>0){steps.find(".step").eq(fieldset.index(current_fs)).removeClass("current");steps.find(".step").eq(fieldset.index(next_fs)).prevAll().addClass("success");steps.find(".step").eq(fieldset.index(next_fs)).addClass("current")}else{var progress=$(".progress").first();var progressBar=progress.find(".progress-bar");var percent=progressBar.find(".percent");var title=progressBar.find(".title");var titles=progressBar.data("titles");if(typeof titles!=="undefined"){titles=titles.split(",");var next_title=titles[fieldset.index(next_fs)];title.html(next_title)}var new_percent=Math.round(100/fieldset.length*fieldset.index(next_fs))+"%";percent.text(new_percent);progressBar.width(new_percent)}}}setInterval(doneResizing,10);var oHeight;function doneResizing(){var nHeight=Math.max(window.document.body.scrollHeight,window.document.body.offsetHeight,window.document.documentElement.offsetHeight);if(oHeight!==nHeight){Utils.postMessage({height:nHeight});oHeight=nHeight}}window.nextStep=function(){var hasError=false;current_fs=formEl.find("fieldset:visible");next_fs=current_fs.next();if(options.skips.length>0){for(var i=0;i<options.skips.length;i++){if(fieldset.index(current_fs)<options.skips[i].to){var tmp_next_fs=fieldset.eq(options.skips[i].to);if(fieldset.index(current_fs)<fieldset.index(tmp_next_fs)){if(options.skips[i].from==null||options.skips[i].from==fieldset.index(current_fs)){options.skips[i].from=fieldset.index(current_fs);next_fs=tmp_next_fs}break}}}}if(next_fs.is("fieldset")){$.ajax({url:options.validationUrl,type:"post",data:formEl.serialize(),headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"},complete:function(jqXHR,textStatus){},beforeSend:function(jqXHR,settings){var requiredFile=current_fs.find("input:file[required]");if(requiredFile.length>0){$.each(requiredFile,function(){if($(this).val()){settings.data=settings.data+"&"+$(this).attr("name")+"=1"}})}settings.data=settings.data+"&current_page="+fieldset.index(current_fs)},success:function(errors){if(errors!==null&&typeof errors==="object"){current_fs.find(".invalid-feedback").remove();current_fs.find(".form-group").removeClass("has-validation");$.each(errors,function(key,error){var field=current_fs.find("#"+key);var fieldType=key.split("_",1).shift();var fieldGroup=field.closest(".form-group");if(fieldGroup.length>0){hasError=true;fieldGroup.addClass("has-validation");if(fieldType!=="matrix"){var errorBlock=$("<div>",{class:"invalid-feedback",html:error});fieldGroup.append(errorBlock)}if(["checkbox","radio","nps","matrix"].includes(fieldType)){if(fieldType!=="matrix"){fieldGroup.find(".invalid-feedback").show()}var checkGroup=fieldType==="matrix"?field.closest("tr").find(".form-check-input"):fieldGroup.find(".form-check-input");checkGroup.each(function(){$(this).get(0).setCustomValidity("invalid")})}}})}if(hasError){formEl.addClass("was-validated")}else{current_fs.hide();var steps=$(".steps");if(steps.length>0){steps.find(".step").eq(fieldset.index(current_fs)).removeClass("current");steps.find(".step").eq(fieldset.index(next_fs)).prevAll().addClass("success");steps.find(".step").eq(fieldset.index(next_fs)).addClass("current")}else{var progress=$(".progress").first();var progressBar=progress.find(".progress-bar");var percent=progressBar.find(".percent");var title=progressBar.find(".title");var titles=progressBar.data("titles");if(typeof titles!=="undefined"){titles=titles.split(",");var next_title=titles[fieldset.index(next_fs)];title.html(next_title)}var new_percent=Math.round(100/fieldset.length*fieldset.index(next_fs))+"%";percent.text(new_percent);progressBar.width(new_percent)}if(next_fs.is("fieldset")){next_fs.show();previous_fs=current_fs}}formEl.trigger("nextStep");Utils.postMessage({scrollToTop:"container"})},error:function(){Utils.showMessage("#messages",options.i18n.unexpectedError,"danger");formEl.hide();Utils.postMessage({scrollToTop:"container"})}})}};window.previousStep=function(){if(previous_fs.is("fieldset")){current_fs=formEl.find("fieldset:visible");for(var i=0;i<options.skips.length;i++){if(fieldset.index(current_fs)===options.skips[i].to){previous_fs=fieldset.eq(options.skips[i].from);break}}var steps=$(".steps");if(steps.length>0){steps.find(".step").removeClass("success");steps.find(".step").eq(fieldset.index(current_fs)).removeClass("current");steps.find(".step").eq(fieldset.index(previous_fs)).prevAll().addClass("success");steps.find(".step").eq(fieldset.index(previous_fs)).addClass("current")}else{var progress=$(".progress").first();var progressBar=progress.find(".progress-bar");var percent=progressBar.find(".percent");var title=progressBar.find(".title");var titles=progressBar.data("titles");if(typeof titles!=="undefined"){titles=titles.split(",");var previous_title=titles[fieldset.index(previous_fs)];title.html(previous_title)}var new_percent=Math.round(100/fieldset.length*fieldset.index(previous_fs))+"%";percent.text(new_percent);progressBar.width(new_percent)}current_fs.hide();previous_fs.show();previous_fs=previous_fs.prev();formEl.trigger("previousStep");Utils.postMessage({scrollToTop:"container"})}};$("input").on("keypress",function(e){if(e.keyCode===13){var next=$(e.currentTarget).parents("fieldset").find(".next");if(next.length>0){e.preventDefault();next.click();return false}}});$(".next").click(nextStep);$(".prev").click(previousStep);formEl.ajaxForm({url:options.actionUrl,type:"post",dataType:"json",beforeSend:function(xhr){if(fp){xhr.setRequestHeader("fp",fp)}if(lat&&lng){xhr.setRequestHeader("lat",lat);xhr.setRequestHeader("lng",lng)}},beforeSubmit:function(formData,jqForm,opts){if(options.mode==="preview"){return false}var showProgressBar=false;$.each(jqForm.find('input[type="file"]'),function(index,fileInput){if($(fileInput).val()){showProgressBar=true}});if(showProgressBar){progressEl.show();var percentVal="0%";barEl.css("width",percentVal);percentEl.html(percentVal+" "+options.i18n.complete)}formEl.find(":submit").attr("disabled",true);formEl.trigger("beforeSubmit")},uploadProgress:function(event,position,total,percentComplete){var percentVal=percentComplete+"%";barEl.css("width",percentVal);percentEl.html(percentVal+" "+options.i18n.complete)},success:function(data){if(data.success){cleanFormUI();successHandler(data)}else{errorHandler(data)}},error:function(jqXHR,textStatus,errorThrown){Utils.showMessage("#messages",options.i18n.unexpectedError,"danger");formEl.hide();Utils.postMessage({scrollToTop:"container"})}});function removeErrorMessages(){formEl.removeClass("was-validated");$(".invalid-feedback").remove();$(".form-group").removeClass("has-validation");formEl.find(":input").each(function(){$(this).get(0).setCustomValidity("")})}function cleanFormUI(){removeErrorMessages();formEl.find(":submit").attr("disabled",false);if(!(options.submissionData&&options.fields)&&!!options.reset){formEl.resetForm()}progressEl.hide()}function successHandler(data){options.submitted=true;var endTime=(new Date).getTime();var completionTime=endTime-startTime;formEl.trigger("success",[data,completionTime]);if(fieldset.length>1){var steps=$(".steps");if(steps.length>0){steps.find(".step").addClass("success")}else{var progress=$(".progress").first();var progressBar=progress.find(".progress-bar");var percent=progressBar.find(".percent");var title=progressBar.find(".title");title.html("Complete");percent.text("100%");progressBar.width("100%")}}if(typeof data.addon!=="undefined"){if(typeof data.addon.redirectTo!=="undefined"){var url=data.addon.redirectTo;if(window.location!==window.parent.location){Utils.postMessage({url:url})}else{window.location.href=url?url:"/"}}}var confirmationType=typeof data.confirmationType==="undefined"?options.confirmationType:data.confirmationType;var confirmationMessage=typeof data.message==="undefined"?options.confirmationMessage:data.message;var confirmationUrl=typeof data.confirmationUrl==="undefined"?options.confirmationUrl:data.confirmationUrl;var confirmationSeconds=typeof data.confirmationSeconds==="undefined"?options.confirmationSeconds:data.confirmationSeconds;var confirmationRedirectToUrl=typeof data.redirectToUrl==="undefined"?options.redirectToUrl:data.redirectToUrl;var confirmationShowOnlyMessage=typeof data.showOnlyMessage==="undefined"?options.showOnlyMessage:data.showOnlyMessage;var redirectToUrl=function(){if(window.location!==window.parent.location){Utils.postMessage({url:confirmationUrl})}else{window.location.href=confirmationUrl?confirmationUrl:"/"}};var showMessage=function(){Utils.hideMessage("#messages");Utils.showMessage("#messages",confirmationMessage,"success");if(confirmationType===confirmationShowOnlyMessage){formEl.hide()}Utils.postMessage({scrollToTop:"container"})};if(confirmationType===confirmationRedirectToUrl){var seconds=parseInt(confirmationSeconds,10);if(seconds>0){showMessage();setTimeout(function(){redirectToUrl()},seconds*1e3)}else{redirectToUrl()}}else{showMessage()}}function errorHandler(data){Utils.hideMessage("#messages");if(typeof data.message!=="undefined"){Utils.showMessage("#messages",data.message,"danger")}Utils.postMessage({scrollToTop:"container"});removeErrorMessages();formEl.addClass("was-validated");if(typeof data.errors!=="undefined"&&data.errors.length>0){var errors=data.errors;for(let k=0;k<errors.length;k++){var field=$("#"+errors[k].field);var fieldType=errors[k].field.split("_",1).shift();var fieldGroup=field.closest(".form-group");fieldGroup.addClass("has-validation");if(fieldType!=="matrix"){for(let i=0;i<errors[k].messages.length;i++){field.get(0).setCustomValidity("invalid");var errorBlock=$("<div>",{class:"invalid-feedback",html:errors[k].messages[i]});fieldGroup.append(errorBlock)}}if(["checkbox","radio","nps","matrix"].includes(fieldType)){if(fieldType!=="matrix"){fieldGroup.find(".invalid-feedback").show()}var checkGroup=fieldType==="matrix"?field.closest("tr").find(".form-check-input"):fieldGroup.find(".form-check-input");checkGroup.each(function(){$(this).get(0).setCustomValidity("invalid")})}}}formEl.find(":submit").removeAttr("disabled");formEl.trigger("error",data)}function prefillForm(){if(options.mode==="preview"){return false}var canvasElement=$("canvas");if(canvasElement.length>0){for(var countCanvas=0;countCanvas<canvasElement.length;countCanvas++){(function(canvasEl){var signaturePad=new SignaturePad(canvasEl),canvasID=signaturePad.canvas.id,fieldID="#hidden_"+canvasID,resetID="#clear_"+canvasID,undoID="#undo_"+canvasID;var updateSignature=function(){var data=signaturePad.toData();if(data.length>0){var signature={data:signaturePad.toData(),dataURL:signaturePad.toDataURL()};$(fieldID).val(JSON.stringify(signature))}else{$(fieldID).val("")}$(fieldID).trigger("change")};signaturePad.onEnd=function(){updateSignature()};var penColor=$("#"+canvasID).data("color");if(penColor&&penColor.length>0&&penColor!=="black"){signaturePad.penColor=penColor}$(resetID).on("click",function(){signaturePad.clear();updateSignature()});$(undoID).on("click",function(){var data=signaturePad.toData();if(data){data.pop();signaturePad.fromData(data);updateSignature()}});formEl.on("success",function(){signaturePad.clear();updateSignature()})})(canvasElement[countCanvas])}}var parentUrl=Utils.getUrlVars()["parentUrl"];parentUrl=parentUrl&&parentUrl.length>0?decodeURIComponent(parentUrl):window.location.href;if(parentUrl&&parentUrl.length>0){formEl.find(":input").each(function(){var fieldEl=$(this);var value=Utils.getUrlVars(parentUrl)[fieldEl.attr("id")];if(!value&&fieldEl.data("alias")&&fieldEl.data("alias").length>0){value=Utils.getUrlVars(parentUrl)[fieldEl.data("alias")]}if(fieldEl.attr("type")==="checkbox"||fieldEl.attr("type")==="radio"){if(value==="true"||value==="false"){fieldEl.prop("checked",value==="true").trigger("change")}}else{if(value){value=decodeURIComponent(value);fieldEl.val(value).trigger("change")}}})}if(options.defaultValues){options.defaultValues=JSON.parse(options.defaultValues);$.each(options.defaultValues,function(field,value){var fieldType=field.split("_",1).shift();var fieldEl=$("#"+field);if(fieldType==="checkbox"||fieldType==="radio"||fieldType==="nps"){fieldEl.prop("checked",value).trigger("change")}else{fieldEl.val(value).trigger("change")}})}if(options.submissionData&&options.fields){var data=JSON.parse(options.submissionData);var keys=$.map(options.fields,function(field,index){return field["name"]});$.each(keys,function(index,key){var parts=key.split("_",1);var componentType=parts[0];if(componentType==="selectlist"){var selected=typeof data[key]==="undefined"?[""]:data[key];$('select[name="'+key+'[]"] option:selected').prop("selected",false);$('select[name="'+key+'[]"]').each(function(){var elem=$(this);if(typeof selected==="string"){$('select[name="'+key+'[]"]'+' option[value="'+selected+'"]').prop("selected",true)}else if(Array.isArray(selected)){$.each(selected,function(i,val){$('select[name="'+key+'[]"]'+' option[value="'+val+'"]').prop("selected",true)})}})}else if(componentType==="checkbox"){var checks=data[key];$('input[name="'+key+'[]"]').prop("checked",false).each(function(){var elem=$(this);if(typeof checks!=="undefined"){$.each(checks,function(i,val){if(elem.val()===val){elem.prop("checked",true)}})}})}else if(componentType==="radio"||componentType==="nps"){$('input[name="'+key+'"]').each(function(i,val){var elem=$(val);if(elem.val()===data[key]){elem.prop("checked",true)}})}else if(componentType==="matrix"){var field=$("#"+key);if(field.length===0||field.not("input")&&!field.is("select")&&!field.is("textarea")){field=$('input[name="'+key+'"]');if(field.length===0){field=$('input[name="'+key+'[]"]')}}if(field.is("select")){var selected=data[key];$('select[name="'+key+'[]"] option:selected').prop("selected",false);$('select[name="'+key+'[]"]').each(function(){var elem=$(this);if(typeof selected==="string"){$('select[name="'+key+'[]"]'+' option[value="'+selected+'"]').prop("selected",true)}else if(Array.isArray(selected)){$.each(selected,function(i,val){$('select[name="'+key+'[]"]'+' option[value="'+val+'"]').prop("selected",true)})}})}else if(field.is(":radio")){field.each(function(i,val){var elem=$(val);if(elem.val()===data[key]){elem.prop("checked",true)}})}else if(field.is(":checkbox")){var checks=data[key];$('input[name="'+key+'[]"]').prop("checked",false).each(function(){var elem=$(this);$.each(checks,function(i,val){if(elem.val()===val){elem.prop("checked",true)}})})}else{field.val(data[key])}}else if(key.substring(0,16)==="hidden_signature"){var canvasElement=$("#"+key).closest("div").find("canvas");if(canvasElement.length>0){for(var countCanvas=0;countCanvas<canvasElement.length;countCanvas++){(function(canvasEl){var signaturePad=new SignaturePad(canvasEl);if(typeof data[key]!=="undefined"&&data[key]){var signature=JSON.parse(data[key]);signaturePad.fromData(signature["data"])}var canvasID=signaturePad.canvas.id;var fieldID="#hidden_"+canvasID;var resetID="#clear_"+canvasID;var undoID="#undo_"+canvasID;var saveData=function(){var signature={data:signaturePad.toData(),dataURL:signaturePad.toDataURL()};$(fieldID).val(JSON.stringify(signature));$(fieldID).trigger("change")};$(fieldID).val(data[key]);signaturePad.onEnd=function(){saveData()};var penColor=$("#"+canvasID).data("color");if(penColor&&penColor.length>0&&penColor!=="black"){signaturePad.penColor=penColor}$(resetID).on("click",function(){signaturePad.clear();$(fieldID).val("")});$(undoID).on("click",function(){var data=signaturePad.toData();if(Array.isArray(data)&&data.length){data.pop();signaturePad.fromData(data);saveData()}})})(canvasElement[countCanvas])}}}else{$("#"+key).val(data[key])}})}formEl.find(":input").each(function(){$(this).one("change",function(){if(beganFilling===false){if(options.mode==="preview"){return false}startTime=(new Date).getTime();formEl.trigger("fill")}beganFilling=true})})}formEl.on("view",function(event){Utils.postMessage({action:"view"})});formEl.on("fill",function(event){Utils.postMessage({action:"fill"})});formEl.on("nextStep",function(event){Utils.postMessage({action:"nextStep"})});formEl.on("previousStep",function(event){Utils.postMessage({action:"previousStep"})});formEl.on("beforeSubmit",function(){Utils.postMessage({action:"beforeSubmit"})});formEl.on("error",function(event,data){Utils.postMessage({action:"error",data:data})});formEl.on("success",function(event,data,completionTime){Utils.postMessage({action:"success",data:data,completionTime:completionTime})});if(options.analytics){(function(p,l,o,w,i,n,g){if(!p[i]){p.FA=p.FA||[];p.FA.push(i);p[i]=function(){(p[i].q=p[i].q||[]).push(arguments)};p[i].q=p[i].q||[];n=l.createElement(o);g=l.getElementsByTagName(o)[0];n.async=1;n.src=w;g.parentNode.insertBefore(n,g)}})(window,document,"script",options.tracker,"tracker");window.tracker("newTracker","t"+options.id,options.app,{encodeBase64:false,appId:options.id});formEl.on("view",function(event){window.tracker("setCustomUrl",decodeURIComponent(Utils.getUrlVars()["url"]));window.tracker("setReferrerUrl",decodeURIComponent(Utils.getUrlVars()["referrer"]));window.tracker("trackPageView",decodeURIComponent(Utils.getUrlVars()["title"]))});formEl.on("fill",function(event){window.tracker("trackStructEvent","form","fill","start",null,null)});formEl.on("error",function(event){window.tracker("trackStructEvent","form","error",null,null,null)});formEl.on("success",function(event,submission,completionTime){window.tracker("trackStructEvent","form","submit",submission.id,"time",completionTime)})}prefillForm();formEl.trigger("view")});