export const productsTemplate = `
<div class="products-page responsive-block">
  <!-- 头部区域 -->
  <div data-bs-component="hero" class="py-5 hero-section">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h1 class="mb-4 display-4 fw-bold">The marketing platform to drive conversion</h1>
            <p class="mb-4 lead text-muted">A state of the art marketing platform designed to give you more than just means for digital success with data.</p>
          </div>
          <div data-bs-component="bootstrap-button" class="mb-4">
            <button class="px-4 btn btn-primary btn-lg rounded-pill">Get started</button>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="position-relative">
            <div class="gap-3 d-flex">
              <div class="p-3 bg-white shadow-sm stats-card rounded-3">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/value-prop-one.webp" alt="Stats" class="mb-2 img-fluid" style="width: 200px;">
                <div class="d-flex align-items-center">
                  <span class="mb-0 h4 text-primary me-2">7.4%</span>
                  <small class="text-muted">Conversion rate</small>
                </div>
              </div>
              <div class="p-3 bg-white shadow-sm user-card rounded-3">
                <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" alt="User" class="mb-2 img-fluid rounded-circle" style="width: 80px;">
                <div class="text-center">
                  <small class="text-muted d-block">Active now</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 合作伙伴 -->
  <div data-bs-component="partners" class="py-4">
    <div class="container">
      <div class="flex-wrap gap-4 d-flex justify-content-center align-items-center">
        <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" alt="Partner" class="partner-logo">
        <img src="https://new-bwms.bingo-test.com/tiptap/spectroxium-light.webp" alt="Partner" class="partner-logo">
        <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" alt="Partner" class="partner-logo">
        <img src="https://new-bwms.bingo-test.com/tiptap/spectroxium-light.webp" alt="Partner" class="partner-logo">
        <img src="https://new-bwms.bingo-test.com/tiptap/lyraxionics-light.webp" alt="Partner" class="partner-logo">
      </div>
    </div>
  </div>

  <!-- 功能特性 -->
  <div data-bs-component="features" class="py-5 features-section">
    <div class="container">
      <div class="row g-4">
        <div class="col-md-4">
          <div class="p-4 feature-card rounded-3">
            <div class="mb-3 feature-icon">
              <i class="fas fa-edit text-primary fs-3"></i>
            </div>
            <h3 class="mb-3 h5">Create and edit</h3>
            <p class="text-muted">Write a brief description of the scope and benefits of this feature.</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4 feature-card rounded-3">
            <div class="mb-3 feature-icon">
              <i class="fas fa-share-alt text-primary fs-3"></i>
            </div>
            <h3 class="mb-3 h5">Publish and share</h3>
            <p class="text-muted">Write a brief description of the scope and benefits of this feature.</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4 feature-card rounded-3">
            <div class="mb-3 feature-icon">
              <i class="fas fa-chart-line text-primary fs-3"></i>
            </div>
            <h3 class="mb-3 h5">Measure and analyze</h3>
            <p class="text-muted">Write a brief description of the scope and benefits of this feature.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 产品功能展示 -->
  <div data-bs-component="capabilities" class="py-5 capabilities-section bg-light">
    <div class="container">
      <div data-bs-component="richTextBlock" class="mb-5 text-center">
        <h2 class="mb-4 display-5 fw-bold">Explore Elevate's capabilities</h2>
      </div>
      <div class="row g-4">
        <div class="col-md-6">
          <div class="p-4 bg-white shadow-sm capability-card rounded-3">
            <div class="d-flex align-items-start">
              <div class="capability-icon me-3">
                <i class="fas fa-pencil-alt text-primary"></i>
              </div>
              <div>
                <h3 class="mb-2 h5">Content creation</h3>
                <p class="mb-0 text-muted">Write a brief description of the scope and benefits of this feature.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="p-4 bg-white shadow-sm capability-card rounded-3">
            <div class="d-flex align-items-start">
              <div class="capability-icon me-3">
                <i class="fas fa-chart-bar text-primary"></i>
              </div>
              <div>
                <h3 class="mb-2 h5">Analytics and reporting</h3>
                <p class="mb-0 text-muted">Write a brief description of the scope and benefits of this feature.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="p-4 bg-white shadow-sm capability-card rounded-3">
            <div class="d-flex align-items-start">
              <div class="capability-icon me-3">
                <i class="fas fa-cogs text-primary"></i>
              </div>
              <div>
                <h3 class="mb-2 h5">Integration</h3>
                <p class="mb-0 text-muted">Write a brief description of the scope and benefits of this feature.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="p-4 bg-white shadow-sm capability-card rounded-3">
            <div class="d-flex align-items-start">
              <div class="capability-icon me-3">
                <i class="fas fa-calendar text-primary"></i>
              </div>
              <div>
                <h3 class="mb-2 h5">Campaign scheduling</h3>
                <p class="mb-0 text-muted">Write a brief description of the scope and benefits of this feature.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 客户推荐 -->
  <div data-bs-component="testimonial-slider" class="py-5 testimonial-section">
    <div class="container">
      <div class="testimonial-slider">
        <button class="nav-button prev-button"><i class="fas fa-chevron-left"></i></button>
        <div class="testimonial-slide active">
          <div class="row align-items-center">
            <div class="mb-3 text-center col-md-3 mb-md-0">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/testimonial-user-image-1.webp" alt="Testimonial" class="mb-3 rounded-circle" width="120">
            </div>
            <div class="col-md-9">
              <div data-bs-component="richTextBlock">
                <p class="mb-4 lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
                <h5 class="mb-1">Neil Kumar</h5>
                <p class="text-muted">VP of Marketing @ Lyrakonics</p>
              </div>
            </div>
          </div>
        </div>
        <button class="nav-button next-button"><i class="fas fa-chevron-right"></i></button>
      </div>
    </div>
  </div>

  <!-- 数据展示 -->
  <div data-bs-component="stats" class="py-5 stats-section">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <div class="p-4 bg-white shadow-sm stats-card rounded-3">
            <div class="mb-3 d-flex align-items-center">
              <span class="badge bg-primary me-2">25,317</span>
              <span class="text-muted">Active users</span>
            </div>
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/value-prop-one.webp" alt="Stats" class="img-fluid">
          </div>
        </div>
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Increase reach and engagement</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- ROI优化 -->
  <div data-bs-component="roi" class="py-5 roi-section bg-light">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6 order-lg-2">
          <div class="p-4 bg-white shadow-sm roi-card rounded-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/value-prop-two.webp" alt="ROI" class="img-fluid">
          </div>
        </div>
        <div class="col-lg-6 order-lg-1">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Optimize Performance and ROI</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 工作流程 -->
  <div data-bs-component="workflow" class="py-5 workflow-section">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <div class="p-4 bg-white shadow-sm workflow-card rounded-3">
            <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/value-prop-three.webp" alt="Workflow" class="img-fluid">
          </div>
        </div>
        <div class="col-lg-6">
          <div data-bs-component="richTextBlock">
            <h2 class="mb-4 display-6 fw-bold">Streamline Workflow and Collaboration</h2>
            <p class="lead text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 营销横幅 -->
  <div data-bs-component="marketing-banner" class="py-4 text-white marketing-banner bg-dark">
    <div class="container">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <div class="me-3">
            <i class="fas fa-rocket fs-2"></i>
          </div>
          <h3 class="mb-0">All your marketing needs in one place</h3>
        </div>
        <div data-bs-component="bootstrap-button">
          <button class="px-4 btn btn-light rounded-pill">Get started</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 常见问题 -->
  <div data-bs-component="faq" class="py-5 faq-section">
    <div class="container">
      <div data-bs-component="richTextBlock" class="mb-5 text-center">
        <h2 class="fw-bold">Frequently asked questions</h2>
      </div>
      <div class="accordion" id="faqAccordion">
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
              What services does your digital marketing agency offer?
            </button>
          </h2>
          <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              We offer a comprehensive suite of digital marketing services including social media management, email marketing, SEO optimization, content creation, and performance analytics.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
              How can your digital marketing agency help my business grow?
            </button>
          </h2>
          <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency helps businesses increase their online visibility, attract more qualified leads, engage with their target audience effectively, and convert prospects into loyal customers.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
              Do you provide customized solutions for different businesses?
            </button>
          </h2>
          <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Yes, we tailor our digital marketing strategies to match your specific business goals, target audience, and industry requirements, ensuring you receive personalized solutions.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
              Can you track the performance of digital marketing campaigns?
            </button>
          </h2>
          <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Absolutely! We provide comprehensive analytics and reporting that track key performance indicators, conversion rates, engagement metrics, and ROI for all your marketing campaigns.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
              What sets your digital marketing agency apart from others in the industry?
            </button>
          </h2>
          <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency stands out through our data-driven approach, transparent communication, industry expertise, customized strategies, and measurable results that help our clients achieve sustainable growth.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* 基础样式 */
.products-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 头部区域样式 */
.hero-section {
  background-color: #fff;
  position: relative;
  overflow: hidden;
}

/* 合作伙伴样式 */
.partner-logo {
  height: 30px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.partner-logo:hover {
  opacity: 1;
}

/* 功能特性卡片样式 */
.feature-card {
  background-color: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* 能力展示卡片样式 */
.capability-card {
  transition: transform 0.3s ease;
}

.capability-card:hover {
  transform: translateY(-5px);
}

.capability-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: rgba(108,92,231,0.1);
}

/* 客户推荐轮播样式 */
.testimonial-slider {
  position: relative;
  padding: 2rem;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  cursor: pointer;
  z-index: 1;
}

.prev-button {
  left: -20px;
}

.next-button {
  right: -20px;
}

/* 响应式样式 */
@media (max-width: 991.98px) {
  .hero-section {
    text-align: center;
  }
  
  .stats-card,
  .user-card {
    margin-top: 2rem;
  }
  
  .marketing-banner .d-flex {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 767.98px) {
  .partner-logo {
    height: 25px;
  }
  
  .nav-button {
    display: none;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // 轮播功能
  const slider = document.querySelector('.testimonial-slider');
  const slides = document.querySelectorAll('.testimonial-slide');
  const prevButton = document.querySelector('.prev-button');
  const nextButton = document.querySelector('.next-button');
  let currentSlide = 0;
  
  function showSlide(index) {
    slides.forEach(slide => slide.classList.remove('active'));
    slides[index].classList.add('active');
  }
  
  function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
  }
  
  function prevSlide() {
    currentSlide = (currentSlide - 1 + slides.length) % slides.length;
    showSlide(currentSlide);
  }
  
  if(prevButton && nextButton) {
    prevButton.addEventListener('click', prevSlide);
    nextButton.addEventListener('click', nextSlide);
  }
  
  // 自动轮播
  setInterval(nextSlide, 5000);
  
  // FAQ手风琴效果
  const accordionButtons = document.querySelectorAll('.accordion-button');
  
  accordionButtons.forEach(button => {
    button.addEventListener('click', function() {
      const isCollapsed = this.classList.contains('collapsed');
      
      accordionButtons.forEach(btn => {
        btn.classList.add('collapsed');
        btn.setAttribute('aria-expanded', 'false');
      });
      
      if (isCollapsed) {
        this.classList.remove('collapsed');
        this.setAttribute('aria-expanded', 'true');
      }
    });
  });
  
  // 滚动动画
  const animateElements = document.querySelectorAll('.feature-card, .capability-card, .stats-card, .roi-card, .workflow-card');
  
  function checkScroll() {
    animateElements.forEach(element => {
      const elementTop = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if(elementTop < windowHeight * 0.8) {
        element.classList.add('animated');
      }
    });
  }
  
  // 初始检查
  checkScroll();
  
  // 滚动时检查
  window.addEventListener('scroll', checkScroll);
});
</script>
`
