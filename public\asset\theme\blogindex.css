.bwms-page .left-content .banner {
  margin-top: 10px;
  padding: 0;
  position: relative;
  overflow: hidden;
}
.bwms-page .left-content .banner .swiper-slide {
  max-height: 316px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .banner .swiper-slide .pic .img {
  object-fit: cover;
}
.bwms-page .left-content .banner .swiper-slide .text-box {
  margin: 30px 0;
  padding: 0 40px;
  width: 100%;
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .left-content .banner .swiper-slide .text-box .title {
  font-size: 40px;
  font-weight: bold;
  line-height: 1;
}
.bwms-page .left-content .banner .swiper-slide .text-box .slogan {
  padding: 10px 0;
  margin-top: 20px;
}
.bwms-page .left-content .banner .swiper-slide .text-box .slogan .line {
  font-size: 20px;
  line-height: 1.3;
}
.bwms-page .left-content .banner .swiper-slide .text-box .link {
  border-radius: 50px;
  padding: 15px 25px;
  color: #fff;
  background-color: transparent;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  border: 1px solid #fff;
  font-size: 13px;
}
.bwms-page .left-content .banner .swiper-slide .text-box .link .iconfont + span {
  margin-left: 5px;
}
.bwms-page .left-content .banner .swiper-slide .text-box .link span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .left-content .banner .swiper-slide .text-box .link span {
  display: block;
  line-height: 1.5;
}
.bwms-page .left-content .banner .swiper-slide .text-box .link:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}
.bwms-page .left-content .banner:hover .navigation-box .iconfont {
  opacity: 0.5;
}
.bwms-page .left-content .banner .navigation-box .iconfont {
  border-radius: 50%;
  opacity: 0;
  background: #fff;
  color: #34495e;
  font-size: 10px;
  width: 50px;
  height: 50px;
  transition: opacity 0.3s ease-in-out;
  font-size: 22px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .banner .navigation-box .iconfont.icon-backios {
  left: 10px;
}
.bwms-page .left-content .banner .navigation-box .iconfont.icon-right {
  right: 10px;
}
.bwms-page .left-content .banner .swiper-pagination {
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .banner .swiper-pagination .swiper-pagination-bullet {
  margin: 0 4px;
  box-shadow: #666 0 0 0.25rem;
  width: 16px;
  height: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.bwms-page .left-content .tag-filter-box p {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px;
  color: #34495e;
  font-size: 13px;
}
.bwms-page .left-content .tag-filter-box p i.iconfont {
  margin-right: 4px;
  display: block;
  color: #34495e;
}
.bwms-page .left-content .tag-filter-box p span {
  padding: 0 4px;
  color: #3555cc;
  font-weight: bold;
}
.bwms-page .left-content .tag-filter-box p a.iconfont {
  margin-left: 4px;
  color: #c4cfdb;
  display: block;
}
.bwms-page .left-content .classify-filter-box .filter-list {
  padding: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .classify-filter-box .filter-list .item {
  margin-right: 10px;
  border-radius: 50px;
  padding: 9px 16px;
  color: #666;
  background-color: transparent;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  border: 1px solid #e5e9ee;
}
.bwms-page .left-content .classify-filter-box .filter-list .item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .left-content .classify-filter-box .filter-list .item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .left-content .classify-filter-box .filter-list .item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .left-content .classify-filter-box .filter-list .item:hover {
  background-color: #3555CC;
  color: #fff;
}
.bwms-page .left-content .classify-filter-box .filter-list .item:hover {
  border-color: #3555CC;
}
.bwms-page .left-content .blog-list {
  padding: 12px;
}
.bwms-page .left-content .blog-list .blog-item {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e9ee;
}
.bwms-page .left-content .blog-list .blog-item .blog-tit {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .blog-list .blog-item .blog-tit .tag {
  margin-right: 4px;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 16px;
  line-height: 1.5;
}
.bwms-page .left-content .blog-list .blog-item .blog-tit .tag.blue {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.3);
}
.bwms-page .left-content .blog-list .blog-item .blog-tit .tag.red {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.3);
}
.bwms-page .left-content .blog-list .blog-item .blog-tit .tag.yellow {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.3);
}
.bwms-page .left-content .blog-list .blog-item .blog-tit h4 {
  color: #34495e;
  font-size: 20px;
  font-weight: normal;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .left-content .blog-list .blog-item .blog-tit h4 a {
  color: #34495e;
  font-size: 20px;
  font-weight: normal;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .left-content .blog-list .blog-item .blog-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag {
  flex-grow: 1;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .time {
  padding-top: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .time .label-text {
  margin-right: 4px;
  color: #9ca3af;
  font-size: 13px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .time .label-text .label {
  margin-right: 3px;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .time .label-text .label .iconfont {
  font-size: 14px;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .desc {
  margin-top: 8px;
  color: #9ca3af;
  font-size: 13px;
  line-height: 1.84;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .tag-list {
  padding-top: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .time-desc-tag .tag-list .tag-item {
  margin-right: 4px;
  border-radius: 14px;
  border: 1px solid #e5e9ee;
  padding: 8px 10px;
  color: #666666;
  cursor: pointer;
  font-size: 10px;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .pic {
  margin-left: 16px;
  border-radius: 4px;
  width: 160px;
  flex-shrink: 0;
  overflow: hidden;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .pic .img {
  transform: rotate(0) scaleX(1) scaleY(1);
  transition: transform 0.35s ease-in-out;
}
.bwms-page .left-content .blog-list .blog-item .blog-content .pic:hover .img {
  transform: rotate(3deg) scaleX(1.1) scaleY(1.1);
}
.bwms-page .left-content .pagination-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .pagination-box a {
  margin: 5px;
  border-radius: 5px;
  color: #666;
  font-size: 13px;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .left-content .pagination-box a.active {
  background-color: #3555CC;
  color: #fff;
}
.bwms-page .left-content .pagination-box a:hover {
  color: #3555CC;
}
.bwms-page .left-content .pagination-box .more {
  color: #666;
  font-size: 13px;
}
.bwms-page .right-side .user-info {
  padding: 54.5px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.bwms-page .right-side .user-info .avatar {
  border-radius: 50%;
  width: 112px;
  height: 112px;
  overflow: hidden;
}
.bwms-page .right-side .user-info .nickname {
  margin-top: 20px;
  color: #34495e;
  font-size: 20px;
  line-height: 1.3;
}
.bwms-page .right-side .user-info .sign-word {
  margin-top: 16px;
  color: #9ca3af;
  font-size: 13px;
}
.bwms-page .right-side .classify {
  padding: 12px;
}
.bwms-page .right-side .classify .col-6 {
  padding-left: 5px;
  padding-right: 5px;
}
.bwms-page .right-side .classify .classify-item {
  margin-bottom: 12px;
  border-radius: 50px;
  border: 1px solid #e5e9ee;
  padding: 6px 14px;
  font-size: 13px;
  line-height: 1.6;
  color: #666;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transition: all 0.35s ease-in-out;
}
.bwms-page .right-side .classify .classify-item .iconfont {
  margin-right: 4px;
  font-size: 12px;
  transition: color 0.35s ease-in-out;
}
.bwms-page .right-side .classify .classify-item:hover {
  border-color: #3555CC;
  color: #3555CC;
}
.bwms-page .right-side .classify .classify-item:hover .iconfont {
  color: #3555CC;
}
.bwms-page .right-side .tag-list {
  padding: 12px;
}
.bwms-page .right-side .tag-list .list {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.bwms-page .right-side .tag-list .list .tag-item {
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 50px;
  padding: 8px 14px;
  color: #666;
  background-color: #f7f7f7;
  transition: all 0.35s ease-in-out;
  justify-content: center;
  font-size: 13px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .right-side .tag-list .list .tag-item .iconfont + span {
  margin-left: 5px;
}
.bwms-page .right-side .tag-list .list .tag-item span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .right-side .tag-list .list .tag-item span {
  display: block;
  line-height: 1.5;
}
.bwms-page .right-side .tag-list .list .tag-item:hover {
  background-color: #eaeaea;
  color: #666;
}
.bwms-page .right-side .tag-list .list .tag-item:hover {
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}
.bwms-page .right-side .tag-list .list .tag-item .num {
  margin-left: 3px;
  border-radius: 50%;
  background-color: #9ca3af;
  font-size: 13px;
  width: 15px;
  height: 15px;
  color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .right-side .popular-blogs {
  padding: 12px;
}
.bwms-page .right-side .popular-blogs .list .blog-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .right-side .popular-blogs .list .blog-item:last-child {
  margin-bottom: 0;
}
.bwms-page .right-side .popular-blogs .list .blog-item .iconfont {
  font-size: 13px;
  color: #c4cfdb;
}
.bwms-page .right-side .popular-blogs .list .blog-item a {
  color: #34495e;
  font-size: 13px;
  line-height: 1.2;
}
.bwms-page .right-side .friendship-link {
  padding: 12px 12px 0;
}
.bwms-page .right-side .friendship-link .row {
  margin: 0 -5px;
}
.bwms-page .right-side .friendship-link .col-box {
  margin-bottom: 12px;
  padding-left: 5px;
  padding-right: 5px;
}
.bwms-page .right-side .friendship-link .col-box a {
  border-radius: 4px;
  background-color: #f7f7f7;
  color: #666666;
  font-size: 13px;
  display: block;
  text-align: center;
  line-height: 40px;
}
