.bwms-page {
  background-color: #F7F7F7;
}
.bwms-page .pane-list {
  padding-bottom: 80px;
  position: relative;
}
.bwms-page .pane-list .tab-pane {
  width: 100%;
}
.bwms-page .pane-list .tab-pane .list.cases {
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: calc(-15px * 2);
}
.bwms-page .pane-list .tab-pane .list.cases .item {
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: calc(15px * 2);
}
.bwms-page .pane-list .tab-pane .list.cases .item .box {
  position: relative;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 3;
  padding: 25px;
  background: url(../image/case/bg.png) no-repeat center center / cover;
  color: #fff;
  opacity: 0;
  transform: scale(1);
  transition: transform 0.35s ease-in-out, opacity 0.35s ease-in-out;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .mask h2 {
  margin-bottom: 10px;
  padding-top: 20px;
  font-size: 20px;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .mask p {
  font-size: 14px;
  line-height: 1.71;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .mask .btn-box {
  padding-bottom: 10px;
  font-size: 14px;
  position: absolute;
  bottom: 25px;
  right: 25px;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .text-box {
  padding: 25px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 1;
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 65%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  flex-direction: column-reverse;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box .text-box p {
  margin-bottom: 5px;
  color: #fff;
  font-size: 16px;
}
.bwms-page .pane-list .tab-pane .list.cases .item .box:hover .mask {
  opacity: 1;
  transform: scale(1.1);
}
