<template>
  <div class="faq-question-form-container table-page">
    <div class="faq-question-form-title">FAQ內容</div>

    <div class="faq-question-form" v-for="(item, idx) in questions" :key="item.id">
      <div class="faq-question-header">
        <el-input
          v-model="item.question"
          type="text"
          placeholder="請輸入問題"
        />
        <div class="faq-question-actions">
          <el-button type="primary" link @click="addQuestion(idx)">
            <el-icon size="18"><Plus /></el-icon>
          </el-button>
          <el-button type="primary" link @click="removeQuestion(idx)" :disabled="questions.length === 1">
            <el-icon size="18"><Delete /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="faq-answer-area">
        <el-input
          v-model="item.answer"
          type="textarea"
          rows="5"
          placeholder="請輸入FAQ答案內容..."
        ></el-input>
        <div class="faq-answer-actions flex justify-between" style="margin-top: 16px; gap: 12px;">
            <!-- 附件上传 -->
            <div>
            <el-upload
              :file-list="item.attachments"
              :on-success="(res, file) => handleUploadSuccess(res, file, idx)"
              :on-remove="(file) => handleRemoveFile(file, idx)"
              :auto-upload="true"
              action="#"
              list-type="text"
              :show-file-list="true"
              :limit="5"
              :before-upload="() => true"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                <span>上傳附件</span>
              </el-button>
            </el-upload>
            <!-- 已上传附件列表 -->
            <div v-if="item.attachments.length" class="faq-attachments-list">
              <div v-for="file in item.attachments" :key="file.uid" class="faq-attachment-item">
                <el-link :href="file.url || file.name" target="_blank">{{ file.name }}</el-link>
                <el-button type="primary" link @click="handleRemoveFile(file, idx)" style="line-height: 15px;">
                  <el-icon size="14"><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <!-- 連結 -->
          <div>
            <el-button @click="openLinkDialog(idx)">
              <el-icon><Link /></el-icon>
              <span>連結</span>
            </el-button>
            <!-- 已添加链接 -->
            <div v-if="item.link" class="faq-link-item">
              <el-link :href="item.link" target="_blank">{{ item.link }}</el-link>
              <el-button type="primary" link @click="removeLink(idx)" style="line-height: 15px;">
                <el-icon size="14"><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 連結输入弹窗 -->
    <el-dialog class="el-dialog-common-cls" align-center v-model="linkDialog.visible" title="輸入連結地址" width="400px" @close="linkDialog.input = ''">
      <el-input v-model="linkDialog.input" placeholder="請輸入連結地址" />
      <div class="flex" style="margin-top: 26px;">
        <el-button @click="linkDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveLink">確定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Upload, Link, Plus, Delete } from '@element-plus/icons-vue'

interface Attachment {
  name: string
  url?: string
  uid: string | number
}

interface QuestionItem {
  id: number
  question: string
  answer: string
  attachments: Attachment[]
  link: string
}

const questions = ref<QuestionItem[]>([
  { id: Date.now(), question: '', answer: '', attachments: [], link: '' }
])

function addQuestion(idx: number) {
  const newId = Date.now() + Math.floor(Math.random() * 10000)
  questions.value.splice(idx + 1, 0, { id: newId, question: '', answer: '', attachments: [], link: '' })
}

function removeQuestion(idx: number) {
  if (questions.value.length > 1) {
    questions.value.splice(idx, 1)
  }
}

// 附件上传相关
function handleUploadSuccess(res: any, file: any, idx: number) {
  // 这里只做本地演示，实际应用可根据后端返回处理
  questions.value[idx].attachments.push({
    name: file.name,
    url: file.url || '',
    uid: file.uid
  })
}
function handleRemoveFile(file: any, idx: number) {
  questions.value[idx].attachments = questions.value[idx].attachments.filter(f => f.uid !== file.uid)
}

// 連結相关
const linkDialog = reactive({
  visible: false,
  input: '',
  idx: -1
})
function openLinkDialog(idx: number) {
  linkDialog.visible = true
  linkDialog.input = questions.value[idx].link || ''
  linkDialog.idx = idx
}
function saveLink() {
  if (linkDialog.idx !== -1) {
    questions.value[linkDialog.idx].link = linkDialog.input
  }
  linkDialog.visible = false
  linkDialog.input = ''
  linkDialog.idx = -1
}
function removeLink(idx: number) {
  questions.value[idx].link = ''
}
</script>

<style lang="scss" scoped>
.faq-question-form-container {
  padding-right: 8px;
}
.faq-question-form-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}
.faq-question-form {
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 16px 20px;
  margin-bottom: 26px;
  background: #fff;
}
.faq-question-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.faq-question-actions {
  display: flex;
  align-items: center;
  margin-left: 12px;
}
.faq-answer-area {
  margin-top: 8px;
}
.faq-attachments-list {
  margin-top: 8px;
  .faq-attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }
}
.faq-link-item {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
