<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1360" height="980" xmlns="http://www.w3.org/2000/svg" version="1.1">
  <defs>
    <style type="text/css">
      .layer-title { font-size: 20px; font-weight: bold; text-anchor: middle; }
      .section-title { font-size: 18px; font-weight: bold; text-anchor: middle; }
      .component-text { font-size: 13px; text-anchor: middle; dominant-baseline: middle; }
      .component-box { stroke: #333; stroke-width: 1; }
      .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
      .data-flow { stroke: #8B5A2B; stroke-width: 2; marker-end: url(#brownArrow); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="brownArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8B5A2B" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1360" height="980" fill="white"/>
  
  <!-- 标题 -->
  <text x="680" y="40" font-size="24" font-weight="bold" text-anchor="middle">BWMS 管理后台架构图</text>
  <text x="680" y="65" font-size="16" font-style="italic" text-anchor="middle">Bootstrap + BWMS Admin API (重写版本)</text>

  <!-- 管理员访问层 -->
  <rect x="130" y="100" width="1100" height="80" fill="#FFF7ED" stroke="#EA580C" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="125" class="section-title">管理员访问层</text>
  <g transform="translate(180, 145)">
    <rect x="0" y="0" width="190" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="95" y="13" class="component-text" fill="white">超级管理员</text>
    
    <rect x="210" y="0" width="190" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="305" y="13" class="component-text" fill="white">系统管理员</text>
    
    <rect x="420" y="0" width="190" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="515" y="13" class="component-text" fill="white">内容管理员</text>
    
    <rect x="630" y="0" width="190" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="725" y="13" class="component-text" fill="white">运营人员</text>
    
    <rect x="840" y="0" width="160" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="920" y="13" class="component-text" fill="white">客服人员</text>
  </g>

  <!-- 前端管理层 - Bootstrap Admin -->
  <rect x="130" y="200" width="1100" height="160" fill="#F4F1E8" stroke="#8B5A2B" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="225" class="section-title">管理后台前端层 - Bootstrap 轻量级技术栈</text>
  
  <!-- Bootstrap 管理后台核心 -->
  <g transform="translate(150, 245)">
    <rect x="0" y="0" width="200" height="35" fill="#7952B3" class="component-box" rx="5" ry="5"/>
    <text x="100" y="18" class="component-text" fill="white">Bootstrap 5.x</text>
    
    <rect x="220" y="0" width="200" height="35" fill="#F7DF1E" class="component-box" rx="5" ry="5"/>
    <text x="320" y="18" class="component-text">JavaScript ES6+</text>
    
    <rect x="440" y="0" width="200" height="35" fill="#E34F26" class="component-box" rx="5" ry="5"/>
    <text x="540" y="18" class="component-text" fill="white">HTML5 + CSS3</text>
    
    <rect x="660" y="0" width="200" height="35" fill="#563D7C" class="component-box" rx="5" ry="5"/>
    <text x="760" y="18" class="component-text" fill="white">AdminLTE 主题</text>
    
    <rect x="880" y="0" width="120" height="35" fill="#DC3545" class="component-box" rx="5" ry="5"/>
    <text x="940" y="18" class="component-text" fill="white">权限控制</text>
  </g>
  
  <!-- JavaScript 增强功能 -->
  <g transform="translate(150, 290)">
    <rect x="0" y="0" width="160" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="80" y="15" class="component-text" fill="white">jQuery 3.x</text>
    
    <rect x="180" y="0" width="160" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="260" y="15" class="component-text" fill="white">Axios HTTP</text>
    
    <rect x="360" y="0" width="160" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="440" y="15" class="component-text" fill="white">Chart.js 图表</text>
    
    <rect x="540" y="0" width="160" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="620" y="15" class="component-text" fill="white">DataTables</text>
    
    <rect x="720" y="0" width="160" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="800" y="15" class="component-text" fill="white">SweetAlert2</text>
    
    <rect x="900" y="0" width="140" height="30" fill="#8B5A2B" class="component-box" rx="5" ry="5"/>
    <text x="970" y="15" class="component-text" fill="white">Summernote</text>
  </g>
  
  <!-- 管理功能模块 -->
  <g transform="translate(150, 330)">
    <rect x="0" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="70" y="13" class="component-text" fill="white" font-size="11">用户管理</text>
    
    <rect x="160" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="230" y="13" class="component-text" fill="white" font-size="11">内容管理</text>
    
    <rect x="320" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="390" y="13" class="component-text" fill="white" font-size="11">数据统计</text>
    
    <rect x="480" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="550" y="13" class="component-text" fill="white" font-size="11">系统设置</text>
    
    <rect x="640" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="710" y="13" class="component-text" fill="white" font-size="11">操作日志</text>
    
    <rect x="800" y="0" width="140" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="870" y="13" class="component-text" fill="white" font-size="11">基础配置</text>
    
    <rect x="960" y="0" width="80" height="25" fill="#6F4E37" class="component-box" rx="5" ry="5"/>
    <text x="1000" y="13" class="component-text" fill="white" font-size="11">帮助</text>
  </g>

  <!-- BWMS Admin API 层 -->
  <rect x="130" y="380" width="1100" height="120" fill="#ECFDF5" stroke="#059669" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="405" class="section-title">BWMS Admin API 接口层</text>
  
  <g transform="translate(170, 425)">
    <rect x="0" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="80" y="13" class="component-text" fill="white">管理员认证</text>
    
    <rect x="180" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="260" y="13" class="component-text" fill="white">权限管理</text>
    
    <rect x="360" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="440" y="13" class="component-text" fill="white">内容管理</text>
    
    <rect x="540" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="620" y="13" class="component-text" fill="white">用户管理</text>
    
    <rect x="720" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="800" y="13" class="component-text" fill="white">系统配置</text>
    
    <rect x="900" y="0" width="120" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="960" y="13" class="component-text" fill="white">数据统计</text>
  </g>
  
  <g transform="translate(170, 455)">
    <rect x="0" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="95" y="10" class="component-text" font-size="11">Session 认证</text>
    
    <rect x="210" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="305" y="10" class="component-text" font-size="11">CSRF 保护</text>
    
    <rect x="420" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="515" y="10" class="component-text" font-size="11">表单验证</text>
    
    <rect x="630" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="725" y="10" class="component-text" fill="white">XSS 防护</text>
    
    <rect x="840" y="0" width="180" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="930" y="10" class="component-text" font-size="11">基础架构 API</text>
  </g>

  <!-- Laravel 基础架构层 -->
  <rect x="130" y="520" width="1100" height="100" fill="#FFE6E6" stroke="#FF6666" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="545" class="section-title">Laravel 基础架构层 (精简版)</text>
  
  <g transform="translate(170, 565)">
    <rect x="0" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white">Laravel 核心</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white">Eloquent ORM</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white">路由控制器</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white">中间件</text>
    
    <rect x="800" y="0" width="180" height="25" fill="#FF6666" class="component-box" rx="5" ry="5"/>
    <text x="890" y="13" class="component-text" fill="white">基础服务</text>
  </g>
  
  <g transform="translate(170, 595)">
    <rect x="0" y="0" width="310" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="155" y="10" class="component-text" font-size="11">简化的业务逻辑层</text>
    
    <rect x="330" y="0" width="310" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="485" y="10" class="component-text" font-size="11">传统的 MVC 模式</text>
    
    <rect x="660" y="0" width="300" height="20" fill="#FFAAAA" class="component-box" rx="3" ry="3"/>
    <text x="810" y="10" class="component-text" font-size="11">Blade 模板渲染</text>
  </g>

  <!-- 数据存储层 -->
  <rect x="130" y="640" width="1100" height="80" fill="#E6F0FF" stroke="#6699FF" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="665" class="section-title">数据存储层</text>
  <g transform="translate(230, 685)">
    <rect x="0" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">MySQL 数据库</text>
    
    <rect x="230" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="330" y="13" class="component-text" fill="white">Redis 缓存</text>
    
    <rect x="460" y="0" width="200" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="560" y="13" class="component-text" fill="white">文件存储</text>
    
    <rect x="690" y="0" width="170" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="775" y="13" class="component-text" fill="white">基础日志</text>
  </g>

  <!-- 技术特点说明 -->
  <rect x="130" y="740" width="1100" height="190" fill="#F8F9FA" stroke="#6C757D" stroke-width="2" rx="10" ry="10"/>
  <text x="680" y="765" class="section-title">Bootstrap 管理后台架构特点</text>
  
  <g transform="translate(170, 790)">
    <circle cx="10" cy="10" r="5" fill="#28A745"/>
    <text x="25" y="15" font-size="14" font-weight="bold">优势：</text>
    <text x="80" y="15" font-size="13">• 快速开发部署</text>
    <text x="200" y="15" font-size="13">• 学习成本低</text>
    <text x="300" y="15" font-size="13">• 服务端渲染SEO友好</text>
    <text x="470" y="15" font-size="13">• 资源占用少</text>
    <text x="570" y="15" font-size="13">• 兼容性强</text>
    <text x="650" y="15" font-size="13">• 维护简单</text>
    
    <circle cx="10" cy="40" r="5" fill="#FFC107"/>
    <text x="25" y="45" font-size="14" font-weight="bold">适用：</text>
    <text x="80" y="45" font-size="13">• 中小型团队快速开发</text>
    <text x="250" y="45" font-size="13">• 基础管理功能</text>
    <text x="370" y="45" font-size="13">• 原型验证</text>
    <text x="460" y="45" font-size="13">• 简单数据管理</text>
    <text x="570" y="45" font-size="13">• 报表展示</text>
    
    <circle cx="10" cy="70" r="5" fill="#17A2B8"/>
    <text x="25" y="75" font-size="14" font-weight="bold">技术：</text>
    <text x="80" y="75" font-size="13">• Bootstrap 5.x 响应式UI</text>
    <text x="250" y="75" font-size="13">• jQuery + 原生JavaScript</text>
    <text x="420" y="75" font-size="13">• AdminLTE 管理主题</text>
    <text x="570" y="75" font-size="13">• Laravel MVC</text>
    <text x="670" y="75" font-size="13">• Blade 模板</text>
    
    <circle cx="10" cy="100" r="5" fill="#6F42C1"/>
    <text x="25" y="105" font-size="14" font-weight="bold">特性：</text>
    <text x="80" y="105" font-size="13">• 传统多页面应用</text>
    <text x="200" y="105" font-size="13">• 服务端表单验证</text>
    <text x="340" y="105" font-size="13">• Session 状态管理</text>
    <text x="480" y="105" font-size="13">• 简化的权限控制</text>
    <text x="620" y="105" font-size="13">• 基础数据展示</text>
    
    <circle cx="10" cy="130" r="5" fill="#DC3545"/>
    <text x="25" y="135" font-size="14" font-weight="bold">限制：</text>
    <text x="80" y="135" font-size="13">• 复杂交互体验受限</text>
    <text x="220" y="135" font-size="13">• 不支持实时更新</text>
    <text x="360" y="135" font-size="13">• 前端状态管理简单</text>
    <text x="520" y="135" font-size="13">• 组件复用性较低</text>
  </g>

  <!-- 数据流箭头 -->
  <line x1="680" y1="180" x2="680" y2="200" class="arrow"/>
  <line x1="680" y1="360" x2="680" y2="380" class="arrow"/>
  <line x1="680" y1="500" x2="680" y2="520" class="arrow"/>
  <line x1="680" y1="620" x2="680" y2="640" class="arrow"/>
  
  <!-- 表单提交数据流 -->
  <line x1="270" y1="320" x2="270" y2="425" class="data-flow"/>
  <text x="280" y="375" font-size="12" fill="#8B5A2B">表单提交</text>
  
  <!-- 页面渲染数据流 -->
  <line x1="1090" y1="425" x2="1090" y2="320" class="data-flow"/>
  <text x="1000" y="375" font-size="12" fill="#8B5A2B">页面渲染</text>
  
  <!-- AJAX 请求数据流 -->
  <line x1="470" y1="320" x2="570" y2="425" class="data-flow"/>
  <text x="500" y="375" font-size="11" fill="#8B5A2B">AJAX更新</text>
</svg> 