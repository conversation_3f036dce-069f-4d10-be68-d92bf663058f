<template>
  <div class="common-layout">
    <el-container class="h-screen">
      <el-container class="overflow-hidden">
        <el-aside width="264px" class="h-full">
          <IamMenu></IamMenu>
        </el-aside>
        <el-main>
          <Breadcrumbs v-if="!isHome" />
          <router-view></router-view>
        </el-main>
      </el-container>
      <el-footer> All rights reserved © 2009 - {{ currentYear }} Bingo(HK). </el-footer>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import IamMenu from './components/iam/menu.vue'
import Breadcrumbs from '/admin/components/breadcrumbs/index.vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
const isHome = computed(() => route.path === '/dashboard')
const currentYear = computed(() => new Date().getFullYear())
</script>

<style lang="scss">
body {
  font-family: 'Inter', 'Microsoft JhengHei';
}
// html[lang='zh_CN'] body {
//   font-family: 'Microsoft JhengHei';
// }
// html[lang='en'] body {
//   font-family: 'Inter';
// }

.el-header {
  --el-header-height: 80px;
  --el-header-padding: 16px 30px 16px 20px;
  box-shadow: 0px 3px 6px #0000004d;
  position: relative;
  z-index: 999;
}

.el-aside,
.el-main {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgb(0, 126, 229, 0.3);
    border-radius: 5px;
    background-clip: content-box;
  }
}

.el-aside {
  box-shadow: 0px 3px 6px #00000029;
  padding: 1px 0;
}

.el-main {
  --el-main-padding: 0;
  background-color: #f4f7f9;

  display: flex;
  flex-direction: column;
}

.el-footer {
  --el-footer-height: 50px;

  background-color: #19496a;
  font-size: 14px;
  color: #fff;
  line-height: 1;

  display: flex;
  align-items: center;
  justify-content: center;
}

.bwms-module {
  margin-top: -30px;
  padding: 0 34px 20px;
  flex-grow: 1;
  overflow: hidden;

  display: flex;
  flex-direction: column;

  .module-tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22px;

    h2 {
      color: #19496A;
      font-size: 24px;
      font-weight: bold;
    }

    .btn-list {
      display: flex;
      align-items: center;

      .bwms-btn {
        padding: 12px 20px;
        display: flex;
        --el-button-border-color: #86C0E9;
        --el-button-text-color: #37A0EA;
        --el-border: 2px solid var(--el-button-border-color);
        --el-button-bg-color: #F4F9FD;

        img {
          margin-right: 10px;
          width: 26px;
        }

        .el-icon {
          margin-left: 42px;
        }
      }
    }
  }

  .module-header {
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    min-height: 32px;

    .btn-list {
      display: flex;
    }
  }

  .module-con {
    flex-grow: 1;
    overflow: hidden;

    display: flex;
    flex-direction: column;

    .box {
      border-radius: 10px;
      padding: 5px 20px 15px;
      background-color: #fff;
      flex-grow: 1;
      overflow: hidden;

      &.scroll-box {
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }

        &::-webkit-scrollbar-button {
          display: none;
        }

        &::-webkit-scrollbar-corner {
          background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgb(0, 126, 229, 0.3);
          border-radius: 5px;
          background-clip: content-box;
        }
      }

      .el-tabs {
        display: flex;
        height: 100%;

        .el-tabs__header {
          flex-shrink: 0;
        }

        .el-tabs__content {
          flex-grow: 1;
          overflow-x: hidden;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
          }

          &::-webkit-scrollbar-button {
            display: none;
          }

          &::-webkit-scrollbar-corner {
            background: #f1f1f1;
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgb(0, 126, 229, 0.3);
            border-radius: 5px;
            background-clip: content-box;
          }

          .el-tab-pane {
            height: 100%;
          }
        }
      }
    }

    .box-footer {
      margin-top: 20px;
      flex-shrink: 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
