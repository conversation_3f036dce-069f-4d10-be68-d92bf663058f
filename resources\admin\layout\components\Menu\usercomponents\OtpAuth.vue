<template>
  <div class="otp-auth">
    <div class="otp-header">
      <el-button link @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </el-button>
    </div>

    <div class="otp-steps">
      <el-steps :active="activeStep" finish-status="success">
        <el-step title="下载验证器"></el-step>
        <el-step title="扫描二维码"></el-step>
        <el-step title="输入安全码"></el-step>
        <el-step title="解绑 OTP"></el-step>
      </el-steps>
    </div>

    <div class="step-content">
      <!-- 步骤一：下载验证器 -->
      <template v-if="activeStep === 0">
        <div class="step-title">请使用手机扫码下载验证器</div>
        <div class="authenticator-options">
          <el-button 
            :type="selectedAuthenticator === 'google' ? 'primary' : 'default'" 
            @click="selectAuthenticator('google')"
          >
            Google Authenticator
          </el-button>
          <el-button 
            :type="selectedAuthenticator === 'microsoft' ? 'primary' : 'default'" 
            @click="selectAuthenticator('microsoft')"
          >
            Microsoft Authenticator
          </el-button>
        </div>
        <div class="qr-codes" v-if="selectedAuthenticator">
          <div class="qr-code">
            <img :src="$asset(authenticatorImages[selectedAuthenticator].android)" class="qr-image" alt="Android" />
            <p>Android 下载</p>
          </div>
          <div class="qr-code">
            <img :src="$asset(authenticatorImages[selectedAuthenticator].ios)" class="qr-image" alt="iOS" />
            <p>iOS 下载</p>
          </div>
        </div>
      </template>

      <!-- 步骤二：扫描二维码 -->
      <template v-if="activeStep === 1">
        <div class="step-title">扫描二维码</div>
        <div class="step-description">请在手机打开 Google Authenticator / Microsoft Authenticator 扫码添加安全码。</div>
        <div class="qr-code-container">
          <el-skeleton :loading="isQrCodeLoading" animated>
            <template #template>
              <div class="qr-code-skeleton">
                <el-skeleton-item variant="image" style="width: 200px; height: 200px" />
              </div>
            </template>
            <template #default>
              <img v-if="otpQrCode" :src="otpQrCode" alt="OTP 二维码" class="otp-qr-code" />
            </template>
          </el-skeleton>
        </div>
      </template>

      <!-- 步骤三：输入安全码 -->
      <template v-if="activeStep === 2">
        <div class="step-title">输入安全码</div>
        <div class="step-description">在手机查看并输入 6 位数字安全码，完成后进入下一步</div>
        <div class="verification-code">
          <input
            v-for="(digit, index) in 6"
            :key="index"
            v-model="otpCode[index]"
            type="text"
            maxlength="1"
            @input="onInput(index)"
            @paste="onPaste"
            :ref="el => { if (el) otpInputs[index] = el }"
          />
        </div>
      </template>

      <!-- 步骤四：解绑 OTP -->
      <template v-if="activeStep === 3">
        <div class="step-title">解绑 OTP</div>
        <div class="step-description">请输入您的 OTP 验证码以解绑当前设备</div>
        <div class="verification-code">
          <input
            v-for="(digit, index) in 6"
            :key="index"
            v-model="unbindOtpCode[index]"
            type="text"
            maxlength="1"
            @input="onUnbindInput(index)"
            @paste="onUnbindPaste"
            :ref="el => { if (el) unbindOtpInputs[index] = el }"
          />
        </div>
        <div class="confirm-checkbox">
          <el-checkbox v-model="isConfirmed">我确认要解绑 OTP</el-checkbox>
        </div>
      </template>
    </div>

    <div class="step-actions">
      <el-button v-if="activeStep > 0" @click="activeStep--">上一步</el-button>
      <el-button 
        v-if="activeStep < 3" 
        type="primary" 
        @click="handleNextStep" 
        :loading="isLoading"
      >
        下一步
      </el-button>
      <el-button 
        v-if="activeStep === 3" 
        type="primary" 
        @click="unbindOtp" 
        :disabled="!isConfirmed || unbindOtpCode.join('').length !== 6" 
        :loading="isLoading"
      >
        解绑 OTP
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { UserCenterService } from '../application/UserCenterService'
import { useUserStore } from '/admin/stores/modules/user/userStore'

const activeStep = ref(0)
const selectedAuthenticator = ref('google')
const otpQrCode = computed(() => {
  return localStorage.getItem(`otpQrCode_${userStore.userInfo.id}`) || ''
})
const otpCode = ref<any>(['', '', '', '', '', ''])
const otpInputs = ref<any>([])
const unbindOtpCode = ref<string[]>(['', '', '', '', '', ''])
const unbindOtpInputs = ref<any>([])
const isConfirmed = ref(false)

const emit = defineEmits(['finish-setup', 'go-back'])

const goBack = () => {
  emit('go-back')
}

const selectAuthenticator = (authenticator: string) => {
  selectedAuthenticator.value = authenticator
}

const userCenterService = new UserCenterService()
const userStore: any = useUserStore()

const isQrCodeLoading = ref(false)
const isLoading = ref(false)
const getOtpQrCode = async () => {
  try {
    // 检查本地存储中是否已有二维码
    if (otpQrCode.value) {
      console.log('Using stored QR code')
      return
    }

    const phone = userStore.userInfo.phone
    const email = userStore.userInfo.email

    if (!phone && !email) {
      ElMessage.warning('请先绑定手机号和邮箱后再尝试绑定 OTP')
      return
    }

    if (!phone) {
      ElMessage.warning('请先绑定手机号后再尝试绑定 OTP')
      return
    }

    if (!email) {
      ElMessage.warning('请先绑定邮箱后再尝试绑定 OTP')
      return
    }

    isQrCodeLoading.value = true
    const data = {
      factorType: 'TOTP',
      profile: {
        phoneNumber: phone,
        phoneCountryCode: '+86',
        email: email,
      },
    }
    const response: any = await userCenterService.getOtpEnrollmentQrCode(data)

    localStorage.setItem(`otpQrCode_${userStore.userInfo.id}`, response.data.data.otpData.qrCodeDataUrl)
    localStorage.setItem(`enrollmentToken_${userStore.userInfo.id}`, response.data.data.enrollmentToken)
  } catch (error) {
    console.error('获取 OTP 二维码失败', error)
    ElMessage.error('获取 OTP 二维码失败，请稍后重试')
  } finally {
    isQrCodeLoading.value = false
  }
}

// 添加一个新的响应式变量来跟踪绑定状态
const isBound = computed(() => {
  return localStorage.getItem(`otpBound_${userStore.userInfo.id}`) === 'true'
})

// 在组件挂载时获取用户信息
onMounted(async () => {
  if (!userStore.userInfo.id) {
    await userStore.fetchUserInfo()
  }

  // 如果已绑定，直接跳转到第四步
  if (isBound.value) {
    activeStep.value = 3
  }
})

// 在进入第二步时调用
const onEnterStep2 = () => {
  getOtpQrCode()
}

// 修改 activeStep 的 watch 或 computed 属性
watch(activeStep, newStep => {
  if (newStep === 1) {
    onEnterStep2()
  } else if (newStep === 2) {
    otpCode.value = ['', '', '', '', '', ''] // 重置 OTP 输入
  }
})

const handleDigitInput = (index: number) => {
  if (otpCode.value[index].length === 1 && index < 5) {
    otpInputs.value[index + 1].focus()
  }
}

const handleDelete = (index: number) => {
  if (otpCode.value[index] === '' && index > 0) {
    otpInputs.value[index - 1].focus()
  }
}

const onInput = (index: number) => {
  if (otpCode.value[index] && index < 5) {
    otpInputs.value[index + 1].focus()
  }
}

const onPaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const pastedCode = pastedData.slice(0, 6)

  for (let i = 0; i < pastedCode.length; i++) {
    if (i < 6) {
      otpCode.value[i] = pastedCode[i]
    }
  }

  if (pastedCode.length === 6) {
    otpInputs.value[5].focus()
    verifyOtpCode()
  } else {
    otpInputs.value[pastedCode.length].focus()
  }
}

const handleNextStep = async () => {
  if (activeStep.value === 2) {
    await verifyOtpCode()
  } else {
    activeStep.value++
  }
}

const verifyOtpCode = async () => {
  try {
    const totpCode = otpCode.value.join('')
    if (totpCode.length !== 6) {
      ElMessage.warning('请输入6位数字验证码')
      return
    }
    isLoading.value = true
    const data = {
      factorType: 'TOTP',
      enrollmentToken: localStorage.getItem(`enrollmentToken_${userStore.userInfo.id}`),
      enrollmentData: {
        passCode: totpCode,
        photo: '132434235ad244',
        isExternalPhoto: true,
      },
    }
    const result: any = await userCenterService.verifyOtpCode(data)
    if (result) {
      activeStep.value++
      ElMessage.success('OTP 绑定成功')
      // 设置绑定成功标志
      localStorage.setItem(`otpBound_${userStore.userInfo.id}`, 'true')
    } else {
      ElMessage.error('OTP 验证失败，请重试')
    }
  } catch (error) {
    console.error('OTP 验证失败', error)
    ElMessage.error('OTP 验证失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 添加一个清除本地存储的函数
const clearStoredQrCode = () => {
  localStorage.removeItem(`otpQrCode_${userStore.userInfo.id}`)
  localStorage.removeItem(`enrollmentToken_${userStore.userInfo.id}`)
}

// 添加新的处理函数
const onUnbindInput = (index: number) => {
  if (unbindOtpCode.value[index] && index < 5) {
    unbindOtpInputs.value[index + 1].focus()
  }
}

const onUnbindPaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const pastedCode = pastedData.slice(0, 6)

  for (let i = 0; i < pastedCode.length; i++) {
    if (i < 6) {
      unbindOtpCode.value[i] = pastedCode[i]
    }
  }

  if (pastedCode.length === 6) {
    unbindOtpInputs.value[5].focus()
  } else {
    unbindOtpInputs.value[pastedCode.length].focus()
  }
}

// 修改 resetFactor 函数
const unbindOtp = async () => {
  try {
    const totpCode = unbindOtpCode.value.join('')
    if (totpCode.length !== 6) {
      ElMessage.warning('请输入6位数字验证码')
      return
    }

    isLoading.value = true

    await userCenterService.resetFactor(totpCode)
    ElMessage.success('OTP 解绑成功')
    clearStoredQrCode() // 清除存储的二维码
    // 清除绑定成功标志
    localStorage.removeItem(`otpBound_${userStore.userInfo.id}`)
    emit('finish-setup')
  } catch (error) {
    console.error('OTP 解绑失败', error)
    ElMessage.error('OTP 解绑失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 添加这行来默认导出组件
defineExpose({})

// 修改 authenticatorImages 的类型定义
const authenticatorImages: {
  [key: string]: {
    android: string
    ios: string
  }
} = {
  google: {
    android: 'Iam/Asset/frontendImage/androidGoogle.png',
    ios: 'Iam/Asset/frontendImage/iosGoogle.png',
  },
  microsoft: {
    android: 'Iam/Asset/frontendImage/androidMic.png',
    ios: 'Iam/Asset/frontendImage/iosMic.png',
  },
}
const baseURL = import.meta.env.VITE_APP_BASE_API
// 添加一个 $asset 方法的实现或导入
const $asset = (path: string) => {
  // 这里应该是您项目中实际的 asset 处理逻辑
  // 例如，可能是拼接基础 URL 或使用 Webpack 的 require 等
  return `${baseURL}/Vendor/${path}`
}
</script>

<style lang="scss" scoped>
.otp-auth {
  padding: 20px;
}

.otp-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24px;

  .back-button {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      margin: 0;
    }
  }
}

.otp-steps {
  margin-bottom: 40px;
}

.step-content {
  padding: 24px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  margin-bottom: 24px;

  .step-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
  }

  .step-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 24px;
  }
}

.authenticator-options {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;

  .el-button {
    min-width: 180px;
  }
}

.qr-codes {
  display: flex;
  justify-content: center;
  gap: 40px;

  .qr-code {
    text-align: center;

    .qr-image {
      width: 150px;
      height: 150px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.qr-code-container {
  display: flex;
  justify-content: center;
  margin: 24px 0;

  .otp-qr-code {
    width: 200px;
    height: 200px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    padding: 8px;
  }
}

.verification-code {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin: 24px 0;

  input {
    width: 45px;
    height: 45px;
    text-align: center;
    font-size: 20px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    transition: all 0.3s;

    &:focus {
      border-color: var(--el-color-primary);
      outline: none;
    }
  }
}

.confirm-checkbox {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 12px;

  .el-button {
    min-width: 100px;
  }
}
</style>
