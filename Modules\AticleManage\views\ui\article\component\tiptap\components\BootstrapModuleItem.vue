<template>
  <div
    class="module-item-container"
    :class="{ 'active': isActive }"
    draggable="true"
    @dragstart="handleDragStart"
    @click="handleClick"
  >
    <div class="module-icon" v-if="module.icon">
      <el-icon :size="32">
        <component :is="module.icon" />
      </el-icon>
    </div>
    <div class="module-title">{{ module.name }}</div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, defineOptions } from 'vue'
import { ElIcon } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'BootstrapModuleItem'
})

const props = defineProps({
  module: {
    type: Object,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['select', 'drag', 'click-insert'])

const handleClick = () => {
  emit('select', props.module.type)
  // 触发点击插入事件
  emit('click-insert', props.module.type)
}

const handleDragStart = (event: DragEvent) => {
  if (!event.dataTransfer) return
  
  try {
    // 设置明确的拖拽文本数据 - 这是兼容性最好的方法
    event.dataTransfer.setData('text/plain', props.module.type)
    
    // 对于支持自定义类型的浏览器，同时设置自定义类型
    // 注意：有些浏览器可能不完全支持非标准MIME类型，故同时设置两种数据
    event.dataTransfer.setData('application/bootstrap-component', props.module.type)
    
    // 明确设置拖拽效果为复制
    event.dataTransfer.effectAllowed = 'copy'
    
    // 确保拖拽目标元素的样式表明可拖拽
    const element = event.currentTarget as HTMLElement
    if (element) {
      // 添加正在拖拽的视觉反馈
      element.classList.add('dragging')
      
      // 延迟移除样式
      setTimeout(() => {
        element.classList.remove('dragging')
      }, 100)
    }

    // 通知父组件拖拽开始
    emit('drag', props.module.type)
    
    console.log('拖拽已设置：', props.module.type)
  } catch (error) {
    console.error('设置拖拽数据失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.module-item-container {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
  border: 1px solid #ebeef5;
  box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.05);
  width: 120px;
  height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  }

  &.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }
}

.module-icon {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
}

.module-title {
  font-size: 12px;
  text-align: center;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.module-item-container.dragging {
  opacity: 0.6;
  transform: scale(0.95);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border-color: #409eff;
  background-color: #ecf5ff;
}

/* 强化拖拽提示 */
.module-item-container::after {
  content: '拖拽添加';
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 10px;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border-radius: 2px;
  padding: 2px 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.module-item-container:hover::after {
  opacity: 1;
}
</style>
