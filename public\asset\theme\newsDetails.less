@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .news-details {
    margin-bottom: 52px;

    .container {
      .df(flex-start);

      .news-content {
        padding: 30px 50px;
        background-color: #fff;
        flex-grow: 1;

        h3 {
          margin-bottom: 10px;
          font-weight: bold;
          font-size: 28px;
          color: #333;
        }

        .news-info {
          margin-bottom: 20px;
          border-bottom: 1px solid #F7F7F7;
          padding-bottom: 20px;
          .df(center);

          span {
            margin-right: 10px;
            display: block;
            font-size: 14px;
            color: #888;
          }
        }

        .rich-text {
          margin-bottom: 58px;

          p {
            margin-bottom: 10px;
            font-size: 16px;
            line-height: 1.75;
          }
        }

        .collection-btn {
          margin-bottom: 30px;
          .df(center, center);

          .btn-box {
            .btn-radius(50px, 10px, 16px, #383838, #F7F7F7, #aaa, #fff);
          }
        }

        .tag-list {
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
          .df(center);

          .tag-text {
            color: #333;
            font-size: 14px;
          }

          .tag-item {
            margin-left: 10px;
            border: 1px solid #ff9600;
            .btn-radius(50px, 5px, 14px, #ff9600, transparent, transparent);
          }
        }

        .more {
          margin-top: 30px;
          .df(center);

          .more-news {
            flex-grow: 1;

            .prev-news {
              margin-bottom: 10px;
              color: #333;
              font-size: 14px;
              line-height: 1.71;
              .df(center);

              a {
                color: #333;
                line-height: 1.71;
              }
            }

            .next-news {
              font-size: 14px;
              color: #333;
              line-height: 1.71;
              .df(center);

              span {
                color: #888;
                line-height: 1.71;
              }
            }
          }

          .back-list {
            .btn-box {
              .btn-radius(50px, 12px, 14px, #fff, #ff9600, #FCB319);
            }
          }
        }
      }

      .side {
        padding-left: 30px;
        width: calc(25% + 30px);
        flex-shrink: 0;

        .tit {
          margin-bottom: 10px;
          line-height: 1.11;
          color: #333;
          font-size: 18px;
          .df(stretch);

          &::before {
            margin-right: 15px;
            content: '';
            display: block;
            width: 5px;
            background-color: #ff9600;
          }
        }

        .random-news {
          margin-bottom: 20px;
          padding: 20px;
          background-color: #fff;

          .random-list {
            .news-item {
              padding-top: 5px;
              padding-bottom: 5px;
              .df(center);

              &::before {
                content: "";
                margin-right: 12px;
                border-radius: 50%;
                width: 6px;
                height: 6px;
                background-color: #ccc;
                display: block;
                flex-shrink: 0;
              }

              a {
                color: #6E6E6E;
                line-height: 1.71;
                font-size: 14px;
                .vertical(1);
                flex-grow: 1;
                line-height: 1.71;
                transition: color .35s ease-in-out;

                &:hover {
                  color: #ff9600;
                }
              }
            }
          }
        }

        .product-sell {
          padding: 20px;
          background-color: #fff;

          .product-list {
            .item {
              margin-bottom: 20px;
              cursor: pointer;
              
              .df(flex-start);

              .pic {
                width: 100px;

                img {
                  transform: scale(1);
                  transition: transform .35s ease-in-out;
                }
              }

              .product-name {
                padding-left: 15px;
                color: #6E6E6E;
                line-height: 1.5;
                font-size: 16px;
              }

              &:hover {
                .pic {
                  img {
                    transform: scale(1.1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}