<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// 创建 tvb_pen_names 和 tvb_admin_pen_names 表的迁移文件
// 本文件由AI自动生成，所有字段均带中文注释
return new class extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        // 创建笔名表
        Schema::create('pen_names', function (Blueprint $table) {
            $table->increments('id')->comment('笔名ID');
            $table->string('pen_name', 100)->unique()->comment('笔名');
            $table->tinyInteger('pen_name_type')->default(1)->comment('笔名类型: 1-记者, 2-编辑, 3-特约, 4-其他');
            $table->string('description', 255)->nullable()->comment('笔名描述');
            $table->integer('created_by')->comment('创建者ID');
            $table->tinyInteger('status')->default(1)->comment('状态: 0-禁用, 1-启用');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间');
        });

        // 创建管理员笔名关联表
        Schema::create('admin_pen_names', function (Blueprint $table) {
            $table->increments('id')->comment('关联ID');
            $table->integer('admin_id')->comment('管理员ID');
            $table->integer('pen_name_id')->comment('笔名ID');
            $table->boolean('is_default')->default(false)->comment('是否为默认笔名');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('软删除时间');
            $table->unique(['admin_id', 'pen_name_id'], 'uk_admin_pen_name');
        });
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_pen_names');
        Schema::dropIfExists('pen_names');
    }
}; 