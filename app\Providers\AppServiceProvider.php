<?php

namespace App\Providers;

use Bingo\Core\Services\NavigationService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Carbon;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(NavigationService::class, function () {
            return new NavigationService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 设置PHP时区与Laravel应用时区一致
        date_default_timezone_set(config('app.timezone'));
        
        // 全局设置Carbon日期序列化格式
        Carbon::serializeUsing(function ($carbon) {
            return $carbon->format('Y-m-d H:i:s');
        });
    }
}
