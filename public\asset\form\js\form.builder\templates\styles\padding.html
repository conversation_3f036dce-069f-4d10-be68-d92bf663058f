<div class="form-group styles-{{- name }}">
    <label class="form-label" for="{{- name }}">{{- label }}</label>
    <input type="text" id="{{- name }}" class="form-control data-list" name="{{- name }}" value="{{- value }}" placeholder="{{- placeholder }}" list="{{- name }}-data-list">
    <datalist id="{{- name }}-data-list">
        <option value="auto">auto</option>
        <option value="initial">initial</option>
        <option value="inherit">inherit</option>
        <option value="0">0</option>
        {{ _.each(_.range(1, 26, 1), function(number) { }}
        <option value="{{- number }}px">{{- number }}px</option>
        {{ }); }}
        {{ _.each(_.range(30, 100, 5), function(number) { }}
        <option value="{{- number }}px">{{- number }}px</option>
        {{ }); }}
        {{ _.each(_.range(1, 26, 1), function(number) { }}
        <option value="{{- number }}%">{{- number }}%</option>
        {{ }); }}
        {{ _.each(_.range(1, 26, 1), function(number) { }}
        <option value="0 {{- number }}px">0 {{- number }}px</option>
        {{ }); }}
        {{ _.each(_.range(5, 30, 5), function(number) { }}
        <option value="0 {{- number }}%">0 {{- number }}%</option>
        {{ }); }}
    </datalist>
</div>