<?php

// 设置执行时间
set_time_limit(0);
ignore_user_abort(true);

// 加载 Laravel 框架
require __DIR__.'/../vendor/autoload.php';
$app = require_once __DIR__.'/../bootstrap/app.php';

// 验证访问令牌
$config = require __DIR__.'/../config/cronjob.php';
$validToken = $config['runner_token'] ?? env('CRONJOB_RUNNER_TOKEN');

if (empty($_GET['token']) || $_GET['token'] !== $validToken) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

// 验证IP白名单
$allowedIps = $config['allowed_ips'] ?? [];
$clientIp = $_SERVER['REMOTE_ADDR'];
if (!empty($allowedIps) && !in_array($clientIp, $allowedIps)) {
    header('HTTP/1.1 403 Forbidden');
    exit('IP not allowed');
}

try {
    // 执行任务
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->call('cronjob:run', [
        '--daemon' => true,
        '--interval' => $_GET['interval'] ?? 1
    ]);
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    exit('Error: ' . $e->getMessage());
}
