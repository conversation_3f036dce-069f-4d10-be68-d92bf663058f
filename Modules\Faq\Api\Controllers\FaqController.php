<?php

namespace Modules\Faq\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Faq\Models\Faq;
use Modules\Faq\Models\FaqSeo;
use Modules\Faq\Models\FaqTag;
use Modules\Faq\Services\FaqService;

/**
 * 常见问题控制器
 * 负责处理常见问题的增删改查等操作
 */
class FaqController extends Controller
{

    public function __construct(private FaqService $faqService){}

    /**
     * 获取常见问题列表
     * 支持分页、分类筛选、状态筛选
     *
     * @param Request $request 请求对象
     * @return array 常见问题列表
     */
    public function index(Request $request): array
    {
        // 获取分页参数，默认每页10条
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 10);
        $keyword = $request->input('keyword', '');
        $categoryId = $request->input('category_id', '');
        $status = $request->input('status', '');

        // 构建查询，Laravel软删除会自动排除已删除的记录
        $query = Faq::query();

        // 关键字筛选
        if (!empty($keyword)) {
            $query->where('title', 'like', '%' . $keyword . '%');
            $query->orWhere('content', 'like', '%' . $keyword . '%');
        }

        if (!empty($categoryId)) {
            $query->where('category_id', $categoryId);
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        // 排序：先按sort_order升序，再按id降序
        $query->orderBy('sort_order', 'asc')->orderBy('id', 'desc');

        // 分页获取数据
        $faqs = $query->with('category')->paginate($limit, ['*'], 'page', $page);

        // 返回JSON响应
        return [
            'total' => $faqs->total(),
            'page' => $faqs->currentPage(),
            'limit' => $faqs->perPage(),
            'items' => $faqs->items(),
        ];
    }

    /**
     * 新建常见问题
     *
     * @param Request $request 请求对象
     * @return array 创建结果
     */
    public function store(Request $request): array
    {
        // 验证请求参数
        $validated = $request->validate([
            'category_id' => 'required|integer|exists:faq_category,id',
            'status'      => 'required|integer|in:0,1,2',
            'sort_order'  => 'nullable|integer|min:0',
            'contents'   => 'required|array',   // 多语言内容数组
            'contents.*.title' => 'required|string|max:255',
            'contents.*.content' => 'required|string',
            'contents.*.lang'   => 'required|string|in:zh_HK,zh_CN,en',
            'tags'       => 'nullable|array',   // 标签数组（可以是ID或名称）
            'tags.*'      => 'required',  // 标签项必填（可以是整数ID或字符串名称）
            'seo'         => 'nullable|array',   // SEO数组
            'seo.title'   => 'nullable|string|max:255',
            'seo.description' => 'nullable|string|max:255',
            'seo.keywords' => 'nullable|string|max:255',
            'seo.regular_url' => 'nullable|string|max:255',
            'seo.allow_index' => 'nullable|boolean',
            'seo.allow_follow' => 'nullable|boolean',
            'seo.open_graph_title' => 'nullable|string|max:255',
            'seo.open_graph_description' => 'nullable|string|max:255',
        ]);

        $result = $this->faqService->createFaq($validated);
        return $result;
    }

    /**
     * 获取指定ID的常见问题详情
     *
     * @param int $id 常见问题ID
     * @return array 常见问题详情
     */
    public function show(int $id): array
    {
        // 查询常见问题，Laravel软删除会自动排除已删除的记录
        $faq = Faq::with('category')->find($id);

        if (!$faq) {
            // 未找到
            return [];
        }

        // 返回详情
        return $faq->toArray();
    }

    /**
     * 更新指定ID的常见问题
     *
     * @param Request $request 请求对象
     * @param int $id 常见问题ID
     * @return array 更新结果
     */
    public function update(Request $request, int $id): array
    {
        // 查询常见问题，Laravel软删除会自动排除已删除的记录
        $faq = Faq::find($id);

        if (!$faq) {
            return [];
        }

        // 验证请求参数
        $validated = $request->validate([
            'code'        => 'sometimes|required|string|max:64|unique:faq,code,' . $id,
            'category_id' => 'sometimes|required|integer|exists:faq_category,id',
            'title'       => 'sometimes|required|string|max:255',
            'content'     => 'sometimes|required|string',
            'status'      => 'sometimes|required|integer|in:0,1,2',
            'sort_order'  => 'nullable|integer|min:0',
        ]);

        // 更新常见问题
        $faq->update($validated);

        // 返回JSON响应
        return $faq->toArray();
    }

    /**
     * 删除指定ID的常见问题（软删除）
     *
     * @param int $id 常见问题ID
     * @return array 删除结果
     */
    public function destroy(int $id): array
    {
        // 查询常见问题，Laravel软删除会自动排除已删除的记录
        $faq = Faq::findOrFail($id);

        try {
            // 开始事务
            DB::beginTransaction();

            // 1. 删除相关的SEO记录（使用软删除）
            if ($faq->seo) {
                $faq->seo->delete();
            }

            // 2. 删除相关的标签关联记录（使用软删除）
            $faq->faqTags()->each(function ($tag) {
                $tag->delete();
            });

            // 3. 软删除FAQ本身
            $faq->delete();

            // 提交事务
            DB::commit();

            return [
                'success' => true,
                'message' => 'FAQ删除成功',
                'deleted_id' => $id
            ];

        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            Log::error('FAQ删除失败', [
                'faq_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'FAQ删除失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量删除常见问题（软删除）
     * @param Request $request
     * @return array
     */
    public function batchDelete(Request $request): array
    {
        $ids = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required',
        ]);

        if (empty($ids)) {
            throw new \Exception('No ids provided');
        }

        try {
            // 开始事务
            DB::beginTransaction();

            // 1. 批量删除相关的SEO记录（使用软删除）
            FaqSeo::softDeleteByField($ids, 'faq_id');

            // 2. 批量删除相关的标签关联记录（使用软删除）
            FaqTag::softDeleteByField($ids, 'faq_id');

            // 3. 批量软删除FAQ记录
            $count = Faq::softDeleteByIds($ids);

            // 提交事务
            DB::commit();

            return [
                'success' => true,
                'deleted' => $count,
                'message' => "成功删除 {$count} 条FAQ记录"
            ];

        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            Log::error('批量删除FAQ失败', [
                'faq_ids' => $ids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'deleted' => 0,
                'message' => '批量删除失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量下架常见问题（状态设为2）
     * @param Request $request
     * @return array
     */
    public function batchUnshelf(Request $request): array
    {
        $ids = $request->input('ids', []);
        if (!is_array($ids) || empty($ids)) {
            return ['updated' => 0, 'message' => 'No ids provided'];
        }
        // 批量更新状态为2（已下架），Laravel软删除会自动排除已删除的记录
        $count = Faq::whereIn('id', $ids)->update(['status' => 2]);
        return ['updated' => $count];
    }

}
