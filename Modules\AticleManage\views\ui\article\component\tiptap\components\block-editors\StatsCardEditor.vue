<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.statsCardEditor.tab.content')" name="content">
        <el-form label-position="top" size="small">
          <!-- 标题和描述 -->
          <el-form-item :label="$t('Editor.statsCardEditor.form.title')">
            <el-input v-model="cardData.title" :placeholder="$t('Editor.statsCardEditor.form.titlePlaceholder')" @input="markAsChanged" />
          </el-form-item>
          <el-form-item :label="$t('Editor.statsCardEditor.form.description')">
            <el-input v-model="cardData.description" type="textarea" :rows="3" :placeholder="$t('Editor.statsCardEditor.form.descriptionPlaceholder')" @input="markAsChanged" />
          </el-form-item>

          <!-- 图片设置 -->
          <el-divider content-position="left">{{$t('Editor.statsCardEditor.form.imageSetting')}}</el-divider>
          <div class="image-preview-container">
            <div v-if="cardData.image.src" class="image-preview">
              <img :src="cardData.image.src" class="preview-img" />
            </div>
            <div v-else class="image-placeholder">
              <el-icon class="placeholder-icon"><Picture /></el-icon>
              <div class="placeholder-text">{{$t('Editor.statsCardEditor.form.noImage')}}</div>
            </div>
          </div>

          <!-- 图片操作按钮 -->
          <div class="button-group">
            <el-button type="primary" class="button-no-border" @click="openFileManager">
              <el-icon class="icon"><Upload /></el-icon>
              <span>{{$t('Editor.statsCardEditor.form.selectImage')}}</span>
            </el-button>
            <el-button v-if="cardData.image.src" class="delete-btn" @click="confirmDeleteImage">
              <el-icon class="icon"><Delete /></el-icon>
              <span>{{$t('Editor.statsCardEditor.form.deleteImage')}}</span>
            </el-button>
          </div>

          <el-form-item :label="$t('Editor.statsCardEditor.form.imageAlt')">
            <el-input v-model="cardData.image.alt" @input="markAsChanged" :placeholder="$t('Editor.statsCardEditor.form.imageAltPlaceholder')" />
          </el-form-item>

          <!-- 统计数据项 -->
          <el-divider content-position="left">{{$t('Editor.statsCardEditor.form.statsItems')}}</el-divider>
          <div class="stat-items">
            <div v-for="(item, index) in cardData.stats" :key="index" class="stat-item-edit">
              <div class="item-header">
                <h4 class="item-title">{{$t('Editor.statsCardEditor.form.statItem')}} #{{ index + 1 }}</h4>
                <el-button
                  v-if="cardData.stats.length > 1"
                  type="danger"
                  size="small"
                  circle
                  :icon="Delete"
                  @click="removeStat(index)"
                />
              </div>
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item :label="$t('Editor.statsCardEditor.form.value')">
                    <el-input v-model="item.value" :placeholder="$t('Editor.statsCardEditor.form.valuePlaceholder')" @input="markAsChanged" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('Editor.statsCardEditor.form.label')">
                    <el-input v-model="item.label" :placeholder="$t('Editor.statsCardEditor.form.labelPlaceholder')" @input="markAsChanged" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item :label="$t('Editor.statsCardEditor.form.icon')">
                <el-select v-model="item.icon" :placeholder="$t('Editor.statsCardEditor.form.iconPlaceholder')" style="width: 100%" @change="markAsChanged">
                  <el-option :label="$t('Editor.statsCardEditor.icon.users')" value="fas fa-users" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.chartLine')" value="fas fa-chart-line" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.checkCircle')" value="fas fa-check-circle" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.eye')" value="fas fa-eye" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.rocket')" value="fas fa-rocket" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.star')" value="fas fa-star" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.coins')" value="fas fa-coins" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.heart')" value="fas fa-heart" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.globe')" value="fas fa-globe" />
                  <el-option :label="$t('Editor.statsCardEditor.icon.bolt')" value="fas fa-bolt" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="stat-controls">
            <el-button type="primary" @click="addStat" :disabled="cardData.stats.length >= 4">{{$t('Editor.statsCardEditor.form.addStat')}}</el-button>
          </div>

          <!-- 按钮设置 -->
          <el-divider content-position="left">{{$t('Editor.statsCardEditor.form.buttonSetting')}}</el-divider>
          <el-form-item :label="$t('Editor.statsCardEditor.form.buttonText')">
            <el-input v-model="cardData.button.text" :placeholder="$t('Editor.statsCardEditor.form.buttonTextPlaceholder')" @input="markAsChanged" />
          </el-form-item>
          <el-form-item :label="$t('Editor.statsCardEditor.form.buttonLink')">
            <el-input v-model="cardData.button.link" :placeholder="$t('Editor.statsCardEditor.form.buttonLinkPlaceholder')" @input="markAsChanged" />
          </el-form-item>
          <el-form-item :label="$t('Editor.statsCardEditor.form.buttonShow')">
            <el-switch v-model="cardData.button.show" @change="markAsChanged" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane :label="$t('Editor.statsCardEditor.tab.style')" name="style">
        <StyleEditor
          :styles="localStyles"
          :active-tab="activeTab"
          @update-styles="updateStyles"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">{{$t('Editor.statsCardEditor.button.apply')}}</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="false" 
      :locale="localeLang"
      @confirmSelection="confirmSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Delete, Picture, Upload } from '@element-plus/icons-vue'
import { defineAsyncComponent } from 'vue'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
const StyleEditor = defineAsyncComponent(() => import('./StyleEditor.vue'))

// 定义组件名称
defineOptions({
  name: 'StatsCardEditor'
})

// 自定义类型定义
interface StatItem {
  icon: string;
  value: string;
  label: string;
}

interface CardButton {
  text: string;
  link: string;
  show: boolean;
}

interface CardImage {
  src: string;
  alt: string;
}

interface CardData {
  title: string;
  description: string;
  stats: StatItem[];
  button: CardButton;
  image: CardImage;
  customAttributes?: Record<string, string>;
}

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    required: true
  },
  styles: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update-block', 'update-styles'])

const activeTab = ref('content')
const localStyles = ref({ ...props.styles })

// 原始HTML
const originalHtml = ref('')

// 是否有未保存的更改
const isChanged = ref(false)

// 卡片数据
const cardData = ref<CardData>({
  title: '提升您的数据分析能力',
  description: '我们的数据分析平台帮助企业深入了解关键指标，做出更明智的决策，提高业务效率并实现可持续增长。',
  stats: [
    { icon: 'fas fa-users', value: '25,317', label: '满意客户' },
    { icon: 'fas fa-chart-line', value: '10倍', label: '搜索流量增长' },
    { icon: 'fas fa-check-circle', value: '已认证', label: '安全可靠' },
    { icon: 'fas fa-eye', value: '4,532', label: '每日浏览量' }
  ],
  button: {
    text: '立即探索',
    link: '#',
    show: true
  },
  image: {
    src: 'https://via.placeholder.com/600x700',
    alt: '数据分析专家'
  },
  customAttributes: {}
})

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')
const { t } = useI18n()

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

/**
 * 解析统计卡片组件数据
 */
const extractCardData = (): boolean => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 解析当前元素中的数据
    const element = props.blockElement
    
    // 提取自定义属性
    cardData.value.customAttributes = extractAttributes(element)
    
    // 提取标题
    const titleEl = element.querySelector('.stats-title, .feature-title')
    if (titleEl) {
      cardData.value.title = titleEl.textContent?.trim() || '提升您的数据分析能力'
    }
    
    // 提取描述
    const descriptionEl = element.querySelector('.stats-description, .feature-description')
    if (descriptionEl) {
      cardData.value.description = descriptionEl.textContent?.trim() || '我们的数据分析平台帮助企业深入了解关键指标...'
    }
    
    // 提取统计项
    const statItems = element.querySelectorAll('.stat-item, .floating-badge')
    if (statItems.length > 0) {
      const newStats: StatItem[] = []
      
      statItems.forEach((item, index) => {
        if (index < 4) { // 最多支持4个统计项
          const iconEl = item.querySelector('.stat-icon i, .badge-icon i')
          const valueEl = item.querySelector('.stat-value, .badge-value')
          const labelEl = item.querySelector('.stat-label, .badge-label')
          
          newStats.push({
            icon: iconEl ? iconEl.className : 'fas fa-users',
            value: valueEl ? valueEl.textContent?.trim() || '0' : '0',
            label: labelEl ? labelEl.textContent?.trim() || '标签' : '标签'
          })
        }
      })
      
      if (newStats.length > 0) {
        cardData.value.stats = newStats
      }
    }
    
    // 提取按钮
    const buttonEl = element.querySelector('.stats-cta .btn, .feature-cta .btn')
    if (buttonEl) {
      cardData.value.button = {
        text: buttonEl.textContent?.trim() || '立即探索',
        link: buttonEl.getAttribute('href') || '#',
        show: true
      }
    } else {
      cardData.value.button.show = false
    }
    
    // 提取图片
    const imageEl = element.querySelector('.stats-image, .feature-image')
    if (imageEl) {
      cardData.value.image = {
        src: imageEl.getAttribute('src') || 'https://via.placeholder.com/600x700',
        alt: imageEl.getAttribute('alt') || '数据分析专家'
      }
    }
    
    return true
  } catch (error) {
    console.error('提取统计卡片数据时出错:', error)
    return false
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 重置卡片数据到默认值
const resetToDefault = () => {
  cardData.value = {
    title: '提升您的数据分析能力',
    description: '我们的数据分析平台帮助企业深入了解关键指标，做出更明智的决策，提高业务效率并实现可持续增长。',
    stats: [
      { icon: 'fas fa-users', value: '25,317', label: '满意客户' },
      { icon: 'fas fa-chart-line', value: '10倍', label: '搜索流量增长' }
    ],
    button: {
      text: '立即探索',
      link: '#',
      show: true
    },
    image: {
      src: 'https://via.placeholder.com/600x700',
      alt: '数据分析专家'
    },
    customAttributes: {}
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue) => {
  if (newValue) {
    const extracted = extractCardData()
    if (!extracted) {
      // 如果无法提取数据，使用默认值
      resetToDefault()
    }
  } else {
    // 如果 blockElement 为空，重置为默认值
    resetToDefault()
  }
  // 重置更改状态
  isChanged.value = false
}, { immediate: true })

// 修改 onMounted 钩子
onMounted(() => {
  // 初始化时标记为未更改
  isChanged.value = false
})

// 添加统计项
const addStat = () => {
  if (cardData.value.stats.length < 4) {
    cardData.value.stats.push({
      icon: 'fas fa-star',
      value: '新数据',
      label: '新标签'
    })
    markAsChanged()
  }
}

// 移除统计项
const removeStat = (index: number) => {
  cardData.value.stats.splice(index, 1)
  markAsChanged()
}

// 准备提交的HTML
const prepareCardHTML = (): string => {
  try {
    // 使用与原始结构相似的HTML结构
    const isFeatureLayout = originalHtml.value.includes('feature-image') || originalHtml.value.includes('feature-title')
    
    // 生成属性字符串
    let attributesStr = ''
    if (cardData.value.customAttributes) {
      Object.entries(cardData.value.customAttributes).forEach(([key, value]) => {
        if (key !== 'class' && key !== 'style') {
          attributesStr += ` ${key}="${value}"`
        }
      })
    }
    
    // 使用与原始HTML匹配的类名
    const titleClass = isFeatureLayout ? 'feature-title' : 'stats-title'
    const descriptionClass = isFeatureLayout ? 'feature-description' : 'stats-description'
    const imageWrapperClass = isFeatureLayout ? 'feature-image-wrapper' : 'stats-image-wrapper'
    const imageClass = isFeatureLayout ? 'feature-image' : 'stats-image'
    const ctaClass = isFeatureLayout ? 'feature-cta' : 'stats-cta'
    const contentWrapperClass = isFeatureLayout ? 'content-wrapper' : 'stats-content'
    
    // 构建HTML - 使用与原始结构匹配的结构
    let html = `
    <div data-bs-component="stats-card" class="stats-card-block"${attributesStr}>
      <div class="container py-5">
        <div class="row align-items-center">
    `
    
    // 根据原始HTML的结构确定图片和内容的位置
    const isImageFirst = originalHtml.value.indexOf('col-md-6') < originalHtml.value.indexOf(titleClass) || 
                         !originalHtml.value.includes(titleClass)
    
    if (isImageFirst) {
      // 图片在左侧，内容在右侧
      html += `
          <!-- 左侧图片 -->
          <div class="col-md-6">
            <div class="${imageWrapperClass}">
              <img src="${cardData.value.image.src}" alt="${cardData.value.image.alt}" class="${imageClass} img-fluid rounded-lg shadow">
              
              <!-- 浮动徽章 -->
              <div class="floating-badge customer-badge">
                <div class="badge-icon">
                  <i class="${cardData.value.stats[0]?.icon || 'fas fa-heart text-danger'}"></i>
                </div>
                <div class="badge-content">
                  <div class="badge-value">${cardData.value.stats[0]?.value || '25,317'}</div>
                  <div class="badge-label">${cardData.value.stats[0]?.label || '满意客户'}</div>
                </div>
              </div>
              
              <div class="floating-badge stats-badge">
                <div class="badge-content">
                  <i class="${cardData.value.stats[1]?.icon || 'fas fa-chart-line text-primary me-2'}"></i>
                  <span>${cardData.value.stats[1]?.value || '10倍'} ${cardData.value.stats[1]?.label || '搜索流量增长'}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧内容 -->
          <div class="col-md-6">
            <div class="${contentWrapperClass}">
              <h2 class="${titleClass}">${cardData.value.title}</h2>
              <p class="${descriptionClass}">
                ${cardData.value.description}
              </p>
              
              ${cardData.value.button.show ? `
              <div class="mt-4 ${ctaClass}">
                <a href="${cardData.value.button.link}" class="btn btn-primary btn-lg rounded-pill">${cardData.value.button.text}</a>
              </div>
              ` : ''}
            </div>
          </div>
      `
    } else {
      // 内容在左侧，图片在右侧
      html += `
          <!-- 左侧内容 -->
          <div class="col-md-6">
            <div class="${contentWrapperClass}">
              <h2 class="${titleClass}">${cardData.value.title}</h2>
              <p class="${descriptionClass}">
                ${cardData.value.description}
              </p>
              
              <div class="stats-grid">
                <div class="row">
      `
      
      // 添加统计项
      cardData.value.stats.forEach((stat, index) => {
        html += `
                  <div class="mb-4 col-6">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="${stat.icon}"></i>
                      </div>
                      <div class="stat-info">
                        <h3 class="stat-value">${stat.value}</h3>
                        <p class="stat-label">${stat.label}</p>
                      </div>
                    </div>
                  </div>
        `
      })
      
      html += `
                </div>
              </div>
              
              ${cardData.value.button.show ? `
              <div class="mt-4 ${ctaClass}">
                <a href="${cardData.value.button.link}" class="btn btn-primary btn-lg">${cardData.value.button.text}</a>
              </div>
              ` : ''}
            </div>
          </div>
          
          <!-- 右侧图片 -->
          <div class="col-md-6">
            <div class="${imageWrapperClass}">
              <img src="${cardData.value.image.src}" alt="${cardData.value.image.alt}" class="${imageClass} img-fluid">
            </div>
          </div>
      `
    }
    
    // 保留原始CSS样式
    const styleMatch = originalHtml.value.match(/<style>([\s\S]*?)<\/style>/)
    const styleContent = styleMatch ? styleMatch[1] : `
    .stats-card-block {
      padding: 40px 0;
      background-color: #f8f9fa;
      overflow: hidden;
    }
    
    .${titleClass} {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1.5rem;
    }
    
    .${descriptionClass} {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    
    .stat-item {
      display: flex;
      align-items: center;
      padding: 15px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.08);
      height: 100%;
      transition: transform 0.3s ease;
    }
    
    .stat-item:hover {
      transform: translateY(-5px);
    }
    
    .stat-icon {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f1f5ff;
      border-radius: 50%;
      margin-right: 15px;
      color: #5e72e4;
      font-size: 20px;
    }
    
    .stat-info {
      flex: 1;
    }
    
    .stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: #333;
      margin: 0;
      line-height: 1.2;
    }
    
    .stat-label {
      font-size: 0.85rem;
      color: #888;
      margin: 0;
    }
    
    .${imageWrapperClass} {
      position: relative;
      padding: 20px;
    }
    
    .${imageClass} {
      border-radius: 15px;
      box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }
    
    .floating-badge {
      position: absolute;
      background: white;
      padding: 10px 15px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      display: flex;
      align-items: center;
      z-index: 2;
    }
    
    .customer-badge {
      top: 30px;
      left: 20px;
      padding: 12px 18px;
    }
    
    .stats-badge {
      bottom: 30px;
      right: 20px;
    }
    
    .badge-icon {
      margin-right: 10px;
      font-size: 20px;
    }
    
    .badge-content {
      display: flex;
      align-items: center;
    }
    
    .badge-value {
      font-weight: 700;
      font-size: 18px;
      color: #333;
      line-height: 1.2;
    }
    
    .badge-label {
      font-size: 14px;
      color: #666;
    }
    
    .${ctaClass} .btn {
      padding: 12px 30px;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    
    .${ctaClass} .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,123,255,0.3);
    }
    
    @media (max-width: 767.98px) {
      .${contentWrapperClass} {
        margin-bottom: 2rem;
      }
      
      .${imageWrapperClass} {
        padding: 0;
      }
      
      .floating-badge {
        transform: scale(0.85);
      }
    }
    `
    
    // 完成HTML
    html += `
        </div>
      </div>
      
      <style>${styleContent}</style>
      
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    </div>
    `
    
    return html.trim()
  } catch (error) {
    console.error('准备统计卡片HTML时出错:', error)
    // 在出错的情况下，返回原始HTML
    return originalHtml.value || ''
  }
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareCardHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    ElMessage.success(t('Editor.statsCardEditor.message.updateSuccess'))
  } catch (error) {
    console.error('应用统计卡片更改时出错:', error)
    ElMessage.error(t('Editor.statsCardEditor.message.updateFail'))
  }
}

// 监听并更新样式变化
watch(() => localStyles.value, (newStyles) => {
  emit('update-styles', newStyles)
}, { deep: true })

// 更新样式
const updateStyles = (styles: any) => {
  localStyles.value = styles
  markAsChanged()
}

// 打开文件管理器
const openFileManager = () => {
  visibleDialog.value = true
}

// 确认删除图片
const confirmDeleteImage = () => {
  ElMessageBox.confirm(
    '确定要删除当前图片吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    cardData.value.image.src = ''
    cardData.value.image.alt = ''
    markAsChanged()
    ElMessage.success('图片已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// 文件选择确认
const confirmSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0) return
  
  const file = selectedFiles[0]
  cardData.value.image.src = file.path || file.url
  markAsChanged()
}
</script>

<style lang="scss" scoped>
.edit-section {
  padding: 10px 0;
}

.stat-item-edit {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 5px;
  margin-bottom: 15px;
  background-color: #f8f9fa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.stat-controls {
  margin: 15px 0;
  display: flex;
  justify-content: center;
}

.apply-button-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

.image-preview-container {
  width: 100%;
  height: 200px;
  background-color: #F6F6F6;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 4px;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .placeholder-icon {
    font-size: 40px;
    color: #9E9E9E;
    margin-bottom: 10px;
  }
  
  .placeholder-text {
    color: #9E9E9E;
    font-size: 16px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  .delete-btn {
    color: #707070;
    background: #FFFFFF;
    border-radius: 5px;
  }
}
</style> 