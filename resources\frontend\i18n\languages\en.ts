const en = {
    system: {
        name: 'BWMS Dashboard',
        chinese: 'Chinese',
        english: 'English',
        confirm: 'Confirm',
        cancel: 'Cancel',
        warning: 'Warning',
        next: 'Next',
        prev: 'Prev',
        yes: 'Y',
        no: 'N',
        add: 'Add',
        finish: 'Finish',
        back: 'Back',
        update: 'Update',
        export: 'Export',
        search: 'Search',
        refresh: 'Refresh',
        detail: 'Detail',
        delete: 'Delete',
        prompt: 'Prompt',
        more: 'More',
        logout: 'Log Out',
        fail: 'Fail',
        success: 'Success',
        close: 'Close',
        download: 'Download',
        lang: {
            en: 'English',
            zh_HK: 'Traditional Chinese',
            zh_CN: 'Simplified Chinese',
        },
    },

    login: {
        email: 'Email',
        password: 'Password',
        sign_in: 'Sign In',
        welcome: 'Welcome Back👏',
        lost_password: 'lost password?',
        remember: 'Remember me',
        verify: {
            email: {
                required: 'Please input email first',
                invalid: 'Email address is invalid',
            },

            password: {
                required: 'Please input password first',
            },
        },
    },

    register: {
        sign_up: 'Sign Up',
    },

    generate: {
        schema: {
            title: 'Create Schema',
            name: 'Schema Name',
            name_verify: 'please input schema name',
            engine: {
                name: 'Search Engine',
                verify: 'please select schema engine',
                placeholder: 'select schema engine',
            },
            default_field: {
                name: 'Default Field',
                created_at: 'Create time',
                updated_at: 'Update Time',
                creator: 'Creator',
                delete_at: 'SoftDelete',
            },
            comment: {
                name: 'Schema Comment',
                verify: 'please input schema comment',
            },

            structure: {
                title: 'Create Schema Structure',
                field_name: {
                    name: 'Field Name',
                    verify: 'please input field name',
                },
                length: 'Length',
                type: {
                    name: 'Field Type',
                    placeholder: 'select field type',
                    verify: 'please select field type',
                },
                form_label: 'Form Label',
                form_component: 'Component',
                list: 'List',
                form: 'Form',
                unique: 'Unique',
                search: 'Search',
                search_op: {
                    name: 'Search Operate',
                    placeholder: 'select search operate',
                },
                nullable: 'Nullable',
                default: 'Default',
                rules: {
                    name: 'Verify Rules',
                    placeholder: 'select verify rules',
                },
                operate: 'Operate',
                comment: 'Field Comment',
            },
        },
        code: {
            title: 'Code Gen',
            module: {
                name: 'module',
                placeholder: 'please select module',
                verify: 'please select module first',
            },
            controller: {
                name: 'Controller',
                placeholder: 'please input controller name',
                verify: 'please input Controller name  first',
            },
            model: {
                name: 'Model',
                placeholder: 'please input model name',
                verify: 'please input model name  first',
            },
            paginate: 'Paginate',
        },
    },

    module: {
        create: 'Create Module',
        update: 'Update Module',
        form: {
            name: {
                title: 'Module Name',
                required: 'module name required',
            },

            path: {
                title: 'Module Path',
                required: 'module Path required',
            },

            desc: {
                title: 'Description',
            },

            keywords: {
                title: 'Keywords',
            },

            dirs: {
                title: 'Default Dirs',
                Controller: 'Controller',
                Model: 'Model',
                Database: 'Database',
                Request: 'Request',
            },
        },
    },
    widgets: {
        widget_configuration: 'Widget Configuration',
        report_widget: 'Configure Report Widget',
        width: 'Width (1-12)',
        newLine: 'Force New Line',
        title: 'Widget Title',
        manage: 'Manage Widget',
        add: 'Add Widget',
        widgets: 'Widget',
        close: 'Close',
        sure: 'Confirm',
        loading: 'Loading...',
        title_required: 'Please enter the widget title',
        x: 'X',
        y: 'Y',
        h: 'Height',
    },

    header: {
        settings: {
            tit1: 'Account',
            li1: 'Profile & Preferences',
            li2: 'Reset password',
            tit2: 'System Language',
            li3: 'Language Switching',
            langswitch: 'Language Switching',
        },
        contact: {
            title: 'Your After-Sales Specialist',
            label1: 'Service hours',
            label2: 'Email',
            btn_text: 'Send Email',
        },
    },
    home: {
        title: 'DASHBOARD',
        btn_text1: 'Add Widget',
        dialog_tit1: 'Are you sure to delete this module?',
        activityLog: {
            th1: 'Account',
            th2: 'Browser',
            th3: 'Platform',
            th4: 'IP',
            th5: 'State',
            th6: 'Login Time',
        },
        analytics: {
            placeholder1: 'Start Time',
            placeholder2: 'End Time',
            reportTypes: {
                basicUser: 'Basic User Report',
                trafficSource: 'Traffic Source Report',
                pagePerformance: 'Page Performance Report',
                ecommerce: 'E-commerce Report',
                userBehavior: 'User Behavior Report',
                mobileApp: 'Mobile App Report',
                adPerformance: 'Ad Performance Report',
                content: 'Content Analysis Report',
                geographic: 'Geographic Report',
                technical: 'Technical Analysis Report',
                event: 'Event Analysis Report',
                conversionFunnel: 'Conversion Funnel Report',
            },
        },
        news: {
            title: 'News',
        },
        system: {
            title1: 'Website Storage Capacity',
            title2: 'How many days are left until renewal',
            con1: 'Day',
            btn_text1: 'View Storage',
            btn_text2: 'Renewal Now',
            btn_text3: 'Increase Capacity',
        },
    },
    404: {
        tips: 'Sorry, the page you visited does not exist.',
        btn_text: 'Return to Home',
    },
    analytics: {
        dimensions: {
            date: 'Date',
            country: 'Country',
            deviceCategory: 'Device Category',
            source: 'Source',
            medium: 'Medium',
            campaign: 'Campaign',
            pagePath: 'Page Path',
            itemName: 'Item Name',
            itemCategory: 'Item Category',
            sessionSourceMedium: 'Session Source/Medium',
            landingPage: 'Landing Page',
            appVersion: 'App Version',
            operatingSystem: 'Operating System',
            adGroup: 'Ad Group',
            adContent: 'Ad Content',
            pageTitle: 'Page Title',
            region: 'Region',
            city: 'City',
            browser: 'Browser',
            eventName: 'Event Name',
            eventCategory: 'Event Category',
        },
        metrics: {
            totalUsers: 'Total Users',
            newUsers: 'New Users',
            sessions: 'Sessions',
            bounceRate: 'Bounce Rate',
            screenPageViews: 'Screen/Page Views',
            averageSessionDuration: 'Avg. Session Duration',
            conversions: 'Conversions',
            averagePageLoadTime: 'Avg. Page Load Time',
            exitRate: 'Exit Rate',
            itemViews: 'Item Views',
            itemsAddedToCart: 'Items Added to Cart',
            purchases: 'Purchases',
            itemRevenue: 'Item Revenue',
            engagementRate: 'Engagement Rate',
            conversionsPerSession: 'Conversions per Session',
            crashFreeUsersRate: 'Crash-Free Users Rate',
            userEngagementDuration: 'User Engagement Duration',
            adClicks: 'Ad Clicks',
            adImpressions: 'Ad Impressions',
            adCost: 'Ad Cost',
            adConversions: 'Ad Conversions',
            averageTimeOnPage: 'Avg. Time on Page',
            entrances: 'Entrances',
            eventCount: 'Event Count',
            eventValue: 'Event Value',
            addToCarts: 'Add to Carts',
            checkouts: 'Checkouts',
        },
    },
}

export default en
