(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.dropdown_input=factory())})(this,function(){"use strict";const KEY_ESC=27;const KEY_TAB=9;typeof navigator==="undefined"?false:/Mac/.test(navigator.userAgent);const accent_pat="[̀-ͯ·ʾʼ]";const latin_convert={};const latin_condensed={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let latin in latin_condensed){let unicode=latin_condensed[latin]||"";for(let i=0;i<unicode.length;i++){let char=unicode.substring(i,i+1);latin_convert[char]=latin}}new RegExp(Object.keys(latin_convert).join("|")+"|"+accent_pat,"gu");const iterate=(object,callback)=>{if(Array.isArray(object)){object.forEach(callback)}else{for(var key in object){if(object.hasOwnProperty(key)){callback(object[key],key)}}}};const getDom=query=>{if(query.jquery){return query[0]}if(query instanceof HTMLElement){return query}if(isHtmlString(query)){var tpl=document.createElement("template");tpl.innerHTML=query.trim();return tpl.content.firstChild}return document.querySelector(query)};const isHtmlString=arg=>{if(typeof arg==="string"&&arg.indexOf("<")>-1){return true}return false};const addClasses=(elmts,...classes)=>{var norm_classes=classesArray(classes);elmts=castAsArray(elmts);elmts.map(el=>{norm_classes.map(cls=>{el.classList.add(cls)})})};const classesArray=args=>{var classes=[];iterate(args,_classes=>{if(typeof _classes==="string"){_classes=_classes.trim().split(/[\11\12\14\15\40]/)}if(Array.isArray(_classes)){classes=classes.concat(_classes)}});return classes.filter(Boolean)};const castAsArray=arg=>{if(!Array.isArray(arg)){arg=[arg]}return arg};const preventDefault=(evt,stop=false)=>{if(evt){evt.preventDefault();if(stop){evt.stopPropagation()}}};const addEvent=(target,type,callback,options)=>{target.addEventListener(type,callback,options)};function plugin(){const self=this;self.settings.shouldOpen=true;self.hook("before","setup",()=>{self.focus_node=self.control;addClasses(self.control_input,"dropdown-input");const div=getDom('<div class="dropdown-input-wrap">');div.append(self.control_input);self.dropdown.insertBefore(div,self.dropdown.firstChild);const placeholder=getDom('<input class="items-placeholder" tabindex="-1" />');placeholder.placeholder=self.settings.placeholder||"";self.control.append(placeholder)});self.on("initialize",()=>{self.control_input.addEventListener("keydown",evt=>{switch(evt.keyCode){case KEY_ESC:if(self.isOpen){preventDefault(evt,true);self.close()}self.clearActiveItems();return;case KEY_TAB:self.focus_node.tabIndex=-1;break}return self.onKeyDown.call(self,evt)});self.on("blur",()=>{self.focus_node.tabIndex=self.isDisabled?-1:self.tabIndex});self.on("dropdown_open",()=>{self.control_input.focus()});const orig_onBlur=self.onBlur;self.hook("instead","onBlur",evt=>{if(evt&&evt.relatedTarget==self.control_input)return;return orig_onBlur.call(self)});addEvent(self.control_input,"blur",()=>self.onBlur());self.hook("before","close",()=>{if(!self.isOpen)return;self.focus_node.focus({preventScroll:true})})})}return plugin});