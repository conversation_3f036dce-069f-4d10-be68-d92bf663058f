<template>
  <div class="breadcrumb-box">
    <h1>{{ currentPageTitle }}</h1>
    <el-breadcrumb separator-icon="ArrowRight">
      <transition-group name="breadcrumb">
        <el-breadcrumb-item 
          v-for="(item, index) in appStore.breadcrumbs" 
          :key="item.path"
        >
          <span class="breadcrumb-item" :class="{ 'active': index === appStore.breadcrumbs.length - 1 }">
            {{ getDisplayName(item) }}
          </span>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script lang="ts" setup>
import router from '/admin/router'
import { watch, onMounted, ref, computed, nextTick, onUnmounted } from 'vue'
import { useAppStore } from '/admin/stores/modules/app'
import { RouteLocationNormalizedLoaded } from 'vue-router'
import { Breadcrumb } from '/admin/stores/modules/app'
import useEventBus from '/admin/support/eventBus'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// 使用事件总线来接收导航数据
const { $on, $off } = useEventBus()

interface NavItem {
  nav_name: string;
  url: string;
  path: string;
  key: string;
  icon?: string;
  children?: NavItem[];
  [key: string]: any;
}

// 面包屑路径缓存，以路由路径为键，面包屑数组为值
interface BreadcrumbCache {
  [key: string]: Breadcrumb[];
}

const appStore = useAppStore()
const navList = ref<NavItem[]>([])
// 缓存已计算过的面包屑路径
const breadcrumbCache = ref<BreadcrumbCache>({})
// 最后一次更新的路径
const lastPath = ref<string>('')
// 标记是否已处理过当前路由
const routeProcessed = ref<boolean>(false)
// 初始化完成状态
const initialized = ref<boolean>(false)

// 添加判断是否需要翻译的函数
function shouldTranslate(name: string): boolean {
  return name.startsWith('Iam.routes');
}

// 检查是否是i18n key
function isI18nKey(item: Breadcrumb): boolean {
  return item.i18n === true;
}

// 添加路由标题翻译函数
function translateRouteTitle(title: string): string {
  if (!title) return '';
  
  try {
    // 如果是多语言key（包含点号），翻译它
    if (title.includes('.')) {
      return t(title);
    }
    // 否则直接返回原标题
    return title;
  } catch (error) {
    console.warn(`Failed to translate route title: ${title}`, error);
    return title;
  }
}

// 获取显示名称（翻译或原始）
function getDisplayName(item: Breadcrumb): string {
  if (shouldTranslate(item.name) || isI18nKey(item)) {
    return t(item.name);
  }
  return item.name;
}

// 计算当前页面标题
const currentPageTitle = computed(() => {
  const breadcrumbs = appStore.breadcrumbs
  if (breadcrumbs.length > 0) {
    const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1]
    
    // 统一使用翻译函数处理标题
    if (shouldTranslate(lastBreadcrumb.name) || isI18nKey(lastBreadcrumb)) {
      return t(lastBreadcrumb.name)
    }
    
    // 对于可能是路由标题的情况，也尝试翻译
    return translateRouteTitle(lastBreadcrumb.name)
  }
  return ''
})

// 生成缓存键
function getCacheKey(route: RouteLocationNormalizedLoaded): string {
  // 使用活动菜单+查询参数作为缓存键
  const activeMenu = appStore.getActiveMenu
  const modelId = route.query.model_id
  return modelId ? `${activeMenu}?model_id=${modelId}` : activeMenu
}

// 添加 CMS_BREADCRUMB_CONFIG
const CMS_BREADCRUMB_CONFIG = computed(() => ({
  '1': {
    name: t('dashboard.cms.news.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.news.edit')
  },
  '2': {
    name: t('dashboard.cms.case.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.case.edit')
  },
  '3': {
    name: t('dashboard.cms.product.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.product.edit')
  },
  '4': {
    name: t('dashboard.cms.job.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.job.edit')
  },
  '5': {
    name: t('dashboard.cms.page.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.page.edit')
  },
  '6': {
    name: t('dashboard.cms.messages.name'),
    parent: t('dashboard.cms.parent'),
    edit: t('dashboard.cms.messages.edit')
  }
}))

// 设置活动菜单
const setActiveMenu = (route: RouteLocationNormalizedLoaded) => {
  if (route.path !== '/') {
    // 设置激活菜单，优先使用 meta.active_menu
    if (route.meta.active_menu) {
      appStore.setActiveMenu(route.meta.active_menu as string)
    } else {
      appStore.setActiveMenu(route.path)
    }
  }
}

// 递归查找导航项
function findNavItemByPath(navItems: NavItem[], path: string): NavItem | null {
  // 使用与menus.vue相同的匹配逻辑
  const matchItem = (item: NavItem, targetPath: string) => {
    // 兼容不同的属性名（url 或 path）
    const itemPath = item.url || item.path
    
    // 对于CMS列表路径特殊处理
    if (
      (itemPath.includes('/cms/cmsList') && targetPath.includes('/cms/cmsList')) ||
      (itemPath.includes('/cms/categories') && targetPath.includes('/cms/categories'))
    ) {
      const [itemBasePath, itemQuery] = itemPath.split('?')
      const [targetBasePath, targetQuery] = targetPath.split('?')
      
      // 先比较基础路径
      if (itemBasePath !== targetBasePath) {
        return false
      }
      
      // 如果基础路径相同，进一步比较model_id
      const itemParams = new URLSearchParams(itemQuery || '')
      const targetParams = new URLSearchParams(targetQuery || '')
      
      return itemParams.get('model_id') === targetParams.get('model_id')
    }
    
    // 常规路径比较
    const [itemBasePath, itemQuery] = itemPath.split('?')
    const [targetBasePath, targetQuery] = targetPath.split('?')

    // 如果基础路径不同，直接返回false
    if (itemBasePath !== targetBasePath) {
      return false
    }

    // 如果有查询参数，需要进一步匹配model_id
    if (itemQuery || targetQuery) {
      const itemModelId = itemQuery ? new URLSearchParams(itemQuery).get('model_id') : null
      const targetModelId = targetQuery ? new URLSearchParams(targetQuery).get('model_id') : null
      return itemModelId === targetModelId
    }

    return true
  }

  // 递归查找匹配项
  for (const item of navItems) {
    if (matchItem(item, path)) {
      return item
    }
    
    if (item.children?.length) {
      const found = findNavItemByPath(item.children, path)
      if (found) return found
    }
  }
  
  return null
}

// 递归构建面包屑路径
function buildBreadcrumbPath(
  navItems: NavItem[], 
  targetPath: string, 
  parentPath: NavItem[] = []
): Breadcrumb[] | null {
  for (const item of navItems) {
    // 当前导航路径
    const itemPath = item.url || item.path
    // 当前导航项加上所有父级
    const currentPath = [...parentPath, item]
    
    // 判断当前项是否匹配目标路径
    const [itemBasePath, itemQuery] = itemPath.split('?')
    const [targetBasePath, targetQuery] = targetPath.split('?')
    
    // 如果基础路径匹配
    if (itemBasePath === targetBasePath) {
      // 如果查询参数也匹配(或都没有查询参数)
      const itemModelId = itemQuery ? new URLSearchParams(itemQuery).get('model_id') : null
      const targetModelId = targetQuery ? new URLSearchParams(targetQuery).get('model_id') : null
      
      if (itemModelId === targetModelId) {
        // 转换为面包屑格式并返回
        return currentPath.map(nav => ({
          name: nav.nav_name, 
          path: nav.url || nav.path
        }))
      }
    }
    
    // 递归查找子项
    if (item.children?.length) {
      const foundPath = buildBreadcrumbPath(item.children, targetPath, currentPath)
      if (foundPath) return foundPath
    }
  }
  
  return null
}

// 修改 handleCmsDetailBreadcrumb 函数，完善CMS路由处理
function handleCmsDetailBreadcrumb(route: RouteLocationNormalizedLoaded): boolean {
  // 处理推荐模块相关路由
  if (route.name === 'recommendList' || route.name === 'recommendDetail' || route.name === 'recommendCreate') {
    appStore.clearBreadcrumbs()
    appStore.addBreadcrumb({
      name: t('dashboard.home'),
      path: '/'
    })
    appStore.addBreadcrumb({
      name: t('dashboard.cms.parent'),
      path: '/cms'
    })
    appStore.addBreadcrumb({
      name: t('dashboard.cms.recommend.name'),
      path: '/recommend/recommendList'
    })
    
    // 根据路由名称判断是列表、创建还是详情
    if (route.name === 'recommendCreate') {
      appStore.addBreadcrumb({
        name: t('dashboard.cms.recommend.create'),
        path: route.fullPath
      })
    } else if (route.name === 'recommendDetail') {
      appStore.addBreadcrumb({
        name: t('dashboard.cms.recommend.detail'),
        path: route.fullPath
      })
    }
    
    // 标记路由已处理
    routeProcessed.value = true
    return true
  }
  
  // 处理CMS详情和创建页面
  if (route.name === 'cmsDetail' || route.name === 'cmsCreate') {
    const modelId = route.query.model_id as keyof typeof CMS_BREADCRUMB_CONFIG.value
    const config = CMS_BREADCRUMB_CONFIG.value[modelId]
    
    if (config) {
      appStore.clearBreadcrumbs()
      appStore.addBreadcrumb({
        name: t('dashboard.home'),
        path: '/'
      })
      appStore.addBreadcrumb({
        name: config.parent,
        path: '/cms'
      })
      appStore.addBreadcrumb({
        name: config.name,
        path: `/cms?model_id=${modelId}`
      })
      
      // 根据路由名称判断是创建还是编辑
      if (route.name === 'cmsCreate') {
        appStore.addBreadcrumb({
          name: t('dashboard.cms.create'),
          path: route.fullPath
        })
      } else if (config.edit) {
        appStore.addBreadcrumb({
          name: config.edit,
          path: route.fullPath
        })
      }
      
      // 标记路由已处理
      routeProcessed.value = true
      return true
    }
  }
  
  // 处理CMS列表页面
  if (route.path === '/cms/cmsList' || route.path.startsWith('/cms/cmsList/')) {
    const modelId = route.query.model_id as keyof typeof CMS_BREADCRUMB_CONFIG.value
    const config = CMS_BREADCRUMB_CONFIG.value[modelId]
    
    if (config) {
      appStore.clearBreadcrumbs()
      appStore.addBreadcrumb({
        name: t('dashboard.home'),
        path: '/'
      })
      appStore.addBreadcrumb({
        name: t('dashboard.content.title'),
        path: '/content'
      })
      appStore.addBreadcrumb({
        name: config.name,
        path: `/cms/cmsList?model_id=${modelId}`
      })
      
      // 标记路由已处理
      routeProcessed.value = true
      return true
    }
  }
  
  // 处理CMS分类页面
  if (route.path === '/cms/categories') {
    const modelId = route.query.model_id as keyof typeof CMS_BREADCRUMB_CONFIG.value
    const config = CMS_BREADCRUMB_CONFIG.value[modelId]
    
    if (config) {
      appStore.clearBreadcrumbs()
      appStore.addBreadcrumb({
        name: t('dashboard.home'),
        path: '/'
      })
      appStore.addBreadcrumb({
        name: t('dashboard.content.title'),
        path: '/content'
      })
      appStore.addBreadcrumb({
        name: config.name,
        path: `/cms/cmsList?model_id=${modelId}`
      })
      appStore.addBreadcrumb({
        name: t('dashboard.cms.categories'),
        path: route.fullPath
      })
      
      // 标记路由已处理
      routeProcessed.value = true
      return true
    }
  }
  
  // 处理联系我们页面
  if (route.path === '/cms/contact/' && route.query.model_id === '6') {
    appStore.clearBreadcrumbs()
    appStore.addBreadcrumb({
      name: t('dashboard.home'),
      path: '/'
    })
    appStore.addBreadcrumb({
      name: t('dashboard.content.title'),
      path: '/content'
    })
    appStore.addBreadcrumb({
      name: t('dashboard.cms.messages.name'),
      path: `/cms/cmsList/?model_id=6`
    })
    appStore.addBreadcrumb({
      name: t('dashboard.cms.contact.records'),
      path: route.fullPath
    })
    
    // 标记路由已处理
    routeProcessed.value = true
    return true
  }
  
  return false
}

// 基于路由更新面包屑（备用方法）
function updateBreadcrumbsFromRoute(route: RouteLocationNormalizedLoaded, resetBreadcrumbs: boolean = true) {
  // 是否需要重置面包屑
  if (resetBreadcrumbs) {
    appStore.clearBreadcrumbs()
    appStore.addBreadcrumb({
      name: t('dashboard.home'),
      path: '/'
    })
  }
  
  // 添加路由匹配的面包屑
  route.matched.forEach(m => {
    if (m.meta.title !== undefined && m.path !== '/') {
      // 获取标题并进行翻译处理
      const title = m.meta.title as string;
      const translatedTitle = translateRouteTitle(title);
      
      appStore.addBreadcrumb({
        name: translatedTitle,
        path: m.path,
        i18n: m.meta.i18n as boolean | undefined
      })
    }
  })
}

// 基于导航数据更新面包屑
function updateBreadcrumbsFromNav(route: RouteLocationNormalizedLoaded) {
  // 清空现有面包屑
  appStore.clearBreadcrumbs()
  
  // 添加首页（始终添加，不从缓存中获取）
  appStore.addBreadcrumb({
    name: t('dashboard.home'),
    path: '/'
  })
  
  // 获取当前路径
  const fullPath = route.query.model_id 
    ? `${appStore.getActiveMenu}?model_id=${route.query.model_id}` 
    : appStore.getActiveMenu
  
  // 检查是否是动态路由
  const isDynamicRoute = route.matched.some(m => m.path.includes(':'))
  
  // 仅在非动态路由且缓存存在时使用缓存
  if (!isDynamicRoute && breadcrumbCache.value[fullPath]) {
    // 从缓存中获取面包屑并设置
    const cachedBreadcrumbs = breadcrumbCache.value[fullPath]
    cachedBreadcrumbs.forEach(item => {
      appStore.addBreadcrumb(item)
    })
    return
  }
  
  // 从导航数据构建面包屑路径
  const breadcrumbPath = buildBreadcrumbPath(navList.value, fullPath)
  
  // 如果找到路径，设置面包屑并缓存
  if (breadcrumbPath && breadcrumbPath.length > 0) {
    // 设置面包屑
    breadcrumbPath.forEach(item => {
      appStore.addBreadcrumb(item)
    })
    
    // 仅在非动态路由时缓存
    if (!isDynamicRoute) {
      // 缓存面包屑路径（不包括首页）
      breadcrumbCache.value[fullPath] = breadcrumbPath
    }
  } else {
    // 如果没有找到，回退到基于路由的面包屑
    updateBreadcrumbsFromRoute(route, false)
    
    // 仅在非动态路由时缓存
    if (!isDynamicRoute) {
      // 获取当前面包屑状态（除了首页）
      const currentBreadcrumbs = [...appStore.breadcrumbs].slice(1)
      // 缓存面包屑路径
      breadcrumbCache.value[fullPath] = currentBreadcrumbs
    }
  }
}

// 统一的更新面包屑函数优化
function updateBreadcrumbs(route: RouteLocationNormalizedLoaded) {
  // 针对CMS特殊页面先进行处理
  const isCmsRoute = route.path.includes('/cms/') && route.query.model_id;
  
  // 首先尝试处理特殊路由
  if (isCmsRoute && handleCmsDetailBreadcrumb(route)) {
    return // 如果是特殊路由并已处理，直接返回
  } else if (handleCmsDetailBreadcrumb(route)) {
    // 处理其他特殊路由
    return
  }
  
  // 更新缓存键
  const cacheKey = getCacheKey(route)
  lastPath.value = cacheKey
  
  // 如果导航数据已加载，使用导航数据更新面包屑
  if (navList.value && navList.value.length > 0) {
    updateBreadcrumbsFromNav(route)
  } else {
    // 导航数据尚未加载，使用基于路由的方式
    updateBreadcrumbsFromRoute(route, true)
  }
}

// 检查路由是否需要更新面包屑
function shouldUpdateBreadcrumbs(newRoute: RouteLocationNormalizedLoaded, oldRoute?: RouteLocationNormalizedLoaded): boolean {
  // 如果没有旧路由，说明是首次加载
  if (!oldRoute) return true
  
  // 检查路径是否改变
  const pathChanged = newRoute.path !== oldRoute.path
  
  // 检查查询参数是否改变
  const queryChanged = JSON.stringify(newRoute.query) !== JSON.stringify(oldRoute.query)
  
  // 检查路由名称是否改变
  const nameChanged = newRoute.name !== oldRoute.name
  
  return pathChanged || queryChanged || nameChanged
}

// 导航列表就绪处理函数增强
const handleNavListReady = (data: NavItem[]) => {
  // 检查导航数据是否变化
  const navDataChanged = JSON.stringify(navList.value) !== JSON.stringify(data)
  
  // 更新导航数据
  navList.value = data
  
  // 导航数据变化，清空缓存
  if (navDataChanged) {
    breadcrumbCache.value = {}
  }
  
  // 只有在组件初始化后才触发重新计算
  if (initialized.value && !routeProcessed.value) {
    nextTick(() => {
      // 检测是否为CMS页面，如果是且需要处理，先执行特殊处理
      const currentRoute = router.currentRoute.value;
      if (currentRoute.path.includes('/cms/')) {
        if (!handleCmsDetailBreadcrumb(currentRoute)) {
          updateBreadcrumbs(currentRoute);
        }
      } else {
        updateBreadcrumbs(currentRoute);
      }
    })
  }
  
  // 标记初始化完成
  initialized.value = true
}

// 修改 watch 监听函数
watch(
  () => router.currentRoute.value,
  (newRoute, oldRoute) => {
    // 设置活动菜单
    setActiveMenu(newRoute)
    
    // 重置处理状态
    routeProcessed.value = false
    
    // 判断是否需要更新面包屑
    if (shouldUpdateBreadcrumbs(newRoute, oldRoute)) {
      updateBreadcrumbs(newRoute)
    }
  },
  { immediate: true }
)

// 初始化
onMounted(() => {
  // 订阅导航数据就绪事件
  $on('navList:ready', handleNavListReady)
})

// 组件卸载时解绑事件
onUnmounted(() => {
  $off('navList:ready', handleNavListReady)
})
</script>

<style lang="scss" scoped>
.breadcrumb-box {
  padding: 34px 34px 0;
  flex-shrink: 0;

  h1 {
    margin: 0;
    margin-bottom: 10px;
    font-size: 30px;
    color: #202020;
    line-height: 40px;
  }

  .el-breadcrumb {
    .el-breadcrumb__item {
      .breadcrumb-item {
        font-size: 14px;
        color: #7E7E7E;
        line-height: 19px;
        text-decoration: none;
        
        .active {
          color: #7E7E7E;
          cursor: default;
        }
      }

      .el-breadcrumb__separator {
        margin: 0;
        color: #7E7E7E;
      }
    }
  }
}

// 面包屑过渡动画
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.3s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.3s;
}

.breadcrumb-leave-active {
  position: absolute;
}
</style>
