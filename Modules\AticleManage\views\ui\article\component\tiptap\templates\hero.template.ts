export const heroTemplate = `
<div class="py-5 hero-section responsive-block" data-bs-component="hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6 hero-content">
        <div data-bs-component="rich-text" class="bootstrap-heading">
          <h1 class="mb-3 display-4 fw-bold hero-title">Create legendary brands</h1>
          <p class="text-muted hero-description">Add a brief and powerful description of your business value proposition and how you solve for customers</p>
        </div>
        <div class="gap-2 mt-4 d-flex hero-buttons">
          <div data-bs-component="button">
            <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Request demo</button>
          </div>
          <div data-bs-component="button">
            <button class="px-4 py-2 bootstrap-button btn btn-outline-secondary rounded-pill">Learn more</button>
          </div>
        </div>
      </div>
      <div class="col-lg-6 hero-image-wrapper">
        <div class="position-relative">
          <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/hero-banner.png" class="img-fluid rounded-4 hero-image" alt="Hero image">
          <div class="p-3 bg-white shadow position-absolute preview-panel rounded-3">
            <div class="gap-2 d-flex align-items-center">
              <div class="rounded-circle bg-success" style="width: 12px; height: 12px;"></div>
              <span>Browser preview</span>
            </div>
            <div class="mt-2">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch1" checked>
                <label class="form-check-label" for="previewSwitch1">Mobile</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch2" checked>
                <label class="form-check-label" for="previewSwitch2">Desktop</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch3" checked>
                <label class="form-check-label" for="previewSwitch3">Tablet</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch4" checked>
                <label class="form-check-label" for="previewSwitch4">Print</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .hero-section {
      overflow: hidden;
      position: relative;
      background-color: #fff;
    }

    .hero-content {
      position: relative;
      z-index: 1;
    }

    .hero-title {
      color: #2d3748;
      line-height: 1.2;
    }

    .hero-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: #4a5568;
    }

    .hero-buttons {
      flex-wrap: wrap;
    }

    .hero-buttons .btn {
      transition: all 0.3s ease;
    }

    .hero-buttons .btn:hover {
      transform: translateY(-2px);
    }

    .hero-image-wrapper {
      position: relative;
    }

    .hero-image {
      width: 100%;
      height: auto;
      transition: transform 0.3s ease;
    }

    .preview-panel {
      max-width: 200px;
      right: -30px;
      top: 50%;
      transform: translateY(-50%);
    }

    /* 移动端预览模式样式 */
    .mobile-preview .hero-section {
      padding: 2rem 0;
    }

    .mobile-preview .hero-content {
      text-align: center;
      padding: 0 1rem;
    }

    .mobile-preview .hero-title {
      font-size: 2rem;
      margin-bottom: 1rem;
    }

    .mobile-preview .hero-description {
      font-size: 1rem;
      margin-bottom: 1.5rem;
    }

    .mobile-preview .hero-buttons {
      justify-content: center;
      gap: 0.5rem !important;
    }

    .mobile-preview .hero-buttons .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .mobile-preview .hero-image-wrapper {
      margin-top: 2rem;
      padding: 0 1rem;
    }

    .mobile-preview .preview-panel {
      display: none;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .hero-section {
        padding: 2rem 0;
      }

      .hero-content {
        text-align: center;
        padding: 0 1rem;
      }

      .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
      }

      .hero-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
      }

      .hero-buttons {
        justify-content: center;
        gap: 0.5rem !important;
      }

      .hero-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
      }

      .hero-image-wrapper {
        margin-top: 2rem;
        padding: 0 1rem;
      }

      .preview-panel {
        display: none;
      }
    }

    /* 平板端样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .hero-section {
        padding: 3rem 0;
      }

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-description {
        font-size: 1.1rem;
      }

      .hero-buttons .btn {
        padding: 0.5rem 1.5rem;
      }

      .preview-panel {
        max-width: 180px;
        right: -20px;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .hero-section {
        padding: 5rem 0;
      }

      .hero-title {
        font-size: 3.5rem;
      }

      .hero-description {
        font-size: 1.25rem;
      }

      .hero-buttons .btn {
        padding: 0.75rem 2rem;
      }

      .preview-panel {
        max-width: 200px;
        right: -30px;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .hero-section {
      padding: 5rem 0;
    }

    .desktop-preview .hero-content {
      text-align: left;
      padding: 0;
    }

    .desktop-preview .hero-title {
      font-size: 3.5rem;
    }

    .desktop-preview .hero-description {
      font-size: 1.25rem;
    }

    .desktop-preview .hero-buttons {
      justify-content: flex-start;
    }

    .desktop-preview .hero-buttons .btn {
      width: auto;
      padding: 0.75rem 2rem;
    }

    .desktop-preview .hero-image-wrapper {
      margin-top: 0;
    }

    .desktop-preview .preview-panel {
      display: block;
    }
  </style>
</div>
` 