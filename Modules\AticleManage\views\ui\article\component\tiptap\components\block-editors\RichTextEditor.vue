<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="$t('Editor.richTextEditor.tab.content')" name="content">
        <div class="editor-container">
          <div class="editor-toolbar">
            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive('bold') }"
                @click="editor?.chain().focus().toggleBold().run()"
              >
                {{$t('Editor.richTextEditor.toolbar.bold')}}
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('italic') }"
                @click="editor?.chain().focus().toggleItalic().run()"
              >
                {{$t('Editor.richTextEditor.toolbar.italic')}}
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('strike') }"
                @click="editor?.chain().focus().toggleStrike().run()"
              >
                {{$t('Editor.richTextEditor.toolbar.strike')}}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                v-for="level in headingLevels" 
                :key="level"
                :class="{ 'is-active': editor?.isActive('heading', { level }) }"
                @click="editor?.chain().focus().toggleHeading({ level }).run()"
              >
                {{$t('Editor.richTextEditor.toolbar.heading')}}{{ level }}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive('bulletList') }"
                @click="editor?.chain().focus().toggleBulletList().run()"
              >
                {{$t('Editor.richTextEditor.toolbar.bulletList')}}
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('orderedList') }"
                @click="editor?.chain().focus().toggleOrderedList().run()"
              >
                {{$t('Editor.richTextEditor.toolbar.orderedList')}}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'left' }) }"
                @click="editor?.chain().focus().setTextAlign('left').run()"
              >
                {{$t('Editor.richTextEditor.toolbar.alignLeft')}}
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'center' }) }"
                @click="editor?.chain().focus().setTextAlign('center').run()"
              >
                {{$t('Editor.richTextEditor.toolbar.alignCenter')}}
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'right' }) }"
                @click="editor?.chain().focus().setTextAlign('right').run()"
              >
                {{$t('Editor.richTextEditor.toolbar.alignRight')}}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button @click="editor?.chain().focus().undo().run()">
                {{$t('Editor.richTextEditor.toolbar.undo')}}
              </el-button>
              <el-button @click="editor?.chain().focus().redo().run()">
                {{$t('Editor.richTextEditor.toolbar.redo')}}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-color-picker 
              v-model="currentTextColor"
              :predefine="predefineColors"
              size="small"
              @change="onTextColorChange"
            />
          </div>
          <EditorContent :editor="editor" />
        </div>
      </el-tab-pane>

      <el-tab-pane :label="$t('Editor.richTextEditor.tab.style')" name="style">
        <el-form label-position="top" size="small">
          <el-form-item :label="$t('Editor.richTextEditor.style.textColor')">
            <el-color-picker v-model="textColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item :label="$t('Editor.richTextEditor.style.backgroundColor')">
            <el-color-picker v-model="backgroundColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item :label="$t('Editor.richTextEditor.style.padding')">
            <div class="padding-inputs">
              <div class="padding-input">
                <span>{{$t('Editor.richTextEditor.style.paddingTop')}}</span>
                <el-input-number 
                  v-model="padding.top" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>{{$t('Editor.richTextEditor.style.paddingRight')}}</span>
                <el-input-number 
                  v-model="padding.right" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>{{$t('Editor.richTextEditor.style.paddingBottom')}}</span>
                <el-input-number 
                  v-model="padding.bottom" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>{{$t('Editor.richTextEditor.style.paddingLeft')}}</span>
                <el-input-number 
                  v-model="padding.left" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 應用按鈕，只在有更改時顯示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">{{$t('Editor.richTextEditor.button.apply')}}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps, defineEmits, defineOptions, watch, nextTick } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import { ElMessage } from 'element-plus'
import type { Editor as EditorType } from '@tiptap/core'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// 定义组件名称
defineOptions({
  name: 'RichTextEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

const activeTab = ref('content')
const editor = ref<EditorType | null>(null)
const textColor = ref('#000000')
const backgroundColor = ref('#ffffff')
const isChanged = ref(false)
const padding = ref({
  top: 0,
  right: 0,
  bottom: 0,
  left: 0
})

// 原始HTML和結構信息
const originalHtml = ref('')
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  richTextClasses: [] as string[],
  attributes: {} as Record<string, string>
})

// 標題級別
const headingLevels = [1, 2, 3] as const

// 預定義顏色
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#000000',
  '#666666',
  '#999999'
]

// 當前文本顏色
const currentTextColor = ref('#000000')

// 標記為已更改
const markAsChanged = () => {
  isChanged.value = true
}

/**
 * 提取富文本内容和样式
 */
const extractRichTextContent = () => {
  if (!props.blockElement) return false
  
  try {
    // 调试日志
    console.log('开始提取富文本内容，原始元素：', props.blockElement)
    console.log('原始HTML：', props.blockElement.outerHTML)
    
    // 确保在提取新内容前状态是清空的
    originalHtml.value = props.blockElement.outerHTML
    
    // 清空原始结构
    originalStructure.value = {
      containerClasses: [],
      rowClasses: [],
      colClasses: [],
      richTextClasses: [],
      attributes: {}
    }
    
    // 解析DOM结构，提取原始类和属性
    let contentElement = null
    let extractedContent = ''
    
    // 1. 优先处理直接的富文本组件（避免重复嵌套）
    if (props.blockElement.hasAttribute('data-bs-component') && 
        props.blockElement.getAttribute('data-bs-component') === 'richTextBlock') {
      
      console.log('检测到直接富文本组件')
      
      // 保存富文本组件的类名和属性
      originalStructure.value.richTextClasses = Array.from(props.blockElement.classList)
      Array.from(props.blockElement.attributes).forEach(attr => {
        if (attr.name !== 'class' && attr.name !== 'style') {
          originalStructure.value.attributes[attr.name] = attr.value
        }
      })
      
      // 直接使用当前元素作为内容元素
      contentElement = props.blockElement
      extractedContent = contentElement.innerHTML
      
      console.log('直接提取的内容：', extractedContent)
    } 
    // 2. 处理包含富文本组件的复杂结构
    else {
      console.log('处理复杂结构')
      
      // 解析容器结构
      const container = props.blockElement.querySelector('.container, .container-fluid')
      if (container) {
        originalStructure.value.containerClasses = Array.from(container.classList)
        
        const row = container.querySelector('.row')
        if (row) {
          originalStructure.value.rowClasses = Array.from(row.classList)
          
          const col = row.querySelector('[class*="col-"]')
          if (col) {
            originalStructure.value.colClasses = Array.from(col.classList)
          }
        }
      }
      
      // 查找富文本内容 - 按优先级查找
      const richTextComponent = props.blockElement.querySelector('[data-bs-component="richTextBlock"]')
      
      if (richTextComponent) {
        console.log('找到嵌套的富文本组件：', richTextComponent)
        
        // 保存富文本组件的类名和属性
        originalStructure.value.richTextClasses = Array.from(richTextComponent.classList)
        Array.from(richTextComponent.attributes).forEach(attr => {
          if (attr.name !== 'class' && attr.name !== 'style') {
            originalStructure.value.attributes[attr.name] = attr.value
          }
        })
        
        contentElement = richTextComponent
        extractedContent = richTextComponent.innerHTML
        
        console.log('从富文本组件提取的内容：', extractedContent)
      } 
      // 如果没有找到富文本组件，查找特定的内容容器
      else {
        const richTextContent = props.blockElement.querySelector('.richTextBlock-content')
        if (richTextContent) {
          console.log('找到richTextBlock-content容器')
          contentElement = richTextContent
          extractedContent = richTextContent.innerHTML
        } 
        // 最后尝试查找列容器中的内容
        else {
          const col = props.blockElement.querySelector('[class*="col-"]')
          if (col) {
            console.log('从列容器提取内容')
            contentElement = col
            // 获取列容器的直接内容，避免嵌套的子元素
            const children = Array.from(col.children)
            if (children.length > 0) {
              // 如果有子元素，取第一个有内容的元素
              const contentChild = children.find(child => 
                child.textContent?.trim() && 
                !child.classList.contains('container') &&
                !child.classList.contains('row') &&
                !child.className.includes('col-')
              )
              extractedContent = contentChild ? contentChild.outerHTML : col.innerHTML
            } else {
              extractedContent = col.innerHTML
            }
          } else {
            console.log('使用根元素作为备选')
            // 最后的备选方案
            contentElement = props.blockElement
            extractedContent = props.blockElement.innerHTML
          }
        }
      }
    }
    
    if (contentElement && extractedContent) {
      console.log('开始清理内容，原始提取内容：', extractedContent)
      
      // 清理提取的内容，移除可能导致重复的嵌套结构
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = extractedContent
      
      // 如果内容中包含重复的富文本组件或容器结构，只保留最核心的内容
      const nestedRichText = tempDiv.querySelector('[data-bs-component="richTextBlock"]')
      if (nestedRichText && nestedRichText !== tempDiv.firstElementChild) {
        console.log('发现嵌套的富文本组件，提取其内容')
        extractedContent = nestedRichText.innerHTML
      }
      
      // 移除可能的重复容器结构
      const nestedContainer = tempDiv.querySelector('.container .row [class*="col-"] .richTextBlock-content')
      if (nestedContainer) {
        console.log('发现嵌套的容器结构，提取最内层内容')
        extractedContent = nestedContainer.innerHTML
      }
      
      // 进一步清理：检测并移除重复的嵌套结构
      let cleanedContent = extractedContent
      
      // 创建临时DOM来分析内容结构
      const analyzeDiv = document.createElement('div')
      analyzeDiv.innerHTML = cleanedContent
      
      // 移除空的容器元素
      const emptyContainers = analyzeDiv.querySelectorAll('.container:empty, .row:empty, [class*="col-"]:empty, .richTextBlock-content:empty')
      emptyContainers.forEach(el => el.remove())
      
      // 如果发现嵌套的Bootstrap结构，只保留最内层的内容
      const bootstrapNesting = analyzeDiv.querySelector('.container .row [class*="col-"]')
      if (bootstrapNesting) {
        console.log('发现Bootstrap嵌套结构，寻找最深层内容')
        // 获取最深层的内容元素
        let deepestContent = bootstrapNesting
        while (deepestContent.children.length === 1 && 
               (deepestContent.children[0].classList.contains('container') ||
                deepestContent.children[0].classList.contains('row') ||
                deepestContent.children[0].className.includes('col-') ||
                deepestContent.children[0].classList.contains('richTextBlock-content'))) {
          deepestContent = deepestContent.children[0]
        }
        
        // 如果找到了更深层的内容，使用它
        if (deepestContent !== bootstrapNesting && deepestContent.innerHTML.trim()) {
          console.log('使用最深层内容：', deepestContent.innerHTML)
          cleanedContent = deepestContent.innerHTML
        }
      }
      
      // 检测重复的文本内容模式
      const textContent = analyzeDiv.textContent || ''
      const trimmedText = textContent.trim()
      
      console.log('分析文本内容重复模式，文本长度：', trimmedText.length)
      
      // 如果文本内容看起来被重复了，尝试去重
      if (trimmedText.length > 50) {
        const words = trimmedText.split(/\s+/)
        const firstQuarter = words.slice(0, Math.floor(words.length / 4)).join(' ')
        const secondQuarter = words.slice(Math.floor(words.length / 4), Math.floor(words.length / 2)).join(' ')
        
        // 如果前两个四分之一的内容相同，说明可能有重复
        if (firstQuarter === secondQuarter && firstQuarter.length > 20) {
          console.log('检测到文本重复模式，进行去重处理')
          // 尝试查找重复模式并只保留第一份
          const children = Array.from(analyzeDiv.children)
          if (children.length > 1) {
            // 比较相邻子元素的内容
            const uniqueChildren = []
            const seenContents = new Set()
            
            for (const child of children) {
              const childText = child.textContent?.trim()
              const childHtml = child.outerHTML
              
              if (childText && !seenContents.has(childText)) {
                seenContents.add(childText)
                uniqueChildren.push(child)
              } else if (!childText && childHtml.trim()) {
                // 对于没有文本但有HTML结构的元素，也检查HTML内容
                if (!seenContents.has(childHtml)) {
                  seenContents.add(childHtml)
                  uniqueChildren.push(child)
                }
              }
            }
            
            if (uniqueChildren.length < children.length) {
              console.log(`去重处理：从${children.length}个元素减少到${uniqueChildren.length}个`)
              // 重新构建DOM，只包含去重后的内容
              analyzeDiv.innerHTML = ''
              uniqueChildren.forEach(child => analyzeDiv.appendChild(child))
              cleanedContent = analyzeDiv.innerHTML
            }
          }
        }
      }
      
      // 最终内容验证和清理
      const finalDiv = document.createElement('div')
      finalDiv.innerHTML = cleanedContent
      
      // 如果最终内容为空或只包含空白，提供默认内容
      if (!finalDiv.textContent?.trim()) {
        console.log('最终内容为空，使用默认内容')
        cleanedContent = '<p></p>'
      }
      
      console.log('最终清理后的内容：', cleanedContent)
      
      // 提取样式属性
      const computedStyle = window.getComputedStyle(contentElement)
      
      // 设置样式属性
      textColor.value = computedStyle.color || '#000000'
      backgroundColor.value = computedStyle.backgroundColor || '#ffffff'
      padding.value = {
        top: parseInt(computedStyle.paddingTop) || 0,
        right: parseInt(computedStyle.paddingRight) || 0,
        bottom: parseInt(computedStyle.paddingBottom) || 0,
        left: parseInt(computedStyle.paddingLeft) || 0
      }
      
      // 初始化编辑器 - 确保内容是干净的
      const finalContent = cleanedContent.trim() || '<p></p>'
      
      console.log('初始化编辑器，最终内容：', finalContent)
      
      const editorInstance = new Editor({
        extensions: [
          StarterKit,
          TextAlign.configure({
            types: ['heading', 'paragraph']
          }),
          TextStyle,
          Color
        ],
        content: finalContent,
        onUpdate: () => {
          markAsChanged()
        }
      })
      
      editor.value = editorInstance
      return true
    }
    
    console.log('无法提取内容，返回false')
    return false
  } catch (error) {
    console.error('提取富文本内容时出错:', error)
    return false
  }
}

// 初始化编辑器的函数
const initEditor = (content: string = '<p></p>') => {
  // 确保先清理现有编辑器
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }

  // 等待下一个 tick 再创建新编辑器
  nextTick(() => {
    editor.value = new Editor({
      extensions: [
        StarterKit,
        TextAlign.configure({
          types: ['heading', 'paragraph']
        }),
        TextStyle,
        Color
      ],
      content: content,
      onUpdate: () => {
        markAsChanged()
      }
    })
  })
}

// 文本颜色改变处理函数
const onTextColorChange = (color: string) => {
  if (editor.value) {
    editor.value.chain().focus().setColor(color).run()
    markAsChanged()
  }
}

// 清空编辑器内容
const clearEditor = () => {
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }
  // 重置所有状态
  isChanged.value = false
  currentTextColor.value = '#000000'
  originalHtml.value = ''
  originalStructure.value = {
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    richTextClasses: [],
    attributes: {}
  }
  padding.value = {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 1. 先清空当前编辑器和状态
    clearEditor()
    
    // 2. 等待 DOM 更新
    nextTick(() => {
      if (newValue) {
        // 3. 提取新内容并初始化编辑器
        const extracted = extractRichTextContent()
        if (!extracted) {
          // 如果无法提取内容，使用空内容初始化编辑器
          initEditor('<p></p>')
        }
      } else {
        // 如果 blockElement 为空，初始化空编辑器
        initEditor('<p></p>')
      }
    })
  }
}, { immediate: true, deep: true })

// 修改 onMounted 钩子
onMounted(() => {
  // 初始化时标记为未更改
  isChanged.value = false
})

// 生成富文本HTML
const generateRichTextHTML = (): string => {
  if (!editor.value) {
    return ''
  }

  // 获取编辑器内容
  const editorContent = editor.value.getHTML()
  
  // 应用样式
  const style = [
    `color: ${textColor.value}`,
    `background-color: ${backgroundColor.value}`,
    `padding: ${padding.value.top}px ${padding.value.right}px ${padding.value.bottom}px ${padding.value.left}px`
  ].join(';')

  // 检查原始元素是否为单个标签（p, h1-h6）
  const isSingleTag = props.blockElement && (
    // 检查DOM元素标签名
    /^(H[1-6]|P)$/i.test(props.blockElement.tagName) ||
    // 检查HTML字符串是否以标签开头和结尾
    /^<(h[1-6]|p)[\s>].*<\/(h[1-6]|p)>$/i.test(props.blockElement.outerHTML.trim())
  );
    
  // 检查原始HTML是否为纯文本容器
  const isDirectRichTextComponent = props.blockElement && 
    props.blockElement.hasAttribute('data-bs-component') && 
    props.blockElement.getAttribute('data-bs-component') === 'richTextBlock' &&
    !props.blockElement.querySelector('.container, .container-fluid, .row, [class*="col-"]');
  
  // 如果是单个标签或直接的富文本组件
  if (isSingleTag || isDirectRichTextComponent) {
    // 构建自定义属性字符串
    let attributesStr = ''
    Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
      if (key !== 'class' && key !== 'style') {
        attributesStr += ` ${key}="${value}"`
      }
    })
    
    // 确保有data-bs-component属性
    if (!attributesStr.includes('data-bs-component')) {
      attributesStr += ' data-bs-component="richTextBlock"'
    }
    
    // 获取原始元素的类名
    const originalClasses = props.blockElement ? Array.from(props.blockElement.classList) : []
    const classesStr = originalClasses.length > 0 ? ` class="${originalClasses.join(' ')}"` : ''
    
    // 解析编辑器内容
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = editorContent
    
    // 检查编辑器内容是否为单个标签
    if (tempDiv.children.length === 1 && /^(H[1-6]|P)$/i.test(tempDiv.children[0].tagName)) {
      const contentElement = tempDiv.children[0]
      const tagName = contentElement.tagName.toLowerCase()
      
      // 如果原始元素也是相同类型的标签，直接使用新内容更新
      if (props.blockElement && props.blockElement.tagName.toLowerCase() === tagName) {
        return `<${tagName}${classesStr}${attributesStr} style="${style}">${contentElement.innerHTML}</${tagName}>`
      }
      
      // 如果不同类型，但都是标题或段落，也直接返回内容元素
      return `<${tagName}${classesStr}${attributesStr} style="${style}">${contentElement.innerHTML}</${tagName}>`
    } else {
      // 编辑器内容不是单个标签，但原始元素是
      if (isSingleTag && props.blockElement) {
        const tagName = props.blockElement.tagName.toLowerCase()
        return `<${tagName}${classesStr}${attributesStr} style="${style}">${editorContent}</${tagName}>`
      }
      
      // 当原始元素是直接的富文本组件时
      return `<div${classesStr}${attributesStr} style="${style}">${editorContent}</div>`
    }
  }

  // 编辑器内容是单个标签，但原始容器不是
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = editorContent
  
  if (tempDiv.children.length === 1 && /^(H[1-6]|P)$/i.test(tempDiv.children[0].tagName)) {
    const contentElement = tempDiv.children[0]
    const contentHtml = contentElement.innerHTML
    const tagName = contentElement.tagName.toLowerCase()
    
    // 检查原始块是否包含带有data-bs-component="richTextBlock"的元素
    const richTextEl = props.blockElement?.querySelector('[data-bs-component="richTextBlock"]')
    
    if (richTextEl && /^(H[1-6]|P)$/i.test(richTextEl.tagName)) {
      // 如果原始富文本元素也是标题或段落，直接更新它
      const classes = Array.from(richTextEl.classList).join(' ')
      const classAttr = classes ? ` class="${classes}"` : ''
      
      let attributesStr = ''
      Array.from(richTextEl.attributes).forEach(attr => {
        if (attr.name !== 'class' && attr.name !== 'style') {
          attributesStr += ` ${attr.name}="${attr.value}"`
        }
      })
      
      // 确保有data-bs-component属性
      if (!attributesStr.includes('data-bs-component')) {
        attributesStr += ' data-bs-component="richTextBlock"'
      }
      
      return `<${tagName}${classAttr}${attributesStr} style="${style}">${contentHtml}</${tagName}>`
    }
  }

  // 检查原始HTML是否在富文本组件内
  const isInRichTextComponent = props.blockElement?.querySelector('[data-bs-component="richTextBlock"]') ||
    (props.blockElement?.getAttribute('data-bs-component') === 'richTextBlock')
    
  // 如果是完整的营销模板结构中的富文本组件
  if (isInRichTextComponent) {
    // 克隆原始HTML结构
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = originalHtml.value
    
    // 寻找富文本内容容器
    const richTextComponent = tempDiv.querySelector('[data-bs-component="richTextBlock"]') || tempDiv
    
    // 如果找到了富文本容器
    if (richTextComponent) {
      // 查找实际的内容元素
      const contentElement = richTextComponent.querySelector('h1, h2, h3, h4, h5, h6, p, div') || richTextComponent
      
      // 替换内容元素的内容
      contentElement.innerHTML = editorContent
      
      // 应用样式到内容元素
      contentElement.setAttribute('style', style)
      
      return tempDiv.innerHTML
    }
  }
  
  // 保留原始结构或使用默认结构
  const containerClasses = originalStructure.value.containerClasses.length > 0 ? 
    originalStructure.value.containerClasses.join(' ') : 
    'container py-5'
  
  const rowClasses = originalStructure.value.rowClasses.length > 0 ?
    originalStructure.value.rowClasses.join(' ') :
    'row justify-content-center'
  
  const colClasses = originalStructure.value.colClasses.length > 0 ?
    originalStructure.value.colClasses.join(' ') :
    'col-12 col-md-10 col-lg-8'
  
  const richTextClasses = originalStructure.value.richTextClasses.length > 0 ?
    originalStructure.value.richTextClasses.filter(cls => cls !== 'richTextBlock-content').join(' ') :
    ''
  
  // 构建自定义属性字符串
  let attributesStr = ''
  Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
    attributesStr += ` ${key}="${value}"`
  })
  
  // 确保有data-bs-component属性
  if (!attributesStr.includes('data-bs-component')) {
    attributesStr += ' data-bs-component="richTextBlock"'
  }

  // 默认标准结构
  return `
    <div class="${containerClasses}">
      <div class="${rowClasses}">
        <div class="${colClasses}">
          <div class="richTextBlock-content ${richTextClasses}"${attributesStr} style="${style}">
            ${editorContent}
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateRichTextHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success(t('Editor.richTextEditor.message.updateSuccess'))
  } catch (error) {
    console.error('应用富文本更改时出错:', error)
    ElMessage.error(t('Editor.richTextEditor.message.updateFail'))
  }
}

// 组件销毁时清理编辑器
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }
})
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

:deep(.el-button) {
  padding: 6px 12px;
  font-size: 13px;
  
  &.is-active {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #409eff;
  }

  &:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
}

:deep(.el-color-picker) {
  margin-left: 5px;
}

:deep(.ProseMirror) {
  padding: 20px;
  min-height: 200px;
  outline: none;
  background-color: #ffffff;

  p {
    margin: 1em 0;
    line-height: 1.6;
  }

  h1, h2, h3 {
    margin: 1.5em 0 0.5em;
    line-height: 1.4;
  }

  ul, ol {
    padding-left: 1.5em;
    margin: 1em 0;
  }

  li {
    margin: 0.5em 0;
  }
}

.padding-inputs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.padding-input {
  display: flex;
  flex-direction: column;
  gap: 4px;

  span {
    font-size: 12px;
    color: #909399;
  }
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 