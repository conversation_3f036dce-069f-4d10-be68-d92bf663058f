/**
 * A类模块模板 - 漂亮的宣传展示模块
 */

export const aClassTemplate = `
<div data-bs-component="a-class" class="a-class-container">
  <div class="container py-5">
    <div class="row align-items-center">
      <div class="col-md-6">
        <h2 class="mb-4 a-class-title fw-bold">探索更好的解决方案</h2>
        <p class="mb-4 a-class-subtitle fs-5">我们提供专业的服务，让您的业务更上一层楼。无论您需要什么，我们都能提供最佳解决方案。</p>
        <div class="mb-4 a-class-features">
          <div class="mb-3 d-flex align-items-center">
            <div class="a-class-icon-wrapper me-3">
              <i class="bi bi-check-circle-fill text-primary"></i>
            </div>
            <div class="a-class-feature-text">高效的工作流程</div>
          </div>
          <div class="mb-3 d-flex align-items-center">
            <div class="a-class-icon-wrapper me-3">
              <i class="bi bi-check-circle-fill text-primary"></i>
            </div>
            <div class="a-class-feature-text">专业的团队支持</div>
          </div>
          <div class="d-flex align-items-center">
            <div class="a-class-icon-wrapper me-3">
              <i class="bi bi-check-circle-fill text-primary"></i>
            </div>
            <div class="a-class-feature-text">全天候客户服务</div>
          </div>
        </div>
        <div class="a-class-actions">
          <button type="button" class="btn btn-primary btn-lg me-3">立即开始</button>
          <button type="button" class="btn btn-outline-secondary btn-lg">了解更多</button>
        </div>
      </div>
      <div class="col-md-6">
        <div class="text-center a-class-image-container">
          <img src="https://via.placeholder.com/600x400" class="rounded shadow img-fluid" alt="产品展示图">
        </div>
      </div>
    </div>
  </div>
</div>
`;

export default aClassTemplate; 