/*!
 * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2023
 * @version 2.2.5
 *
 * Additional enhancements for Select2 widget extension for Yii 2.0.
 *
 * Author: <PERSON><PERSON><PERSON>
 * For more JQuery plugins visit http://plugins.krajee.com
 * For more Yii related demos visit http://demos.krajee.com
 */var initS2ToggleAll=function(){},initS2Order=function(){},initS2Loading=function(){},initS2Change=function(){},initS2Unselect=function(){};!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(window.jQuery)}(function(e){"use strict";initS2ToggleAll=function(t){var n,s=e("#"+t),l="#s2-togall-"+t,o=e(l);s.attr("multiple")&&(n=function(){o.off(".krajees2").on("click.krajees2",function(){var n,l,i=o.hasClass("s2-togall-select"),a="selectall";i||(a="unselectall"),e("#select2-"+t+'-results .select2-results__option[role="option"]').each(function(){n=e(this).attr("id").match(/^select2-\S*-result-.{4}-(.*)$/),n.length&&n[1]&&(l=n[1],s.find('option:not([disabled])[value="'+l+'"]').prop("selected",!!i))}),s.select2("close").trigger("krajeeselect2:"+a).trigger("change")})},s.on("select2:open.krajees2",function(){var i="input.krajees2 keyup.krajees2";o.parent().attr("id")!=="parent-"+l&&s.attr("multiple")&&(e("#select2-"+t+"-results").closest(".select2-dropdown").prepend(o),e("#parent-"+l).remove(),e(this).parent().find(".select2-search__field").off(i).on(i,function(){setTimeout(function(){var s="#select2-"+t+'-results .select2-results__option[role="option"]',l=s+'[aria-selected="true"]',i=e(s).length;o.removeClass("s2-togall-select s2-togall-unselect"),i>0&&e(l).length===i?o.addClass("s2-togall-unselect"):o.addClass("s2-togall-select"),n()},100)}))}).on("change.krajees2",function(){if(s.attr("multiple")){var t=0,l=s.val()?s.val().length:0;o.removeClass("s2-togall-select s2-togall-unselect"),s.find("option:enabled").each(function(){e(this).val().length&&t++}),0===t||l!==t?o.addClass("s2-togall-select"):o.addClass("s2-togall-unselect"),n()}}),n())},initS2Change=function(t){t=t||e(this);var n,s,l=e(".select2-container--open"),o=t.parents("[class*='has-']");if(o.length)for(n=o[0].className.split(/\s+/),s=0;s<n.length;s++)n[s].match("has-")&&l.removeClass("has-success has-error has-warning").addClass(n[s])},initS2Unselect=function(){var t=e(this),n=t.data("select2");n&&n.options&&(n.options.set("disabled",!0),setTimeout(function(){n.options.set("disabled",!1),t.trigger("krajeeselect2:cleared")},1))},initS2Order=function(t,n){var s=e("#"+t);n&&n.length&&(e.each(n,function(e,t){s.find('option[value="'+t+'"]').appendTo(s)}),s.find("option:not(:selected)").appendTo(s))},initS2Loading=function(t,n){var s=window[n]||{},l=s.themeCss,o=s.sizeCss,i=s.doOrder,a=s.doReset,r=s.doToggle,c=e("#"+t),u=e(l),d=e(".kv-plugin-loading.loading-"+t),f=e(".group-"+t);c.off(".krajees2").on("select2:open.krajees2",function(){var e=document.querySelector('input[aria-controls="select2-'+t+'-results"]');e&&e.focus()}),u.length||c.show(),f.length&&f.removeClass("kv-input-group-hide").removeClass(".group-"+t),d.length&&d.remove(),o&&c.next(l).removeClass(o).addClass(o),a&&c.closest("form").off(".krajees2").on("reset.krajees2",function(){setTimeout(function(){c.trigger("change").trigger("krajeeselect2:reset")},100)}),r&&initS2ToggleAll(t),i&&c.on("select2:select.krajees2 select2:unselect.krajees2",function(t){var n=e(t.params.data.element);n&&n.length&&(n.detach(),c.append(n).find("option:not(:selected)").appendTo(c))}),c.on("change.krajees2",function(){setTimeout(initS2Change,500)}).on("select2:unselecting.krajees2",initS2Unselect),setTimeout(function(){c.attr("multiple")&&"rtl"===c.attr("dir")&&(c.parent().find(".select2-search__field").css({width:"100%",direction:"rtl"}),c.parent().find(".select2-search--inline").css({"float":"none"}))},100)}});
