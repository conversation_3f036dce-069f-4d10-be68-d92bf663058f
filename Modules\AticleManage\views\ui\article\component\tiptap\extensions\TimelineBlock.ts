import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { timelineTemplate } from '../templates/timeline.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    timelineBlock: {
      insertTimelineBlock: () => ReturnType
    }
  }
}

export const TimelineBlock = Node.create({
  name: 'timelineBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="timeline"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'timeline',
      'class': 'timeline-block'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertTimelineBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(timelineTemplate)
        },
    }
  },
}) 