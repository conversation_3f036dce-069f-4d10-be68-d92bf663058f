@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;

  .pane-list {
    .tab-pane {
      width: 100%;

      .news-list {
        .news-item {
          margin-bottom: 30px;
          background-color: #fff;
          .df(center);
  
          .left {
            width: 33.33%;
            flex-shrink: 0;
          }
  
          .time {
            border-right: 1px solid #eee;
            padding: 15px 15px 10px;
            .df(center, flex-start, column);
  
            .day {
              font-size: 52px;
              font-weight: bold;
              color: #333;
            }
  
            .year-month {
              color: #888;
              font-size: 16px;
              line-height: 1.5;
              white-space: nowrap;
            }
          }
  
          .news-info {
            padding: 0 30px;
            flex-grow: 1;
  
            h6 {
              a {
                margin-bottom: 20px;
                display: block;
                font-size: 24px;
                color: #333;
                transition: color .35s ease-in-out;
              }
            }
  
            .desc {
              color: #888;
              font-size: 14px;
              line-height: 1.71;
            }
  
            .tag-list {
              .df(center);
  
              .tag-item {
                margin-top: 20px;
                margin-right: 5px;
                border-radius: 28px;
                padding: 5px 14px;
                color: #888;
                font-size: 14px;
                background-color: #f7f7f7;
              }
            }
          }
  
          .btn-con {
            padding-right: 28px;
  
            .btn-box {
              .btn-square(70px, 70px, transparent, 0, 22px, #6E6E6E, 22px);
              border: 1px solid #ebebeb;
            }
          }
  
          &:hover {
            .news-info {
              padding: 0 30px;
              flex-grow: 1;
    
              h6 {
                a {
                  color: #ff9600;
                }
              }
            }
  
            .btn-con {
              .btn-box {
                border-color: #ff9600;
                background-color: #ff9600;
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
}