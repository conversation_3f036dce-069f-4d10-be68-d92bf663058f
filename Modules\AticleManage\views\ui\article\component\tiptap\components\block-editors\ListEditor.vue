<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab" class="compact-tabs">
      <!-- 内容设置选项卡 -->
      <el-tab-pane :label="$t('Editor.listEditor.contentTab')" name="content">
        <el-form label-position="top" size="small" class="compact-form">
          <div class="list-items-container">
            <div v-for="(item, index) in items" :key="index" class="list-item-row">
              <div class="list-item-content">
                <el-input
                  v-model="items[index]"
                  type="textarea"
                  :rows="2"
                  :placeholder="`${$t('Editor.listEditor.itemPlaceholder')} ${index + 1}`"
                  @input="markAsChanged"
                />
              </div>
              <div class="list-item-actions">
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click="removeItem(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="list-actions">
              <el-button
                type="primary"
                size="small"
                @click="addItem"
              >
                <el-icon><Plus /></el-icon> {{ $t('Editor.listEditor.addItem') }}
              </el-button>
            </div>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 样式设置选项卡 -->
      <el-tab-pane :label="$t('Editor.listEditor.styleTab')" name="style">
        <el-form label-position="top" size="small" class="compact-form">
          <el-form-item :label="$t('Editor.listEditor.icon')">
            <div class="icon-preview-container">
              <div 
                class="icon-preview" 
                :style="{ 
                  borderRadius: listStyle === 'circle' ? '50%' : listStyle === 'square' ? '0' : '50%',
                  backgroundColor: listColor
                }"
              ></div>
              <el-button 
                size="small" 
                class="replace-icon-btn"
                @click="showIconSelector = true"
              >
                {{ $t('Editor.listEditor.replaceIcon') }}
              </el-button>
              <el-button 
                size="small" 
                class="delete-icon-btn"
                @click="resetIcon"
              >
                {{ $t('Editor.listEditor.deleteIcon') }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item :label="$t('Editor.listEditor.markerStyle')">
            <el-radio-group v-model="listStyle" @change="markAsChanged" size="small">
              <el-radio-button v-for="(label, value) in markerStyleOptions" :key="value" :label="value">{{ $t('Editor.listEditor.markerStyleOptions.' + value) }}</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="$t('Editor.listEditor.textColor')">
            <div class="color-picker-row">
              <span class="color-label">*</span>
              <el-color-picker 
                v-model="listColor" 
                @change="markAsChanged" 
                show-alpha 
                size="small"
              />
              <span class="color-value">{{ listColor }}</span>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 排版设置选项卡 -->
      <el-tab-pane :label="$t('Editor.listEditor.typographyTab')" name="typography">
        <el-form label-position="top" size="small" class="compact-form">
          <el-form-item :label="$t('Editor.listEditor.itemSpacing')">
            <el-input-number 
              v-model="itemSpacing" 
              :min="0" 
              :max="50"
              size="small"
              @change="markAsChanged"
            />
            <span class="unit-label">px</span>
          </el-form-item>
          
          <el-form-item :label="$t('Editor.listEditor.textAlign')">
            <div class="text-align-buttons">
              <el-button
                size="small"
                :class="{ active: textAlign === 'left' }"
                @click="setTextAlign('left')"
              >
                {{ $t('Editor.listEditor.textAlignOptions.left') }}
              </el-button>
              <el-button
                size="small"
                :class="{ active: textAlign === 'center' }"
                @click="setTextAlign('center')"
              >
                {{ $t('Editor.listEditor.textAlignOptions.center') }}
              </el-button>
              <el-button
                size="small"
                :class="{ active: textAlign === 'right' }"
                @click="setTextAlign('right')"
              >
                {{ $t('Editor.listEditor.textAlignOptions.right') }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" size="small" @click="applyChanges">{{ $t('Editor.listEditor.applyChanges') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'
import { Delete, Plus, Location, Basketball, Aim } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const markerStyleOptions = {
  circle: t('Editor.listEditor.markerStyleOptions.circle'),
  disc: t('Editor.listEditor.markerStyleOptions.disc'),
  square: t('Editor.listEditor.markerStyleOptions.square'),
  decimal: t('Editor.listEditor.markerStyleOptions.decimal')
}

// 定义组件名称
defineOptions({
  name: 'ListEditor'
})

const props = defineProps({
  editor: {
    type: Object,
    default: null
  },
  node: {
    type: Object,
    default: null
  },
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的选项卡
const activeTab = ref('content')

// 原始HTML和结构信息
const originalHtml = ref('')
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  listClasses: [] as string[],
  attributes: {} as Record<string, string>
})

// 列表样式相关状态
const listStyle = ref('circle')
const listColor = ref('#6c5ce7')
const items = ref<string[]>([])
const itemSpacing = ref(16)
const textAlign = ref('left')
const showIconSelector = ref(false)

// 是否有未保存的更改
const isChanged = ref(false)

/**
 * 从HTML块元素中提取列表数据
 */
const extractListData = () => {
  if (!props.blockElement) {
    // 如果没有blockElement，尝试从node属性中提取
    if (props.node && props.node.attrs) {
      const attrs = props.node.attrs
      
      if (attrs.listStyle) {
        listStyle.value = attrs.listStyle
      }
      
      if (attrs.listColor) {
        listColor.value = attrs.listColor
      }
      
      if (attrs.items && Array.isArray(attrs.items)) {
        items.value = [...attrs.items]
      } else {
        // 默认列表项
        items.value = ['这是第一个列表项', '这是第二个列表项', '这是第三个列表项']
      }
      
      if (attrs.itemSpacing) {
        itemSpacing.value = attrs.itemSpacing
      }
      
      if (attrs.textAlign) {
        textAlign.value = attrs.textAlign
      }
    }
    
    return false
  }
  
  try {
    // 保存原始HTML以备参考
    originalHtml.value = props.blockElement.outerHTML
    
    // 解析DOM结构，提取原始类和属性
    // 1. 找到主容器
    const container = props.blockElement.closest('[data-bs-component="list"]') || props.blockElement
    
    if (container) {
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      // 保存自定义属性
      Array.from(container.attributes).forEach(attr => {
        if (attr.name !== 'class' && attr.name !== 'style') {
          originalStructure.value.attributes[attr.name] = attr.value
        }
      })
      
      // 2. 找到行元素
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        // 3. 找到列元素
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
          
          // 提取文本对齐方式
          if (col.classList.contains('text-center')) {
            textAlign.value = 'center'
          } else if (col.classList.contains('text-right')) {
            textAlign.value = 'right'
          } else {
            textAlign.value = 'left'
          }
        }
      }
    }
    
    // 4. 找到列表元素
    const listElement = props.blockElement.querySelector('.custom-list') || 
                        props.blockElement.querySelector('[data-list-style]') ||
                        props.blockElement.querySelector('ul, ol')
    
    if (listElement) {
      originalStructure.value.listClasses = Array.from(listElement.classList)
      
      // 提取列表样式
      const dataListStyle = listElement.getAttribute('data-list-style')
      if (dataListStyle) {
        listStyle.value = dataListStyle
      } else if (listElement.classList.contains('list-style-circle')) {
        listStyle.value = 'circle'
      } else if (listElement.classList.contains('list-style-disc')) {
        listStyle.value = 'disc'
      } else if (listElement.classList.contains('list-style-square')) {
        listStyle.value = 'square'
      } else if (listElement.classList.contains('list-style-decimal') || listElement.tagName === 'OL') {
        listStyle.value = 'decimal'
      }
      
      // 提取列表颜色
      const dataListColor = listElement.getAttribute('data-list-color')
      if (dataListColor) {
        listColor.value = dataListColor
      }
      
      // 提取列表间距
      const style = listElement.getAttribute('style') || ''
      const spacingMatch = style.match(/--item-spacing:\s*(\d+)px/)
      if (spacingMatch && spacingMatch[1]) {
        itemSpacing.value = parseInt(spacingMatch[1], 10)
      }
    }
    
    // 5. 提取列表项内容
    const listItems = props.blockElement.querySelectorAll('.list-item, li')
    if (listItems.length > 0) {
      const extractedItems: string[] = []
      
      listItems.forEach(item => {
        const textElement = item.querySelector('.list-item-text') || item
        const text = textElement.textContent?.trim() || ''
        extractedItems.push(text)
      })
      
      if (extractedItems.length > 0) {
        items.value = extractedItems
      } else {
        // 默认列表项
        items.value = ['这是第一个列表项', '这是第二个列表项', '这是第三个列表项']
      }
    } else {
      // 默认列表项
      items.value = ['这是第一个列表项', '这是第二个列表项', '这是第三个列表项']
    }
    
    // 初始化时标记为未更改
    isChanged.value = false
    return true
  } catch (error) {
    console.error('提取列表数据时出错:', error)
    
    // 设置默认值
    listStyle.value = 'circle'
    listColor.value = '#6c5ce7'
    items.value = ['这是第一个列表项', '这是第二个列表项', '这是第三个列表项']
    itemSpacing.value = 16
    textAlign.value = 'left'
    
    isChanged.value = false
    return false
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加新列表项
const addItem = () => {
  items.value.push('新的列表项')
  markAsChanged()
}

// 删除列表项
const removeItem = (index: number) => {
  items.value.splice(index, 1)
  markAsChanged()
}

// 设置文本对齐
const setTextAlign = (align: string) => {
  textAlign.value = align
  markAsChanged()
}

// 重置图标
const resetIcon = () => {
  // 如果需要重置为默认图标，可以在这里实现
  markAsChanged()
}

/**
 * 准备列表HTML
 */
const prepareListHTML = (): string => {
  // 生成列表项HTML
  const listItemsHTML = items.value.map(item => `
    <div class="list-item">
      <div class="list-marker" style="background-color: ${listColor.value};"></div>
      <div class="list-item-text">${item}</div>
    </div>
  `).join('')

  // 保留原始容器类或使用默认类
  const containerClasses = originalStructure.value.containerClasses
    .filter(c => !c.includes('list') && c !== 'bootstrap-list')
    .join(' ') || 'container py-5'
    
  // 保留原始行类或使用默认类
  const rowClasses = originalStructure.value.rowClasses.join(' ') || 'row justify-content-center'
  
  // 保留原始列类或使用默认类
  const colClasses = originalStructure.value.colClasses
    .filter(c => !c.includes('text-'))
    .join(' ') || 'col-12 col-md-10 col-lg-8'
    
  // 保留原始列表类或使用默认类
  const listClasses = originalStructure.value.listClasses
    .filter(c => !c.includes('list-style-'))
    .join(' ') || 'custom-list'
    
  // 构建自定义属性字符串
  let attributesStr = ''
  Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
    if (key !== 'class' && key !== 'style' && key !== 'data-bs-component') {
      attributesStr += ` ${key}="${value}"`
    }
  })

  // 构建HTML
  const html = `
    <div data-bs-component="list" class="bootstrap-list ${containerClasses}"${attributesStr}>
      <div class="p-0 container-fluid">
        <div class="${rowClasses}">
          <div class="${colClasses} text-${textAlign.value}">
            <div class="${listClasses} list-style-${listStyle.value}" data-list-style="${listStyle.value}" data-list-color="${listColor.value}" style="--item-spacing: ${itemSpacing.value}px;">
              <div class="list-container">
                ${listItemsHTML}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style>
      .bootstrap-list .list-container {
        gap: var(--item-spacing, 16px);
      }
    </style>
  `.trim()

  return html
}

// 组件挂载时，从现有块元素中提取列表数据
onMounted(() => {
  extractListData()
})

// 应用更改
const applyChanges = () => {
  try {
    // 准备HTML
    const html = prepareListHTML()
    
    // 如果使用编辑器模式
    if (props.editor && props.node) {
      // 更新属性
      props.editor.chain().focus().updateAttributes('listBlock', {
        listStyle: listStyle.value,
        listColor: listColor.value,
        items: items.value,
        itemSpacing: itemSpacing.value,
        textAlign: textAlign.value
      }).run()
      
      // 更新节点
      if (props.node.pos !== undefined) {
        const transaction = props.editor.state.tr.setNodeMarkup(
          props.node.pos, 
          undefined, 
          {
            ...props.node.attrs,
            listStyle: listStyle.value,
            listColor: listColor.value,
            items: items.value,
            itemSpacing: itemSpacing.value,
            textAlign: textAlign.value
          }
        )
        props.editor.view.dispatch(transaction)
      }
    } else {
      // 使用blockElement模式，发出更新事件
      emit('update-block', { html })
    }
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用列表更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 10px;
  position: relative;
}

.list-items-container {
  .list-item-row {
    display: flex;
    margin-bottom: 8px;
    
    .list-item-content {
      flex: 1;
      margin-right: 8px;
    }
    
    .list-item-actions {
      display: flex;
      align-items: center;
    }
  }
  
  .list-actions {
    margin-top: 12px;
  }
}

.icon-preview-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  
  .icon-preview {
    width: 24px;
    height: 24px;
    background-color: #6c5ce7;
    border-radius: 50%;
  }
  
  .replace-icon-btn, .delete-icon-btn {
    font-size: 12px;
    padding: 5px 10px;
  }
}

.text-align-buttons {
  display: flex;
  gap: 5px;

  .el-button {
    flex: 1;
    padding: 6px 10px;
    height: auto;

    &.active {
      background-color: #409eff;
      color: white;
    }
  }
}

.color-picker-row {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .color-label {
    font-size: 14px;
    color: #606266;
  }
  
  .color-value {
    font-size: 12px;
    color: #909399;
  }
}

.unit-label {
  margin-left: 5px;
  color: #909399;
  font-size: 12px;
}

.apply-button-container {
  margin-top: 10px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}

/* 紧凑样式 */
.compact-tabs {
  :deep(.el-tabs__item) {
    padding: 0 16px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
  }
  
  :deep(.el-tabs__content) {
    padding-top: 10px;
  }
}

.compact-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    padding-bottom: 4px;
    line-height: 1.2;
    font-size: 13px;
  }
  
  :deep(.el-input-number) {
    width: 100px;
  }

  :deep(.el-radio-button) {
    --el-radio-button-checked-text-color: #fff;
    --el-radio-button-checked-bg-color: #409eff;
    --el-radio-button-checked-border-color: #409eff;

    .el-radio-button__inner {
      padding: 5px 12px;
      height: 28px;
      line-height: 1;
    }
  }
}
</style> 