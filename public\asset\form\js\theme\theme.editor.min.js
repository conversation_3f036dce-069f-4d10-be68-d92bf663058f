var css=$(options.css);var iID=options.iframe;var iH="250";var iExists=false;var previewUnselected=function(e){e.preventDefault();$("#preview-container").hide();$("#"+iID).remove();return iExists=false};var previewSelected=function(e){e.preventDefault();$("#preview-container").show();var src=$(e.currentTarget).val();if(iExists===true){$("#"+iID).attr("src",src)}else{var i=$("<iframe></iframe>").attr({src:src,id:iID,frameborder:0,width:"100%",height:iH});i.on("load",function(){$("#"+iID).contents().find("#liveCSS").text(editor.getSession().getValue())});$("#preview").html(i);return iExists=true}};$("#resizeFull").click(function(e){e.preventDefault();if(iExists){$("#"+iID).height(document.getElementById(iID).contentWindow.document.body.scrollHeight);$(".toggleButton").toggle()}});$("#resizeSmall").click(function(e){e.preventDefault();if(iExists){$("#"+iID).height(iH);$(".toggleButton").toggle()}});$(document).ready(function(){editor=ace.edit("editor");editor.$blockScrolling=Infinity;editor.setTheme("ace/theme/clouds");editor.getSession().setMode("ace/mode/css");editor.getSession().setValue(css.val());editor.getSession().on("change",function(){var styles=editor.getSession().getValue();css.val(styles);if(iExists){$("#"+iID).contents().find("#liveCSS").text(styles)}})});
