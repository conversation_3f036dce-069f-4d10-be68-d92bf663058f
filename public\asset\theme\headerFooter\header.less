@import "../variable.less";

header {
  position: sticky;
  top: 0;
  background-color: #fff;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03);
  z-index: 99;
  width: 100%;

  .header-con {
    .df(center, space-between);

    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
    max-width: 1500px;

    .logo {
      width: 125px;
    }

    nav {
      flex-grow: 1;

      & > ul {
        .df(center, center);

        & > li {
          position: relative;

          & > a {
            padding-left: 16px;
            padding-right: 16px;
            color: #3e3e3e;
            font-size: 16px;
            line-height: 5.625;
            display: block;
            transition: color .35s ease-in-out;

            .df(center);

            .iconfont {
              margin-left: 4px;
              font-size: 12px;
              display: block;
              transform: rotate(0);
              transition: transform .35s ease-in-out;
            }
          }

          &.active,
          &:hover {
            a {
              color: #ff9600;
            }
          }

          &:hover {
            .sub-menu {
              display: flex;
            }

            a {
              .iconfont {
                transform: rotate(90deg)
              }
            }
          }

          .sub-menu {
            position: absolute;
            left: 0;
            top: 100%;
            z-index: 999;
            .df(center, center, column);
            display: none;

            ul {
              background-color: #fff;
              min-width: 160px;
              box-shadow: 0 5px 15px -8px rgba(0,0,0,.175);

              li {
                a {
                  padding: 14px 16px;
                  color: #666;
                  font-size: 14px;
                  white-space: nowrap;
                  display: block;
                  transition: all .35s ease-in-out;

                  &:hover {
                    background-color: #ff9600;
                    color: #fff;
                  }
                }
              }
            }
          }
        }
      }
    }

    .more {
      .df();
      text-align: center;

      .site-select {
        color: #6E6E6E;
        font-size: 16px;
        position: relative;
        line-height: 32px;
        width: 60px;

        .site-list {
          padding-top: 18px;
          position: absolute;
          left: 50%;
          top: 100%;
          transform: translateX(-50%);
          z-index: 999;
          .df(center, center, column);
          display: none;

          .single {
            border: 6px solid transparent;
            border-bottom-color: #eee;
          }

          ul {
            background-color: #fff;

            li {
              a {
                padding: 10px 12px;
                text-align: center;
                color: #666;
                font-size: 14px;
                white-space: nowrap;
                display: block;
                transition: background-color .35s ease-in-out;

                &:hover {
                  background-color: #f5f5f5;
                }
              }
            }
          }
        }

        &:hover {
          .site-list {
            display: flex;
          }
        }
      }

      .lang-select {
        color: #6E6E6E;
        font-size: 16px;
        position: relative;
        line-height: 32px;
        width: 32px;

        .lang-list {
          padding-top: 18px;
          position: absolute;
          left: 50%;
          top: 100%;
          transform: translateX(-50%);
          z-index: 999;
          .df(center, center, column);
          display: none;

          .single {
            border: 6px solid transparent;
            border-bottom-color: #eee;
          }

          ul {
            background-color: #fff;

            li {
              a {
                padding: 10px 12px;
                text-align: center;
                color: #666;
                font-size: 14px;
                white-space: nowrap;
                display: block;
                transition: background-color .35s ease-in-out;

                &:hover {
                  background-color: #f5f5f5;
                }
              }
            }
          }
        }

        &:hover {
          .lang-list {
            display: flex;
          }
        }
      }

      .iconfont {
        font-size: 20px;
        width: 32px;
        height: 32px;
        color: #6E6E6E;
        .df(center, center);
        cursor: pointer;

        &.icon-user {
          margin-left: 14px;
          border-radius: 50%;
          background-color: #ECEFF5;
        }
      }

      .user-avatar {
        margin-left: 14px;
        position: relative;

        .pic {
          border-radius: 50%;
          overflow: hidden;
          width: 32px;
          height: 32px;
          display: block;
        }

        .drop-box {
          padding-top: 18px;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          z-index: 999;
          .df(center, center, column);
          display: none;

          .single {
            border: 6px solid transparent;
            border-bottom-color: #eee;
          }
        
          .drop-list {
            padding: 12px 20px;
            background-color: #fff;
            box-shadow: 9px 13px 12px rgba(0, 0, 0, 0.2);

            ul {
              li {
                color: #666;
                font-size: 14px;
                line-height: 2.28;
                transition: color .35s ease-in-out;

                a {
                  white-space: nowrap;
                  color: #666;
                  font-size: 14px;
                  line-height: 2.28;
                  transition: color .35s ease-in-out;
                }

                &:hover {
                  color: #ff9600;

                  a {
                    color: #ff9600;
                  }
                }
              }
            }
          }
        }

        &:hover {
          .drop-box {
            display: flex;
          }
        }
      }
    }
  }
}