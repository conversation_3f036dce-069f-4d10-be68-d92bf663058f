/*
 * @Author: jay <EMAIL>
 * @Date: 2025-04-06 19:02:19
 * @LastEditors: jay <EMAIL>
 * @LastEditTime: 2025-04-14 18:18:07
 * @FilePath: /bwms/public/ChatBot/config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 更细致的环境判断
const getEnvironment = () => {
  const hostname = window.location.hostname

  if (hostname === 'dev-bwms.bingo-test.com') {
    return 'production'
  } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'development'
  }

  return 'development' // 默认环境
}

const getApiUrl = () => {
  const env = getEnvironment()

  const urls = {
    production: 'https://dev-bwms.bingo-test.com/api',
    development: 'http://127.0.0.1:8000/api',
  }

  return urls[env]
}

window.appConfig = {
  APP_NAME: 'Bwms后台管理系统',
  APP_ENV: 'prod',
  APP_KEY: 'base64:F0hxhhkfXpaqI5stbvNxMwdKAta8j+hwZL3mFEmyep8=',
  APP_DEBUG: true,
  APP_URL: 'https://dev-bwms.bingo-test.com',

  VITE_BASE_URL: 'https://dev-bwms.bingo-test.com/admin/',
  VITE_APP_NAME: 'Bwms后台管理系统',

  VITE_API_BASE_URL: getApiUrl(),
  VITE_FRONTEND_API_BASE_URL: getApiUrl(),

  FRONTEND_APP_NAME: 'BwmsClient',
}

// 为了兼容之前的代码，添加 process.env
window.process = {
  env: {
    ...window.appConfig,
    NODE_ENV: getEnvironment(),
  },
}
