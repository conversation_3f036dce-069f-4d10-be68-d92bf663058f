<?php

namespace Modules\Common\Util;

/**
 * Class SmsUtil
 * @package Modules\Common\Sms
 */
class SmsUtil
{
    public static function calcNumber($content): float
    {
        return ceil(mb_strlen($content) / 67);
    }

    public static function parseContent($template, $values = []): array|string
    {
        $param1 = [];
        $param2 = [];
        foreach ($values as $k => $v) {
            $param1[] = '{'.$k.'}';
            $param2[] = $v;
        }
        return str_replace($param1, $param2, $template);
    }

    public static function parseTemplateParam($template): array
    {
        preg_match_all('/\\{(.*?)\\}/', $template, $mat);
        return $mat[1];
    }

    public static function replaceTemplate($template, $callbackOrBorder = '#')
    {
        $param = self::parseTemplateParam($template);
        foreach ($param as $v) {
            if (is_string($callbackOrBorder)) {
                $template = str_replace('{'.$v.'}', $callbackOrBorder.$v.$callbackOrBorder, $template);
            } else {
                $template = str_replace('{'.$v.'}', call_user_func($callbackOrBorder, $v), $template);
            }
        }
        return $template;
    }

    /**
     * @return string[][]
     */
    public static function templates(): array
    {
        return [
            [
                'name' => 'verify',
                'title' => '验证码',
                'desc' => '验证码模板变量为 code'
            ]
        ];
    }

}
