<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 数据权限模型
 *
 * @property int $data_permission_id 数据权限ID
 * @property int $role_id 角色ID
 * @property string $resource_type 资源类型
 * @property int $permission_type 权限类型: 1-全部, 2-部门, 3-个人, 4-自定义
 * @property array|null $scope_value 权限范围值
 * @property int $created_by 创建人ID
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
class DataPermission extends Model
{
    protected $table = 'data_permissions';
    protected $primaryKey = 'data_permission_id';

    protected $fillable = [
        'role_id',
        'resource_type',
        'permission_type',
        'scope_value',
        'created_by'
    ];

    protected $casts = [
        'role_id' => 'integer',
        'permission_type' => 'integer',
        'scope_value' => 'array',
        'created_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取关联的角色
     *
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'role_id');
    }

    /**
     * 获取创建者
     *
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by', 'admin_id');
    }

    /**
     * 获取权限类型文本
     *
     * @return string
     */
    public function getPermissionTypeTextAttribute(): string
    {
        $types = [
            1 => '全部权限',
            2 => '部门权限',
            3 => '个人权限',
            4 => '自定义权限'
        ];

        return $types[$this->permission_type] ?? '未知';
    }

    /**
     * 检查是否为全部权限
     *
     * @return bool
     */
    public function isAllPermission(): bool
    {
        return $this->permission_type === 1;
    }

    /**
     * 检查是否为部门权限
     *
     * @return bool
     */
    public function isDepartmentPermission(): bool
    {
        return $this->permission_type === 2;
    }

    /**
     * 检查是否为个人权限
     *
     * @return bool
     */
    public function isPersonalPermission(): bool
    {
        return $this->permission_type === 3;
    }

    /**
     * 检查是否为自定义权限
     *
     * @return bool
     */
    public function isCustomPermission(): bool
    {
        return $this->permission_type === 4;
    }

    /**
     * 获取权限范围值
     *
     * @param string $key 键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getScopeValue(string $key, $default = null)
    {
        return $this->scope_value[$key] ?? $default;
    }

    /**
     * 设置权限范围值
     *
     * @param string $key 键名
     * @param mixed $value 值
     * @return void
     */
    public function setScopeValue(string $key, $value): void
    {
        $scopeValue = $this->scope_value ?? [];
        $scopeValue[$key] = $value;
        $this->scope_value = $scopeValue;
    }

    /**
     * 作用域：按角色筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $roleId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRole($query, int $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    /**
     * 作用域：按资源类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $resourceType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByResourceType($query, string $resourceType)
    {
        return $query->where('resource_type', $resourceType);
    }

    /**
     * 作用域：按权限类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $permissionType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPermissionType($query, int $permissionType)
    {
        return $query->where('permission_type', $permissionType);
    }

    /**
     * 作用域：全部权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAllPermission($query)
    {
        return $query->where('permission_type', 1);
    }

    /**
     * 作用域：部门权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDepartmentPermission($query)
    {
        return $query->where('permission_type', 2);
    }

    /**
     * 作用域：个人权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePersonalPermission($query)
    {
        return $query->where('permission_type', 3);
    }

    /**
     * 作用域：自定义权限
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCustomPermission($query)
    {
        return $query->where('permission_type', 4);
    }
}
