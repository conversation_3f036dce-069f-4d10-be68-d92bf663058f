<?php

namespace Modules\Users\Api\Controller;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;


/**
 * 认证控制器
 * 处理用户登录、注册、登出等认证相关功能
 */
class AuthController extends Controller
{



    /**
     * 用户登录
     *
     * @param Request $request 请求对象
     * @return JsonResponse 登录结果
     */
    public function login(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '登录成功',
            'data' => null
        ], 200);
    }

    /**
     * 用户注册
     *
     * @param Request $request 请求对象
     * @return JsonResponse 注册结果
     */
    public function register(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '注册成功',
            'data' => null
        ], 200);
    }

    /**
     * 用户登出
     *
     * @param Request $request 请求对象
     * @return JsonResponse 登出结果
     */
    public function logout(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '登出成功',
            'data' => null
        ], 200);
    }

    /**
     * 获取当前用户信息
     *
     * @param Request $request 请求对象
     * @return JsonResponse 用户信息
     */
    public function me(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '获取用户信息成功',
            'data' => null
        ], 200);
    }

    /**
     * 刷新访问令牌
     *
     * @param Request $request 请求对象
     * @return JsonResponse 刷新结果
     */
    public function refresh(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '刷新访问令牌成功',
            'data' => null
        ], 200);
    }
}


