{"version": 3, "file": "tom-select.base.js", "sources": ["../../src/contrib/microevent.ts", "../../src/contrib/microplugin.ts", "../../node_modules/@orchidjs/unicode-variants/dist/esm/regex.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/strings.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/index.js", "../../node_modules/@orchidjs/sifter/dist/esm/utils.js", "../../node_modules/@orchidjs/sifter/dist/esm/sifter.js", "../../node_modules/@orchidjs/sifter/lib/utils.ts", "../../src/vanilla.ts", "../../src/contrib/highlight.ts", "../../src/constants.ts", "../../src/defaults.ts", "../../src/utils.ts", "../../src/getSettings.ts", "../../src/tom-select.ts"], "sourcesContent": ["/**\n * MicroEvent - to make any js object an event emitter\n *\n * - pure javascript - server compatible, browser compatible\n * - dont rely on the browser doms\n * - super simple - you get it immediatly, no mistery, no magic involved\n *\n * <AUTHOR> (https://github.com/jero<PERSON>)\n */\n\ntype TCallback = (...args:any) => any;\n\n/**\n * Execute callback for each event in space separated list of event names\n *\n */\nfunction forEvents(events:string,callback:(event:string)=>any){\n\tevents.split(/\\s+/).forEach((event) =>{\n\t\tcallback(event);\n\t});\n}\n\nexport default class MicroEvent{\n\n\tpublic _events: {[key:string]:TCallback[]};\n\n\tconstructor(){\n\t\tthis._events = {};\n\t}\n\n\ton(events:string, fct:TCallback){\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = this._events[event] || [];\n\t\t\tevent_array.push(fct);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\toff(events:string, fct:TCallback){\n\t\tvar n = arguments.length;\n\t\tif( n === 0 ){\n\t\t\tthis._events = {};\n\t\t\treturn;\n\t\t}\n\n\t\tforEvents(events,(event) => {\n\n\t\t\tif (n === 1){\n\t\t\t\tdelete this._events[event];\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tconst event_array = this._events[event];\n\t\t\tif( event_array === undefined ) return;\n\n\t\t\tevent_array.splice(event_array.indexOf(fct), 1);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\ttrigger(events:string, ...args:any){\n\t\tvar self = this;\n\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = self._events[event];\n\t\t\tif( event_array === undefined ) return;\n\t\t\tevent_array.forEach(fct => {\n\t\t\t\tfct.apply(self, args );\n\t\t\t});\n\n\t\t});\n\t}\n};\n", "/**\n * microplugin.js\n * Copyright (c) 2013 <PERSON> & <PERSON>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\ntype TSettings = {\n\t[key:string]:any\n}\n\ntype TPlugins = {\n\tnames: string[],\n\tsettings: TSettings,\n\trequested: {[key:string]:boolean},\n\tloaded: {[key:string]:any}\n};\n\nexport type TPluginItem = {name:string,options:{}};\nexport type TPluginHash = {[key:string]:{}};\n\n\n\n\nexport default function MicroPlugin(Interface: any ){\n\n\tInterface.plugins = {};\n\n\treturn class extends Interface{\n\n\t\tpublic plugins:TPlugins = {\n\t\t\tnames     : [],\n\t\t\tsettings  : {},\n\t\t\trequested : {},\n\t\t\tloaded    : {}\n\t\t};\n\n\t\t/**\n\t\t * Registers a plugin.\n\t\t *\n\t\t * @param {function} fn\n\t\t */\n\t\tstatic define(name:string, fn:(this:any,settings:TSettings)=>any){\n\t\t\tInterface.plugins[name] = {\n\t\t\t\t'name' : name,\n\t\t\t\t'fn'   : fn\n\t\t\t};\n\t\t}\n\n\n\t\t/**\n\t\t * Initializes the listed plugins (with options).\n\t\t * Acceptable formats:\n\t\t *\n\t\t * List (without options):\n\t\t *   ['a', 'b', 'c']\n\t\t *\n\t\t * List (with options):\n\t\t *   [{'name': 'a', options: {}}, {'name': 'b', options: {}}]\n\t\t *\n\t\t * Hash (with options):\n\t\t *   {'a': { ... }, 'b': { ... }, 'c': { ... }}\n\t\t *\n\t\t * @param {array|object} plugins\n\t\t */\n\t\tinitializePlugins(plugins:string[]|TPluginItem[]|TPluginHash) {\n\t\t\tvar key, name;\n\t\t\tconst self  = this;\n\t\t\tconst queue:string[] = [];\n\n\t\t\tif (Array.isArray(plugins)) {\n\t\t\t\tplugins.forEach((plugin:string|TPluginItem)=>{\n\t\t\t\t\tif (typeof plugin === 'string') {\n\t\t\t\t\t\tqueue.push(plugin);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.plugins.settings[plugin.name] = plugin.options;\n\t\t\t\t\t\tqueue.push(plugin.name);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else if (plugins) {\n\t\t\t\tfor (key in plugins) {\n\t\t\t\t\tif (plugins.hasOwnProperty(key)) {\n\t\t\t\t\t\tself.plugins.settings[key] = plugins[key];\n\t\t\t\t\t\tqueue.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twhile( name = queue.shift() ){\n\t\t\t\tself.require(name);\n\t\t\t}\n\t\t}\n\n\t\tloadPlugin(name:string) {\n\t\t\tvar self    = this;\n\t\t\tvar plugins = self.plugins;\n\t\t\tvar plugin  = Interface.plugins[name];\n\n\t\t\tif (!Interface.plugins.hasOwnProperty(name)) {\n\t\t\t\tthrow new Error('Unable to find \"' +  name + '\" plugin');\n\t\t\t}\n\n\t\t\tplugins.requested[name] = true;\n\t\t\tplugins.loaded[name] = plugin.fn.apply(self, [self.plugins.settings[name] || {}]);\n\t\t\tplugins.names.push(name);\n\t\t}\n\n\t\t/**\n\t\t * Initializes a plugin.\n\t\t *\n\t\t */\n\t\trequire(name:string) {\n\t\t\tvar self = this;\n\t\t\tvar plugins = self.plugins;\n\n\t\t\tif (!self.plugins.loaded.hasOwnProperty(name)) {\n\t\t\t\tif (plugins.requested[name]) {\n\t\t\t\t\tthrow new Error('Plugin has circular dependency (\"' + name + '\")');\n\t\t\t\t}\n\t\t\t\tself.loadPlugin(name);\n\t\t\t}\n\n\t\t\treturn plugins.loaded[name];\n\t\t}\n\n\t};\n\n}\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n * @param {string[]} chars\n * @return {string}\n */\nconst arrayToPattern = chars => {\n  chars = chars.filter(Boolean);\n\n  if (chars.length < 2) {\n    return chars[0] || '';\n  }\n\n  return maxValueLength(chars) == 1 ? '[' + chars.join('') + ']' : '(?:' + chars.join('|') + ')';\n};\n/**\n * @param {string[]} array\n * @return {string}\n */\n\nconst sequencePattern = array => {\n  if (!hasDuplicates(array)) {\n    return array.join('');\n  }\n\n  let pattern = '';\n  let prev_char_count = 0;\n\n  const prev_pattern = () => {\n    if (prev_char_count > 1) {\n      pattern += '{' + prev_char_count + '}';\n    }\n  };\n\n  array.forEach((char, i) => {\n    if (char === array[i - 1]) {\n      prev_char_count++;\n      return;\n    }\n\n    prev_pattern();\n    pattern += char;\n    prev_char_count = 1;\n  });\n  prev_pattern();\n  return pattern;\n};\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n * @param {Set<string>} chars\n * @return {string}\n */\n\nconst setToPattern = chars => {\n  let array = toArray(chars);\n  return arrayToPattern(array);\n};\n/**\n *\n * https://stackoverflow.com/questions/7376598/in-javascript-how-do-i-check-if-an-array-has-duplicate-values\n * @param {any[]} array\n */\n\nconst hasDuplicates = array => {\n  return new Set(array).size !== array.length;\n};\n/**\n * https://stackoverflow.com/questions/63006601/why-does-u-throw-an-invalid-escape-error\n * @param {string} str\n * @return {string}\n */\n\nconst escape_regex = str => {\n  return (str + '').replace(/([\\$\\(\\)\\*\\+\\.\\?\\[\\]\\^\\{\\|\\}\\\\])/gu, '\\\\$1');\n};\n/**\n * Return the max length of array values\n * @param {string[]} array\n *\n */\n\nconst maxValueLength = array => {\n  return array.reduce((longest, value) => Math.max(longest, unicodeLength(value)), 0);\n};\n/**\n * @param {string} str\n */\n\nconst unicodeLength = str => {\n  return toArray(str).length;\n};\n/**\n * @param {any} p\n * @return {any[]}\n */\n\nconst toArray = p => Array.from(p);\n\nexport { arrayToPattern, escape_regex, hasDuplicates, maxValueLength, sequencePattern, setToPattern, toArray, unicodeLength };\n//# sourceMappingURL=regex.js.map\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\n/**\n * Get all possible combinations of substrings that add up to the given string\n * https://stackoverflow.com/questions/30169587/find-all-the-combination-of-substrings-that-add-up-to-the-given-string\n * @param {string} input\n * @return {string[][]}\n */\nconst allSubstrings = input => {\n  if (input.length === 1) return [[input]];\n  /** @type {string[][]} */\n\n  let result = [];\n  const start = input.substring(1);\n  const suba = allSubstrings(start);\n  suba.forEach(function (subresult) {\n    let tmp = subresult.slice(0);\n    tmp[0] = input.charAt(0) + tmp[0];\n    result.push(tmp);\n    tmp = subresult.slice(0);\n    tmp.unshift(input.charAt(0));\n    result.push(tmp);\n  });\n  return result;\n};\n\nexport { allSubstrings };\n//# sourceMappingURL=strings.js.map\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\nimport { toArray, setToPattern, escape_regex, arrayToPattern, sequencePattern } from './regex.js';\nexport { escape_regex } from './regex.js';\nimport { allSubstrings } from './strings.js';\n\n/**\n * @typedef {{[key:string]:string}} TUnicodeMap\n * @typedef {{[key:string]:Set<string>}} TUnicodeSets\n * @typedef {[[number,number]]} TCodePoints\n * @typedef {{folded:string,composed:string,code_point:number}} TCodePointObj\n * @typedef {{start:number,end:number,length:number,substr:string}} TSequencePart\n */\n/** @type {TCodePoints} */\n\nconst code_points = [[0, 65535]];\nconst accent_pat = '[\\u0300-\\u036F\\u{b7}\\u{2be}\\u{2bc}]';\n/** @type {TUnicodeMap} */\n\nlet unicode_map;\n/** @type {RegExp} */\n\nlet multi_char_reg;\nconst max_char_length = 3;\n/** @type {TUnicodeMap} */\n\nconst latin_convert = {};\n/** @type {TUnicodeMap} */\n\nconst latin_condensed = {\n  '/': '⁄∕',\n  '0': '߀',\n  \"a\": \"ⱥɐɑ\",\n  \"aa\": \"ꜳ\",\n  \"ae\": \"æǽǣ\",\n  \"ao\": \"ꜵ\",\n  \"au\": \"ꜷ\",\n  \"av\": \"ꜹꜻ\",\n  \"ay\": \"ꜽ\",\n  \"b\": \"ƀɓƃ\",\n  \"c\": \"ꜿƈȼↄ\",\n  \"d\": \"đɗɖᴅƌꮷԁɦ\",\n  \"e\": \"ɛǝᴇɇ\",\n  \"f\": \"ꝼƒ\",\n  \"g\": \"ǥɠꞡᵹꝿɢ\",\n  \"h\": \"ħⱨⱶɥ\",\n  \"i\": \"ɨı\",\n  \"j\": \"ɉȷ\",\n  \"k\": \"ƙⱪꝁꝃꝅꞣ\",\n  \"l\": \"łƚɫⱡꝉꝇꞁɭ\",\n  \"m\": \"ɱɯϻ\",\n  \"n\": \"ꞥƞɲꞑᴎлԉ\",\n  \"o\": \"øǿɔɵꝋꝍᴑ\",\n  \"oe\": \"œ\",\n  \"oi\": \"ƣ\",\n  \"oo\": \"ꝏ\",\n  \"ou\": \"ȣ\",\n  \"p\": \"ƥᵽꝑꝓꝕρ\",\n  \"q\": \"ꝗꝙɋ\",\n  \"r\": \"ɍɽꝛꞧꞃ\",\n  \"s\": \"ßȿꞩꞅʂ\",\n  \"t\": \"ŧƭʈⱦꞇ\",\n  \"th\": \"þ\",\n  \"tz\": \"ꜩ\",\n  \"u\": \"ʉ\",\n  \"v\": \"ʋꝟʌ\",\n  \"vy\": \"ꝡ\",\n  \"w\": \"ⱳ\",\n  \"y\": \"ƴɏỿ\",\n  \"z\": \"ƶȥɀⱬꝣ\",\n  \"hv\": \"ƕ\"\n};\n\nfor (let latin in latin_condensed) {\n  let unicode = latin_condensed[latin] || '';\n\n  for (let i = 0; i < unicode.length; i++) {\n    let char = unicode.substring(i, i + 1);\n    latin_convert[char] = latin;\n  }\n}\n\nconst convert_pat = new RegExp(Object.keys(latin_convert).join('|') + '|' + accent_pat, 'gu');\n/**\n * Initialize the unicode_map from the give code point ranges\n *\n * @param {TCodePoints=} _code_points\n */\n\nconst initialize = _code_points => {\n  if (unicode_map !== undefined) return;\n  unicode_map = generateMap(_code_points || code_points);\n};\n/**\n * Helper method for normalize a string\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize\n * @param {string} str\n * @param {string} form\n */\n\nconst normalize = (str, form = 'NFKD') => str.normalize(form);\n/**\n * Remove accents without reordering string\n * calling str.normalize('NFKD') on \\u{594}\\u{595}\\u{596} becomes \\u{596}\\u{594}\\u{595}\n * via https://github.com/krisk/Fuse/issues/133#issuecomment-318692703\n * @param {string} str\n * @return {string}\n */\n\nconst asciifold = str => {\n  return toArray(str).reduce(\n  /**\n   * @param {string} result\n   * @param {string} char\n   */\n  (result, char) => {\n    return result + _asciifold(char);\n  }, '');\n};\n/**\n * @param {string} str\n * @return {string}\n */\n\nconst _asciifold = str => {\n  str = normalize(str).toLowerCase().replace(convert_pat, (\n  /** @type {string} */\n  char) => {\n    return latin_convert[char] || '';\n  }); //return str;\n\n  return normalize(str, 'NFC');\n};\n/**\n * Generate a list of unicode variants from the list of code points\n * @param {TCodePoints} code_points\n * @yield {TCodePointObj}\n */\n\nfunction* generator(code_points) {\n  for (const [code_point_min, code_point_max] of code_points) {\n    for (let i = code_point_min; i <= code_point_max; i++) {\n      let composed = String.fromCharCode(i);\n      let folded = asciifold(composed);\n\n      if (folded == composed.toLowerCase()) {\n        continue;\n      } // skip when folded is a string longer than 3 characters long\n      // bc the resulting regex patterns will be long\n      // eg:\n      // folded صلى الله عليه وسلم length 18 code point 65018\n      // folded جل جلاله length 8 code point 65019\n\n\n      if (folded.length > max_char_length) {\n        continue;\n      }\n\n      if (folded.length == 0) {\n        continue;\n      }\n\n      yield {\n        folded: folded,\n        composed: composed,\n        code_point: i\n      };\n    }\n  }\n}\n/**\n * Generate a unicode map from the list of code points\n * @param {TCodePoints} code_points\n * @return {TUnicodeSets}\n */\n\nconst generateSets = code_points => {\n  /** @type {{[key:string]:Set<string>}} */\n  const unicode_sets = {};\n  /**\n   * @param {string} folded\n   * @param {string} to_add\n   */\n\n  const addMatching = (folded, to_add) => {\n    /** @type {Set<string>} */\n    const folded_set = unicode_sets[folded] || new Set();\n    const patt = new RegExp('^' + setToPattern(folded_set) + '$', 'iu');\n\n    if (to_add.match(patt)) {\n      return;\n    }\n\n    folded_set.add(escape_regex(to_add));\n    unicode_sets[folded] = folded_set;\n  };\n\n  for (let value of generator(code_points)) {\n    addMatching(value.folded, value.folded);\n    addMatching(value.folded, value.composed);\n  }\n\n  return unicode_sets;\n};\n/**\n * Generate a unicode map from the list of code points\n * ae => (?:(?:ae|Æ|Ǽ|Ǣ)|(?:A|Ⓐ|Ａ...)(?:E|ɛ|Ⓔ...))\n *\n * @param {TCodePoints} code_points\n * @return {TUnicodeMap}\n */\n\nconst generateMap = code_points => {\n  /** @type {TUnicodeSets} */\n  const unicode_sets = generateSets(code_points);\n  /** @type {TUnicodeMap} */\n\n  const unicode_map = {};\n  /** @type {string[]} */\n\n  let multi_char = [];\n\n  for (let folded in unicode_sets) {\n    let set = unicode_sets[folded];\n\n    if (set) {\n      unicode_map[folded] = setToPattern(set);\n    }\n\n    if (folded.length > 1) {\n      multi_char.push(escape_regex(folded));\n    }\n  }\n\n  multi_char.sort((a, b) => b.length - a.length);\n  const multi_char_patt = arrayToPattern(multi_char);\n  multi_char_reg = new RegExp('^' + multi_char_patt, 'u');\n  return unicode_map;\n};\n/**\n * Map each element of an array from it's folded value to all possible unicode matches\n * @param {string[]} strings\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst mapSequence = (strings, min_replacement = 1) => {\n  let chars_replaced = 0;\n  strings = strings.map(str => {\n    if (unicode_map[str]) {\n      chars_replaced += str.length;\n    }\n\n    return unicode_map[str] || str;\n  });\n\n  if (chars_replaced >= min_replacement) {\n    return sequencePattern(strings);\n  }\n\n  return '';\n};\n/**\n * Convert a short string and split it into all possible patterns\n * Keep a pattern only if min_replacement is met\n *\n * 'abc'\n * \t\t=> [['abc'],['ab','c'],['a','bc'],['a','b','c']]\n *\t\t=> ['abc-pattern','ab-c-pattern'...]\n *\n *\n * @param {string} str\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst substringsToPattern = (str, min_replacement = 1) => {\n  min_replacement = Math.max(min_replacement, str.length - 1);\n  return arrayToPattern(allSubstrings(str).map(sub_pat => {\n    return mapSequence(sub_pat, min_replacement);\n  }));\n};\n/**\n * Convert an array of sequences into a pattern\n * [{start:0,end:3,length:3,substr:'iii'}...] => (?:iii...)\n *\n * @param {Sequence[]} sequences\n * @param {boolean} all\n */\n\nconst sequencesToPattern = (sequences, all = true) => {\n  let min_replacement = sequences.length > 1 ? 1 : 0;\n  return arrayToPattern(sequences.map(sequence => {\n    let seq = [];\n    const len = all ? sequence.length() : sequence.length() - 1;\n\n    for (let j = 0; j < len; j++) {\n      seq.push(substringsToPattern(sequence.substrs[j] || '', min_replacement));\n    }\n\n    return sequencePattern(seq);\n  }));\n};\n/**\n * Return true if the sequence is already in the sequences\n * @param {Sequence} needle_seq\n * @param {Sequence[]} sequences\n */\n\n\nconst inSequences = (needle_seq, sequences) => {\n  for (const seq of sequences) {\n    if (seq.start != needle_seq.start || seq.end != needle_seq.end) {\n      continue;\n    }\n\n    if (seq.substrs.join('') !== needle_seq.substrs.join('')) {\n      continue;\n    }\n\n    let needle_parts = needle_seq.parts;\n    /**\n     * @param {TSequencePart} part\n     */\n\n    const filter = part => {\n      for (const needle_part of needle_parts) {\n        if (needle_part.start === part.start && needle_part.substr === part.substr) {\n          return false;\n        }\n\n        if (part.length == 1 || needle_part.length == 1) {\n          continue;\n        } // check for overlapping parts\n        // a = ['::=','==']\n        // b = ['::','===']\n        // a = ['r','sm']\n        // b = ['rs','m']\n\n\n        if (part.start < needle_part.start && part.end > needle_part.start) {\n          return true;\n        }\n\n        if (needle_part.start < part.start && needle_part.end > part.start) {\n          return true;\n        }\n      }\n\n      return false;\n    };\n\n    let filtered = seq.parts.filter(filter);\n\n    if (filtered.length > 0) {\n      continue;\n    }\n\n    return true;\n  }\n\n  return false;\n};\n\nclass Sequence {\n  constructor() {\n    /** @type {TSequencePart[]} */\n    this.parts = [];\n    /** @type {string[]} */\n\n    this.substrs = [];\n    this.start = 0;\n    this.end = 0;\n  }\n  /**\n   * @param {TSequencePart|undefined} part\n   */\n\n\n  add(part) {\n    if (part) {\n      this.parts.push(part);\n      this.substrs.push(part.substr);\n      this.start = Math.min(part.start, this.start);\n      this.end = Math.max(part.end, this.end);\n    }\n  }\n\n  last() {\n    return this.parts[this.parts.length - 1];\n  }\n\n  length() {\n    return this.parts.length;\n  }\n  /**\n   * @param {number} position\n   * @param {TSequencePart} last_piece\n   */\n\n\n  clone(position, last_piece) {\n    let clone = new Sequence();\n    let parts = JSON.parse(JSON.stringify(this.parts));\n    let last_part = parts.pop();\n\n    for (const part of parts) {\n      clone.add(part);\n    }\n\n    let last_substr = last_piece.substr.substring(0, position - last_part.start);\n    let clone_last_len = last_substr.length;\n    clone.add({\n      start: last_part.start,\n      end: last_part.start + clone_last_len,\n      length: clone_last_len,\n      substr: last_substr\n    });\n    return clone;\n  }\n\n}\n/**\n * Expand a regular expression pattern to include unicode variants\n * \teg /a/ becomes /aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑAⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ/\n *\n * Issue:\n *  ﺊﺋ [ 'ﺊ = \\\\u{fe8a}', 'ﺋ = \\\\u{fe8b}' ]\n *\tbecomes:\tئئ [ 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}', 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}' ]\n *\n *\tİĲ = IIJ = ⅡJ\n *\n * \t1/2/4\n *\n * @param {string} str\n * @return {string|undefined}\n */\n\n\nconst getPattern = str => {\n  initialize();\n  str = asciifold(str);\n  let pattern = '';\n  let sequences = [new Sequence()];\n\n  for (let i = 0; i < str.length; i++) {\n    let substr = str.substring(i);\n    let match = substr.match(multi_char_reg);\n    const char = str.substring(i, i + 1);\n    const match_str = match ? match[0] : null; // loop through sequences\n    // add either the char or multi_match\n\n    let overlapping = [];\n    let added_types = new Set();\n\n    for (const sequence of sequences) {\n      const last_piece = sequence.last();\n\n      if (!last_piece || last_piece.length == 1 || last_piece.end <= i) {\n        // if we have a multi match\n        if (match_str) {\n          const len = match_str.length;\n          sequence.add({\n            start: i,\n            end: i + len,\n            length: len,\n            substr: match_str\n          });\n          added_types.add('1');\n        } else {\n          sequence.add({\n            start: i,\n            end: i + 1,\n            length: 1,\n            substr: char\n          });\n          added_types.add('2');\n        }\n      } else if (match_str) {\n        let clone = sequence.clone(i, last_piece);\n        const len = match_str.length;\n        clone.add({\n          start: i,\n          end: i + len,\n          length: len,\n          substr: match_str\n        });\n        overlapping.push(clone);\n      } else {\n        // don't add char\n        // adding would create invalid patterns: 234 => [2,34,4]\n        added_types.add('3');\n      }\n    } // if we have overlapping\n\n\n    if (overlapping.length > 0) {\n      // ['ii','iii'] before ['i','i','iii']\n      overlapping = overlapping.sort((a, b) => {\n        return a.length() - b.length();\n      });\n\n      for (let clone of overlapping) {\n        // don't add if we already have an equivalent sequence\n        if (inSequences(clone, sequences)) {\n          continue;\n        }\n\n        sequences.push(clone);\n      }\n\n      continue;\n    } // if we haven't done anything unique\n    // clean up the patterns\n    // helps keep patterns smaller\n    // if str = 'r₨㎧aarss', pattern will be 446 instead of 655\n\n\n    if (i > 0 && added_types.size == 1 && !added_types.has('3')) {\n      pattern += sequencesToPattern(sequences, false);\n      let new_seq = new Sequence();\n      const old_seq = sequences[0];\n\n      if (old_seq) {\n        new_seq.add(old_seq.last());\n      }\n\n      sequences = [new_seq];\n    }\n  }\n\n  pattern += sequencesToPattern(sequences, true);\n  return pattern;\n};\n\nexport { _asciifold, asciifold, code_points, generateMap, generateSets, generator, getPattern, initialize, mapSequence, normalize, substringsToPattern, unicode_map };\n//# sourceMappingURL=index.js.map\n", "/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */\nimport { asciifold } from '@orchidjs/unicode-variants';\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nconst getAttr = (obj, name) => {\n  if (!obj) return;\n  return obj[name];\n};\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\n\nconst getAttrNesting = (obj, name) => {\n  if (!obj) return;\n  var part,\n      names = name.split(\".\");\n\n  while ((part = names.shift()) && (obj = obj[part]));\n\n  return obj;\n};\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\n\nconst scoreValue = (value, token, weight) => {\n  var score, pos;\n  if (!value) return 0;\n  value = value + '';\n  if (token.regex == null) return 0;\n  pos = value.search(token.regex);\n  if (pos === -1) return 0;\n  score = token.string.length / value.length;\n  if (pos === 0) score += 0.5;\n  return score * weight;\n};\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\n\nconst propToArray = (obj, key) => {\n  var value = obj[key];\n  if (typeof value == 'function') return value;\n\n  if (value && !Array.isArray(value)) {\n    obj[key] = [value];\n  }\n};\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\n\nconst iterate = (object, callback) => {\n  if (Array.isArray(object)) {\n    object.forEach(callback);\n  } else {\n    for (var key in object) {\n      if (object.hasOwnProperty(key)) {\n        callback(object[key], key);\n      }\n    }\n  }\n};\nconst cmp = (a, b) => {\n  if (typeof a === 'number' && typeof b === 'number') {\n    return a > b ? 1 : a < b ? -1 : 0;\n  }\n\n  a = asciifold(a + '').toLowerCase();\n  b = asciifold(b + '').toLowerCase();\n  if (a > b) return 1;\n  if (b > a) return -1;\n  return 0;\n};\n\nexport { cmp, getAttr, getAttrNesting, iterate, propToArray, scoreValue };\n//# sourceMappingURL=utils.js.map\n", "/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */\nimport { iterate, cmp, propToArray, getAttrNesting, getAttr, scoreValue } from './utils.js';\nexport { cmp, getAttr, getAttrNesting, iterate, propToArray, scoreValue } from './utils.js';\nimport { escape_regex, getPattern } from '@orchidjs/unicode-variants';\nexport { getPattern } from '@orchidjs/unicode-variants';\n\n/**\n * sifter.js\n * Copyright (c) 2013–2020 Brian <PERSON> & contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\nclass Sifter {\n  // []|{};\n\n  /**\n   * Textually searches arrays and hashes of objects\n   * by property (or multiple properties). Designed\n   * specifically for autocomplete.\n   *\n   */\n  constructor(items, settings) {\n    this.items = void 0;\n    this.settings = void 0;\n    this.items = items;\n    this.settings = settings || {\n      diacritics: true\n    };\n  }\n\n  /**\n   * Splits a search string into an array of individual\n   * regexps to be used to match results.\n   *\n   */\n  tokenize(query, respect_word_boundaries, weights) {\n    if (!query || !query.length) return [];\n    const tokens = [];\n    const words = query.split(/\\s+/);\n    var field_regex;\n\n    if (weights) {\n      field_regex = new RegExp('^(' + Object.keys(weights).map(escape_regex).join('|') + ')\\:(.*)$');\n    }\n\n    words.forEach(word => {\n      let field_match;\n      let field = null;\n      let regex = null; // look for \"field:query\" tokens\n\n      if (field_regex && (field_match = word.match(field_regex))) {\n        field = field_match[1];\n        word = field_match[2];\n      }\n\n      if (word.length > 0) {\n        if (this.settings.diacritics) {\n          regex = getPattern(word) || null;\n        } else {\n          regex = escape_regex(word);\n        }\n\n        if (regex && respect_word_boundaries) regex = \"\\\\b\" + regex;\n      }\n\n      tokens.push({\n        string: word,\n        regex: regex ? new RegExp(regex, 'iu') : null,\n        field: field\n      });\n    });\n    return tokens;\n  }\n\n  /**\n   * Returns a function to be used to score individual results.\n   *\n   * Good matches will have a higher score than poor matches.\n   * If an item is not a match, 0 will be returned by the function.\n   *\n   * @returns {T.ScoreFn}\n   */\n  getScoreFunction(query, options) {\n    var search = this.prepareSearch(query, options);\n    return this._getScoreFunction(search);\n  }\n  /**\n   * @returns {T.ScoreFn}\n   *\n   */\n\n\n  _getScoreFunction(search) {\n    const tokens = search.tokens,\n          token_count = tokens.length;\n\n    if (!token_count) {\n      return function () {\n        return 0;\n      };\n    }\n\n    const fields = search.options.fields,\n          weights = search.weights,\n          field_count = fields.length,\n          getAttrFn = search.getAttrFn;\n\n    if (!field_count) {\n      return function () {\n        return 1;\n      };\n    }\n    /**\n     * Calculates the score of an object\n     * against the search query.\n     *\n     */\n\n\n    const scoreObject = function () {\n      if (field_count === 1) {\n        return function (token, data) {\n          const field = fields[0].field;\n          return scoreValue(getAttrFn(data, field), token, weights[field] || 1);\n        };\n      }\n\n      return function (token, data) {\n        var sum = 0; // is the token specific to a field?\n\n        if (token.field) {\n          const value = getAttrFn(data, token.field);\n\n          if (!token.regex && value) {\n            sum += 1 / field_count;\n          } else {\n            sum += scoreValue(value, token, 1);\n          }\n        } else {\n          iterate(weights, (weight, field) => {\n            sum += scoreValue(getAttrFn(data, field), token, weight);\n          });\n        }\n\n        return sum / field_count;\n      };\n    }();\n\n    if (token_count === 1) {\n      return function (data) {\n        return scoreObject(tokens[0], data);\n      };\n    }\n\n    if (search.options.conjunction === 'and') {\n      return function (data) {\n        var score,\n            sum = 0;\n\n        for (let token of tokens) {\n          score = scoreObject(token, data);\n          if (score <= 0) return 0;\n          sum += score;\n        }\n\n        return sum / token_count;\n      };\n    } else {\n      return function (data) {\n        var sum = 0;\n        iterate(tokens, token => {\n          sum += scoreObject(token, data);\n        });\n        return sum / token_count;\n      };\n    }\n  }\n\n  /**\n   * Returns a function that can be used to compare two\n   * results, for sorting purposes. If no sorting should\n   * be performed, `null` will be returned.\n   *\n   * @return function(a,b)\n   */\n  getSortFunction(query, options) {\n    var search = this.prepareSearch(query, options);\n    return this._getSortFunction(search);\n  }\n\n  _getSortFunction(search) {\n    var implicit_score,\n        sort_flds = [];\n    const self = this,\n          options = search.options,\n          sort = !search.query && options.sort_empty ? options.sort_empty : options.sort;\n\n    if (typeof sort == 'function') {\n      return sort.bind(this);\n    }\n    /**\n     * Fetches the specified sort field value\n     * from a search result item.\n     *\n     */\n\n\n    const get_field = function get_field(name, result) {\n      if (name === '$score') return result.score;\n      return search.getAttrFn(self.items[result.id], name);\n    }; // parse options\n\n\n    if (sort) {\n      for (let s of sort) {\n        if (search.query || s.field !== '$score') {\n          sort_flds.push(s);\n        }\n      }\n    } // the \"$score\" field is implied to be the primary\n    // sort field, unless it's manually specified\n\n\n    if (search.query) {\n      implicit_score = true;\n\n      for (let fld of sort_flds) {\n        if (fld.field === '$score') {\n          implicit_score = false;\n          break;\n        }\n      }\n\n      if (implicit_score) {\n        sort_flds.unshift({\n          field: '$score',\n          direction: 'desc'\n        });\n      } // without a search.query, all items will have the same score\n\n    } else {\n      sort_flds = sort_flds.filter(fld => fld.field !== '$score');\n    } // build function\n\n\n    const sort_flds_count = sort_flds.length;\n\n    if (!sort_flds_count) {\n      return null;\n    }\n\n    return function (a, b) {\n      var result, field;\n\n      for (let sort_fld of sort_flds) {\n        field = sort_fld.field;\n        let multiplier = sort_fld.direction === 'desc' ? -1 : 1;\n        result = multiplier * cmp(get_field(field, a), get_field(field, b));\n        if (result) return result;\n      }\n\n      return 0;\n    };\n  }\n\n  /**\n   * Parses a search query and returns an object\n   * with tokens and fields ready to be populated\n   * with results.\n   *\n   */\n  prepareSearch(query, optsUser) {\n    const weights = {};\n    var options = Object.assign({}, optsUser);\n    propToArray(options, 'sort');\n    propToArray(options, 'sort_empty'); // convert fields to new format\n\n    if (options.fields) {\n      propToArray(options, 'fields');\n      const fields = [];\n      options.fields.forEach(field => {\n        if (typeof field == 'string') {\n          field = {\n            field: field,\n            weight: 1\n          };\n        }\n\n        fields.push(field);\n        weights[field.field] = 'weight' in field ? field.weight : 1;\n      });\n      options.fields = fields;\n    }\n\n    return {\n      options: options,\n      query: query.toLowerCase().trim(),\n      tokens: this.tokenize(query, options.respect_word_boundaries, weights),\n      total: 0,\n      items: [],\n      weights: weights,\n      getAttrFn: options.nesting ? getAttrNesting : getAttr\n    };\n  }\n\n  /**\n   * Searches through all items and returns a sorted array of matches.\n   *\n   */\n  search(query, options) {\n    var self = this,\n        score,\n        search;\n    search = this.prepareSearch(query, options);\n    options = search.options;\n    query = search.query; // generate result scoring function\n\n    const fn_score = options.score || self._getScoreFunction(search); // perform search and sort\n\n\n    if (query.length) {\n      iterate(self.items, (item, id) => {\n        score = fn_score(item);\n\n        if (options.filter === false || score > 0) {\n          search.items.push({\n            'score': score,\n            'id': id\n          });\n        }\n      });\n    } else {\n      iterate(self.items, (_, id) => {\n        search.items.push({\n          'score': 1,\n          'id': id\n        });\n      });\n    }\n\n    const fn_sort = self._getSortFunction(search);\n\n    if (fn_sort) search.items.sort(fn_sort); // apply limits\n\n    search.total = search.items.length;\n\n    if (typeof options.limit === 'number') {\n      search.items = search.items.slice(0, options.limit);\n    }\n\n    return search;\n  }\n\n}\n\nexport { Sifter };\n//# sourceMappingURL=sifter.js.map\n", "\nimport { asciifold } from '@orchidjs/unicode-variants';\nimport * as T from './types';\n\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttr = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    return obj[name];\n};\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttrNesting = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    var part, names = name.split(\".\");\n\twhile( (part = names.shift()) && (obj = obj[part]));\n    return obj;\n};\n\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\nexport const scoreValue = (value:string, token:T.Token, weight:number ):number => {\n\tvar score, pos;\n\n\tif (!value) return 0;\n\n\tvalue = value + '';\n\tif( token.regex == null ) return 0;\n\tpos = value.search(token.regex);\n\tif (pos === -1) return 0;\n\n\tscore = token.string.length / value.length;\n\tif (pos === 0) score += 0.5;\n\n\treturn score * weight;\n};\n\n\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\nexport const propToArray = (obj:{[key:string]:any}, key:string) => {\n\tvar value = obj[key];\n\n\tif( typeof value == 'function' ) return value;\n\n\tif( value && !Array.isArray(value) ){\n\t\tobj[key] = [value];\n\t}\n}\n\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n\n\n\nexport const cmp = (a:number|string, b:number|string) => {\n\tif (typeof a === 'number' && typeof b === 'number') {\n\t\treturn a > b ? 1 : (a < b ? -1 : 0);\n\t}\n\ta = asciifold(a + '').toLowerCase();\n\tb = asciifold(b + '').toLowerCase();\n\tif (a > b) return 1;\n\tif (b > a) return -1;\n\treturn 0;\n};\n", "\nimport { iterate } from '@orchidjs/sifter/lib/utils';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\11\\12\\14\\15\\40]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * highlight v3 | MIT license | <PERSON> <<EMAIL>>\n * Highlights arbitrary terms in a node.\n *\n * - Modified by <PERSON> <<EMAIL>> 2011-6-24 (added regex)\n * - Modified by <PERSON> <<EMAIL>> 2012-8-27 (cleanup)\n */\n\nimport {replaceNode} from '../vanilla';\n\n\nexport const highlight = (element:HTMLElement, regex:string|RegExp) => {\n\n\tif( regex === null ) return;\n\n\t// convet string to regex\n\tif( typeof regex === 'string' ){\n\n\t\tif( !regex.length ) return;\n\t\tregex = new RegExp(regex, 'i');\n\t}\n\n\n\t// Wrap matching part of text node with highlighting <span>, e.g.\n\t// Soccer  ->  <span class=\"highlight\">Soc</span>cer  for regex = /soc/i\n\tconst highlightText = ( node:Text ):number => {\n\n\t\tvar match = node.data.match(regex);\n\t\tif( match && node.data.length > 0 ){\n\t\t\tvar spannode\t\t= document.createElement('span');\n\t\t\tspannode.className\t= 'highlight';\n\t\t\tvar middlebit\t\t= node.splitText(match.index as number);\n\n\t\t\tmiddlebit.splitText(match[0]!.length);\n\t\t\tvar middleclone\t\t= middlebit.cloneNode(true);\n\n\t\t\tspannode.appendChild(middleclone);\n\t\t\treplaceNode(middlebit, spannode);\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn 0;\n\t};\n\n\t// Recurse element node, looking for child text nodes to highlight, unless element\n\t// is childless, <script>, <style>, or already highlighted: <span class=\"hightlight\">\n\tconst highlightChildren = ( node:Element ):void => {\n\t\tif( node.nodeType === 1 && node.childNodes && !/(script|style)/i.test(node.tagName) && ( node.className !== 'highlight' || node.tagName !== 'SPAN' ) ){\n\t\t\tArray.from(node.childNodes).forEach(element => {\n\t\t\t\thighlightRecursive(element);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tconst highlightRecursive = ( node:Node|Element ):number => {\n\n\t\tif( node.nodeType === 3 ){\n\t\t\treturn highlightText(node as Text);\n\t\t}\n\n\t\thighlightChildren(node as Element);\n\n\t\treturn 0;\n\t};\n\n\thighlightRecursive( element );\n};\n\n/**\n * removeHighlight fn copied from highlight v5 and\n * edited to remove with(), pass js strict mode, and use without jquery\n */\nexport const removeHighlight = (el:HTMLElement) => {\n\tvar elements = el.querySelectorAll(\"span.highlight\");\n\tArray.prototype.forEach.call(elements, function(el:HTMLElement){\n\t\tvar parent = el.parentNode as Node;\n\t\tparent.replaceChild(el.firstChild as Node, el);\n\t\tparent.normalize();\n\t});\n};\n", "export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nexport default {\n\toptions: [],\n\toptgroups: [],\n\n\tplugins: [],\n\tdelimiter: ',',\n\tsplitOn: null, // regexp or string for splitting up values from a paste command\n\tpersist: true,\n\tdiacritics: true,\n\tcreate: null,\n\tcreateOnBlur: false,\n\tcreateFilter: null,\n\thighlight: true,\n\topenOnFocus: true,\n\tshouldOpen: null,\n\tmaxOptions: 50,\n\tmaxItems: null,\n\thideSelected: null,\n\tduplicates: false,\n\taddPrecedence: false,\n\tselectOnTab: false,\n\tpreload: null,\n\tallowEmptyOption: false,\n\t//closeAfterSelect: false,\n\n\tloadThrottle: 300,\n\tloadingClass: 'loading',\n\n\tdataAttr: null, //'data-data',\n\toptgroupField: 'optgroup',\n\tvalueField: 'value',\n\tlabelField: 'text',\n\tdisabledField: 'disabled',\n\toptgroupLabelField: 'label',\n\toptgroupValueField: 'value',\n\tlockOptgroupOrder: false,\n\n\tsortField: '$order',\n\tsearchField: ['text'],\n\tsearchConjunction: 'and',\n\n\tmode: null,\n\twrapperClass: 'ts-wrapper',\n\tcontrolClass: 'ts-control',\n\tdropdownClass: 'ts-dropdown',\n\tdropdownContentClass: 'ts-dropdown-content',\n\titemClass: 'item',\n\toptionClass: 'option',\n\n\tdropdownParent: null,\n\tcontrolInput: '<input type=\"text\" autocomplete=\"off\" size=\"1\" />',\n\n\tcopyClassesToDropdown: false,\n\n\tplaceholder: null,\n\thidePlaceholder: null,\n\n\tshouldLoad: function(query:string):boolean{\n\t\treturn query.length > 0;\n\t},\n\n\t/*\n\tload                 : null, // function(query, callback) { ... }\n\tscore                : null, // function(search) { ... }\n\tonInitialize         : null, // function() { ... }\n\tonChange             : null, // function(value) { ... }\n\tonItemAdd            : null, // function(value, $item) { ... }\n\tonItemRemove         : null, // function(value) { ... }\n\tonClear              : null, // function() { ... }\n\tonOptionAdd          : null, // function(value, data) { ... }\n\tonOptionRemove       : null, // function(value) { ... }\n\tonOptionClear        : null, // function() { ... }\n\tonOptionGroupAdd     : null, // function(id, data) { ... }\n\tonOptionGroupRemove  : null, // function(id) { ... }\n\tonOptionGroupClear   : null, // function() { ... }\n\tonDropdownOpen       : null, // function(dropdown) { ... }\n\tonDropdownClose      : null, // function(dropdown) { ... }\n\tonType               : null, // function(str) { ... }\n\tonDelete             : null, // function(values) { ... }\n\t*/\n\n\trender: {\n\t\t/*\n\t\titem: null,\n\t\toptgroup: null,\n\t\toptgroup_header: null,\n\t\toption: null,\n\t\toption_create: null\n\t\t*/\n\t}\n};\n", "\nimport TomSelect from './tom-select';\nimport { TomLoadCallback } from './types/index';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n", "import defaults from './defaults';\nimport { hash_key } from './utils';\nimport { TomOption, TomSettings, RecursivePartial } from './types/index';\nimport { iterate } from '@orchidjs/sifter/lib/utils';\nimport { TomInput } from './types/index';\n\n\nexport default function getSettings( input:TomInput, settings_user:RecursivePartial<TomSettings>):TomSettings{\n\tvar settings:TomSettings\t= Object.assign({}, defaults, settings_user);\n\n\tvar attr_data\t\t\t\t= settings.dataAttr;\n\tvar field_label\t\t\t\t= settings.labelField;\n\tvar field_value\t\t\t\t= settings.valueField;\n\tvar field_disabled\t\t\t= settings.disabledField;\n\tvar field_optgroup\t\t\t= settings.optgroupField;\n\tvar field_optgroup_label\t= settings.optgroupLabelField;\n\tvar field_optgroup_value\t= settings.optgroupValueField;\n\n\tvar tag_name\t\t\t\t= input.tagName.toLowerCase();\n\tvar placeholder\t\t\t\t= input.getAttribute('placeholder') || input.getAttribute('data-placeholder');\n\n\tif (!placeholder && !settings.allowEmptyOption) {\n\t\tlet option\t\t= input.querySelector('option[value=\"\"]');\n\t\tif( option ){\n\t\t\tplaceholder = option.textContent;\n\t\t}\n\n\t}\n\n\tvar settings_element:{\n\t\tplaceholder\t: null|string,\n\t\toptions\t\t: TomOption[],\n\t\toptgroups\t: TomOption[],\n\t\titems\t\t: string[],\n\t\tmaxItems\t: null|number,\n\t} = {\n\t\tplaceholder\t: placeholder,\n\t\toptions\t\t: [],\n\t\toptgroups\t: [],\n\t\titems\t\t: [],\n\t\tmaxItems\t: null,\n\t};\n\n\n\t/**\n\t * Initialize from a <select> element.\n\t *\n\t */\n\tvar init_select = () => {\n\t\tvar tagName;\n\t\tvar options = settings_element.options;\n\t\tvar optionsMap:{[key:string]:any} = {};\n\t\tvar group_count = 1;\n\n\t\tvar readData = (el:HTMLElement):TomOption => {\n\n\t\t\tvar data\t= Object.assign({},el.dataset); // get plain object from DOMStringMap\n\t\t\tvar json\t= attr_data && data[attr_data];\n\n\t\t\tif( typeof json === 'string' && json.length ){\n\t\t\t\tdata = Object.assign(data,JSON.parse(json));\n\t\t\t}\n\n\t\t\treturn data;\n\t\t};\n\n\t\tvar addOption = (option:HTMLOptionElement, group?:string) => {\n\n\t\t\tvar value = hash_key(option.value);\n\t\t\tif ( value == null ) return;\n\t\t\tif ( !value && !settings.allowEmptyOption) return;\n\n\t\t\t// if the option already exists, it's probably been\n\t\t\t// duplicated in another optgroup. in this case, push\n\t\t\t// the current group to the \"optgroup\" property on the\n\t\t\t// existing option so that it's rendered in both places.\n\t\t\tif (optionsMap.hasOwnProperty(value)) {\n\t\t\t\tif (group) {\n\t\t\t\t\tvar arr = optionsMap[value][field_optgroup];\n\t\t\t\t\tif (!arr) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = group;\n\t\t\t\t\t} else if (!Array.isArray(arr)) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = [arr, group];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarr.push(group);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}else{\n\n\t\t\t\tvar option_data             = readData(option);\n\t\t\t\toption_data[field_label]    = option_data[field_label] || option.textContent;\n\t\t\t\toption_data[field_value]    = option_data[field_value] || value;\n\t\t\t\toption_data[field_disabled] = option_data[field_disabled] || option.disabled;\n\t\t\t\toption_data[field_optgroup] = option_data[field_optgroup] || group;\n\t\t\t\toption_data.$option\t\t\t= option;\n\n\t\t\t\toptionsMap[value] = option_data;\n\t\t\t\toptions.push(option_data);\n\t\t\t}\n\n\t\t\tif( option.selected ){\n\t\t\t\tsettings_element.items.push(value);\n\t\t\t}\n\t\t};\n\n\t\tvar addGroup = ( optgroup:HTMLOptGroupElement ) => {\n\t\t\tvar id:string, optgroup_data\n\n\t\t\toptgroup_data\t\t\t\t\t\t\t= readData(optgroup);\n\t\t\toptgroup_data[field_optgroup_label]\t\t= optgroup_data[field_optgroup_label] || optgroup.getAttribute('label') || '';\n\t\t\toptgroup_data[field_optgroup_value]\t\t= optgroup_data[field_optgroup_value] || group_count++;\n\t\t\toptgroup_data[field_disabled]\t\t\t= optgroup_data[field_disabled] || optgroup.disabled;\n\t\t\tsettings_element.optgroups.push(optgroup_data);\n\n\t\t\tid = optgroup_data[field_optgroup_value];\n\n\t\t\titerate(optgroup.children, (option)=>{\n\t\t\t\taddOption(option as HTMLOptionElement, id);\n\t\t\t});\n\n\t\t};\n\n\t\tsettings_element.maxItems = input.hasAttribute('multiple') ? null : 1;\n\n\t\titerate(input.children,(child)=>{\n\t\t\ttagName = child.tagName.toLowerCase();\n\t\t\tif (tagName === 'optgroup') {\n\t\t\t\taddGroup(child as HTMLOptGroupElement);\n\t\t\t} else if (tagName === 'option') {\n\t\t\t\taddOption(child as HTMLOptionElement);\n\t\t\t}\n\t\t});\n\n\t};\n\n\n\t/**\n\t * Initialize from a <input type=\"text\"> element.\n\t *\n\t */\n\tvar init_textbox = () => {\n\t\tconst data_raw = input.getAttribute(attr_data);\n\n\t\tif (!data_raw) {\n\t\t\tvar value = input.value.trim() || '';\n\t\t\tif (!settings.allowEmptyOption && !value.length) return;\n\t\t\tconst values = value.split(settings.delimiter);\n\n\t\t\titerate( values, (value) => {\n\t\t\t\tconst option:TomOption = {};\n\t\t\t\toption[field_label] = value;\n\t\t\t\toption[field_value] = value;\n\t\t\t\tsettings_element.options.push(option);\n\t\t\t});\n\t\t\tsettings_element.items = values;\n\t\t} else {\n\t\t\tsettings_element.options = JSON.parse(data_raw);\n\t\t\titerate( settings_element.options, (opt) => {\n\t\t\t\tsettings_element.items.push(opt[field_value]);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tif (tag_name === 'select') {\n\t\tinit_select();\n\t} else {\n\t\tinit_textbox();\n\t}\n\n\treturn Object.assign( {}, defaults, settings_element, settings_user) as TomSettings;\n};\n", "\nimport MicroEvent from './contrib/microevent';\nimport MicroPlugin from './contrib/microplugin';\nimport { Sifter, iterate } from '@orchidjs/sifter';\nimport { escape_regex } from '@orchidjs/unicode-variants';\nimport { TomInput, TomArgObject, TomO<PERSON>, TomOptions, TomCreateFilter, TomCreateCallback, TomItem, TomSettings, TomTemplateNames, TomClearFilter, RecursivePartial } from './types/index';\nimport {highlight, removeHighlight} from './contrib/highlight';\nimport * as constants from './constants';\nimport getSettings from './getSettings';\nimport {\n\thash_key,\n\tget_hash,\n\tescape_html,\n\tdebounce_events,\n\tgetSelection,\n\tpreventDefault,\n\taddEvent,\n\tloadDebounce,\n\tisKeyDown,\n\tgetId,\n\taddSlashes,\n\tappend\n} from './utils';\n\nimport {\n\tgetDom,\n\tisHtmlString,\n\tescapeQuery,\n\ttriggerEvent,\n\tapplyCSS,\n\taddClasses,\n\tremoveClasses,\n\tparentMatch,\n\tgetTail,\n\tisEmptyObject,\n\tnodeIndex,\n\tsetAttr,\n\treplaceNode\n} from './vanilla';\n\nvar instance_i = 0;\n\nexport default class TomSelect extends MicroPlugin(MicroEvent){\n\n\tpublic control_input\t\t\t: HTMLInputElement;\n\tpublic wrapper\t\t\t\t\t: HTMLElement;\n\tpublic dropdown\t\t\t\t\t: HTMLElement;\n\tpublic control\t\t\t\t\t: HTMLElement;\n\tpublic dropdown_content\t\t\t: HTMLElement;\n\tpublic focus_node\t\t\t\t: HTMLElement;\n\n\tpublic order\t\t\t\t\t: number = 0;\n\tpublic settings\t\t\t\t\t: TomSettings;\n\tpublic input\t\t\t\t\t: TomInput;\n\tpublic tabIndex\t\t\t\t\t: number;\n\tpublic is_select_tag\t\t\t: boolean;\n\tpublic rtl\t\t\t\t\t\t: boolean;\n\tprivate inputId\t\t\t\t\t: string;\n\n\tprivate _destroy\t\t\t\t!: () => void;\n\tpublic sifter\t\t\t\t\t: Sifter;\n\n\n\tpublic isOpen\t\t\t\t\t: boolean = false;\n\tpublic isDisabled\t\t\t\t: boolean = false;\n\tpublic isRequired\t\t\t\t: boolean;\n\tpublic isInvalid\t\t\t\t: boolean = false; // @deprecated 1.8\n\tpublic isValid\t\t\t\t\t: boolean = true;\n\tpublic isLocked\t\t\t\t\t: boolean = false;\n\tpublic isFocused\t\t\t\t: boolean = false;\n\tpublic isInputHidden\t\t\t: boolean = false;\n\tpublic isSetup\t\t\t\t\t: boolean = false;\n\tpublic ignoreFocus\t\t\t\t: boolean = false;\n\tpublic ignoreHover\t\t\t\t: boolean = false;\n\tpublic hasOptions\t\t\t\t: boolean = false;\n\tpublic currentResults\t\t\t?: ReturnType<Sifter['search']>;\n\tpublic lastValue\t\t\t\t: string = '';\n\tpublic caretPos\t\t\t\t\t: number = 0;\n\tpublic loading\t\t\t\t\t: number = 0;\n\tpublic loadedSearches\t\t\t: { [key: string]: boolean } = {};\n\n\tpublic activeOption\t\t\t\t: null|HTMLElement = null;\n\tpublic activeItems\t\t\t\t: TomItem[] = [];\n\n\tpublic optgroups\t\t\t\t: TomOptions = {};\n\tpublic options\t\t\t\t\t: TomOptions = {};\n\tpublic userOptions\t\t\t\t: {[key:string]:boolean} = {};\n\tpublic items\t\t\t\t\t: string[] = [];\n\n\n\n\tconstructor( input_arg: string|TomInput, user_settings:RecursivePartial<TomSettings> ){\n\t\tsuper();\n\n\t\tinstance_i++;\n\n\t\tvar dir;\n\t\tvar input\t\t\t\t= getDom( input_arg ) as TomInput;\n\n\t\tif( input.tomselect ){\n\t\t\tthrow new Error('Tom Select already initialized on this element');\n\t\t}\n\n\n\t\tinput.tomselect\t\t\t= this;\n\n\n\t\t// detect rtl environment\n\t\tvar computedStyle\t\t= window.getComputedStyle && window.getComputedStyle(input, null);\n\t\tdir\t\t\t\t\t\t= computedStyle.getPropertyValue('direction');\n\n\t\t// setup default state\n\t\tconst settings\t\t\t= getSettings( input, user_settings );\n\t\tthis.settings\t\t\t= settings;\n\t\tthis.input\t\t\t\t= input;\n\t\tthis.tabIndex\t\t\t= input.tabIndex || 0;\n\t\tthis.is_select_tag\t\t= input.tagName.toLowerCase() === 'select';\n\t\tthis.rtl\t\t\t\t= /rtl/i.test(dir);\n\t\tthis.inputId\t\t\t= getId(input, 'tomselect-'+instance_i);\n\t\tthis.isRequired\t\t\t= input.required;\n\n\n\t\t// search system\n\t\tthis.sifter = new Sifter(this.options, {diacritics: settings.diacritics});\n\n\t\t// option-dependent defaults\n\t\tsettings.mode = settings.mode || (settings.maxItems === 1 ? 'single' : 'multi');\n\t\tif (typeof settings.hideSelected !== 'boolean') {\n\t\t\tsettings.hideSelected = settings.mode === 'multi';\n\t\t}\n\n\t\tif( typeof settings.hidePlaceholder !== 'boolean' ){\n\t\t\tsettings.hidePlaceholder = settings.mode !== 'multi';\n\t\t}\n\n\t\t// set up createFilter callback\n\t\tvar filter = settings.createFilter;\n\t\tif( typeof filter !== 'function' ){\n\n\t\t\tif( typeof filter === 'string' ){\n\t\t\t\tfilter = new RegExp(filter);\n\t\t\t}\n\n\t\t\tif( filter instanceof RegExp ){\n\t\t\t\tsettings.createFilter = (input) => (filter as RegExp).test(input);\n\t\t\t}else{\n\t\t\t\tsettings.createFilter = (value) => {\n\t\t\t\t\treturn this.settings.duplicates || !this.options[value];\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\n\t\tthis.initializePlugins(settings.plugins);\n\t\tthis.setupCallbacks();\n\t\tthis.setupTemplates();\n\n\n\t\t// Create all elements\n\t\tconst wrapper\t\t\t= getDom('<div>');\n\t\tconst control\t\t\t= getDom('<div>');\n\t\tconst dropdown\t\t\t= this._render('dropdown');\n\t\tconst dropdown_content\t= getDom(`<div role=\"listbox\" tabindex=\"-1\">`);\n\n\t\tconst classes\t\t\t= this.input.getAttribute('class') || '';\n\t\tconst inputMode\t\t\t= settings.mode;\n\n\t\tvar control_input: HTMLInputElement;\n\n\n\t\taddClasses( wrapper, settings.wrapperClass, classes, inputMode);\n\n\n\t\taddClasses(control,settings.controlClass);\n\t\tappend( wrapper, control );\n\n\n\t\taddClasses(dropdown, settings.dropdownClass, inputMode);\n\t\tif( settings.copyClassesToDropdown ){\n\t\t\taddClasses( dropdown, classes);\n\t\t}\n\n\n\t\taddClasses(dropdown_content, settings.dropdownContentClass);\n\t\tappend( dropdown, dropdown_content );\n\n\t\tgetDom( settings.dropdownParent || wrapper ).appendChild( dropdown );\n\n\n\t\t// default controlInput\n\t\tif( isHtmlString(settings.controlInput) ){\n\t\t\tcontrol_input\t\t= getDom(settings.controlInput ) as HTMLInputElement;\n\n\t\t\t// set attributes\n\t\t\tvar attrs = ['autocorrect','autocapitalize','autocomplete'];\n\t\t\titerate(attrs,(attr:string) => {\n\t\t\t\tif( input.getAttribute(attr) ){\n\t\t\t\t\tsetAttr(control_input,{[attr]:input.getAttribute(attr)});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tcontrol_input.tabIndex = -1;\n\t\t\tcontrol.appendChild( control_input );\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t// dom element\n\t\t}else if( settings.controlInput ){\n\t\t\tcontrol_input\t\t= getDom( settings.controlInput ) as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t}else{\n\t\t\tcontrol_input\t\t= getDom('<input/>') as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control;\n\t\t}\n\n\t\tthis.wrapper\t\t\t= wrapper;\n\t\tthis.dropdown\t\t\t= dropdown;\n\t\tthis.dropdown_content\t= dropdown_content;\n\t\tthis.control \t\t\t= control;\n\t\tthis.control_input\t\t= control_input;\n\n\t\tthis.setup();\n\t}\n\n\t/**\n\t * set up event bindings.\n\t *\n\t */\n\tsetup(){\n\n\t\tconst self = this;\n\t\tconst settings\t\t\t\t= self.settings;\n\t\tconst control_input\t\t\t= self.control_input;\n\t\tconst dropdown\t\t\t\t= self.dropdown;\n\t\tconst dropdown_content\t\t= self.dropdown_content;\n\t\tconst wrapper\t\t\t\t= self.wrapper;\n\t\tconst control\t\t\t\t= self.control;\n\t\tconst input\t\t\t\t\t= self.input;\n\t\tconst focus_node\t\t\t= self.focus_node;\n\t\tconst passive_event\t\t\t= { passive: true };\n\t\tconst listboxId\t\t\t\t= self.inputId +'-ts-dropdown';\n\n\n\t\tsetAttr(dropdown_content,{\n\t\t\tid: listboxId\n\t\t});\n\n\t\tsetAttr(focus_node,{\n\t\t\trole:'combobox',\n\t\t\t'aria-haspopup':'listbox',\n\t\t\t'aria-expanded':'false',\n\t\t\t'aria-controls':listboxId\n\t\t});\n\n\t\tconst control_id\t= getId(focus_node,self.inputId + '-ts-control');\n\t\tconst query\t\t\t= \"label[for='\"+escapeQuery(self.inputId)+\"']\";\n\t\tconst label\t\t\t= document.querySelector(query);\n\t\tconst label_click\t= self.focus.bind(self);\n\t\tif( label ){\n\t\t\taddEvent(label,'click', label_click );\n\t\t\tsetAttr(label,{for:control_id});\n\t\t\tconst label_id = getId(label,self.inputId+'-ts-label');\n\t\t\tsetAttr(focus_node,{'aria-labelledby':label_id});\n\t\t\tsetAttr(dropdown_content,{'aria-labelledby':label_id});\n\t\t}\n\n\t\twrapper.style.width = input.style.width;\n\n\t\tif (self.plugins.names.length) {\n\t\t\tconst classes_plugins = 'plugin-' + self.plugins.names.join(' plugin-');\n\t\t\taddClasses( [wrapper,dropdown], classes_plugins);\n\t\t}\n\n\t\tif ((settings.maxItems === null || settings.maxItems > 1) && self.is_select_tag ){\n\t\t\tsetAttr(input,{multiple:'multiple'});\n\t\t}\n\n\t\tif (settings.placeholder) {\n\t\t\tsetAttr(control_input,{placeholder:settings.placeholder});\n\t\t}\n\n\t\t// if splitOn was not passed in, construct it from the delimiter to allow pasting universally\n\t\tif (!settings.splitOn && settings.delimiter) {\n\t\t\tsettings.splitOn = new RegExp('\\\\s*' + escape_regex(settings.delimiter) + '+\\\\s*');\n\t\t}\n\n\t\t// debounce user defined load() if loadThrottle > 0\n\t\t// after initializePlugins() so plugins can create/modify user defined loaders\n\t\tif( settings.load && settings.loadThrottle ){\n\t\t\tsettings.load = loadDebounce(settings.load,settings.loadThrottle)\n\t\t}\n\n\t\tself.control_input.type\t= input.type;\n\n\t\taddEvent(dropdown,'mousemove', () => {\n\t\t\tself.ignoreHover = false;\n\t\t});\n\n\t\taddEvent(dropdown,'mouseenter', (e) => {\n\n\t\t\tvar target_match = parentMatch(e.target as HTMLElement, '[data-selectable]', dropdown);\n\t\t\tif( target_match ) self.onOptionHover( e as MouseEvent, target_match );\n\n\t\t}, {capture:true});\n\n\t\t// clicking on an option should select it\n\t\taddEvent(dropdown,'click',(evt) => {\n\t\t\tconst option = parentMatch(evt.target as HTMLElement, '[data-selectable]');\n\t\t\tif( option ){\n\t\t\t\tself.onOptionSelect( evt as MouseEvent, option );\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\t\t});\n\n\t\taddEvent(control,'click', (evt) => {\n\n\t\t\tvar target_match = parentMatch( evt.target as HTMLElement, '[data-ts-item]', control);\n\t\t\tif( target_match && self.onItemSelect(evt as MouseEvent, target_match as TomItem) ){\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// retain focus (see control_input mousedown)\n\t\t\tif( control_input.value != '' ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself.onClick();\n\t\t\tpreventDefault(evt,true);\n\t\t});\n\n\n\t\t// keydown on focus_node for arrow_down/arrow_up\n\t\taddEvent(focus_node,'keydown',\t\t(e) => self.onKeyDown(e as KeyboardEvent) );\n\n\t\t// keypress and input/keyup\n\t\taddEvent(control_input,'keypress',\t(e) => self.onKeyPress(e as KeyboardEvent) );\n\t\taddEvent(control_input,'input',\t\t(e) => self.onInput(e as KeyboardEvent) );\n\t\taddEvent(focus_node,'blur', \t\t(e) => self.onBlur(e as FocusEvent) );\n\t\taddEvent(focus_node,'focus',\t\t(e) => self.onFocus(e as MouseEvent) );\n\t\taddEvent(control_input,'paste',\t\t(e) => self.onPaste(e as MouseEvent) );\n\n\n\t\tconst doc_mousedown = (evt:Event) => {\n\n\t\t\t// blur if target is outside of this instance\n\t\t\t// dropdown is not always inside wrapper\n\t\t\tconst target = evt.composedPath()[0];\n\t\t\tif( !wrapper.contains(target as HTMLElement) && !dropdown.contains(target as HTMLElement) ){\n\t\t\t\tif (self.isFocused) {\n\t\t\t\t\tself.blur();\n\t\t\t\t}\n\t\t\t\tself.inputState();\n\t\t\t\treturn;\n\t\t\t}\n\n\n\t\t\t// retain focus by preventing native handling. if the\n\t\t\t// event target is the input it should not be modified.\n\t\t\t// otherwise, text selection within the input won't work.\n\t\t\t// Fixes bug #212 which is no covered by tests\n\t\t\tif( target == control_input && self.isOpen ){\n\t\t\t\tevt.stopPropagation();\n\n\t\t\t// clicking anywhere in the control should not blur the control_input (which would close the dropdown)\n\t\t\t}else{\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\n\t\t};\n\n\t\tconst win_scroll = () => {\n\t\t\tif (self.isOpen) {\n\t\t\t\tself.positionDropdown();\n\t\t\t}\n\t\t};\n\n\n\t\taddEvent(document,'mousedown', doc_mousedown);\n\t\taddEvent(window,'scroll', win_scroll, passive_event);\n\t\taddEvent(window,'resize', win_scroll, passive_event);\n\n\t\tthis._destroy = () => {\n\t\t\tdocument.removeEventListener('mousedown',doc_mousedown);\n\t\t\twindow.removeEventListener('scroll',win_scroll);\n\t\t\twindow.removeEventListener('resize',win_scroll);\n\t\t\tif( label ) label.removeEventListener('click',label_click);\n\t\t};\n\n\t\t// store original html and tab index so that they can be\n\t\t// restored when the destroy() method is called.\n\t\tthis.revertSettings = {\n\t\t\tinnerHTML : input.innerHTML,\n\t\t\ttabIndex : input.tabIndex\n\t\t};\n\n\n\t\tinput.tabIndex = -1;\n\t\tinput.insertAdjacentElement('afterend', self.wrapper);\n\n\t\tself.sync(false);\n\t\tsettings.items = [];\n\t\tdelete settings.optgroups;\n\t\tdelete settings.options;\n\n\t\taddEvent(input,'invalid', () => {\n\t\t\tif( self.isValid ){\n\t\t\t\tself.isValid = false;\n\t\t\t\tself.isInvalid = true;\n\t\t\t\tself.refreshState();\n\t\t\t}\n\t\t});\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshItems();\n\t\tself.close(false);\n\t\tself.inputState();\n\t\tself.isSetup = true;\n\n\t\tif( input.disabled ){\n\t\t\tself.disable();\n\t\t}else{\n\t\t\tself.enable(); //sets tabIndex\n\t\t}\n\n\t\tself.on('change', this.onChange);\n\n\t\taddClasses(input,'tomselected','ts-hidden-accessible');\n\t\tself.trigger('initialize');\n\n\t\t// preload options\n\t\tif (settings.preload === true) {\n\t\t\tself.preload();\n\t\t}\n\n\t}\n\n\n\t/**\n\t * Register options and optgroups\n\t *\n\t */\n\tsetupOptions(options:TomOption[] = [], optgroups:TomOption[] = []){\n\n\t\t// build options table\n\t\tthis.addOptions(options);\n\n\n\t\t// build optgroup table\n\t\titerate( optgroups, (optgroup:TomOption) => {\n\t\t\tthis.registerOptionGroup(optgroup);\n\t\t});\n\t}\n\n\t/**\n\t * Sets up default rendering functions.\n\t */\n\tsetupTemplates() {\n\t\tvar self = this;\n\t\tvar field_label = self.settings.labelField;\n\t\tvar field_optgroup = self.settings.optgroupLabelField;\n\n\t\tvar templates = {\n\t\t\t'optgroup': (data:TomOption) => {\n\t\t\t\tlet optgroup = document.createElement('div');\n\t\t\t\toptgroup.className = 'optgroup';\n\t\t\t\toptgroup.appendChild(data.options);\n\t\t\t\treturn optgroup;\n\n\t\t\t},\n\t\t\t'optgroup_header': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"optgroup-header\">' + escape(data[field_optgroup]) + '</div>';\n\t\t\t},\n\t\t\t'option': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'item': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'option_create': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"create\">Add <strong>' + escape(data.input) + '</strong>&hellip;</div>';\n\t\t\t},\n\t\t\t'no_results':() => {\n\t\t\t\treturn '<div class=\"no-results\">No results found</div>';\n\t\t\t},\n\t\t\t'loading':() => {\n\t\t\t\treturn '<div class=\"spinner\"></div>';\n\t\t\t},\n\t\t\t'not_loading':() => {},\n\t\t\t'dropdown':() => {\n\t\t\t\treturn '<div></div>';\n\t\t\t}\n\t\t};\n\n\n\t\tself.settings.render = Object.assign({}, templates, self.settings.render);\n\t}\n\n\t/**\n\t * Maps fired events to callbacks provided\n\t * in the settings used when creating the control.\n\t */\n\tsetupCallbacks() {\n\t\tvar key, fn;\n\t\tvar callbacks:{[key:string]:string} = {\n\t\t\t'initialize'      : 'onInitialize',\n\t\t\t'change'          : 'onChange',\n\t\t\t'item_add'        : 'onItemAdd',\n\t\t\t'item_remove'     : 'onItemRemove',\n\t\t\t'item_select'     : 'onItemSelect',\n\t\t\t'clear'           : 'onClear',\n\t\t\t'option_add'      : 'onOptionAdd',\n\t\t\t'option_remove'   : 'onOptionRemove',\n\t\t\t'option_clear'    : 'onOptionClear',\n\t\t\t'optgroup_add'    : 'onOptionGroupAdd',\n\t\t\t'optgroup_remove' : 'onOptionGroupRemove',\n\t\t\t'optgroup_clear'  : 'onOptionGroupClear',\n\t\t\t'dropdown_open'   : 'onDropdownOpen',\n\t\t\t'dropdown_close'  : 'onDropdownClose',\n\t\t\t'type'            : 'onType',\n\t\t\t'load'            : 'onLoad',\n\t\t\t'focus'           : 'onFocus',\n\t\t\t'blur'            : 'onBlur'\n\t\t};\n\n\t\tfor (key in callbacks) {\n\n\t\t\tfn = this.settings[callbacks[key] as (keyof TomSettings)];\n\t\t\tif (fn) this.on(key, fn);\n\n\t\t}\n\t}\n\n\t/**\n\t * Sync the Tom Select instance with the original input or select\n\t *\n\t */\n\tsync(get_settings:boolean=true):void{\n\t\tconst self\t\t= this;\n\t\tconst settings\t= get_settings ? getSettings( self.input, {delimiter:self.settings.delimiter} as RecursivePartial<TomSettings> ) : self.settings;\n\n\t\tself.setupOptions(settings.options,settings.optgroups);\n\n\t\tself.setValue(settings.items||[],true); // silent prevents recursion\n\n\t\tself.lastQuery = null; // so updated options will be displayed in dropdown\n\t}\n\n\t/**\n\t * Triggered when the main control element\n\t * has a click event.\n\t *\n\t */\n\tonClick():void {\n\t\tvar self = this;\n\n\t\tif( self.activeItems.length > 0 ){\n\t\t\tself.clearActiveItems();\n\t\t\tself.focus();\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.isFocused && self.isOpen ){\n\t\t\tself.blur();\n\t\t} else {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * @deprecated v1.7\n\t *\n\t */\n\tonMouseDown():void {}\n\n\t/**\n\t * Triggered when the value of the control has been changed.\n\t * This should propagate the event to the original DOM\n\t * input / select element.\n\t */\n\tonChange() {\n\t\ttriggerEvent(this.input, 'input');\n\t\ttriggerEvent(this.input, 'change');\n\t}\n\n\t/**\n\t * Triggered on <input> paste.\n\t *\n\t */\n\tonPaste(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isInputHidden || self.isLocked ){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\t// If a regex or string is included, this will split the pasted\n\t\t// input and create Items for each separate value\n\t\tif( !self.settings.splitOn ){\n\t\t\treturn;\n\t\t}\n\n\t\t// Wait for pasted text to be recognized in value\n\t\tsetTimeout(() => {\n\t\t\tvar pastedText = self.inputValue();\n\t\t\tif( !pastedText.match(self.settings.splitOn)){\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tvar splitInput = pastedText.trim().split(self.settings.splitOn);\n\t\t\titerate( splitInput, (piece:string) => {\n\n\t\t\t\tconst hash = hash_key(piece);\n\t\t\t\tif( hash ){\n\t\t\t\t\tif( this.options[piece] ){\n\t\t\t\t\t\tself.addItem(piece);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tself.createItem(piece);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}, 0);\n\n\t}\n\n\t/**\n\t * Triggered on <input> keypress.\n\t *\n\t */\n\tonKeyPress(e:KeyboardEvent):void {\n\t\tvar self = this;\n\t\tif(self.isLocked){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t\tvar character = String.fromCharCode(e.keyCode || e.which);\n\t\tif (self.settings.create && self.settings.mode === 'multi' && character === self.settings.delimiter) {\n\t\t\tself.createItem();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keydown.\n\t *\n\t */\n\tonKeyDown(e:KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tself.ignoreHover = true;\n\n\t\tif (self.isLocked) {\n\t\t\tif (e.keyCode !== constants.KEY_TAB) {\n\t\t\t\tpreventDefault(e);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tswitch (e.keyCode) {\n\n\t\t\t// ctrl+A: select all\n\t\t\tcase constants.KEY_A:\n\t\t\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\t\t\tif( self.control_input.value == '' ){\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t\tself.selectAll();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\t// esc: close dropdown\n\t\t\tcase constants.KEY_ESC:\n\t\t\t\tif (self.isOpen) {\n\t\t\t\t\tpreventDefault(e,true);\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\n\t\t\t// down: open dropdown or move selection down\n\t\t\tcase constants.KEY_DOWN:\n\t\t\t\tif (!self.isOpen && self.hasOptions) {\n\t\t\t\t\tself.open();\n\t\t\t\t} else if (self.activeOption) {\n\t\t\t\t\tlet next = self.getAdjacent(self.activeOption, 1);\n\t\t\t\t\tif (next) self.setActiveOption(next);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// up: move selection up\n\t\t\tcase constants.KEY_UP:\n\t\t\t\tif (self.activeOption) {\n\t\t\t\t\tlet prev = self.getAdjacent(self.activeOption, -1);\n\t\t\t\t\tif (prev) self.setActiveOption(prev);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// return: select active option\n\t\t\tcase constants.KEY_RETURN:\n\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// if the option_create=null, the dropdown might be closed\n\t\t\t\t}else if (self.settings.create && self.createItem()) {\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// don't submit form when searching for a value\n\t\t\t\t}else if( document.activeElement == self.control_input && self.isOpen ){\n\t\t\t\t\tpreventDefault(e);\n\t\t\t\t}\n\n\t\t\t\treturn;\n\n\t\t\t// left: modifiy item selection to the left\n\t\t\tcase constants.KEY_LEFT:\n\t\t\t\tself.advanceSelection(-1, e);\n\t\t\t\treturn;\n\n\t\t\t// right: modifiy item selection to the right\n\t\t\tcase constants.KEY_RIGHT:\n\t\t\t\tself.advanceSelection(1, e);\n\t\t\t\treturn;\n\n\t\t\t// tab: select active option and/or create item\n\t\t\tcase constants.KEY_TAB:\n\n\t\t\t\tif( self.settings.selectOnTab ){\n\t\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\n\t\t\t\t\t\t// prevent default [tab] behaviour of jump to the next field\n\t\t\t\t\t\t// if select isFull, then the dropdown won't be open and [tab] will work normally\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t\tif (self.settings.create && self.createItem()) {\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn;\n\n\t\t\t// delete|backspace: delete items\n\t\t\tcase constants.KEY_BACKSPACE:\n\t\t\tcase constants.KEY_DELETE:\n\t\t\t\tself.deleteSelection(e);\n\t\t\t\treturn;\n\t\t}\n\n\t\t// don't enter text in the control_input when active items are selected\n\t\tif( self.isInputHidden && !isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\tpreventDefault(e);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keyup.\n\t *\n\t */\n\tonInput(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isLocked ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar value = self.inputValue();\n\t\tif (self.lastValue !== value) {\n\t\t\tself.lastValue = value;\n\n\t\t\tif( self.settings.shouldLoad.call(self,value) ){\n\t\t\t\tself.load(value);\n\t\t\t}\n\n\t\t\tself.refreshOptions();\n\t\t\tself.trigger('type', value);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered when the user rolls over\n\t * an option in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionHover( evt:MouseEvent|KeyboardEvent, option:HTMLElement ):void{\n\t\tif( this.ignoreHover ) return;\n\t\tthis.setActiveOption(option, false);\n\t}\n\n\t/**\n\t * Triggered on <input> focus.\n\t *\n\t */\n\tonFocus(e?:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\t\tvar wasFocused = self.isFocused;\n\n\t\tif (self.isDisabled) {\n\t\t\tself.blur();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\tif (self.ignoreFocus) return;\n\t\tself.isFocused = true;\n\t\tif( self.settings.preload === 'focus' ) self.preload();\n\n\t\tif (!wasFocused) self.trigger('focus');\n\n\t\tif (!self.activeItems.length) {\n\t\t\tself.showInput();\n\t\t\tself.refreshOptions(!!self.settings.openOnFocus);\n\t\t}\n\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Triggered on <input> blur.\n\t *\n\t */\n\tonBlur(e?:FocusEvent):void {\n\n\t\tif( document.hasFocus() === false ) return;\n\n\t\tvar self = this;\n\t\tif (!self.isFocused) return;\n\t\tself.isFocused = false;\n\t\tself.ignoreFocus = false;\n\n\t\tvar deactivate = () => {\n\t\t\tself.close();\n\t\t\tself.setActiveItem();\n\t\t\tself.setCaret(self.items.length);\n\t\t\tself.trigger('blur');\n\t\t};\n\n\t\tif (self.settings.create && self.settings.createOnBlur) {\n\t\t\tself.createItem(null, deactivate);\n\t\t} else {\n\t\t\tdeactivate();\n\t\t}\n\t}\n\n\n\t/**\n\t * Triggered when the user clicks on an option\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionSelect( evt:MouseEvent|KeyboardEvent, option:HTMLElement ){\n\t\tvar value, self = this;\n\n\n\t\t// should not be possible to trigger a option under a disabled optgroup\n\t\tif( option.parentElement && option.parentElement.matches('[data-disabled]') ){\n\t\t\treturn;\n\t\t}\n\n\n\t\tif( option.classList.contains('create') ){\n\t\t\tself.createItem(null, () => {\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tvalue = option.dataset.value;\n\t\t\tif (typeof value !== 'undefined') {\n\t\t\t\tself.lastQuery = null;\n\t\t\t\tself.addItem(value);\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( !self.settings.hideSelected && evt.type && /click/.test(evt.type) ){\n\t\t\t\t\tself.setActiveOption(option);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return true if the given option can be selected\n\t *\n\t */\n\tcanSelect(option:HTMLElement|null):boolean{\n\n\t\tif( this.isOpen && option && this.dropdown_content.contains(option) ) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Triggered when the user clicks on an item\n\t * that has been selected.\n\t *\n\t */\n\tonItemSelect( evt?:MouseEvent, item?:TomItem ):boolean{\n\t\tvar self = this;\n\n\t\tif( !self.isLocked && self.settings.mode === 'multi' ){\n\t\t\tpreventDefault(evt);\n\t\t\tself.setActiveItem(item, evt);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Determines whether or not to invoke\n\t * the user-provided option provider / loader\n\t *\n\t * Note, there is a subtle difference between\n\t * this.canLoad() and this.settings.shouldLoad();\n\t *\n\t *\t- settings.shouldLoad() is a user-input validator.\n\t *\tWhen false is returned, the not_loading template\n\t *\twill be added to the dropdown\n\t *\n\t *\t- canLoad() is lower level validator that checks\n\t * \tthe Tom Select instance. There is no inherent user\n\t *\tfeedback when canLoad returns false\n\t *\n\t */\n\tcanLoad(value:string):boolean{\n\n\t\tif( !this.settings.load ) return false;\n\t\tif( this.loadedSearches.hasOwnProperty(value) ) return false;\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Invokes the user-provided option provider / loader.\n\t *\n\t */\n\tload(value:string):void {\n\t\tconst self = this;\n\n\t\tif( !self.canLoad(value) ) return;\n\n\t\taddClasses(self.wrapper,self.settings.loadingClass);\n\t\tself.loading++;\n\n\t\tconst callback = self.loadCallback.bind(self);\n\t\tself.settings.load.call(self, value, callback);\n\t}\n\n\t/**\n\t * Invoked by the user-provided option provider\n\t *\n\t */\n\tloadCallback( options:TomOption[], optgroups:TomOption[] ):void{\n\t\tconst self = this;\n\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\tself.lastQuery = null;\n\n\t\tself.clearActiveOption(); // when new results load, focus should be on first option\n\t\tself.setupOptions(options,optgroups);\n\n\t\tself.refreshOptions(self.isFocused && !self.isInputHidden);\n\n\t\tif (!self.loading) {\n\t\t\tremoveClasses(self.wrapper,self.settings.loadingClass);\n\t\t}\n\n\t\tself.trigger('load', options, optgroups);\n\t}\n\n\tpreload():void{\n\t\tvar classList = this.wrapper.classList;\n\t\tif( classList.contains('preloaded') ) return;\n\t\tclassList.add('preloaded');\n\t\tthis.load('');\n\t}\n\n\n\t/**\n\t * Sets the input field of the control to the specified value.\n\t *\n\t */\n\tsetTextboxValue(value:string = '') {\n\t\tvar input = this.control_input;\n\t\tvar changed = input.value !== value;\n\t\tif (changed) {\n\t\t\tinput.value = value;\n\t\t\ttriggerEvent(input,'update');\n\t\t\tthis.lastValue = value;\n\t\t}\n\t}\n\n\t/**\n\t * Returns the value of the control. If multiple items\n\t * can be selected (e.g. <select multiple>), this returns\n\t * an array. If only one item can be selected, this\n\t * returns a string.\n\t *\n\t */\n\tgetValue():string|string[] {\n\n\t\tif( this.is_select_tag && this.input.hasAttribute('multiple')) {\n\t\t\treturn this.items;\n\t\t}\n\n\t\treturn this.items.join(this.settings.delimiter);\n\t}\n\n\t/**\n\t * Resets the selected items to the given value.\n\t *\n\t */\n\tsetValue( value:string|string[], silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change'];\n\n\t\tdebounce_events(this, events,() => {\n\t\t\tthis.clear(silent);\n\t\t\tthis.addItems(value, silent);\n\t\t});\n\t}\n\n\n\t/**\n\t * Resets the number of max items to the given value\n\t *\n\t */\n\tsetMaxItems(value:null|number){\n\t\tif(value === 0) value = null; //reset to unlimited items.\n\t\tthis.settings.maxItems = value;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Sets the selected item.\n\t *\n\t */\n\tsetActiveItem( item?:TomItem, e?:MouseEvent|KeyboardEvent ){\n\t\tvar self = this;\n\t\tvar eventName;\n\t\tvar i, begin, end, swap;\n\t\tvar last;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\t// clear the active selection\n\t\tif( !item ){\n\t\t\tself.clearActiveItems();\n\t\t\tif (self.isFocused) {\n\t\t\t\tself.showInput();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// modify selection\n\t\teventName = e && e.type.toLowerCase();\n\n\t\tif (eventName === 'click' && isKeyDown('shiftKey',e) && self.activeItems.length) {\n\t\t\tlast\t= self.getLastActive();\n\t\t\tbegin\t= Array.prototype.indexOf.call(self.control.children, last);\n\t\t\tend\t\t= Array.prototype.indexOf.call(self.control.children, item);\n\n\t\t\tif (begin > end) {\n\t\t\t\tswap  = begin;\n\t\t\t\tbegin = end;\n\t\t\t\tend   = swap;\n\t\t\t}\n\t\t\tfor (i = begin; i <= end; i++) {\n\t\t\t\titem = self.control.children[i] as TomItem;\n\t\t\t\tif (self.activeItems.indexOf(item) === -1) {\n\t\t\t\t\tself.setActiveItemClass(item);\n\t\t\t\t}\n\t\t\t}\n\t\t\tpreventDefault(e);\n\t\t} else if ((eventName === 'click' && isKeyDown(constants.KEY_SHORTCUT,e) ) || (eventName === 'keydown' && isKeyDown('shiftKey',e))) {\n\t\t\tif( item.classList.contains('active') ){\n\t\t\t\tself.removeActiveItem( item );\n\t\t\t} else {\n\t\t\t\tself.setActiveItemClass(item);\n\t\t\t}\n\t\t} else {\n\t\t\tself.clearActiveItems();\n\t\t\tself.setActiveItemClass(item);\n\t\t}\n\n\t\t// ensure control has focus\n\t\tself.hideInput();\n\t\tif (!self.isFocused) {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * Set the active and last-active classes\n\t *\n\t */\n\tsetActiveItemClass( item:TomItem ){\n\t\tconst self = this;\n\t\tconst last_active = self.control.querySelector('.last-active');\n\t\tif( last_active ) removeClasses(last_active as HTMLElement,'last-active');\n\n\t\taddClasses(item,'active last-active');\n\t\tself.trigger('item_select', item);\n\t\tif( self.activeItems.indexOf(item) == -1 ){\n\t\t\tself.activeItems.push( item );\n\t\t}\n\t}\n\n\t/**\n\t * Remove active item\n\t *\n\t */\n\tremoveActiveItem( item:TomItem ){\n\t\tvar idx = this.activeItems.indexOf(item);\n\t\tthis.activeItems.splice(idx, 1);\n\t\tremoveClasses(item,'active');\n\t}\n\n\t/**\n\t * Clears all the active items\n\t *\n\t */\n\tclearActiveItems(){\n\t\tremoveClasses(this.activeItems,'active');\n\t\tthis.activeItems = [];\n\t}\n\n\t/**\n\t * Sets the selected item in the dropdown menu\n\t * of available options.\n\t *\n\t */\n\tsetActiveOption( option:null|HTMLElement,scroll:boolean=true ):void{\n\n\t\tif( option === this.activeOption ){\n\t\t\treturn;\n\t\t}\n\n\t\tthis.clearActiveOption();\n\t\tif( !option ) return;\n\n\t\tthis.activeOption = option;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':option.getAttribute('id')});\n\t\tsetAttr(option,{'aria-selected':'true'});\n\t\taddClasses(option,'active');\n\t\tif( scroll ) this.scrollToOption(option);\n\t}\n\n\t/**\n\t * Sets the dropdown_content scrollTop to display the option\n\t *\n\t */\n\tscrollToOption( option:null|HTMLElement, behavior?:string ):void{\n\n\t\tif( !option ) return;\n\n\t\tconst content\t\t= this.dropdown_content;\n\t\tconst height_menu\t= content.clientHeight;\n\t\tconst scrollTop\t\t= content.scrollTop || 0;\n\t\tconst height_item\t= option.offsetHeight;\n\t\tconst y\t\t\t\t= option.getBoundingClientRect().top - content.getBoundingClientRect().top + scrollTop;\n\n\t\tif (y + height_item > height_menu + scrollTop) {\n\t\t\tthis.scroll(y - height_menu + height_item, behavior);\n\n\t\t} else if (y < scrollTop) {\n\t\t\tthis.scroll(y, behavior);\n\t\t}\n\t}\n\n\t/**\n\t * Scroll the dropdown to the given position\n\t *\n\t */\n\tscroll( scrollTop:number, behavior?:string ):void{\n\t\tconst content = this.dropdown_content;\n\t\tif( behavior ){\n\t\t\tcontent.style.scrollBehavior = behavior;\n\t\t}\n\t\tcontent.scrollTop = scrollTop;\n\t\tcontent.style.scrollBehavior = '';\n\t}\n\n\t/**\n\t * Clears the active option\n\t *\n\t */\n\tclearActiveOption(){\n\t\tif( this.activeOption ){\n\t\t\tremoveClasses(this.activeOption,'active');\n\t\t\tsetAttr(this.activeOption,{'aria-selected':null});\n\t\t}\n\t\tthis.activeOption = null;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':null});\n\t}\n\n\n\t/**\n\t * Selects all items (CTRL + A).\n\t */\n\tselectAll() {\n\t\tconst self = this;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\tconst activeItems = self.controlChildren();\n\n\t\tif( !activeItems.length ) return;\n\n\t\tself.hideInput();\n\t\tself.close();\n\n\t\tself.activeItems = activeItems;\n\t\titerate( activeItems, (item:TomItem) => {\n\t\t\tself.setActiveItemClass(item);\n\t\t});\n\n\t}\n\n\t/**\n\t * Determines if the control_input should be in a hidden or visible state\n\t *\n\t */\n\tinputState(){\n\t\tvar self = this;\n\n\t\tif( !self.control.contains(self.control_input) ) return;\n\n\t\tsetAttr(self.control_input,{placeholder:self.settings.placeholder});\n\n\t\tif( self.activeItems.length > 0 || (!self.isFocused && self.settings.hidePlaceholder && self.items.length > 0) ){\n\t\t\tself.setTextboxValue();\n\t\t\tself.isInputHidden = true;\n\n\t\t}else{\n\n\t\t\tif( self.settings.hidePlaceholder && self.items.length > 0 ){\n\t\t\t\tsetAttr(self.control_input,{placeholder:''});\n\t\t\t}\n\t\t\tself.isInputHidden = false;\n\t\t}\n\n\t\tself.wrapper.classList.toggle('input-hidden', self.isInputHidden );\n\t}\n\n\t/**\n\t * Hides the input element out of view, while\n\t * retaining its focus.\n\t * @deprecated 1.3\n\t */\n\thideInput() {\n\t\tthis.inputState();\n\t}\n\n\t/**\n\t * Restores input visibility.\n\t * @deprecated 1.3\n\t */\n\tshowInput() {\n\t\tthis.inputState();\n\t}\n\n\t/**\n\t * Get the input value\n\t */\n\tinputValue(){\n\t\treturn this.control_input.value.trim();\n\t}\n\n\t/**\n\t * Gives the control focus.\n\t */\n\tfocus() {\n\t\tvar self = this;\n\t\tif (self.isDisabled) return;\n\n\t\tself.ignoreFocus = true;\n\n\t\tif( self.control_input.offsetWidth ){\n\t\t\tself.control_input.focus();\n\t\t}else{\n\t\t\tself.focus_node.focus();\n\t\t}\n\n\t\tsetTimeout(() => {\n\t\t\tself.ignoreFocus = false;\n\t\t\tself.onFocus();\n\t\t}, 0);\n\t}\n\n\t/**\n\t * Forces the control out of focus.\n\t *\n\t */\n\tblur():void {\n\t\tthis.focus_node.blur();\n\t\tthis.onBlur();\n\t}\n\n\t/**\n\t * Returns a function that scores an object\n\t * to show how good of a match it is to the\n\t * provided query.\n\t *\n\t * @return {function}\n\t */\n\tgetScoreFunction(query:string) {\n\t\treturn this.sifter.getScoreFunction(query, this.getSearchOptions());\n\t}\n\n\t/**\n\t * Returns search options for sifter (the system\n\t * for scoring and sorting results).\n\t *\n\t * @see https://github.com/orchidjs/sifter.js\n\t * @return {object}\n\t */\n\tgetSearchOptions() {\n\t\tvar settings = this.settings;\n\t\tvar sort = settings.sortField;\n\t\tif (typeof settings.sortField === 'string') {\n\t\t\tsort = [{field: settings.sortField}];\n\t\t}\n\n\t\treturn {\n\t\t\tfields      : settings.searchField,\n\t\t\tconjunction : settings.searchConjunction,\n\t\t\tsort        : sort,\n\t\t\tnesting     : settings.nesting\n\t\t};\n\t}\n\n\t/**\n\t * Searches through available options and returns\n\t * a sorted array of matches.\n\t *\n\t */\n\tsearch(query:string) : ReturnType<Sifter['search']>{\n\t\tvar result, calculateScore;\n\t\tvar self     = this;\n\t\tvar options  = this.getSearchOptions();\n\n\t\t// validate user-provided result scoring function\n\t\tif ( self.settings.score ){\n\t\t\tcalculateScore = self.settings.score.call(self,query);\n\t\t\tif (typeof calculateScore !== 'function') {\n\t\t\t\tthrow new Error('Tom Select \"score\" setting must be a function that returns a function');\n\t\t\t}\n\t\t}\n\n\t\t// perform search\n\t\tif (query !== self.lastQuery) {\n\t\t\tself.lastQuery\t\t\t= query;\n\t\t\tresult\t\t\t\t\t= self.sifter.search(query, Object.assign(options, {score: calculateScore}));\n\t\t\tself.currentResults\t\t= result;\n\t\t} else {\n\t\t\tresult\t\t\t\t\t= Object.assign( {}, self.currentResults);\n\t\t}\n\n\t\t// filter out selected items\n\t\tif( self.settings.hideSelected ){\n\t\t\tresult.items = result.items.filter((item) => {\n\t\t\t\tlet hashed = hash_key(item.id);\n\t\t\t\treturn !(hashed && self.items.indexOf(hashed) !== -1 );\n\t\t\t});\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t/**\n\t * Refreshes the list of available options shown\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\trefreshOptions( triggerDropdown:boolean = true ){\n\t\tvar i, j, k, n, optgroup, optgroups, html:DocumentFragment, has_create_option, active_group;\n\t\tvar create;\n\t\tconst groups: {[key:string]:DocumentFragment} = {};\n\n\t\tconst groups_order:string[]\t= [];\n\t\tvar self\t\t\t\t\t= this;\n\t\tvar query\t\t\t\t\t= self.inputValue();\n\t\tconst same_query\t\t\t= query === self.lastQuery || (query == '' && self.lastQuery == null);\n\t\tvar results\t\t\t\t\t= self.search(query);\n\t\tvar active_option\t\t\t= null;\n\t\tvar show_dropdown\t\t\t= self.settings.shouldOpen || false;\n\t\tvar dropdown_content\t\t= self.dropdown_content;\n\n\n\t\tif( same_query ){\n\t\t\tactive_option\t\t\t= self.activeOption;\n\n\t\t\tif( active_option ){\n\t\t\t\tactive_group = active_option.closest('[data-group]') as HTMLElement;\n\t\t\t}\n\t\t}\n\n\t\t// build markup\n\t\tn = results.items.length;\n\t\tif (typeof self.settings.maxOptions === 'number') {\n\t\t\tn = Math.min(n, self.settings.maxOptions);\n\t\t}\n\n\t\tif( n > 0 ){\n\t\t\tshow_dropdown = true;\n\t\t}\n\n\t\t// render and group available options individually\n\t\tfor (i = 0; i < n; i++) {\n\n\t\t\t// get option dom element\n\t\t\tlet item\t\t\t= results.items[i];\n\t\t\tif( !item ) continue;\n\n\t\t\tlet opt_value\t\t= item.id;\n\t\t\tlet option\t\t\t= self.options[opt_value];\n\n\t\t\tif( option === undefined ) continue;\n\n\t\t\tlet opt_hash\t\t= get_hash(opt_value);\n\t\t\tlet option_el\t\t= self.getOption(opt_hash,true) as HTMLElement;\n\n\t\t\t// toggle 'selected' class\n\t\t\tif( !self.settings.hideSelected ){\n\t\t\t\toption_el.classList.toggle('selected', self.items.includes(opt_hash) );\n\t\t\t}\n\n\t\t\toptgroup    = option[self.settings.optgroupField] || '';\n\t\t\toptgroups   = Array.isArray(optgroup) ? optgroup : [optgroup];\n\n\t\t\tfor (j = 0, k = optgroups && optgroups.length; j < k; j++) {\n\t\t\t\toptgroup = optgroups[j];\n\t\t\t\tif (!self.optgroups.hasOwnProperty(optgroup)) {\n\t\t\t\t\toptgroup = '';\n\t\t\t\t}\n\n\t\t\t\tlet group_fragment = groups[optgroup];\n\t\t\t\tif( group_fragment === undefined ){\n\t\t\t\t\tgroup_fragment = document.createDocumentFragment();\n\t\t\t\t\tgroups_order.push(optgroup);\n\t\t\t\t}\n\n\t\t\t\t// nodes can only have one parent, so if the option is in mutple groups, we need a clone\n\t\t\t\tif( j > 0 ){\n\t\t\t\t\toption_el = option_el.cloneNode(true) as HTMLElement;\n\t\t\t\t\tsetAttr(option_el,{id: option.$id+'-clone-'+j,'aria-selected':null});\n\t\t\t\t\toption_el.classList.add('ts-cloned');\n\t\t\t\t\tremoveClasses(option_el,'active');\n\n\n\t\t\t\t\t// make sure we keep the activeOption in the same group\n\t\t\t\t\tif( self.activeOption && self.activeOption.dataset.value == opt_value ){\n\t\t\t\t\t\tif( active_group && active_group.dataset.group === optgroup.toString() ){\n\t\t\t\t\t\t\tactive_option = option_el;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tgroup_fragment.appendChild(option_el);\n\t\t\t\tgroups[optgroup] = group_fragment;\n\t\t\t}\n\t\t}\n\n\t\t// sort optgroups\n\t\tif( self.settings.lockOptgroupOrder ){\n\t\t\tgroups_order.sort((a, b) => {\n\t\t\t\tconst grp_a\t\t= self.optgroups[a];\n\t\t\t\tconst grp_b\t\t= self.optgroups[b];\n\t\t\t\tconst a_order\t= grp_a && grp_a.$order || 0;\n\t\t\t\tconst b_order\t= grp_b && grp_b.$order || 0;\n\t\t\t\treturn a_order - b_order;\n\t\t\t});\n\t\t}\n\n\t\t// render optgroup headers & join groups\n\t\thtml = document.createDocumentFragment();\n\t\titerate( groups_order, (optgroup:string) => {\n\n\t\t\tlet group_fragment = groups[optgroup];\n\n\t\t\tif( !group_fragment || !group_fragment.children.length ) return;\n\n\t\t\tlet group_heading = self.optgroups[optgroup];\n\n\t\t\tif( group_heading !== undefined ){\n\n\t\t\t\tlet group_options = document.createDocumentFragment();\n\t\t\t\tlet header = self.render('optgroup_header', group_heading);\n\t\t\t\tappend( group_options, header );\n\t\t\t\tappend( group_options, group_fragment );\n\n\t\t\t\tlet group_html = self.render('optgroup', {group:group_heading,options:group_options} );\n\n\t\t\t\tappend( html, group_html );\n\n\t\t\t} else {\n\t\t\t\tappend( html, group_fragment );\n\t\t\t}\n\t\t});\n\n\t\tdropdown_content.innerHTML = '';\n\t\tappend( dropdown_content, html );\n\n\t\t// highlight matching terms inline\n\t\tif (self.settings.highlight) {\n\t\t\tremoveHighlight( dropdown_content );\n\t\t\tif (results.query.length && results.tokens.length) {\n\t\t\t\titerate( results.tokens, (tok) => {\n\t\t\t\t\thighlight( dropdown_content, tok.regex);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// helper method for adding templates to dropdown\n\t\tvar add_template = (template:TomTemplateNames) => {\n\t\t\tlet content = self.render(template,{input:query});\n\t\t\tif( content ){\n\t\t\t\tshow_dropdown = true;\n\t\t\t\tdropdown_content.insertBefore(content, dropdown_content.firstChild);\n\t\t\t}\n\t\t\treturn content;\n\t\t};\n\n\n\t\t// add loading message\n\t\tif( self.loading ){\n\t\t\tadd_template('loading');\n\n\t\t// invalid query\n\t\t}else if( !self.settings.shouldLoad.call(self,query) ){\n\t\t\tadd_template('not_loading');\n\n\t\t// add no_results message\n\t\t}else if( results.items.length === 0 ){\n\t\t\tadd_template('no_results');\n\n\t\t}\n\n\n\n\t\t// add create option\n\t\thas_create_option = self.canCreate(query);\n\t\tif (has_create_option) {\n\t\t\tcreate = add_template('option_create');\n\t\t}\n\n\n\t\t// activate\n\t\tself.hasOptions = results.items.length > 0 || has_create_option;\n\t\tif( show_dropdown ){\n\n\t\t\tif (results.items.length > 0) {\n\n\t\t\t\tif( !active_option && self.settings.mode === 'single' && self.items[0] != undefined ){\n\t\t\t\t\tactive_option = self.getOption(self.items[0]);\n\t\t\t\t}\n\n\t\t\t\tif( !dropdown_content.contains(active_option)  ){\n\n\t\t\t\t\tlet active_index = 0;\n\t\t\t\t\tif( create && !self.settings.addPrecedence ){\n\t\t\t\t\t\tactive_index = 1;\n\t\t\t\t\t}\n\t\t\t\t\tactive_option = self.selectable()[active_index] as HTMLElement;\n\t\t\t\t}\n\n\t\t\t}else if( create ){\n\t\t\t\tactive_option = create;\n\t\t\t}\n\n\t\t\tif( triggerDropdown && !self.isOpen ){\n\t\t\t\tself.open();\n\t\t\t\tself.scrollToOption(active_option,'auto');\n\t\t\t}\n\t\t\tself.setActiveOption(active_option);\n\n\t\t}else{\n\t\t\tself.clearActiveOption();\n\t\t\tif( triggerDropdown && self.isOpen ){\n\t\t\t\tself.close(false); // if create_option=null, we want the dropdown to close but not reset the textbox value\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return list of selectable options\n\t *\n\t */\n\tselectable():NodeList{\n\t\treturn this.dropdown_content.querySelectorAll('[data-selectable]');\n\t}\n\n\n\n\t/**\n\t * Adds an available option. If it already exists,\n\t * nothing will happen. Note: this does not refresh\n\t * the options list dropdown (use `refreshOptions`\n\t * for that).\n\t *\n\t * Usage:\n\t *\n\t *   this.addOption(data)\n\t *\n\t */\n\taddOption( data:TomOption, user_created = false ):false|string {\n\t\tconst self = this;\n\n\t\t// @deprecated 1.7.7\n\t\t// use addOptions( array, user_created ) for adding multiple options\n\t\tif( Array.isArray(data) ){\n\t\t\tself.addOptions( data, user_created);\n\t\t\treturn false;\n\t\t}\n\n\t\tconst key = hash_key(data[self.settings.valueField]);\n\t\tif( key === null || self.options.hasOwnProperty(key) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tdata.$order\t\t\t= data.$order || ++self.order;\n\t\tdata.$id\t\t\t= self.inputId + '-opt-' + data.$order;\n\t\tself.options[key]\t= data;\n\t\tself.lastQuery\t\t= null;\n\n\t\tif( user_created ){\n\t\t\tself.userOptions[key] = user_created;\n\t\t\tself.trigger('option_add', key, data);\n\t\t}\n\n\t\treturn key;\n\t}\n\n\t/**\n\t * Add multiple options\n\t *\n\t */\n\taddOptions( data:TomOption[], user_created = false ):void{\n\t\titerate( data, (dat:TomOption) => {\n\t\t\tthis.addOption(dat, user_created);\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated 1.7.7\n\t */\n\tregisterOption( data:TomOption ):false|string {\n\t\treturn this.addOption(data);\n\t}\n\n\t/**\n\t * Registers an option group to the pool of option groups.\n\t *\n\t * @return {boolean|string}\n\t */\n\tregisterOptionGroup(data:TomOption) {\n\t\tvar key = hash_key(data[this.settings.optgroupValueField]);\n\n\t\tif ( key === null ) return false;\n\n\t\tdata.$order = data.$order || ++this.order;\n\t\tthis.optgroups[key] = data;\n\t\treturn key;\n\t}\n\n\t/**\n\t * Registers a new optgroup for options\n\t * to be bucketed into.\n\t *\n\t */\n\taddOptionGroup(id:string, data:TomOption) {\n\t\tvar hashed_id;\n\t\tdata[this.settings.optgroupValueField] = id;\n\n\t\tif( hashed_id = this.registerOptionGroup(data) ){\n\t\t\tthis.trigger('optgroup_add', hashed_id, data);\n\t\t}\n\t}\n\n\t/**\n\t * Removes an existing option group.\n\t *\n\t */\n\tremoveOptionGroup(id:string) {\n\t\tif (this.optgroups.hasOwnProperty(id)) {\n\t\t\tdelete this.optgroups[id];\n\t\t\tthis.clearCache();\n\t\t\tthis.trigger('optgroup_remove', id);\n\t\t}\n\t}\n\n\t/**\n\t * Clears all existing option groups.\n\t */\n\tclearOptionGroups() {\n\t\tthis.optgroups = {};\n\t\tthis.clearCache();\n\t\tthis.trigger('optgroup_clear');\n\t}\n\n\t/**\n\t * Updates an option available for selection. If\n\t * it is visible in the selected items or options\n\t * dropdown, it will be re-rendered automatically.\n\t *\n\t */\n\tupdateOption(value:string, data:TomOption) {\n\t\tconst self = this;\n\t\tvar item_new;\n\t\tvar index_item;\n\n\t\tconst value_old\t\t= hash_key(value);\n\t\tconst value_new\t\t= hash_key(data[self.settings.valueField]);\n\n\t\t// sanity checks\n\t\tif( value_old === null ) return;\n\n\t\tconst data_old\t\t= self.options[value_old];\n\n\t\tif( data_old == undefined ) return;\n\t\tif( typeof value_new !== 'string' ) throw new Error('Value must be set in option data');\n\n\n\t\tconst option\t\t= self.getOption(value_old);\n\t\tconst item\t\t\t= self.getItem(value_old);\n\n\n\t\tdata.$order = data.$order || data_old.$order;\n\t\tdelete self.options[value_old];\n\n\t\t// invalidate render cache\n\t\t// don't remove existing node yet, we'll remove it after replacing it\n\t\tself.uncacheValue(value_new);\n\n\t\tself.options[value_new] = data;\n\n\t\t// update the option if it's in the dropdown\n\t\tif( option ){\n\t\t\tif( self.dropdown_content.contains(option) ){\n\n\t\t\t\tconst option_new\t= self._render('option', data);\n\t\t\t\treplaceNode(option, option_new);\n\n\t\t\t\tif( self.activeOption === option ){\n\t\t\t\t\tself.setActiveOption(option_new);\n\t\t\t\t}\n\t\t\t}\n\t\t\toption.remove();\n\t\t}\n\n\t\t// update the item if we have one\n\t\tif( item ){\n\t\t\tindex_item = self.items.indexOf(value_old);\n\t\t\tif (index_item !== -1) {\n\t\t\t\tself.items.splice(index_item, 1, value_new);\n\t\t\t}\n\n\t\t\titem_new\t= self._render('item', data);\n\n\t\t\tif( item.classList.contains('active') ) addClasses(item_new,'active');\n\n\t\t\treplaceNode( item, item_new);\n\t\t}\n\n\t\t// invalidate last query because we might have updated the sortField\n\t\tself.lastQuery = null;\n\t}\n\n\t/**\n\t * Removes a single option.\n\t *\n\t */\n\tremoveOption(value:string, silent?:boolean):void {\n\t\tconst self = this;\n\t\tvalue = get_hash(value);\n\n\t\tself.uncacheValue(value);\n\n\t\tdelete self.userOptions[value];\n\t\tdelete self.options[value];\n\t\tself.lastQuery = null;\n\t\tself.trigger('option_remove', value);\n\t\tself.removeItem(value, silent);\n\t}\n\n\t/**\n\t * Clears all options.\n\t */\n\tclearOptions(filter?:TomClearFilter ) {\n\n\t\tconst boundFilter = (filter || this.clearFilter).bind(this);\n\n\t\tthis.loadedSearches\t\t= {};\n\t\tthis.userOptions\t\t= {};\n\t\tthis.clearCache();\n\n\t\tconst selected:TomOptions\t= {};\n\t\titerate(this.options,(option:TomOption,key:string)=>{\n\t\t\tif( boundFilter(option,key as string) ){\n\t\t\t\tselected[key] = option;\n\t\t\t}\n\t\t});\n\n\t\tthis.options = this.sifter.items = selected;\n\t\tthis.lastQuery = null;\n\t\tthis.trigger('option_clear');\n\t}\n\n\t/**\n\t * Used by clearOptions() to decide whether or not an option should be removed\n\t * Return true to keep an option, false to remove\n\t *\n\t */\n\tclearFilter(option:TomOption,value:string){\n\t\tif( this.items.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Returns the dom element of the option\n\t * matching the given value.\n\t *\n\t */\n\tgetOption(value:undefined|null|boolean|string|number, create:boolean=false):null|HTMLElement {\n\n\t\tconst hashed = hash_key(value);\n\t\tif( hashed === null ) return null;\n\n\t\tconst option = this.options[hashed];\n\t\tif( option != undefined ){\n\n\t\t\tif( option.$div ){\n\t\t\t\treturn option.$div;\n\t\t\t}\n\n\t\t\tif( create ){\n\t\t\t\treturn this._render('option', option);\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Returns the dom element of the next or previous dom element of the same type\n\t * Note: adjacent options may not be adjacent DOM elements (optgroups)\n\t *\n\t */\n\tgetAdjacent( option:null|HTMLElement, direction:number, type:string = 'option' ) : HTMLElement|null{\n\t\tvar self = this, all;\n\n\t\tif( !option ){\n\t\t\treturn null;\n\t\t}\n\n\t\tif( type == 'item' ){\n\t\t\tall\t\t\t= self.controlChildren();\n\t\t}else{\n\t\t\tall\t\t\t= self.dropdown_content.querySelectorAll('[data-selectable]');\n\t\t}\n\n\t\tfor( let i = 0; i < all.length; i++ ){\n\t\t\tif( all[i] != option ){\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif( direction > 0 ){\n\t\t\t\treturn all[i+1] as HTMLElement;\n\t\t\t}\n\n\t\t\treturn all[i-1] as HTMLElement;\n\t\t}\n\t\treturn null;\n\t}\n\n\n\t/**\n\t * Returns the dom element of the item\n\t * matching the given value.\n\t *\n\t */\n\tgetItem(item:string|TomItem|null):null|TomItem {\n\n\t\tif( typeof item == 'object' ){\n\t\t\treturn item;\n\t\t}\n\n\t\tvar value = hash_key(item);\n\t\treturn value !== null\n\t\t\t? this.control.querySelector(`[data-value=\"${addSlashes(value)}\"]`)\n\t\t\t: null;\n\t}\n\n\t/**\n\t * \"Selects\" multiple items at once. Adds them to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItems( values:string|string[], silent?:boolean ):void{\n\t\tvar self = this;\n\n\t\tvar items = Array.isArray(values) ? values : [values];\n\t\titems = items.filter(x => self.items.indexOf(x) === -1);\n\t\tconst last_item = items[items.length - 1];\n\t\titems.forEach(item => {\n\t\t\tself.isPending = (item !== last_item);\n\t\t\tself.addItem(item, silent);\n\t\t});\n\t}\n\n\t/**\n\t * \"Selects\" an item. Adds it to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItem( value:string, silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change','dropdown_close'];\n\n\t\tdebounce_events(this, events, () => {\n\t\t\tvar item, wasFull;\n\t\t\tconst self = this;\n\t\t \tconst inputMode = self.settings.mode;\n\t\t\tconst hashed = hash_key(value);\n\n\t\t\tif( hashed && self.items.indexOf(hashed) !== -1 ){\n\n\t\t\t\tif( inputMode === 'single' ){\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( inputMode === 'single' || !self.settings.duplicates ){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (hashed === null || !self.options.hasOwnProperty(hashed)) return;\n\t\t\tif (inputMode === 'single') self.clear(silent);\n\t\t\tif (inputMode === 'multi' && self.isFull()) return;\n\n\t\t\titem = self._render('item', self.options[hashed]);\n\n\t\t\tif( self.control.contains(item) ){ // duplicates\n\t\t\t\titem = item.cloneNode(true) as HTMLElement;\n\t\t\t}\n\n\t\t\twasFull = self.isFull();\n\t\t\tself.items.splice(self.caretPos, 0, hashed);\n\t\t\tself.insertAtCaret(item);\n\n\t\t\tif (self.isSetup) {\n\n\t\t\t\t// update menu / remove the option (if this is not one item being added as part of series)\n\t\t\t\tif( !self.isPending && self.settings.hideSelected ){\n\t\t\t\t\tlet option = self.getOption(hashed);\n\t\t\t\t\tlet next = self.getAdjacent(option, 1);\n\t\t\t\t\tif( next ){\n\t\t\t\t\t\tself.setActiveOption(next);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// refreshOptions after setActiveOption(),\n\t\t\t\t// otherwise setActiveOption() will be called by refreshOptions() with the wrong value\n\t\t\t\tif( !self.isPending && !self.settings.closeAfterSelect ){\n\t\t\t\t\tself.refreshOptions(self.isFocused && inputMode !== 'single');\n\t\t\t\t}\n\n\t\t\t\t// hide the menu if the maximum number of items have been selected or no options are left\n\t\t\t\tif( self.settings.closeAfterSelect != false && self.isFull() ){\n\t\t\t\t\tself.close();\n\t\t\t\t} else if (!self.isPending) {\n\t\t\t\t\tself.positionDropdown();\n\t\t\t\t}\n\n\t\t\t\tself.trigger('item_add', hashed, item);\n\n\t\t\t\tif (!self.isPending) {\n\t\t\t\t\tself.updateOriginalInput({silent: silent});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!self.isPending || (!wasFull && self.isFull())) {\n\t\t\t\tself.inputState();\n\t\t\t\tself.refreshState();\n\t\t\t}\n\n\t\t});\n\t}\n\n\t/**\n\t * Removes the selected item matching\n\t * the provided value.\n\t *\n\t */\n\tremoveItem( item:string|TomItem|null=null, silent?:boolean ){\n\t\tconst self\t\t= this;\n\t\titem\t\t\t= self.getItem(item);\n\n\t\tif( !item ) return;\n\n\t\tvar i,idx;\n\t\tconst value\t= item.dataset.value;\n\t\ti = nodeIndex(item);\n\n\t\titem.remove();\n\t\tif( item.classList.contains('active') ){\n\t\t\tidx = self.activeItems.indexOf(item);\n\t\t\tself.activeItems.splice(idx, 1);\n\t\t\tremoveClasses(item,'active');\n\t\t}\n\n\t\tself.items.splice(i, 1);\n\t\tself.lastQuery = null;\n\t\tif (!self.settings.persist && self.userOptions.hasOwnProperty(value)) {\n\t\t\tself.removeOption(value, silent);\n\t\t}\n\n\t\tif (i < self.caretPos) {\n\t\t\tself.setCaret(self.caretPos - 1);\n\t\t}\n\n\t\tself.updateOriginalInput({silent: silent});\n\t\tself.refreshState();\n\t\tself.positionDropdown();\n\t\tself.trigger('item_remove', value, item);\n\n\t}\n\n\t/**\n\t * Invokes the `create` method provided in the\n\t * TomSelect options that should provide the data\n\t * for the new item, given the user input.\n\t *\n\t * Once this completes, it will be added\n\t * to the item list.\n\t *\n\t */\n\tcreateItem( input:null|string=null, callback:TomCreateCallback = ()=>{} ):boolean{\n\n\t\t// triggerDropdown parameter @deprecated 2.1.1\n\t\tif( arguments.length === 3 ){\n\t\t\tcallback = arguments[2];\n\t\t}\n\t\tif( typeof callback != 'function' ){\n\t\t\tcallback = () => {};\n\t\t}\n\n\t\tvar self  = this;\n\t\tvar caret = self.caretPos;\n\t\tvar output;\n\t\tinput = input || self.inputValue();\n\n\t\tif (!self.canCreate(input)) {\n\t\t\tcallback();\n\t\t\treturn false;\n\t\t}\n\n\t\tself.lock();\n\n\t\tvar created = false;\n\t\tvar create = (data?:boolean|TomOption) => {\n\t\t\tself.unlock();\n\n\t\t\tif (!data || typeof data !== 'object') return callback();\n\t\t\tvar value = hash_key(data[self.settings.valueField]);\n\t\t\tif( typeof value !== 'string' ){\n\t\t\t\treturn callback();\n\t\t\t}\n\n\t\t\tself.setTextboxValue();\n\t\t\tself.addOption(data,true);\n\t\t\tself.setCaret(caret);\n\t\t\tself.addItem(value);\n\t\t\tcallback(data);\n\t\t\tcreated = true;\n\t\t};\n\n\t\tif( typeof self.settings.create === 'function' ){\n\t\t\toutput = self.settings.create.call(this, input, create);\n\t\t}else{\n\t\t\toutput = {\n\t\t\t\t[self.settings.labelField]: input,\n\t\t\t\t[self.settings.valueField]: input,\n\t\t\t};\n\t\t}\n\n\t\tif( !created ){\n\t\t\tcreate(output);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Re-renders the selected item lists.\n\t */\n\trefreshItems() {\n\t\tvar self = this;\n\t\tself.lastQuery = null;\n\n\t\tif (self.isSetup) {\n\t\t\tself.addItems(self.items);\n\t\t}\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Updates all state-dependent attributes\n\t * and CSS classes.\n\t */\n\trefreshState() {\n\t\tconst self     = this;\n\n\t\tself.refreshValidityState();\n\n\t\tconst isFull\t= self.isFull();\n\t\tconst isLocked\t= self.isLocked;\n\n\t\tself.wrapper.classList.toggle('rtl',self.rtl);\n\n\n\t\tconst wrap_classList = self.wrapper.classList;\n\n\t\twrap_classList.toggle('focus', self.isFocused)\n\t\twrap_classList.toggle('disabled', self.isDisabled)\n\t\twrap_classList.toggle('required', self.isRequired)\n\t\twrap_classList.toggle('invalid', !self.isValid)\n\t\twrap_classList.toggle('locked', isLocked)\n\t\twrap_classList.toggle('full', isFull)\n\t\twrap_classList.toggle('input-active', self.isFocused && !self.isInputHidden)\n\t\twrap_classList.toggle('dropdown-active', self.isOpen)\n\t\twrap_classList.toggle('has-options', isEmptyObject(self.options) )\n\t\twrap_classList.toggle('has-items', self.items.length > 0);\n\n\t}\n\n\n\t/**\n\t * Update the `required` attribute of both input and control input.\n\t *\n\t * The `required` property needs to be activated on the control input\n\t * for the error to be displayed at the right place. `required` also\n\t * needs to be temporarily deactivated on the input since the input is\n\t * hidden and can't show errors.\n\t */\n\trefreshValidityState() {\n\t\tvar self = this;\n\n\t\tif( !self.input.validity ){\n\t\t\treturn;\n\t\t}\n\n\t\tself.isValid = self.input.validity.valid;\n\t\tself.isInvalid = !self.isValid;\n\t}\n\n\t/**\n\t * Determines whether or not more items can be added\n\t * to the control without exceeding the user-defined maximum.\n\t *\n\t * @returns {boolean}\n\t */\n\tisFull() {\n\t\treturn this.settings.maxItems !== null && this.items.length >= this.settings.maxItems;\n\t}\n\n\t/**\n\t * Refreshes the original <select> or <input>\n\t * element to reflect the current state.\n\t *\n\t */\n\tupdateOriginalInput( opts:TomArgObject = {} ){\n\t\tconst self = this;\n\t\tvar option, label;\n\n\t\tconst empty_option = self.input.querySelector('option[value=\"\"]') as HTMLOptionElement;\n\n\t\tif( self.is_select_tag ){\n\n\t\t\tconst selected:HTMLOptionElement[]\t\t= [];\n\t\t\tconst has_selected:number\t\t\t\t= self.input.querySelectorAll('option:checked').length;\n\n\t\t\tfunction AddSelected(option_el:HTMLOptionElement|null, value:string, label:string):HTMLOptionElement{\n\n\t\t\t\tif( !option_el ){\n\t\t\t\t\toption_el = getDom('<option value=\"' + escape_html(value) + '\">' + escape_html(label) + '</option>') as HTMLOptionElement;\n\t\t\t\t}\n\n\t\t\t\t// don't move empty option from top of list\n\t\t\t\t// fixes bug in firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1725293\n\t\t\t\tif( option_el != empty_option ){\n\t\t\t\t\tself.input.append(option_el);\n\t\t\t\t}\n\n\t\t\t\tselected.push(option_el);\n\n\t\t\t\t// marking empty option as selected can break validation\n\t\t\t\t// fixes https://github.com/orchidjs/tom-select/issues/303\n\t\t\t\tif( option_el != empty_option || has_selected > 0 ){\n\t\t\t\t\toption_el.selected = true;\n\t\t\t\t}\n\n\t\t\t\treturn option_el;\n\t\t\t}\n\n\t\t\t// unselect all selected options\n\t\t\tself.input.querySelectorAll('option:checked').forEach((option_el:Element) => {\n\t\t\t\t(<HTMLOptionElement>option_el).selected = false;\n\t\t\t});\n\n\n\t\t\t// nothing selected?\n\t\t\tif( self.items.length == 0 && self.settings.mode == 'single' ){\n\n\t\t\t\tAddSelected(empty_option, \"\", \"\");\n\n\t\t\t// order selected <option> tags for values in self.items\n\t\t\t}else{\n\n\t\t\t\tself.items.forEach((value)=>{\n\t\t\t\t\toption\t\t\t= self.options[value]!;\n\t\t\t\t\tlabel\t\t\t= option[self.settings.labelField] || '';\n\n\t\t\t\t\tif( selected.includes(option.$option) ){\n\t\t\t\t\t\tconst reuse_opt = self.input.querySelector(`option[value=\"${addSlashes(value)}\"]:not(:checked)`) as HTMLOptionElement;\n\t\t\t\t\t\tAddSelected(reuse_opt, value, label);\n\t\t\t\t\t}else{\n\t\t\t\t\t\toption.$option\t= AddSelected(option.$option, value, label);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t}\n\n\t\t} else {\n\t\t\tself.input.value = self.getValue() as string;\n\t\t}\n\n\t\tif (self.isSetup) {\n\t\t\tif (!opts.silent) {\n\t\t\t\tself.trigger('change', self.getValue() );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Shows the autocomplete dropdown containing\n\t * the available options.\n\t */\n\topen() {\n\t\tvar self = this;\n\n\t\tif (self.isLocked || self.isOpen || (self.settings.mode === 'multi' && self.isFull())) return;\n\t\tself.isOpen = true;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'true'});\n\t\tself.refreshState();\n\t\tapplyCSS(self.dropdown,{visibility: 'hidden', display: 'block'});\n\t\tself.positionDropdown();\n\t\tapplyCSS(self.dropdown,{visibility: 'visible', display: 'block'});\n\t\tself.focus();\n\t\tself.trigger('dropdown_open', self.dropdown);\n\t}\n\n\t/**\n\t * Closes the autocomplete dropdown menu.\n\t */\n\tclose(setTextboxValue=true) {\n\t\tvar self = this;\n\t\tvar trigger = self.isOpen;\n\n\t\tif( setTextboxValue ){\n\n\t\t\t// before blur() to prevent form onchange event\n\t\t\tself.setTextboxValue();\n\n\t\t\tif (self.settings.mode === 'single' && self.items.length) {\n\t\t\t\tself.hideInput();\n\t\t\t}\n\t\t}\n\n\t\tself.isOpen = false;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'false'});\n\t\tapplyCSS(self.dropdown,{display: 'none'});\n\t\tif( self.settings.hideSelected ){\n\t\t\tself.clearActiveOption();\n\t\t}\n\t\tself.refreshState();\n\n\t\tif (trigger) self.trigger('dropdown_close', self.dropdown);\n\t}\n\n\t/**\n\t * Calculates and applies the appropriate\n\t * position of the dropdown if dropdownParent = 'body'.\n\t * Otherwise, position is determined by css\n\t */\n\tpositionDropdown(){\n\n\t\tif( this.settings.dropdownParent !== 'body' ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar context\t\t\t= this.control;\n\t\tvar rect\t\t\t= context.getBoundingClientRect();\n\t\tvar top\t\t\t\t= context.offsetHeight + rect.top  + window.scrollY;\n\t\tvar left\t\t\t= rect.left + window.scrollX;\n\n\n\t\tapplyCSS(this.dropdown,{\n\t\t\twidth : rect.width + 'px',\n\t\t\ttop   : top + 'px',\n\t\t\tleft  : left + 'px'\n\t\t});\n\n\t}\n\n\t/**\n\t * Resets / clears all selected items\n\t * from the control.\n\t *\n\t */\n\tclear(silent?:boolean) {\n\t\tvar self = this;\n\n\t\tif (!self.items.length) return;\n\n\t\tvar items = self.controlChildren();\n\t\titerate(items,(item:TomItem)=>{\n\t\t\tself.removeItem(item,true);\n\t\t});\n\n\t\tself.showInput();\n\t\tif( !silent ) self.updateOriginalInput();\n\t\tself.trigger('clear');\n\t}\n\n\t/**\n\t * A helper method for inserting an element\n\t * at the current caret position.\n\t *\n\t */\n\tinsertAtCaret(el:HTMLElement) {\n\t\tconst self\t\t= this;\n\t\tconst caret\t\t= self.caretPos;\n\t\tconst target\t= self.control;\n\n\t\ttarget.insertBefore(el, target.children[caret] || null);\n\t\tself.setCaret(caret + 1);\n\t}\n\n\t/**\n\t * Removes the current selected item(s).\n\t *\n\t */\n\tdeleteSelection(e:KeyboardEvent):boolean {\n\t\tvar direction, selection, caret, tail;\n\t\tvar self = this;\n\n\t\tdirection = (e && e.keyCode === constants.KEY_BACKSPACE) ? -1 : 1;\n\t\tselection = getSelection(self.control_input);\n\n\n\t\t// determine items that will be removed\n\t\tconst rm_items:TomItem[]\t= [];\n\n\t\tif (self.activeItems.length) {\n\n\t\t\ttail = getTail(self.activeItems, direction);\n\t\t\tcaret = nodeIndex(tail);\n\n\t\t\tif (direction > 0) { caret++; }\n\n\t\t\titerate(self.activeItems, (item:TomItem) => rm_items.push(item) );\n\n\t\t} else if ((self.isFocused || self.settings.mode === 'single') && self.items.length) {\n\t\t\tconst items = self.controlChildren();\n\t\t\tlet rm_item;\n\t\t\tif( direction < 0 && selection.start === 0 && selection.length === 0 ){\n\t\t\t\trm_item = items[self.caretPos - 1];\n\n\t\t\t}else if( direction > 0 && selection.start === self.inputValue().length ){\n\t\t\t\trm_item = items[self.caretPos];\n\t\t\t}\n\n\t\t\tif( rm_item !== undefined ){\n\t\t\t\trm_items.push( rm_item );\n\t\t\t}\n\t\t}\n\n\t\tif( !self.shouldDelete(rm_items,e) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tpreventDefault(e,true);\n\n\t\t// perform removal\n\t\tif (typeof caret !== 'undefined') {\n\t\t\tself.setCaret(caret);\n\t\t}\n\n\t\twhile( rm_items.length ){\n\t\t\tself.removeItem(rm_items.pop());\n\t\t}\n\n\t\tself.showInput();\n\t\tself.positionDropdown();\n\t\tself.refreshOptions(false);\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Return true if the items should be deleted\n\t */\n\tshouldDelete(items:TomItem[],evt:MouseEvent|KeyboardEvent){\n\n\t\tconst values = items.map(item => item.dataset.value);\n\n\t\t// allow the callback to abort\n\t\tif( !values.length || (typeof this.settings.onDelete === 'function' && this.settings.onDelete(values,evt) === false) ){\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Selects the previous / next item (depending on the `direction` argument).\n\t *\n\t * > 0 - right\n\t * < 0 - left\n\t *\n\t */\n\tadvanceSelection(direction:number, e?:MouseEvent|KeyboardEvent) {\n\t\tvar last_active, adjacent, self = this;\n\n\t\tif (self.rtl) direction *= -1;\n\t\tif( self.inputValue().length ) return;\n\n\n\t\t// add or remove to active items\n\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) || isKeyDown('shiftKey',e) ){\n\n\t\t\tlast_active\t\t\t= self.getLastActive(direction);\n\t\t\tif( last_active ){\n\n\t\t\t\tif( !last_active.classList.contains('active') ){\n\t\t\t\t\tadjacent\t\t\t= last_active;\n\t\t\t\t}else{\n\t\t\t\t\tadjacent\t\t\t= self.getAdjacent(last_active,direction,'item');\n\t\t\t\t}\n\n\t\t\t// if no active item, get items adjacent to the control input\n\t\t\t}else if( direction > 0 ){\n\t\t\t\tadjacent\t\t\t= self.control_input.nextElementSibling;\n\t\t\t}else{\n\t\t\t\tadjacent\t\t\t= self.control_input.previousElementSibling;\n\t\t\t}\n\n\n\t\t\tif( adjacent ){\n\t\t\t\tif( adjacent.classList.contains('active') ){\n\t\t\t\t\tself.removeActiveItem(last_active);\n\t\t\t\t}\n\t\t\t\tself.setActiveItemClass(adjacent); // mark as last_active !! after removeActiveItem() on last_active\n\t\t\t}\n\n\t\t// move caret to the left or right\n\t\t}else{\n\t\t\tself.moveCaret(direction);\n\t\t}\n\t}\n\n\tmoveCaret(direction:number){}\n\n\t/**\n\t * Get the last active item\n\t *\n\t */\n\tgetLastActive(direction?:number){\n\n\t\tlet last_active = this.control.querySelector('.last-active');\n\t\tif( last_active ){\n\t\t\treturn last_active;\n\t\t}\n\n\n\t\tvar result = this.control.querySelectorAll('.active');\n\t\tif( result ){\n\t\t\treturn getTail(result,direction);\n\t\t}\n\t}\n\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tsetCaret(new_pos:number) {\n\t\tthis.caretPos = this.items.length;\n\t}\n\n\t/**\n\t * Return list of item dom elements\n\t *\n\t */\n\tcontrolChildren():TomItem[]{\n\t\treturn Array.from( this.control.querySelectorAll('[data-ts-item]') ) as TomItem[];\n\t}\n\n\t/**\n\t * Disables user input on the control. Used while\n\t * items are being asynchronously created.\n\t */\n\tlock() {\n\t\tthis.isLocked = true;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Re-enables user input on the control.\n\t */\n\tunlock() {\n\t\tthis.isLocked = false;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Disables user input on the control completely.\n\t * While disabled, it cannot receive focus.\n\t */\n\tdisable() {\n\t\tvar self = this;\n\t\tself.input.disabled\t\t\t\t= true;\n\t\tself.control_input.disabled\t\t= true;\n\t\tself.focus_node.tabIndex\t\t= -1;\n\t\tself.isDisabled\t\t\t\t\t= true;\n\t\tthis.close();\n\t\tself.lock();\n\t}\n\n\t/**\n\t * Enables the control so that it can respond\n\t * to focus and user input.\n\t */\n\tenable() {\n\t\tvar self = this;\n\t\tself.input.disabled\t\t\t\t= false;\n\t\tself.control_input.disabled\t\t= false;\n\t\tself.focus_node.tabIndex\t\t= self.tabIndex;\n\t\tself.isDisabled\t\t\t\t\t= false;\n\t\tself.unlock();\n\t}\n\n\t/**\n\t * Completely destroys the control and\n\t * unbinds all event listeners so that it can\n\t * be garbage collected.\n\t */\n\tdestroy() {\n\t\tvar self = this;\n\t\tvar revertSettings = self.revertSettings;\n\n\t\tself.trigger('destroy');\n\t\tself.off();\n\t\tself.wrapper.remove();\n\t\tself.dropdown.remove();\n\n\t\tself.input.innerHTML = revertSettings.innerHTML;\n\t\tself.input.tabIndex = revertSettings.tabIndex;\n\n\t\tremoveClasses(self.input,'tomselected','ts-hidden-accessible');\n\n\t\tself._destroy();\n\n\t\tdelete self.input.tomselect;\n\t}\n\n\t/**\n\t * A helper method for rendering \"item\" and\n\t * \"option\" templates, given the data.\n\t *\n\t */\n\trender( templateName:TomTemplateNames, data?:any ):null|HTMLElement{\n\t\tvar id, html;\n\t\tconst self = this;\n\n\t\tif( typeof this.settings.render[templateName] !== 'function' ){\n\t\t\treturn null;\n\t\t}\n\n\t\t// render markup\n\t\thtml = self.settings.render[templateName].call(this, data, escape_html);\n\n\t\tif( !html ){\n\t\t\treturn null;\n\t\t}\n\n\t\thtml = getDom( html );\n\n\t\t// add mandatory attributes\n\t\tif (templateName === 'option' || templateName === 'option_create') {\n\n\t\t\tif( data[self.settings.disabledField] ){\n\t\t\t\tsetAttr(html,{'aria-disabled':'true'});\n\t\t\t}else{\n\t\t\t\tsetAttr(html,{'data-selectable': ''});\n\t\t\t}\n\n\t\t}else if (templateName === 'optgroup') {\n\t\t\tid = data.group[self.settings.optgroupValueField];\n\t\t\tsetAttr(html,{'data-group': id});\n\t\t\tif(data.group[self.settings.disabledField]) {\n\t\t\t\tsetAttr(html,{'data-disabled': ''});\n\t\t\t}\n\t\t}\n\n\t\tif (templateName === 'option' || templateName === 'item') {\n\t\t\tconst value\t= get_hash(data[self.settings.valueField]);\n\t\t\tsetAttr(html,{'data-value': value });\n\n\n\t\t\t// make sure we have some classes if a template is overwritten\n\t\t\tif( templateName === 'item' ){\n\t\t\t\taddClasses(html,self.settings.itemClass);\n\t\t\t\tsetAttr(html,{'data-ts-item':''});\n\t\t\t}else{\n\t\t\t\taddClasses(html,self.settings.optionClass);\n\t\t\t\tsetAttr(html,{\n\t\t\t\t\trole:'option',\n\t\t\t\t\tid:data.$id\n\t\t\t\t});\n\n\t\t\t\t// update cache\n\t\t\t\tdata.$div = html;\n\t\t\t\tself.options[value] = data;\n\t\t\t}\n\n\n\t\t}\n\n\t\treturn html;\n\n\t}\n\n\n\t/**\n\t * Type guarded rendering\n\t *\n\t */\n\t_render( templateName:TomTemplateNames, data?:any ):HTMLElement{\n\t\tconst html = this.render(templateName, data);\n\n\t\tif( html == null ){\n\t\t\tthrow 'HTMLElement expected';\n\t\t}\n\t\treturn html;\n\t}\n\n\n\t/**\n\t * Clears the render cache for a template. If\n\t * no template is given, clears all render\n\t * caches.\n\t *\n\t */\n\tclearCache():void{\n\n\t\titerate(this.options, (option:TomOption)=>{\n\t\t\tif( option.$div ){\n\t\t\t\toption.$div.remove();\n\t\t\t\tdelete option.$div;\n\t\t\t}\n\t\t});\n\n\t}\n\n\t/**\n\t * Removes a value from item and option caches\n\t *\n\t */\n\tuncacheValue(value:string){\n\n\t\tconst option_el\t\t\t= this.getOption(value);\n\t\tif( option_el ) option_el.remove();\n\n\t}\n\n\t/**\n\t * Determines whether or not to display the\n\t * create item prompt, given a user input.\n\t *\n\t */\n\tcanCreate( input:string ):boolean {\n\t\treturn this.settings.create && (input.length > 0) && (this.settings.createFilter as TomCreateFilter ).call(this, input);\n\t}\n\n\n\t/**\n\t * Wraps this.`method` so that `new_fn` can be invoked 'before', 'after', or 'instead' of the original method\n\t *\n\t * this.hook('instead','onKeyDown',function( arg1, arg2 ...){\n\t *\n\t * });\n\t */\n\thook( when:string, method:string, new_fn:any ){\n\t\tvar self = this;\n\t\tvar orig_method = self[method];\n\n\n\t\tself[method] = function(){\n\t\t\tvar result, result_new;\n\n\t\t\tif( when === 'after' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\tresult_new = new_fn.apply(self, arguments );\n\n\t\t\tif( when === 'instead' ){\n\t\t\t\treturn result_new;\n\t\t\t}\n\n\t\t\tif( when === 'before' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t}\n\n};\n"], "names": ["forEvents", "events", "callback", "split", "for<PERSON>ach", "event", "MicroEvent", "constructor", "_events", "on", "fct", "event_array", "push", "off", "n", "arguments", "length", "undefined", "splice", "indexOf", "trigger", "args", "self", "apply", "MicroPlugin", "Interface", "plugins", "names", "settings", "requested", "loaded", "define", "name", "fn", "initializePlugins", "key", "queue", "Array", "isArray", "plugin", "options", "hasOwnProperty", "shift", "require", "loadPlugin", "Error", "iterate", "object", "getDom", "query", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "trim", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "escape<PERSON><PERSON>y", "replace", "triggerEvent", "dom_el", "event_name", "createEvent", "initEvent", "dispatchEvent", "applyCSS", "css", "Object", "assign", "style", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "el", "cls", "classList", "add", "removeClasses", "remove", "_classes", "concat", "filter", "Boolean", "parentMatch", "target", "selector", "wrapper", "contains", "matches", "parentNode", "getTail", "list", "direction", "isEmptyObject", "obj", "keys", "nodeIndex", "amongst", "nodeName", "i", "previousElementSibling", "setAttr", "attrs", "val", "attr", "removeAttribute", "setAttribute", "replaceNode", "existing", "replacement", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "element", "regex", "RegExp", "highlightText", "node", "match", "data", "spannode", "className", "middlebit", "splitText", "index", "middle<PERSON>lone", "cloneNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "childNodes", "test", "tagName", "from", "highlightRecursive", "removeHighlight", "elements", "querySelectorAll", "prototype", "call", "parent", "normalize", "KEY_A", "KEY_RETURN", "KEY_ESC", "KEY_LEFT", "KEY_UP", "KEY_RIGHT", "KEY_DOWN", "KEY_BACKSPACE", "KEY_DELETE", "KEY_TAB", "IS_MAC", "navigator", "userAgent", "KEY_SHORTCUT", "optgroups", "delimiter", "splitOn", "persist", "diacritics", "create", "createOnBlur", "createFilter", "openOnFocus", "shouldOpen", "maxOptions", "maxItems", "hideSelected", "duplicates", "addPrecedence", "selectOnTab", "preload", "allowEmptyOption", "loadThrottle", "loadingClass", "dataAttr", "optgroupField", "valueField", "labelField", "<PERSON><PERSON><PERSON>", "optgroupLabelField", "optgroupValueField", "lockOptgroupOrder", "sortField", "searchField", "searchConjunction", "mode", "wrapperClass", "controlClass", "dropdownClass", "dropdownContentClass", "itemClass", "optionClass", "dropdownParent", "controlInput", "copyClassesToDropdown", "placeholder", "hidePlaceholder", "shouldLoad", "render", "hash_key", "value", "get_hash", "escape_html", "str", "loadDebounce", "delay", "timeout", "loading", "Math", "max", "clearTimeout", "setTimeout", "loadedSearches", "debounce_events", "types", "type", "event_args", "getSelection", "input", "start", "selectionStart", "selectionEnd", "preventDefault", "evt", "stop", "stopPropagation", "addEvent", "addEventListener", "isKeyDown", "key_name", "count", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "getId", "id", "existing_id", "getAttribute", "addSlashes", "append", "getSettings", "settings_user", "defaults", "attr_data", "field_label", "field_value", "field_disabled", "field_optgroup", "field_optgroup_label", "field_optgroup_value", "tag_name", "toLowerCase", "option", "textContent", "settings_element", "items", "init_select", "optionsMap", "group_count", "readData", "dataset", "json", "JSON", "parse", "addOption", "group", "arr", "option_data", "disabled", "$option", "selected", "addGroup", "optgroup", "optgroup_data", "children", "hasAttribute", "child", "init_textbox", "data_raw", "values", "opt", "instance_i", "TomSelect", "input_arg", "user_settings", "control_input", "dropdown", "control", "dropdown_content", "focus_node", "order", "tabIndex", "is_select_tag", "rtl", "inputId", "_destroy", "sifter", "isOpen", "isDisabled", "isRequired", "isInvalid", "<PERSON><PERSON><PERSON><PERSON>", "isLocked", "isFocused", "isInputHidden", "isSetup", "ignoreFocus", "ignoreHover", "hasOptions", "currentResults", "lastValue", "caretPos", "activeOption", "activeItems", "userOptions", "dir", "tomselect", "computedStyle", "window", "getComputedStyle", "getPropertyValue", "required", "Sifter", "setupCallbacks", "setupTemplates", "_render", "inputMode", "setup", "passive_event", "passive", "listboxId", "role", "control_id", "label", "label_click", "focus", "bind", "for", "label_id", "width", "classes_plugins", "join", "multiple", "escape_regex", "load", "e", "target_match", "onOptionHover", "capture", "onOptionSelect", "onItemSelect", "onClick", "onKeyDown", "onKeyPress", "onInput", "onBlur", "onFocus", "onPaste", "doc_mousedown", "<PERSON><PERSON><PERSON>", "blur", "inputState", "win_scroll", "positionDropdown", "removeEventListener", "revertSettings", "insertAdjacentElement", "sync", "refreshState", "updateOriginalInput", "refreshItems", "close", "disable", "enable", "onChange", "setupOptions", "addOptions", "registerOptionGroup", "templates", "escape", "callbacks", "get_settings", "setValue", "last<PERSON><PERSON>y", "clearActiveItems", "onMouseDown", "pastedText", "inputValue", "splitInput", "piece", "hash", "addItem", "createItem", "character", "String", "fromCharCode", "keyCode", "which", "constants", "selectAll", "open", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setActiveOption", "prev", "canSelect", "activeElement", "advanceSelection", "deleteSelection", "refreshOptions", "wasFocused", "showInput", "hasFocus", "deactivate", "setActiveItem", "setCaret", "parentElement", "closeAfterSelect", "item", "canLoad", "loadCallback", "clearActiveOption", "setTextboxValue", "changed", "getValue", "silent", "clear", "addItems", "setMaxItems", "eventName", "begin", "end", "swap", "last", "getLastActive", "setActiveItemClass", "removeActiveItem", "hideInput", "last_active", "idx", "scroll", "scrollToOption", "behavior", "height_menu", "clientHeight", "scrollTop", "height_item", "offsetHeight", "y", "getBoundingClientRect", "top", "scroll<PERSON>eh<PERSON>or", "controlChildren", "toggle", "offsetWidth", "getScoreFunction", "getSearchOptions", "sort", "field", "fields", "conjunction", "nesting", "search", "result", "calculateScore", "score", "hashed", "triggerDropdown", "j", "k", "html", "has_create_option", "active_group", "groups", "groups_order", "same_query", "results", "active_option", "show_dropdown", "closest", "min", "opt_value", "opt_hash", "option_el", "getOption", "includes", "group_fragment", "createDocumentFragment", "$id", "toString", "a", "b", "grp_a", "grp_b", "a_order", "$order", "b_order", "group_heading", "group_options", "header", "group_html", "tokens", "tok", "add_template", "template", "insertBefore", "canCreate", "active_index", "selectable", "user_created", "dat", "registerOption", "addOptionGroup", "hashed_id", "removeOptionGroup", "clearCache", "clearOptionGroups", "updateOption", "item_new", "index_item", "value_old", "value_new", "data_old", "getItem", "uncacheValue", "option_new", "removeOption", "removeItem", "clearOptions", "boundFilter", "clearFilter", "$div", "all", "x", "last_item", "isPending", "<PERSON><PERSON><PERSON>", "isFull", "insertAtCaret", "caret", "output", "lock", "created", "unlock", "refreshValidityState", "wrap_classList", "validity", "valid", "opts", "empty_option", "has_selected", "AddSelected", "reuse_opt", "visibility", "display", "context", "rect", "scrollY", "left", "scrollX", "selection", "tail", "rm_items", "rm_item", "shouldDelete", "pop", "onDelete", "adjacent", "nextElement<PERSON><PERSON>ling", "moveCaret", "new_pos", "destroy", "templateName", "hook", "when", "method", "new_fn", "orig_method", "result_new"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAIA;CACA;CACA;CACA;CACA,SAASA,SAAT,CAAmBC,MAAnB,EAAiCC,QAAjC,EAA8D;CAC7DD,EAAAA,MAAM,CAACE,KAAP,CAAa,KAAb,CAAoBC,CAAAA,OAApB,CAA6BC,KAAD,IAAU;CACrCH,IAAAA,QAAQ,CAACG,KAAD,CAAR,CAAA;CACA,GAFD,CAAA,CAAA;CAGA,CAAA;;CAEc,MAAMC,UAAN,CAAgB;CAI9BC,EAAAA,WAAW,GAAE;CAAA,IAAA,IAAA,CAFNC,OAEM,GAAA,KAAA,CAAA,CAAA;CACZ,IAAKA,IAAAA,CAAAA,OAAL,GAAe,EAAf,CAAA;CACA,GAAA;;CAEDC,EAAAA,EAAE,CAACR,MAAD,EAAgBS,GAAhB,EAA8B;CAC/BV,IAAAA,SAAS,CAACC,MAAD,EAASI,KAAD,IAAW;CAC3B,MAAA,MAAMM,WAAW,GAAG,IAAA,CAAKH,OAAL,CAAaH,KAAb,KAAuB,EAA3C,CAAA;CACAM,MAAAA,WAAW,CAACC,IAAZ,CAAiBF,GAAjB,CAAA,CAAA;CACA,MAAA,IAAA,CAAKF,OAAL,CAAaH,KAAb,CAAA,GAAsBM,WAAtB,CAAA;CACA,KAJQ,CAAT,CAAA;CAKA,GAAA;;CAEDE,EAAAA,GAAG,CAACZ,MAAD,EAAgBS,GAAhB,EAA8B;CAChC,IAAA,IAAII,CAAC,GAAGC,SAAS,CAACC,MAAlB,CAAA;;CACA,IAAIF,IAAAA,CAAC,KAAK,CAAV,EAAa;CACZ,MAAKN,IAAAA,CAAAA,OAAL,GAAe,EAAf,CAAA;CACA,MAAA,OAAA;CACA,KAAA;;CAEDR,IAAAA,SAAS,CAACC,MAAD,EAASI,KAAD,IAAW;CAE3B,MAAIS,IAAAA,CAAC,KAAK,CAAV,EAAY;CACX,QAAA,OAAO,IAAKN,CAAAA,OAAL,CAAaH,KAAb,CAAP,CAAA;CACA,QAAA,OAAA;CACA,OAAA;;CAED,MAAA,MAAMM,WAAW,GAAG,IAAA,CAAKH,OAAL,CAAaH,KAAb,CAApB,CAAA;CACA,MAAIM,IAAAA,WAAW,KAAKM,SAApB,EAAgC,OAAA;CAEhCN,MAAAA,WAAW,CAACO,MAAZ,CAAmBP,WAAW,CAACQ,OAAZ,CAAoBT,GAApB,CAAnB,EAA6C,CAA7C,CAAA,CAAA;CACA,MAAA,IAAA,CAAKF,OAAL,CAAaH,KAAb,CAAA,GAAsBM,WAAtB,CAAA;CACA,KAZQ,CAAT,CAAA;CAaA,GAAA;;CAEDS,EAAAA,OAAO,CAACnB,MAAD,EAAgB,GAAGoB,IAAnB,EAA4B;CAClC,IAAIC,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEAtB,IAAAA,SAAS,CAACC,MAAD,EAASI,KAAD,IAAW;CAC3B,MAAA,MAAMM,WAAW,GAAGW,IAAI,CAACd,OAAL,CAAaH,KAAb,CAApB,CAAA;CACA,MAAIM,IAAAA,WAAW,KAAKM,SAApB,EAAgC,OAAA;CAChCN,MAAAA,WAAW,CAACP,OAAZ,CAAoBM,GAAG,IAAI;CAC1BA,QAAAA,GAAG,CAACa,KAAJ,CAAUD,IAAV,EAAgBD,IAAhB,CAAA,CAAA;CACA,OAFD,CAAA,CAAA;CAIA,KAPQ,CAAT,CAAA;CAQA,GAAA;;CAjD6B;;CCtB/B;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAmBe,SAASG,WAAT,CAAqBC,SAArB,EAAqC;CAEnDA,EAAAA,SAAS,CAACC,OAAV,GAAoB,EAApB,CAAA;CAEA,EAAO,OAAA,cAAcD,SAAd,CAAuB;CAAA,IAAA,WAAA,CAAA,GAAA,IAAA,EAAA;CAAA,MAAA,KAAA,CAAA,GAAA,IAAA,CAAA,CAAA;CAAA,MAAA,IAAA,CAEtBC,OAFsB,GAEH;CACzBC,QAAAA,KAAK,EAAO,EADa;CAEzBC,QAAAA,QAAQ,EAAI,EAFa;CAGzBC,QAAAA,SAAS,EAAG,EAHa;CAIzBC,QAAAA,MAAM,EAAM,EAAA;CAJa,OAFG,CAAA;CAAA,KAAA;;CAS7B;CACF;CACA;CACA;CACA;CACe,IAAA,OAANC,MAAM,CAACC,IAAD,EAAcC,EAAd,EAAoD;CAChER,MAAAA,SAAS,CAACC,OAAV,CAAkBM,IAAlB,CAA0B,GAAA;CACzB,QAAA,MAAA,EAASA,IADgB;CAEzB,QAASC,IAAAA,EAAAA,EAAAA;CAFgB,OAA1B,CAAA;CAIA,KAAA;CAGD;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;CACEC,IAAAA,iBAAiB,CAACR,OAAD,EAA6C;CAC7D,MAAIS,IAAAA,GAAJ,EAASH,IAAT,CAAA;CACA,MAAMV,MAAAA,IAAI,GAAI,IAAd,CAAA;CACA,MAAMc,MAAAA,KAAc,GAAG,EAAvB,CAAA;;CAEA,MAAA,IAAIC,KAAK,CAACC,OAAN,CAAcZ,OAAd,CAAJ,EAA4B;CAC3BA,QAAAA,OAAO,CAACtB,OAAR,CAAiBmC,MAAD,IAA6B;CAC5C,UAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;CAC/BH,YAAAA,KAAK,CAACxB,IAAN,CAAW2B,MAAX,CAAA,CAAA;CACA,WAFD,MAEO;CACNjB,YAAAA,IAAI,CAACI,OAAL,CAAaE,QAAb,CAAsBW,MAAM,CAACP,IAA7B,CAAA,GAAqCO,MAAM,CAACC,OAA5C,CAAA;CACAJ,YAAAA,KAAK,CAACxB,IAAN,CAAW2B,MAAM,CAACP,IAAlB,CAAA,CAAA;CACA,WAAA;CACD,SAPD,CAAA,CAAA;CAQA,OATD,MASO,IAAIN,OAAJ,EAAa;CACnB,QAAKS,KAAAA,GAAL,IAAYT,OAAZ,EAAqB;CACpB,UAAA,IAAIA,OAAO,CAACe,cAAR,CAAuBN,GAAvB,CAAJ,EAAiC;CAChCb,YAAAA,IAAI,CAACI,OAAL,CAAaE,QAAb,CAAsBO,GAAtB,CAA6BT,GAAAA,OAAO,CAACS,GAAD,CAApC,CAAA;CACAC,YAAAA,KAAK,CAACxB,IAAN,CAAWuB,GAAX,CAAA,CAAA;CACA,WAAA;CACD,SAAA;CACD,OAAA;;CAED,MAAA,OAAOH,IAAI,GAAGI,KAAK,CAACM,KAAN,EAAd,EAA6B;CAC5BpB,QAAAA,IAAI,CAACqB,OAAL,CAAaX,IAAb,CAAA,CAAA;CACA,OAAA;CACD,KAAA;;CAEDY,IAAAA,UAAU,CAACZ,IAAD,EAAc;CACvB,MAAIV,IAAAA,IAAI,GAAM,IAAd,CAAA;CACA,MAAA,IAAII,OAAO,GAAGJ,IAAI,CAACI,OAAnB,CAAA;CACA,MAAA,IAAIa,MAAM,GAAId,SAAS,CAACC,OAAV,CAAkBM,IAAlB,CAAd,CAAA;;CAEA,MAAI,IAAA,CAACP,SAAS,CAACC,OAAV,CAAkBe,cAAlB,CAAiCT,IAAjC,CAAL,EAA6C;CAC5C,QAAA,MAAM,IAAIa,KAAJ,CAAU,qBAAsBb,IAAtB,GAA6B,UAAvC,CAAN,CAAA;CACA,OAAA;;CAEDN,MAAAA,OAAO,CAACG,SAAR,CAAkBG,IAAlB,IAA0B,IAA1B,CAAA;CACAN,MAAAA,OAAO,CAACI,MAAR,CAAeE,IAAf,IAAuBO,MAAM,CAACN,EAAP,CAAUV,KAAV,CAAgBD,IAAhB,EAAsB,CAACA,IAAI,CAACI,OAAL,CAAaE,QAAb,CAAsBI,IAAtB,CAAA,IAA+B,EAAhC,CAAtB,CAAvB,CAAA;CACAN,MAAAA,OAAO,CAACC,KAAR,CAAcf,IAAd,CAAmBoB,IAAnB,CAAA,CAAA;CACA,KAAA;CAED;CACF;CACA;CACA;;;CACEW,IAAAA,OAAO,CAACX,IAAD,EAAc;CACpB,MAAIV,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,MAAA,IAAII,OAAO,GAAGJ,IAAI,CAACI,OAAnB,CAAA;;CAEA,MAAI,IAAA,CAACJ,IAAI,CAACI,OAAL,CAAaI,MAAb,CAAoBW,cAApB,CAAmCT,IAAnC,CAAL,EAA+C;CAC9C,QAAA,IAAIN,OAAO,CAACG,SAAR,CAAkBG,IAAlB,CAAJ,EAA6B;CAC5B,UAAA,MAAM,IAAIa,KAAJ,CAAU,sCAAsCb,IAAtC,GAA6C,IAAvD,CAAN,CAAA;CACA,SAAA;;CACDV,QAAAA,IAAI,CAACsB,UAAL,CAAgBZ,IAAhB,CAAA,CAAA;CACA,OAAA;;CAED,MAAA,OAAON,OAAO,CAACI,MAAR,CAAeE,IAAf,CAAP,CAAA;CACA,KAAA;;CA/F4B,GAA9B,CAAA;CAmGA;;CCxID;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,cAAc,GAAG,KAAK,IAAI;CAChC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAChC;CACA,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CACxB,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;CAC1B,GAAG;AACH;CACA,EAAE,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;CACjG,CAAC,CAAC;CACF;CACA;CACA;CACA;AACA;CACA,MAAM,eAAe,GAAG,KAAK,IAAI;CACjC,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;CAC7B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CAC1B,GAAG;AACH;CACA,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;CACnB,EAAE,IAAI,eAAe,GAAG,CAAC,CAAC;AAC1B;CACA,EAAE,MAAM,YAAY,GAAG,MAAM;CAC7B,IAAI,IAAI,eAAe,GAAG,CAAC,EAAE;CAC7B,MAAM,OAAO,IAAI,GAAG,GAAG,eAAe,GAAG,GAAG,CAAC;CAC7C,KAAK;CACL,GAAG,CAAC;AACJ;CACA,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;CAC7B,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CAC/B,MAAM,eAAe,EAAE,CAAC;CACxB,MAAM,OAAO;CACb,KAAK;AACL;CACA,IAAI,YAAY,EAAE,CAAC;CACnB,IAAI,OAAO,IAAI,IAAI,CAAC;CACpB,IAAI,eAAe,GAAG,CAAC,CAAC;CACxB,GAAG,CAAC,CAAC;CACL,EAAE,YAAY,EAAE,CAAC;CACjB,EAAE,OAAO,OAAO,CAAC;CACjB,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,YAAY,GAAG,KAAK,IAAI;CAC9B,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;CAC7B,EAAE,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;CAC/B,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,aAAa,GAAG,KAAK,IAAI;CAC/B,EAAE,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC;CAC9C,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,YAAY,GAAG,GAAG,IAAI;CAC5B,EAAE,OAAO,CAAC,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC;CAC1E,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,cAAc,GAAG,KAAK,IAAI;CAChC,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACtF,CAAC,CAAC;CACF;CACA;CACA;AACA;CACA,MAAM,aAAa,GAAG,GAAG,IAAI;CAC7B,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;CAC7B,CAAC,CAAC;CACF;CACA;CACA;CACA;AACA;CACA,MAAM,OAAO,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;CCpGlC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,aAAa,GAAG,KAAK,IAAI;CAC/B,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;CAC3C;AACA;CACA,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;CAClB,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;CACnC,EAAE,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;CACpC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;CACpC,IAAI,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACjC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;CACtC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrB,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CAC7B,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CACjC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrB,GAAG,CAAC,CAAC;CACL,EAAE,OAAO,MAAM,CAAC;CAChB,CAAC;;CCvBD;AAIA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,UAAU,GAAG,qCAAqC,CAAC;CACzD;AACA;CACA,IAAI,WAAW,CAAC;CAChB;AACA;CACA,IAAI,cAAc,CAAC;CACnB,MAAM,eAAe,GAAG,CAAC,CAAC;CAC1B;AACA;CACA,MAAM,aAAa,GAAG,EAAE,CAAC;CACzB;AACA;CACA,MAAM,eAAe,GAAG;CACxB,EAAE,GAAG,EAAE,IAAI;CACX,EAAE,GAAG,EAAE,GAAG;CACV,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,KAAK;CACb,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,IAAI;CACZ,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,GAAG,EAAE,MAAM;CACb,EAAE,GAAG,EAAE,UAAU;CACjB,EAAE,GAAG,EAAE,MAAM;CACb,EAAE,GAAG,EAAE,IAAI;CACX,EAAE,GAAG,EAAE,QAAQ;CACf,EAAE,GAAG,EAAE,MAAM;CACb,EAAE,GAAG,EAAE,IAAI;CACX,EAAE,GAAG,EAAE,IAAI;CACX,EAAE,GAAG,EAAE,QAAQ;CACf,EAAE,GAAG,EAAE,UAAU;CACjB,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,GAAG,EAAE,SAAS;CAChB,EAAE,GAAG,EAAE,SAAS;CAChB,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,GAAG,EAAE,QAAQ;CACf,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,GAAG,EAAE,OAAO;CACd,EAAE,GAAG,EAAE,OAAO;CACd,EAAE,GAAG,EAAE,OAAO;CACd,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,GAAG,EAAE,GAAG;CACV,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,IAAI,EAAE,GAAG;CACX,EAAE,GAAG,EAAE,GAAG;CACV,EAAE,GAAG,EAAE,KAAK;CACZ,EAAE,GAAG,EAAE,OAAO;CACd,EAAE,IAAI,EAAE,GAAG;CACX,CAAC,CAAC;AACF;CACA,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE;CACnC,EAAE,IAAI,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC7C;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC3C,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC3C,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;CAChC,GAAG;CACH,CAAC;AACD;CACA,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,UAAU,EAAE,IAAI,CAAC,CAAC;CAC9F;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,UAAU,GAAG,YAAY,IAAI;CACnC,EAAE,IAAI,WAAW,KAAK,SAAS,EAAE,OAAO;CACxC,EAAE,WAAW,GAAG,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,CAAC;CACzD,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CAC9D;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,SAAS,GAAG,GAAG,IAAI;CACzB,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM;CAC5B;CACA;CACA;CACA;CACA,EAAE,CAAC,MAAM,EAAE,IAAI,KAAK;CACpB,IAAI,OAAO,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;CACrC,GAAG,EAAE,EAAE,CAAC,CAAC;CACT,CAAC,CAAC;CACF;CACA;CACA;CACA;AACA;CACA,MAAM,UAAU,GAAG,GAAG,IAAI;CAC1B,EAAE,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE;CAC1D;CACA,EAAE,IAAI,KAAK;CACX,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACrC,GAAG,CAAC,CAAC;AACL;CACA,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;CAC/B,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;CACA,UAAU,SAAS,CAAC,WAAW,EAAE;CACjC,EAAE,KAAK,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC,IAAI,WAAW,EAAE;CAC9D,IAAI,KAAK,IAAI,CAAC,GAAG,cAAc,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAE;CAC3D,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;CAC5C,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;AACvC;CACA,MAAM,IAAI,MAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE;CAC5C,QAAQ,SAAS;CACjB,OAAO;CACP;CACA;CACA;CACA;AACA;AACA;CACA,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,eAAe,EAAE;CAC3C,QAAQ,SAAS;CACjB,OAAO;AACP;CACA,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;CAC9B,QAAQ,SAAS;CACjB,OAAO;AACP;CACA,MAAM,MAAM;CACZ,QAAQ,MAAM,EAAE,MAAM;CACtB,QAAQ,QAAQ,EAAE,QAAQ;CAC1B,QAAQ,UAAU,EAAE,CAAC;CACrB,OAAO,CAAC;CACR,KAAK;CACL,GAAG;CACH,CAAC;CACD;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,YAAY,GAAG,WAAW,IAAI;CACpC;CACA,EAAE,MAAM,YAAY,GAAG,EAAE,CAAC;CAC1B;CACA;CACA;CACA;AACA;CACA,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;CAC1C;CACA,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;CACzD,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;AACxE;CACA,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;CAC5B,MAAM,OAAO;CACb,KAAK;AACL;CACA,IAAI,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;CACzC,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC;CACtC,GAAG,CAAC;AACJ;CACA,EAAE,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;CAC5C,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CAC5C,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;CAC9C,GAAG;AACH;CACA,EAAE,OAAO,YAAY,CAAC;CACtB,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,WAAW,GAAG,WAAW,IAAI;CACnC;CACA,EAAE,MAAM,YAAY,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;CACjD;AACA;CACA,EAAE,MAAM,WAAW,GAAG,EAAE,CAAC;CACzB;AACA;CACA,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB;CACA,EAAE,KAAK,IAAI,MAAM,IAAI,YAAY,EAAE;CACnC,IAAI,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACnC;CACA,IAAI,IAAI,GAAG,EAAE;CACb,MAAM,WAAW,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;CAC9C,KAAK;AACL;CACA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;CAC3B,MAAM,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;CAC5C,KAAK;CACL,GAAG;AACH;CACA,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;CACjD,EAAE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;CACrD,EAAE,cAAc,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,eAAe,EAAE,GAAG,CAAC,CAAC;CAC1D,EAAE,OAAO,WAAW,CAAC;CACrB,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,eAAe,GAAG,CAAC,KAAK;CACtD,EAAE,IAAI,cAAc,GAAG,CAAC,CAAC;CACzB,EAAE,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI;CAC/B,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;CAC1B,MAAM,cAAc,IAAI,GAAG,CAAC,MAAM,CAAC;CACnC,KAAK;AACL;CACA,IAAI,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;CACnC,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,cAAc,IAAI,eAAe,EAAE;CACzC,IAAI,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;CACpC,GAAG;AACH;CACA,EAAE,OAAO,EAAE,CAAC;CACZ,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,eAAe,GAAG,CAAC,KAAK;CAC1D,EAAE,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CAC9D,EAAE,OAAO,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI;CAC1D,IAAI,OAAO,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;CACjD,GAAG,CAAC,CAAC,CAAC;CACN,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,kBAAkB,GAAG,CAAC,SAAS,EAAE,GAAG,GAAG,IAAI,KAAK;CACtD,EAAE,IAAI,eAAe,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrD,EAAE,OAAO,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI;CAClD,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;CACjB,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAChE;CACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAClC,MAAM,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;CAChF,KAAK;AACL;CACA,IAAI,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;CAChC,GAAG,CAAC,CAAC,CAAC;CACN,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;AACA;CACA,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,SAAS,KAAK;CAC/C,EAAE,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;CAC/B,IAAI,IAAI,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE;CACpE,MAAM,SAAS;CACf,KAAK;AACL;CACA,IAAI,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;CAC9D,MAAM,SAAS;CACf,KAAK;AACL;CACA,IAAI,IAAI,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC;CACxC;CACA;CACA;AACA;CACA,IAAI,MAAM,MAAM,GAAG,IAAI,IAAI;CAC3B,MAAM,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;CAC9C,QAAQ,IAAI,WAAW,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;CACpF,UAAU,OAAO,KAAK,CAAC;CACvB,SAAS;AACT;CACA,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;CACzD,UAAU,SAAS;CACnB,SAAS;CACT;CACA;CACA;CACA;AACA;AACA;CACA,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE;CAC5E,UAAU,OAAO,IAAI,CAAC;CACtB,SAAS;AACT;CACA,QAAQ,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE;CAC5E,UAAU,OAAO,IAAI,CAAC;CACtB,SAAS;CACT,OAAO;AACP;CACA,MAAM,OAAO,KAAK,CAAC;CACnB,KAAK,CAAC;AACN;CACA,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC5C;CACA,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;CAC7B,MAAM,SAAS;CACf,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;CAChB,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;CACf,CAAC,CAAC;AACF;CACA,MAAM,QAAQ,CAAC;CACf,EAAE,WAAW,GAAG;CAChB;CACA,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CACpB;AACA;CACA,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CACtB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;CACnB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CACjB,GAAG;CACH;CACA;CACA;AACA;AACA;CACA,EAAE,GAAG,CAAC,IAAI,EAAE;CACZ,IAAI,IAAI,IAAI,EAAE;CACd,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACrC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;CACpD,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;CAC9C,KAAK;CACL,GAAG;AACH;CACA,EAAE,IAAI,GAAG;CACT,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CAC7C,GAAG;AACH;CACA,EAAE,MAAM,GAAG;CACX,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;CAC7B,GAAG;CACH;CACA;CACA;CACA;AACA;AACA;CACA,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE;CAC9B,IAAI,IAAI,KAAK,GAAG,IAAI,QAAQ,EAAE,CAAC;CAC/B,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;CACvD,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAChC;CACA,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;CAC9B,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACtB,KAAK;AACL;CACA,IAAI,IAAI,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;CACjF,IAAI,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC;CAC5C,IAAI,KAAK,CAAC,GAAG,CAAC;CACd,MAAM,KAAK,EAAE,SAAS,CAAC,KAAK;CAC5B,MAAM,GAAG,EAAE,SAAS,CAAC,KAAK,GAAG,cAAc;CAC3C,MAAM,MAAM,EAAE,cAAc;CAC5B,MAAM,MAAM,EAAE,WAAW;CACzB,KAAK,CAAC,CAAC;CACP,IAAI,OAAO,KAAK,CAAC;CACjB,GAAG;AACH;CACA,CAAC;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;AACA;CACA,MAAM,UAAU,GAAG,GAAG,IAAI;CAC1B,EAAE,UAAU,EAAE,CAAC;CACf,EAAE,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;CACvB,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;CACnB,EAAE,IAAI,SAAS,GAAG,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC;AACnC;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACvC,IAAI,IAAI,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;CAClC,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;CAC7C,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CACzC,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CAC9C;AACA;CACA,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;CACzB,IAAI,IAAI,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC;CACA,IAAI,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;CACtC,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzC;CACA,MAAM,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE;CACxE;CACA,QAAQ,IAAI,SAAS,EAAE;CACvB,UAAU,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;CACvC,UAAU,QAAQ,CAAC,GAAG,CAAC;CACvB,YAAY,KAAK,EAAE,CAAC;CACpB,YAAY,GAAG,EAAE,CAAC,GAAG,GAAG;CACxB,YAAY,MAAM,EAAE,GAAG;CACvB,YAAY,MAAM,EAAE,SAAS;CAC7B,WAAW,CAAC,CAAC;CACb,UAAU,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC/B,SAAS,MAAM;CACf,UAAU,QAAQ,CAAC,GAAG,CAAC;CACvB,YAAY,KAAK,EAAE,CAAC;CACpB,YAAY,GAAG,EAAE,CAAC,GAAG,CAAC;CACtB,YAAY,MAAM,EAAE,CAAC;CACrB,YAAY,MAAM,EAAE,IAAI;CACxB,WAAW,CAAC,CAAC;CACb,UAAU,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC/B,SAAS;CACT,OAAO,MAAM,IAAI,SAAS,EAAE;CAC5B,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;CAClD,QAAQ,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;CACrC,QAAQ,KAAK,CAAC,GAAG,CAAC;CAClB,UAAU,KAAK,EAAE,CAAC;CAClB,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG;CACtB,UAAU,MAAM,EAAE,GAAG;CACrB,UAAU,MAAM,EAAE,SAAS;CAC3B,SAAS,CAAC,CAAC;CACX,QAAQ,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAChC,OAAO,MAAM;CACb;CACA;CACA,QAAQ,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CAC7B,OAAO;CACP,KAAK;AACL;AACA;CACA,IAAI,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;CAChC;CACA,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAC/C,QAAQ,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;CACvC,OAAO,CAAC,CAAC;AACT;CACA,MAAM,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE;CACrC;CACA,QAAQ,IAAI,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;CAC3C,UAAU,SAAS;CACnB,SAAS;AACT;CACA,QAAQ,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC9B,OAAO;AACP;CACA,MAAM,SAAS;CACf,KAAK;CACL;CACA;CACA;AACA;AACA;CACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;CACjE,MAAM,OAAO,IAAI,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;CACtD,MAAM,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;CACnC,MAAM,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACnC;CACA,MAAM,IAAI,OAAO,EAAE;CACnB,QAAQ,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;CACpC,OAAO;AACP;CACA,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,CAAC;CAC5B,KAAK;CACL,GAAG;AACH;CACA,EAAE,OAAO,IAAI,kBAAkB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;CACjD,EAAE,OAAO,OAAO,CAAC;CACjB,CAAC;;CCphBD;AAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;CAC/B,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;CACnB,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;CACnB,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;CACtC,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;CACnB,EAAE,IAAI,IAAI;CACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9B;CACA,EAAE,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtD;CACA,EAAE,OAAO,GAAG,CAAC;CACb,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,KAAK;CAC7C,EAAE,IAAI,KAAK,EAAE,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACvB,EAAE,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CACrB,EAAE,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;CACpC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;CAClC,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;CAC3B,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;CAC7C,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC;CAC9B,EAAE,OAAO,KAAK,GAAG,MAAM,CAAC;CACxB,CAAC,CAAC;CACF;CACA;CACA;CACA;AACA;CACA,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK;CAClC,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;CACvB,EAAE,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE,OAAO,KAAK,CAAC;AAC/C;CACA,EAAE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CACtC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACvB,GAAG;CACH,CAAC,CAAC;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAMc,SAAO,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;CACtC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;CAC7B,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CAC7B,GAAG,MAAM;CACT,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC5B,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACtC,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;CACnC,OAAO;CACP,KAAK;CACL,GAAG;CACH,CAAC,CAAC;CACF,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;CACtB,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;CACtD,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CACtC,GAAG;AACH;CACA,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;CACtC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;CACtC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;CACtB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;CACvB,EAAE,OAAO,CAAC,CAAC;CACX,CAAC;;CC3FD;AAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,MAAM,MAAM,CAAC;CACb;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE;CAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;CACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;CAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACvB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI;CAChC,MAAM,UAAU,EAAE,IAAI;CACtB,KAAK,CAAC;CACN,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,QAAQ,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE;CACpD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;CAC3C,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;CACtB,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;CACrC,IAAI,IAAI,WAAW,CAAC;AACpB;CACA,IAAI,IAAI,OAAO,EAAE;CACjB,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;CACrG,KAAK;AACL;CACA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;CAC1B,MAAM,IAAI,WAAW,CAAC;CACtB,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;CACvB,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;AACvB;CACA,MAAM,IAAI,WAAW,KAAK,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;CAClE,QAAQ,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;CAC/B,QAAQ,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;CAC9B,OAAO;AACP;CACA,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;CAC3B,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;CACtC,UAAU,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;CAC3C,SAAS,MAAM;CACf,UAAU,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;CACrC,SAAS;AACT;CACA,QAAQ,IAAI,KAAK,IAAI,uBAAuB,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;CACpE,OAAO;AACP;CACA,MAAM,MAAM,CAAC,IAAI,CAAC;CAClB,QAAQ,MAAM,EAAE,IAAI;CACpB,QAAQ,KAAK,EAAE,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI;CACrD,QAAQ,KAAK,EAAE,KAAK;CACpB,OAAO,CAAC,CAAC;CACT,KAAK,CAAC,CAAC;CACP,IAAI,OAAO,MAAM,CAAC;CAClB,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE;CACnC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACpD,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;CAC1C,GAAG;CACH;CACA;CACA;CACA;AACA;AACA;CACA,EAAE,iBAAiB,CAAC,MAAM,EAAE;CAC5B,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM;CAChC,UAAU,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;AACtC;CACA,IAAI,IAAI,CAAC,WAAW,EAAE;CACtB,MAAM,OAAO,YAAY;CACzB,QAAQ,OAAO,CAAC,CAAC;CACjB,OAAO,CAAC;CACR,KAAK;AACL;CACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM;CACxC,UAAU,OAAO,GAAG,MAAM,CAAC,OAAO;CAClC,UAAU,WAAW,GAAG,MAAM,CAAC,MAAM;CACrC,UAAU,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AACvC;CACA,IAAI,IAAI,CAAC,WAAW,EAAE;CACtB,MAAM,OAAO,YAAY;CACzB,QAAQ,OAAO,CAAC,CAAC;CACjB,OAAO,CAAC;CACR,KAAK;CACL;CACA;CACA;CACA;CACA;AACA;AACA;CACA,IAAI,MAAM,WAAW,GAAG,YAAY;CACpC,MAAM,IAAI,WAAW,KAAK,CAAC,EAAE;CAC7B,QAAQ,OAAO,UAAU,KAAK,EAAE,IAAI,EAAE;CACtC,UAAU,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;CACxC,UAAU,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;CAChF,SAAS,CAAC;CACV,OAAO;AACP;CACA,MAAM,OAAO,UAAU,KAAK,EAAE,IAAI,EAAE;CACpC,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;AACpB;CACA,QAAQ,IAAI,KAAK,CAAC,KAAK,EAAE;CACzB,UAAU,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACrD;CACA,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,EAAE;CACrC,YAAY,GAAG,IAAI,CAAC,GAAG,WAAW,CAAC;CACnC,WAAW,MAAM;CACjB,YAAY,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;CAC/C,WAAW;CACX,SAAS,MAAM;CACf,UAAUA,SAAO,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,KAAK;CAC9C,YAAY,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;CACrE,WAAW,CAAC,CAAC;CACb,SAAS;AACT;CACA,QAAQ,OAAO,GAAG,GAAG,WAAW,CAAC;CACjC,OAAO,CAAC;CACR,KAAK,EAAE,CAAC;AACR;CACA,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE;CAC3B,MAAM,OAAO,UAAU,IAAI,EAAE;CAC7B,QAAQ,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;CAC5C,OAAO,CAAC;CACR,KAAK;AACL;CACA,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE;CAC9C,MAAM,OAAO,UAAU,IAAI,EAAE;CAC7B,QAAQ,IAAI,KAAK;CACjB,YAAY,GAAG,GAAG,CAAC,CAAC;AACpB;CACA,QAAQ,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;CAClC,UAAU,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;CAC3C,UAAU,IAAI,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;CACnC,UAAU,GAAG,IAAI,KAAK,CAAC;CACvB,SAAS;AACT;CACA,QAAQ,OAAO,GAAG,GAAG,WAAW,CAAC;CACjC,OAAO,CAAC;CACR,KAAK,MAAM;CACX,MAAM,OAAO,UAAU,IAAI,EAAE;CAC7B,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;CACpB,QAAQA,SAAO,CAAC,MAAM,EAAE,KAAK,IAAI;CACjC,UAAU,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;CAC1C,SAAS,CAAC,CAAC;CACX,QAAQ,OAAO,GAAG,GAAG,WAAW,CAAC;CACjC,OAAO,CAAC;CACR,KAAK;CACL,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE;CAClC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACpD,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;CACzC,GAAG;AACH;CACA,EAAE,gBAAgB,CAAC,MAAM,EAAE;CAC3B,IAAI,IAAI,cAAc;CACtB,QAAQ,SAAS,GAAG,EAAE,CAAC;CACvB,IAAI,MAAM,IAAI,GAAG,IAAI;CACrB,UAAU,OAAO,GAAG,MAAM,CAAC,OAAO;CAClC,UAAU,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;AACzF;CACA,IAAI,IAAI,OAAO,IAAI,IAAI,UAAU,EAAE;CACnC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC7B,KAAK;CACL;CACA;CACA;CACA;CACA;AACA;AACA;CACA,IAAI,MAAM,SAAS,GAAG,SAAS,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE;CACvD,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC;CACjD,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;CAC3D,KAAK,CAAC;AACN;AACA;CACA,IAAI,IAAI,IAAI,EAAE;CACd,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;CAC1B,QAAQ,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE;CAClD,UAAU,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAC5B,SAAS;CACT,OAAO;CACP,KAAK;CACL;AACA;AACA;CACA,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;CACtB,MAAM,cAAc,GAAG,IAAI,CAAC;AAC5B;CACA,MAAM,KAAK,IAAI,GAAG,IAAI,SAAS,EAAE;CACjC,QAAQ,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE;CACpC,UAAU,cAAc,GAAG,KAAK,CAAC;CACjC,UAAU,MAAM;CAChB,SAAS;CACT,OAAO;AACP;CACA,MAAM,IAAI,cAAc,EAAE;CAC1B,QAAQ,SAAS,CAAC,OAAO,CAAC;CAC1B,UAAU,KAAK,EAAE,QAAQ;CACzB,UAAU,SAAS,EAAE,MAAM;CAC3B,SAAS,CAAC,CAAC;CACX,OAAO;AACP;CACA,KAAK,MAAM;CACX,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;CAClE,KAAK;AACL;AACA;CACA,IAAI,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC;AAC7C;CACA,IAAI,IAAI,CAAC,eAAe,EAAE;CAC1B,MAAM,OAAO,IAAI,CAAC;CAClB,KAAK;AACL;CACA,IAAI,OAAO,UAAU,CAAC,EAAE,CAAC,EAAE;CAC3B,MAAM,IAAI,MAAM,EAAE,KAAK,CAAC;AACxB;CACA,MAAM,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;CACtC,QAAQ,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC/B,QAAQ,IAAI,UAAU,GAAG,QAAQ,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAChE,QAAQ,MAAM,GAAG,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;CAC5E,QAAQ,IAAI,MAAM,EAAE,OAAO,MAAM,CAAC;CAClC,OAAO;AACP;CACA,MAAM,OAAO,CAAC,CAAC;CACf,KAAK,CAAC;CACN,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE;CACjC,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;CACvB,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;CAC9C,IAAI,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;CACjC,IAAI,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACvC;CACA,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;CACxB,MAAM,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;CACrC,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;CACxB,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;CACtC,QAAQ,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;CACtC,UAAU,KAAK,GAAG;CAClB,YAAY,KAAK,EAAE,KAAK;CACxB,YAAY,MAAM,EAAE,CAAC;CACrB,WAAW,CAAC;CACZ,SAAS;AACT;CACA,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;CACpE,OAAO,CAAC,CAAC;CACT,MAAM,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;CAC9B,KAAK;AACL;CACA,IAAI,OAAO;CACX,MAAM,OAAO,EAAE,OAAO;CACtB,MAAM,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE;CACvC,MAAM,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,uBAAuB,EAAE,OAAO,CAAC;CAC5E,MAAM,KAAK,EAAE,CAAC;CACd,MAAM,KAAK,EAAE,EAAE;CACf,MAAM,OAAO,EAAE,OAAO;CACtB,MAAM,SAAS,EAAE,OAAO,CAAC,OAAO,GAAG,cAAc,GAAG,OAAO;CAC3D,KAAK,CAAC;CACN,GAAG;AACH;CACA;CACA;CACA;CACA;CACA,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE;CACzB,IAAI,IAAI,IAAI,GAAG,IAAI;CACnB,QAAQ,KAAK;CACb,QAAQ,MAAM,CAAC;CACf,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAChD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;CAC7B,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACzB;CACA,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACrE;AACA;CACA,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;CACtB,MAAMA,SAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK;CACxC,QAAQ,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC/B;CACA,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE;CACnD,UAAU,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;CAC5B,YAAY,OAAO,EAAE,KAAK;CAC1B,YAAY,IAAI,EAAE,EAAE;CACpB,WAAW,CAAC,CAAC;CACb,SAAS;CACT,OAAO,CAAC,CAAC;CACT,KAAK,MAAM;CACX,MAAMA,SAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK;CACrC,QAAQ,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;CAC1B,UAAU,OAAO,EAAE,CAAC;CACpB,UAAU,IAAI,EAAE,EAAE;CAClB,SAAS,CAAC,CAAC;CACX,OAAO,CAAC,CAAC;CACT,KAAK;AACL;CACA,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClD;CACA,IAAI,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C;CACA,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AACvC;CACA,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;CAC3C,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;CAC1D,KAAK;AACL;CACA,IAAI,OAAO,MAAM,CAAC;CAClB,GAAG;AACH;CACA;;CCzSA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CACO,MAAMA,OAAO,GAAG,CAACC,MAAD,EAA+B7C,QAA/B,KAAqE;CAE3F,EAAA,IAAKmC,KAAK,CAACC,OAAN,CAAcS,MAAd,CAAL,EAA4B;CAC3BA,IAAAA,MAAM,CAAC3C,OAAP,CAAeF,QAAf,CAAA,CAAA;CAEA,GAHD,MAGK;CAEJ,IAAA,KAAK,IAAIiC,GAAT,IAAgBY,MAAhB,EAAwB;CACvB,MAAA,IAAIA,MAAM,CAACN,cAAP,CAAsBN,GAAtB,CAAJ,EAAgC;CAC/BjC,QAAAA,QAAQ,CAAC6C,MAAM,CAACZ,GAAD,CAAP,EAAcA,GAAd,CAAR,CAAA;CACA,OAAA;CACD,KAAA;CACD,GAAA;CACD,CAbM;;CCzEP;CACA;CACA;CACA;CACA;CACA;;CACO,MAAMa,MAAM,GAAKC,KAAF,IAA6B;CAElD,EAAIA,IAAAA,KAAK,CAACC,MAAV,EAAkB;CACjB,IAAOD,OAAAA,KAAK,CAAC,CAAD,CAAZ,CAAA;CACA,GAAA;;CAED,EAAIA,IAAAA,KAAK,YAAYE,WAArB,EAAkC;CACjC,IAAA,OAAOF,KAAP,CAAA;CACA,GAAA;;CAED,EAAA,IAAIG,YAAY,CAACH,KAAD,CAAhB,EAAyB;CACxB,IAAA,IAAII,GAAG,GAAGC,QAAQ,CAACC,aAAT,CAAuB,UAAvB,CAAV,CAAA;CACAF,IAAAA,GAAG,CAACG,SAAJ,GAAgBP,KAAK,CAACQ,IAAN,EAAhB,CAFwB;;CAGxB,IAAA,OAAOJ,GAAG,CAACK,OAAJ,CAAYC,UAAnB,CAAA;CACA,GAAA;;CAED,EAAA,OAAOL,QAAQ,CAACM,aAAT,CAAuBX,KAAvB,CAAP,CAAA;CACA,CAjBM,CAAA;CAmBA,MAAMG,YAAY,GAAIS,GAAD,IAAsB;CACjD,EAAA,IAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAAC1C,OAAJ,CAAY,GAAZ,CAAmB,GAAA,CAAC,CAAnD,EAAsD;CACrD,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;;CACD,EAAA,OAAO,KAAP,CAAA;CACA,CALM,CAAA;CAOA,MAAM2C,WAAW,GAAIb,KAAD,IAAyB;CACnD,EAAA,OAAOA,KAAK,CAACc,OAAN,CAAc,SAAd,EAAyB,MAAzB,CAAP,CAAA;CACA,CAFM,CAAA;CAIP;CACA;CACA;CACA;;CACO,MAAMC,YAAY,GAAG,CAAEC,MAAF,EAAsBC,UAAtB,KAAkD;CAC7E,EAAA,IAAI7D,KAAK,GAAGiD,QAAQ,CAACa,WAAT,CAAqB,YAArB,CAAZ,CAAA;CACA9D,EAAAA,KAAK,CAAC+D,SAAN,CAAgBF,UAAhB,EAA4B,IAA5B,EAAkC,KAAlC,CAAA,CAAA;CACAD,EAAAA,MAAM,CAACI,aAAP,CAAqBhE,KAArB,CAAA,CAAA;CACA,CAJM,CAAA;CAMP;CACA;CACA;CACA;;CACO,MAAMiE,QAAQ,GAAG,CAAEL,MAAF,EAAsBM,GAAtB,KAAoE;CAC3FC,EAAAA,MAAM,CAACC,MAAP,CAAcR,MAAM,CAACS,KAArB,EAA4BH,GAA5B,CAAA,CAAA;CACA,CAFM,CAAA;CAKP;CACA;CACA;CACA;;CACO,MAAMI,UAAU,GAAG,CAAEC,KAAF,EAAmC,GAAGC,OAAtC,KAAuE;CAEhG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAD,CAAhC,CAAA;CACAD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAD,CAAtB,CAAA;CAEAA,EAAAA,KAAK,CAACK,GAAN,CAAWC,EAAE,IAAI;CAChBJ,IAAAA,YAAY,CAACG,GAAb,CAAkBE,GAAG,IAAI;CACxBD,MAAAA,EAAE,CAACE,SAAH,CAAaC,GAAb,CAAkBF,GAAlB,CAAA,CAAA;CACA,KAFD,CAAA,CAAA;CAGA,GAJD,CAAA,CAAA;CAKA,CAVM,CAAA;CAYP;CACA;CACA;CACA;;CACQ,MAAMG,aAAa,GAAG,CAAEV,KAAF,EAAmC,GAAGC,OAAtC,KAAuE;CAEnG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAD,CAAhC,CAAA;CACDD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAD,CAAtB,CAAA;CAEAA,EAAAA,KAAK,CAACK,GAAN,CAAWC,EAAE,IAAI;CAChBJ,IAAAA,YAAY,CAACG,GAAb,CAAiBE,GAAG,IAAI;CACtBD,MAAAA,EAAE,CAACE,SAAH,CAAaG,MAAb,CAAqBJ,GAArB,CAAA,CAAA;CACD,KAFD,CAAA,CAAA;CAGC,GAJF,CAAA,CAAA;CAKC,CAVM,CAAA;CAaR;CACA;CACA;CACA;;CACO,MAAMJ,YAAY,GAAI1D,IAAD,IAAuC;CAClE,EAAIwD,IAAAA,OAAgB,GAAG,EAAvB,CAAA;CACA/B,EAAAA,OAAO,CAAEzB,IAAF,EAASmE,QAAD,IAAa;CAC3B,IAAA,IAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC;CACjCA,MAAAA,QAAQ,GAAGA,QAAQ,CAAC/B,IAAT,EAAgBtD,CAAAA,KAAhB,CAAsB,mBAAtB,CAAX,CAAA;CACA,KAAA;;CACD,IAAA,IAAIkC,KAAK,CAACC,OAAN,CAAckD,QAAd,CAAJ,EAA6B;CAC5BX,MAAAA,OAAO,GAAGA,OAAO,CAACY,MAAR,CAAeD,QAAf,CAAV,CAAA;CACA,KAAA;CACD,GAPM,CAAP,CAAA;CASA,EAAA,OAAOX,OAAO,CAACa,MAAR,CAAeC,OAAf,CAAP,CAAA;CACA,CAZM,CAAA;CAeP;CACA;CACA;CACA;;CACO,MAAMX,WAAW,GAAInB,GAAD,IAAwB;CAClD,EAAA,IAAI,CAACxB,KAAK,CAACC,OAAN,CAAcuB,GAAd,CAAL,EAAyB;CACvBA,IAAAA,GAAG,GAAG,CAACA,GAAD,CAAN,CAAA;CACA,GAAA;;CACF,EAAA,OAAOA,GAAP,CAAA;CACA,CALM,CAAA;CAQP;CACA;CACA;CACA;CACA;;CACO,MAAM+B,WAAW,GAAG,CAAEC,MAAF,EAA2BC,QAA3B,EAA4CC,OAA5C,KAAuF;CAEjH,EAAIA,IAAAA,OAAO,IAAI,CAACA,OAAO,CAACC,QAAR,CAAiBH,MAAjB,CAAhB,EAA0C;CACzC,IAAA,OAAA;CACA,GAAA;;CAED,EAAA,OAAOA,MAAM,IAAIA,MAAM,CAACI,OAAxB,EAAiC;CAEhC,IAAA,IAAIJ,MAAM,CAACI,OAAP,CAAeH,QAAf,CAAJ,EAA8B;CAC7B,MAAA,OAAOD,MAAP,CAAA;CACA,KAAA;;CAEDA,IAAAA,MAAM,GAAGA,MAAM,CAACK,UAAhB,CAAA;CACA,GAAA;CACD,CAdM,CAAA;CAiBP;CACA;CACA;CACA;CACA;CACA;CACA;;CACO,MAAMC,OAAO,GAAG,CAAEC,IAAF,EAA4BC,SAAgB,GAAC,CAA7C,KAAwD;CAE9E,EAAIA,IAAAA,SAAS,GAAG,CAAhB,EAAmB;CAClB,IAAA,OAAOD,IAAI,CAACA,IAAI,CAACpF,MAAL,GAAY,CAAb,CAAX,CAAA;CACA,GAAA;;CAED,EAAOoF,OAAAA,IAAI,CAAC,CAAD,CAAX,CAAA;CACA,CAPM,CAAA;CASP;CACA;CACA;CACA;;CACO,MAAME,aAAa,GAAIC,GAAD,IAAwB;CACpD,EAAQ/B,OAAAA,MAAM,CAACgC,IAAP,CAAYD,GAAZ,CAAiBvF,CAAAA,MAAjB,KAA4B,CAApC,CAAA;CACA,CAFM,CAAA;CAKP;CACA;CACA;CACA;;CACO,MAAMyF,SAAS,GAAG,CAAEvB,EAAF,EAAmBwB,OAAnB,KAA+C;CACvE,EAAA,IAAI,CAACxB,EAAL,EAAS,OAAO,CAAC,CAAR,CAAA;CAETwB,EAAAA,OAAO,GAAGA,OAAO,IAAIxB,EAAE,CAACyB,QAAxB,CAAA;CAEA,EAAIC,IAAAA,CAAC,GAAG,CAAR,CAAA;;CACA,EAAA,OAAO1B,EAAE,GAAGA,EAAE,CAAC2B,sBAAf,EAAuC;CAEtC,IAAA,IAAI3B,EAAE,CAACe,OAAH,CAAWS,OAAX,CAAJ,EAAyB;CACxBE,MAAAA,CAAC,EAAA,CAAA;CACD,KAAA;CACD,GAAA;;CACD,EAAA,OAAOA,CAAP,CAAA;CACA,CAbM,CAAA;CAgBP;CACA;CACA;CACA;;CACO,MAAME,OAAO,GAAG,CAAC5B,EAAD,EAAY6B,KAAZ,KAA4D;CAClFjE,EAAAA,OAAO,CAAEiE,KAAF,EAAQ,CAACC,GAAD,EAAKC,IAAL,KAAc;CAC5B,IAAID,IAAAA,GAAG,IAAI,IAAX,EAAiB;CAChB9B,MAAAA,EAAE,CAACgC,eAAH,CAAmBD,IAAnB,CAAA,CAAA;CACA,KAFD,MAEK;CACJ/B,MAAAA,EAAE,CAACiC,YAAH,CAAgBF,IAAhB,EAAgC,KAAGD,GAAnC,CAAA,CAAA;CACA,KAAA;CACD,GANM,CAAP,CAAA;CAOA,CARM,CAAA;CAWP;CACA;CACA;;CACO,MAAMI,WAAW,GAAG,CAAEC,QAAF,EAAiBC,WAAjB,KAAuC;CACjE,EAAA,IAAID,QAAQ,CAACnB,UAAb,EAA0BmB,QAAQ,CAACnB,UAAT,CAAoBqB,YAApB,CAAiCD,WAAjC,EAA8CD,QAA9C,CAAA,CAAA;CAC1B,CAFM;;CC/MP;CACA;CACA;CACA;CACA;CACA;CACA;CAKO,MAAMG,SAAS,GAAG,CAACC,OAAD,EAAsBC,KAAtB,KAA8C;CAEtE,EAAA,IAAIA,KAAK,KAAK,IAAd,EAAqB,OAFiD;;CAKtE,EAAA,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;CAE9B,IAAA,IAAI,CAACA,KAAK,CAAC1G,MAAX,EAAoB,OAAA;CACpB0G,IAAAA,KAAK,GAAG,IAAIC,MAAJ,CAAWD,KAAX,EAAkB,GAAlB,CAAR,CAAA;CACA,GATqE;CAatE;;;CACA,EAAME,MAAAA,aAAa,GAAKC,IAAF,IAAwB;CAE7C,IAAIC,IAAAA,KAAK,GAAGD,IAAI,CAACE,IAAL,CAAUD,KAAV,CAAgBJ,KAAhB,CAAZ,CAAA;;CACA,IAAII,IAAAA,KAAK,IAAID,IAAI,CAACE,IAAL,CAAU/G,MAAV,GAAmB,CAAhC,EAAmC;CAClC,MAAA,IAAIgH,QAAQ,GAAI1E,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAhB,CAAA;CACAyE,MAAAA,QAAQ,CAACC,SAAT,GAAqB,WAArB,CAAA;CACA,MAAIC,IAAAA,SAAS,GAAIL,IAAI,CAACM,SAAL,CAAeL,KAAK,CAACM,KAArB,CAAjB,CAAA;CAEAF,MAAAA,SAAS,CAACC,SAAV,CAAoBL,KAAK,CAAC,CAAD,CAAL,CAAU9G,MAA9B,CAAA,CAAA;CACA,MAAA,IAAIqH,WAAW,GAAIH,SAAS,CAACI,SAAV,CAAoB,IAApB,CAAnB,CAAA;CAEAN,MAAAA,QAAQ,CAACO,WAAT,CAAqBF,WAArB,CAAA,CAAA;CACAjB,MAAAA,WAAW,CAACc,SAAD,EAAYF,QAAZ,CAAX,CAAA;CACA,MAAA,OAAO,CAAP,CAAA;CACA,KAAA;;CAED,IAAA,OAAO,CAAP,CAAA;CACA,GAjBD,CAdsE;CAkCtE;;;CACA,EAAMQ,MAAAA,iBAAiB,GAAKX,IAAF,IAAyB;CAClD,IAAA,IAAIA,IAAI,CAACY,QAAL,KAAkB,CAAlB,IAAuBZ,IAAI,CAACa,UAA5B,IAA0C,CAAC,iBAAA,CAAkBC,IAAlB,CAAuBd,IAAI,CAACe,OAA5B,CAA3C,KAAqFf,IAAI,CAACI,SAAL,KAAmB,WAAnB,IAAkCJ,IAAI,CAACe,OAAL,KAAiB,MAAxI,CAAJ,EAAsJ;CACrJvG,MAAAA,KAAK,CAACwG,IAAN,CAAWhB,IAAI,CAACa,UAAhB,CAA4BtI,CAAAA,OAA5B,CAAoCqH,OAAO,IAAI;CAC9CqB,QAAAA,kBAAkB,CAACrB,OAAD,CAAlB,CAAA;CACA,OAFD,CAAA,CAAA;CAGA,KAAA;CACD,GAND,CAAA;;CASA,EAAMqB,MAAAA,kBAAkB,GAAKjB,IAAF,IAAgC;CAE1D,IAAA,IAAIA,IAAI,CAACY,QAAL,KAAkB,CAAtB,EAAyB;CACxB,MAAOb,OAAAA,aAAa,CAACC,IAAD,CAApB,CAAA;CACA,KAAA;;CAEDW,IAAAA,iBAAiB,CAACX,IAAD,CAAjB,CAAA;CAEA,IAAA,OAAO,CAAP,CAAA;CACA,GATD,CAAA;;CAWAiB,EAAAA,kBAAkB,CAAErB,OAAF,CAAlB,CAAA;CACA,CAxDM,CAAA;CA0DP;CACA;CACA;CACA;;CACO,MAAMsB,eAAe,GAAI7D,EAAD,IAAoB;CAClD,EAAA,IAAI8D,QAAQ,GAAG9D,EAAE,CAAC+D,gBAAH,CAAoB,gBAApB,CAAf,CAAA;CACA5G,EAAAA,KAAK,CAAC6G,SAAN,CAAgB9I,OAAhB,CAAwB+I,IAAxB,CAA6BH,QAA7B,EAAuC,UAAS9D,EAAT,EAAwB;CAC9D,IAAA,IAAIkE,MAAM,GAAGlE,EAAE,CAACgB,UAAhB,CAAA;CACAkD,IAAAA,MAAM,CAAC7B,YAAP,CAAoBrC,EAAE,CAACvB,UAAvB,EAA2CuB,EAA3C,CAAA,CAAA;CACAkE,IAAAA,MAAM,CAACC,SAAP,EAAA,CAAA;CACA,GAJD,CAAA,CAAA;CAKA,CAPM;;CCzEA,MAAMC,KAAK,GAAM,EAAjB,CAAA;CACA,MAAMC,UAAU,GAAK,EAArB,CAAA;CACA,MAAMC,OAAO,GAAK,EAAlB,CAAA;CACA,MAAMC,QAAQ,GAAK,EAAnB,CAAA;CACA,MAAMC,MAAM,GAAM,EAAlB,CAAA;CACA,MAAMC,SAAS,GAAK,EAApB,CAAA;CACA,MAAMC,QAAQ,GAAK,EAAnB,CAAA;CACA,MAAMC,aAAa,GAAI,CAAvB,CAAA;CACA,MAAMC,UAAU,GAAK,EAArB,CAAA;CACA,MAAMC,OAAO,GAAK,CAAlB,CAAA;CAEA,MAAMC,MAAM,GAAU,OAAOC,SAAP,KAAqB,WAArB,GAAmC,KAAnC,GAA2C,MAAMtB,IAAN,CAAWsB,SAAS,CAACC,SAArB,CAAjE,CAAA;CACA,MAAMC,YAAY,GAAIH,MAAM,GAAG,SAAH,GAAe,SAA3C;;ACXP,gBAAe;CACdxH,EAAAA,OAAO,EAAE,EADK;CAEd4H,EAAAA,SAAS,EAAE,EAFG;CAId1I,EAAAA,OAAO,EAAE,EAJK;CAKd2I,EAAAA,SAAS,EAAE,GALG;CAMdC,EAAAA,OAAO,EAAE,IANK;CAMC;CACfC,EAAAA,OAAO,EAAE,IAPK;CAQdC,EAAAA,UAAU,EAAE,IARE;CASdC,EAAAA,MAAM,EAAE,IATM;CAUdC,EAAAA,YAAY,EAAE,KAVA;CAWdC,EAAAA,YAAY,EAAE,IAXA;CAYdnD,EAAAA,SAAS,EAAE,IAZG;CAadoD,EAAAA,WAAW,EAAE,IAbC;CAcdC,EAAAA,UAAU,EAAE,IAdE;CAedC,EAAAA,UAAU,EAAE,EAfE;CAgBdC,EAAAA,QAAQ,EAAE,IAhBI;CAiBdC,EAAAA,YAAY,EAAE,IAjBA;CAkBdC,EAAAA,UAAU,EAAE,KAlBE;CAmBdC,EAAAA,aAAa,EAAE,KAnBD;CAoBdC,EAAAA,WAAW,EAAE,KApBC;CAqBdC,EAAAA,OAAO,EAAE,IArBK;CAsBdC,EAAAA,gBAAgB,EAAE,KAtBJ;CAuBd;CAEAC,EAAAA,YAAY,EAAE,GAzBA;CA0BdC,EAAAA,YAAY,EAAE,SA1BA;CA4BdC,EAAAA,QAAQ,EAAE,IA5BI;CA4BE;CAChBC,EAAAA,aAAa,EAAE,UA7BD;CA8BdC,EAAAA,UAAU,EAAE,OA9BE;CA+BdC,EAAAA,UAAU,EAAE,MA/BE;CAgCdC,EAAAA,aAAa,EAAE,UAhCD;CAiCdC,EAAAA,kBAAkB,EAAE,OAjCN;CAkCdC,EAAAA,kBAAkB,EAAE,OAlCN;CAmCdC,EAAAA,iBAAiB,EAAE,KAnCL;CAqCdC,EAAAA,SAAS,EAAE,QArCG;CAsCdC,EAAAA,WAAW,EAAE,CAAC,MAAD,CAtCC;CAuCdC,EAAAA,iBAAiB,EAAE,KAvCL;CAyCdC,EAAAA,IAAI,EAAE,IAzCQ;CA0CdC,EAAAA,YAAY,EAAE,YA1CA;CA2CdC,EAAAA,YAAY,EAAE,YA3CA;CA4CdC,EAAAA,aAAa,EAAE,aA5CD;CA6CdC,EAAAA,oBAAoB,EAAE,qBA7CR;CA8CdC,EAAAA,SAAS,EAAE,MA9CG;CA+CdC,EAAAA,WAAW,EAAE,QA/CC;CAiDdC,EAAAA,cAAc,EAAE,IAjDF;CAkDdC,EAAAA,YAAY,EAAE,mDAlDA;CAoDdC,EAAAA,qBAAqB,EAAE,KApDT;CAsDdC,EAAAA,WAAW,EAAE,IAtDC;CAuDdC,EAAAA,eAAe,EAAE,IAvDH;CAyDdC,EAAAA,UAAU,EAAE,UAAS9J,KAAT,EAA8B;CACzC,IAAA,OAAOA,KAAK,CAACjC,MAAN,GAAe,CAAtB,CAAA;CACA,GA3Da;;CA6Dd;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAECgM,EAAAA,MAAM,EAAE;CACP;CACF;CACA;CACA;CACA;CACA;CACA;CAPS,GAAA;CAjFM,CAAf;;CCIA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMC,QAAQ,GAAIC,KAAD,IAA4D;CACnF,EAAI,IAAA,OAAOA,KAAP,KAAiB,WAAjB,IAAgCA,KAAK,KAAK,IAA9C,EAAoD,OAAO,IAAP,CAAA;CACpD,EAAOC,OAAAA,QAAQ,CAACD,KAAD,CAAf,CAAA;CACA,CAHM,CAAA;CAKA,MAAMC,QAAQ,GAAID,KAAD,IAAwC;CAC/D,EAAI,IAAA,OAAOA,KAAP,KAAiB,SAArB,EAAgC,OAAOA,KAAK,GAAG,GAAH,GAAS,GAArB,CAAA;CAChC,EAAOA,OAAAA,KAAK,GAAG,EAAf,CAAA;CACA,CAHM,CAAA;CAKP;CACA;CACA;CACA;;CACO,MAAME,WAAW,GAAIC,GAAD,IAAuB;CACjD,EAAA,OAAO,CAACA,GAAG,GAAG,EAAP,EACLtJ,OADK,CACG,IADH,EACS,OADT,CAELA,CAAAA,OAFK,CAEG,IAFH,EAES,MAFT,CAGLA,CAAAA,OAHK,CAGG,IAHH,EAGS,MAHT,CAILA,CAAAA,OAJK,CAIG,IAJH,EAIS,QAJT,CAAP,CAAA;CAKA,CANM,CAAA;CASP;CACA;CACA;CACA;;CACO,MAAMuJ,YAAY,GAAG,CAACrL,EAAD,EAAoDsL,KAApD,KAAqE;CAChG,EAAA,IAAIC,OAAJ,CAAA;CACA,EAAA,OAAO,UAAyBN,KAAzB,EAAsChN,QAAtC,EAAgE;CACtE,IAAIoB,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAA,IAAIkM,OAAJ,EAAa;CACZlM,MAAAA,IAAI,CAACmM,OAAL,GAAeC,IAAI,CAACC,GAAL,CAASrM,IAAI,CAACmM,OAAL,GAAe,CAAxB,EAA2B,CAA3B,CAAf,CAAA;CACAG,MAAAA,YAAY,CAACJ,OAAD,CAAZ,CAAA;CACA,KAAA;;CACDA,IAAAA,OAAO,GAAGK,UAAU,CAAC,YAAW;CAC/BL,MAAAA,OAAO,GAAG,IAAV,CAAA;CACAlM,MAAAA,IAAI,CAACwM,cAAL,CAAoBZ,KAApB,IAA6B,IAA7B,CAAA;CACAjL,MAAAA,EAAE,CAACkH,IAAH,CAAQ7H,IAAR,EAAc4L,KAAd,EAAqBhN,QAArB,CAAA,CAAA;CAEA,KALmB,EAKjBqN,KALiB,CAApB,CAAA;CAMA,GAbD,CAAA;CAcA,CAhBM,CAAA;CAmBP;CACA;CACA;CACA;CACA;;CACO,MAAMQ,eAAe,GAAG,CAAEzM,IAAF,EAAkB0M,KAAlB,EAAkC/L,EAAlC,KAAqD;CACnF,EAAA,IAAIgM,IAAJ,CAAA;CACA,EAAA,IAAI7M,OAAO,GAAGE,IAAI,CAACF,OAAnB,CAAA;CACA,EAAA,IAAI8M,UAAiC,GAAG,EAAxC,CAHmF;;CAMnF5M,EAAAA,IAAI,CAACF,OAAL,GAAe,YAAU;CACxB,IAAA,IAAI6M,IAAI,GAAGlN,SAAS,CAAC,CAAD,CAApB,CAAA;;CACA,IAAIiN,IAAAA,KAAK,CAAC7M,OAAN,CAAc8M,IAAd,CAAwB,KAAA,CAAC,CAA7B,EAAgC;CAC/BC,MAAAA,UAAU,CAACD,IAAD,CAAV,GAAmBlN,SAAnB,CAAA;CACA,KAFD,MAEO;CACN,MAAA,OAAOK,OAAO,CAACG,KAAR,CAAcD,IAAd,EAAoBP,SAApB,CAAP,CAAA;CACA,KAAA;CACD,GAPD,CANmF;;;CAgBnFkB,EAAAA,EAAE,CAACV,KAAH,CAASD,IAAT,EAAe,EAAf,CAAA,CAAA;CACAA,EAAAA,IAAI,CAACF,OAAL,GAAeA,OAAf,CAjBmF;;CAoBnF,EAAK6M,KAAAA,IAAL,IAAaD,KAAb,EAAoB;CACnB,IAAIC,IAAAA,IAAI,IAAIC,UAAZ,EAAwB;CACvB9M,MAAAA,OAAO,CAACG,KAAR,CAAcD,IAAd,EAAoB4M,UAAU,CAACD,IAAD,CAA9B,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CACD,CAzBM,CAAA;CA4BP;CACA;CACA;CACA;CACA;CACA;CACA;;CACO,MAAME,YAAY,GAAIC,KAAD,IAA8D;CACzF,EAAO,OAAA;CACNC,IAAAA,KAAK,EAAGD,KAAK,CAACE,cAAN,IAAwB,CAD1B;CAENtN,IAAAA,MAAM,EAAG,CAACoN,KAAK,CAACG,YAAN,IAAoB,CAArB,KAA2BH,KAAK,CAACE,cAAN,IAAsB,CAAjD,CAAA;CAFH,GAAP,CAAA;CAIA,CALM,CAAA;CAQP;CACA;CACA;CACA;;CACO,MAAME,cAAc,GAAG,CAACC,GAAD,EAAaC,IAAY,GAAC,KAA1B,KAAyC;CACtE,EAAA,IAAID,GAAJ,EAAS;CACRA,IAAAA,GAAG,CAACD,cAAJ,EAAA,CAAA;;CACA,IAAA,IAAIE,IAAJ,EAAU;CACTD,MAAAA,GAAG,CAACE,eAAJ,EAAA,CAAA;CACA,KAAA;CACD,GAAA;CACD,CAPM,CAAA;CAUP;CACA;CACA;CACA;;CACO,MAAMC,QAAQ,GAAG,CAAC/I,MAAD,EAAqBoI,IAArB,EAAkC/N,QAAlC,EAA+EsC,OAA/E,KAAwG;CAC/HqD,EAAAA,MAAM,CAACgJ,gBAAP,CAAwBZ,IAAxB,EAA6B/N,QAA7B,EAAsCsC,OAAtC,CAAA,CAAA;CACA,CAFM,CAAA;CAKP;CACA;CACA;CACA;CACA;CACA;;CACO,MAAMsM,SAAS,GAAG,CAAEC,QAAF,EAA6CN,GAA7C,KAAgF;CAExG,EAAI,IAAA,CAACA,GAAL,EAAU;CACT,IAAA,OAAO,KAAP,CAAA;CACA,GAAA;;CAED,EAAA,IAAI,CAACA,GAAG,CAACM,QAAD,CAAR,EAAoB;CACnB,IAAA,OAAO,KAAP,CAAA;CACA,GAAA;;CAED,EAAA,IAAIC,KAAK,GAAG,CAACP,GAAG,CAACQ,MAAJ,GAAW,CAAX,GAAa,CAAd,KAAoBR,GAAG,CAACS,OAAJ,GAAY,CAAZ,GAAc,CAAlC,CAAA,IAAwCT,GAAG,CAACU,QAAJ,GAAa,CAAb,GAAe,CAAvD,CAA6DV,IAAAA,GAAG,CAACW,OAAJ,GAAY,CAAZ,GAAc,CAA3E,CAAZ,CAAA;;CAEA,EAAIJ,IAAAA,KAAK,KAAK,CAAd,EAAiB;CAChB,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;;CAED,EAAA,OAAO,KAAP,CAAA;CACA,CAjBM,CAAA;CAoBP;CACA;CACA;CACA;CACA;;CACO,MAAMK,KAAK,GAAG,CAACnK,EAAD,EAAYoK,EAAZ,KAA0B;CAC9C,EAAA,MAAMC,WAAW,GAAGrK,EAAE,CAACsK,YAAH,CAAgB,IAAhB,CAApB,CAAA;;CACA,EAAA,IAAID,WAAJ,EAAiB;CAChB,IAAA,OAAOA,WAAP,CAAA;CACA,GAAA;;CAEDrK,EAAAA,EAAE,CAACiC,YAAH,CAAgB,IAAhB,EAAqBmI,EAArB,CAAA,CAAA;CACA,EAAA,OAAOA,EAAP,CAAA;CACA,CARM,CAAA;CAWP;CACA;CACA;;CACO,MAAMG,UAAU,GAAIpC,GAAD,IAAuB;CAChD,EAAA,OAAOA,GAAG,CAACtJ,OAAJ,CAAY,SAAZ,EAAuB,MAAvB,CAAP,CAAA;CACA,CAFM,CAAA;CAIP;CACA;CACA;;CACO,MAAM2L,MAAM,GAAG,CAAEtG,MAAF,EAAmCvB,IAAnC,KAA6E;CAClG,EAAA,IAAIA,IAAJ,EAAWuB,MAAM,CAACsG,MAAP,CAAc7H,IAAd,CAAA,CAAA;CACX,CAFM;;CCrLQ,SAAS8H,WAAT,CAAsBvB,KAAtB,EAAsCwB,aAAtC,EAA8F;CAC5G,EAAIhO,IAAAA,QAAoB,GAAG4C,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBoL,QAAlB,EAA4BD,aAA5B,CAA3B,CAAA;CAEA,EAAA,IAAIE,SAAS,GAAMlO,QAAQ,CAAC4J,QAA5B,CAAA;CACA,EAAA,IAAIuE,WAAW,GAAMnO,QAAQ,CAAC+J,UAA9B,CAAA;CACA,EAAA,IAAIqE,WAAW,GAAMpO,QAAQ,CAAC8J,UAA9B,CAAA;CACA,EAAA,IAAIuE,cAAc,GAAKrO,QAAQ,CAACgK,aAAhC,CAAA;CACA,EAAA,IAAIsE,cAAc,GAAKtO,QAAQ,CAAC6J,aAAhC,CAAA;CACA,EAAA,IAAI0E,oBAAoB,GAAGvO,QAAQ,CAACiK,kBAApC,CAAA;CACA,EAAA,IAAIuE,oBAAoB,GAAGxO,QAAQ,CAACkK,kBAApC,CAAA;CAEA,EAAA,IAAIuE,QAAQ,GAAMjC,KAAK,CAACxF,OAAN,CAAc0H,WAAd,EAAlB,CAAA;CACA,EAAA,IAAIzD,WAAW,GAAMuB,KAAK,CAACoB,YAAN,CAAmB,aAAnB,CAAA,IAAqCpB,KAAK,CAACoB,YAAN,CAAmB,kBAAnB,CAA1D,CAAA;;CAEA,EAAA,IAAI,CAAC3C,WAAD,IAAgB,CAACjL,QAAQ,CAACyJ,gBAA9B,EAAgD;CAC/C,IAAA,IAAIkF,MAAM,GAAInC,KAAK,CAACxK,aAAN,CAAoB,kBAApB,CAAd,CAAA;;CACA,IAAA,IAAI2M,MAAJ,EAAY;CACX1D,MAAAA,WAAW,GAAG0D,MAAM,CAACC,WAArB,CAAA;CACA,KAAA;CAED,GAAA;;CAED,EAAA,IAAIC,gBAMH,GAAG;CACH5D,IAAAA,WAAW,EAAGA,WADX;CAEHrK,IAAAA,OAAO,EAAI,EAFR;CAGH4H,IAAAA,SAAS,EAAG,EAHT;CAIHsG,IAAAA,KAAK,EAAI,EAJN;CAKH3F,IAAAA,QAAQ,EAAG,IAAA;CALR,GANJ,CAAA;CAeA;CACD;CACA;CACA;;CACC,EAAI4F,IAAAA,WAAW,GAAG,MAAM;CACvB,IAAA,IAAI/H,OAAJ,CAAA;CACA,IAAA,IAAIpG,OAAO,GAAGiO,gBAAgB,CAACjO,OAA/B,CAAA;CACA,IAAIoO,IAAAA,UAA6B,GAAG,EAApC,CAAA;CACA,IAAIC,IAAAA,WAAW,GAAG,CAAlB,CAAA;;CAEA,IAAIC,IAAAA,QAAQ,GAAI5L,EAAD,IAA8B;CAE5C,MAAA,IAAI6C,IAAI,GAAGvD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAiBS,EAAE,CAAC6L,OAApB,CAAX,CAF4C;;CAG5C,MAAA,IAAIC,IAAI,GAAGlB,SAAS,IAAI/H,IAAI,CAAC+H,SAAD,CAA5B,CAAA;;CAEA,MAAI,IAAA,OAAOkB,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,CAAChQ,MAArC,EAA6C;CAC5C+G,QAAAA,IAAI,GAAGvD,MAAM,CAACC,MAAP,CAAcsD,IAAd,EAAmBkJ,IAAI,CAACC,KAAL,CAAWF,IAAX,CAAnB,CAAP,CAAA;CACA,OAAA;;CAED,MAAA,OAAOjJ,IAAP,CAAA;CACA,KAVD,CAAA;;CAYA,IAAA,IAAIoJ,SAAS,GAAG,CAACZ,MAAD,EAA2Ba,KAA3B,KAA6C;CAE5D,MAAA,IAAIlE,KAAK,GAAGD,QAAQ,CAACsD,MAAM,CAACrD,KAAR,CAApB,CAAA;CACA,MAAKA,IAAAA,KAAK,IAAI,IAAd,EAAqB,OAAA;CACrB,MAAK,IAAA,CAACA,KAAD,IAAU,CAACtL,QAAQ,CAACyJ,gBAAzB,EAA2C,OAJiB;CAO5D;CACA;CACA;;CACA,MAAA,IAAIuF,UAAU,CAACnO,cAAX,CAA0ByK,KAA1B,CAAJ,EAAsC;CACrC,QAAA,IAAIkE,KAAJ,EAAW;CACV,UAAIC,IAAAA,GAAG,GAAGT,UAAU,CAAC1D,KAAD,CAAV,CAAkBgD,cAAlB,CAAV,CAAA;;CACA,UAAI,IAAA,CAACmB,GAAL,EAAU;CACTT,YAAAA,UAAU,CAAC1D,KAAD,CAAV,CAAkBgD,cAAlB,IAAoCkB,KAApC,CAAA;CACA,WAFD,MAEO,IAAI,CAAC/O,KAAK,CAACC,OAAN,CAAc+O,GAAd,CAAL,EAAyB;CAC/BT,YAAAA,UAAU,CAAC1D,KAAD,CAAV,CAAkBgD,cAAlB,CAAA,GAAoC,CAACmB,GAAD,EAAMD,KAAN,CAApC,CAAA;CACA,WAFM,MAEA;CACNC,YAAAA,GAAG,CAACzQ,IAAJ,CAASwQ,KAAT,CAAA,CAAA;CACA,WAAA;CACD,SAAA;CAED,OAZD,MAYK;CAEJ,QAAA,IAAIE,WAAW,GAAeR,QAAQ,CAACP,MAAD,CAAtC,CAAA;CACAe,QAAAA,WAAW,CAACvB,WAAD,CAAX,GAA8BuB,WAAW,CAACvB,WAAD,CAAX,IAA4BQ,MAAM,CAACC,WAAjE,CAAA;CACAc,QAAAA,WAAW,CAACtB,WAAD,CAAX,GAA8BsB,WAAW,CAACtB,WAAD,CAAX,IAA4B9C,KAA1D,CAAA;CACAoE,QAAAA,WAAW,CAACrB,cAAD,CAAX,GAA8BqB,WAAW,CAACrB,cAAD,CAAX,IAA+BM,MAAM,CAACgB,QAApE,CAAA;CACAD,QAAAA,WAAW,CAACpB,cAAD,CAAX,GAA8BoB,WAAW,CAACpB,cAAD,CAAX,IAA+BkB,KAA7D,CAAA;CACAE,QAAAA,WAAW,CAACE,OAAZ,GAAwBjB,MAAxB,CAAA;CAEAK,QAAAA,UAAU,CAAC1D,KAAD,CAAV,GAAoBoE,WAApB,CAAA;CACA9O,QAAAA,OAAO,CAAC5B,IAAR,CAAa0Q,WAAb,CAAA,CAAA;CACA,OAAA;;CAED,MAAIf,IAAAA,MAAM,CAACkB,QAAX,EAAqB;CACpBhB,QAAAA,gBAAgB,CAACC,KAAjB,CAAuB9P,IAAvB,CAA4BsM,KAA5B,CAAA,CAAA;CACA,OAAA;CACD,KAtCD,CAAA;;CAwCA,IAAIwE,IAAAA,QAAQ,GAAKC,QAAF,IAAoC;CAClD,MAAIrC,IAAAA,EAAJ,EAAesC,aAAf,CAAA;CAEAA,MAAAA,aAAa,GAASd,QAAQ,CAACa,QAAD,CAA9B,CAAA;CACAC,MAAAA,aAAa,CAACzB,oBAAD,CAAb,GAAuCyB,aAAa,CAACzB,oBAAD,CAAb,IAAuCwB,QAAQ,CAACnC,YAAT,CAAsB,OAAtB,CAAvC,IAAyE,EAAhH,CAAA;CACAoC,MAAAA,aAAa,CAACxB,oBAAD,CAAb,GAAuCwB,aAAa,CAACxB,oBAAD,CAAb,IAAuCS,WAAW,EAAzF,CAAA;CACAe,MAAAA,aAAa,CAAC3B,cAAD,CAAb,GAAkC2B,aAAa,CAAC3B,cAAD,CAAb,IAAiC0B,QAAQ,CAACJ,QAA5E,CAAA;CACAd,MAAAA,gBAAgB,CAACrG,SAAjB,CAA2BxJ,IAA3B,CAAgCgR,aAAhC,CAAA,CAAA;CAEAtC,MAAAA,EAAE,GAAGsC,aAAa,CAACxB,oBAAD,CAAlB,CAAA;CAEAtN,MAAAA,OAAO,CAAC6O,QAAQ,CAACE,QAAV,EAAqBtB,MAAD,IAAU;CACpCY,QAAAA,SAAS,CAACZ,MAAD,EAA8BjB,EAA9B,CAAT,CAAA;CACA,OAFM,CAAP,CAAA;CAIA,KAfD,CAAA;;CAiBAmB,IAAAA,gBAAgB,CAAC1F,QAAjB,GAA4BqD,KAAK,CAAC0D,YAAN,CAAmB,UAAnB,CAAA,GAAiC,IAAjC,GAAwC,CAApE,CAAA;CAEAhP,IAAAA,OAAO,CAACsL,KAAK,CAACyD,QAAP,EAAiBE,KAAD,IAAS;CAC/BnJ,MAAAA,OAAO,GAAGmJ,KAAK,CAACnJ,OAAN,CAAc0H,WAAd,EAAV,CAAA;;CACA,MAAI1H,IAAAA,OAAO,KAAK,UAAhB,EAA4B;CAC3B8I,QAAAA,QAAQ,CAACK,KAAD,CAAR,CAAA;CACA,OAFD,MAEO,IAAInJ,OAAO,KAAK,QAAhB,EAA0B;CAChCuI,QAAAA,SAAS,CAACY,KAAD,CAAT,CAAA;CACA,OAAA;CACD,KAPM,CAAP,CAAA;CASA,GAtFD,CAAA;CAyFA;CACD;CACA;CACA;;;CACC,EAAIC,IAAAA,YAAY,GAAG,MAAM;CACxB,IAAA,MAAMC,QAAQ,GAAG7D,KAAK,CAACoB,YAAN,CAAmBM,SAAnB,CAAjB,CAAA;;CAEA,IAAI,IAAA,CAACmC,QAAL,EAAe;CACd,MAAI/E,IAAAA,KAAK,GAAGkB,KAAK,CAAClB,KAAN,CAAYzJ,IAAZ,MAAsB,EAAlC,CAAA;CACA,MAAI,IAAA,CAAC7B,QAAQ,CAACyJ,gBAAV,IAA8B,CAAC6B,KAAK,CAAClM,MAAzC,EAAiD,OAAA;CACjD,MAAMkR,MAAAA,MAAM,GAAGhF,KAAK,CAAC/M,KAAN,CAAYyB,QAAQ,CAACyI,SAArB,CAAf,CAAA;CAEAvH,MAAAA,OAAO,CAAEoP,MAAF,EAAWhF,KAAD,IAAW;CAC3B,QAAMqD,MAAAA,MAAgB,GAAG,EAAzB,CAAA;CACAA,QAAAA,MAAM,CAACR,WAAD,CAAN,GAAsB7C,KAAtB,CAAA;CACAqD,QAAAA,MAAM,CAACP,WAAD,CAAN,GAAsB9C,KAAtB,CAAA;CACAuD,QAAAA,gBAAgB,CAACjO,OAAjB,CAAyB5B,IAAzB,CAA8B2P,MAA9B,CAAA,CAAA;CACA,OALM,CAAP,CAAA;CAMAE,MAAAA,gBAAgB,CAACC,KAAjB,GAAyBwB,MAAzB,CAAA;CACA,KAZD,MAYO;CACNzB,MAAAA,gBAAgB,CAACjO,OAAjB,GAA2ByO,IAAI,CAACC,KAAL,CAAWe,QAAX,CAA3B,CAAA;CACAnP,MAAAA,OAAO,CAAE2N,gBAAgB,CAACjO,OAAnB,EAA6B2P,GAAD,IAAS;CAC3C1B,QAAAA,gBAAgB,CAACC,KAAjB,CAAuB9P,IAAvB,CAA4BuR,GAAG,CAACnC,WAAD,CAA/B,CAAA,CAAA;CACA,OAFM,CAAP,CAAA;CAGA,KAAA;CACD,GArBD,CAAA;;CAwBA,EAAIK,IAAAA,QAAQ,KAAK,QAAjB,EAA2B;CAC1BM,IAAAA,WAAW,EAAA,CAAA;CACX,GAFD,MAEO;CACNqB,IAAAA,YAAY,EAAA,CAAA;CACZ,GAAA;;CAED,EAAOxN,OAAAA,MAAM,CAACC,MAAP,CAAe,EAAf,EAAmBoL,QAAnB,EAA6BY,gBAA7B,EAA+Cb,aAA/C,CAAP,CAAA;CACA;;CCpID,IAAIwC,UAAU,GAAG,CAAjB,CAAA;CAEe,MAAMC,SAAN,SAAwB7Q,WAAW,CAAClB,UAAD,CAAnC,CAA+C;CAwBtB;CAyBvCC,EAAAA,WAAW,CAAE+R,SAAF,EAA8BC,aAA9B,EAA2E;CACrF,IAAA,KAAA,EAAA,CAAA;CADqF,IAAA,IAAA,CA/C/EC,aA+C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA9C/EzM,OA8C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA7C/E0M,QA6C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA5C/EC,OA4C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA3C/EC,gBA2C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA1C/EC,UA0C+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAxC/EC,IAAAA,CAAAA,KAwC+E,GAxC1D,CAwC0D,CAAA;CAAA,IAAA,IAAA,CAvC/EjR,QAuC+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CAtC/EwM,KAsC+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CArC/E0E,QAqC+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CApC/EC,aAoC+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CAnC/EC,GAmC+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CAlC9EC,OAkC8E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CAhC9EC,QAgC8E,GAAA,KAAA,CAAA,CAAA;CAAA,IAAA,IAAA,CA/B/EC,MA+B+E,GAAA,KAAA,CAAA,CAAA;CAAA,IA5B/EC,IAAAA,CAAAA,MA4B+E,GA5BxD,KA4BwD,CAAA;CAAA,IA3B/EC,IAAAA,CAAAA,UA2B+E,GA3BrD,KA2BqD,CAAA;CAAA,IAAA,IAAA,CA1B/EC,UA0B+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAzB/EC,IAAAA,CAAAA,SAyB+E,GAzBtD,KAyBsD,CAAA;CAAA,IAxB/EC,IAAAA,CAAAA,OAwB+E,GAxBvD,IAwBuD,CAAA;CAAA,IAvB/EC,IAAAA,CAAAA,QAuB+E,GAvBtD,KAuBsD,CAAA;CAAA,IAtB/EC,IAAAA,CAAAA,SAsB+E,GAtBtD,KAsBsD,CAAA;CAAA,IArB/EC,IAAAA,CAAAA,aAqB+E,GArBnD,KAqBmD,CAAA;CAAA,IApB/EC,IAAAA,CAAAA,OAoB+E,GApBvD,KAoBuD,CAAA;CAAA,IAnB/EC,IAAAA,CAAAA,WAmB+E,GAnBpD,KAmBoD,CAAA;CAAA,IAlB/EC,IAAAA,CAAAA,WAkB+E,GAlBpD,KAkBoD,CAAA;CAAA,IAjB/EC,IAAAA,CAAAA,UAiB+E,GAjBrD,KAiBqD,CAAA;CAAA,IAAA,IAAA,CAhB/EC,cAgB+E,GAAA,KAAA,CAAA,CAAA;CAAA,IAf/EC,IAAAA,CAAAA,SAe+E,GAfvD,EAeuD,CAAA;CAAA,IAd/EC,IAAAA,CAAAA,QAc+E,GAdvD,CAcuD,CAAA;CAAA,IAb/EzG,IAAAA,CAAAA,OAa+E,GAbxD,CAawD,CAAA;CAAA,IAZ/EK,IAAAA,CAAAA,cAY+E,GAZ/B,EAY+B,CAAA;CAAA,IAV/EqG,IAAAA,CAAAA,YAU+E,GAV1C,IAU0C,CAAA;CAAA,IAT/EC,IAAAA,CAAAA,WAS+E,GATlD,EASkD,CAAA;CAAA,IAP/EhK,IAAAA,CAAAA,SAO+E,GAPnD,EAOmD,CAAA;CAAA,IAN/E5H,IAAAA,CAAAA,OAM+E,GANpD,EAMoD,CAAA;CAAA,IAL/E6R,IAAAA,CAAAA,WAK+E,GALrC,EAKqC,CAAA;CAAA,IAJ/E3D,IAAAA,CAAAA,KAI+E,GAJxD,EAIwD,CAAA;CAGrF0B,IAAAA,UAAU,EAAA,CAAA;CAEV,IAAA,IAAIkC,GAAJ,CAAA;CACA,IAAA,IAAIlG,KAAK,GAAMpL,MAAM,CAAEsP,SAAF,CAArB,CAAA;;CAEA,IAAIlE,IAAAA,KAAK,CAACmG,SAAV,EAAqB;CACpB,MAAA,MAAM,IAAI1R,KAAJ,CAAU,gDAAV,CAAN,CAAA;CACA,KAAA;;CAGDuL,IAAAA,KAAK,CAACmG,SAAN,GAAoB,IAApB,CAbqF;;CAiBrF,IAAA,IAAIC,aAAa,GAAIC,MAAM,CAACC,gBAAP,IAA2BD,MAAM,CAACC,gBAAP,CAAwBtG,KAAxB,EAA+B,IAA/B,CAAhD,CAAA;CACAkG,IAAAA,GAAG,GAAQE,aAAa,CAACG,gBAAd,CAA+B,WAA/B,CAAX,CAlBqF;;CAqBrF,IAAA,MAAM/S,QAAQ,GAAK+N,WAAW,CAAEvB,KAAF,EAASmE,aAAT,CAA9B,CAAA;CACA,IAAK3Q,IAAAA,CAAAA,QAAL,GAAkBA,QAAlB,CAAA;CACA,IAAKwM,IAAAA,CAAAA,KAAL,GAAgBA,KAAhB,CAAA;CACA,IAAA,IAAA,CAAK0E,QAAL,GAAkB1E,KAAK,CAAC0E,QAAN,IAAkB,CAApC,CAAA;CACA,IAAKC,IAAAA,CAAAA,aAAL,GAAsB3E,KAAK,CAACxF,OAAN,CAAc0H,WAAd,OAAgC,QAAtD,CAAA;CACA,IAAA,IAAA,CAAK0C,GAAL,GAAc,MAAA,CAAOrK,IAAP,CAAY2L,GAAZ,CAAd,CAAA;CACA,IAAKrB,IAAAA,CAAAA,OAAL,GAAiB5D,KAAK,CAACjB,KAAD,EAAQ,YAAA,GAAagE,UAArB,CAAtB,CAAA;CACA,IAAA,IAAA,CAAKkB,UAAL,GAAoBlF,KAAK,CAACwG,QAA1B,CA5BqF;;CAgCrF,IAAA,IAAA,CAAKzB,MAAL,GAAc,IAAI0B,MAAJ,CAAW,IAAA,CAAKrS,OAAhB,EAAyB;CAACgI,MAAAA,UAAU,EAAE5I,QAAQ,CAAC4I,UAAAA;CAAtB,KAAzB,CAAd,CAhCqF;;CAmCrF5I,IAAAA,QAAQ,CAACuK,IAAT,GAAgBvK,QAAQ,CAACuK,IAAT,KAAkBvK,QAAQ,CAACmJ,QAAT,KAAsB,CAAtB,GAA0B,QAA1B,GAAqC,OAAvD,CAAhB,CAAA;;CACA,IAAA,IAAI,OAAOnJ,QAAQ,CAACoJ,YAAhB,KAAiC,SAArC,EAAgD;CAC/CpJ,MAAAA,QAAQ,CAACoJ,YAAT,GAAwBpJ,QAAQ,CAACuK,IAAT,KAAkB,OAA1C,CAAA;CACA,KAAA;;CAED,IAAA,IAAI,OAAOvK,QAAQ,CAACkL,eAAhB,KAAoC,SAAxC,EAAmD;CAClDlL,MAAAA,QAAQ,CAACkL,eAAT,GAA2BlL,QAAQ,CAACuK,IAAT,KAAkB,OAA7C,CAAA;CACA,KA1CoF;;;CA6CrF,IAAA,IAAIzG,MAAM,GAAG9D,QAAQ,CAAC+I,YAAtB,CAAA;;CACA,IAAA,IAAI,OAAOjF,MAAP,KAAkB,UAAtB,EAAkC;CAEjC,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;CAC/BA,QAAAA,MAAM,GAAG,IAAIiC,MAAJ,CAAWjC,MAAX,CAAT,CAAA;CACA,OAAA;;CAED,MAAIA,IAAAA,MAAM,YAAYiC,MAAtB,EAA8B;CAC7B/F,QAAAA,QAAQ,CAAC+I,YAAT,GAAyByD,KAAD,IAAY1I,MAAD,CAAmBiD,IAAnB,CAAwByF,KAAxB,CAAnC,CAAA;CACA,OAFD,MAEK;CACJxM,QAAAA,QAAQ,CAAC+I,YAAT,GAAyBuC,KAAD,IAAW;CAClC,UAAO,OAAA,IAAA,CAAKtL,QAAL,CAAcqJ,UAAd,IAA4B,CAAC,IAAKzI,CAAAA,OAAL,CAAa0K,KAAb,CAApC,CAAA;CACA,SAFD,CAAA;CAGA,OAAA;CACD,KAAA;;CAGD,IAAA,IAAA,CAAKhL,iBAAL,CAAuBN,QAAQ,CAACF,OAAhC,CAAA,CAAA;CACA,IAAA,IAAA,CAAKoT,cAAL,EAAA,CAAA;CACA,IAAKC,IAAAA,CAAAA,cAAL,GAhEqF;;CAoErF,IAAA,MAAMhP,OAAO,GAAK/C,MAAM,CAAC,OAAD,CAAxB,CAAA;CACA,IAAA,MAAM0P,OAAO,GAAK1P,MAAM,CAAC,OAAD,CAAxB,CAAA;;CACA,IAAA,MAAMyP,QAAQ,GAAK,IAAA,CAAKuC,OAAL,CAAa,UAAb,CAAnB,CAAA;;CACA,IAAA,MAAMrC,gBAAgB,GAAG3P,MAAM,CAAE,oCAAF,CAA/B,CAAA;CAEA,IAAM6B,MAAAA,OAAO,GAAK,IAAKuJ,CAAAA,KAAL,CAAWoB,YAAX,CAAwB,OAAxB,CAAA,IAAoC,EAAtD,CAAA;CACA,IAAA,MAAMyF,SAAS,GAAKrT,QAAQ,CAACuK,IAA7B,CAAA;CAEA,IAAA,IAAIqG,aAAJ,CAAA;CAGA7N,IAAAA,UAAU,CAAEoB,OAAF,EAAWnE,QAAQ,CAACwK,YAApB,EAAkCvH,OAAlC,EAA2CoQ,SAA3C,CAAV,CAAA;CAGAtQ,IAAAA,UAAU,CAAC+N,OAAD,EAAS9Q,QAAQ,CAACyK,YAAlB,CAAV,CAAA;CACAqD,IAAAA,MAAM,CAAE3J,OAAF,EAAW2M,OAAX,CAAN,CAAA;CAGA/N,IAAAA,UAAU,CAAC8N,QAAD,EAAW7Q,QAAQ,CAAC0K,aAApB,EAAmC2I,SAAnC,CAAV,CAAA;;CACA,IAAIrT,IAAAA,QAAQ,CAACgL,qBAAb,EAAoC;CACnCjI,MAAAA,UAAU,CAAE8N,QAAF,EAAY5N,OAAZ,CAAV,CAAA;CACA,KAAA;;CAGDF,IAAAA,UAAU,CAACgO,gBAAD,EAAmB/Q,QAAQ,CAAC2K,oBAA5B,CAAV,CAAA;CACAmD,IAAAA,MAAM,CAAE+C,QAAF,EAAYE,gBAAZ,CAAN,CAAA;CAEA3P,IAAAA,MAAM,CAAEpB,QAAQ,CAAC8K,cAAT,IAA2B3G,OAA7B,CAAN,CAA6CwC,WAA7C,CAA0DkK,QAA1D,EA/FqF;;CAmGrF,IAAA,IAAIrP,YAAY,CAACxB,QAAQ,CAAC+K,YAAV,CAAhB,EAAyC;CACxC6F,MAAAA,aAAa,GAAIxP,MAAM,CAACpB,QAAQ,CAAC+K,YAAV,CAAvB,CADwC;;CAIxC,MAAI5F,IAAAA,KAAK,GAAG,CAAC,aAAD,EAAe,gBAAf,EAAgC,cAAhC,CAAZ,CAAA;CACAjE,MAAAA,SAAO,CAACiE,KAAD,EAAQE,IAAD,IAAiB;CAC9B,QAAA,IAAImH,KAAK,CAACoB,YAAN,CAAmBvI,IAAnB,CAAJ,EAA8B;CAC7BH,UAAAA,OAAO,CAAC0L,aAAD,EAAe;CAAC,YAAA,CAACvL,IAAD,GAAOmH,KAAK,CAACoB,YAAN,CAAmBvI,IAAnB,CAAA;CAAR,WAAf,CAAP,CAAA;CACA,SAAA;CACD,OAJM,CAAP,CAAA;CAMAuL,MAAAA,aAAa,CAACM,QAAd,GAAyB,CAAC,CAA1B,CAAA;CACAJ,MAAAA,OAAO,CAACnK,WAAR,CAAqBiK,aAArB,CAAA,CAAA;CACA,MAAA,IAAA,CAAKI,UAAL,GAAmBJ,aAAnB,CAbwC;CAgBxC,KAhBD,MAgBM,IAAI5Q,QAAQ,CAAC+K,YAAb,EAA2B;CAChC6F,MAAAA,aAAa,GAAIxP,MAAM,CAAEpB,QAAQ,CAAC+K,YAAX,CAAvB,CAAA;CACA,MAAKiG,IAAAA,CAAAA,UAAL,GAAmBJ,aAAnB,CAAA;CAEA,KAJK,MAID;CACJA,MAAAA,aAAa,GAAIxP,MAAM,CAAC,UAAD,CAAvB,CAAA;CACA,MAAK4P,IAAAA,CAAAA,UAAL,GAAmBF,OAAnB,CAAA;CACA,KAAA;;CAED,IAAK3M,IAAAA,CAAAA,OAAL,GAAiBA,OAAjB,CAAA;CACA,IAAK0M,IAAAA,CAAAA,QAAL,GAAkBA,QAAlB,CAAA;CACA,IAAKE,IAAAA,CAAAA,gBAAL,GAAwBA,gBAAxB,CAAA;CACA,IAAKD,IAAAA,CAAAA,OAAL,GAAkBA,OAAlB,CAAA;CACA,IAAKF,IAAAA,CAAAA,aAAL,GAAsBA,aAAtB,CAAA;CAEA,IAAA,IAAA,CAAK0C,KAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCA,EAAAA,KAAK,GAAE;CAEN,IAAM5T,MAAAA,IAAI,GAAG,IAAb,CAAA;CACA,IAAA,MAAMM,QAAQ,GAAMN,IAAI,CAACM,QAAzB,CAAA;CACA,IAAA,MAAM4Q,aAAa,GAAKlR,IAAI,CAACkR,aAA7B,CAAA;CACA,IAAA,MAAMC,QAAQ,GAAMnR,IAAI,CAACmR,QAAzB,CAAA;CACA,IAAA,MAAME,gBAAgB,GAAIrR,IAAI,CAACqR,gBAA/B,CAAA;CACA,IAAA,MAAM5M,OAAO,GAAMzE,IAAI,CAACyE,OAAxB,CAAA;CACA,IAAA,MAAM2M,OAAO,GAAMpR,IAAI,CAACoR,OAAxB,CAAA;CACA,IAAA,MAAMtE,KAAK,GAAO9M,IAAI,CAAC8M,KAAvB,CAAA;CACA,IAAA,MAAMwE,UAAU,GAAKtR,IAAI,CAACsR,UAA1B,CAAA;CACA,IAAA,MAAMuC,aAAa,GAAK;CAAEC,MAAAA,OAAO,EAAE,IAAA;CAAX,KAAxB,CAAA;CACA,IAAA,MAAMC,SAAS,GAAM/T,IAAI,CAAC2R,OAAL,GAAc,cAAnC,CAAA;CAGAnM,IAAAA,OAAO,CAAC6L,gBAAD,EAAkB;CACxBrD,MAAAA,EAAE,EAAE+F,SAAAA;CADoB,KAAlB,CAAP,CAAA;CAIAvO,IAAAA,OAAO,CAAC8L,UAAD,EAAY;CAClB0C,MAAAA,IAAI,EAAC,UADa;CAElB,MAAA,eAAA,EAAgB,SAFE;CAGlB,MAAA,eAAA,EAAgB,OAHE;CAIlB,MAAgBD,eAAAA,EAAAA,SAAAA;CAJE,KAAZ,CAAP,CAAA;CAOA,IAAME,MAAAA,UAAU,GAAGlG,KAAK,CAACuD,UAAD,EAAYtR,IAAI,CAAC2R,OAAL,GAAe,aAA3B,CAAxB,CAAA;CACA,IAAMhQ,MAAAA,KAAK,GAAK,aAAA,GAAca,WAAW,CAACxC,IAAI,CAAC2R,OAAN,CAAzB,GAAwC,IAAxD,CAAA;CACA,IAAA,MAAMuC,KAAK,GAAKlS,QAAQ,CAACM,aAAT,CAAuBX,KAAvB,CAAhB,CAAA;CACA,IAAMwS,MAAAA,WAAW,GAAGnU,IAAI,CAACoU,KAAL,CAAWC,IAAX,CAAgBrU,IAAhB,CAApB,CAAA;;CACA,IAAA,IAAIkU,KAAJ,EAAW;CACV5G,MAAAA,QAAQ,CAAC4G,KAAD,EAAO,OAAP,EAAgBC,WAAhB,CAAR,CAAA;CACA3O,MAAAA,OAAO,CAAC0O,KAAD,EAAO;CAACI,QAAAA,GAAG,EAACL,UAAAA;CAAL,OAAP,CAAP,CAAA;CACA,MAAMM,MAAAA,QAAQ,GAAGxG,KAAK,CAACmG,KAAD,EAAOlU,IAAI,CAAC2R,OAAL,GAAa,WAApB,CAAtB,CAAA;CACAnM,MAAAA,OAAO,CAAC8L,UAAD,EAAY;CAAC,QAAkBiD,iBAAAA,EAAAA,QAAAA;CAAnB,OAAZ,CAAP,CAAA;CACA/O,MAAAA,OAAO,CAAC6L,gBAAD,EAAkB;CAAC,QAAkBkD,iBAAAA,EAAAA,QAAAA;CAAnB,OAAlB,CAAP,CAAA;CACA,KAAA;;CAED9P,IAAAA,OAAO,CAACrB,KAAR,CAAcoR,KAAd,GAAsB1H,KAAK,CAAC1J,KAAN,CAAYoR,KAAlC,CAAA;;CAEA,IAAA,IAAIxU,IAAI,CAACI,OAAL,CAAaC,KAAb,CAAmBX,MAAvB,EAA+B;CAC9B,MAAA,MAAM+U,eAAe,GAAG,SAAYzU,GAAAA,IAAI,CAACI,OAAL,CAAaC,KAAb,CAAmBqU,IAAnB,CAAwB,UAAxB,CAApC,CAAA;CACArR,MAAAA,UAAU,CAAE,CAACoB,OAAD,EAAS0M,QAAT,CAAF,EAAsBsD,eAAtB,CAAV,CAAA;CACA,KAAA;;CAED,IAAA,IAAI,CAACnU,QAAQ,CAACmJ,QAAT,KAAsB,IAAtB,IAA8BnJ,QAAQ,CAACmJ,QAAT,GAAoB,CAAnD,KAAyDzJ,IAAI,CAACyR,aAAlE,EAAiF;CAChFjM,MAAAA,OAAO,CAACsH,KAAD,EAAO;CAAC6H,QAAAA,QAAQ,EAAC,UAAA;CAAV,OAAP,CAAP,CAAA;CACA,KAAA;;CAED,IAAIrU,IAAAA,QAAQ,CAACiL,WAAb,EAA0B;CACzB/F,MAAAA,OAAO,CAAC0L,aAAD,EAAe;CAAC3F,QAAAA,WAAW,EAACjL,QAAQ,CAACiL,WAAAA;CAAtB,OAAf,CAAP,CAAA;CACA,KAnDK;;;CAsDN,IAAI,IAAA,CAACjL,QAAQ,CAAC0I,OAAV,IAAqB1I,QAAQ,CAACyI,SAAlC,EAA6C;CAC5CzI,MAAAA,QAAQ,CAAC0I,OAAT,GAAmB,IAAI3C,MAAJ,CAAW,MAAA,GAASuO,YAAY,CAACtU,QAAQ,CAACyI,SAAV,CAArB,GAA4C,OAAvD,CAAnB,CAAA;CACA,KAxDK;CA2DN;;;CACA,IAAA,IAAIzI,QAAQ,CAACuU,IAAT,IAAiBvU,QAAQ,CAAC0J,YAA9B,EAA4C;CAC3C1J,MAAAA,QAAQ,CAACuU,IAAT,GAAgB7I,YAAY,CAAC1L,QAAQ,CAACuU,IAAV,EAAevU,QAAQ,CAAC0J,YAAxB,CAA5B,CAAA;CACA,KAAA;;CAEDhK,IAAAA,IAAI,CAACkR,aAAL,CAAmBvE,IAAnB,GAA0BG,KAAK,CAACH,IAAhC,CAAA;CAEAW,IAAAA,QAAQ,CAAC6D,QAAD,EAAU,WAAV,EAAuB,MAAM;CACpCnR,MAAAA,IAAI,CAACwS,WAAL,GAAmB,KAAnB,CAAA;CACA,KAFO,CAAR,CAAA;CAIAlF,IAAAA,QAAQ,CAAC6D,QAAD,EAAU,YAAV,EAAyB2D,CAAD,IAAO;CAEtC,MAAIC,IAAAA,YAAY,GAAGzQ,WAAW,CAACwQ,CAAC,CAACvQ,MAAH,EAA0B,mBAA1B,EAA+C4M,QAA/C,CAA9B,CAAA;CACA,MAAI4D,IAAAA,YAAJ,EAAmB/U,IAAI,CAACgV,aAAL,CAAoBF,CAApB,EAAqCC,YAArC,CAAA,CAAA;CAEnB,KALO,EAKL;CAACE,MAAAA,OAAO,EAAC,IAAA;CAAT,KALK,CAAR,CAtEM;;CA8EN3H,IAAAA,QAAQ,CAAC6D,QAAD,EAAU,OAAV,EAAmBhE,GAAD,IAAS;CAClC,MAAM8B,MAAAA,MAAM,GAAG3K,WAAW,CAAC6I,GAAG,CAAC5I,MAAL,EAA4B,mBAA5B,CAA1B,CAAA;;CACA,MAAA,IAAI0K,MAAJ,EAAY;CACXjP,QAAAA,IAAI,CAACkV,cAAL,CAAqB/H,GAArB,EAAwC8B,MAAxC,CAAA,CAAA;CACA/B,QAAAA,cAAc,CAACC,GAAD,EAAK,IAAL,CAAd,CAAA;CACA,OAAA;CACD,KANO,CAAR,CAAA;CAQAG,IAAAA,QAAQ,CAAC8D,OAAD,EAAS,OAAT,EAAmBjE,GAAD,IAAS;CAElC,MAAI4H,IAAAA,YAAY,GAAGzQ,WAAW,CAAE6I,GAAG,CAAC5I,MAAN,EAA6B,gBAA7B,EAA+C6M,OAA/C,CAA9B,CAAA;;CACA,MAAI2D,IAAAA,YAAY,IAAI/U,IAAI,CAACmV,YAAL,CAAkBhI,GAAlB,EAAqC4H,YAArC,CAApB,EAAmF;CAClF7H,QAAAA,cAAc,CAACC,GAAD,EAAK,IAAL,CAAd,CAAA;CACA,QAAA,OAAA;CACA,OANiC;;;CASlC,MAAA,IAAI+D,aAAa,CAACtF,KAAd,IAAuB,EAA3B,EAA+B;CAC9B,QAAA,OAAA;CACA,OAAA;;CAED5L,MAAAA,IAAI,CAACoV,OAAL,EAAA,CAAA;CACAlI,MAAAA,cAAc,CAACC,GAAD,EAAK,IAAL,CAAd,CAAA;CACA,KAfO,CAAR,CAtFM;;CAyGNG,IAAAA,QAAQ,CAACgE,UAAD,EAAY,SAAZ,EAAyBwD,CAAD,IAAO9U,IAAI,CAACqV,SAAL,CAAeP,CAAf,CAA/B,CAAR,CAzGM;;CA4GNxH,IAAAA,QAAQ,CAAC4D,aAAD,EAAe,UAAf,EAA4B4D,CAAD,IAAO9U,IAAI,CAACsV,UAAL,CAAgBR,CAAhB,CAAlC,CAAR,CAAA;CACAxH,IAAAA,QAAQ,CAAC4D,aAAD,EAAe,OAAf,EAA0B4D,CAAD,IAAO9U,IAAI,CAACuV,OAAL,CAAaT,CAAb,CAAhC,CAAR,CAAA;CACAxH,IAAAA,QAAQ,CAACgE,UAAD,EAAY,MAAZ,EAAuBwD,CAAD,IAAO9U,IAAI,CAACwV,MAAL,CAAYV,CAAZ,CAA7B,CAAR,CAAA;CACAxH,IAAAA,QAAQ,CAACgE,UAAD,EAAY,OAAZ,EAAuBwD,CAAD,IAAO9U,IAAI,CAACyV,OAAL,CAAaX,CAAb,CAA7B,CAAR,CAAA;CACAxH,IAAAA,QAAQ,CAAC4D,aAAD,EAAe,OAAf,EAA0B4D,CAAD,IAAO9U,IAAI,CAAC0V,OAAL,CAAaZ,CAAb,CAAhC,CAAR,CAAA;;CAGA,IAAMa,MAAAA,aAAa,GAAIxI,GAAD,IAAe;CAEpC;CACA;CACA,MAAA,MAAM5I,MAAM,GAAG4I,GAAG,CAACyI,YAAJ,EAAA,CAAmB,CAAnB,CAAf,CAAA;;CACA,MAAA,IAAI,CAACnR,OAAO,CAACC,QAAR,CAAiBH,MAAjB,CAAD,IAA4C,CAAC4M,QAAQ,CAACzM,QAAT,CAAkBH,MAAlB,CAAjD,EAA2F;CAC1F,QAAIvE,IAAAA,IAAI,CAACoS,SAAT,EAAoB;CACnBpS,UAAAA,IAAI,CAAC6V,IAAL,EAAA,CAAA;CACA,SAAA;;CACD7V,QAAAA,IAAI,CAAC8V,UAAL,EAAA,CAAA;CACA,QAAA,OAAA;CACA,OAXmC;CAepC;CACA;CACA;;;CACA,MAAA,IAAIvR,MAAM,IAAI2M,aAAV,IAA2BlR,IAAI,CAAC8R,MAApC,EAA4C;CAC3C3E,QAAAA,GAAG,CAACE,eAAJ,EAAA,CAD2C;CAI3C,OAJD,MAIK;CACJH,QAAAA,cAAc,CAACC,GAAD,EAAK,IAAL,CAAd,CAAA;CACA,OAAA;CAED,KA1BD,CAAA;;CA4BA,IAAM4I,MAAAA,UAAU,GAAG,MAAM;CACxB,MAAI/V,IAAAA,IAAI,CAAC8R,MAAT,EAAiB;CAChB9R,QAAAA,IAAI,CAACgW,gBAAL,EAAA,CAAA;CACA,OAAA;CACD,KAJD,CAAA;;CAOA1I,IAAAA,QAAQ,CAACtL,QAAD,EAAU,WAAV,EAAuB2T,aAAvB,CAAR,CAAA;CACArI,IAAAA,QAAQ,CAAC6F,MAAD,EAAQ,QAAR,EAAkB4C,UAAlB,EAA8BlC,aAA9B,CAAR,CAAA;CACAvG,IAAAA,QAAQ,CAAC6F,MAAD,EAAQ,QAAR,EAAkB4C,UAAlB,EAA8BlC,aAA9B,CAAR,CAAA;;CAEA,IAAKjC,IAAAA,CAAAA,QAAL,GAAgB,MAAM;CACrB5P,MAAAA,QAAQ,CAACiU,mBAAT,CAA6B,WAA7B,EAAyCN,aAAzC,CAAA,CAAA;CACAxC,MAAAA,MAAM,CAAC8C,mBAAP,CAA2B,QAA3B,EAAoCF,UAApC,CAAA,CAAA;CACA5C,MAAAA,MAAM,CAAC8C,mBAAP,CAA2B,QAA3B,EAAoCF,UAApC,CAAA,CAAA;CACA,MAAI7B,IAAAA,KAAJ,EAAYA,KAAK,CAAC+B,mBAAN,CAA0B,OAA1B,EAAkC9B,WAAlC,CAAA,CAAA;CACZ,KALD,CA1JM;CAkKN;;;CACA,IAAA,IAAA,CAAK+B,cAAL,GAAsB;CACrBhU,MAAAA,SAAS,EAAG4K,KAAK,CAAC5K,SADG;CAErBsP,MAAAA,QAAQ,EAAG1E,KAAK,CAAC0E,QAAAA;CAFI,KAAtB,CAAA;CAMA1E,IAAAA,KAAK,CAAC0E,QAAN,GAAiB,CAAC,CAAlB,CAAA;CACA1E,IAAAA,KAAK,CAACqJ,qBAAN,CAA4B,UAA5B,EAAwCnW,IAAI,CAACyE,OAA7C,CAAA,CAAA;CAEAzE,IAAAA,IAAI,CAACoW,IAAL,CAAU,KAAV,CAAA,CAAA;CACA9V,IAAAA,QAAQ,CAAC8O,KAAT,GAAiB,EAAjB,CAAA;CACA,IAAO9O,OAAAA,QAAQ,CAACwI,SAAhB,CAAA;CACA,IAAOxI,OAAAA,QAAQ,CAACY,OAAhB,CAAA;CAEAoM,IAAAA,QAAQ,CAACR,KAAD,EAAO,SAAP,EAAkB,MAAM;CAC/B,MAAI9M,IAAAA,IAAI,CAACkS,OAAT,EAAkB;CACjBlS,QAAAA,IAAI,CAACkS,OAAL,GAAe,KAAf,CAAA;CACAlS,QAAAA,IAAI,CAACiS,SAAL,GAAiB,IAAjB,CAAA;CACAjS,QAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACA,OAAA;CACD,KANO,CAAR,CAAA;CAQArW,IAAAA,IAAI,CAACsW,mBAAL,EAAA,CAAA;CACAtW,IAAAA,IAAI,CAACuW,YAAL,EAAA,CAAA;CACAvW,IAAAA,IAAI,CAACwW,KAAL,CAAW,KAAX,CAAA,CAAA;CACAxW,IAAAA,IAAI,CAAC8V,UAAL,EAAA,CAAA;CACA9V,IAAAA,IAAI,CAACsS,OAAL,GAAe,IAAf,CAAA;;CAEA,IAAIxF,IAAAA,KAAK,CAACmD,QAAV,EAAoB;CACnBjQ,MAAAA,IAAI,CAACyW,OAAL,EAAA,CAAA;CACA,KAFD,MAEK;CACJzW,MAAAA,IAAI,CAAC0W,MAAL,EAAA,CADI;CAEJ,KAAA;;CAED1W,IAAAA,IAAI,CAACb,EAAL,CAAQ,QAAR,EAAkB,KAAKwX,QAAvB,CAAA,CAAA;CAEAtT,IAAAA,UAAU,CAACyJ,KAAD,EAAO,aAAP,EAAqB,sBAArB,CAAV,CAAA;CACA9M,IAAAA,IAAI,CAACF,OAAL,CAAa,YAAb,EAxMM;;CA2MN,IAAA,IAAIQ,QAAQ,CAACwJ,OAAT,KAAqB,IAAzB,EAA+B;CAC9B9J,MAAAA,IAAI,CAAC8J,OAAL,EAAA,CAAA;CACA,KAAA;CAED,GAAA;CAGD;CACD;CACA;CACA;;;CACC8M,EAAAA,YAAY,CAAC1V,OAAmB,GAAG,EAAvB,EAA2B4H,SAAqB,GAAG,EAAnD,EAAsD;CAEjE;CACA,IAAA,IAAA,CAAK+N,UAAL,CAAgB3V,OAAhB,CAAA,CAHiE;;CAOjEM,IAAAA,SAAO,CAAEsH,SAAF,EAAcuH,QAAD,IAAwB;CAC3C,MAAKyG,IAAAA,CAAAA,mBAAL,CAAyBzG,QAAzB,CAAA,CAAA;CACA,KAFM,CAAP,CAAA;CAGA,GAAA;CAED;CACD;CACA;;;CACCoD,EAAAA,cAAc,GAAG;CAChB,IAAIzT,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAIyO,WAAW,GAAGzO,IAAI,CAACM,QAAL,CAAc+J,UAAhC,CAAA;CACA,IAAA,IAAIuE,cAAc,GAAG5O,IAAI,CAACM,QAAL,CAAciK,kBAAnC,CAAA;CAEA,IAAA,IAAIwM,SAAS,GAAG;CACf,MAAA,UAAA,EAAatQ,IAAD,IAAoB;CAC/B,QAAA,IAAI4J,QAAQ,GAAGrO,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAf,CAAA;CACAoO,QAAAA,QAAQ,CAAC1J,SAAT,GAAqB,UAArB,CAAA;CACA0J,QAAAA,QAAQ,CAACpJ,WAAT,CAAqBR,IAAI,CAACvF,OAA1B,CAAA,CAAA;CACA,QAAA,OAAOmP,QAAP,CAAA;CAEA,OAPc;CAQf,MAAA,iBAAA,EAAmB,CAAC5J,IAAD,EAAiBuQ,MAAjB,KAA+C;CACjE,QAAO,OAAA,+BAAA,GAAkCA,MAAM,CAACvQ,IAAI,CAACmI,cAAD,CAAL,CAAxC,GAAiE,QAAxE,CAAA;CACA,OAVc;CAWf,MAAA,QAAA,EAAU,CAACnI,IAAD,EAAiBuQ,MAAjB,KAA+C;CACxD,QAAO,OAAA,OAAA,GAAUA,MAAM,CAACvQ,IAAI,CAACgI,WAAD,CAAL,CAAhB,GAAsC,QAA7C,CAAA;CACA,OAbc;CAcf,MAAA,MAAA,EAAQ,CAAChI,IAAD,EAAiBuQ,MAAjB,KAA+C;CACtD,QAAO,OAAA,OAAA,GAAUA,MAAM,CAACvQ,IAAI,CAACgI,WAAD,CAAL,CAAhB,GAAsC,QAA7C,CAAA;CACA,OAhBc;CAiBf,MAAA,eAAA,EAAiB,CAAChI,IAAD,EAAiBuQ,MAAjB,KAA+C;CAC/D,QAAO,OAAA,kCAAA,GAAqCA,MAAM,CAACvQ,IAAI,CAACqG,KAAN,CAA3C,GAA0D,yBAAjE,CAAA;CACA,OAnBc;CAoBf,MAAA,YAAA,EAAa,MAAM;CAClB,QAAA,OAAO,gDAAP,CAAA;CACA,OAtBc;CAuBf,MAAA,SAAA,EAAU,MAAM;CACf,QAAA,OAAO,6BAAP,CAAA;CACA,OAzBc;CA0Bf,MAAc,aAAA,EAAA,MAAM,EA1BL;CA2Bf,MAAA,UAAA,EAAW,MAAM;CAChB,QAAA,OAAO,aAAP,CAAA;CACA,OAAA;CA7Bc,KAAhB,CAAA;CAiCA9M,IAAAA,IAAI,CAACM,QAAL,CAAcoL,MAAd,GAAuBxI,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB4T,SAAlB,EAA6B/W,IAAI,CAACM,QAAL,CAAcoL,MAA3C,CAAvB,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC8H,EAAAA,cAAc,GAAG;CAChB,IAAI3S,IAAAA,GAAJ,EAASF,EAAT,CAAA;CACA,IAAA,IAAIsW,SAA+B,GAAG;CACrC,MAAA,YAAA,EAAoB,cADiB;CAErC,MAAA,QAAA,EAAoB,UAFiB;CAGrC,MAAA,UAAA,EAAoB,WAHiB;CAIrC,MAAA,aAAA,EAAoB,cAJiB;CAKrC,MAAA,aAAA,EAAoB,cALiB;CAMrC,MAAA,OAAA,EAAoB,SANiB;CAOrC,MAAA,YAAA,EAAoB,aAPiB;CAQrC,MAAA,eAAA,EAAoB,gBARiB;CASrC,MAAA,cAAA,EAAoB,eATiB;CAUrC,MAAA,cAAA,EAAoB,kBAViB;CAWrC,MAAA,iBAAA,EAAoB,qBAXiB;CAYrC,MAAA,gBAAA,EAAoB,oBAZiB;CAarC,MAAA,eAAA,EAAoB,gBAbiB;CAcrC,MAAA,gBAAA,EAAoB,iBAdiB;CAerC,MAAA,MAAA,EAAoB,QAfiB;CAgBrC,MAAA,MAAA,EAAoB,QAhBiB;CAiBrC,MAAA,OAAA,EAAoB,SAjBiB;CAkBrC,MAAoB,MAAA,EAAA,QAAA;CAlBiB,KAAtC,CAAA;;CAqBA,IAAKpW,KAAAA,GAAL,IAAYoW,SAAZ,EAAuB;CAEtBtW,MAAAA,EAAE,GAAG,IAAKL,CAAAA,QAAL,CAAc2W,SAAS,CAACpW,GAAD,CAAvB,CAAL,CAAA;CACA,MAAA,IAAIF,EAAJ,EAAQ,IAAA,CAAKxB,EAAL,CAAQ0B,GAAR,EAAaF,EAAb,CAAA,CAAA;CAER,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCyV,EAAAA,IAAI,CAACc,YAAoB,GAAC,IAAtB,EAAgC;CACnC,IAAMlX,MAAAA,IAAI,GAAI,IAAd,CAAA;CACA,IAAMM,MAAAA,QAAQ,GAAG4W,YAAY,GAAG7I,WAAW,CAAErO,IAAI,CAAC8M,KAAP,EAAc;CAAC/D,MAAAA,SAAS,EAAC/I,IAAI,CAACM,QAAL,CAAcyI,SAAAA;CAAzB,KAAd,CAAd,GAAqG/I,IAAI,CAACM,QAAvI,CAAA;CAEAN,IAAAA,IAAI,CAAC4W,YAAL,CAAkBtW,QAAQ,CAACY,OAA3B,EAAmCZ,QAAQ,CAACwI,SAA5C,CAAA,CAAA;CAEA9I,IAAAA,IAAI,CAACmX,QAAL,CAAc7W,QAAQ,CAAC8O,KAAT,IAAgB,EAA9B,EAAiC,IAAjC,CAAA,CANmC;;CAQnCpP,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CARmC;CASnC,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACChC,EAAAA,OAAO,GAAQ;CACd,IAAIpV,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAA,IAAIA,IAAI,CAAC8S,WAAL,CAAiBpT,MAAjB,GAA0B,CAA9B,EAAiC;CAChCM,MAAAA,IAAI,CAACqX,gBAAL,EAAA,CAAA;CACArX,MAAAA,IAAI,CAACoU,KAAL,EAAA,CAAA;CACA,MAAA,OAAA;CACA,KAAA;;CAED,IAAA,IAAIpU,IAAI,CAACoS,SAAL,IAAkBpS,IAAI,CAAC8R,MAA3B,EAAmC;CAClC9R,MAAAA,IAAI,CAAC6V,IAAL,EAAA,CAAA;CACA,KAFD,MAEO;CACN7V,MAAAA,IAAI,CAACoU,KAAL,EAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCkD,EAAAA,WAAW,GAAQ,EAAE;CAErB;CACD;CACA;CACA;CACA;;;CACCX,EAAAA,QAAQ,GAAG;CACVjU,IAAAA,YAAY,CAAC,IAAA,CAAKoK,KAAN,EAAa,OAAb,CAAZ,CAAA;CACApK,IAAAA,YAAY,CAAC,IAAA,CAAKoK,KAAN,EAAa,QAAb,CAAZ,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC4I,EAAAA,OAAO,CAACZ,CAAD,EAAkC;CACxC,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAA,IAAIA,IAAI,CAACqS,aAAL,IAAsBrS,IAAI,CAACmS,QAA/B,EAAyC;CACxCjF,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,MAAA,OAAA;CACA,KANuC;CASxC;;;CACA,IAAA,IAAI,CAAC9U,IAAI,CAACM,QAAL,CAAc0I,OAAnB,EAA4B;CAC3B,MAAA,OAAA;CACA,KAZuC;;;CAexCuD,IAAAA,UAAU,CAAC,MAAM;CAChB,MAAA,IAAIgL,UAAU,GAAGvX,IAAI,CAACwX,UAAL,EAAjB,CAAA;;CACA,MAAI,IAAA,CAACD,UAAU,CAAC/Q,KAAX,CAAiBxG,IAAI,CAACM,QAAL,CAAc0I,OAA/B,CAAL,EAA6C;CAC5C,QAAA,OAAA;CACA,OAAA;;CAED,MAAA,IAAIyO,UAAU,GAAGF,UAAU,CAACpV,IAAX,EAAA,CAAkBtD,KAAlB,CAAwBmB,IAAI,CAACM,QAAL,CAAc0I,OAAtC,CAAjB,CAAA;CACAxH,MAAAA,SAAO,CAAEiW,UAAF,EAAeC,KAAD,IAAkB;CAEtC,QAAA,MAAMC,IAAI,GAAGhM,QAAQ,CAAC+L,KAAD,CAArB,CAAA;;CACA,QAAA,IAAIC,IAAJ,EAAU;CACT,UAAA,IAAI,IAAKzW,CAAAA,OAAL,CAAawW,KAAb,CAAJ,EAAyB;CACxB1X,YAAAA,IAAI,CAAC4X,OAAL,CAAaF,KAAb,CAAA,CAAA;CACA,WAFD,MAEK;CACJ1X,YAAAA,IAAI,CAAC6X,UAAL,CAAgBH,KAAhB,CAAA,CAAA;CACA,WAAA;CACD,SAAA;CACD,OAVM,CAAP,CAAA;CAWA,KAlBS,EAkBP,CAlBO,CAAV,CAAA;CAoBA,GAAA;CAED;CACD;CACA;CACA;;;CACCpC,EAAAA,UAAU,CAACR,CAAD,EAAuB;CAChC,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;;CACA,IAAGA,IAAAA,IAAI,CAACmS,QAAR,EAAiB;CAChBjF,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,MAAA,OAAA;CACA,KAAA;;CACD,IAAA,IAAIgD,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoBlD,CAAC,CAACmD,OAAF,IAAanD,CAAC,CAACoD,KAAnC,CAAhB,CAAA;;CACA,IAAIlY,IAAAA,IAAI,CAACM,QAAL,CAAc6I,MAAd,IAAwBnJ,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,OAA/C,IAA0DiN,SAAS,KAAK9X,IAAI,CAACM,QAAL,CAAcyI,SAA1F,EAAqG;CACpG/I,MAAAA,IAAI,CAAC6X,UAAL,EAAA,CAAA;CACA3K,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,MAAA,OAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCO,EAAAA,SAAS,CAACP,CAAD,EAAuB;CAC/B,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEAA,IAAAA,IAAI,CAACwS,WAAL,GAAmB,IAAnB,CAAA;;CAEA,IAAIxS,IAAAA,IAAI,CAACmS,QAAT,EAAmB;CAClB,MAAA,IAAI2C,CAAC,CAACmD,OAAF,KAAcE,OAAlB,EAAqC;CACpCjL,QAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,OAAA;;CACD,MAAA,OAAA;CACA,KAAA;;CAED,IAAQA,QAAAA,CAAC,CAACmD,OAAV;CAEC;CACA,MAAKE,KAAAA,KAAL;CACC,QAAI3K,IAAAA,SAAS,CAAC2K,YAAD,EAAwBrD,CAAxB,CAAb,EAAyC;CACxC,UAAA,IAAI9U,IAAI,CAACkR,aAAL,CAAmBtF,KAAnB,IAA4B,EAAhC,EAAoC;CACnCsB,YAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA9U,YAAAA,IAAI,CAACoY,SAAL,EAAA,CAAA;CACA,YAAA,OAAA;CACA,WAAA;CACD,SAAA;;CACD,QAAA,MAAA;CAED;;CACA,MAAKD,KAAAA,OAAL;CACC,QAAInY,IAAAA,IAAI,CAAC8R,MAAT,EAAiB;CAChB5E,UAAAA,cAAc,CAAC4H,CAAD,EAAG,IAAH,CAAd,CAAA;CACA9U,UAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACA,SAAA;;CACDxW,QAAAA,IAAI,CAACqX,gBAAL,EAAA,CAAA;CACA,QAAA,OAAA;CAED;;CACA,MAAKc,KAAAA,QAAL;CACC,QAAI,IAAA,CAACnY,IAAI,CAAC8R,MAAN,IAAgB9R,IAAI,CAACyS,UAAzB,EAAqC;CACpCzS,UAAAA,IAAI,CAACqY,IAAL,EAAA,CAAA;CACA,SAFD,MAEO,IAAIrY,IAAI,CAAC6S,YAAT,EAAuB;CAC7B,UAAIyF,IAAAA,IAAI,GAAGtY,IAAI,CAACuY,WAAL,CAAiBvY,IAAI,CAAC6S,YAAtB,EAAoC,CAApC,CAAX,CAAA;CACA,UAAA,IAAIyF,IAAJ,EAAUtY,IAAI,CAACwY,eAAL,CAAqBF,IAArB,CAAA,CAAA;CACV,SAAA;;CACDpL,QAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,MAAL;CACC,QAAInY,IAAAA,IAAI,CAAC6S,YAAT,EAAuB;CACtB,UAAA,IAAI4F,IAAI,GAAGzY,IAAI,CAACuY,WAAL,CAAiBvY,IAAI,CAAC6S,YAAtB,EAAoC,CAAC,CAArC,CAAX,CAAA;CACA,UAAA,IAAI4F,IAAJ,EAAUzY,IAAI,CAACwY,eAAL,CAAqBC,IAArB,CAAA,CAAA;CACV,SAAA;;CACDvL,QAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,UAAL;CACC,QAAInY,IAAAA,IAAI,CAAC0Y,SAAL,CAAe1Y,IAAI,CAAC6S,YAApB,CAAJ,EAAuC;CACtC7S,UAAAA,IAAI,CAACkV,cAAL,CAAoBJ,CAApB,EAAsB9U,IAAI,CAAC6S,YAA3B,CAAA,CAAA;CACA3F,UAAAA,cAAc,CAAC4H,CAAD,CAAd,CAFsC;CAKtC,SALD,MAKM,IAAI9U,IAAI,CAACM,QAAL,CAAc6I,MAAd,IAAwBnJ,IAAI,CAAC6X,UAAL,EAA5B,EAA+C;CACpD3K,UAAAA,cAAc,CAAC4H,CAAD,CAAd,CADoD;CAIpD,SAJK,MAIA,IAAI9S,QAAQ,CAAC2W,aAAT,IAA0B3Y,IAAI,CAACkR,aAA/B,IAAgDlR,IAAI,CAAC8R,MAAzD,EAAiE;CACtE5E,UAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,SAAA;;CAED,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,QAAL;CACCnY,QAAAA,IAAI,CAAC4Y,gBAAL,CAAsB,CAAC,CAAvB,EAA0B9D,CAA1B,CAAA,CAAA;CACA,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,SAAL;CACCnY,QAAAA,IAAI,CAAC4Y,gBAAL,CAAsB,CAAtB,EAAyB9D,CAAzB,CAAA,CAAA;CACA,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,OAAL;CAEC,QAAA,IAAInY,IAAI,CAACM,QAAL,CAAcuJ,WAAlB,EAA+B;CAC9B,UAAI7J,IAAAA,IAAI,CAAC0Y,SAAL,CAAe1Y,IAAI,CAAC6S,YAApB,CAAJ,EAAuC;CACtC7S,YAAAA,IAAI,CAACkV,cAAL,CAAoBJ,CAApB,EAAsB9U,IAAI,CAAC6S,YAA3B,CAAA,CADsC;CAItC;;CACA3F,YAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,WAAA;;CACD,UAAI9U,IAAAA,IAAI,CAACM,QAAL,CAAc6I,MAAd,IAAwBnJ,IAAI,CAAC6X,UAAL,EAA5B,EAA+C;CAC9C3K,YAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,WAAA;CACD,SAAA;;CACD,QAAA,OAAA;CAED;;CACA,MAAKqD,KAAAA,aAAL,CAAA;CACA,MAAKA,KAAAA,UAAL;CACCnY,QAAAA,IAAI,CAAC6Y,eAAL,CAAqB/D,CAArB,CAAA,CAAA;CACA,QAAA,OAAA;CA1FF,KAZ+B;;;CA0G/B,IAAA,IAAI9U,IAAI,CAACqS,aAAL,IAAsB,CAAC7E,SAAS,CAAC2K,YAAD,EAAwBrD,CAAxB,CAApC,EAAgE;CAC/D5H,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCS,EAAAA,OAAO,CAACT,CAAD,EAAkC;CACxC,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAIA,IAAAA,IAAI,CAACmS,QAAT,EAAmB;CAClB,MAAA,OAAA;CACA,KAAA;;CAED,IAAA,IAAIvG,KAAK,GAAG5L,IAAI,CAACwX,UAAL,EAAZ,CAAA;;CACA,IAAA,IAAIxX,IAAI,CAAC2S,SAAL,KAAmB/G,KAAvB,EAA8B;CAC7B5L,MAAAA,IAAI,CAAC2S,SAAL,GAAiB/G,KAAjB,CAAA;;CAEA,MAAA,IAAI5L,IAAI,CAACM,QAAL,CAAcmL,UAAd,CAAyB5D,IAAzB,CAA8B7H,IAA9B,EAAmC4L,KAAnC,CAAJ,EAA+C;CAC9C5L,QAAAA,IAAI,CAAC6U,IAAL,CAAUjJ,KAAV,CAAA,CAAA;CACA,OAAA;;CAED5L,MAAAA,IAAI,CAAC8Y,cAAL,EAAA,CAAA;CACA9Y,MAAAA,IAAI,CAACF,OAAL,CAAa,MAAb,EAAqB8L,KAArB,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCoJ,EAAAA,aAAa,CAAE7H,GAAF,EAAgC8B,MAAhC,EAAyD;CACrE,IAAI,IAAA,IAAA,CAAKuD,WAAT,EAAuB,OAAA;CACvB,IAAA,IAAA,CAAKgG,eAAL,CAAqBvJ,MAArB,EAA6B,KAA7B,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCwG,EAAAA,OAAO,CAACX,CAAD,EAAmC;CACzC,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAI+Y,UAAU,GAAG/Y,IAAI,CAACoS,SAAtB,CAAA;;CAEA,IAAIpS,IAAAA,IAAI,CAAC+R,UAAT,EAAqB;CACpB/R,MAAAA,IAAI,CAAC6V,IAAL,EAAA,CAAA;CACA3I,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,MAAA,OAAA;CACA,KAAA;;CAED,IAAI9U,IAAAA,IAAI,CAACuS,WAAT,EAAsB,OAAA;CACtBvS,IAAAA,IAAI,CAACoS,SAAL,GAAiB,IAAjB,CAAA;CACA,IAAIpS,IAAAA,IAAI,CAACM,QAAL,CAAcwJ,OAAd,KAA0B,OAA9B,EAAwC9J,IAAI,CAAC8J,OAAL,EAAA,CAAA;CAExC,IAAA,IAAI,CAACiP,UAAL,EAAiB/Y,IAAI,CAACF,OAAL,CAAa,OAAb,CAAA,CAAA;;CAEjB,IAAA,IAAI,CAACE,IAAI,CAAC8S,WAAL,CAAiBpT,MAAtB,EAA8B;CAC7BM,MAAAA,IAAI,CAACgZ,SAAL,EAAA,CAAA;CACAhZ,MAAAA,IAAI,CAAC8Y,cAAL,CAAoB,CAAC,CAAC9Y,IAAI,CAACM,QAAL,CAAcgJ,WAApC,CAAA,CAAA;CACA,KAAA;;CAEDtJ,IAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCb,EAAAA,MAAM,CAACV,CAAD,EAAqB;CAE1B,IAAA,IAAI9S,QAAQ,CAACiX,QAAT,EAAA,KAAwB,KAA5B,EAAoC,OAAA;CAEpC,IAAIjZ,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAI,CAACA,IAAI,CAACoS,SAAV,EAAqB,OAAA;CACrBpS,IAAAA,IAAI,CAACoS,SAAL,GAAiB,KAAjB,CAAA;CACApS,IAAAA,IAAI,CAACuS,WAAL,GAAmB,KAAnB,CAAA;;CAEA,IAAI2G,IAAAA,UAAU,GAAG,MAAM;CACtBlZ,MAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACAxW,MAAAA,IAAI,CAACmZ,aAAL,EAAA,CAAA;CACAnZ,MAAAA,IAAI,CAACoZ,QAAL,CAAcpZ,IAAI,CAACoP,KAAL,CAAW1P,MAAzB,CAAA,CAAA;CACAM,MAAAA,IAAI,CAACF,OAAL,CAAa,MAAb,CAAA,CAAA;CACA,KALD,CAAA;;CAOA,IAAIE,IAAAA,IAAI,CAACM,QAAL,CAAc6I,MAAd,IAAwBnJ,IAAI,CAACM,QAAL,CAAc8I,YAA1C,EAAwD;CACvDpJ,MAAAA,IAAI,CAAC6X,UAAL,CAAgB,IAAhB,EAAsBqB,UAAtB,CAAA,CAAA;CACA,KAFD,MAEO;CACNA,MAAAA,UAAU,EAAA,CAAA;CACV,KAAA;CACD,GAAA;CAGD;CACD;CACA;CACA;CACA;;;CACChE,EAAAA,cAAc,CAAE/H,GAAF,EAAgC8B,MAAhC,EAAoD;CACjE,IAAA,IAAIrD,KAAJ;CAAA,QAAW5L,IAAI,GAAG,IAAlB,CADiE;;CAKjE,IAAA,IAAIiP,MAAM,CAACoK,aAAP,IAAwBpK,MAAM,CAACoK,aAAP,CAAqB1U,OAArB,CAA6B,iBAA7B,CAA5B,EAA6E;CAC5E,MAAA,OAAA;CACA,KAAA;;CAGD,IAAIsK,IAAAA,MAAM,CAACnL,SAAP,CAAiBY,QAAjB,CAA0B,QAA1B,CAAJ,EAAyC;CACxC1E,MAAAA,IAAI,CAAC6X,UAAL,CAAgB,IAAhB,EAAsB,MAAM;CAC3B,QAAA,IAAI7X,IAAI,CAACM,QAAL,CAAcgZ,gBAAlB,EAAoC;CACnCtZ,UAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACA,SAAA;CACD,OAJD,CAAA,CAAA;CAKA,KAND,MAMO;CACN5K,MAAAA,KAAK,GAAGqD,MAAM,CAACQ,OAAP,CAAe7D,KAAvB,CAAA;;CACA,MAAA,IAAI,OAAOA,KAAP,KAAiB,WAArB,EAAkC;CACjC5L,QAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;CACApX,QAAAA,IAAI,CAAC4X,OAAL,CAAahM,KAAb,CAAA,CAAA;;CACA,QAAA,IAAI5L,IAAI,CAACM,QAAL,CAAcgZ,gBAAlB,EAAoC;CACnCtZ,UAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACA,SAAA;;CAED,QAAA,IAAI,CAACxW,IAAI,CAACM,QAAL,CAAcoJ,YAAf,IAA+ByD,GAAG,CAACR,IAAnC,IAA2C,QAAQtF,IAAR,CAAa8F,GAAG,CAACR,IAAjB,CAA/C,EAAuE;CACtE3M,UAAAA,IAAI,CAACwY,eAAL,CAAqBvJ,MAArB,CAAA,CAAA;CACA,SAAA;CACD,OAAA;CACD,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCyJ,EAAAA,SAAS,CAACzJ,MAAD,EAAiC;CAEzC,IAAA,IAAI,IAAK6C,CAAAA,MAAL,IAAe7C,MAAf,IAAyB,IAAA,CAAKoC,gBAAL,CAAsB3M,QAAtB,CAA+BuK,MAA/B,CAA7B,EAAsE;CACrE,MAAA,OAAO,IAAP,CAAA;CACA,KAAA;;CACD,IAAA,OAAO,KAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCkG,EAAAA,YAAY,CAAEhI,GAAF,EAAmBoM,IAAnB,EAA0C;CACrD,IAAIvZ,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAA,IAAI,CAACA,IAAI,CAACmS,QAAN,IAAkBnS,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,OAA7C,EAAsD;CACrDqC,MAAAA,cAAc,CAACC,GAAD,CAAd,CAAA;CACAnN,MAAAA,IAAI,CAACmZ,aAAL,CAAmBI,IAAnB,EAAyBpM,GAAzB,CAAA,CAAA;CACA,MAAA,OAAO,IAAP,CAAA;CACA,KAAA;;CACD,IAAA,OAAO,KAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;CACCqM,EAAAA,OAAO,CAAC5N,KAAD,EAAsB;CAE5B,IAAA,IAAI,CAAC,IAAKtL,CAAAA,QAAL,CAAcuU,IAAnB,EAA0B,OAAO,KAAP,CAAA;CAC1B,IAAI,IAAA,IAAA,CAAKrI,cAAL,CAAoBrL,cAApB,CAAmCyK,KAAnC,CAAJ,EAAgD,OAAO,KAAP,CAAA;CAEhD,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCiJ,EAAAA,IAAI,CAACjJ,KAAD,EAAoB;CACvB,IAAM5L,MAAAA,IAAI,GAAG,IAAb,CAAA;CAEA,IAAA,IAAI,CAACA,IAAI,CAACwZ,OAAL,CAAa5N,KAAb,CAAL,EAA2B,OAAA;CAE3BvI,IAAAA,UAAU,CAACrD,IAAI,CAACyE,OAAN,EAAczE,IAAI,CAACM,QAAL,CAAc2J,YAA5B,CAAV,CAAA;CACAjK,IAAAA,IAAI,CAACmM,OAAL,EAAA,CAAA;CAEA,IAAMvN,MAAAA,QAAQ,GAAGoB,IAAI,CAACyZ,YAAL,CAAkBpF,IAAlB,CAAuBrU,IAAvB,CAAjB,CAAA;CACAA,IAAAA,IAAI,CAACM,QAAL,CAAcuU,IAAd,CAAmBhN,IAAnB,CAAwB7H,IAAxB,EAA8B4L,KAA9B,EAAqChN,QAArC,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC6a,EAAAA,YAAY,CAAEvY,OAAF,EAAuB4H,SAAvB,EAAmD;CAC9D,IAAM9I,MAAAA,IAAI,GAAG,IAAb,CAAA;CACAA,IAAAA,IAAI,CAACmM,OAAL,GAAeC,IAAI,CAACC,GAAL,CAASrM,IAAI,CAACmM,OAAL,GAAe,CAAxB,EAA2B,CAA3B,CAAf,CAAA;CACAnM,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;CAEApX,IAAAA,IAAI,CAAC0Z,iBAAL,EAAA,CAL8D;;CAM9D1Z,IAAAA,IAAI,CAAC4W,YAAL,CAAkB1V,OAAlB,EAA0B4H,SAA1B,CAAA,CAAA;CAEA9I,IAAAA,IAAI,CAAC8Y,cAAL,CAAoB9Y,IAAI,CAACoS,SAAL,IAAkB,CAACpS,IAAI,CAACqS,aAA5C,CAAA,CAAA;;CAEA,IAAA,IAAI,CAACrS,IAAI,CAACmM,OAAV,EAAmB;CAClBnI,MAAAA,aAAa,CAAChE,IAAI,CAACyE,OAAN,EAAczE,IAAI,CAACM,QAAL,CAAc2J,YAA5B,CAAb,CAAA;CACA,KAAA;;CAEDjK,IAAAA,IAAI,CAACF,OAAL,CAAa,MAAb,EAAqBoB,OAArB,EAA8B4H,SAA9B,CAAA,CAAA;CACA,GAAA;;CAEDgB,EAAAA,OAAO,GAAO;CACb,IAAA,IAAIhG,SAAS,GAAG,IAAKW,CAAAA,OAAL,CAAaX,SAA7B,CAAA;CACA,IAAA,IAAIA,SAAS,CAACY,QAAV,CAAmB,WAAnB,CAAJ,EAAsC,OAAA;CACtCZ,IAAAA,SAAS,CAACC,GAAV,CAAc,WAAd,CAAA,CAAA;CACA,IAAK8Q,IAAAA,CAAAA,IAAL,CAAU,EAAV,CAAA,CAAA;CACA,GAAA;CAGD;CACD;CACA;CACA;;;CACC8E,EAAAA,eAAe,CAAC/N,KAAY,GAAG,EAAhB,EAAoB;CAClC,IAAIkB,IAAAA,KAAK,GAAG,IAAA,CAAKoE,aAAjB,CAAA;CACA,IAAA,IAAI0I,OAAO,GAAG9M,KAAK,CAAClB,KAAN,KAAgBA,KAA9B,CAAA;;CACA,IAAA,IAAIgO,OAAJ,EAAa;CACZ9M,MAAAA,KAAK,CAAClB,KAAN,GAAcA,KAAd,CAAA;CACAlJ,MAAAA,YAAY,CAACoK,KAAD,EAAO,QAAP,CAAZ,CAAA;CACA,MAAK6F,IAAAA,CAAAA,SAAL,GAAiB/G,KAAjB,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;;;CACCiO,EAAAA,QAAQ,GAAmB;CAE1B,IAAI,IAAA,IAAA,CAAKpI,aAAL,IAAsB,IAAK3E,CAAAA,KAAL,CAAW0D,YAAX,CAAwB,UAAxB,CAA1B,EAA+D;CAC9D,MAAA,OAAO,KAAKpB,KAAZ,CAAA;CACA,KAAA;;CAED,IAAO,OAAA,IAAA,CAAKA,KAAL,CAAWsF,IAAX,CAAgB,IAAKpU,CAAAA,QAAL,CAAcyI,SAA9B,CAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCoO,EAAAA,QAAQ,CAAEvL,KAAF,EAAyBkO,MAAzB,EAA+C;CACtD,IAAInb,IAAAA,MAAM,GAAGmb,MAAM,GAAG,EAAH,GAAQ,CAAC,QAAD,CAA3B,CAAA;CAEArN,IAAAA,eAAe,CAAC,IAAD,EAAO9N,MAAP,EAAc,MAAM;CAClC,MAAKob,IAAAA,CAAAA,KAAL,CAAWD,MAAX,CAAA,CAAA;CACA,MAAA,IAAA,CAAKE,QAAL,CAAcpO,KAAd,EAAqBkO,MAArB,CAAA,CAAA;CACA,KAHc,CAAf,CAAA;CAIA,GAAA;CAGD;CACD;CACA;CACA;;;CACCG,EAAAA,WAAW,CAACrO,KAAD,EAAmB;CAC7B,IAAGA,IAAAA,KAAK,KAAK,CAAb,EAAgBA,KAAK,GAAG,IAAR,CADa;;CAE7B,IAAA,IAAA,CAAKtL,QAAL,CAAcmJ,QAAd,GAAyBmC,KAAzB,CAAA;CACA,IAAA,IAAA,CAAKyK,YAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC8C,EAAAA,aAAa,CAAEI,IAAF,EAAiBzE,CAAjB,EAA8C;CAC1D,IAAI9U,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAIka,SAAJ,CAAA;CACA,IAAA,IAAI5U,CAAJ,EAAO6U,KAAP,EAAcC,GAAd,EAAmBC,IAAnB,CAAA;CACA,IAAA,IAAIC,IAAJ,CAAA;CAEA,IAAIta,IAAAA,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,QAA3B,EAAqC,OANqB;;CAS1D,IAAI,IAAA,CAAC0O,IAAL,EAAW;CACVvZ,MAAAA,IAAI,CAACqX,gBAAL,EAAA,CAAA;;CACA,MAAIrX,IAAAA,IAAI,CAACoS,SAAT,EAAoB;CACnBpS,QAAAA,IAAI,CAACgZ,SAAL,EAAA,CAAA;CACA,OAAA;;CACD,MAAA,OAAA;CACA,KAfyD;;;CAkB1DkB,IAAAA,SAAS,GAAGpF,CAAC,IAAIA,CAAC,CAACnI,IAAF,CAAOqC,WAAP,EAAjB,CAAA;;CAEA,IAAA,IAAIkL,SAAS,KAAK,OAAd,IAAyB1M,SAAS,CAAC,UAAD,EAAYsH,CAAZ,CAAlC,IAAoD9U,IAAI,CAAC8S,WAAL,CAAiBpT,MAAzE,EAAiF;CAChF4a,MAAAA,IAAI,GAAGta,IAAI,CAACua,aAAL,EAAP,CAAA;CACAJ,MAAAA,KAAK,GAAGpZ,KAAK,CAAC6G,SAAN,CAAgB/H,OAAhB,CAAwBgI,IAAxB,CAA6B7H,IAAI,CAACoR,OAAL,CAAab,QAA1C,EAAoD+J,IAApD,CAAR,CAAA;CACAF,MAAAA,GAAG,GAAIrZ,KAAK,CAAC6G,SAAN,CAAgB/H,OAAhB,CAAwBgI,IAAxB,CAA6B7H,IAAI,CAACoR,OAAL,CAAab,QAA1C,EAAoDgJ,IAApD,CAAP,CAAA;;CAEA,MAAIY,IAAAA,KAAK,GAAGC,GAAZ,EAAiB;CAChBC,QAAAA,IAAI,GAAIF,KAAR,CAAA;CACAA,QAAAA,KAAK,GAAGC,GAAR,CAAA;CACAA,QAAAA,GAAG,GAAKC,IAAR,CAAA;CACA,OAAA;;CACD,MAAK/U,KAAAA,CAAC,GAAG6U,KAAT,EAAgB7U,CAAC,IAAI8U,GAArB,EAA0B9U,CAAC,EAA3B,EAA+B;CAC9BiU,QAAAA,IAAI,GAAGvZ,IAAI,CAACoR,OAAL,CAAab,QAAb,CAAsBjL,CAAtB,CAAP,CAAA;;CACA,QAAItF,IAAAA,IAAI,CAAC8S,WAAL,CAAiBjT,OAAjB,CAAyB0Z,IAAzB,CAAA,KAAmC,CAAC,CAAxC,EAA2C;CAC1CvZ,UAAAA,IAAI,CAACwa,kBAAL,CAAwBjB,IAAxB,CAAA,CAAA;CACA,SAAA;CACD,OAAA;;CACDrM,MAAAA,cAAc,CAAC4H,CAAD,CAAd,CAAA;CACA,KAjBD,MAiBO,IAAKoF,SAAS,KAAK,OAAd,IAAyB1M,SAAS,CAAC2K,YAAD,EAAwBrD,CAAxB,CAAnC,IAAoEoF,SAAS,KAAK,SAAd,IAA2B1M,SAAS,CAAC,UAAD,EAAYsH,CAAZ,CAA5G,EAA6H;CACnI,MAAIyE,IAAAA,IAAI,CAACzV,SAAL,CAAeY,QAAf,CAAwB,QAAxB,CAAJ,EAAuC;CACtC1E,QAAAA,IAAI,CAACya,gBAAL,CAAuBlB,IAAvB,CAAA,CAAA;CACA,OAFD,MAEO;CACNvZ,QAAAA,IAAI,CAACwa,kBAAL,CAAwBjB,IAAxB,CAAA,CAAA;CACA,OAAA;CACD,KANM,MAMA;CACNvZ,MAAAA,IAAI,CAACqX,gBAAL,EAAA,CAAA;CACArX,MAAAA,IAAI,CAACwa,kBAAL,CAAwBjB,IAAxB,CAAA,CAAA;CACA,KA9CyD;;;CAiD1DvZ,IAAAA,IAAI,CAAC0a,SAAL,EAAA,CAAA;;CACA,IAAA,IAAI,CAAC1a,IAAI,CAACoS,SAAV,EAAqB;CACpBpS,MAAAA,IAAI,CAACoU,KAAL,EAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCoG,EAAAA,kBAAkB,CAAEjB,IAAF,EAAgB;CACjC,IAAMvZ,MAAAA,IAAI,GAAG,IAAb,CAAA;CACA,IAAM2a,MAAAA,WAAW,GAAG3a,IAAI,CAACoR,OAAL,CAAa9O,aAAb,CAA2B,cAA3B,CAApB,CAAA;CACA,IAAA,IAAIqY,WAAJ,EAAkB3W,aAAa,CAAC2W,WAAD,EAA4B,aAA5B,CAAb,CAAA;CAElBtX,IAAAA,UAAU,CAACkW,IAAD,EAAM,oBAAN,CAAV,CAAA;CACAvZ,IAAAA,IAAI,CAACF,OAAL,CAAa,aAAb,EAA4ByZ,IAA5B,CAAA,CAAA;;CACA,IAAIvZ,IAAAA,IAAI,CAAC8S,WAAL,CAAiBjT,OAAjB,CAAyB0Z,IAAzB,CAAA,IAAkC,CAAC,CAAvC,EAA0C;CACzCvZ,MAAAA,IAAI,CAAC8S,WAAL,CAAiBxT,IAAjB,CAAuBia,IAAvB,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCkB,EAAAA,gBAAgB,CAAElB,IAAF,EAAgB;CAC/B,IAAIqB,IAAAA,GAAG,GAAG,IAAK9H,CAAAA,WAAL,CAAiBjT,OAAjB,CAAyB0Z,IAAzB,CAAV,CAAA;CACA,IAAA,IAAA,CAAKzG,WAAL,CAAiBlT,MAAjB,CAAwBgb,GAAxB,EAA6B,CAA7B,CAAA,CAAA;CACA5W,IAAAA,aAAa,CAACuV,IAAD,EAAM,QAAN,CAAb,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACClC,EAAAA,gBAAgB,GAAE;CACjBrT,IAAAA,aAAa,CAAC,IAAA,CAAK8O,WAAN,EAAkB,QAAlB,CAAb,CAAA;CACA,IAAKA,IAAAA,CAAAA,WAAL,GAAmB,EAAnB,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC0F,EAAAA,eAAe,CAAEvJ,MAAF,EAA0B4L,MAAc,GAAC,IAAzC,EAAoD;CAElE,IAAA,IAAI5L,MAAM,KAAK,IAAK4D,CAAAA,YAApB,EAAkC;CACjC,MAAA,OAAA;CACA,KAAA;;CAED,IAAA,IAAA,CAAK6G,iBAAL,EAAA,CAAA;CACA,IAAI,IAAA,CAACzK,MAAL,EAAc,OAAA;CAEd,IAAK4D,IAAAA,CAAAA,YAAL,GAAoB5D,MAApB,CAAA;CACAzJ,IAAAA,OAAO,CAAC,IAAK8L,CAAAA,UAAN,EAAiB;CAAC,MAAA,uBAAA,EAAwBrC,MAAM,CAACf,YAAP,CAAoB,IAApB,CAAA;CAAzB,KAAjB,CAAP,CAAA;CACA1I,IAAAA,OAAO,CAACyJ,MAAD,EAAQ;CAAC,MAAgB,eAAA,EAAA,MAAA;CAAjB,KAAR,CAAP,CAAA;CACA5L,IAAAA,UAAU,CAAC4L,MAAD,EAAQ,QAAR,CAAV,CAAA;CACA,IAAA,IAAI4L,MAAJ,EAAa,IAAKC,CAAAA,cAAL,CAAoB7L,MAApB,CAAA,CAAA;CACb,GAAA;CAED;CACD;CACA;CACA;;;CACC6L,EAAAA,cAAc,CAAE7L,MAAF,EAA2B8L,QAA3B,EAAkD;CAE/D,IAAI,IAAA,CAAC9L,MAAL,EAAc,OAAA;CAEd,IAAM7M,MAAAA,OAAO,GAAI,IAAA,CAAKiP,gBAAtB,CAAA;CACA,IAAA,MAAM2J,WAAW,GAAG5Y,OAAO,CAAC6Y,YAA5B,CAAA;CACA,IAAA,MAAMC,SAAS,GAAI9Y,OAAO,CAAC8Y,SAAR,IAAqB,CAAxC,CAAA;CACA,IAAA,MAAMC,WAAW,GAAGlM,MAAM,CAACmM,YAA3B,CAAA;CACA,IAAA,MAAMC,CAAC,GAAMpM,MAAM,CAACqM,qBAAP,EAA+BC,CAAAA,GAA/B,GAAqCnZ,OAAO,CAACkZ,qBAAR,EAAgCC,CAAAA,GAArE,GAA2EL,SAAxF,CAAA;;CAEA,IAAA,IAAIG,CAAC,GAAGF,WAAJ,GAAkBH,WAAW,GAAGE,SAApC,EAA+C;CAC9C,MAAKL,IAAAA,CAAAA,MAAL,CAAYQ,CAAC,GAAGL,WAAJ,GAAkBG,WAA9B,EAA2CJ,QAA3C,CAAA,CAAA;CAEA,KAHD,MAGO,IAAIM,CAAC,GAAGH,SAAR,EAAmB;CACzB,MAAA,IAAA,CAAKL,MAAL,CAAYQ,CAAZ,EAAeN,QAAf,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCF,EAAAA,MAAM,CAAEK,SAAF,EAAoBH,QAApB,EAA2C;CAChD,IAAM3Y,MAAAA,OAAO,GAAG,IAAA,CAAKiP,gBAArB,CAAA;;CACA,IAAA,IAAI0J,QAAJ,EAAc;CACb3Y,MAAAA,OAAO,CAACgB,KAAR,CAAcoY,cAAd,GAA+BT,QAA/B,CAAA;CACA,KAAA;;CACD3Y,IAAAA,OAAO,CAAC8Y,SAAR,GAAoBA,SAApB,CAAA;CACA9Y,IAAAA,OAAO,CAACgB,KAAR,CAAcoY,cAAd,GAA+B,EAA/B,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC9B,EAAAA,iBAAiB,GAAE;CAClB,IAAI,IAAA,IAAA,CAAK7G,YAAT,EAAuB;CACtB7O,MAAAA,aAAa,CAAC,IAAA,CAAK6O,YAAN,EAAmB,QAAnB,CAAb,CAAA;CACArN,MAAAA,OAAO,CAAC,IAAKqN,CAAAA,YAAN,EAAmB;CAAC,QAAgB,eAAA,EAAA,IAAA;CAAjB,OAAnB,CAAP,CAAA;CACA,KAAA;;CACD,IAAKA,IAAAA,CAAAA,YAAL,GAAoB,IAApB,CAAA;CACArN,IAAAA,OAAO,CAAC,IAAK8L,CAAAA,UAAN,EAAiB;CAAC,MAAwB,uBAAA,EAAA,IAAA;CAAzB,KAAjB,CAAP,CAAA;CACA,GAAA;CAGD;CACD;CACA;;;CACC8G,EAAAA,SAAS,GAAG;CACX,IAAMpY,MAAAA,IAAI,GAAG,IAAb,CAAA;CAEA,IAAA,IAAIA,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,QAA3B,EAAqC,OAAA;CAErC,IAAA,MAAMiI,WAAW,GAAG9S,IAAI,CAACyb,eAAL,EAApB,CAAA;CAEA,IAAA,IAAI,CAAC3I,WAAW,CAACpT,MAAjB,EAA0B,OAAA;CAE1BM,IAAAA,IAAI,CAAC0a,SAAL,EAAA,CAAA;CACA1a,IAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CAEAxW,IAAAA,IAAI,CAAC8S,WAAL,GAAmBA,WAAnB,CAAA;CACAtR,IAAAA,SAAO,CAAEsR,WAAF,EAAgByG,IAAD,IAAkB;CACvCvZ,MAAAA,IAAI,CAACwa,kBAAL,CAAwBjB,IAAxB,CAAA,CAAA;CACA,KAFM,CAAP,CAAA;CAIA,GAAA;CAED;CACD;CACA;CACA;;;CACCzD,EAAAA,UAAU,GAAE;CACX,IAAI9V,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEA,IAAI,IAAA,CAACA,IAAI,CAACoR,OAAL,CAAa1M,QAAb,CAAsB1E,IAAI,CAACkR,aAA3B,CAAL,EAAiD,OAAA;CAEjD1L,IAAAA,OAAO,CAACxF,IAAI,CAACkR,aAAN,EAAoB;CAAC3F,MAAAA,WAAW,EAACvL,IAAI,CAACM,QAAL,CAAciL,WAAAA;CAA3B,KAApB,CAAP,CAAA;;CAEA,IAAIvL,IAAAA,IAAI,CAAC8S,WAAL,CAAiBpT,MAAjB,GAA0B,CAA1B,IAAgC,CAACM,IAAI,CAACoS,SAAN,IAAmBpS,IAAI,CAACM,QAAL,CAAckL,eAAjC,IAAoDxL,IAAI,CAACoP,KAAL,CAAW1P,MAAX,GAAoB,CAA5G,EAAgH;CAC/GM,MAAAA,IAAI,CAAC2Z,eAAL,EAAA,CAAA;CACA3Z,MAAAA,IAAI,CAACqS,aAAL,GAAqB,IAArB,CAAA;CAEA,KAJD,MAIK;CAEJ,MAAA,IAAIrS,IAAI,CAACM,QAAL,CAAckL,eAAd,IAAiCxL,IAAI,CAACoP,KAAL,CAAW1P,MAAX,GAAoB,CAAzD,EAA4D;CAC3D8F,QAAAA,OAAO,CAACxF,IAAI,CAACkR,aAAN,EAAoB;CAAC3F,UAAAA,WAAW,EAAC,EAAA;CAAb,SAApB,CAAP,CAAA;CACA,OAAA;;CACDvL,MAAAA,IAAI,CAACqS,aAAL,GAAqB,KAArB,CAAA;CACA,KAAA;;CAEDrS,IAAAA,IAAI,CAACyE,OAAL,CAAaX,SAAb,CAAuB4X,MAAvB,CAA8B,cAA9B,EAA8C1b,IAAI,CAACqS,aAAnD,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCqI,EAAAA,SAAS,GAAG;CACX,IAAA,IAAA,CAAK5E,UAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCkD,EAAAA,SAAS,GAAG;CACX,IAAA,IAAA,CAAKlD,UAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACC0B,EAAAA,UAAU,GAAE;CACX,IAAA,OAAO,KAAKtG,aAAL,CAAmBtF,KAAnB,CAAyBzJ,IAAzB,EAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACCiS,EAAAA,KAAK,GAAG;CACP,IAAIpU,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAIA,IAAAA,IAAI,CAAC+R,UAAT,EAAqB,OAAA;CAErB/R,IAAAA,IAAI,CAACuS,WAAL,GAAmB,IAAnB,CAAA;;CAEA,IAAA,IAAIvS,IAAI,CAACkR,aAAL,CAAmByK,WAAvB,EAAoC;CACnC3b,MAAAA,IAAI,CAACkR,aAAL,CAAmBkD,KAAnB,EAAA,CAAA;CACA,KAFD,MAEK;CACJpU,MAAAA,IAAI,CAACsR,UAAL,CAAgB8C,KAAhB,EAAA,CAAA;CACA,KAAA;;CAED7H,IAAAA,UAAU,CAAC,MAAM;CAChBvM,MAAAA,IAAI,CAACuS,WAAL,GAAmB,KAAnB,CAAA;CACAvS,MAAAA,IAAI,CAACyV,OAAL,EAAA,CAAA;CACA,KAHS,EAGP,CAHO,CAAV,CAAA;CAIA,GAAA;CAED;CACD;CACA;CACA;;;CACCI,EAAAA,IAAI,GAAQ;CACX,IAAKvE,IAAAA,CAAAA,UAAL,CAAgBuE,IAAhB,EAAA,CAAA;CACA,IAAA,IAAA,CAAKL,MAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;;;CACCoG,EAAAA,gBAAgB,CAACja,KAAD,EAAe;CAC9B,IAAO,OAAA,IAAA,CAAKkQ,MAAL,CAAY+J,gBAAZ,CAA6Bja,KAA7B,EAAoC,IAAA,CAAKka,gBAAL,EAApC,CAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;;;CACCA,EAAAA,gBAAgB,GAAG;CAClB,IAAIvb,IAAAA,QAAQ,GAAG,IAAA,CAAKA,QAApB,CAAA;CACA,IAAA,IAAIwb,IAAI,GAAGxb,QAAQ,CAACoK,SAApB,CAAA;;CACA,IAAA,IAAI,OAAOpK,QAAQ,CAACoK,SAAhB,KAA8B,QAAlC,EAA4C;CAC3CoR,MAAAA,IAAI,GAAG,CAAC;CAACC,QAAAA,KAAK,EAAEzb,QAAQ,CAACoK,SAAAA;CAAjB,OAAD,CAAP,CAAA;CACA,KAAA;;CAED,IAAO,OAAA;CACNsR,MAAAA,MAAM,EAAQ1b,QAAQ,CAACqK,WADjB;CAENsR,MAAAA,WAAW,EAAG3b,QAAQ,CAACsK,iBAFjB;CAGNkR,MAAAA,IAAI,EAAUA,IAHR;CAINI,MAAAA,OAAO,EAAO5b,QAAQ,CAAC4b,OAAAA;CAJjB,KAAP,CAAA;CAMA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCC,EAAAA,MAAM,CAACxa,KAAD,EAA6C;CAClD,IAAIya,IAAAA,MAAJ,EAAYC,cAAZ,CAAA;CACA,IAAIrc,IAAAA,IAAI,GAAO,IAAf,CAAA;CACA,IAAA,IAAIkB,OAAO,GAAI,IAAA,CAAK2a,gBAAL,EAAf,CAHkD;;CAMlD,IAAA,IAAK7b,IAAI,CAACM,QAAL,CAAcgc,KAAnB,EAA0B;CACzBD,MAAAA,cAAc,GAAGrc,IAAI,CAACM,QAAL,CAAcgc,KAAd,CAAoBzU,IAApB,CAAyB7H,IAAzB,EAA8B2B,KAA9B,CAAjB,CAAA;;CACA,MAAA,IAAI,OAAO0a,cAAP,KAA0B,UAA9B,EAA0C;CACzC,QAAA,MAAM,IAAI9a,KAAJ,CAAU,uEAAV,CAAN,CAAA;CACA,OAAA;CACD,KAXiD;;;CAclD,IAAA,IAAII,KAAK,KAAK3B,IAAI,CAACoX,SAAnB,EAA8B;CAC7BpX,MAAAA,IAAI,CAACoX,SAAL,GAAmBzV,KAAnB,CAAA;CACAya,MAAAA,MAAM,GAAOpc,IAAI,CAAC6R,MAAL,CAAYsK,MAAZ,CAAmBxa,KAAnB,EAA0BuB,MAAM,CAACC,MAAP,CAAcjC,OAAd,EAAuB;CAACob,QAAAA,KAAK,EAAED,cAAAA;CAAR,OAAvB,CAA1B,CAAb,CAAA;CACArc,MAAAA,IAAI,CAAC0S,cAAL,GAAuB0J,MAAvB,CAAA;CACA,KAJD,MAIO;CACNA,MAAAA,MAAM,GAAOlZ,MAAM,CAACC,MAAP,CAAe,EAAf,EAAmBnD,IAAI,CAAC0S,cAAxB,CAAb,CAAA;CACA,KApBiD;;;CAuBlD,IAAA,IAAI1S,IAAI,CAACM,QAAL,CAAcoJ,YAAlB,EAAgC;CAC/B0S,MAAAA,MAAM,CAAChN,KAAP,GAAegN,MAAM,CAAChN,KAAP,CAAahL,MAAb,CAAqBmV,IAAD,IAAU;CAC5C,QAAA,IAAIgD,MAAM,GAAG5Q,QAAQ,CAAC4N,IAAI,CAACvL,EAAN,CAArB,CAAA;CACA,QAAA,OAAO,EAAEuO,MAAM,IAAIvc,IAAI,CAACoP,KAAL,CAAWvP,OAAX,CAAmB0c,MAAnB,CAA+B,KAAA,CAAC,CAA5C,CAAP,CAAA;CACA,OAHc,CAAf,CAAA;CAIA,KAAA;;CAED,IAAA,OAAOH,MAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCtD,EAAAA,cAAc,CAAE0D,eAAuB,GAAG,IAA5B,EAAkC;CAC/C,IAAA,IAAIlX,CAAJ,EAAOmX,CAAP,EAAUC,CAAV,EAAald,CAAb,EAAgB6Q,QAAhB,EAA0BvH,SAA1B,EAAqC6T,IAArC,EAA4DC,iBAA5D,EAA+EC,YAA/E,CAAA;CACA,IAAA,IAAI1T,MAAJ,CAAA;CACA,IAAM2T,MAAAA,MAAuC,GAAG,EAAhD,CAAA;CAEA,IAAMC,MAAAA,YAAqB,GAAG,EAA9B,CAAA;CACA,IAAI/c,IAAAA,IAAI,GAAO,IAAf,CAAA;CACA,IAAA,IAAI2B,KAAK,GAAO3B,IAAI,CAACwX,UAAL,EAAhB,CAAA;CACA,IAAA,MAAMwF,UAAU,GAAKrb,KAAK,KAAK3B,IAAI,CAACoX,SAAf,IAA6BzV,KAAK,IAAI,EAAT,IAAe3B,IAAI,CAACoX,SAAL,IAAkB,IAAnF,CAAA;CACA,IAAA,IAAI6F,OAAO,GAAOjd,IAAI,CAACmc,MAAL,CAAYxa,KAAZ,CAAlB,CAAA;CACA,IAAIub,IAAAA,aAAa,GAAK,IAAtB,CAAA;CACA,IAAIC,IAAAA,aAAa,GAAKnd,IAAI,CAACM,QAAL,CAAciJ,UAAd,IAA4B,KAAlD,CAAA;CACA,IAAA,IAAI8H,gBAAgB,GAAIrR,IAAI,CAACqR,gBAA7B,CAAA;;CAGA,IAAA,IAAI2L,UAAJ,EAAgB;CACfE,MAAAA,aAAa,GAAKld,IAAI,CAAC6S,YAAvB,CAAA;;CAEA,MAAA,IAAIqK,aAAJ,EAAmB;CAClBL,QAAAA,YAAY,GAAGK,aAAa,CAACE,OAAd,CAAsB,cAAtB,CAAf,CAAA;CACA,OAAA;CACD,KArB8C;;;CAwB/C5d,IAAAA,CAAC,GAAGyd,OAAO,CAAC7N,KAAR,CAAc1P,MAAlB,CAAA;;CACA,IAAI,IAAA,OAAOM,IAAI,CAACM,QAAL,CAAckJ,UAArB,KAAoC,QAAxC,EAAkD;CACjDhK,MAAAA,CAAC,GAAG4M,IAAI,CAACiR,GAAL,CAAS7d,CAAT,EAAYQ,IAAI,CAACM,QAAL,CAAckJ,UAA1B,CAAJ,CAAA;CACA,KAAA;;CAED,IAAIhK,IAAAA,CAAC,GAAG,CAAR,EAAW;CACV2d,MAAAA,aAAa,GAAG,IAAhB,CAAA;CACA,KA/B8C;;;CAkC/C,IAAK7X,KAAAA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG9F,CAAhB,EAAmB8F,CAAC,EAApB,EAAwB;CAEvB;CACA,MAAA,IAAIiU,IAAI,GAAK0D,OAAO,CAAC7N,KAAR,CAAc9J,CAAd,CAAb,CAAA;CACA,MAAI,IAAA,CAACiU,IAAL,EAAY,SAAA;CAEZ,MAAA,IAAI+D,SAAS,GAAI/D,IAAI,CAACvL,EAAtB,CAAA;CACA,MAAA,IAAIiB,MAAM,GAAKjP,IAAI,CAACkB,OAAL,CAAaoc,SAAb,CAAf,CAAA;CAEA,MAAIrO,IAAAA,MAAM,KAAKtP,SAAf,EAA2B,SAAA;CAE3B,MAAA,IAAI4d,QAAQ,GAAI1R,QAAQ,CAACyR,SAAD,CAAxB,CAAA;CACA,MAAIE,IAAAA,SAAS,GAAIxd,IAAI,CAACyd,SAAL,CAAeF,QAAf,EAAwB,IAAxB,CAAjB,CAZuB;;CAevB,MAAA,IAAI,CAACvd,IAAI,CAACM,QAAL,CAAcoJ,YAAnB,EAAiC;CAChC8T,QAAAA,SAAS,CAAC1Z,SAAV,CAAoB4X,MAApB,CAA2B,UAA3B,EAAuC1b,IAAI,CAACoP,KAAL,CAAWsO,QAAX,CAAoBH,QAApB,CAAvC,CAAA,CAAA;CACA,OAAA;;CAEDlN,MAAAA,QAAQ,GAAMpB,MAAM,CAACjP,IAAI,CAACM,QAAL,CAAc6J,aAAf,CAAN,IAAuC,EAArD,CAAA;CACArB,MAAAA,SAAS,GAAK/H,KAAK,CAACC,OAAN,CAAcqP,QAAd,CAAA,GAA0BA,QAA1B,GAAqC,CAACA,QAAD,CAAnD,CAAA;;CAEA,MAAA,KAAKoM,CAAC,GAAG,CAAJ,EAAOC,CAAC,GAAG5T,SAAS,IAAIA,SAAS,CAACpJ,MAAvC,EAA+C+c,CAAC,GAAGC,CAAnD,EAAsDD,CAAC,EAAvD,EAA2D;CAC1DpM,QAAAA,QAAQ,GAAGvH,SAAS,CAAC2T,CAAD,CAApB,CAAA;;CACA,QAAI,IAAA,CAACzc,IAAI,CAAC8I,SAAL,CAAe3H,cAAf,CAA8BkP,QAA9B,CAAL,EAA8C;CAC7CA,UAAAA,QAAQ,GAAG,EAAX,CAAA;CACA,SAAA;;CAED,QAAA,IAAIsN,cAAc,GAAGb,MAAM,CAACzM,QAAD,CAA3B,CAAA;;CACA,QAAIsN,IAAAA,cAAc,KAAKhe,SAAvB,EAAkC;CACjCge,UAAAA,cAAc,GAAG3b,QAAQ,CAAC4b,sBAAT,EAAjB,CAAA;CACAb,UAAAA,YAAY,CAACzd,IAAb,CAAkB+Q,QAAlB,CAAA,CAAA;CACA,SAVyD;;;CAa1D,QAAIoM,IAAAA,CAAC,GAAG,CAAR,EAAW;CACVe,UAAAA,SAAS,GAAGA,SAAS,CAACxW,SAAV,CAAoB,IAApB,CAAZ,CAAA;CACAxB,UAAAA,OAAO,CAACgY,SAAD,EAAW;CAACxP,YAAAA,EAAE,EAAEiB,MAAM,CAAC4O,GAAP,GAAW,SAAX,GAAqBpB,CAA1B;CAA4B,YAAgB,eAAA,EAAA,IAAA;CAA5C,WAAX,CAAP,CAAA;CACAe,UAAAA,SAAS,CAAC1Z,SAAV,CAAoBC,GAApB,CAAwB,WAAxB,CAAA,CAAA;CACAC,UAAAA,aAAa,CAACwZ,SAAD,EAAW,QAAX,CAAb,CAJU;;CAQV,UAAA,IAAIxd,IAAI,CAAC6S,YAAL,IAAqB7S,IAAI,CAAC6S,YAAL,CAAkBpD,OAAlB,CAA0B7D,KAA1B,IAAmC0R,SAA5D,EAAuE;CACtE,YAAA,IAAIT,YAAY,IAAIA,YAAY,CAACpN,OAAb,CAAqBK,KAArB,KAA+BO,QAAQ,CAACyN,QAAT,EAAnD,EAAwE;CACvEZ,cAAAA,aAAa,GAAGM,SAAhB,CAAA;CACA,aAAA;CACD,WAAA;CACD,SAAA;;CAEDG,QAAAA,cAAc,CAAC1W,WAAf,CAA2BuW,SAA3B,CAAA,CAAA;CACAV,QAAAA,MAAM,CAACzM,QAAD,CAAN,GAAmBsN,cAAnB,CAAA;CACA,OAAA;CACD,KAvF8C;;;CA0F/C,IAAA,IAAI3d,IAAI,CAACM,QAAL,CAAcmK,iBAAlB,EAAqC;CACpCsS,MAAAA,YAAY,CAACjB,IAAb,CAAkB,CAACiC,CAAD,EAAIC,CAAJ,KAAU;CAC3B,QAAA,MAAMC,KAAK,GAAIje,IAAI,CAAC8I,SAAL,CAAeiV,CAAf,CAAf,CAAA;CACA,QAAA,MAAMG,KAAK,GAAIle,IAAI,CAAC8I,SAAL,CAAekV,CAAf,CAAf,CAAA;CACA,QAAMG,MAAAA,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAACG,MAAf,IAAyB,CAAzC,CAAA;CACA,QAAMC,MAAAA,OAAO,GAAGH,KAAK,IAAIA,KAAK,CAACE,MAAf,IAAyB,CAAzC,CAAA;CACA,QAAOD,OAAAA,OAAO,GAAGE,OAAjB,CAAA;CACA,OAND,CAAA,CAAA;CAOA,KAlG8C;;;CAqG/C1B,IAAAA,IAAI,GAAG3a,QAAQ,CAAC4b,sBAAT,EAAP,CAAA;CACApc,IAAAA,SAAO,CAAEub,YAAF,EAAiB1M,QAAD,IAAqB;CAE3C,MAAA,IAAIsN,cAAc,GAAGb,MAAM,CAACzM,QAAD,CAA3B,CAAA;CAEA,MAAI,IAAA,CAACsN,cAAD,IAAmB,CAACA,cAAc,CAACpN,QAAf,CAAwB7Q,MAAhD,EAAyD,OAAA;CAEzD,MAAA,IAAI4e,aAAa,GAAGte,IAAI,CAAC8I,SAAL,CAAeuH,QAAf,CAApB,CAAA;;CAEA,MAAIiO,IAAAA,aAAa,KAAK3e,SAAtB,EAAiC;CAEhC,QAAA,IAAI4e,aAAa,GAAGvc,QAAQ,CAAC4b,sBAAT,EAApB,CAAA;CACA,QAAIY,IAAAA,MAAM,GAAGxe,IAAI,CAAC0L,MAAL,CAAY,iBAAZ,EAA+B4S,aAA/B,CAAb,CAAA;CACAlQ,QAAAA,MAAM,CAAEmQ,aAAF,EAAiBC,MAAjB,CAAN,CAAA;CACApQ,QAAAA,MAAM,CAAEmQ,aAAF,EAAiBZ,cAAjB,CAAN,CAAA;CAEA,QAAA,IAAIc,UAAU,GAAGze,IAAI,CAAC0L,MAAL,CAAY,UAAZ,EAAwB;CAACoE,UAAAA,KAAK,EAACwO,aAAP;CAAqBpd,UAAAA,OAAO,EAACqd,aAAAA;CAA7B,SAAxB,CAAjB,CAAA;CAEAnQ,QAAAA,MAAM,CAAEuO,IAAF,EAAQ8B,UAAR,CAAN,CAAA;CAEA,OAXD,MAWO;CACNrQ,QAAAA,MAAM,CAAEuO,IAAF,EAAQgB,cAAR,CAAN,CAAA;CACA,OAAA;CACD,KAtBM,CAAP,CAAA;CAwBAtM,IAAAA,gBAAgB,CAACnP,SAAjB,GAA6B,EAA7B,CAAA;CACAkM,IAAAA,MAAM,CAAEiD,gBAAF,EAAoBsL,IAApB,CAAN,CA/H+C;;CAkI/C,IAAA,IAAI3c,IAAI,CAACM,QAAL,CAAc4F,SAAlB,EAA6B;CAC5BuB,MAAAA,eAAe,CAAE4J,gBAAF,CAAf,CAAA;;CACA,MAAI4L,IAAAA,OAAO,CAACtb,KAAR,CAAcjC,MAAd,IAAwBud,OAAO,CAACyB,MAAR,CAAehf,MAA3C,EAAmD;CAClD8B,QAAAA,SAAO,CAAEyb,OAAO,CAACyB,MAAV,EAAmBC,GAAD,IAAS;CACjCzY,UAAAA,SAAS,CAAEmL,gBAAF,EAAoBsN,GAAG,CAACvY,KAAxB,CAAT,CAAA;CACA,SAFM,CAAP,CAAA;CAGA,OAAA;CACD,KAzI8C;;;CA4I/C,IAAIwY,IAAAA,YAAY,GAAIC,QAAD,IAA+B;CACjD,MAAA,IAAIzc,OAAO,GAAGpC,IAAI,CAAC0L,MAAL,CAAYmT,QAAZ,EAAqB;CAAC/R,QAAAA,KAAK,EAACnL,KAAAA;CAAP,OAArB,CAAd,CAAA;;CACA,MAAA,IAAIS,OAAJ,EAAa;CACZ+a,QAAAA,aAAa,GAAG,IAAhB,CAAA;CACA9L,QAAAA,gBAAgB,CAACyN,YAAjB,CAA8B1c,OAA9B,EAAuCiP,gBAAgB,CAAChP,UAAxD,CAAA,CAAA;CACA,OAAA;;CACD,MAAA,OAAOD,OAAP,CAAA;CACA,KAPD,CA5I+C;;;CAuJ/C,IAAIpC,IAAAA,IAAI,CAACmM,OAAT,EAAkB;CACjByS,MAAAA,YAAY,CAAC,SAAD,CAAZ,CADiB;CAIjB,KAJD,MAIM,IAAI,CAAC5e,IAAI,CAACM,QAAL,CAAcmL,UAAd,CAAyB5D,IAAzB,CAA8B7H,IAA9B,EAAmC2B,KAAnC,CAAL,EAAgD;CACrDid,MAAAA,YAAY,CAAC,aAAD,CAAZ,CADqD;CAIrD,KAJK,MAIA,IAAI3B,OAAO,CAAC7N,KAAR,CAAc1P,MAAd,KAAyB,CAA7B,EAAgC;CACrCkf,MAAAA,YAAY,CAAC,YAAD,CAAZ,CAAA;CAEA,KAlK8C;;;CAuK/ChC,IAAAA,iBAAiB,GAAG5c,IAAI,CAAC+e,SAAL,CAAepd,KAAf,CAApB,CAAA;;CACA,IAAA,IAAIib,iBAAJ,EAAuB;CACtBzT,MAAAA,MAAM,GAAGyV,YAAY,CAAC,eAAD,CAArB,CAAA;CACA,KA1K8C;;;CA8K/C5e,IAAAA,IAAI,CAACyS,UAAL,GAAkBwK,OAAO,CAAC7N,KAAR,CAAc1P,MAAd,GAAuB,CAAvB,IAA4Bkd,iBAA9C,CAAA;;CACA,IAAA,IAAIO,aAAJ,EAAmB;CAElB,MAAA,IAAIF,OAAO,CAAC7N,KAAR,CAAc1P,MAAd,GAAuB,CAA3B,EAA8B;CAE7B,QAAA,IAAI,CAACwd,aAAD,IAAkBld,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,QAAzC,IAAqD7K,IAAI,CAACoP,KAAL,CAAW,CAAX,CAAA,IAAiBzP,SAA1E,EAAqF;CACpFud,UAAAA,aAAa,GAAGld,IAAI,CAACyd,SAAL,CAAezd,IAAI,CAACoP,KAAL,CAAW,CAAX,CAAf,CAAhB,CAAA;CACA,SAAA;;CAED,QAAA,IAAI,CAACiC,gBAAgB,CAAC3M,QAAjB,CAA0BwY,aAA1B,CAAL,EAAgD;CAE/C,UAAI8B,IAAAA,YAAY,GAAG,CAAnB,CAAA;;CACA,UAAI7V,IAAAA,MAAM,IAAI,CAACnJ,IAAI,CAACM,QAAL,CAAcsJ,aAA7B,EAA4C;CAC3CoV,YAAAA,YAAY,GAAG,CAAf,CAAA;CACA,WAAA;;CACD9B,UAAAA,aAAa,GAAGld,IAAI,CAACif,UAAL,EAAA,CAAkBD,YAAlB,CAAhB,CAAA;CACA,SAAA;CAED,OAfD,MAeM,IAAI7V,MAAJ,EAAY;CACjB+T,QAAAA,aAAa,GAAG/T,MAAhB,CAAA;CACA,OAAA;;CAED,MAAA,IAAIqT,eAAe,IAAI,CAACxc,IAAI,CAAC8R,MAA7B,EAAqC;CACpC9R,QAAAA,IAAI,CAACqY,IAAL,EAAA,CAAA;CACArY,QAAAA,IAAI,CAAC8a,cAAL,CAAoBoC,aAApB,EAAkC,MAAlC,CAAA,CAAA;CACA,OAAA;;CACDld,MAAAA,IAAI,CAACwY,eAAL,CAAqB0E,aAArB,CAAA,CAAA;CAEA,KA3BD,MA2BK;CACJld,MAAAA,IAAI,CAAC0Z,iBAAL,EAAA,CAAA;;CACA,MAAA,IAAI8C,eAAe,IAAIxc,IAAI,CAAC8R,MAA5B,EAAoC;CACnC9R,QAAAA,IAAI,CAACwW,KAAL,CAAW,KAAX,EADmC;CAEnC,OAAA;CACD,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCyI,EAAAA,UAAU,GAAW;CACpB,IAAA,OAAO,KAAK5N,gBAAL,CAAsB1J,gBAAtB,CAAuC,mBAAvC,CAAP,CAAA;CACA,GAAA;CAID;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;CACCkI,EAAAA,SAAS,CAAEpJ,IAAF,EAAkByY,YAAY,GAAG,KAAjC,EAAsD;CAC9D,IAAA,MAAMlf,IAAI,GAAG,IAAb,CAD8D;CAI9D;;CACA,IAAA,IAAIe,KAAK,CAACC,OAAN,CAAcyF,IAAd,CAAJ,EAAyB;CACxBzG,MAAAA,IAAI,CAAC6W,UAAL,CAAiBpQ,IAAjB,EAAuByY,YAAvB,CAAA,CAAA;CACA,MAAA,OAAO,KAAP,CAAA;CACA,KAAA;;CAED,IAAA,MAAMre,GAAG,GAAG8K,QAAQ,CAAClF,IAAI,CAACzG,IAAI,CAACM,QAAL,CAAc8J,UAAf,CAAL,CAApB,CAAA;;CACA,IAAA,IAAIvJ,GAAG,KAAK,IAAR,IAAgBb,IAAI,CAACkB,OAAL,CAAaC,cAAb,CAA4BN,GAA5B,CAApB,EAAsD;CACrD,MAAA,OAAO,KAAP,CAAA;CACA,KAAA;;CAED4F,IAAAA,IAAI,CAAC2X,MAAL,GAAgB3X,IAAI,CAAC2X,MAAL,IAAe,EAAEpe,IAAI,CAACuR,KAAtC,CAAA;CACA9K,IAAAA,IAAI,CAACoX,GAAL,GAAa7d,IAAI,CAAC2R,OAAL,GAAe,OAAf,GAAyBlL,IAAI,CAAC2X,MAA3C,CAAA;CACApe,IAAAA,IAAI,CAACkB,OAAL,CAAaL,GAAb,IAAoB4F,IAApB,CAAA;CACAzG,IAAAA,IAAI,CAACoX,SAAL,GAAkB,IAAlB,CAAA;;CAEA,IAAA,IAAI8H,YAAJ,EAAkB;CACjBlf,MAAAA,IAAI,CAAC+S,WAAL,CAAiBlS,GAAjB,IAAwBqe,YAAxB,CAAA;CACAlf,MAAAA,IAAI,CAACF,OAAL,CAAa,YAAb,EAA2Be,GAA3B,EAAgC4F,IAAhC,CAAA,CAAA;CACA,KAAA;;CAED,IAAA,OAAO5F,GAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCgW,EAAAA,UAAU,CAAEpQ,IAAF,EAAoByY,YAAY,GAAG,KAAnC,EAA+C;CACxD1d,IAAAA,SAAO,CAAEiF,IAAF,EAAS0Y,GAAD,IAAmB;CACjC,MAAA,IAAA,CAAKtP,SAAL,CAAesP,GAAf,EAAoBD,YAApB,CAAA,CAAA;CACA,KAFM,CAAP,CAAA;CAGA,GAAA;CAED;CACD;CACA;;;CACCE,EAAAA,cAAc,CAAE3Y,IAAF,EAAgC;CAC7C,IAAA,OAAO,IAAKoJ,CAAAA,SAAL,CAAepJ,IAAf,CAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCqQ,EAAAA,mBAAmB,CAACrQ,IAAD,EAAiB;CACnC,IAAI5F,IAAAA,GAAG,GAAG8K,QAAQ,CAAClF,IAAI,CAAC,IAAA,CAAKnG,QAAL,CAAckK,kBAAf,CAAL,CAAlB,CAAA;CAEA,IAAA,IAAK3J,GAAG,KAAK,IAAb,EAAoB,OAAO,KAAP,CAAA;CAEpB4F,IAAAA,IAAI,CAAC2X,MAAL,GAAc3X,IAAI,CAAC2X,MAAL,IAAe,EAAE,IAAA,CAAK7M,KAApC,CAAA;CACA,IAAA,IAAA,CAAKzI,SAAL,CAAejI,GAAf,CAAA,GAAsB4F,IAAtB,CAAA;CACA,IAAA,OAAO5F,GAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCwe,EAAAA,cAAc,CAACrR,EAAD,EAAYvH,IAAZ,EAA4B;CACzC,IAAA,IAAI6Y,SAAJ,CAAA;CACA7Y,IAAAA,IAAI,CAAC,IAAKnG,CAAAA,QAAL,CAAckK,kBAAf,CAAJ,GAAyCwD,EAAzC,CAAA;;CAEA,IAAA,IAAIsR,SAAS,GAAG,IAAA,CAAKxI,mBAAL,CAAyBrQ,IAAzB,CAAhB,EAAgD;CAC/C,MAAA,IAAA,CAAK3G,OAAL,CAAa,cAAb,EAA6Bwf,SAA7B,EAAwC7Y,IAAxC,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACC8Y,EAAAA,iBAAiB,CAACvR,EAAD,EAAY;CAC5B,IAAA,IAAI,KAAKlF,SAAL,CAAe3H,cAAf,CAA8B6M,EAA9B,CAAJ,EAAuC;CACtC,MAAA,OAAO,IAAKlF,CAAAA,SAAL,CAAekF,EAAf,CAAP,CAAA;CACA,MAAA,IAAA,CAAKwR,UAAL,EAAA,CAAA;CACA,MAAA,IAAA,CAAK1f,OAAL,CAAa,iBAAb,EAAgCkO,EAAhC,CAAA,CAAA;CACA,KAAA;CACD,GAAA;CAED;CACD;CACA;;;CACCyR,EAAAA,iBAAiB,GAAG;CACnB,IAAK3W,IAAAA,CAAAA,SAAL,GAAiB,EAAjB,CAAA;CACA,IAAA,IAAA,CAAK0W,UAAL,EAAA,CAAA;CACA,IAAK1f,IAAAA,CAAAA,OAAL,CAAa,gBAAb,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;;;CACC4f,EAAAA,YAAY,CAAC9T,KAAD,EAAenF,IAAf,EAA+B;CAC1C,IAAMzG,MAAAA,IAAI,GAAG,IAAb,CAAA;CACA,IAAA,IAAI2f,QAAJ,CAAA;CACA,IAAA,IAAIC,UAAJ,CAAA;CAEA,IAAA,MAAMC,SAAS,GAAIlU,QAAQ,CAACC,KAAD,CAA3B,CAAA;CACA,IAAA,MAAMkU,SAAS,GAAInU,QAAQ,CAAClF,IAAI,CAACzG,IAAI,CAACM,QAAL,CAAc8J,UAAf,CAAL,CAA3B,CAN0C;;CAS1C,IAAIyV,IAAAA,SAAS,KAAK,IAAlB,EAAyB,OAAA;CAEzB,IAAA,MAAME,QAAQ,GAAI/f,IAAI,CAACkB,OAAL,CAAa2e,SAAb,CAAlB,CAAA;CAEA,IAAIE,IAAAA,QAAQ,IAAIpgB,SAAhB,EAA4B,OAAA;CAC5B,IAAI,IAAA,OAAOmgB,SAAP,KAAqB,QAAzB,EAAoC,MAAM,IAAIve,KAAJ,CAAU,kCAAV,CAAN,CAAA;CAGpC,IAAA,MAAM0N,MAAM,GAAIjP,IAAI,CAACyd,SAAL,CAAeoC,SAAf,CAAhB,CAAA;CACA,IAAA,MAAMtG,IAAI,GAAKvZ,IAAI,CAACggB,OAAL,CAAaH,SAAb,CAAf,CAAA;CAGApZ,IAAAA,IAAI,CAAC2X,MAAL,GAAc3X,IAAI,CAAC2X,MAAL,IAAe2B,QAAQ,CAAC3B,MAAtC,CAAA;CACA,IAAA,OAAOpe,IAAI,CAACkB,OAAL,CAAa2e,SAAb,CAAP,CAtB0C;CAyB1C;;CACA7f,IAAAA,IAAI,CAACigB,YAAL,CAAkBH,SAAlB,CAAA,CAAA;CAEA9f,IAAAA,IAAI,CAACkB,OAAL,CAAa4e,SAAb,CAA0BrZ,GAAAA,IAA1B,CA5B0C;;CA+B1C,IAAA,IAAIwI,MAAJ,EAAY;CACX,MAAIjP,IAAAA,IAAI,CAACqR,gBAAL,CAAsB3M,QAAtB,CAA+BuK,MAA/B,CAAJ,EAA4C;CAE3C,QAAMiR,MAAAA,UAAU,GAAGlgB,IAAI,CAAC0T,OAAL,CAAa,QAAb,EAAuBjN,IAAvB,CAAnB,CAAA;;CACAX,QAAAA,WAAW,CAACmJ,MAAD,EAASiR,UAAT,CAAX,CAAA;;CAEA,QAAA,IAAIlgB,IAAI,CAAC6S,YAAL,KAAsB5D,MAA1B,EAAkC;CACjCjP,UAAAA,IAAI,CAACwY,eAAL,CAAqB0H,UAArB,CAAA,CAAA;CACA,SAAA;CACD,OAAA;;CACDjR,MAAAA,MAAM,CAAChL,MAAP,EAAA,CAAA;CACA,KA1CyC;;;CA6C1C,IAAA,IAAIsV,IAAJ,EAAU;CACTqG,MAAAA,UAAU,GAAG5f,IAAI,CAACoP,KAAL,CAAWvP,OAAX,CAAmBggB,SAAnB,CAAb,CAAA;;CACA,MAAA,IAAID,UAAU,KAAK,CAAC,CAApB,EAAuB;CACtB5f,QAAAA,IAAI,CAACoP,KAAL,CAAWxP,MAAX,CAAkBggB,UAAlB,EAA8B,CAA9B,EAAiCE,SAAjC,CAAA,CAAA;CACA,OAAA;;CAEDH,MAAAA,QAAQ,GAAG3f,IAAI,CAAC0T,OAAL,CAAa,MAAb,EAAqBjN,IAArB,CAAX,CAAA;CAEA,MAAA,IAAI8S,IAAI,CAACzV,SAAL,CAAeY,QAAf,CAAwB,QAAxB,CAAJ,EAAwCrB,UAAU,CAACsc,QAAD,EAAU,QAAV,CAAV,CAAA;CAExC7Z,MAAAA,WAAW,CAAEyT,IAAF,EAAQoG,QAAR,CAAX,CAAA;CACA,KAxDyC;;;CA2D1C3f,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC+I,EAAAA,YAAY,CAACvU,KAAD,EAAekO,MAAf,EAAqC;CAChD,IAAM9Z,MAAAA,IAAI,GAAG,IAAb,CAAA;CACA4L,IAAAA,KAAK,GAAGC,QAAQ,CAACD,KAAD,CAAhB,CAAA;CAEA5L,IAAAA,IAAI,CAACigB,YAAL,CAAkBrU,KAAlB,CAAA,CAAA;CAEA,IAAA,OAAO5L,IAAI,CAAC+S,WAAL,CAAiBnH,KAAjB,CAAP,CAAA;CACA,IAAA,OAAO5L,IAAI,CAACkB,OAAL,CAAa0K,KAAb,CAAP,CAAA;CACA5L,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;CACApX,IAAAA,IAAI,CAACF,OAAL,CAAa,eAAb,EAA8B8L,KAA9B,CAAA,CAAA;CACA5L,IAAAA,IAAI,CAACogB,UAAL,CAAgBxU,KAAhB,EAAuBkO,MAAvB,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACCuG,EAAAA,YAAY,CAACjc,MAAD,EAA0B;CAErC,IAAMkc,MAAAA,WAAW,GAAG,CAAClc,MAAM,IAAI,IAAKmc,CAAAA,WAAhB,EAA6BlM,IAA7B,CAAkC,IAAlC,CAApB,CAAA;CAEA,IAAK7H,IAAAA,CAAAA,cAAL,GAAuB,EAAvB,CAAA;CACA,IAAKuG,IAAAA,CAAAA,WAAL,GAAoB,EAApB,CAAA;CACA,IAAA,IAAA,CAAKyM,UAAL,EAAA,CAAA;CAEA,IAAMrP,MAAAA,QAAmB,GAAG,EAA5B,CAAA;CACA3O,IAAAA,SAAO,CAAC,IAAKN,CAAAA,OAAN,EAAc,CAAC+N,MAAD,EAAkBpO,GAAlB,KAA+B;CACnD,MAAA,IAAIyf,WAAW,CAACrR,MAAD,EAAQpO,GAAR,CAAf,EAAuC;CACtCsP,QAAAA,QAAQ,CAACtP,GAAD,CAAR,GAAgBoO,MAAhB,CAAA;CACA,OAAA;CACD,KAJM,CAAP,CAAA;CAMA,IAAA,IAAA,CAAK/N,OAAL,GAAe,IAAA,CAAK2Q,MAAL,CAAYzC,KAAZ,GAAoBe,QAAnC,CAAA;CACA,IAAKiH,IAAAA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;CACA,IAAKtX,IAAAA,CAAAA,OAAL,CAAa,cAAb,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCygB,EAAAA,WAAW,CAACtR,MAAD,EAAkBrD,KAAlB,EAA+B;CACzC,IAAI,IAAA,IAAA,CAAKwD,KAAL,CAAWvP,OAAX,CAAmB+L,KAAnB,CAAA,IAA6B,CAAjC,EAAoC;CACnC,MAAA,OAAO,IAAP,CAAA;CACA,KAAA;;CACD,IAAA,OAAO,KAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC6R,EAAAA,SAAS,CAAC7R,KAAD,EAA6CzC,MAAc,GAAC,KAA5D,EAAoF;CAE5F,IAAA,MAAMoT,MAAM,GAAG5Q,QAAQ,CAACC,KAAD,CAAvB,CAAA;CACA,IAAA,IAAI2Q,MAAM,KAAK,IAAf,EAAsB,OAAO,IAAP,CAAA;CAEtB,IAAA,MAAMtN,MAAM,GAAG,IAAA,CAAK/N,OAAL,CAAaqb,MAAb,CAAf,CAAA;;CACA,IAAItN,IAAAA,MAAM,IAAItP,SAAd,EAAyB;CAExB,MAAIsP,IAAAA,MAAM,CAACuR,IAAX,EAAiB;CAChB,QAAOvR,OAAAA,MAAM,CAACuR,IAAd,CAAA;CACA,OAAA;;CAED,MAAA,IAAIrX,MAAJ,EAAY;CACX,QAAA,OAAO,KAAKuK,OAAL,CAAa,QAAb,EAAuBzE,MAAvB,CAAP,CAAA;CACA,OAAA;CACD,KAAA;;CAED,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCsJ,EAAAA,WAAW,CAAEtJ,MAAF,EAA2BlK,SAA3B,EAA6C4H,IAAW,GAAG,QAA3D,EAAwF;CAClG,IAAI3M,IAAAA,IAAI,GAAG,IAAX;CAAA,QAAiBygB,GAAjB,CAAA;;CAEA,IAAI,IAAA,CAACxR,MAAL,EAAa;CACZ,MAAA,OAAO,IAAP,CAAA;CACA,KAAA;;CAED,IAAItC,IAAAA,IAAI,IAAI,MAAZ,EAAoB;CACnB8T,MAAAA,GAAG,GAAKzgB,IAAI,CAACyb,eAAL,EAAR,CAAA;CACA,KAFD,MAEK;CACJgF,MAAAA,GAAG,GAAKzgB,IAAI,CAACqR,gBAAL,CAAsB1J,gBAAtB,CAAuC,mBAAvC,CAAR,CAAA;CACA,KAAA;;CAED,IAAA,KAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmb,GAAG,CAAC/gB,MAAxB,EAAgC4F,CAAC,EAAjC,EAAqC;CACpC,MAAA,IAAImb,GAAG,CAACnb,CAAD,CAAH,IAAU2J,MAAd,EAAsB;CACrB,QAAA,SAAA;CACA,OAAA;;CAED,MAAIlK,IAAAA,SAAS,GAAG,CAAhB,EAAmB;CAClB,QAAA,OAAO0b,GAAG,CAACnb,CAAC,GAAC,CAAH,CAAV,CAAA;CACA,OAAA;;CAED,MAAA,OAAOmb,GAAG,CAACnb,CAAC,GAAC,CAAH,CAAV,CAAA;CACA,KAAA;;CACD,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAGD;CACD;CACA;CACA;CACA;;;CACC0a,EAAAA,OAAO,CAACzG,IAAD,EAAwC;CAE9C,IAAA,IAAI,OAAOA,IAAP,IAAe,QAAnB,EAA6B;CAC5B,MAAA,OAAOA,IAAP,CAAA;CACA,KAAA;;CAED,IAAA,IAAI3N,KAAK,GAAGD,QAAQ,CAAC4N,IAAD,CAApB,CAAA;CACA,IAAA,OAAO3N,KAAK,KAAK,IAAV,GACJ,IAAA,CAAKwF,OAAL,CAAa9O,aAAb,CAA4B,CAAA,aAAA,EAAe6L,UAAU,CAACvC,KAAD,CAAQ,CAA7D,EAAA,CAAA,CADI,GAEJ,IAFH,CAAA;CAGA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCoO,EAAAA,QAAQ,CAAEpJ,MAAF,EAA0BkJ,MAA1B,EAAgD;CACvD,IAAI9Z,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEA,IAAA,IAAIoP,KAAK,GAAGrO,KAAK,CAACC,OAAN,CAAc4P,MAAd,CAAA,GAAwBA,MAAxB,GAAiC,CAACA,MAAD,CAA7C,CAAA;CACAxB,IAAAA,KAAK,GAAGA,KAAK,CAAChL,MAAN,CAAasc,CAAC,IAAI1gB,IAAI,CAACoP,KAAL,CAAWvP,OAAX,CAAmB6gB,CAAnB,CAA0B,KAAA,CAAC,CAA7C,CAAR,CAAA;CACA,IAAMC,MAAAA,SAAS,GAAGvR,KAAK,CAACA,KAAK,CAAC1P,MAAN,GAAe,CAAhB,CAAvB,CAAA;CACA0P,IAAAA,KAAK,CAACtQ,OAAN,CAAcya,IAAI,IAAI;CACrBvZ,MAAAA,IAAI,CAAC4gB,SAAL,GAAkBrH,IAAI,KAAKoH,SAA3B,CAAA;CACA3gB,MAAAA,IAAI,CAAC4X,OAAL,CAAa2B,IAAb,EAAmBO,MAAnB,CAAA,CAAA;CACA,KAHD,CAAA,CAAA;CAIA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACClC,EAAAA,OAAO,CAAEhM,KAAF,EAAgBkO,MAAhB,EAAsC;CAC5C,IAAInb,IAAAA,MAAM,GAAGmb,MAAM,GAAG,EAAH,GAAQ,CAAC,QAAD,EAAU,gBAAV,CAA3B,CAAA;CAEArN,IAAAA,eAAe,CAAC,IAAD,EAAO9N,MAAP,EAAe,MAAM;CACnC,MAAI4a,IAAAA,IAAJ,EAAUsH,OAAV,CAAA;CACA,MAAM7gB,MAAAA,IAAI,GAAG,IAAb,CAAA;CACC,MAAA,MAAM2T,SAAS,GAAG3T,IAAI,CAACM,QAAL,CAAcuK,IAAhC,CAAA;CACD,MAAA,MAAM0R,MAAM,GAAG5Q,QAAQ,CAACC,KAAD,CAAvB,CAAA;;CAEA,MAAA,IAAI2Q,MAAM,IAAIvc,IAAI,CAACoP,KAAL,CAAWvP,OAAX,CAAmB0c,MAAnB,CAAA,KAA+B,CAAC,CAA9C,EAAiD;CAEhD,QAAI5I,IAAAA,SAAS,KAAK,QAAlB,EAA4B;CAC3B3T,UAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACA,SAAA;;CAED,QAAI7C,IAAAA,SAAS,KAAK,QAAd,IAA0B,CAAC3T,IAAI,CAACM,QAAL,CAAcqJ,UAA7C,EAAyD;CACxD,UAAA,OAAA;CACA,SAAA;CACD,OAAA;;CAED,MAAA,IAAI4S,MAAM,KAAK,IAAX,IAAmB,CAACvc,IAAI,CAACkB,OAAL,CAAaC,cAAb,CAA4Bob,MAA5B,CAAxB,EAA6D,OAAA;CAC7D,MAAI5I,IAAAA,SAAS,KAAK,QAAlB,EAA4B3T,IAAI,CAAC+Z,KAAL,CAAWD,MAAX,CAAA,CAAA;CAC5B,MAAInG,IAAAA,SAAS,KAAK,OAAd,IAAyB3T,IAAI,CAAC8gB,MAAL,EAA7B,EAA4C,OAAA;CAE5CvH,MAAAA,IAAI,GAAGvZ,IAAI,CAAC0T,OAAL,CAAa,MAAb,EAAqB1T,IAAI,CAACkB,OAAL,CAAaqb,MAAb,CAArB,CAAP,CAAA;;CAEA,MAAIvc,IAAAA,IAAI,CAACoR,OAAL,CAAa1M,QAAb,CAAsB6U,IAAtB,CAAJ,EAAiC;CAAE;CAClCA,QAAAA,IAAI,GAAGA,IAAI,CAACvS,SAAL,CAAe,IAAf,CAAP,CAAA;CACA,OAAA;;CAED6Z,MAAAA,OAAO,GAAG7gB,IAAI,CAAC8gB,MAAL,EAAV,CAAA;CACA9gB,MAAAA,IAAI,CAACoP,KAAL,CAAWxP,MAAX,CAAkBI,IAAI,CAAC4S,QAAvB,EAAiC,CAAjC,EAAoC2J,MAApC,CAAA,CAAA;CACAvc,MAAAA,IAAI,CAAC+gB,aAAL,CAAmBxH,IAAnB,CAAA,CAAA;;CAEA,MAAIvZ,IAAAA,IAAI,CAACsS,OAAT,EAAkB;CAEjB;CACA,QAAI,IAAA,CAACtS,IAAI,CAAC4gB,SAAN,IAAmB5gB,IAAI,CAACM,QAAL,CAAcoJ,YAArC,EAAmD;CAClD,UAAA,IAAIuF,MAAM,GAAGjP,IAAI,CAACyd,SAAL,CAAelB,MAAf,CAAb,CAAA;CACA,UAAIjE,IAAAA,IAAI,GAAGtY,IAAI,CAACuY,WAAL,CAAiBtJ,MAAjB,EAAyB,CAAzB,CAAX,CAAA;;CACA,UAAA,IAAIqJ,IAAJ,EAAU;CACTtY,YAAAA,IAAI,CAACwY,eAAL,CAAqBF,IAArB,CAAA,CAAA;CACA,WAAA;CACD,SATgB;CAYjB;;;CACA,QAAI,IAAA,CAACtY,IAAI,CAAC4gB,SAAN,IAAmB,CAAC5gB,IAAI,CAACM,QAAL,CAAcgZ,gBAAtC,EAAwD;CACvDtZ,UAAAA,IAAI,CAAC8Y,cAAL,CAAoB9Y,IAAI,CAACoS,SAAL,IAAkBuB,SAAS,KAAK,QAApD,CAAA,CAAA;CACA,SAfgB;;;CAkBjB,QAAA,IAAI3T,IAAI,CAACM,QAAL,CAAcgZ,gBAAd,IAAkC,KAAlC,IAA2CtZ,IAAI,CAAC8gB,MAAL,EAA/C,EAA8D;CAC7D9gB,UAAAA,IAAI,CAACwW,KAAL,EAAA,CAAA;CACA,SAFD,MAEO,IAAI,CAACxW,IAAI,CAAC4gB,SAAV,EAAqB;CAC3B5gB,UAAAA,IAAI,CAACgW,gBAAL,EAAA,CAAA;CACA,SAAA;;CAEDhW,QAAAA,IAAI,CAACF,OAAL,CAAa,UAAb,EAAyByc,MAAzB,EAAiChD,IAAjC,CAAA,CAAA;;CAEA,QAAA,IAAI,CAACvZ,IAAI,CAAC4gB,SAAV,EAAqB;CACpB5gB,UAAAA,IAAI,CAACsW,mBAAL,CAAyB;CAACwD,YAAAA,MAAM,EAAEA,MAAAA;CAAT,WAAzB,CAAA,CAAA;CACA,SAAA;CACD,OAAA;;CAED,MAAA,IAAI,CAAC9Z,IAAI,CAAC4gB,SAAN,IAAoB,CAACC,OAAD,IAAY7gB,IAAI,CAAC8gB,MAAL,EAApC,EAAoD;CACnD9gB,QAAAA,IAAI,CAAC8V,UAAL,EAAA,CAAA;CACA9V,QAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACA,OAAA;CAED,KAnEc,CAAf,CAAA;CAoEA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC+J,EAAAA,UAAU,CAAE7G,IAAwB,GAAC,IAA3B,EAAiCO,MAAjC,EAAkD;CAC3D,IAAM9Z,MAAAA,IAAI,GAAI,IAAd,CAAA;CACAuZ,IAAAA,IAAI,GAAKvZ,IAAI,CAACggB,OAAL,CAAazG,IAAb,CAAT,CAAA;CAEA,IAAI,IAAA,CAACA,IAAL,EAAY,OAAA;CAEZ,IAAIjU,IAAAA,CAAJ,EAAMsV,GAAN,CAAA;CACA,IAAA,MAAMhP,KAAK,GAAG2N,IAAI,CAAC9J,OAAL,CAAa7D,KAA3B,CAAA;CACAtG,IAAAA,CAAC,GAAGH,SAAS,CAACoU,IAAD,CAAb,CAAA;CAEAA,IAAAA,IAAI,CAACtV,MAAL,EAAA,CAAA;;CACA,IAAIsV,IAAAA,IAAI,CAACzV,SAAL,CAAeY,QAAf,CAAwB,QAAxB,CAAJ,EAAuC;CACtCkW,MAAAA,GAAG,GAAG5a,IAAI,CAAC8S,WAAL,CAAiBjT,OAAjB,CAAyB0Z,IAAzB,CAAN,CAAA;CACAvZ,MAAAA,IAAI,CAAC8S,WAAL,CAAiBlT,MAAjB,CAAwBgb,GAAxB,EAA6B,CAA7B,CAAA,CAAA;CACA5W,MAAAA,aAAa,CAACuV,IAAD,EAAM,QAAN,CAAb,CAAA;CACA,KAAA;;CAEDvZ,IAAAA,IAAI,CAACoP,KAAL,CAAWxP,MAAX,CAAkB0F,CAAlB,EAAqB,CAArB,CAAA,CAAA;CACAtF,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;;CACA,IAAA,IAAI,CAACpX,IAAI,CAACM,QAAL,CAAc2I,OAAf,IAA0BjJ,IAAI,CAAC+S,WAAL,CAAiB5R,cAAjB,CAAgCyK,KAAhC,CAA9B,EAAsE;CACrE5L,MAAAA,IAAI,CAACmgB,YAAL,CAAkBvU,KAAlB,EAAyBkO,MAAzB,CAAA,CAAA;CACA,KAAA;;CAED,IAAA,IAAIxU,CAAC,GAAGtF,IAAI,CAAC4S,QAAb,EAAuB;CACtB5S,MAAAA,IAAI,CAACoZ,QAAL,CAAcpZ,IAAI,CAAC4S,QAAL,GAAgB,CAA9B,CAAA,CAAA;CACA,KAAA;;CAED5S,IAAAA,IAAI,CAACsW,mBAAL,CAAyB;CAACwD,MAAAA,MAAM,EAAEA,MAAAA;CAAT,KAAzB,CAAA,CAAA;CACA9Z,IAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACArW,IAAAA,IAAI,CAACgW,gBAAL,EAAA,CAAA;CACAhW,IAAAA,IAAI,CAACF,OAAL,CAAa,aAAb,EAA4B8L,KAA5B,EAAmC2N,IAAnC,CAAA,CAAA;CAEA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;CACC1B,EAAAA,UAAU,CAAE/K,KAAiB,GAAC,IAApB,EAA0BlO,QAA0B,GAAG,MAAI,EAA3D,EAAuE;CAEhF;CACA,IAAA,IAAIa,SAAS,CAACC,MAAV,KAAqB,CAAzB,EAA4B;CAC3Bd,MAAAA,QAAQ,GAAGa,SAAS,CAAC,CAAD,CAApB,CAAA;CACA,KAAA;;CACD,IAAA,IAAI,OAAOb,QAAP,IAAmB,UAAvB,EAAmC;CAClCA,MAAAA,QAAQ,GAAG,MAAM,EAAjB,CAAA;CACA,KAAA;;CAED,IAAIoB,IAAAA,IAAI,GAAI,IAAZ,CAAA;CACA,IAAA,IAAIghB,KAAK,GAAGhhB,IAAI,CAAC4S,QAAjB,CAAA;CACA,IAAA,IAAIqO,MAAJ,CAAA;CACAnU,IAAAA,KAAK,GAAGA,KAAK,IAAI9M,IAAI,CAACwX,UAAL,EAAjB,CAAA;;CAEA,IAAA,IAAI,CAACxX,IAAI,CAAC+e,SAAL,CAAejS,KAAf,CAAL,EAA4B;CAC3BlO,MAAAA,QAAQ,EAAA,CAAA;CACR,MAAA,OAAO,KAAP,CAAA;CACA,KAAA;;CAEDoB,IAAAA,IAAI,CAACkhB,IAAL,EAAA,CAAA;CAEA,IAAIC,IAAAA,OAAO,GAAG,KAAd,CAAA;;CACA,IAAIhY,IAAAA,MAAM,GAAI1C,IAAD,IAA6B;CACzCzG,MAAAA,IAAI,CAACohB,MAAL,EAAA,CAAA;CAEA,MAAI,IAAA,CAAC3a,IAAD,IAAS,OAAOA,IAAP,KAAgB,QAA7B,EAAuC,OAAO7H,QAAQ,EAAf,CAAA;CACvC,MAAA,IAAIgN,KAAK,GAAGD,QAAQ,CAAClF,IAAI,CAACzG,IAAI,CAACM,QAAL,CAAc8J,UAAf,CAAL,CAApB,CAAA;;CACA,MAAA,IAAI,OAAOwB,KAAP,KAAiB,QAArB,EAA+B;CAC9B,QAAA,OAAOhN,QAAQ,EAAf,CAAA;CACA,OAAA;;CAEDoB,MAAAA,IAAI,CAAC2Z,eAAL,EAAA,CAAA;CACA3Z,MAAAA,IAAI,CAAC6P,SAAL,CAAepJ,IAAf,EAAoB,IAApB,CAAA,CAAA;CACAzG,MAAAA,IAAI,CAACoZ,QAAL,CAAc4H,KAAd,CAAA,CAAA;CACAhhB,MAAAA,IAAI,CAAC4X,OAAL,CAAahM,KAAb,CAAA,CAAA;CACAhN,MAAAA,QAAQ,CAAC6H,IAAD,CAAR,CAAA;CACA0a,MAAAA,OAAO,GAAG,IAAV,CAAA;CACA,KAfD,CAAA;;CAiBA,IAAI,IAAA,OAAOnhB,IAAI,CAACM,QAAL,CAAc6I,MAArB,KAAgC,UAApC,EAAgD;CAC/C8X,MAAAA,MAAM,GAAGjhB,IAAI,CAACM,QAAL,CAAc6I,MAAd,CAAqBtB,IAArB,CAA0B,IAA1B,EAAgCiF,KAAhC,EAAuC3D,MAAvC,CAAT,CAAA;CACA,KAFD,MAEK;CACJ8X,MAAAA,MAAM,GAAG;CACR,QAAA,CAACjhB,IAAI,CAACM,QAAL,CAAc+J,UAAf,GAA4ByC,KADpB;CAER,QAAA,CAAC9M,IAAI,CAACM,QAAL,CAAc8J,UAAf,GAA4B0C,KAAAA;CAFpB,OAAT,CAAA;CAIA,KAAA;;CAED,IAAI,IAAA,CAACqU,OAAL,EAAc;CACbhY,MAAAA,MAAM,CAAC8X,MAAD,CAAN,CAAA;CACA,KAAA;;CAED,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACC1K,EAAAA,YAAY,GAAG;CACd,IAAIvW,IAAAA,IAAI,GAAG,IAAX,CAAA;CACAA,IAAAA,IAAI,CAACoX,SAAL,GAAiB,IAAjB,CAAA;;CAEA,IAAIpX,IAAAA,IAAI,CAACsS,OAAT,EAAkB;CACjBtS,MAAAA,IAAI,CAACga,QAAL,CAAcha,IAAI,CAACoP,KAAnB,CAAA,CAAA;CACA,KAAA;;CAEDpP,IAAAA,IAAI,CAACsW,mBAAL,EAAA,CAAA;CACAtW,IAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCA,EAAAA,YAAY,GAAG;CACd,IAAMrW,MAAAA,IAAI,GAAO,IAAjB,CAAA;CAEAA,IAAAA,IAAI,CAACqhB,oBAAL,EAAA,CAAA;CAEA,IAAA,MAAMP,MAAM,GAAG9gB,IAAI,CAAC8gB,MAAL,EAAf,CAAA;CACA,IAAA,MAAM3O,QAAQ,GAAGnS,IAAI,CAACmS,QAAtB,CAAA;CAEAnS,IAAAA,IAAI,CAACyE,OAAL,CAAaX,SAAb,CAAuB4X,MAAvB,CAA8B,KAA9B,EAAoC1b,IAAI,CAAC0R,GAAzC,CAAA,CAAA;CAGA,IAAA,MAAM4P,cAAc,GAAGthB,IAAI,CAACyE,OAAL,CAAaX,SAApC,CAAA;CAEAwd,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,OAAtB,EAA+B1b,IAAI,CAACoS,SAApC,CAAA,CAAA;CACAkP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,UAAtB,EAAkC1b,IAAI,CAAC+R,UAAvC,CAAA,CAAA;CACAuP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,UAAtB,EAAkC1b,IAAI,CAACgS,UAAvC,CAAA,CAAA;CACAsP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,SAAtB,EAAiC,CAAC1b,IAAI,CAACkS,OAAvC,CAAA,CAAA;CACAoP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,QAAtB,EAAgCvJ,QAAhC,CAAA,CAAA;CACAmP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,MAAtB,EAA8BoF,MAA9B,CAAA,CAAA;CACAQ,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,cAAtB,EAAsC1b,IAAI,CAACoS,SAAL,IAAkB,CAACpS,IAAI,CAACqS,aAA9D,CAAA,CAAA;CACAiP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,iBAAtB,EAAyC1b,IAAI,CAAC8R,MAA9C,CAAA,CAAA;CACAwP,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,aAAtB,EAAqC1W,aAAa,CAAChF,IAAI,CAACkB,OAAN,CAAlD,CAAA,CAAA;CACAogB,IAAAA,cAAc,CAAC5F,MAAf,CAAsB,WAAtB,EAAmC1b,IAAI,CAACoP,KAAL,CAAW1P,MAAX,GAAoB,CAAvD,CAAA,CAAA;CAEA,GAAA;CAGD;CACD;CACA;CACA;CACA;CACA;CACA;CACA;;;CACC2hB,EAAAA,oBAAoB,GAAG;CACtB,IAAIrhB,IAAAA,IAAI,GAAG,IAAX,CAAA;;CAEA,IAAA,IAAI,CAACA,IAAI,CAAC8M,KAAL,CAAWyU,QAAhB,EAA0B;CACzB,MAAA,OAAA;CACA,KAAA;;CAEDvhB,IAAAA,IAAI,CAACkS,OAAL,GAAelS,IAAI,CAAC8M,KAAL,CAAWyU,QAAX,CAAoBC,KAAnC,CAAA;CACAxhB,IAAAA,IAAI,CAACiS,SAAL,GAAiB,CAACjS,IAAI,CAACkS,OAAvB,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;;;CACC4O,EAAAA,MAAM,GAAG;CACR,IAAA,OAAO,IAAKxgB,CAAAA,QAAL,CAAcmJ,QAAd,KAA2B,IAA3B,IAAmC,IAAK2F,CAAAA,KAAL,CAAW1P,MAAX,IAAqB,IAAKY,CAAAA,QAAL,CAAcmJ,QAA7E,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC6M,EAAAA,mBAAmB,CAAEmL,IAAiB,GAAG,EAAtB,EAA0B;CAC5C,IAAMzhB,MAAAA,IAAI,GAAG,IAAb,CAAA;CACA,IAAIiP,IAAAA,MAAJ,EAAYiF,KAAZ,CAAA;CAEA,IAAMwN,MAAAA,YAAY,GAAG1hB,IAAI,CAAC8M,KAAL,CAAWxK,aAAX,CAAyB,kBAAzB,CAArB,CAAA;;CAEA,IAAItC,IAAAA,IAAI,CAACyR,aAAT,EAAwB;CAEvB,MAAMtB,MAAAA,QAA4B,GAAI,EAAtC,CAAA;CACA,MAAMwR,MAAAA,YAAmB,GAAM3hB,IAAI,CAAC8M,KAAL,CAAWnF,gBAAX,CAA4B,gBAA5B,CAAA,CAA8CjI,MAA7E,CAAA;;CAEA,MAAA,SAASkiB,WAAT,CAAqBpE,SAArB,EAAuD5R,KAAvD,EAAqEsI,KAArE,EAAoG;CAEnG,QAAI,IAAA,CAACsJ,SAAL,EAAgB;CACfA,UAAAA,SAAS,GAAG9b,MAAM,CAAC,iBAAoBoK,GAAAA,WAAW,CAACF,KAAD,CAA/B,GAAyC,IAAzC,GAAgDE,WAAW,CAACoI,KAAD,CAA3D,GAAqE,WAAtE,CAAlB,CAAA;CACA,SAJkG;CAOnG;;;CACA,QAAIsJ,IAAAA,SAAS,IAAIkE,YAAjB,EAA+B;CAC9B1hB,UAAAA,IAAI,CAAC8M,KAAL,CAAWsB,MAAX,CAAkBoP,SAAlB,CAAA,CAAA;CACA,SAAA;;CAEDrN,QAAAA,QAAQ,CAAC7Q,IAAT,CAAcke,SAAd,EAZmG;CAenG;;CACA,QAAA,IAAIA,SAAS,IAAIkE,YAAb,IAA6BC,YAAY,GAAG,CAAhD,EAAmD;CAClDnE,UAAAA,SAAS,CAACrN,QAAV,GAAqB,IAArB,CAAA;CACA,SAAA;;CAED,QAAA,OAAOqN,SAAP,CAAA;CACA,OA1BsB;;;CA6BvBxd,MAAAA,IAAI,CAAC8M,KAAL,CAAWnF,gBAAX,CAA4B,gBAA5B,CAA8C7I,CAAAA,OAA9C,CAAuD0e,SAAD,IAAuB;CACxDA,QAAAA,SAApB,CAA+BrN,QAA/B,GAA0C,KAA1C,CAAA;CACA,OAFD,EA7BuB;;CAmCvB,MAAA,IAAInQ,IAAI,CAACoP,KAAL,CAAW1P,MAAX,IAAqB,CAArB,IAA0BM,IAAI,CAACM,QAAL,CAAcuK,IAAd,IAAsB,QAApD,EAA8D;CAE7D+W,QAAAA,WAAW,CAACF,YAAD,EAAe,EAAf,EAAmB,EAAnB,CAAX,CAF6D;CAK7D,OALD,MAKK;CAEJ1hB,QAAAA,IAAI,CAACoP,KAAL,CAAWtQ,OAAX,CAAoB8M,KAAD,IAAS;CAC3BqD,UAAAA,MAAM,GAAKjP,IAAI,CAACkB,OAAL,CAAa0K,KAAb,CAAX,CAAA;CACAsI,UAAAA,KAAK,GAAKjF,MAAM,CAACjP,IAAI,CAACM,QAAL,CAAc+J,UAAf,CAAN,IAAoC,EAA9C,CAAA;;CAEA,UAAI8F,IAAAA,QAAQ,CAACuN,QAAT,CAAkBzO,MAAM,CAACiB,OAAzB,CAAJ,EAAuC;CACtC,YAAA,MAAM2R,SAAS,GAAG7hB,IAAI,CAAC8M,KAAL,CAAWxK,aAAX,CAA0B,CAAA,cAAA,EAAgB6L,UAAU,CAACvC,KAAD,CAAQ,kBAA5D,CAAlB,CAAA;CACAgW,YAAAA,WAAW,CAACC,SAAD,EAAYjW,KAAZ,EAAmBsI,KAAnB,CAAX,CAAA;CACA,WAHD,MAGK;CACJjF,YAAAA,MAAM,CAACiB,OAAP,GAAiB0R,WAAW,CAAC3S,MAAM,CAACiB,OAAR,EAAiBtE,KAAjB,EAAwBsI,KAAxB,CAA5B,CAAA;CACA,WAAA;CACD,SAVD,CAAA,CAAA;CAYA,OAAA;CAED,KAxDD,MAwDO;CACNlU,MAAAA,IAAI,CAAC8M,KAAL,CAAWlB,KAAX,GAAmB5L,IAAI,CAAC6Z,QAAL,EAAnB,CAAA;CACA,KAAA;;CAED,IAAI7Z,IAAAA,IAAI,CAACsS,OAAT,EAAkB;CACjB,MAAA,IAAI,CAACmP,IAAI,CAAC3H,MAAV,EAAkB;CACjB9Z,QAAAA,IAAI,CAACF,OAAL,CAAa,QAAb,EAAuBE,IAAI,CAAC6Z,QAAL,EAAvB,CAAA,CAAA;CACA,OAAA;CACD,KAAA;CACD,GAAA;CAED;CACD;CACA;CACA;;;CACCxB,EAAAA,IAAI,GAAG;CACN,IAAIrY,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEA,IAAIA,IAAAA,IAAI,CAACmS,QAAL,IAAiBnS,IAAI,CAAC8R,MAAtB,IAAiC9R,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,OAAvB,IAAkC7K,IAAI,CAAC8gB,MAAL,EAAvE,EAAuF,OAAA;CACvF9gB,IAAAA,IAAI,CAAC8R,MAAL,GAAc,IAAd,CAAA;CACAtM,IAAAA,OAAO,CAACxF,IAAI,CAACsR,UAAN,EAAiB;CAAC,MAAiB,eAAA,EAAA,MAAA;CAAlB,KAAjB,CAAP,CAAA;CACAtR,IAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CACArT,IAAAA,QAAQ,CAAChD,IAAI,CAACmR,QAAN,EAAe;CAAC2Q,MAAAA,UAAU,EAAE,QAAb;CAAuBC,MAAAA,OAAO,EAAE,OAAA;CAAhC,KAAf,CAAR,CAAA;CACA/hB,IAAAA,IAAI,CAACgW,gBAAL,EAAA,CAAA;CACAhT,IAAAA,QAAQ,CAAChD,IAAI,CAACmR,QAAN,EAAe;CAAC2Q,MAAAA,UAAU,EAAE,SAAb;CAAwBC,MAAAA,OAAO,EAAE,OAAA;CAAjC,KAAf,CAAR,CAAA;CACA/hB,IAAAA,IAAI,CAACoU,KAAL,EAAA,CAAA;CACApU,IAAAA,IAAI,CAACF,OAAL,CAAa,eAAb,EAA8BE,IAAI,CAACmR,QAAnC,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACCqF,EAAAA,KAAK,CAACmD,eAAe,GAAC,IAAjB,EAAuB;CAC3B,IAAI3Z,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAIF,OAAO,GAAGE,IAAI,CAAC8R,MAAnB,CAAA;;CAEA,IAAA,IAAI6H,eAAJ,EAAqB;CAEpB;CACA3Z,MAAAA,IAAI,CAAC2Z,eAAL,EAAA,CAAA;;CAEA,MAAA,IAAI3Z,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,QAAvB,IAAmC7K,IAAI,CAACoP,KAAL,CAAW1P,MAAlD,EAA0D;CACzDM,QAAAA,IAAI,CAAC0a,SAAL,EAAA,CAAA;CACA,OAAA;CACD,KAAA;;CAED1a,IAAAA,IAAI,CAAC8R,MAAL,GAAc,KAAd,CAAA;CACAtM,IAAAA,OAAO,CAACxF,IAAI,CAACsR,UAAN,EAAiB;CAAC,MAAiB,eAAA,EAAA,OAAA;CAAlB,KAAjB,CAAP,CAAA;CACAtO,IAAAA,QAAQ,CAAChD,IAAI,CAACmR,QAAN,EAAe;CAAC4Q,MAAAA,OAAO,EAAE,MAAA;CAAV,KAAf,CAAR,CAAA;;CACA,IAAA,IAAI/hB,IAAI,CAACM,QAAL,CAAcoJ,YAAlB,EAAgC;CAC/B1J,MAAAA,IAAI,CAAC0Z,iBAAL,EAAA,CAAA;CACA,KAAA;;CACD1Z,IAAAA,IAAI,CAACqW,YAAL,EAAA,CAAA;CAEA,IAAIvW,IAAAA,OAAJ,EAAaE,IAAI,CAACF,OAAL,CAAa,gBAAb,EAA+BE,IAAI,CAACmR,QAApC,CAAA,CAAA;CACb,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC6E,EAAAA,gBAAgB,GAAE;CAEjB,IAAA,IAAI,KAAK1V,QAAL,CAAc8K,cAAd,KAAiC,MAArC,EAA6C;CAC5C,MAAA,OAAA;CACA,KAAA;;CAED,IAAI4W,IAAAA,OAAO,GAAK,IAAA,CAAK5Q,OAArB,CAAA;CACA,IAAA,IAAI6Q,IAAI,GAAKD,OAAO,CAAC1G,qBAAR,EAAb,CAAA;CACA,IAAA,IAAIC,GAAG,GAAMyG,OAAO,CAAC5G,YAAR,GAAuB6G,IAAI,CAAC1G,GAA5B,GAAmCpI,MAAM,CAAC+O,OAAvD,CAAA;CACA,IAAIC,IAAAA,IAAI,GAAKF,IAAI,CAACE,IAAL,GAAYhP,MAAM,CAACiP,OAAhC,CAAA;CAGApf,IAAAA,QAAQ,CAAC,IAAKmO,CAAAA,QAAN,EAAe;CACtBqD,MAAAA,KAAK,EAAGyN,IAAI,CAACzN,KAAL,GAAa,IADC;CAEtB+G,MAAAA,GAAG,EAAKA,GAAG,GAAG,IAFQ;CAGtB4G,MAAAA,IAAI,EAAIA,IAAI,GAAG,IAAA;CAHO,KAAf,CAAR,CAAA;CAMA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCpI,EAAAA,KAAK,CAACD,MAAD,EAAkB;CACtB,IAAI9Z,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEA,IAAA,IAAI,CAACA,IAAI,CAACoP,KAAL,CAAW1P,MAAhB,EAAwB,OAAA;CAExB,IAAA,IAAI0P,KAAK,GAAGpP,IAAI,CAACyb,eAAL,EAAZ,CAAA;CACAja,IAAAA,SAAO,CAAC4N,KAAD,EAAQmK,IAAD,IAAgB;CAC7BvZ,MAAAA,IAAI,CAACogB,UAAL,CAAgB7G,IAAhB,EAAqB,IAArB,CAAA,CAAA;CACA,KAFM,CAAP,CAAA;CAIAvZ,IAAAA,IAAI,CAACgZ,SAAL,EAAA,CAAA;CACA,IAAA,IAAI,CAACc,MAAL,EAAc9Z,IAAI,CAACsW,mBAAL,EAAA,CAAA;CACdtW,IAAAA,IAAI,CAACF,OAAL,CAAa,OAAb,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCihB,EAAAA,aAAa,CAACnd,EAAD,EAAiB;CAC7B,IAAM5D,MAAAA,IAAI,GAAI,IAAd,CAAA;CACA,IAAA,MAAMghB,KAAK,GAAIhhB,IAAI,CAAC4S,QAApB,CAAA;CACA,IAAA,MAAMrO,MAAM,GAAGvE,IAAI,CAACoR,OAApB,CAAA;CAEA7M,IAAAA,MAAM,CAACua,YAAP,CAAoBlb,EAApB,EAAwBW,MAAM,CAACgM,QAAP,CAAgByQ,KAAhB,CAAA,IAA0B,IAAlD,CAAA,CAAA;CACAhhB,IAAAA,IAAI,CAACoZ,QAAL,CAAc4H,KAAK,GAAG,CAAtB,CAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCnI,EAAAA,eAAe,CAAC/D,CAAD,EAA0B;CACxC,IAAA,IAAI/P,SAAJ,EAAesd,SAAf,EAA0BrB,KAA1B,EAAiCsB,IAAjC,CAAA;CACA,IAAItiB,IAAAA,IAAI,GAAG,IAAX,CAAA;CAEA+E,IAAAA,SAAS,GAAI+P,CAAC,IAAIA,CAAC,CAACmD,OAAF,KAAcE,aAApB,GAA+C,CAAC,CAAhD,GAAoD,CAAhE,CAAA;CACAkK,IAAAA,SAAS,GAAGxV,YAAY,CAAC7M,IAAI,CAACkR,aAAN,CAAxB,CALwC;;CASxC,IAAMqR,MAAAA,QAAkB,GAAG,EAA3B,CAAA;;CAEA,IAAA,IAAIviB,IAAI,CAAC8S,WAAL,CAAiBpT,MAArB,EAA6B;CAE5B4iB,MAAAA,IAAI,GAAGzd,OAAO,CAAC7E,IAAI,CAAC8S,WAAN,EAAmB/N,SAAnB,CAAd,CAAA;CACAic,MAAAA,KAAK,GAAG7b,SAAS,CAACmd,IAAD,CAAjB,CAAA;;CAEA,MAAIvd,IAAAA,SAAS,GAAG,CAAhB,EAAmB;CAAEic,QAAAA,KAAK,EAAA,CAAA;CAAK,OAAA;;CAE/Bxf,MAAAA,SAAO,CAACxB,IAAI,CAAC8S,WAAN,EAAoByG,IAAD,IAAkBgJ,QAAQ,CAACjjB,IAAT,CAAcia,IAAd,CAArC,CAAP,CAAA;CAEA,KATD,MASO,IAAI,CAACvZ,IAAI,CAACoS,SAAL,IAAkBpS,IAAI,CAACM,QAAL,CAAcuK,IAAd,KAAuB,QAA1C,KAAuD7K,IAAI,CAACoP,KAAL,CAAW1P,MAAtE,EAA8E;CACpF,MAAA,MAAM0P,KAAK,GAAGpP,IAAI,CAACyb,eAAL,EAAd,CAAA;CACA,MAAA,IAAI+G,OAAJ,CAAA;;CACA,MAAA,IAAIzd,SAAS,GAAG,CAAZ,IAAiBsd,SAAS,CAACtV,KAAV,KAAoB,CAArC,IAA0CsV,SAAS,CAAC3iB,MAAV,KAAqB,CAAnE,EAAsE;CACrE8iB,QAAAA,OAAO,GAAGpT,KAAK,CAACpP,IAAI,CAAC4S,QAAL,GAAgB,CAAjB,CAAf,CAAA;CAEA,OAHD,MAGM,IAAI7N,SAAS,GAAG,CAAZ,IAAiBsd,SAAS,CAACtV,KAAV,KAAoB/M,IAAI,CAACwX,UAAL,EAAA,CAAkB9X,MAA3D,EAAmE;CACxE8iB,QAAAA,OAAO,GAAGpT,KAAK,CAACpP,IAAI,CAAC4S,QAAN,CAAf,CAAA;CACA,OAAA;;CAED,MAAI4P,IAAAA,OAAO,KAAK7iB,SAAhB,EAA2B;CAC1B4iB,QAAAA,QAAQ,CAACjjB,IAAT,CAAekjB,OAAf,CAAA,CAAA;CACA,OAAA;CACD,KAAA;;CAED,IAAI,IAAA,CAACxiB,IAAI,CAACyiB,YAAL,CAAkBF,QAAlB,EAA2BzN,CAA3B,CAAL,EAAoC;CACnC,MAAA,OAAO,KAAP,CAAA;CACA,KAAA;;CAED5H,IAAAA,cAAc,CAAC4H,CAAD,EAAG,IAAH,CAAd,CAvCwC;;CA0CxC,IAAA,IAAI,OAAOkM,KAAP,KAAiB,WAArB,EAAkC;CACjChhB,MAAAA,IAAI,CAACoZ,QAAL,CAAc4H,KAAd,CAAA,CAAA;CACA,KAAA;;CAED,IAAOuB,OAAAA,QAAQ,CAAC7iB,MAAhB,EAAwB;CACvBM,MAAAA,IAAI,CAACogB,UAAL,CAAgBmC,QAAQ,CAACG,GAAT,EAAhB,CAAA,CAAA;CACA,KAAA;;CAED1iB,IAAAA,IAAI,CAACgZ,SAAL,EAAA,CAAA;CACAhZ,IAAAA,IAAI,CAACgW,gBAAL,EAAA,CAAA;CACAhW,IAAAA,IAAI,CAAC8Y,cAAL,CAAoB,KAApB,CAAA,CAAA;CAEA,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACC2J,EAAAA,YAAY,CAACrT,KAAD,EAAiBjC,GAAjB,EAA8C;CAEzD,IAAA,MAAMyD,MAAM,GAAGxB,KAAK,CAACzL,GAAN,CAAU4V,IAAI,IAAIA,IAAI,CAAC9J,OAAL,CAAa7D,KAA/B,CAAf,CAFyD;;CAKzD,IAAI,IAAA,CAACgF,MAAM,CAAClR,MAAR,IAAmB,OAAO,IAAA,CAAKY,QAAL,CAAcqiB,QAArB,KAAkC,UAAlC,IAAgD,IAAA,CAAKriB,QAAL,CAAcqiB,QAAd,CAAuB/R,MAAvB,EAA8BzD,GAA9B,CAAuC,KAAA,KAA9G,EAAsH;CACrH,MAAA,OAAO,KAAP,CAAA;CACA,KAAA;;CAED,IAAA,OAAO,IAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;CACA;CACA;;;CACCyL,EAAAA,gBAAgB,CAAC7T,SAAD,EAAmB+P,CAAnB,EAAgD;CAC/D,IAAA,IAAI6F,WAAJ;CAAA,QAAiBiI,QAAjB;CAAA,QAA2B5iB,IAAI,GAAG,IAAlC,CAAA;CAEA,IAAA,IAAIA,IAAI,CAAC0R,GAAT,EAAc3M,SAAS,IAAI,CAAC,CAAd,CAAA;CACd,IAAA,IAAI/E,IAAI,CAACwX,UAAL,GAAkB9X,MAAtB,EAA+B,OAJgC;;CAQ/D,IAAA,IAAI8N,SAAS,CAAC2K,YAAD,EAAwBrD,CAAxB,CAAT,IAAuCtH,SAAS,CAAC,UAAD,EAAYsH,CAAZ,CAApD,EAAoE;CAEnE6F,MAAAA,WAAW,GAAK3a,IAAI,CAACua,aAAL,CAAmBxV,SAAnB,CAAhB,CAAA;;CACA,MAAA,IAAI4V,WAAJ,EAAiB;CAEhB,QAAI,IAAA,CAACA,WAAW,CAAC7W,SAAZ,CAAsBY,QAAtB,CAA+B,QAA/B,CAAL,EAA+C;CAC9Cke,UAAAA,QAAQ,GAAKjI,WAAb,CAAA;CACA,SAFD,MAEK;CACJiI,UAAAA,QAAQ,GAAK5iB,IAAI,CAACuY,WAAL,CAAiBoC,WAAjB,EAA6B5V,SAA7B,EAAuC,MAAvC,CAAb,CAAA;CACA,SANe;;CAShB,OATD,MASM,IAAIA,SAAS,GAAG,CAAhB,EAAmB;CACxB6d,QAAAA,QAAQ,GAAK5iB,IAAI,CAACkR,aAAL,CAAmB2R,kBAAhC,CAAA;CACA,OAFK,MAED;CACJD,QAAAA,QAAQ,GAAK5iB,IAAI,CAACkR,aAAL,CAAmB3L,sBAAhC,CAAA;CACA,OAAA;;CAGD,MAAA,IAAIqd,QAAJ,EAAc;CACb,QAAIA,IAAAA,QAAQ,CAAC9e,SAAT,CAAmBY,QAAnB,CAA4B,QAA5B,CAAJ,EAA2C;CAC1C1E,UAAAA,IAAI,CAACya,gBAAL,CAAsBE,WAAtB,CAAA,CAAA;CACA,SAAA;;CACD3a,QAAAA,IAAI,CAACwa,kBAAL,CAAwBoI,QAAxB,EAJa;CAKb,OAxBkE;;CA2BnE,KA3BD,MA2BK;CACJ5iB,MAAAA,IAAI,CAAC8iB,SAAL,CAAe/d,SAAf,CAAA,CAAA;CACA,KAAA;CACD,GAAA;;CAED+d,EAAAA,SAAS,CAAC/d,SAAD,EAAkB,EAAE;CAE7B;CACD;CACA;CACA;;;CACCwV,EAAAA,aAAa,CAACxV,SAAD,EAAmB;CAE/B,IAAI4V,IAAAA,WAAW,GAAG,IAAKvJ,CAAAA,OAAL,CAAa9O,aAAb,CAA2B,cAA3B,CAAlB,CAAA;;CACA,IAAA,IAAIqY,WAAJ,EAAiB;CAChB,MAAA,OAAOA,WAAP,CAAA;CACA,KAAA;;CAGD,IAAIyB,IAAAA,MAAM,GAAG,IAAKhL,CAAAA,OAAL,CAAazJ,gBAAb,CAA8B,SAA9B,CAAb,CAAA;;CACA,IAAA,IAAIyU,MAAJ,EAAY;CACX,MAAA,OAAOvX,OAAO,CAACuX,MAAD,EAAQrX,SAAR,CAAd,CAAA;CACA,KAAA;CACD,GAAA;CAGD;CACD;CACA;CACA;CACA;CACA;CACA;CACA;;;CACCqU,EAAAA,QAAQ,CAAC2J,OAAD,EAAiB;CACxB,IAAA,IAAA,CAAKnQ,QAAL,GAAgB,IAAKxD,CAAAA,KAAL,CAAW1P,MAA3B,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACC+b,EAAAA,eAAe,GAAY;CAC1B,IAAO1a,OAAAA,KAAK,CAACwG,IAAN,CAAY,IAAA,CAAK6J,OAAL,CAAazJ,gBAAb,CAA8B,gBAA9B,CAAZ,CAAP,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCuZ,EAAAA,IAAI,GAAG;CACN,IAAK/O,IAAAA,CAAAA,QAAL,GAAgB,IAAhB,CAAA;CACA,IAAA,IAAA,CAAKkE,YAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;;;CACC+K,EAAAA,MAAM,GAAG;CACR,IAAKjP,IAAAA,CAAAA,QAAL,GAAgB,KAAhB,CAAA;CACA,IAAA,IAAA,CAAKkE,YAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCI,EAAAA,OAAO,GAAG;CACT,IAAIzW,IAAAA,IAAI,GAAG,IAAX,CAAA;CACAA,IAAAA,IAAI,CAAC8M,KAAL,CAAWmD,QAAX,GAAyB,IAAzB,CAAA;CACAjQ,IAAAA,IAAI,CAACkR,aAAL,CAAmBjB,QAAnB,GAA+B,IAA/B,CAAA;CACAjQ,IAAAA,IAAI,CAACsR,UAAL,CAAgBE,QAAhB,GAA4B,CAAC,CAA7B,CAAA;CACAxR,IAAAA,IAAI,CAAC+R,UAAL,GAAsB,IAAtB,CAAA;CACA,IAAA,IAAA,CAAKyE,KAAL,EAAA,CAAA;CACAxW,IAAAA,IAAI,CAACkhB,IAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;;;CACCxK,EAAAA,MAAM,GAAG;CACR,IAAI1W,IAAAA,IAAI,GAAG,IAAX,CAAA;CACAA,IAAAA,IAAI,CAAC8M,KAAL,CAAWmD,QAAX,GAAyB,KAAzB,CAAA;CACAjQ,IAAAA,IAAI,CAACkR,aAAL,CAAmBjB,QAAnB,GAA+B,KAA/B,CAAA;CACAjQ,IAAAA,IAAI,CAACsR,UAAL,CAAgBE,QAAhB,GAA4BxR,IAAI,CAACwR,QAAjC,CAAA;CACAxR,IAAAA,IAAI,CAAC+R,UAAL,GAAsB,KAAtB,CAAA;CACA/R,IAAAA,IAAI,CAACohB,MAAL,EAAA,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC4B,EAAAA,OAAO,GAAG;CACT,IAAIhjB,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAIkW,cAAc,GAAGlW,IAAI,CAACkW,cAA1B,CAAA;CAEAlW,IAAAA,IAAI,CAACF,OAAL,CAAa,SAAb,CAAA,CAAA;CACAE,IAAAA,IAAI,CAACT,GAAL,EAAA,CAAA;CACAS,IAAAA,IAAI,CAACyE,OAAL,CAAaR,MAAb,EAAA,CAAA;CACAjE,IAAAA,IAAI,CAACmR,QAAL,CAAclN,MAAd,EAAA,CAAA;CAEAjE,IAAAA,IAAI,CAAC8M,KAAL,CAAW5K,SAAX,GAAuBgU,cAAc,CAAChU,SAAtC,CAAA;CACAlC,IAAAA,IAAI,CAAC8M,KAAL,CAAW0E,QAAX,GAAsB0E,cAAc,CAAC1E,QAArC,CAAA;CAEAxN,IAAAA,aAAa,CAAChE,IAAI,CAAC8M,KAAN,EAAY,aAAZ,EAA0B,sBAA1B,CAAb,CAAA;;CAEA9M,IAAAA,IAAI,CAAC4R,QAAL,EAAA,CAAA;;CAEA,IAAA,OAAO5R,IAAI,CAAC8M,KAAL,CAAWmG,SAAlB,CAAA;CACA,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACCvH,EAAAA,MAAM,CAAEuX,YAAF,EAAiCxc,IAAjC,EAA6D;CAClE,IAAIuH,IAAAA,EAAJ,EAAQ2O,IAAR,CAAA;CACA,IAAM3c,MAAAA,IAAI,GAAG,IAAb,CAAA;;CAEA,IAAI,IAAA,OAAO,IAAKM,CAAAA,QAAL,CAAcoL,MAAd,CAAqBuX,YAArB,CAAP,KAA8C,UAAlD,EAA8D;CAC7D,MAAA,OAAO,IAAP,CAAA;CACA,KANiE;;;CASlEtG,IAAAA,IAAI,GAAG3c,IAAI,CAACM,QAAL,CAAcoL,MAAd,CAAqBuX,YAArB,CAAA,CAAmCpb,IAAnC,CAAwC,IAAxC,EAA8CpB,IAA9C,EAAoDqF,WAApD,CAAP,CAAA;;CAEA,IAAI,IAAA,CAAC6Q,IAAL,EAAW;CACV,MAAA,OAAO,IAAP,CAAA;CACA,KAAA;;CAEDA,IAAAA,IAAI,GAAGjb,MAAM,CAAEib,IAAF,CAAb,CAfkE;;CAkBlE,IAAA,IAAIsG,YAAY,KAAK,QAAjB,IAA6BA,YAAY,KAAK,eAAlD,EAAmE;CAElE,MAAIxc,IAAAA,IAAI,CAACzG,IAAI,CAACM,QAAL,CAAcgK,aAAf,CAAR,EAAuC;CACtC9E,QAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,UAAgB,eAAA,EAAA,MAAA;CAAjB,SAAN,CAAP,CAAA;CACA,OAFD,MAEK;CACJnX,QAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,UAAmB,iBAAA,EAAA,EAAA;CAApB,SAAN,CAAP,CAAA;CACA,OAAA;CAED,KARD,MAQM,IAAIsG,YAAY,KAAK,UAArB,EAAiC;CACtCjV,MAAAA,EAAE,GAAGvH,IAAI,CAACqJ,KAAL,CAAW9P,IAAI,CAACM,QAAL,CAAckK,kBAAzB,CAAL,CAAA;CACAhF,MAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,QAAc3O,YAAAA,EAAAA,EAAAA;CAAf,OAAN,CAAP,CAAA;;CACA,MAAGvH,IAAAA,IAAI,CAACqJ,KAAL,CAAW9P,IAAI,CAACM,QAAL,CAAcgK,aAAzB,CAAH,EAA4C;CAC3C9E,QAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,UAAiB,eAAA,EAAA,EAAA;CAAlB,SAAN,CAAP,CAAA;CACA,OAAA;CACD,KAAA;;CAED,IAAA,IAAIsG,YAAY,KAAK,QAAjB,IAA6BA,YAAY,KAAK,MAAlD,EAA0D;CACzD,MAAA,MAAMrX,KAAK,GAAGC,QAAQ,CAACpF,IAAI,CAACzG,IAAI,CAACM,QAAL,CAAc8J,UAAf,CAAL,CAAtB,CAAA;CACA5E,MAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,QAAc/Q,YAAAA,EAAAA,KAAAA;CAAf,OAAN,CAAP,CAFyD;;CAMzD,MAAIqX,IAAAA,YAAY,KAAK,MAArB,EAA6B;CAC5B5f,QAAAA,UAAU,CAACsZ,IAAD,EAAM3c,IAAI,CAACM,QAAL,CAAc4K,SAApB,CAAV,CAAA;CACA1F,QAAAA,OAAO,CAACmX,IAAD,EAAM;CAAC,UAAe,cAAA,EAAA,EAAA;CAAhB,SAAN,CAAP,CAAA;CACA,OAHD,MAGK;CACJtZ,QAAAA,UAAU,CAACsZ,IAAD,EAAM3c,IAAI,CAACM,QAAL,CAAc6K,WAApB,CAAV,CAAA;CACA3F,QAAAA,OAAO,CAACmX,IAAD,EAAM;CACZ3I,UAAAA,IAAI,EAAC,QADO;CAEZhG,UAAAA,EAAE,EAACvH,IAAI,CAACoX,GAAAA;CAFI,SAAN,CAAP,CAFI;;CAQJpX,QAAAA,IAAI,CAAC+Z,IAAL,GAAY7D,IAAZ,CAAA;CACA3c,QAAAA,IAAI,CAACkB,OAAL,CAAa0K,KAAb,IAAsBnF,IAAtB,CAAA;CACA,OAAA;CAGD,KAAA;;CAED,IAAA,OAAOkW,IAAP,CAAA;CAEA,GAAA;CAGD;CACD;CACA;CACA;;;CACCjJ,EAAAA,OAAO,CAAEuP,YAAF,EAAiCxc,IAAjC,EAAwD;CAC9D,IAAMkW,MAAAA,IAAI,GAAG,IAAKjR,CAAAA,MAAL,CAAYuX,YAAZ,EAA0Bxc,IAA1B,CAAb,CAAA;;CAEA,IAAIkW,IAAAA,IAAI,IAAI,IAAZ,EAAkB;CACjB,MAAA,MAAM,sBAAN,CAAA;CACA,KAAA;;CACD,IAAA,OAAOA,IAAP,CAAA;CACA,GAAA;CAGD;CACD;CACA;CACA;CACA;CACA;;;CACC6C,EAAAA,UAAU,GAAO;CAEhBhe,IAAAA,SAAO,CAAC,IAAA,CAAKN,OAAN,EAAgB+N,MAAD,IAAoB;CACzC,MAAIA,IAAAA,MAAM,CAACuR,IAAX,EAAiB;CAChBvR,QAAAA,MAAM,CAACuR,IAAP,CAAYvc,MAAZ,EAAA,CAAA;CACA,QAAOgL,OAAAA,MAAM,CAACuR,IAAd,CAAA;CACA,OAAA;CACD,KALM,CAAP,CAAA;CAOA,GAAA;CAED;CACD;CACA;CACA;;;CACCP,EAAAA,YAAY,CAACrU,KAAD,EAAc;CAEzB,IAAA,MAAM4R,SAAS,GAAK,IAAA,CAAKC,SAAL,CAAe7R,KAAf,CAApB,CAAA;CACA,IAAA,IAAI4R,SAAJ,EAAgBA,SAAS,CAACvZ,MAAV,EAAA,CAAA;CAEhB,GAAA;CAED;CACD;CACA;CACA;CACA;;;CACC8a,EAAAA,SAAS,CAAEjS,KAAF,EAAyB;CACjC,IAAO,OAAA,IAAA,CAAKxM,QAAL,CAAc6I,MAAd,IAAyB2D,KAAK,CAACpN,MAAN,GAAe,CAAxC,IAA+C,IAAKY,CAAAA,QAAL,CAAc+I,YAAf,CAAiDxB,IAAjD,CAAsD,IAAtD,EAA4DiF,KAA5D,CAArD,CAAA;CACA,GAAA;CAGD;CACD;CACA;CACA;CACA;CACA;CACA;;;CACCoW,EAAAA,IAAI,CAAEC,IAAF,EAAeC,MAAf,EAA8BC,MAA9B,EAA0C;CAC7C,IAAIrjB,IAAAA,IAAI,GAAG,IAAX,CAAA;CACA,IAAA,IAAIsjB,WAAW,GAAGtjB,IAAI,CAACojB,MAAD,CAAtB,CAAA;;CAGApjB,IAAAA,IAAI,CAACojB,MAAD,CAAJ,GAAe,YAAU;CACxB,MAAIhH,IAAAA,MAAJ,EAAYmH,UAAZ,CAAA;;CAEA,MAAIJ,IAAAA,IAAI,KAAK,OAAb,EAAsB;CACrB/G,QAAAA,MAAM,GAAGkH,WAAW,CAACrjB,KAAZ,CAAkBD,IAAlB,EAAwBP,SAAxB,CAAT,CAAA;CACA,OAAA;;CAED8jB,MAAAA,UAAU,GAAGF,MAAM,CAACpjB,KAAP,CAAaD,IAAb,EAAmBP,SAAnB,CAAb,CAAA;;CAEA,MAAI0jB,IAAAA,IAAI,KAAK,SAAb,EAAwB;CACvB,QAAA,OAAOI,UAAP,CAAA;CACA,OAAA;;CAED,MAAIJ,IAAAA,IAAI,KAAK,QAAb,EAAuB;CACtB/G,QAAAA,MAAM,GAAGkH,WAAW,CAACrjB,KAAZ,CAAkBD,IAAlB,EAAwBP,SAAxB,CAAT,CAAA;CACA,OAAA;;CAED,MAAA,OAAO2c,MAAP,CAAA;CACA,KAlBD,CAAA;CAoBA,GAAA;;CA3oF4D;;;;;;;;;;;"}