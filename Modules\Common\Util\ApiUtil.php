<?php

namespace Modules\Common\Util;

use Bingo\Core\Assets\AssetsUtil;
use Bingo\Core\Input\Request;
use Bingo\Core\Util\EnvUtil;

class ApiUtil
{
    public static function config(): array
    {
        $config = bingostart_config();

        $data = [];

        // 基础
        $data['siteBase'] = Request::domainUrl();
        $data['siteLogo'] = AssetsUtil::fixFull($config->get('siteLogo'));
        $data['siteName'] = $config->get('siteName');
        $data['siteSlogan'] = $config->get('siteSlogan');
        $data['siteDomain'] = $config->get('siteDomain');
        $data['siteKeywords'] = $config->get('siteKeywords');
        $data['siteDescription'] = $config->get('siteDescription');
        $data['siteFavIco'] = AssetsUtil::fixFull($config->get('siteFavIco'));
        $data['siteBeian'] = $config->get('siteBeian');
        $data['siteCDN'] = AssetsUtil::fixFull(AssetsUtil::cdn(), false);

        $data['modules'] = ModulesManager::listAllEnableModulesNames();

        // 支付
        /** @deprecated delete after 2023-09-27 */
        $data['payAlipayOn'] = $config->getBoolean('payAlipayOn');
        $data['payAlipayWebOn'] = $config->getBoolean('payAlipayWebOn');
        $data['payWechatOn'] = $config->getBoolean('payWechatOn');
        $data['payMemberMoneyOn'] = $config->getBoolean('payMemberMoneyOn');

        // 上传
        $data['dataUpload'] = [];
        $data['dataUpload'] = [
            'chunkSize' => EnvUtil::env('uploadMaxSize'),
            'category' => [],
        ];
        $uploads = config('data.upload');
        foreach ($uploads as $category => $categoryInfo) {
            $info = [
                'maxSize' => $categoryInfo['maxSize'],
                'extensions' => $categoryInfo['extensions'],
            ];
            if ('image' == $category) {
                $info['compress'] = $categoryInfo['compress'];
                $info['compressMaxWidthOrHeight'] = $categoryInfo['compressMaxWidthOrHeight'];
                $info['compressMaxSize'] = $categoryInfo['compressMaxSize'];
            }
            $data['dataUpload']['category'][$category] = $info;
        }

        return $data;
    }
}
