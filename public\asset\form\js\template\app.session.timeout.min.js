(function($){"use strict";$.sessionTimeout=function(options){var defaults={title:"Your Session is About to Expire!",message:"Your session is about to expire.",actionMessage:'Please click "Stay Connected" to keep working or click "Logout" to end your session now.',logoutButton:"Logout",keepAliveButton:"Stay Connected",keepAliveUrl:"/keep-alive",ajaxType:"POST",ajaxData:"",redirUrl:"/timed-out",logoutUrl:"/log-out",warnAfter:9e5,redirAfter:12e5,keepAliveInterval:5e3,keepAlive:true,ignoreUserActivity:false,onStart:false,onWarn:false,onRedir:false,countdownMessage:false,countdownBar:false,countdownSmart:false};var opt=defaults,timer,countdown={};if(options){opt=$.extend(defaults,options)}if(opt.warnAfter>=opt.redirAfter){console.error('Bootstrap-session-timeout plugin is miss-configured. Option "redirAfter" must be equal or greater than "warnAfter".');return false}var logout=function(){var _csrfToken={};var _csrfParam=$('meta[name="csrf-param"]').attr("content");_csrfToken[_csrfParam]=$('meta[name="csrf-token"]').attr("content");$.ajax({method:"POST",url:opt.logoutUrl,dataType:"json",data:_csrfToken}).always(function(){location.reload()})};if(typeof opt.onWarn!=="function"){var actionMessage=opt.actionMessage.length>0?opt.actionMessage.replace(/{keepAliveButton}/g,opt.keepAliveButton).replace(/{logoutButton}/g,opt.logoutButton):"";var countdownMessage=opt.countdownMessage?"<p>"+opt.countdownMessage.replace(/{timer}/g,'<span class="countdown-holder"></span>')+"</p>":"";var countdownBarHtml=opt.countdownBar?'<div class="progress">                   <div class="progress-bar progress-bar-striped countdown-bar active" role="progressbar" style="min-width: 15px; width: 100%;">                     <span class="countdown-holder"></span>                   </div>                 </div>':"";$("body").append('<div class="modal fade" id="session-timeout-dialog">               <div class="modal-dialog modal-sm">                 <div class="modal-content">                   <div class="modal-header">                     <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>                     <h4 class="session-timeout-title modal-title">'+opt.title+'</h4>                   </div>                   <div class="modal-body">                     <p class="session-timeout-message">                        <span class="session-time-out-icon glyphicon glyphicon-alarm"></span>                    '+opt.message+'</p>                     <h1 class="session-time-out-countdown-message">'+countdownMessage+"</h1>                     "+countdownBarHtml+'                     <p class="session-timeout-info">'+actionMessage+'</p>                   </div>                   <div class="modal-footer">                     <button id="session-timeout-dialog-logout" type="button" class="btn btn-default">'+opt.logoutButton+'</button>                     <button id="session-timeout-dialog-keepalive" type="button" class="btn btn-primary" data-dismiss="modal">'+opt.keepAliveButton+"</button>                   </div>                 </div>               </div>              </div>");$("#session-timeout-dialog-logout").click(function(e){e.preventDefault();logout()});$("#session-timeout-dialog").on("hide.bs.modal",function(){startSessionTimer()})}if(!opt.ignoreUserActivity){var mousePosition=[-1,-1];$(document).on("keyup mouseup mousemove touchend touchmove",function(e){if(e.type==="mousemove"){if(e.clientX===mousePosition[0]&&e.clientY===mousePosition[1]){return}mousePosition[0]=e.clientX;mousePosition[1]=e.clientY;if($("#session-timeout-dialog").length>0&&$("#session-timeout-dialog").data("bs.modal")&&$("#session-timeout-dialog").data("bs.modal").isShown){return}}startSessionTimer();if($("#session-timeout-dialog").length>0&&$("#session-timeout-dialog").data("bs.modal")&&$("#session-timeout-dialog").data("bs.modal").isShown){$("#session-timeout-dialog").modal("hide");$("body").removeClass("modal-open");$("div.modal-backdrop").remove()}})}var keepAlivePinged=false;function keepAlive(){if(!keepAlivePinged){$.ajax({type:opt.ajaxType,url:opt.keepAliveUrl,data:opt.ajaxData});keepAlivePinged=true;setTimeout(function(){keepAlivePinged=false},opt.keepAliveInterval)}}function startSessionTimer(){clearTimeout(timer);if(opt.countdownMessage||opt.countdownBar){startCountdownTimer("session",true)}if(typeof opt.onStart==="function"){opt.onStart(opt)}if(opt.keepAlive){keepAlive()}timer=setTimeout(function(){if(typeof opt.onWarn!=="function"){$("#session-timeout-dialog").modal("show")}else{opt.onWarn(opt)}startDialogTimer()},opt.warnAfter)}function startDialogTimer(){clearTimeout(timer);if(!$("#session-timeout-dialog").hasClass("in")&&(opt.countdownMessage||opt.countdownBar)){startCountdownTimer("dialog",true)}timer=setTimeout(function(){if(typeof opt.onRedir!=="function"){logout()}else{opt.onRedir(opt)}},opt.redirAfter-opt.warnAfter)}function startCountdownTimer(type,reset){clearTimeout(countdown.timer);if(type==="dialog"&&reset){countdown.timeLeft=Math.floor((opt.redirAfter-opt.warnAfter)/1e3)}else if(type==="session"&&reset){countdown.timeLeft=Math.floor(opt.redirAfter/1e3)}if(opt.countdownBar&&type==="dialog"){countdown.percentLeft=Math.floor(countdown.timeLeft/((opt.redirAfter-opt.warnAfter)/1e3)*100)}else if(opt.countdownBar&&type==="session"){countdown.percentLeft=Math.floor(countdown.timeLeft/(opt.redirAfter/1e3)*100)}var countdownEl=$(".countdown-holder");var secondsLeft=countdown.timeLeft>=0?countdown.timeLeft:0;if(opt.countdownSmart){var minLeft=Math.floor(secondsLeft/60);var secRemain=secondsLeft%60;var countTxt=minLeft>0?minLeft+"m":"";if(countTxt.length>0){countTxt+=" "}countTxt+=secRemain+"s";countdownEl.text(countTxt)}else{countdownEl.text(secondsLeft+"s")}if(opt.countdownBar){$(".countdown-bar").css("width",countdown.percentLeft+"%")}countdown.timeLeft=countdown.timeLeft-1;countdown.timer=setTimeout(function(){startCountdownTimer(type)},1e3)}startSessionTimer()}})(jQuery);
