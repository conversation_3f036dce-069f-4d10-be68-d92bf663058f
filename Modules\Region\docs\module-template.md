# 新模块开发模板

## 🚀 快速创建新模块

### 1. 创建模块目录结构

```bash
# 创建模块目录
mkdir -p Modules/YourModule/{Api/Controllers,Services,Models,Enums,Middleware,Lang/{en,zh_CN,zh_HK},database/migrations,config,docs}

# 创建核心文件
touch Modules/YourModule/Api/route.php
touch Modules/YourModule/Api/Controllers/YourModuleController.php
touch Modules/YourModule/Services/YourModuleService.php
touch Modules/YourModule/Models/YourModule.php
touch Modules/YourModule/Enums/YourModuleErrorCode.php
touch Modules/YourModule/Middleware/LanguageMiddleware.php
touch Modules/YourModule/Lang/{en,zh_CN,zh_HK}/yourmodule.php
touch Modules/YourModule/composer.json
touch Modules/YourModule/config/config.json
```

### 2. 模块配置文件模板

```json
// Modules/YourModule/config/config.json
{
    "name": "YourModule",
    "alias": "yourmodule",
    "description": "Your module description",
    "version": "1.0.0",
    "author": "Your Name",
    "providers": [
        "Modules\\YourModule\\Providers\\YourModuleServiceProvider"
    ],
    "files": [],
    "requires": []
}
```

```json
// Modules/YourModule/composer.json
{
    "name": "yourmodule/yourmodule",
    "description": "Your module for Laravel",
    "type": "laravel-module",
    "license": "MIT",
    "authors": [
        {
            "name": "Your Name",
            "email": "<EMAIL>"
        }
    ],
    "require": {},
    "autoload": {
        "psr-4": {
            "Modules\\YourModule\\": ""
        }
    },
    "extra": {
        "laravel": {
            "providers": [
                "Modules\\YourModule\\Providers\\YourModuleServiceProvider"
            ]
        }
    }
}
```

### 3. 错误码枚举模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Enums;

enum YourModuleErrorCode: int
{
    // 17xxx - YourModule模块错误码
    case YOUR_MODULE_NOT_FOUND = 17001;
    case YOUR_MODULE_CREATE_FAILED = 17002;
    case YOUR_MODULE_UPDATE_FAILED = 17003;
    case YOUR_MODULE_DELETE_FAILED = 17004;
    case YOUR_MODULE_VALIDATION_FAILED = 17005;
    case YOUR_MODULE_STATUS_CHANGE_FAILED = 17006;
    case YOUR_MODULE_LIST_FAILED = 17007;
    case YOUR_MODULE_BATCH_ACTION_FAILED = 17008;

    public function message(): string
    {
        return match($this) {
            self::YOUR_MODULE_NOT_FOUND => 'Your module not found',
            self::YOUR_MODULE_CREATE_FAILED => 'Failed to create your module',
            self::YOUR_MODULE_UPDATE_FAILED => 'Failed to update your module',
            self::YOUR_MODULE_DELETE_FAILED => 'Failed to delete your module',
            self::YOUR_MODULE_VALIDATION_FAILED => 'Validation failed',
            self::YOUR_MODULE_STATUS_CHANGE_FAILED => 'Failed to change status',
            self::YOUR_MODULE_LIST_FAILED => 'Failed to get list',
            self::YOUR_MODULE_BATCH_ACTION_FAILED => 'Batch action failed',
        };
    }

    public function httpCode(): int
    {
        return match($this) {
            self::YOUR_MODULE_NOT_FOUND => 404,
            self::YOUR_MODULE_CREATE_FAILED => 500,
            self::YOUR_MODULE_UPDATE_FAILED => 500,
            self::YOUR_MODULE_DELETE_FAILED => 500,
            self::YOUR_MODULE_VALIDATION_FAILED => 422,
            self::YOUR_MODULE_STATUS_CHANGE_FAILED => 500,
            self::YOUR_MODULE_LIST_FAILED => 500,
            self::YOUR_MODULE_BATCH_ACTION_FAILED => 500,
        };
    }
}
```

### 4. 模型模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class YourModule extends Model
{
    use SoftDeletes;

    protected $table = 'tvb_your_modules';

    protected $fillable = [
        'name', 'description', 'status', 'sort', 
        'created_by', 'updated_by'
    ];

    protected $casts = [
        'status' => 'integer',
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 查询作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 查询作用域：禁用状态
     */
    public function scopeDisabled($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 查询作用域：按名称搜索
     */
    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 关联关系示例
     */
    public function relatedModules(): BelongsToMany
    {
        return $this->belongsToMany(RelatedModule::class, 'tvb_your_modules_related', 'your_module_id', 'related_module_id')
                    ->withTimestamps();
    }
}
```

### 5. 服务层模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Services;

use Modules\YourModule\Models\YourModule;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class YourModuleService
{
    /**
     * 获取列表（包含关联数据）
     */
    public function getYourModuleListWithRelated(array $params): array
    {
        $query = YourModule::query();

        // 搜索条件
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        $items = $query->paginate($perPage);

        // 业务逻辑：添加关联数据
        $itemsData = $items->getCollection()->map(function ($item) {
            $itemData = $item->toArray();
            $itemData['related_count'] = $item->relatedModules()->count();
            return $itemData;
        })->toArray();

        return [
            'total' => $items->total(),
            'page' => $items->currentPage(),
            'limit' => $items->perPage(),
            'items' => $itemsData
        ];
    }

    /**
     * 获取列表
     */
    public function getYourModuleList(array $params): LengthAwarePaginator
    {
        $query = YourModule::query();

        // 按名称搜索
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }

        // 按状态筛选
        if (isset($params['status']) && !empty($params['status'])) {
            if ($params['status'] == 1) {
                $query->enabled();
            } else {
                $query->disabled();
            }
        }

        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * 根据ID获取
     */
    public function getYourModuleById(int $id): ?YourModule
    {
        return YourModule::find($id);
    }

    /**
     * 创建
     */
    public function createYourModule(array $params): YourModule
    {
        return DB::transaction(function () use ($params) {
            return YourModule::create($params);
        });
    }

    /**
     * 更新
     */
    public function updateYourModule(int $id, array $params): ?YourModule
    {
        return DB::transaction(function () use ($id, $params) {
            $item = YourModule::find($id);
            if (!$item) {
                return null;
            }

            $item->update($params);
            return $item->fresh();
        });
    }

    /**
     * 删除
     */
    public function deleteYourModule(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $item = YourModule::find($id);
            if (!$item) {
                return false;
            }

            return $item->delete();
        });
    }

    /**
     * 更新状态
     */
    public function updateYourModuleStatus(int $id, int $status, int $updatedBy): bool
    {
        return DB::transaction(function () use ($id, $status, $updatedBy) {
            $item = YourModule::find($id);
            if (!$item) {
                return false;
            }

            $item->update([
                'status' => $status,
                'updated_by' => $updatedBy
            ]);

            return true;
        });
    }

    /**
     * 批量操作
     */
    public function batchAction(string $action, array $ids, int $operatorId): array
    {
        return DB::transaction(function () use ($action, $ids, $operatorId) {
            $items = YourModule::whereIn('id', $ids)->get();
            
            if ($items->isEmpty()) {
                return [
                    'success' => false,
                    'message' => 'No items found',
                    'affected_count' => 0
                ];
            }

            $successCount = 0;

            foreach ($items as $item) {
                try {
                    switch ($action) {
                        case 'delete':
                            $item->delete();
                            $successCount++;
                            break;
                        case 'enable':
                            $item->update(['status' => 1, 'updated_by' => $operatorId]);
                            $successCount++;
                            break;
                        case 'disable':
                            $item->update(['status' => 0, 'updated_by' => $operatorId]);
                            $successCount++;
                            break;
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他项目
                    continue;
                }
            }

            return [
                'success' => true,
                'message' => "Batch {$action} completed",
                'affected_count' => $successCount
            ];
        });
    }
}
```

### 6. 控制器模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\YourModule\Services\YourModuleService;
use Modules\YourModule\Enums\YourModuleErrorCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\ValidationException;

class YourModuleController extends Controller
{
    public function __construct(private YourModuleService $yourModuleService)
    {
    }

    /**
     * 获取列表
     */
    public function index(Request $request): array
    {
        try {
            // 获取请求参数
            $params = [
                'name' => $request->input('name', ''),
                'status' => $request->input('status', ''),
                'sort' => $request->input('order_by', 'sort'),
                'order' => $request->input('order_dir', 'asc'),
                'per_page' => (int) $request->input('limit', 20)
            ];

            // 调用Service层获取列表
            $data = $this->yourModuleService->getYourModuleListWithRelated($params);

            // 返回统一格式
            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.success'),
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_LIST_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 创建
     */
    public function store(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'required|string|max:100|unique:tvb_your_modules,name,NULL,id,deleted_at,NULL',
                'description' => 'nullable|string|max:500',
                'status' => 'integer|in:0,1',
                'sort' => 'nullable|integer|min:0|max:9999'
            ], [
                'name.required' => Lang::get('YourModule::yourmodule.name_required'),
                'name.max' => Lang::get('YourModule::yourmodule.name_max', ['max' => 100]),
                'status.in' => Lang::get('YourModule::yourmodule.status_invalid'),
                'sort.integer' => Lang::get('YourModule::yourmodule.sort_invalid'),
            ]);

            $validated['created_by'] = 1;
            $validated['updated_by'] = 1;

            // 调用Service层创建
            $item = $this->yourModuleService->createYourModule($validated);

            // 返回创建的数据
            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.create_success'),
                'data' => $item->toArray()
            ];
        } catch (ValidationException $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_VALIDATION_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_CREATE_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.create_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 获取单个
     */
    public function show(int $id): array
    {
        try {
            $item = $this->yourModuleService->getYourModuleById($id);

            if (!$item) {
                return [
                    'code' => YourModuleErrorCode::YOUR_MODULE_NOT_FOUND->value,
                    'message' => Lang::get('YourModule::yourmodule.not_found'),
                    'data' => [
                        'error' => 'Item not found'
                    ]
                ];
            }

            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.success'),
                'data' => $item->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_LIST_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.list_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新
     */
    public function update(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'name' => 'required|string|max:100|unique:tvb_your_modules,name,' . $id . ',id,deleted_at,NULL',
                'description' => 'nullable|string|max:500',
                'status' => 'integer|in:0,1',
                'sort' => 'nullable|integer|min:0|max:9999'
            ], [
                'name.required' => Lang::get('YourModule::yourmodule.name_required'),
                'name.max' => Lang::get('YourModule::yourmodule.name_max', ['max' => 100]),
                'status.in' => Lang::get('YourModule::yourmodule.status_invalid'),
                'sort.integer' => Lang::get('YourModule::yourmodule.sort_invalid'),
            ]);

            $validated['updated_by'] = 1;

            // 调用Service层更新
            $item = $this->yourModuleService->updateYourModule($id, $validated);

            if (!$item) {
                return [
                    'code' => YourModuleErrorCode::YOUR_MODULE_NOT_FOUND->value,
                    'message' => Lang::get('YourModule::yourmodule.not_found'),
                    'data' => [
                        'error' => 'Item not found'
                    ]
                ];
            }

            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.update_success'),
                'data' => $item->toArray()
            ];
        } catch (ValidationException $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_VALIDATION_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_UPDATE_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.update_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 删除
     */
    public function destroy(int $id): array
    {
        try {
            $result = $this->yourModuleService->deleteYourModule($id);

            if (!$result) {
                return [
                    'code' => YourModuleErrorCode::YOUR_MODULE_NOT_FOUND->value,
                    'message' => Lang::get('YourModule::yourmodule.not_found'),
                    'data' => [
                        'error' => 'Item not found'
                    ]
                ];
            }

            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.delete_success'),
                'data' => [
                    'success' => true
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_DELETE_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.delete_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 更新状态
     */
    public function updateStatus(Request $request, int $id): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'status' => 'required|integer|in:0,1'
            ], [
                'status.required' => Lang::get('YourModule::yourmodule.status_required'),
                'status.in' => Lang::get('YourModule::yourmodule.status_invalid'),
            ]);

            $result = $this->yourModuleService->updateYourModuleStatus($id, $validated['status'], 1);

            if (!$result) {
                return [
                    'code' => YourModuleErrorCode::YOUR_MODULE_NOT_FOUND->value,
                    'message' => Lang::get('YourModule::yourmodule.not_found'),
                    'data' => [
                        'error' => 'Item not found'
                    ]
                ];
            }

            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.status_update_success'),
                'data' => [
                    'success' => true
                ]
            ];
        } catch (ValidationException $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_VALIDATION_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_STATUS_CHANGE_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.status_change_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 批量操作
     */
    public function batchAction(Request $request): array
    {
        try {
            // 验证请求参数
            $validated = $request->validate([
                'action' => 'required|string|in:delete,enable,disable',
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|min:1'
            ], [
                'action.required' => Lang::get('YourModule::yourmodule.action_required'),
                'action.in' => Lang::get('YourModule::yourmodule.action_invalid'),
                'ids.required' => Lang::get('YourModule::yourmodule.ids_required'),
                'ids.array' => Lang::get('YourModule::yourmodule.ids_invalid'),
                'ids.min' => Lang::get('YourModule::yourmodule.ids_invalid'),
                'ids.*.integer' => Lang::get('YourModule::yourmodule.id_invalid'),
            ]);

            $result = $this->yourModuleService->batchAction(
                $validated['action'],
                $validated['ids'],
                1
            );

            if (!$result['success']) {
                return [
                    'code' => YourModuleErrorCode::YOUR_MODULE_NOT_FOUND->value,
                    'message' => Lang::get('YourModule::yourmodule.not_found'),
                    'data' => [
                        'success' => false,
                        'message' => $result['message'],
                        'affected_count' => $result['affected_count']
                    ]
                ];
            }

            $messageKey = match($validated['action']) {
                'delete' => 'batch_delete_success',
                'enable' => 'batch_enable_success',
                'disable' => 'batch_disable_success',
            };

            return [
                'code' => 200,
                'message' => Lang::get('YourModule::yourmodule.batch_success'),
                'data' => [
                    'success' => true,
                    'message' => Lang::get("YourModule::yourmodule.{$messageKey}", ['count' => $result['affected_count']]),
                    'affected_count' => $result['affected_count'],
                    'action' => $validated['action']
                ]
            ];
        } catch (ValidationException $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_VALIDATION_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.validation_failed'),
                'data' => [
                    'errors' => $e->errors()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => YourModuleErrorCode::YOUR_MODULE_BATCH_ACTION_FAILED->value,
                'message' => Lang::get('YourModule::yourmodule.batch_action_failed'),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
}
```

### 7. 路由模板

```php
<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\YourModule\Api\Controllers\YourModuleController;
use Modules\YourModule\Middleware\LanguageMiddleware;

Route::group(['prefix' => 'api'], function () {
    Route::group(['prefix' => 'yourmodule'], function () {
        Route::middleware([LanguageMiddleware::class])->group(function () {
            Route::get('/', [YourModuleController::class, 'index']);
            Route::post('/', [YourModuleController::class, 'store']);
            Route::get('/{id}', [YourModuleController::class, 'show']);
            Route::put('/{id}', [YourModuleController::class, 'update']);
            Route::delete('/{id}', [YourModuleController::class, 'destroy']);
            Route::patch('/{id}/status', [YourModuleController::class, 'updateStatus']);
            Route::post('/batch', [YourModuleController::class, 'batchAction']);
        });
    });
});
```

### 8. 多语言文件模板

```php
// Modules/YourModule/Lang/zh_CN/yourmodule.php
return [
    // 通用消息
    'success' => '操作成功',
    'create_success' => '创建成功',
    'update_success' => '更新成功',
    'delete_success' => '删除成功',
    'status_update_success' => '状态更新成功',
    
    // 字段名称
    'name' => '名称',
    'description' => '描述',
    'status' => '状态',
    'sort' => '排序',
    
    // 验证消息
    'name_required' => '名称不能为空',
    'name_max' => '名称不能超过:max个字符',
    'status_required' => '状态不能为空',
    'status_invalid' => '状态值无效',
    'sort_invalid' => '排序值无效',
    'validation_failed' => '验证失败',
    
    // 错误消息
    'not_found' => '记录不存在',
    'create_failed' => '创建失败',
    'update_failed' => '更新失败',
    'delete_failed' => '删除失败',
    'list_failed' => '获取列表失败',
    'status_change_failed' => '状态更新失败',
    'batch_action_failed' => '批量操作失败',
    
    // 批量操作
    'batch_success' => '批量操作成功',
    'batch_delete_success' => '批量删除成功，共:count条',
    'batch_enable_success' => '批量启用成功，共:count条',
    'batch_disable_success' => '批量禁用成功，共:count条',
    'action_required' => '操作类型不能为空',
    'action_invalid' => '操作类型无效',
    'ids_required' => 'ID列表不能为空',
    'ids_invalid' => 'ID列表格式无效',
    'id_invalid' => 'ID格式无效',
];
```

### 9. 数据库迁移模板

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tvb_your_modules', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('名称');
            $table->text('description')->nullable()->comment('描述');
            $table->tinyInteger('status')->default(1)->comment('状态：1启用，0禁用');
            $table->integer('sort')->default(0)->comment('排序');
            $table->unsignedBigInteger('created_by')->comment('创建人ID');
            $table->unsignedBigInteger('updated_by')->comment('更新人ID');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'sort']);
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tvb_your_modules');
    }
};
```

### 10. 服务提供者模板

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Providers;

use Illuminate\Support\ServiceProvider;

class YourModuleServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 加载语言文件
        $this->loadTranslationsFrom(__DIR__ . '/../Lang', 'YourModule');
        
        // 加载数据库迁移
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // 加载路由
        $this->loadRoutesFrom(__DIR__ . '/../Api/route.php');
    }
}
```

## 🎯 快速开发步骤

1. **复制模板文件**：将上述模板文件复制到新模块目录
2. **修改命名空间**：将所有 `YourModule` 替换为实际模块名
3. **调整错误码**：修改错误码范围（如 18xxx）
4. **创建数据库迁移**：根据实际需求修改表结构
5. **实现业务逻辑**：在Service层添加具体业务逻辑
6. **添加多语言**：完善语言文件内容
7. **注册模块**：在 `config/app.php` 中注册服务提供者
8. **测试API**：使用Postman测试各个接口

## 📋 检查清单

- [ ] 创建模块目录结构
- [ ] 配置模块文件（composer.json, config.json）
- [ ] 创建错误码枚举
- [ ] 实现Model和关系
- [ ] 编写Service层业务逻辑
- [ ] 实现Controller层
- [ ] 配置路由和中间件
- [ ] 创建数据库迁移
- [ ] 添加多语言支持
- [ ] 注册服务提供者
- [ ] 测试API接口
- [ ] 编写文档

这个模板可以快速创建符合最佳实践的新模块！ 