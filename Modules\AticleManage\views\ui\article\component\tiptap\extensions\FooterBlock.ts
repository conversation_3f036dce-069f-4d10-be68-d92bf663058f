import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { footerTemplate } from '../templates/footer.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    footerBlock: {
      insertFooterBlock: () => ReturnType
    }
  }
}

export const FooterBlock = Node.create({
  name: 'footerBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: 'block*',  // 允许嵌套区块内容

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="footer"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // 添加Font Awesome样式
    const fontAwesomeStyle = document.querySelector('link[href*="font-awesome"]');
    if (!fontAwesomeStyle) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
      document.head.appendChild(link);
    }

    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'footer',
      'class': 'footer-block py-5 text-white bg-dark'
    }), 0]  // 0代表渲染子内容
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertFooterBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(footerTemplate)
        },
    }
  },
}) 