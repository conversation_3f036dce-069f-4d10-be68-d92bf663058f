(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,global.no_active_items=factory())})(this,function(){"use strict";function plugin(){this.hook("instead","setActiveItem",()=>{});this.hook("instead","selectAll",()=>{})}return plugin});