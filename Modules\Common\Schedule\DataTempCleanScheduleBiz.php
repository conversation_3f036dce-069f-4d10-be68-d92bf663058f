<?php

namespace Modules\Common\Schedule;

use Bingo\Core\Dao\ModelUtil;
use Bingo\Data\AbstractDataStorage;
use Bingo\Data\DataManager;
use Illuminate\Support\Facades\Log;
use Modules\Common\Provider\Schedule\AbstractScheduleBiz;

class DataTempCleanScheduleBiz extends AbstractScheduleBiz
{
    public function cron(): string
    {
        return $this->cronEveryHour();
    }

    public function title(): string
    {
        return 'data_temp 文件自动清理';
    }

    public function run()
    {
        $expiredRecords = ModelUtil::model('data_temp')
            ->where('created_at', '<', date('Y-m-d H:i:s', time() - TimeUtil::PERIOD_DAY))
            ->limit(100)
            ->get(['category', 'path'])->toArray();
        foreach ($expiredRecords as $record) {
            $path = AbstractDataStorage::DATA_TEMP.'/'.$record['category'].'/'.$record['path'];
            try {
                DataManager::deleteDataTempByPath($path);
            } catch (\Exception $e) {
                print_r($e->getMessage());
                exit('asdf');
            }
            Log::info('Vendor.DataTempCleanScheduleBiz - '.$path.' deleted');
        }
    }

}
