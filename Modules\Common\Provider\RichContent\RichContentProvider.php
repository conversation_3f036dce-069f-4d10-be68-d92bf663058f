<?php

namespace Modules\Common\Provider\RichContent;

class RichContentProvider
{
    /**
     * @var AbstractRichContentProvider[]
     */
    private static array $instances = [
        UEditorRichContentProvider::class,
    ];

    public static function register($provider)
    {
        self::$instances[] = $provider;
    }

    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function getByName($name): string|AbstractRichContentProvider|null
    {
        foreach (self::all() as $instance) {
            if ($instance->name() == $name) {
                return $instance;
            }
        }
        return null;
    }
}
