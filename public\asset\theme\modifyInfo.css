.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.bwms-page .container {
  flex-grow: 1;
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .container .left-side {
  background-color: #fff;
  width: 200px;
  flex-shrink: 0;
  min-height: 650px;
}
.bwms-page .container .left-side .ul {
  padding: 15px 48px;
}
.bwms-page .container .left-side .ul .li {
  padding: 8px 0;
}
.bwms-page .container .left-side .ul .li.th {
  font-size: 16px;
  color: #222;
}
.bwms-page .container .left-side .ul .li a {
  font-size: 14px;
  color: #666;
  transition: color 0.35s ease-in-out;
}
.bwms-page .container .left-side .ul .li.active a,
.bwms-page .container .left-side .ul .li:hover a {
  color: #ff9600;
}
.bwms-page .container .right-content {
  padding-left: 20px;
  flex-grow: 1;
}
.bwms-page .container .right-content .userinfo {
  background-color: #fff;
  padding: 25px;
  min-height: 650px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.bwms-page .container .right-content .userinfo .tit {
  margin-bottom: 14px;
  font-size: 18px;
  color: #757575;
}
.bwms-page .container .right-content .userinfo form {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .container .right-content .userinfo form .input-box {
  padding: 8px 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.bwms-page .container .right-content .userinfo form .input-box label {
  margin-right: 10px;
  min-width: 114px;
  color: #666;
  font-size: 15px;
  text-align: right;
  line-height: 2.66;
}
.bwms-page .container .right-content .userinfo form .input-box .text {
  font-size: 15px;
  color: #333;
}
.bwms-page .container .right-content .userinfo form .input-box input {
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  background-color: #fff;
  width: 100%;
  min-width: 430px;
  font-size: 15px;
  line-height: 2.66;
  color: #606266;
}
.bwms-page .container .right-content .userinfo form .input-box .pic {
  border-radius: 50%;
  width: 90px;
  height: 90px;
  overflow: hidden;
  position: relative;
}
.bwms-page .container .right-content .userinfo form .input-box .pic.upload {
  cursor: pointer;
}
.bwms-page .container .right-content .userinfo form .input-box .pic .mask {
  padding: 4px 0;
  background-color: rgba(0, 0, 0, 0.4);
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.bwms-page .container .right-content .userinfo form .input-box .pic .mask i {
  color: #fff;
}
.bwms-page .container .right-content .userinfo form button {
  margin-top: 8px;
  border-radius: 0;
  padding: 16px 68px;
  color: #fff;
  background-color: #ff6700;
  transition: all 0.35s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.bwms-page .container .right-content .userinfo form button .iconfont + span {
  margin-left: 5px;
}
.bwms-page .container .right-content .userinfo form button span + .iconfont {
  display: block;
  margin-right: 5px;
}
.bwms-page .container .right-content .userinfo form button span {
  display: block;
  line-height: 1.5;
}
.bwms-page .container .right-content .userinfo form button:hover {
  background-color: #ff9600;
  color: #fff;
}
