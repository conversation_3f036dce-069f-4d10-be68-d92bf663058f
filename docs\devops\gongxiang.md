# BWMS 共享主机与特定环境部署指南

本文档旨在指导用户如何在共享主机环境（如使用 cPanel, DirectAdmin 等控制面板）以及一些特定要求的环境中部署 BWMS 应用。为了方便理解，关键步骤将预留截图位置。

## 1. 概述

共享主机环境通常具有一定的限制，例如可能没有 SSH 权限或对服务器配置的控制较少。本指南将侧重于使用 FTP 和控制面板工具进行部署。对于有更高要求的环境，本文档也会提供相应的指导和建议。

## 2. 先决条件

在开始部署之前，请确保您的环境满足以下要求：

*   **PHP 版本**: **强制要求 PHP 8.2 或更高版本**。PHP 8.2 为当前稳定且性能优化的版本，支持最新语法特性及安全修复，能有效提升系统执行效率与安全性。采用PHP 8.2可确保系统具备更佳的性能表现，并减少因语言版本过旧导致的兼容性问题及安全漏洞风险。具体请参照项目最新的 `public/check.php` 或 `prod.sh` 要求。
*   **PHP 扩展**: 确保已启用以下关键扩展（完整列表请参照 `public/check.php` 或 `prod.sh`）：
    *   `bcmath`, `bz2`, `calendar`, `Core`, `ctype`, `curl`, `date`, `dba`, `dom`, `exif`
    *   `FFI`, `fileinfo`, `filter`, `ftp`, `gd`, `gettext`, `gmp`, `hash`, `iconv`
    *   `imagick`, `intl`, `json`, `ldap`, `libxml`, `mbstring`, `mysqli`, `mysqlnd`
    *   `odbc`, `openssl`, `pcntl`, `pcre`, `PDO`, `pdo_dblib`, `pdo_mysql`, `pdo_odbc`
    *   `pdo_pgsql`, `pdo_sqlite`, `pgsql`, `Phar`, `posix`, `pspell`, `random`
    *   `readline`, `Reflection`, `session`, `shmop`, `SimpleXML`, `soap`, `sockets`
    *   `sodium`, `SPL`, `sqlite3`, `standard`, `sysvmsg`, `sysvsem`, `sysvshm`, `tidy`
    *   `tokenizer`, `xml`, `xmlreader`, `xmlwriter`, `xsl`, `Zend OPcache`, `zip`, `zlib`
*   **数据库**: MySQL 5.7+ 或 MariaDB (推荐使用 MySQL 5.7 或更高版本以保证高效性能与安全特性)。
*   **FTP 客户端**: 如 FileZilla, Cyberduck 等，用于上传文件。
*   **控制面板访问**: cPanel, DirectAdmin 或其他共享主机提供的管理面板 (如果适用)。
*   **Node.js**: 对于共享主机/FTP部署（已编译版本），通常**不需要**在服务器上安装 Node.js。前端资源应在本地或构建服务器上编译好。

## 3. 部署步骤

### 步骤 1: 准备应用文件

1.  **获取代码**: 从版本控制系统（如 Git）获取最新稳定版本的代码，或者下载预编译的发布包（如果提供）。
    *   *截图位置：显示从 Git 克隆或下载压缩包的示例。*
2.  **本地依赖安装 (如果从源码部署)**:
    *   在您的本地开发环境执行 `composer install --no-dev --optimize-autoloader` 来安装 PHP 依赖。
        *   *截图位置：显示在本地终端运行 `composer install` 命令的示例。*
    *   如果项目包含前端资源需要编译，请在本地执行编译命令（例如 `yarn install` 和 `yarn build`）。确保将编译后的静态资源（通常在 `public/build` 或类似目录）包含在内。
        *   *截图位置：显示在本地终端运行 `yarn build` 命令的示例。*
3.  **打包文件**: 将所有应用文件（包括 `vendor` 目录和编译后的前端资源）打包成一个 `.zip` 文件。
    *   *截图位置：显示将项目文件打包成 ZIP 文件的示例。*

### 步骤 2: 上传文件到服务器

1.  使用 FTP 客户端连接到您的共享主机账户。
    *   *截图位置：显示 FileZilla 或类似 FTP 客户端连接成功的界面。*
2.  导航到您网站的根目录（通常是 `public_html`, `www`, `htdocs` 或类似名称）。
    *   *截图位置：显示 FTP 客户端中网站根目录的结构。*
3.  上传您在步骤 1 中创建的 `.zip` 文件。
    *   *截图位置：显示 FTP 客户端上传 ZIP 文件的过程。*
4.  通过控制面板的文件管理器解压该 `.zip` 文件。
    *   *截图位置：显示 cPanel/DirectAdmin 文件管理器解压 ZIP 文件的操作界面。*

### 步骤 3: 创建数据库

1.  登录到您的共享主机控制面板 (cPanel, DirectAdmin 等)。
    *   *截图位置：显示 cPanel/DirectAdmin 登录界面。*
2.  找到数据库管理工具（例如 "MySQL Databases" 或 "数据库管理"）。
    *   *截图位置：显示 cPanel/DirectAdmin 中数据库管理工具的入口。*
3.  创建一个新的数据库。
    *   *截图位置：显示创建新数据库的表单和成功提示。*
4.  创建一个新的数据库用户，并为其设置一个强密码。
    *   *截图位置：显示创建新数据库用户的表单和成功提示。*
5.  将新创建的用户添加到新创建的数据库，并授予所有权限 (ALL PRIVILEGES)。
    *   *截图位置：显示为用户授予数据库权限的操作界面。*
6.  **记下数据库名、用户名和密码**，后续配置 `.env` 文件时会用到。

### 步骤 4: 配置 `.env` 文件

1.  在服务器上，通过控制面板的文件管理器或 FTP 找到应用根目录下的 `.env.example` 文件。
2.  复制并重命名为 `.env`。
    *   *截图位置：显示在文件管理器中复制并重命名 `.env.example` 文件的操作。*
3.  编辑 `.env` 文件，更新以下关键配置：
    ```env
    APP_NAME="Your Application Name"
    APP_ENV=production
    APP_KEY=
    APP_DEBUG=false # 部署初期可临时设置为 true 以便运行 check.php，完成后务必改回 false
    APP_URL=https://yourdomain.com # 替换为您的域名

    DB_CONNECTION=mysql
    DB_HOST=127.0.0.1 # 或共享主机提供的数据库主机名
    DB_PORT=3306
    DB_DATABASE=your_database_name # 替换为步骤 3 创建的数据库名
    DB_USERNAME=your_database_user # 替换为步骤 3 创建的数据库用户名
    DB_PASSWORD=your_database_password # 替换为步骤 3 创建的数据库密码
    ```
    *   *截图位置：显示编辑 `.env` 文件并填写数据库信息的示例。*
4.  **生成 APP_KEY**: 如果您无法在服务器上运行 `php artisan key:generate`，可以在本地项目中运行该命令，然后将生成的密钥复制到服务器的 `.env` 文件中的 `APP_KEY=` 后面。
    例如：`APP_KEY=base64:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx=`
    *   *截图位置：显示本地运行 `php artisan key:generate` 并复制 APP_KEY 的示例。*

### 步骤 5: 运行环境检查脚本

BWMS 提供了一个环境检查脚本 `public/check.php`，用于验证服务器配置是否满足要求。

1.  **临时启用调试模式**: 在 `.env` 文件中，临时将 `APP_DEBUG` 设置为 `true`。
    ```env
    APP_DEBUG=true
    ```
    *   *截图位置：显示 `.env` 文件中 `APP_DEBUG=true` 的设置。*
2.  通过浏览器访问 `https://yourdomain.com/check.php` (将 `yourdomain.com` 替换为您的实际域名)。
    *   *截图位置：显示浏览器访问 `check.php` 的初始页面。*
3.  在页面上，选择环境类型为 **“共享主机/FTP部署 (已编译)”** 并提交。
    *   *截图位置：显示 `check.php` 页面选择 “共享主机/FTP部署” 选项并提交的界面。*
4.  脚本将检查 PHP 版本、PHP 扩展、目录权限等。
    *   *截图位置：显示 `check.php` 检查结果通过的示例。*
    *   *截图位置：显示 `check.php` 检查结果存在问题的示例（如有必要）。*
5.  **解决问题**: 如果检查报告任何错误或警告，请根据提示进行修复。常见的修复包括：
    *   联系主机商启用缺失的 PHP 扩展。
    *   调整目录权限（见步骤 6）。
6.  **禁用调试模式**: 检查通过后，**务必**将 `.env` 文件中的 `APP_DEBUG` 设置回 `false`。
    ```env
    APP_DEBUG=false
    ```
    *   *截图位置：显示 `.env` 文件中 `APP_DEBUG=false` 的最终设置。*

### 步骤 6: 设置文件和目录权限

正确的文件和目录权限对于应用的安全和功能至关重要。

*   **目录**: 通常设置为 `755`。
*   **文件**: 通常设置为 `644`。
*   **特殊目录**: 以下目录及其子目录和文件需要对 Web 服务器用户可写：
    *   `storage/`
    *   `bootstrap/cache/`
    您可以使用控制面板的文件管理器或 FTP 客户端来修改权限。对于 `storage` 和 `bootstrap/cache`，如果 `755/644` 不足以让应用写入（例如日志、缓存、会话文件），您可能需要尝试 `775` 或在某些情况下（不推荐，但有时共享主机环境需要）`777`。请首先尝试最严格的权限。

    参考 `prod.sh` 中的 `init` 命令，关键目录权限设置如下（这些命令在有 shell 权限时使用，共享主机需通过控制面板或 FTP 修改）：
    *   `storage` 目录及其所有子目录：`777` (或 `775` 如果 Web 服务器用户和您的 FTP 用户在同一组)
    *   `storage` 目录及其所有文件：`666` (或 `664`)
    *   `bootstrap/cache` 目录：`777` (或 `775`)
    *   *截图位置：显示在 cPanel/DirectAdmin 文件管理器中修改 `storage` 和 `bootstrap/cache` 目录权限的操作。*

### 步骤 7: 运行数据库迁移 (可选，取决于主机能力)

1.  **如果您的共享主机提供 SSH 访问或终端工具**:
    *   连接到服务器。
    *   导航到应用根目录。
    *   运行 `php artisan migrate --force` 来创建数据库表结构。
        *   *截图位置：显示通过 SSH 运行 `php artisan migrate --force` 的示例。*
2.  **如果没有 SSH 访问**:
    *   **方案 A: 手动导入 SQL**: 您可以在本地开发环境运行迁移，然后导出数据库结构 (不含数据) 和必要的种子数据 (如果适用)，再通过 phpMyAdmin (通常由控制面板提供) 导入到服务器数据库。确保数据库已做好备份与异常恢复方案，降低数据丢失风险。
        *   *截图位置：显示在 phpMyAdmin 中导入 SQL 文件的操作界面。*
    *   **方案 B: 应用内迁移**: 某些应用可能提供一个通过 Web 界面运行迁移的功能（不常见于标准 Laravel 项目）。

### 步骤 8: 创建存储链接 (Storage Link)

Laravel 使用存储链接 (`public/storage`) 来公开访问 `storage/app/public` 目录中的文件。

1.  **如果您的共享主机提供 SSH 访问或终端工具**:
    *   连接到服务器。
    *   导航到应用根目录。
    *   运行 `php artisan storage:link`。
        *   *截图位置：显示通过 SSH 运行 `php artisan storage:link` 的示例。*
2.  **如果没有 SSH 访问**:
    *   **方案 A: 控制面板 Symlink**: 某些控制面板 (如 cPanel) 可能允许您创建符号链接。您需要创建一个从 `public/storage` 指向 `storage/app/public` 的符号链接。
        *   *截图位置：显示在 cPanel/DirectAdmin 中创建符号链接的操作界面（如果面板支持）。*
    *   **方案 B: 手动复制 (不推荐，但作为备选)**: 如果无法创建符号链接，您可以将 `storage/app/public` 目录的内容**复制**到 `public/storage` 目录。但请注意，这意味着您每次更新 `storage/app/public` 中的文件时，都需要手动同步到 `public/storage`。
        *   *截图位置：显示通过文件管理器复制 `storage/app/public` 内容到 `public/storage` 的操作。*
    *   **方案 C: 修改文件系统配置 (高级)**: 修改 `config/filesystems.php` 中 `public` disk 的 `url` 和 `root` 配置，使其直接使用 `storage/app/public` 而不依赖符号链接。这可能需要调整 Web 服务器配置以允许直接访问该路径，并可能带来安全隐患，不推荐初学者使用。

    *注意: `check.php` 在 “共享主机/FTP部署” 模式下会跳过 Storage Link 的检测，这意味着您需要自行确保公共文件的可访问性。*

### 步骤 9: 配置 Web 服务器文档根目录

确保您的域名或子域名指向应用的 `public` 目录。

1.  登录到您的共享主机控制面板。
2.  找到域名管理或站点设置部分 (例如 cPanel 中的 "Domains" 或 "Addon Domains")。
3.  修改您网站的文档根目录 (Document Root) 为应用根目录下的 `public` 文件夹 (例如: `/home/<USER>/public_html/your_app_folder/public`)。
    *   *截图位置：显示在 cPanel/DirectAdmin 中修改域名文档根目录的设置界面。*

### 步骤 10: 最后测试

1.  清除浏览器缓存和 cookie。
2.  访问您的网站 `https://yourdomain.com`。
    *   *截图位置：显示浏览器成功访问部署后网站首页的示例。*
3.  全面测试网站的各项功能，确保一切正常工作。

## 4. 故障排除

*   **500 Internal Server Error**: 检查 `.env` 文件配置是否正确、文件权限是否正确、PHP 版本是否兼容、是否缺少必要的 PHP 扩展。查看服务器错误日志 (通常在控制面板中可以找到) 获取详细信息。
    *   *截图位置：显示 cPanel/DirectAdmin 中查看错误日志的入口。*
*   **404 Not Found**: 检查文档根目录是否正确指向 `public` 目录，URL 重写规则 (通常在 `.htaccess` 文件中，Laravel 项目默认包含) 是否生效。
*   **无法写入文件/目录**: 通常是 `storage` 或 `bootstrap/cache` 目录权限问题。
*   **`check.php` 报告问题**: 仔细阅读 `check.php` 的输出，并根据提示进行修复。

## 5. 部署后维护与安全

成功部署 BWMS 后，为确保系统稳定运行和数据安全，强烈建议执行以下维护和安全措施：

### 5.1. 核心安全设置

*   **`APP_DEBUG`**: **务必确保**在生产环境的 `.env` 文件中将 `APP_DEBUG` 设置为 `false`。
*   **强密码策略**: 为数据库、FTP/SFTP、控制面板以及 BWMS 管理员账户设置并定期更新强密码。
*   **`check.php` 文件**: 部署和初步检查完成后，建议删除 `public/check.php` 文件，或通过 Web 服务器配置（如 `.htaccess`）限制对其的公开访问。

### 5.2. 系统与应用更新

*   **BWMS 及其依赖**: 定期检查并更新 BWMS 应用及其 PHP 依赖（通过在本地环境运行 `composer update`，充分测试后再上传到生产环境），以获取最新的安全补丁和功能改进。
*   **服务器软件**: 保持服务器操作系统、PHP、MySQL/MariaDB、Web 服务器（Nginx/Apache）等关键软件为最新的稳定版本，并及时应用安全更新。

### 5.3. 数据备份与恢复

*   **定期备份**: 制定并执行定期备份策略，备份内容应包括完整的网站文件（特别是 `storage` 目录）和数据库。许多共享主机控制面板提供自动备份工具。
    *   *截图位置：显示 cPanel/DirectAdmin 中备份工具的示例。*
*   **恢复计划**: 制定并测试数据恢复计划，确保在发生数据丢失或系统故障时能够快速有效地恢复服务。

### 5.4. BWMS 安全检测与修复 (推荐)

*   **自动化扫描**: 定期使用自动化安全扫描工具（如 Acunetix、Fortify，或开源的 OWASP ZAP）对 BWMS 系统进行全面的漏洞扫描和安全评估。
*   **渗透测试**: 如果预算和条件允许，建议安排专业的安全团队进行渗透测试，以发现更深层次的安全问题。
*   **漏洞管理**: 对扫描和测试发现的漏洞进行风险评估和分级（高、中、低风险），并根据严重程度制定修复计划，优先处理高风险漏洞。

### 5.5. 性能监控与优化 (推荐)

*   **性能测试**: 对于预期有较高并发访问的系统，建议进行性能测试。可使用 Apache JMeter 等工具模拟真实用户场景，评估系统在压力下的表现，找出性能瓶颈。
*   **优化措施**: 根据测试结果，针对性地进行性能优化，可能包括：
    *   数据库查询优化、索引调整。
    *   启用和配置缓存机制（如 OpCache、Redis、Memcached，如果服务器环境支持）。
    *   优化代码逻辑，减少资源消耗。

### 5.6. 服务器安全防护 (推荐)

*   **基础防护**: 
    *   **防火墙**: 配置并启用服务器防火墙（如 `iptables`, `firewalld`, `UFW`），仅开放必要的端口。
    *   **HTTPS**: 为网站启用 HTTPS，确保数据传输加密。可使用 Let's Encrypt 获取免费 SSL/TLS 证书。
    *   **Web 服务器安全配置**:遵循 Nginx 或 Apache 的安全最佳实践进行配置。
*   **进阶防护 (根据需求选择)**:
    *   **入侵检测/防御系统 (IDS/IPS)**: 部署 IDS/IPS 以监控和阻止恶意活动。
    *   **Web 应用防火墙 (WAF)**: 使用 WAF 过滤恶意 Web 请求。
    *   **日志监控与分析**: 定期审查服务器和应用日志，及时发现异常行为。
    *   **安全信息和事件管理 (SIEM)**: 集中管理和分析安全日志和事件。

## 6. 推荐的服务器环境与兼容性

虽然本指南主要针对共享主机，但为获得最佳性能、安全性和可维护性，BWMS 推荐在以下环境中部署：

*   **操作系统**: 具备长期支持（LTS）的 Linux 发行版，如 Ubuntu LTS (例如 20.04, 22.04 或以上), CentOS 8+, RedHat Enterprise Linux。
*   **Web 服务器**: Nginx (推荐) 或 Apache。配置需符合最佳安全实践，如启用 HTTPS (使用 Let's Encrypt 等免费证书或商业证书)，合理设定防火墙规则。
*   **数据库**: MySQL 5.7+ 或 MariaDB 10.2+。
*   **多平台兼容性**: BWMS致力于在多种主流环境中稳定运行。开发和测试过程中会考虑以下组合：
    *   Web 服务器：Apache、Nginx
    *   数据库系统：MySQL、MariaDB
    *   操作系统平台：Windows Server (尽力支持，但 Linux 为主推荐)、CentOS 8+、Ubuntu、RedHat

## 7. 关于极端环境的说明

(此部分将根据您提供的“极端情况”具体内容进行补充，说明 BWMS 在这些特定场景下的支持范围、潜在限制或特殊配置要求。)

---

希望这份更新的指南能帮助您成功部署 BWMS！如果您遇到特定于主机环境的问题，建议查阅主机商的文档或联系其技术支持。