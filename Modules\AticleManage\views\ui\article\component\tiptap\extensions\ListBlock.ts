import { Node, mergeAttributes, nodeInputRule } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

export interface ListBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    listBlock: {
      /**
       * 添加列表块
       */
      setListBlock: (options?: { items?: string[] }) => ReturnType
    }
  }
}

export const ListBlock = Node.create<ListBlockOptions>({
  name: 'listBlock',
  group: 'block',
  defining: true,
  isolating: true,
  draggable: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'bootstrap-list'
      }
    }
  },

  addAttributes() {
    return {
      listStyle: {
        default: 'circle',
        parseHTML: element => element.getAttribute('data-list-style') || 'circle',
        renderHTML: attributes => {
          return {
            'data-list-style': attributes.listStyle
          }
        }
      },
      listColor: {
        default: '#6c5ce7',
        parseHTML: element => element.getAttribute('data-list-color') || '#6c5ce7',
        renderHTML: attributes => {
          return {
            'data-list-color': attributes.listColor
          }
        }
      },
      items: {
        default: [],
        parseHTML: element => {
          const itemEls = element.querySelectorAll('.list-item-text')
          const items = Array.from(itemEls).map(item => item.textContent || '')
          return items.length ? items : ['Add a list item here.']
        },
        renderHTML: attributes => {
          return {}
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-bs-component="list"]'
      }
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const listStyle = HTMLAttributes['data-list-style'] || 'circle'
    const listColor = HTMLAttributes['data-list-color'] || '#6c5ce7'
    const items = node.attrs.items || ['Add a list item here.']
    
    // 生成列表项HTML
    const listItemsHTML = items.map(item => `
      <div class="list-item">
        <div class="list-marker" style="background-color: ${listColor};"></div>
        <div class="list-item-text">${item}</div>
      </div>
    `).join('')

    return [
      'div',
      mergeAttributes(
        { 'data-bs-component': 'list' },
        this.options.HTMLAttributes
      ),
      [
        'div',
        { class: 'container-fluid p-0' },
        [
          'div',
          { class: 'row justify-content-center' },
          [
            'div',
            { class: 'col-12 col-md-10 col-lg-8' },
            [
              'div',
              { 
                class: `custom-list list-style-${listStyle}`,
                'data-list-style': listStyle,
                'data-list-color': listColor
              },
              [
                'div',
                { class: 'list-container' },
                listItemsHTML
              ]
            ]
          ]
        ]
      ]
    ]
  },

  addCommands() {
    return {
      setListBlock: (options = {}) => ({ commands }) => {
        const items = options.items || ['Add a list item here.', 'Add a list item here.', 'Add a list item here.', 'Add a list item here.']
        
        return commands.insertContent({
          type: this.name,
          attrs: {
            items: items
          }
        })
      }
    }
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: /^\*\*\* (.+)$/,
        type: this.type,
        getAttributes: match => {
          return { 
            items: [match[1]],
          }
        }
      })
    ]
  }
}) 