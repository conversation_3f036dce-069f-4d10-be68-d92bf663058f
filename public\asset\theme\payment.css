/* 支付模块样式 */
.payment-section {
    margin: 15px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 20px;
}
.payment-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}
.payment-methods {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 15px;
}
.payment-method {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
    width: 180px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
.payment-method:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24,144,255,0.15);
}
.payment-method.active {
    border-color: #1890ff;
    background-color: #f0f9ff;
}
.payment-method-inner {
    display: flex;
    align-items: center;
    padding: 15px;
}
.payment-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    background: transparent;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.payment-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
}
/* 支付图标背景样式已移除，使用图片logo替代 */
.payment-icon i {
    font-size: 18px;
}
.payment-name {
    font-size: 14px;
    font-weight: 500;
}
.payment-description {
    font-size: 12px;
    color: #888;
    margin-top: 3px;
}
.payment-info {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
}
.payment-info-row {
    display: flex;
    margin-bottom: 15px;
}
.payment-info-label {
    width: 120px;
    color: #666;
    font-weight: 500;
}
.payment-info-value {
    flex: 1;
}
.payment-amount-options {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    gap: 10px;
}
.amount-option {
    position: relative;
}
.amount-radio {
    position: absolute;
    opacity: 0;
}
.amount-label {
    display: inline-block;
    padding: 8px 20px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
}
.amount-radio:checked + .amount-label {
    border-color: #1890ff;
    background: #e6f7ff;
    color: #1890ff;
}
.payment-submit {
    text-align: right;
    margin-top: 30px;
}
.payment-button {
    background: #1890ff;
    color: white;
    border: none;
    padding: 10px 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;
    box-shadow: 0 2px 5px rgba(24,144,255,0.2);
}
.payment-button:hover {
    background: #40a9ff;
    box-shadow: 0 4px 8px rgba(24,144,255,0.3);
}
.payment-hint {
    text-align: right;
    margin-top: 8px;
    color: #52c41a;
    font-size: 12px;
}
.payment-price {
    color: #ff4d4f;
    font-weight: bold;
    font-size: 20px;
    margin-right: 15px;
}
.payment-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
}
.payment-table th,
.payment-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}
.payment-table th {
    background: #fafafa;
    font-weight: 500;
    color: #555;
}
.payment-table tr:last-child td {
    border-bottom: none;
}
.payment-table tr:hover td {
    background: #fafafa;
}
.payment-status {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 12px;
}
.status-success {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}
.status-pending {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}
.status-failed {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}
.payment-operate {
    color: #1890ff;
    cursor: pointer;
    transition: all 0.3s;
}
.payment-operate:hover {
    color: #40a9ff;
    text-decoration: underline;
}
.upload-section {
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
    display: none;
    border: 1px dashed #d9d9d9;
}
.upload-section.active {
    display: block;
}
.upload-title {
    font-weight: 500;
    margin-bottom: 10px;
    color: #555;
}
.upload-description {
    color: #888;
    font-size: 13px;
    margin-bottom: 15px;
}
.upload-button {
    display: inline-block;
    padding: 8px 20px;
    background: #f0f0f0;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}
.upload-button:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #e6f7ff;
}
.qrcode-section {
    display: none;
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
}
.qrcode-section.active {
    display: block;
}
.qrcode-image {
    width: 200px;
    height: 200px;
    margin: 0 auto 15px;
    background: #fff;
    padding: 10px;
    border: 1px solid #eee;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.qrcode-title {
    font-weight: 500;
    margin-bottom: 10px;
    color: #555;
}
.qrcode-description {
    color: #888;
    font-size: 13px;
}
.payment-category {
    margin-bottom: 30px;
}
.payment-category-title {
    font-size: 16px;
    color: #555;
    margin-bottom: 15px;
    font-weight: 500;
}

/* 整体优化 */
body {
    background-color: #f7f9fc;
}

/* 卡片优化 */
.payment-section {
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

/* 标题优化 */
.payment-title {
    position: relative;
}
.payment-title:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 3px;
}

/* 支付方式卡片优化 */
.payment-method {
    position: relative;
    transition: all 0.3s ease;
}
.payment-method:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(24,144,255,0.15);
}
.payment-method.active:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 15px 15px 0;
    border-color: transparent #1890ff transparent transparent;
}

/* 图标优化 */
.payment-icon {
    position: relative;
    overflow: hidden;
}
.payment-icon:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
}
.payment-icon.hong-kong {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}
.payment-icon.mainland {
    background: linear-gradient(135deg, #3498db, #2980b9);
}
.payment-icon.international {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

/* 分类标题优化 */
.payment-category-title {
    display: flex;
    align-items: center;
}
.payment-category-title:before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background: linear-gradient(to bottom, #3498db, #2980b9);
    margin-right: 8px;
    border-radius: 2px;
}

/* 金额选项优化 */
.amount-label {
    border-radius: 8px;
}
.amount-radio:checked + .amount-label {
    box-shadow: 0 2px 5px rgba(24,144,255,0.15);
}

/* 按钮优化 */
.payment-button {
    background: linear-gradient(135deg, #1890ff, #0050b3);
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(24,144,255,0.25);
}
.payment-button:hover {
    background: linear-gradient(135deg, #40a9ff, #096dd9);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(24,144,255,0.3);
}

/* 二维码区域优化 */
.qrcode-image {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-radius: 10px;
    position: relative;
}
.qrcode-image:before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 1px dashed #e2e8f0;
    border-radius: 12px;
    z-index: -1;
}

/* 上传按钮优化 */
.upload-button {
    transition: all 0.3s ease;
}
.upload-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(24,144,255,0.15);
}

/* 状态标签优化 */
.payment-status {
    font-weight: 500;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.upload-section.active, .qrcode-section.active {
    animation: fadeIn 0.5s ease;
}

/* 表格优化 */
.payment-table {
    border-radius: 8px;
    overflow: hidden;
}

/* 订单号样式 */
#order-no {
    font-family: monospace;
    background: #edf2f7;
    padding: 3px 6px;
    border-radius: 4px;
    color: #2c3e50;
    font-size: 13px;
}


/* Stripe嵌入式结账样式 */
.stripe-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stripe-wrapper {
    background: white;
    border-radius: 8px;
    width: 480px;
    max-width: 95%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stripe-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.stripe-header h3 {
    margin: 0;
    font-size: 18px;
}

.stripe-close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.stripe-close:hover {
    color: #333;
}

#stripe-checkout-container {
    min-height: 400px;
}

/* 加载状态样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.loading-message {
    font-size: 16px;
    color: #333;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 支付方式样式 */
.payment-section {
    margin-bottom: 30px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.payment-title {
    font-size: 18px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.payment-category {
    margin-bottom: 20px;
}

.payment-category-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.payment-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.payment-method {
    width: calc(33.333% - 10px);
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
}

.payment-method:hover {
    border-color: #999;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-method.active {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.payment-method-inner {
    padding: 15px;
    display: flex;
    align-items: center;
}

.payment-icon {
    width: 48px;
    height: 48px;
    background: #f5f5f5;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 24px;
}

.payment-icon.hong-kong {
    background: #f0f5ff;
    color: #1890ff;
}

.payment-icon.mainland {
    background: #f6ffed;
    color: #52c41a;
}

.payment-icon.international {
    background: #fff7e6;
    color: #fa8c16;
}

.payment-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.payment-description {
    color: #999;
    font-size: 12px;
}

/* 支付信息样式 */
.payment-info {
    margin-bottom: 20px;
    background: #fafafa;
    padding: 15px;
    border-radius: 6px;
}

.payment-info-row {
    display: flex;
    margin-bottom: 10px;
}

.payment-info-label {
    width: 120px;
    font-weight: bold;
}

.payment-amount-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.amount-option {
    position: relative;
}

.amount-radio {
    position: absolute;
    opacity: 0;
}

.amount-label {
    display: block;
    padding: 8px 15px;
    background: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.amount-radio:checked + .amount-label {
    background: #1890ff;
    color: #fff;
}

/* 上传区域样式 */
.upload-section {
    display: none;
    background: #f9f9f9;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.upload-section.active {
    display: block;
}

.upload-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.upload-description {
    color: #666;
    margin-bottom: 15px;
}

.upload-button {
    display: inline-block;
    padding: 10px 20px;
    background: #1890ff;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.upload-button:hover {
    background: #40a9ff;
}

/* 二维码区域样式 */
.qrcode-section {
    display: none;
    background: #f9f9f9;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.qrcode-section.active {
    display: block;
}

.qrcode-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}

.qrcode-image {
    width: 200px;
    height: 200px;
    margin: 0 auto 15px;
}

.qrcode-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.qrcode-description {
    color: #666;
    margin-bottom: 10px;
}

/* 支付按钮样式 */
.payment-submit {
    text-align: center;
    margin-top: 20px;
}

.payment-price {
    display: block;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #f5222d;
}

.payment-button {
    padding: 12px 30px;
    background: #1890ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
}

.payment-button:hover {
    background: #40a9ff;
}

.payment-hint {
    margin-top: 10px;
    color: #999;
    font-size: 12px;
}

/* 支付记录表格样式 */
.payment-table {
    width: 100%;
    border-collapse: collapse;
}

.payment-table th, .payment-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
}

.payment-table th {
    background: #fafafa;
    font-weight: bold;
}

.payment-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-success {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-pending {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.status-failed {
    background: #fff2f0;
    color: #f5222d;
    border: 1px solid #ffccc7;
}

.status-refund {
    background: #f9f0ff;
    color: #722ed1;
    border: 1px solid #d3adf7;
}

.payment-operate {
    color: #1890ff;
    cursor: pointer;
}

.payment-operate:hover {
    text-decoration: underline;
}

/* Stripe容器样式 */
.stripe-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
}

.stripe-wrapper {
    background: #fff;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    overflow: hidden;
}

.stripe-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.stripe-header h3 {
    margin: 0;
}

.stripe-close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

/* 加载状态样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

.loading-message {
    color: #fff;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 新增样式 - 上传凭证按钮 */
.receipt-upload-btn {
    display: inline-block;
    margin-top: 15px;
    padding: 10px 20px;
    background: #1890ff;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 14px;
}

.receipt-upload-btn:hover {
    background: #40a9ff;
}

.receipt-upload-btn i {
    margin-right: 5px;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .payment-method {
        width: 100%;
    }
    
    .payment-info-row {
        flex-direction: column;
    }
    
    .payment-info-label {
        width: 100%;
        margin-bottom: 5px;
    }
}