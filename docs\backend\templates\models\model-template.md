# 模型模板

## 概述

模型是应用程序中的数据层，代表数据库表结构和业务实体。本文档提供了模型的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * YourModel
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property int $status
 * @property int $created_by
 * @property int $updated_by
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $deleted_at
 *
 * @property-read User $creator
 * @property-read User $updater
 * @property-read Collection|RelatedModel[] $relatedModels
 *
 * @method static Builder|self query()
 * @method static Builder|self whereId($value)
 * @method static Builder|self whereName($value)
 * @method static Builder|self whereStatus($value)
 */
class YourModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'your_table';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 主键类型
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * 是否主动维护时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'description',
        'status',
        'created_by',
        'updated_by'
    ];

    /**
     * 隐藏的属性
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at'
    ];

    /**
     * 数据类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id' => 'integer',
        'status' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 默认值
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'status' => 1
    ];

    /**
     * 常量定义
     */
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    /**
     * 状态映射
     *
     * @var array<int, string>
     */
    public static array $statusMap = [
        self::STATUS_ACTIVE => '启用',
        self::STATUS_INACTIVE => '禁用'
    ];

    /**
     * 创建者关联
     *
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 更新者关联
     *
     * @return BelongsTo
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 关联模型
     *
     * @return HasMany
     */
    public function relatedModels(): HasMany
    {
        return $this->hasMany(RelatedModel::class);
    }

    /**
     * 查询作用域：按状态查询
     *
     * @param Builder $query
     * @param int $status
     * @return Builder
     */
    public function scopeWhereStatus(Builder $query, int $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 查询作用域：按名称模糊查询
     *
     * @param Builder $query
     * @param string $name
     * @return Builder
     */
    public function scopeWhereLikeName(Builder $query, string $name): Builder
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 查询作用域：按时间范围查询
     *
     * @param Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return Builder
     */
    public function scopeWhereDateBetween(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return self::$statusMap[$this->status] ?? '';
    }

    /**
     * 启用状态检查
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 禁用状态检查
     *
     * @return bool
     */
    public function isInactive(): bool
    {
        return $this->status === self::STATUS_INACTIVE;
    }

    /**
     * 启用记录
     *
     * @return bool
     */
    public function activate(): bool
    {
        return $this->update(['status' => self::STATUS_ACTIVE]);
    }

    /**
     * 禁用记录
     *
     * @return bool
     */
    public function deactivate(): bool
    {
        return $this->update(['status' => self::STATUS_INACTIVE]);
    }
}
```

## 规范要求

1. 模型结构
   - 使用严格类型声明
   - 完整的 PHPDoc 注释
   - 属性和方法的访问修饰符
   - 合理的 trait 使用

2. 属性定义
   - 表名显式定义
   - 主键类型声明
   - 时间戳设置
   - 字段映射明确

3. 关联关系
   - 关联方法返回类型
   - 关联字段明确
   - 关联模型引用
   - 关联注释完整

4. 查询作用域
   - 命名规范统一
   - 参数类型声明
   - 返回值类型声明
   - 功能注释完整

## 最佳实践

1. 属性定义
```php
protected $fillable = [
    'name',
    'description',
    'status'
];

protected $casts = [
    'id' => 'integer',
    'status' => 'integer',
    'created_at' => 'datetime'
];
```

2. 关联关系
```php
public function creator(): BelongsTo
{
    return $this->belongsTo(User::class, 'created_by');
}

public function items(): HasMany
{
    return $this->hasMany(Item::class);
}
```

3. 查询作用域
```php
public function scopeActive(Builder $query): Builder
{
    return $query->where('status', self::STATUS_ACTIVE);
}

public function scopeSearch(Builder $query, array $params): Builder
{
    return $query->when(
        $params['keyword'] ?? null,
        fn ($q, $keyword) => $q->where('name', 'like', "%{$keyword}%")
    );
}
```

## 常见问题

1. 属性定义
```php
// 好的实践 - 明确的属性定义
protected $fillable = ['name', 'status'];
protected $casts = ['status' => 'integer'];

// 不好的实践 - 使用 guarded
protected $guarded = [];
```

2. 查询作用域
```php
// 好的实践 - 使用查询作用域
public function scopeActive($query)
{
    return $query->where('status', 1);
}

// 不好的实践 - 重复的查询条件
YourModel::where('status', 1)->get();
YourModel::where('status', 1)->first();
```

## 注意事项

1. 属性定义完整
2. 类型转换明确
3. 关联关系清晰
4. 查询作用域复用
5. 代码注释规范
6. 性能优化考虑
