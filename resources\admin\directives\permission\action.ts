import { useUserStore } from '/admin/stores/modules/user';
import { MenuType } from '/admin/enum/app';

// 格式化操作字符串，确保一致的比较逻辑
function formatAction(action: string) {
    return action.replace('@', '.').toLowerCase();
}

// 检查是否有权限
function hasPermission(permissions: any[] | undefined, action: string) {
    return permissions?.some(permission => {
        if (permission.type === MenuType.Button_Type) {
            const formattedPermission = `${permission.module}.${permission.permission_mark}`.replace('@', '.').toLowerCase();
            return formatAction(action) === formattedPermission;
        }
        return false;
    });
}

function checkAction(el: { style: { display: string; }; }, binding: { value: any; }) {
    const action = binding.value;
    if (action && typeof action === 'string') {
        const userStore = useUserStore();
        const permissions = userStore.getPermissions;

        const formattedAction = formatAction(action);
        const hasAction = hasPermission(permissions, formattedAction);

        if (!hasAction) {
            // 如果没有权限，则隐藏元素
            el.style.display = 'none';
        } else {
            // 如果有权限，确保元素可见（如果之前被隐藏的话）
            el.style.display = '';
        }
    } else {
        // 如果指令使用不正确，记录错误而不是抛出异常
        console.error(`需要操作字符串，如 v-action="'module.controller.action'"`);
    }
}

export default {
    mounted(el: { style: { display: string; }; }, binding: { value: any; }) {
        checkAction(el, binding);
    },

    updated(el: { style: { display: string; }; }, binding: { value: any; }) {
        checkAction(el, binding);
    },
};
