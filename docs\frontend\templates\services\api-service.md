# API 服务模板

## 概述

API 服务用于封装前端与后端的接口交互逻辑。本文档提供了 API 服务的标准模板和最佳实践。

## 基本结构

### 请求配置

```typescript
// services/request.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const userStore = useUserStore()
    
    // 添加 token
    if (userStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${userStore.token}`
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data
    
    // 处理业务错���
    if (code !== 0) {
      ElMessage.error(message)
      return Promise.reject(new Error(message))
    }
    
    return data
  },
  (error) => {
    // 处理 HTTP 错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 处理未授权
          handleUnauthorized()
          break
        case 403:
          // 处理权限不足
          ElMessage.error('权限不足')
          break
        case 404:
          // 处理资源不存在
          ElMessage.error('资源不存在')
          break
        case 500:
          // 处理服务器错误
          ElMessage.error('服务器错误')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      // 处理请求超时
      ElMessage.error('请求超时')
    } else {
      // 处理其他错误
      ElMessage.error('请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 处理未授权
const handleUnauthorized = () => {
  const userStore = useUserStore()
  userStore.logout()
  window.location.href = '/login'
}

export default request
```

### API 服务类

```typescript
// services/api.ts
import request from './request'
import type { ListParams, ListResponse, DetailResponse } from '@/types'

export default class Api {
  /**
   * 获取列表数据
   */
  static async getList(params: ListParams): Promise<ListResponse> {
    return request.get('/api/list', { params })
  }
  
  /**
   * 获取详情数据
   */
  static async getDetail(id: number): Promise<DetailResponse> {
    return request.get(`/api/detail/${id}`)
  }
  
  /**
   * 创建数据
   */
  static async create(data: any): Promise<any> {
    return request.post('/api/create', data)
  }
  
  /**
   * 更新数据
   */
  static async update(id: number, data: any): Promise<any> {
    return request.put(`/api/update/${id}`, data)
  }
  
  /**
   * 删除数据
   */
  static async delete(id: number): Promise<any> {
    return request.delete(`/api/delete/${id}`)
  }
  
  /**
   * 批量删除
   */
  static async batchDelete(ids: number[]): Promise<any> {
    return request.post('/api/batch-delete', { ids })
  }
  
  /**
   * 导出数据
   */
  static async export(params: any): Promise<Blob> {
    return request.get('/api/export', {
      params,
      responseType: 'blob'
    })
  }
  
  /**
   * 上传文件
   */
  static async upload(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
```

## 规范要求

1. 请求配置
   - 统一的基础配置
   - 请求/响应拦截
   - 错误处理
   - 超时处理

2. 接口封装
   - 统一的接口定义
   - 类型声明
   - 参数处理
   - 响应处理

3. 错误处理
   - HTTP 错误
   - 业务错误
   - 网络错误
   - 超时处理

4. 安全处理
   - Token 管理
   - 权限控制
   - 数据加密
   - 防止重复请求

## 最佳实践

1. 取消请求
```typescript
// 创建取消令牌
const CancelToken = axios.CancelToken
const source = CancelToken.source()

// 发送请求
request.get('/api/data', {
  cancelToken: source.token
})

// 取消请求
source.cancel('请求已取消')

// 在组件中使用
const loadData = async () => {
  try {
    loading.value = true
    const source = CancelToken.source()
    
    // 保存取消令牌
    cancelTokens.push(source)
    
    const response = await request.get('/api/data', {
      cancelToken: source.token
    })
    
    return response.data
  } finally {
    loading.value = false
  }
}

// 组件卸载时取消请求
onBeforeUnmount(() => {
  cancelTokens.forEach(source => source.cancel())
})
```

2. 请求重试
```typescript
// 创建重试拦截器
const retryInterceptor = (axios: AxiosInstance) => {
  axios.interceptors.response.use(
    response => response,
    async error => {
      const config = error.config
      
      // 设置重试次数和延迟
      config.retryCount = config.retryCount || 0
      const retryDelay = config.retryDelay || 1000
      const maxRetries = config.maxRetries || 3
      
      if (config.retryCount < maxRetries) {
        config.retryCount++
        
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        
        return axios(config)
      }
      
      return Promise.reject(error)
    }
  )
}
```

## 常见问题

1. 请求缓存
```typescript
// 实现请求缓存
const cache = new Map()

const cacheRequest = async (url: string, config: AxiosRequestConfig) => {
  const key = `${url}${JSON.stringify(config)}`
  
  // 检查缓存
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  // 发送请求
  const response = await request.get(url, config)
  
  // 缓存响应
  cache.set(key, response)
  
  return response
}

// 清除缓存
const clearCache = () => {
  cache.clear()
}
```

2. 并发请求
```typescript
// 并发请求处理
const loadData = async () => {
  try {
    loading.value = true
    
    const [
      listResponse,
      countResponse,
      optionsResponse
    ] = await Promise.all([
      Api.getList(params),
      Api.getCount(params),
      Api.getOptions()
    ])
    
    return {
      list: listResponse.data,
      total: countResponse.data,
      options: optionsResponse.data
    }
  } finally {
    loading.value = false
  }
}
```

## 注意事项

1. 统一的错误处理
2. 请求超时设置
3. 取消重复请求
4. 实现请求重试
5. 处理并发请求
6. 注意性能优化
