import { Node } from '@tiptap/core'

export const HtmlBlock = Node.create({
  name: 'htmlBlock',
  group: 'block',
  content: 'inline*',
  
  addAttributes() {
    return {
      html: {
        default: '',
      },
    }
  },
  
  parseHTML() {
    return [
      { tag: 'div[data-html-block]' },
    ]
  },
  
  renderHTML({ HTMLAttributes, node }) {
    return ['div', { 'data-html-block': '', 'innerHTML': node.attrs.html }, 0]
  },
  
  addNodeView() {
    return ({ node }) => {
      const dom = document.createElement('div')
      dom.setAttribute('data-html-block', '')
      dom.innerHTML = node.attrs.html
      
      return {
        dom,
        contentDOM: dom,
      }
    }
  },
}) 