<template>
  <div class="table-page bwms-module">
    <!-- 顶部导航栏 -->
    <div class="module-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon size="16"><ArrowLeft /></el-icon>
        <span>返回</span>
      </el-button>
      <el-button @click="saveFaq" type="primary" class="save-btn" :loading="saveLoading">
        <el-icon size="16"><Check /></el-icon>
        <span>保存</span>
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="module-con">
      <div class="content-layout">
        <!-- 左侧FAQ内容编辑区 -->
        <div class="content-left">
          <div class="box scroll-bar-custom">
            <el-tabs v-model="activeLanguage" class="demo-tabs" @tab-change="handleLanguageChange">
              <!-- 繁體中文 -->
              <el-tab-pane name="zh_HK">
                <template #label>
                  <span class="tabs-tit">繁體中文</span>
                </template>
                <div v-if="contentLoading" class="loading-container">
                  <el-skeleton :rows="10" animated />
                </div>
                <FormComp
                  v-else
                  ref="formRef"
                  :content="contentList['zh_HK']"
                  :lang="'zh_HK'"
                  :hidePageSettings="true"
                  :statusList="statusList"
                />
              </el-tab-pane>

              <!-- 簡體中文 -->
              <el-tab-pane name="zh_CN">
                <template #label>
                  <span class="tabs-tit">簡體中文</span>
                </template>
                <div v-if="contentLoading" class="loading-container">
                  <el-skeleton :rows="10" animated />
                </div>
                <FormComp
                  v-else
                  ref="formRef"
                  :content="contentList['zh_CN']"
                  :lang="'zh_CN'"
                  :hidePageSettings="true"
                  :statusList="statusList"
                />
              </el-tab-pane>

              <!-- 英文 -->
              <el-tab-pane name="en">
                <template #label>
                  <span class="tabs-tit">英文</span>
                </template>
                <div v-if="contentLoading" class="loading-container">
                  <el-skeleton :rows="10" animated />
                </div>
                <FormComp
                  v-else
                  ref="formRef"
                  :content="contentList['en']"
                  :lang="'en'"
                  :hidePageSettings="true"
                  :statusList="statusList"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 右侧设置面板 -->
        <div class="settings-panel">
          <div class="settings-tabs">
            <el-tabs v-model="activeSettingsTab" class="settings-tabs">
              <!-- 基本設定 -->
              <el-tab-pane name="basic">
                <template #label>
                  <el-icon><House /></el-icon>
                  <span>基本設定</span>
                </template>
                <div class="settings-content">
                  <el-form :model="settingsForm" label-width="auto" label-position="top">
                    <el-form-item label="文章狀態">
                      <el-select v-model="settingsForm.status" placeholder="請選擇狀態" style="width: 100%">
                        <el-option
                          v-for="item in statusList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>

                    <el-form-item label="標籤">
                      <div class="tags-container">
                        <el-tag
                          v-for="(tag, index) in settingsForm.tags"
                          :key="index"
                          closable
                          @close="removeTag(index)"
                          class="tag-item"
                        >
                          {{ tag }}
                        </el-tag>
                        <div class="flex" style="gap: 16px;">
                          <el-input @click="showTagInput" placeholder="輸入標籤" ></el-input>
                          <el-button @click="showTagInput" style="height: 40px;border-color: var(--input-border-color);">
                            新增標籤
                          </el-button>
                        </div>
                      </div>
                    </el-form-item>

                    <el-form-item  label-position="left">
                      <div class="flex justify-between items-center" style="width: 100%;">
                        <label class="el-form-item__label" style="margin: 0;">地理限制</label>
                        <el-switch v-model="settingsForm.geoRestriction" />
                      </div>
                    </el-form-item>

                    <el-form-item label="分類">
                      <el-select v-model="settingsForm.category" placeholder="請選擇分類" style="width: 100%">
                        <el-option
                          v-for="item in categoryList"
                          :key="item.id"
                          :label="item.title"
                          :value="item.id"
                        />
                      </el-select>
                    </el-form-item>

                    <el-form-item label="優先級">
                      <el-select v-model="settingsForm.priority" placeholder="請選擇優先級" style="width: 100%">
                        <el-option label="高" value="high" />
                        <el-option label="普通" value="normal" />
                        <el-option label="低" value="low" />
                      </el-select>
                    </el-form-item>

                    <el-form-item label="發布日期">
                      <el-date-picker
                        v-model="settingsForm.publishDate"
                        type="datetime"
                        placeholder="年/月/日 --:--"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- SEO設定 -->
              <el-tab-pane name="seo">
                <template #label>
                  <el-icon><Setting /></el-icon>
                  <span>SEO設定</span>
                </template>
                <div class="settings-content">
                  <el-form :model="seoForm" label-width="auto" label-position="top">
                    <el-form-item label="SEO標題">
                      <el-input v-model="seoForm.seoTitle" placeholder="輸入SEO標題" />
                    </el-form-item>

                    <el-form-item label="SEO描述">
                      <el-input
                        v-model="seoForm.seoDescription"
                        type="textarea"
                        :rows="4"
                        placeholder="輸入SEO描述"
                      />
                    </el-form-item>

                    <el-form-item label="關鍵詞">
                      <el-input v-model="seoForm.keywords" placeholder="輸入關鍵詞,用逗號分隔" />
                    </el-form-item>

                    <el-form-item label="規範URL">
                      <el-input v-model="seoForm.canonicalUrl" placeholder="輸入規範URL" />
                    </el-form-item>

                    <el-form-item  label-position="left">
                      <div class="flex justify-between items-center" style="width: 100%;">
                        <label class="el-form-item__label" style="margin: 0;">允許索引</label>
                        <el-switch v-model="seoForm.allowIndexing" />
                      </div>
                    </el-form-item>

                    <el-form-item  label-position="left">
                      <div class="flex justify-between items-center" style="width: 100%;">
                        <label class="el-form-item__label" style="margin: 0;">跟隨連結</label>
                        <el-switch v-model="seoForm.followLinks" />
                      </div>
                    </el-form-item>

                    <el-form-item label="Open Graph標題">
                      <el-input v-model="seoForm.ogTitle" placeholder="輸入OG標題" />
                    </el-form-item>

                    <el-form-item label="Open Graph描述">
                      <el-input
                        v-model="seoForm.ogDescription"
                        type="textarea"
                        :rows="4"
                        placeholder="輸入OG描述"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import http from '/admin/support/http'
import {
  ArrowLeft,
  Check,
  Plus,
  Delete,
  List,
  Document,
  Picture,
  View,
  House,
  Setting
} from '@element-plus/icons-vue'

// 导入表单组件
import FormComp from './components/questionForm.vue'

const router = useRouter()

// 设置表单数据
const settingsForm = reactive({
  status: 3,
  tags: ['天氣', '香港', '天文台'],
  geoRestriction: false,
  category: '',
  priority: 'normal',
  publishDate: ''
})

// SEO表单数据
const seoForm = reactive({
  seoTitle: '',
  seoDescription: '',
  keywords: '',
  canonicalUrl: '',
  allowIndexing: true,
  followLinks: true,
  ogTitle: '',
  ogDescription: ''
})

// 内容列表
const contentList = ref<{ [key: string]: any }>({
  zh_HK: {},
  zh_CN: {},
  en: {}
})

// 栏目列表
const categoryList = ref([])
const statusList = ref([
  { label: '草稿', value: 3 },
  { label: '已發佈', value: 2 },
  { label: '定時發佈', value: 1 }
])

// 活动标签页
const activeLanguage = ref('zh_HK')
const activeSettingsTab = ref('basic')
const contentLoading = ref(false)

// 保存加载状态
const saveLoading = ref(false)

// 表单引用
const formRef = ref<any>(null)

// 语言切换处理
const handleLanguageChange = (tabName: string) => {
  activeLanguage.value = tabName
  console.log('切换到:', tabName)
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 移除标签
const removeTag = (index: number) => {
  settingsForm.tags.splice(index, 1)
}

// 显示标签输入
const showTagInput = () => {
  ElMessage.info('標籤功能開發中')
}

// 保存FAQ
const saveFaq = async () => {
  saveLoading.value = true

  try {
    // 获取所有语言的内容
    const formRefs = Array.isArray(formRef.value) ?
      formRef.value :
      Object.values(formRef.value || {}).filter(Boolean)

    const contents = await Promise.all(formRefs.map((item: any) => item.saveHandle()))

    // 合并数据
    const data: Record<string, any> = {}

    contents.forEach((item: any) => {
      const hasTitle = !!item.title?.trim()
      const hasSummary = !!item.summary?.trim()
      const hasContent = !!item.content?.trim()

      if (hasTitle && hasSummary && hasContent) {
        data[item.lang] = {
          ...item
        }
      }
    })

    if (Object.keys(data).length === 0) {
      ElMessage.error('請至少填寫一個語言的完整內容')
      return
    }

    // 合并设置数据
    const finalData = {
      ...data,
      settings: {
        ...settingsForm,
        seo: seoForm
      }
    }

    // 调用保存接口
    const response = await http.post('/admin/faq', finalData)

    if (response.data && response.data.code === 200) {
      ElMessage.success('保存成功')
      router.go(-1)
    } else {
      ElMessage.error(response.data?.message || '保存失敗')
    }
  } catch (error) {
    console.error('保存FAQ失败:', error)
    ElMessage.error('保存失敗')
  } finally {
    saveLoading.value = false
  }
}

// 获取栏目列表
const getCategoryList = async () => {
  try {
    const response = await http.get('/admin/faq/categories')
    if (response.data && response.data.code === 200) {
      categoryList.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取栏目列表失败:', error)
  }
}

onMounted(() => {
  // getCategoryList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 0;
    }
  }
}

.content-layout {
  display: flex;
  gap: 26px;
  height: 100%;
}

.content-left {
  flex: 1;
  min-width: 0;

  .box {
    height: 100%;
    overflow-y: auto;
  }
}

.settings-panel {
  width: 400px;
  background: #fff;
  border-radius: 10px;
  padding: 0 5px 20px 15px;
  .settings-tabs {
    height: 100%;

    :deep(.el-tabs__content) {
      height: calc(100% - 60px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(189,216,248, 1);
        border-radius: 3px;

        &:hover {
          background-color: rgba(189,216,248, 1);
        }
      }
    }

    :deep(.el-tabs__item) {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        margin-right: 4px;
      }
    }
  }

  .settings-content {

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 8px;

      .tag-item {
        margin: 0;
      }

      .add-tag-btn {
        margin: 0;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
      }
    }
  }
}

.language-tabs {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}


.loading-container {
  padding: 20px;
}
</style>
