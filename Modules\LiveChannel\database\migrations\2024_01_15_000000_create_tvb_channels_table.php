<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('channels', function (Blueprint $table) {
            $table->id()->comment('频道ID');
            $table->string('channel_num', 20)->nullable()->comment('频道标识 CH + 000');
            $table->string('name', 100)->comment('频道名称');
            $table->string('name_hk', 100)->nullable()->comment('繁体名称');
            $table->text('description')->nullable()->comment('频道描述');
            $table->text('description_hk')->nullable()->comment('频道描述');
            $table->string('cover_image_url', 255)->nullable()->comment('封面图片URL');
            $table->string('stream_url', 500)->nullable()->comment('直播流地址');
            $table->string('stream_key', 500)->nullable()->comment('直播key');
            $table->timestamp('start_time')->nullable()->comment('直播开始时间');
            $table->timestamp('end_time')->nullable()->comment('直播结束时间');
            $table->tinyInteger('is_audio_only')->default(0)->comment('是否仅音频：0-否 1-是');
            $table->tinyInteger('is_breaking_news')->default(0)->comment('是否突发直播：0-否 1-是');
            $table->tinyInteger('is_hk_only')->default(0)->comment('是否仅限香港地区：0-否 1-是');
            $table->tinyInteger('live_status')->default(0)->comment('直播状态：0-关闭 1-直播中 2-暂停');
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用 1-启用');
            $table->integer('sort')->default(0)->comment('排序');
            $table->unsignedBigInteger('created_by')->comment('创建人');
            $table->unsignedBigInteger('updated_by')->comment('更新人');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->unique('name', 'uq_name');
            $table->index('status', 'idx_status');
            $table->index('start_time', 'idx_start_time');
            $table->index('end_time', 'idx_end_time');
            $table->index('created_at', 'idx_created_at');
            $table->index('live_status', 'idx_live_status');
            $table->index('channel_num', 'idx_channel_num');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('channels');
    }
}; 