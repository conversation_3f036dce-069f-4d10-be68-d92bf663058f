<?php

namespace Modules\Common\Job;

use Bingo\Core\Job\BaseJob;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\View;
use Modules\Common\Log\Logger;
use Modules\Common\Provider\MailSender\MailSenderProvider;

class MailSendJob extends BaseJob
{
    public string $email;
    public string $subject;
    public string $template;
    public array $templateData = [];
    public ?string $emailUserName = null;
    public array $option = [];
    public string $module;
    public string $html;
    public string $type;


    /**
     * 创建模板邮件
     * @param string $email 收件人
     * @param int|string $template 模板ID/调用名称
     * @param string|null $emailUserName 收件人名稱
     * @param array $option 模版替換數據
     * @param int $delay 延遲時間
     * @return void
     */
    public static function createTemplate(
        string $email,
        int|string $template,
        string $emailUserName = null,
        array  $option = [],
        int $delay = 0
    ): void {
        $job = new static();
        $job->type = 'template';
        $job->email = $email;
        $job->template = $template;
        $job->emailUserName = $emailUserName;
        $job->option = $option;
        if ($delay > 0) {
            $job->delay($delay);
        }
        app('Illuminate\Contracts\Bus\Dispatcher')->dispatch($job);
    }

    /**
     * 創建html郵件
     * @param string $email 收件人
     * @param string $subject 主題
     * @param string $html 郵件內容(html格式)
     * @param ?string $emailUserName 收件人名稱
     * @param array $option 其他選項
     * @param int $delay 延遲時間
     */
    public static function createHtml(
        string $email,
        string $subject,
        string $html,
        string $emailUserName = null,
        array $option = [],
        int $delay = 0
    ): void {
        $job = new static();
        $job->type = 'html';
        $job->email = $email;
        $job->subject = $subject;
        $job->html = $html;
        $job->emailUserName = $emailUserName;
        $job->option = $option;
        if ($delay > 0) {
            $job->delay($delay);
        }
        app('Illuminate\Contracts\Bus\Dispatcher')->dispatch($job);
    }

    /**
     * 創建模塊自定義郵件
     * @param string $module 模塊名稱
     * @param string $template 視圖名稱
     * @param string $email 收件人
     * @param string $subject 主題
     * @param array $templateData 視圖數據
     * @param ?string $emailUserName 收件人名稱
     * @param array $option 其他選項
     * @param int $delay 延遲時間
     */
    public static function createModule(
        string $module,
        string $template,
        string $email,
        string $subject,
        array  $templateData = [],
        string $emailUserName = null,
        array  $option = [],
        int $delay = 0
    ): void {
        self::create($email, $subject, $template, $templateData, $emailUserName, $option, $delay, $module);
    }

    /**
     * 創建視圖郵件
     * @param string $email 收件人
     * @param string $subject 主題
     * @param string $template 視圖名稱
     * @param array $templateData 視圖數據
     * @param ?string $emailUserName 收件人名稱
     * @param array $option 其他選項
     * @param int $delay 延遲時間
     * @param ?string $module 模塊名稱
     */
    public static function create(
        string $email,
        string $subject,
        string $template,
        array $templateData = [],
        string $emailUserName = null,
        array $option = [],
        int    $delay = 0,
        string $module = null
    ): void {
        $job = new static();
        $job->email = $email;
        $job->subject = $subject;
        $job->template = $template;
        $job->templateData = $templateData;
        $job->emailUserName = $emailUserName;
        $job->option = $option;
        $job->module = $module;
        if ($delay > 0) {
            $job->delay($delay);
        }
        app('Illuminate\Contracts\Bus\Dispatcher')->dispatch($job);
    }

    public function handle(): void
    {
        $provider = bingostart_config()->get('EmailSenderProvider', 'zh-CN', 'edm-email-sender');

        $instance = MailSenderProvider::get($provider);

        Logger::info('Email', 'Start', $this->email);

        switch ($this->type) {
            case 'html':
                $html = $this->html;
                break;
            case 'template':
                $template = (new \Modules\Edm\Models\Template());
                $templateInfo = $template->find($this->template) ?? $template->where('invoke', $this->template)->first();

                if (empty($templateInfo)) {
                    BizException::throws(Code::FAILED, 'mail template not found : '.$this->template);
                }

                $this->subject = $templateInfo->subject;
                $html = self::replaceTemplateFields($templateInfo->content, $this->option);
                break;
            default:
                $view = $this->template;
                if (! view()->exists($view)) {
                    $view = 'theme.default.mail.'.$this->template;
                    if (! view()->exists($view)) {
                        if ($this->module) {
                            $view = 'module::'.$this->module.'.views.mail.'.$this->template;
                        }
                        if (! view()->exists($view)) {
                            $view = 'module::Common.views.mail.'.$this->template;
                        }
                    }
                }
                if (! view()->exists($view)) {
                    BizException::throws(Code::FAILED, 'mail view not found : '.$view);
                }
                if (null === $this->emailUserName) {
                    $this->emailUserName = $this->email;
                }

                $html = View::make($view, $this->templateData)->render();
                break;
        }

        $instance->send($this->email, $this->subject, $html, $this->emailUserName);
        Logger::info('Email', 'End', $this->email.' - '.$this->subject);
    }

    /**
     * 替换HTML模板中的占位符。
     *
     * @param string $template HTML模板内容
     * @param array $data 需要替换的数据，格式为['field' => 'value']
     * @return string 替换后的HTML内容
     */
    private static function replaceTemplateFields(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            // 构造占位符格式
            $placeholder = '%'.$key.'%';
            // 替换占位符
            $template = str_replace($placeholder, $value, $template);
        }
        return $template;
    }
}
