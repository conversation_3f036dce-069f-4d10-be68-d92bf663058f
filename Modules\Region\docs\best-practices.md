# Region 模块最佳实践指南

## 📋 目录

1. [项目结构](#项目结构)
2. [多语言支持](#多语言支持)
3. [代码规范](#代码规范)
4. [API设计规范](#api设计规范)
5. [错误处理](#错误处理)
6. [数据库设计](#数据库设计)
7. [测试规范](#测试规范)
8. [部署指南](#部署指南)

## 🏗️ 项目结构

### 标准模块目录结构

```
Modules/Region/
├── Api/                          # API层
│   ├── Controllers/              # 控制器
│   │   ├── RegionController.php
│   │   └── ChannelController.php
│   └── route.php                 # 路由定义
├── Services/                     # 服务层
│   ├── RegionService.php
│   └── ChannelService.php
├── Models/                       # 模型层
│   ├── Region.php
│   ├── Channel.php
│   └── RegionChannel.php
├── Enums/                        # 枚举定义
│   ├── RegionErrorCode.php
│   └── ChannelErrorCode.php
├── Middleware/                   # 中间件（共享Common模块）
├── Lang/                         # 多语言文件
│   ├── en/
│   │   └── region.php
│   ├── zh_CN/
│   │   └── region.php
│   └── zh_HK/
│       └── region.php
├── database/                     # 数据库迁移
│   └── migrations/
├── config/                       # 配置文件
│   └── config.json
├── docs/                         # 文档
│   ├── best-practices.md
│   └── api-examples.md
├── composer.json                 # 模块依赖
└── package.json                  # 前端依赖
```

### 核心文件说明

| 文件类型 | 职责 | 示例文件 |
|---------|------|----------|
| **Controller** | 处理HTTP请求/响应 | `RegionController.php` |
| **Service** | 业务逻辑处理 | `RegionService.php` |
| **Model** | 数据访问和关系 | `Region.php` |
| **Enum** | 错误码定义 | `RegionErrorCode.php` |
| **Middleware** | 请求预处理 | `LanguageMiddleware.php` |
| **Lang** | 多语言翻译 | `Lang/zh_CN/region.php` |

## 🌐 多语言支持

### 1. 语言文件结构

```php
// Modules/Region/Lang/zh_CN/region.php
return [
    // 通用消息
    'success' => '操作成功',
    'create_success' => '创建成功',
    'update_success' => '更新成功',
    'delete_success' => '删除成功',
    
    // 字段名称
    'name' => '名称',
    'description' => '描述',
    'status' => '状态',
    'sort' => '排序',
    
    // 验证消息
    'name_required' => '名称不能为空',
    'name_max' => '名称不能超过:max个字符',
    'status_invalid' => '状态值无效',
    
    // 错误消息
    'region_not_found' => '区域不存在',
    'create_failed' => '创建失败',
    'update_failed' => '更新失败',
    'delete_failed' => '删除失败',
    
    // 批量操作
    'batch_success' => '批量操作成功',
    'batch_delete_success' => '批量删除成功',
    'batch_enable_success' => '批量启用成功',
    'batch_disable_success' => '批量禁用成功',
];
```

### 2. 中间件配置

```php
// Modules/Common/Middleware/LanguageMiddleware.php
public function handle(Request $request, Closure $next)
{
    // 获取语言参数
    $lang = $request->get('lang', 'zh_cn');
    
    // 验证语言是否支持
    if (isset($this->supportedLanguages[$lang])) {
        $locale = $this->supportedLanguages[$lang];
        App::setLocale($locale);
    } else {
        // 默认使用中文简体
        App::setLocale('zh_CN');
    }

    return $next($request);
}
```

### 3. 路由配置

```php
// Modules/Region/Api/route.php
use Modules\Common\Middleware\LanguageMiddleware;

Route::group(['prefix' => 'api'], function () {
    Route::group(['prefix' => 'region'], function () {
        Route::middleware([LanguageMiddleware::class])->group(function () {
            Route::get('/', [RegionController::class, 'index']);
            Route::post('/', [RegionController::class, 'store']);
            Route::get('/{id}', [RegionController::class, 'show']);
            Route::put('/{id}', [RegionController::class, 'update']);
            Route::delete('/{id}', [RegionController::class, 'destroy']);
            Route::patch('/{id}/status', [RegionController::class, 'updateStatus']);
            Route::post('/batch', [RegionController::class, 'batchAction']);
        });
    });
});
```

### 4. 控制器中使用

```php
// 成功响应
return [
    'code' => 200,
    'message' => Lang::get('Region::region.success'),
    'data' => $data
];

// 错误响应
return [
    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
    'message' => Lang::get('Region::region.region_not_found'),
    'data' => [
        'error' => $e->getMessage()
    ]
];

// 验证错误
return [
    'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
    'message' => Lang::get('Region::region.validation_failed'),
    'data' => [
        'errors' => $e->errors()
    ]
];
```

## 📝 代码规范

### 1. 命名规范

| 类型 | 规范 | 示例 |
|------|------|------|
| **类名** | PascalCase | `RegionController` |
| **方法名** | camelCase | `getRegionList()` |
| **变量名** | camelCase | `$regionList` |
| **常量名** | UPPER_SNAKE_CASE | `REGION_NOT_FOUND` |
| **文件名** | PascalCase.php | `RegionController.php` |

### 2. 错误码规范

```php
// Modules/Region/Enums/RegionErrorCode.php
enum RegionErrorCode: int
{
    // 15xxx - Region模块错误码
    case REGION_NOT_FOUND = 15001;
    case REGION_CREATE_FAILED = 15002;
    case REGION_UPDATE_FAILED = 15003;
    case REGION_DELETE_FAILED = 15004;
    case REGION_VALIDATION_FAILED = 15005;
    case REGION_STATUS_CHANGE_FAILED = 15006;
    case REGION_LIST_FAILED = 15007;
    
    public function message(): string
    {
        return match($this) {
            self::REGION_NOT_FOUND => 'Region not found',
            self::REGION_CREATE_FAILED => 'Failed to create region',
            // ...
        };
    }
    
    public function httpCode(): int
    {
        return match($this) {
            self::REGION_NOT_FOUND => 404,
            self::REGION_CREATE_FAILED => 500,
            // ...
        };
    }
}
```

### 3. 服务层规范

```php
class RegionService
{
    /**
     * 获取区域列表（包含频道数量）
     * @param array $params 查询参数
     * @return array 标准化的响应数据
     */
    public function getRegionListWithChannelCount(array $params): array
    {
        $query = Region::query();
        
        // 搜索条件
        if (!empty($params['name'])) {
            $query->byName($params['name']);
        }
        
        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        // 排序
        $sort = $params['sort'] ?? 'id';
        $order = $params['order'] ?? 'desc';
        $query->orderBy($sort, $order);
        
        // 分页
        $perPage = $params['per_page'] ?? 15;
        $regions = $query->paginate($perPage);
        
        // 业务逻辑：添加频道数量
        $items = $regions->getCollection()->map(function ($region) {
            $regionData = $region->toArray();
            $regionData['channel_num'] = $region->channels()->count();
            return $regionData;
        })->toArray();
        
        return [
            'total' => $regions->total(),
            'page' => $regions->currentPage(),
            'limit' => $regions->perPage(),
            'items' => $items
        ];
    }
}
```

## 🚀 API设计规范

### 1. 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 2. 错误响应格式

```json
{
  "code": 15001,
  "message": "区域不存在",
  "data": {
    "error": "详细错误信息"
  }
}
```

### 3. 验证错误格式

```json
{
  "code": 15005,
  "message": "验证失败",
  "data": {
    "errors": {
      "name": ["名称不能为空"],
      "status": ["状态值无效"]
    }
  }
}
```

### 4. 分页响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "items": [
      {
        "id": 1,
        "name": "区域名称",
        "channel_num": 5
      }
    ]
  }
}
```

## ⚠️ 错误处理

### 1. 异常处理规范

```php
try {
    // 业务逻辑
    $result = $this->regionService->createRegion($validated);
    
    return [
        'code' => 200,
        'message' => Lang::get('Region::region.create_success'),
        'data' => $result->toArray()
    ];
} catch (ValidationException $e) {
    return [
        'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
        'message' => Lang::get('Region::region.validation_failed'),
        'data' => [
            'errors' => $e->errors()
        ]
    ];
} catch (Exception $e) {
    return [
        'code' => RegionErrorCode::REGION_CREATE_FAILED->value,
        'message' => Lang::get('Region::region.create_failed'),
        'data' => [
            'error' => $e->getMessage()
        ]
    ];
}
```

### 2. 验证规则规范

```php
$validated = $request->validate([
    'name' => 'required|string|max:100|unique:regions,name,NULL,id,deleted_at,NULL',
    'description' => 'nullable|string|max:500',
    'status' => 'integer|in:0,1',
    'sort' => 'nullable|integer|min:0|max:9999'
], [
    'name.required' => Lang::get('Region::region.name_required'),
    'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
    'status.in' => Lang::get('Region::region.status_invalid'),
    'sort.integer' => Lang::get('Region::region.sort_invalid'),
]);
```

## 🗄️ 数据库设计

### 1. 迁移文件规范

```php
// database/migrations/2024_07_19_000000_create_tvb_regions_table.php
public function up()
{
    Schema::create('tvb_regions', function (Blueprint $table) {
        $table->id();
        $table->string('name', 100)->comment('区域名称');
        $table->text('description')->nullable()->comment('区域描述');
        $table->tinyInteger('status')->default(1)->comment('状态：1启用，0禁用');
        $table->integer('sort')->default(0)->comment('排序');
        $table->unsignedBigInteger('created_by')->comment('创建人ID');
        $table->unsignedBigInteger('updated_by')->comment('更新人ID');
        $table->timestamps();
        $table->softDeletes();
        
        $table->index(['status', 'sort']);
        $table->index('name');
    });
}
```

### 2. 模型关系规范

```php
class Region extends Model
{
    protected $table = 'tvb_regions';
    
    protected $fillable = [
        'name', 'description', 'status', 'sort', 
        'created_by', 'updated_by'
    ];
    
    protected $casts = [
        'status' => 'integer',
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
    
    // 关联关系
    public function channels()
    {
        return $this->belongsToMany(Channel::class, 'tvb_regions_channel', 'region_id', 'channel_id')
                    ->using(RegionChannel::class)
                    ->withTimestamps();
    }
    
    // 查询作用域
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }
    
    public function scopeDisabled($query)
    {
        return $query->where('status', 0);
    }
    
    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }
}
```

## 🧪 测试规范

### 1. 单元测试

```php
// tests/Unit/RegionServiceTest.php
class RegionServiceTest extends TestCase
{
    use RefreshDatabase;
    
    private RegionService $regionService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->regionService = app(RegionService::class);
    }
    
    public function test_can_create_region()
    {
        $data = [
            'name' => '测试区域',
            'description' => '测试描述',
            'status' => 1,
            'sort' => 1,
            'created_by' => 1,
            'updated_by' => 1
        ];
        
        $region = $this->regionService->createRegion($data);
        
        $this->assertInstanceOf(Region::class, $region);
        $this->assertEquals('测试区域', $region->name);
    }
}
```

### 2. 功能测试

```php
// tests/Feature/RegionApiTest.php
class RegionApiTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_can_get_region_list()
    {
        Region::factory()->count(3)->create();
        
        $response = $this->getJson('/api/region?lang=zh_cn');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'total',
                        'page',
                        'limit',
                        'items'
                    ]
                ]);
    }
}
```

## 🚀 部署指南

### 1. 模块注册

```php
// config/app.php
'providers' => [
    // ...
    Modules\Region\Providers\RegionServiceProvider::class,
],
```

### 2. 路由注册

```php
// app/Providers/RouteServiceProvider.php
Route::middleware('api')
    ->prefix('api')
    ->group(base_path('Modules/Region/Api/route.php'));
```

### 3. 语言文件注册

```php
// Modules/Region/Providers/RegionServiceProvider.php
public function boot()
{
    $this->loadTranslationsFrom(__DIR__ . '/../Lang', 'Region');
    $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
}
```

## 📋 检查清单

### 新模块开发检查清单

- [ ] 创建标准目录结构
- [ ] 配置多语言支持
- [ ] 定义错误码枚举
- [ ] 创建数据库迁移
- [ ] 实现Model和关系
- [ ] 编写Service层业务逻辑
- [ ] 实现Controller层
- [ ] 配置路由和中间件
- [ ] 编写API文档
- [ ] 添加单元测试
- [ ] 添加功能测试
- [ ] 配置模块注册

### 代码质量检查

- [ ] 遵循命名规范
- [ ] 添加完整注释
- [ ] 统一错误处理
- [ ] 实现多语言支持
- [ ] 统一响应格式
- [ ] 添加数据验证
- [ ] 实现软删除
- [ ] 添加查询作用域
- [ ] 配置模型关系
- [ ] 添加时间戳转换

## 🎯 最佳实践总结

1. **分层架构**：Controller → Service → Model
2. **职责分离**：每层只负责自己的职责
3. **统一规范**：命名、响应格式、错误处理
4. **多语言支持**：从项目开始就支持国际化
5. **错误处理**：使用枚举定义错误码
6. **数据验证**：在Controller层进行验证
7. **业务逻辑**：在Service层处理复杂业务
8. **代码复用**：通过Service层实现代码复用
9. **测试覆盖**：编写单元测试和功能测试
10. **文档完善**：及时更新API文档

这个最佳实践指南可以作为新模块开发的模板，确保代码质量和开发效率。 