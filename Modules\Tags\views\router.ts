import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/tags',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: '标签管理', icon: 'datareport' },
        children: [
            {
                path: 'list',
                name: 'TagsList',
                meta: { title: '标签列表' },
                component: () => import('./ui/list.vue'),
            }
        ]
    }
]

export default router
