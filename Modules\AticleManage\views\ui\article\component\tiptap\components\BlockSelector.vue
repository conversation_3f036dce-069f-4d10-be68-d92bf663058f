<template>
  <el-dialog v-model="dialogVisible" title="区块选择器" width="600px" :before-close="closeDialog" class="block-selector-dialog"  style="background-color: #fff; z-index: 1000" append-to-body>
    <div class="block-selector-content">
      <div class="block-category">
        <h4>表格布局</h4>
        <div class="block-items">
          <div class="block-item" @click="insertTable()">
            <div class="block-preview table-preview"></div>
            <span>基础表格</span>
          </div>
        </div>
      </div>

      <div class="block-category">
        <h4>页面布局</h4>
        <div class="block-items">
          <div class="block-item" @click="insertGridBlock('1')">
            <div class="block-preview single-column"></div>
            <span>单列布局</span>
          </div>
          <div class="block-item" @click="insertGridBlock('1:1')">
            <div class="block-preview two-columns"></div>
            <span>双列布局</span>
          </div>
          <div class="block-item" @click="insertGridBlock('1:1:1')">
            <div class="block-preview three-columns"></div>
            <span>三列布局</span>
          </div>
        </div>
      </div>

      <div class="block-category">
        <h4>内容元素</h4>
        <div class="block-items">
          <div class="block-item" @click="insertHeadlineBlock()">
            <div class="block-preview headline-preview"></div>
            <span>标题区块</span>
          </div>
          <div class="block-item" @click="insertImageTextBlock()">
            <div class="block-preview image-text-preview"></div>
            <span>图文组合</span>
          </div>
          <div class="block-item" @click="insertCTABlock()">
            <div class="block-preview cta-preview"></div>
            <span>号召性按钮</span>
          </div>
          <div class="block-item" @click="insertRichTextContent()">
            <div class="block-preview rich-text-preview"></div>
            <span>富文本内容</span>
          </div>
        </div>
      </div>

      <div class="block-category">
        <h4>Bootstrap 组件</h4>
        <div class="block-items">
          <div class="block-item" @click="insertBootstrapCard()">
            <div class="block-preview bootstrap-card-preview"></div>
            <span>Bootstrap 卡片</span>
          </div>
          <div class="block-item" @click="insertBootstrapAlert()">
            <div class="block-preview bootstrap-alert-preview"></div>
            <span>Bootstrap 提示框</span>
          </div>
          <div class="block-item" @click="insertBootstrapButtonGroup()">
            <div class="block-preview bootstrap-button-group-preview"></div>
            <span>按钮组</span>
          </div>
          <div class="block-item" @click="insertBootstrapForm()">
            <div class="block-preview bootstrap-form-preview"></div>
            <span>表单组件</span>
          </div>
          <div class="block-item" @click="insertBootstrapNav()">
            <div class="block-preview bootstrap-nav-preview"></div>
            <span>导航组件</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Editor } from '@tiptap/vue-3'
import { ElDialog } from 'element-plus'

const props = defineProps<{
  editor: Editor | null
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'block-selected', blockHtml?: string): void
  (e: 'close'): void
  (e: 'update:visible', value: boolean): void
}>()

// 使用computed计算属性连接visible属性和el-dialog的v-model
const dialogVisible = computed({
  get: () => props.visible,
  set: value => {
    emit('update:visible', value)
  },
})

// 当el-dialog关闭时触发
const closeDialog = () => {
  emit('close')
}

// 插入表格
const insertTable = () => {
  if (!props.editor) return

  props.editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入网格布局
const insertGridBlock = (layout: string) => {
  if (!props.editor) return

  props.editor.chain().focus().insertGridBlock({ layout }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入标题区块
const insertHeadlineBlock = () => {
  if (!props.editor) return

  props.editor
    .chain()
    .focus()
    .insertHeadlineBlock({
      title: '引人注目的标题',
      content: '详细说明文字，可以添加更多信息描述你的产品或服务。',
      align: 'center',
    })
    .run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入图文组合
const insertImageTextBlock = () => {
  if (!props.editor) return

  props.editor
    .chain()
    .focus()
    .insertImageTextBlock({
      title: '图文标题',
      content: '这里是描述文字，介绍你的产品或服务特点。可以添加更多内容来吸引读者的注意力。',
      imageUrl: 'https://via.placeholder.com/400x300',
      imageAlt: '示例图片',
      layout: 'image-left',
    })
    .run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入号召性按钮
const insertCTABlock = () => {
  if (!props.editor) return

  props.editor
    .chain()
    .focus()
    .insertCTABlock({
      title: '立即行动',
      content: '不要错过这个机会，点击下方按钮了解更多。',
      buttonLabel: '点击这里',
      buttonUrl: '#',
      buttonType: 'primary',
    })
    .run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入Bootstrap提示框
const insertBootstrapAlert = () => {
  if (!props.editor) return

  // 创建Bootstrap提示框的HTML结构
  const alertHtml = `
    <div class="alert alert-primary alert-dismissible fade show" role="alert" data-bs-component="alert">
      <strong>提示信息!</strong> 这是一个使用Bootstrap样式的提示框示例。
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  `

  // 使用自定义命令插入Bootstrap组件
  props.editor.chain().focus().insertBootstrapComponent({ html: alertHtml }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入Bootstrap卡片
const insertBootstrapCard = () => {
  if (!props.editor) return

  // 创建Bootstrap卡片的HTML结构
  const cardHtml = `
    <div class="card" style="width: 100%; margin-bottom: 1.5rem;" data-bs-component="card">
      <img src="https://via.placeholder.com/800x400" class="card-img-top" alt="卡片顶部图片">
      <div class="card-body">
        <h5 class="card-title">卡片标题</h5>
        <p class="card-text">这是一个使用Bootstrap样式的卡片示例。您可以在这里添加您的内容。</p>
        <a href="#" class="btn btn-primary">查看更多</a>
      </div>
    </div>
  `

  // 使用自定义命令插入Bootstrap组件
  props.editor.chain().focus().insertBootstrapComponent({ html: cardHtml }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入Bootstrap按钮组
const insertBootstrapButtonGroup = () => {
  if (!props.editor) return

  // 创建Bootstrap按钮组的HTML结构
  const buttonGroupHtml = `
    <div style="margin: 1.5rem 0;" data-bs-component="button-group">
      <div class="btn-group" role="group" aria-label="基础按钮组示例">
        <button type="button" class="btn btn-primary">左侧按钮</button>
        <button type="button" class="btn btn-secondary">中间按钮</button>
        <button type="button" class="btn btn-success">右侧按钮</button>
      </div>
      <div class="mt-3">
        <div class="btn-group-vertical" role="group" aria-label="垂直按钮组示例">
          <button type="button" style="margin-bottom: 10px;" class="btn btn-outline-primary">按钮 1</button>
          <button type="button" style="margin-bottom: 10px;" class="btn btn-outline-primary">按钮 2</button>
          <button type="button" style="margin-bottom: 10px;" class="btn btn-outline-primary">按钮 3</button>
        </div>
        <div class="btn-group-vertical" role="group" aria-label="垂直按钮组示例">
          <button type="button" style="background-color: red; color: #fff; font-size: 32px;" class="btn btn-outline-primary">按钮 1</button>
        </div>
      </div>
    </div>
  `

  // 使用自定义命令插入Bootstrap组件
  props.editor.chain().focus().insertBootstrapComponent({ html: buttonGroupHtml }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入Bootstrap表单
const insertBootstrapForm = () => {
  if (!props.editor) return

  // 创建Bootstrap表单的HTML结构
  const formHtml = `
    <div class="mb-4 card" data-bs-component="form">
      <div class="card-header">
        <h5 class="mb-0">联系表单</h5>
      </div>
      <div class="card-body">
        <form>
          <div class="mb-3">
            <label for="inputName" class="form-label">姓名</label>
            <input type="text" class="form-control" id="inputName" placeholder="请输入您的姓名">
          </div>
          <div class="mb-3 row">
            <div class="col">
              <label for="inputEmail" class="form-label">邮箱</label>
              <input type="email" class="form-control" id="inputEmail" placeholder="<EMAIL>">
            </div>
            <div class="col">
              <label for="inputPhone" class="form-label">电话</label>
              <input type="tel" class="form-control" id="inputPhone" placeholder="请输入您的电话">
            </div>
          </div>
          <div class="mb-3">
            <label for="selectSubject" class="form-label">主题</label>
            <select class="form-select" id="selectSubject">
              <option selected>请选择主题</option>
              <option value="1">产品咨询</option>
              <option value="2">技术支持</option>
              <option value="3">投诉建议</option>
              <option value="4">其他</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="textareaMessage" class="form-label">留言内容</label>
            <textarea class="form-control" id="textareaMessage" rows="4" placeholder="请输入您的留言内容"></textarea>
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="checkAgree">
            <label class="form-check-label" for="checkAgree">我同意接收后续更新</label>
          </div>
          <div class="gap-2 d-grid d-md-flex justify-content-md-end">
            <button class="btn btn-secondary me-md-2" type="reset">重置</button>
            <button class="btn btn-primary" type="submit">提交</button>
          </div>
        </form>
      </div>
    </div>
  `

  // 使用自定义命令插入Bootstrap组件
  props.editor.chain().focus().insertBootstrapComponent({ html: formHtml }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入Bootstrap导航组件
const insertBootstrapNav = () => {
  if (!props.editor) return

  // 创建Bootstrap导航组件的HTML结构
  const navHtml = `
    <div class="mb-4" data-bs-component="nav">
      <!-- 基础导航 -->
      <ul class="mb-3 nav nav-tabs">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="#">首页</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">产品</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">服务</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">关于我们</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">联系我们</a>
        </li>
      </ul>
      
      <!-- 带内容的标签页 -->
      <div class="tab-content">
        <div class="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
          <div class="p-3 rounded border bg-light">
            <h5>首页内容</h5>
            <p>这里是首页的内容描述，您可以替换成您需要展示的实际内容。</p>
          </div>
        </div>
      </div>
    </div>
  `

  // 使用自定义命令插入Bootstrap组件
  props.editor.chain().focus().insertBootstrapComponent({ html: navHtml }).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 插入富文本内容
const insertRichTextContent = () => {
  if (!props.editor) return

  // 创建一个示例富文本内容
  const richTextHtml = `
    <div class="rich-content" data-rich-content="true">
      <h2 style="font-family: 'Times New Roman', Georgia, '宋體'; font-weight: bold; font-size: 24px; color: #003366; margin-bottom: 20px;">CCSenior Launches Innovative Digital Platform for Hong Kong's Elderly</h2>
      <p style="font-family: Baskerville, '楷體'; font-size: 16px; line-height: 1.3; margin-bottom: 20px; color: #333333; text-align: left;">Hong Kong, [Date] – CCSenior, a pioneering initiative dedicated to enhancing the quality of life for elderly residents through technology, today announced the official launch of its all-in-one digital platform designed to bridge the digital divide for seniors. Combining AI-driven accessibility tools, multilingual support, and community-driven features, the platform aims to serve over 200,000 elderly users and caregivers across Hong Kong within its first year.</p>
      <h3 style="font-family: 'Times New Roman', Georgia, '宋體'; font-weight: bold; font-size: 20px; color: #003366; margin-top: 24px; margin-bottom: 16px;">Integrated Core Systems</h3>
      <p style="font-family: Baskerville, '楷體'; font-size: 16px; line-height: 1.3; margin-bottom: 16px; color: #333333; text-align: left;">Built in compliance with Hong Kong's PDPO and GDPR standards, the platform integrates four core systems:</p>
      <ul style="font-family: Baskerville, '楷體'; font-size: 16px; line-height: 1.3; margin-bottom: 20px; color: #333333; margin-left: 40px;">
        <li style="margin-bottom: 10px; color: #003366;">An AI-powered website with real-time Cantonese voice navigation</li>
        <li style="margin-bottom: 10px; color: #003366;">An encrypted external form submission portal for healthcare partners</li>
        <li style="margin-bottom: 10px; color: #003366;">A WhatsApp-based chatbot with dementia-friendly interaction modes</li>
        <li style="margin-bottom: 10px; color: #003366;">A CMS backend enabling seamless content updates for NGOs</li>
      </ul>
    </div>
  `

  // 将HTML直接插入到编辑器中
  props.editor.chain().focus().insertContent(richTextHtml).run()

  emit('block-selected')
  dialogVisible.value = false
}

// 为了让组件可以被导入为默认
defineExpose({
  insertTable,
  insertGridBlock,
  insertHeadlineBlock,
  insertImageTextBlock,
  insertCTABlock,
  insertBootstrapCard,
  insertBootstrapAlert,
  insertBootstrapButtonGroup,
  insertBootstrapForm,
  insertBootstrapNav,
  insertRichTextContent
})
</script>

<script lang="ts">
export default {
  name: 'BlockSelector'
}
</script>

<style scoped>
.block-selector-dialog {
  background-color: #fff;
  z-index: 1000;
}
.block-selector-content {
  max-height: 60vh;
  overflow-y: auto;
}

.block-category {
  margin-bottom: 20px;
}

.block-category h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
}

.block-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.block-item {
  width: 120px;
  cursor: pointer;
  text-align: center;
  transition: transform 0.2s;
}

.block-item:hover {
  transform: translateY(-3px);
}

.block-item:hover .block-preview {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.block-preview {
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  display: flex;
  transition: all 0.3s;
}

.table-preview {
  position: relative;
}

.table-preview:before {
  content: '';
  position: absolute;
  top: 20%;
  left: 15%;
  right: 15%;
  bottom: 20%;
  background: repeating-linear-gradient(90deg, #e4e7ed 0px, #e4e7ed 20px, transparent 20px, transparent 40px),
    repeating-linear-gradient(0deg, #e4e7ed 0px, #e4e7ed 15px, transparent 15px, transparent 30px);
  border: 1px solid #c0c4cc;
}

.single-column {
  padding: 10px;
}

.single-column:before {
  content: '';
  flex: 1;
  background-color: #e4e7ed;
  border-radius: 2px;
}

.two-columns {
  display: flex;
  padding: 10px;
  gap: 5px;
}

.two-columns:before,
.two-columns:after {
  content: '';
  flex: 1;
  background-color: #e4e7ed;
  border-radius: 2px;
}

.three-columns {
  display: flex;
  padding: 10px;
  gap: 5px;
}

.three-columns:before,
.three-columns:after {
  content: '';
  flex: 1;
  background-color: #e4e7ed;
  border-radius: 2px;
}

.three-columns:before {
  position: relative;
}

.three-columns:before:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 5px;
  background-color: #f5f7fa;
}

.headline-preview,
.image-text-preview,
.cta-preview,
.bootstrap-card-preview,
.bootstrap-alert-preview,
.bootstrap-button-group-preview,
.bootstrap-form-preview,
.bootstrap-nav-preview,
.rich-text-preview {
  position: relative;
}

.headline-preview:before {
  content: 'H';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: bold;
  color: #606266;
}

.image-text-preview {
  display: flex;
  padding: 10px;
  gap: 5px;
}

.image-text-preview:before,
.image-text-preview:after {
  content: '';
  border-radius: 2px;
}

.image-text-preview:before {
  flex: 4;
  background-color: #c0c4cc;
}

.image-text-preview:after {
  flex: 6;
  background-color: #e4e7ed;
}

.cta-preview {
  display: flex;
  flex-direction: column;
  padding: 10px;
  align-items: center;
  justify-content: center;
}

.cta-preview:before {
  content: 'CTA';
  background-color: #409eff;
  color: white;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
}

/* Bootstrap组件预览样式 */
.bootstrap-card-preview {
  display: flex;
  flex-direction: column;
  padding: 5px;
  align-items: center;
  justify-content: center;
}

.bootstrap-card-preview:before {
  content: '';
  position: absolute;
  top: 0;
  left: 5px;
  right: 5px;
  height: 30px;
  background-color: #c0c4cc;
  border-radius: 4px 4px 0 0;
}

.bootstrap-card-preview:after {
  content: '';
  position: absolute;
  top: 30px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  background-color: #f8f9fa;
  border-radius: 0 0 4px 4px;
  border: 1px solid #c0c4cc;
  border-top: none;
}

.bootstrap-alert-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.bootstrap-alert-preview:before {
  content: '';
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  bottom: 15px;
  background-color: #cfe2ff;
  border: 1px solid #b6d4fe;
  border-radius: 4px;
}

.bootstrap-alert-preview:after {
  content: '!';
  position: relative;
  z-index: 1;
  font-size: 24px;
  font-weight: bold;
  color: #084298;
}

/* 按钮组预览样式 */
.bootstrap-button-group-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.bootstrap-button-group-preview:before {
  content: '';
  position: absolute;
  top: 20px;
  left: 15px;
  right: 15px;
  height: 20px;
  display: flex;
  gap: 2px;
}

.bootstrap-button-group-preview:before {
  background: linear-gradient(to right, #0d6efd 32%, #6c757d 33%, #6c757d 65%, #198754 66%);
  border-radius: 4px;
}

.bootstrap-button-group-preview:after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 30px;
  right: 30px;
  height: 20px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  background: linear-gradient(to bottom, #0d6efd 32%, #0d6efd 33%, #0d6efd 65%, #0d6efd 66%);
  border: 1px solid #0d6efd;
  border-radius: 4px;
}

/* 表单预览样式 */
.bootstrap-form-preview {
  display: flex;
  flex-direction: column;
  padding: 5px;
  align-items: center;
  justify-content: center;
}

.bootstrap-form-preview:before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  height: 10px;
  background-color: #e9ecef;
  border-radius: 2px;
}

.bootstrap-form-preview:after {
  content: '';
  position: absolute;
  top: 25px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: 
    linear-gradient(to bottom, 
      #e9ecef 0%, #e9ecef 15%, 
      transparent 15%, transparent 20%, 
      #e9ecef 20%, #e9ecef 35%, 
      transparent 35%, transparent 40%,
      #e9ecef 40%, #e9ecef 55%, 
      transparent 55%, transparent 60%, 
      #e9ecef 60%, #e9ecef 80%,
      transparent 80%, transparent 85%,
      #0d6efd 85%, #0d6efd 100%);
  border: 1px solid #dee2e6;
  border-radius: 2px;
}

/* 导航预览样式 */
.bootstrap-nav-preview {
  display: flex;
  flex-direction: column;
  padding: 5px;
  align-items: center;
  justify-content: center;
}

.bootstrap-nav-preview:before {
  content: '';
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 20px;
  background: linear-gradient(to right,
    #0d6efd 0%, #0d6efd 18%, 
    #f8f9fa 18%, #f8f9fa 36%, 
    #f8f9fa 36%, #f8f9fa 54%, 
    #f8f9fa 54%, #f8f9fa 72%,
    #f8f9fa 72%, #f8f9fa 90%);
  border-radius: 4px 4px 0 0;
  border: 1px solid #dee2e6;
  border-bottom: none;
}

.bootstrap-nav-preview:after {
  content: '';
  position: absolute;
  top: 35px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 4px 4px;
}

/* 富文本预览样式 */
.rich-text-preview {
  display: flex;
  flex-direction: column;
  padding: 8px;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.rich-text-preview:before {
  content: 'Aa';
  font-family: serif;
  font-size: 20px;
  font-weight: bold;
  color: #003366;
}

.rich-text-preview:after {
  content: '';
  position: absolute;
  bottom: 15px;
  left: 10px;
  right: 10px;
  height: 40%;
  background: repeating-linear-gradient(0deg, #ced4da 0px, #ced4da 1px, transparent 1px, transparent 7px);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .block-items {
    justify-content: center;
  }
}
</style>
