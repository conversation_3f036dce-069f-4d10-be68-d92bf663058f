# Region 模块快速参考指南

## 📋 模块信息

- **模块名称**: Region
- **命名空间**: `Modules\Region`
- **错误码范围**: 15xxx (Region), 16xxx (Channel)
- **支持语言**: en, zh_CN, zh_HK

## 🏗️ 目录结构

```
Modules/Region/
├── Api/Controllers/
│   ├── RegionController.php
│   └── ChannelController.php
├── Services/
│   ├── RegionService.php
│   └── ChannelService.php
├── Models/
│   ├── Region.php
│   ├── Channel.php
│   └── RegionChannel.php
├── Enums/
│   ├── RegionErrorCode.php
│   └── ChannelErrorCode.php
├── Middleware/                   # 共享Common模块
├── Lang/
│   ├── en/region.php
│   ├── zh_CN/region.php
│   └── zh_HK/region.php
└── docs/
    ├── best-practices.md
    ├── module-template.md
    ├── quick-reference.md
    └── api-examples.md
```

## 🔗 API 路由

### Region API
- `GET /api/region` - 获取区域列表
- `POST /api/region` - 创建区域
- `GET /api/region/{id}` - 获取单个区域
- `PUT /api/region/{id}` - 更新区域
- `DELETE /api/region/{id}` - 删除区域
- `PATCH /api/region/{id}/status` - 更新区域状态
- `POST /api/region/batch` - 批量操作

### Channel API
- `GET /api/channel` - 获取频道列表
- `POST /api/channel` - 创建频道
- `GET /api/channel/{id}` - 获取单个频道
- `PUT /api/channel/{id}` - 更新频道
- `DELETE /api/channel/{id}` - 删除频道
- `PATCH /api/channel/{id}/status` - 更新频道状态
- `POST /api/channel/batch` - 批量操作
- `GET /api/channel/{id}/regions` - 获取频道关联的区域

## 🌐 多语言支持

### 语言参数
- `lang=en` - 英文
- `lang=zh_cn` - 中文简体
- `lang=zh_hk` - 中文繁体

### 使用示例
```bash
# 中文简体
curl -X GET "http://localhost:8001/api/region?lang=zh_cn"

# 中文繁体
curl -X GET "http://localhost:8001/api/region?lang=zh_hk"

# 英文
curl -X GET "http://localhost:8001/api/region?lang=en"
```

## 📊 数据库表

### tvb_regions
```sql
CREATE TABLE `tvb_regions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '区域名称',
  `description` text COMMENT '区域描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvb_regions_status_sort_index` (`status`,`sort`),
  KEY `tvb_regions_name_index` (`name`)
);
```

### tvb_channels
```sql
CREATE TABLE `tvb_channels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '频道名称',
  `description` text COMMENT '频道描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvb_channels_status_sort_index` (`status`,`sort`),
  KEY `tvb_channels_name_index` (`name`)
);
```

### tvb_regions_channel
```sql
CREATE TABLE `tvb_regions_channel` (
  `region_id` bigint unsigned NOT NULL,
  `channel_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`region_id`,`channel_id`),
  KEY `tvb_regions_channel_channel_id_foreign` (`channel_id`)
);
```

## 🔧 常用代码片段

### 1. 统一响应格式
```php
// 成功响应
return [
    'code' => 200,
    'message' => Lang::get('Region::region.success'),
    'data' => $data
];

// 错误响应
return [
    'code' => RegionErrorCode::REGION_NOT_FOUND->value,
    'message' => Lang::get('Region::region.region_not_found'),
    'data' => [
        'error' => $e->getMessage()
    ]
];

// 验证错误
return [
    'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
    'message' => Lang::get('Region::region.validation_failed'),
    'data' => [
        'errors' => $e->errors()
    ]
];
```

### 2. 验证规则
```php
$validated = $request->validate([
    'name' => 'required|string|max:100|unique:regions,name,NULL,id,deleted_at,NULL',
    'description' => 'nullable|string|max:500',
    'status' => 'integer|in:0,1',
    'sort' => 'nullable|integer|min:0|max:9999'
], [
    'name.required' => Lang::get('Region::region.name_required'),
    'name.max' => Lang::get('Region::region.name_max', ['max' => 100]),
    'status.in' => Lang::get('Region::region.status_invalid'),
    'sort.integer' => Lang::get('Region::region.sort_invalid'),
]);
```

### 3. 异常处理
```php
try {
    // 业务逻辑
    $result = $this->regionService->createRegion($validated);
    
    return [
        'code' => 200,
        'message' => Lang::get('Region::region.create_success'),
        'data' => $result->toArray()
    ];
} catch (ValidationException $e) {
    return [
        'code' => RegionErrorCode::REGION_VALIDATION_FAILED->value,
        'message' => Lang::get('Region::region.validation_failed'),
        'data' => [
            'errors' => $e->errors()
        ]
    ];
} catch (Exception $e) {
    return [
        'code' => RegionErrorCode::REGION_CREATE_FAILED->value,
        'message' => Lang::get('Region::region.create_failed'),
        'data' => [
            'error' => $e->getMessage()
        ]
    ];
}
```

### 4. 模型关系
```php
// Region.php
public function channels()
{
    return $this->belongsToMany(Channel::class, 'tvb_regions_channel', 'region_id', 'channel_id')
                ->using(RegionChannel::class)
                ->withTimestamps();
}

// Channel.php
public function regions()
{
    return $this->belongsToMany(Region::class, 'tvb_regions_channel', 'channel_id', 'region_id')
                ->using(RegionChannel::class)
                ->withTimestamps();
}
```

### 5. 查询作用域
```php
// 启用状态
public function scopeEnabled($query)
{
    return $query->where('status', 1);
}

// 禁用状态
public function scopeDisabled($query)
{
    return $query->where('status', 0);
}

// 按名称搜索
public function scopeByName($query, $name)
{
    return $query->where('name', 'like', "%{$name}%");
}
```

## 🎯 错误码列表

### Region 错误码 (15xxx)
- `15001` - REGION_NOT_FOUND
- `15002` - REGION_CREATE_FAILED
- `15003` - REGION_UPDATE_FAILED
- `15004` - REGION_DELETE_FAILED
- `15005` - REGION_VALIDATION_FAILED
- `15006` - REGION_STATUS_CHANGE_FAILED
- `15007` - REGION_LIST_FAILED

### Channel 错误码 (16xxx)
- `16001` - CHANNEL_NOT_FOUND
- `16002` - CHANNEL_CREATE_FAILED
- `16003` - CHANNEL_UPDATE_FAILED
- `16004` - CHANNEL_DELETE_FAILED
- `16005` - CHANNEL_VALIDATION_FAILED
- `16006` - CHANNEL_STATUS_CHANGE_FAILED
- `16007` - CHANNEL_LIST_FAILED

## 📝 语言文件键值

### 通用消息
- `success` - 操作成功
- `create_success` - 创建成功
- `update_success` - 更新成功
- `delete_success` - 删除成功

### 字段名称
- `name` - 名称
- `description` - 描述
- `status` - 状态
- `sort` - 排序

### 验证消息
- `name_required` - 名称不能为空
- `name_max` - 名称不能超过:max个字符
- `status_invalid` - 状态值无效
- `validation_failed` - 验证失败

### 错误消息
- `region_not_found` - 区域不存在
- `create_failed` - 创建失败
- `update_failed` - 更新失败
- `delete_failed` - 删除失败

## 🧪 测试示例

### API 测试
```bash
# 获取区域列表
curl -X GET "http://localhost:8001/api/region?lang=zh_hk"

# 创建区域
curl -X POST "http://localhost:8001/api/region?lang=zh_hk" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试区域",
    "description": "测试描述",
    "status": 1,
    "sort": 1
  }'

# 更新区域
curl -X PUT "http://localhost:8001/api/region/1?lang=zh_hk" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的区域",
    "description": "更新后的描述",
    "status": 1,
    "sort": 2
  }'

# 删除区域
curl -X DELETE "http://localhost:8001/api/region/1?lang=zh_hk"

# 批量操作
curl -X POST "http://localhost:8001/api/region/batch?lang=zh_hk" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "enable",
    "ids": [1, 2, 3]
  }'
```

## 🔄 状态值

- `1` - 启用
- `0` - 禁用

## 📊 分页参数

- `page` - 页码（默认：1）
- `limit` - 每页数量（默认：20）
- `order_by` - 排序字段（默认：sort）
- `order_dir` - 排序方向（默认：asc）

## 🎯 最佳实践要点

1. **统一响应格式**：所有API返回 `{code, message, data}` 格式
2. **多语言支持**：所有消息都支持多语言
3. **错误处理**：使用枚举定义错误码
4. **数据验证**：在Controller层进行验证
5. **业务逻辑**：在Service层处理复杂业务
6. **软删除**：使用软删除而不是物理删除
7. **时间戳转换**：在Model中配置时间戳转换
8. **关联关系**：使用Pivot模型处理多对多关系
9. **查询作用域**：使用作用域简化查询
10. **批量操作**：支持批量启用/禁用/删除

这个快速参考指南包含了Region模块的所有关键信息，可以作为日常开发的参考。 