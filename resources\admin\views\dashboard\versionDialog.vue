<template>
  <div>
    <!-- 版本选择对话框 -->
    <el-dialog 
      :model-value="visible" 
      title="版本選擇" 
      width="778px"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="true"
      align-center
      @close="handleCancel"
      @update:model-value="handleDialogUpdate"
      style="padding: 47px 92px 39px 92px;"
      class="version-dialog"
    >
      <div class="version-selection">
        <div class="version-options">
          <div 
            :class="[
              'version-card',
              { 'selected': selectedVersion === 'professional' }
            ]"
            @click="selectVersion('professional')"
          >
            <div class="card-content">
              <div class="version-preview" >
                <img :src="selectedVersion === 'professional' ? $asset('Dashboard/Asset/zhuanye-cur.png') : $asset('Dashboard/Asset/zhuanye.png')" />
              </div>
              <div class="version-title">專業版</div>
            </div>
          </div>

          <div 
            :class="[
              'version-card',
              { 'selected': selectedVersion === 'info' }
            ]"
            @click="selectVersion('info')"
          >
            <div class="card-content">
              <div class="version-preview">
                <img :src="selectedVersion === 'info' ? $asset('Dashboard/Asset/zixun-cur.png') : $asset('Dashboard/Asset/zixun.png')" />
              </div>
              <div class="version-title">資訊版</div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确定</el-button>
        <el-button @click="handleCancel">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

// Props 定义
interface Props {
  visible: boolean
  currentVersion?: 'professional' | 'info'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  currentVersion: 'professional'
})

// Emits 定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', version: 'professional' | 'info'): void
  (e: 'cancel'): void
}>()

// 本地状态
const selectedVersion = ref<'professional' | 'info'>(props.currentVersion)

// 监听 props 变化
watch(() => props.currentVersion, (newValue) => {
  selectedVersion.value = newValue
}, { immediate: true })

watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 对话框打开时重置选择
    selectedVersion.value = props.currentVersion
  }
})

// 方法
const selectVersion = (version: 'professional' | 'info') => {
  selectedVersion.value = version
}

const handleConfirm = () => {
  emit('confirm', selectedVersion.value)
  emit('update:visible', false)
}

const handleCancel = () => {
  // 重置选择
  selectedVersion.value = props.currentVersion
  emit('cancel')
  emit('update:visible', false)
}

const handleDialogUpdate = (value: boolean) => {
  emit('update:visible', value)
}
</script>

<style lang="scss" scoped>
.version-selection {
  padding: 0;
  
  .version-options {
    display: flex;
    gap: 45px;
    
    .version-card {
      width: 275px;
      height: 195px;
      border: 1px solid #D6D6D6;
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fff;
      
      &:hover {
        border-color: #007EE5;
        box-shadow: 0px 3px 6px #00000029;
      }
      
      
      
      .card-content {
        padding: 32px 20px 11px 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        gap: 24px;
        
        .version-preview {
          width: 116px;
          height: 105px;
          box-shadow: 0px 3px 6px #00000017;
          border-radius: 5px;
          background-color: #fff;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .version-title {
          font-size: 18px;
          color: #CECECE;
        }
      }
      &.selected {
        border-color: #007EE5;
        box-shadow: 0px 3px 6px #00000029;
        .card-content {
          .version-title {
            color: #2C82BC;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  margin-top: 30px;
  .el-button {
    width: 99px;
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    .el-dialog__headerbtn {
      border: none;
      background-color: transparent;
      font-size: 18px;
      box-shadow: none;
      .el-icon {
        color: #707070;
        font-size: 22px;
      }
    }
  }
 
}
</style>
