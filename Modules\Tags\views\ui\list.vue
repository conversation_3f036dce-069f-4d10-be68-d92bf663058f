<template>
  <div class="bwms-module table-page">
    <div class="module-header">
      <FilterPopover v-model="filterDialog">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16"><img :src="$asset('Faq/Asset/FilterIcon.png')" alt="FilterIcon" /></el-icon>
            <span>篩選</span>
          </el-button>
        </template>
        <el-form :model="filterForm" label-position="top">
          <el-form-item label="標籤ID" class="flex">
            <el-date-picker v-model="filterForm.tagIdRange" type="daterange" range-separator="-" start-placeholder="起始" end-placeholder="結束" />
          </el-form-item>
          <el-form-item label="標籤名稱">
            <el-input v-model="filterForm.name" placeholder="請輸入關鍵字"/>
          </el-form-item>
          <el-form-item label="創建者">
            <el-select v-model="filterForm.creator" placeholder="全部">
              <el-option label="全部" value="" />
              <el-option label="Admin" value="Admin" />
              <el-option label="Marketing" value="Marketing" />
            </el-select>
          </el-form-item>
          <el-form-item label="標籤狀態">
            <el-radio-group v-model="filterForm.status" class="status-group">
              <el-radio-button label="全部" value="" />
              <el-radio-button label="啟用" value="1" />
              <el-radio-button label="停用" value="0" />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="創建日期">
            <el-date-picker v-model="filterForm.dateRange" type="daterange" range-separator="至" start-placeholder="-/-/-" end-placeholder="-/-/-" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="resetFilter">
              <el-icon><Refresh /></el-icon>
              <span>重置</span>
            </el-button>
            <el-button class="button-no-border" @click="applyFilter" type="primary">
              <el-icon><Filter /></el-icon>
              <span>篩選</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" @click="openAddDialog">
        <el-icon><Plus /></el-icon>
        <span>新增標籤</span>
      </el-button>
    </div>
    <!-- Tabs -->
    <div class="tabs-bar">
      <el-tabs class="demo-tabs" v-model="activeTab" @tab-click="onTabChange">
        <el-tab-pane label="所有" name="all" />
        <el-tab-pane label="文章標籤" name="article" />
        <el-tab-pane label="廣告標籤" name="ad" />
        <el-tab-pane label="多媒體標籤" name="media" />
        <el-tab-pane label="FAQ標籤" name="faq" />
        <el-tab-pane label="投票標籤" name="vote" />
      </el-tabs>
    </div>
    <!-- 表格 -->
    <div class="module-con">
      <div class="box">
        <el-table :data="tableData[activeTab]" :loading="loading[activeTab]" style="width: 100%">
          <template #empty>
              <el-empty description="暫無數據" image-size="100px" />
          </template>
          <el-table-column prop="id" label="標籤ID" width="120" />
          <el-table-column prop="displayName" label="顯示名稱" min-width="120" />
          <el-table-column prop="refName" label="內部參考名稱" min-width="120" />
          <el-table-column v-if="activeTab === 'all'" prop="category" label="標籤分類" min-width="120" />
          <el-table-column prop="creator" label="創建者" min-width="100" />
          <el-table-column prop="createTime" label="創建時間" min-width="160" />
          <el-table-column prop="count" label="關聯次數" width="100" />
          <el-table-column prop="status" label="狀態" width="80">
            <template #default="scope">
              <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" />
              </template>
            </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="editTag(scope.row)">
                  <el-icon><img :src="$asset('Faq/Asset/EditIcon.png')" alt="" /></el-icon>
                </el-button>
                <el-button class="bwms-operate-btn del-btn" @click="deleteTag(scope.row)">
                  <el-icon><img :src="$asset('Faq/Asset/DeleteIcon.png')" alt="" /></el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="copyTag(scope.row)">
                  <el-icon><img :src="$asset('Faq/Asset/CopyDocumentIcon.png')" alt="" /></el-icon>
                </el-button>
              </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 分页器 -->
      <div class="box-footer">
          <div class="table-pagination-style">
            <div class="pagination-left">
            <span class="page-size-text">每頁顯示</span>
            <el-select v-model="pageSize" class="page-size-select" @change="onPageSizeChange">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" />
              </el-select>
            <span class="total-text">共 {{ total[activeTab] }} 條記錄</span>
            </div>
            <div class="pagination-right">
            <el-pagination v-model:current-page="page[activeTab]" background layout="prev, pager, next" :page-size="pageSize" :total="total[activeTab]" @current-change="onPageChange" />
          </div>
        </div>
      </div>
    </div>
    <!-- 新增/编辑标签弹窗 -->
    <el-dialog class="el-dialog-common-cls" v-model="tagDialogVisible" :title="tagDialogTitle" width="500px">
      <el-form :model="tagForm" :rules="tagRules" ref="tagFormRef" label-position="top">
        <el-form-item label="標籤顯示名稱" prop="displayName">
          <el-input v-model="tagForm.displayName" maxlength="40" show-word-limit placeholder="請輸入標籤顯示名稱" />
        </el-form-item>
        <el-form-item label="標籤內部參考名稱" prop="refName">
          <el-input v-model="tagForm.refName" maxlength="50" show-word-limit placeholder="請輸入唯一標識名稱" />
        </el-form-item>
        <el-form-item label="標籤描述" prop="desc">
          <el-input type="textarea" v-model="tagForm.desc" maxlength="200" show-word-limit placeholder="請輸入標籤用途和適用範圍" rows="3" />
        </el-form-item>
        <el-form-item label="標籤頁面狀態" prop="pageStatus">
          <el-radio-group v-model="tagForm.pageStatus">
            <el-radio :label="0">關閉</el-radio>
            <el-radio :label="1">開啟</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="標籤類別" prop="category">
          <el-input v-model="tagForm.category" maxlength="30" placeholder="如人物、地點、事件等" />
        </el-form-item>
        <el-form-item label="相關關鍵詞" prop="keywords">
          <el-input 
            type="textarea" 
            v-model="tagForm.keywords" 
            maxlength="200" 
            show-word-limit 
            placeholder="請輸入用於Auto Tagging功能的關鍵詞匹配，多個關鍵詞請用逗號分隔" 
            rows="3" 
          />
        </el-form-item>
        <el-form-item label="標籤頁面模板" prop="pageTemplate" v-if="tagForm.pageStatus === 1">
          <el-select v-model="tagForm.pageTemplate" placeholder="請選擇頁面模板" :disabled="tagForm.pageStatus === 0">
            <el-option label="默認模板" value="default" />
            <el-option label="新聞模板" value="news" />
            <el-option label="專題模板" value="special" />
            <el-option label="自定義模板" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="SEO描述" prop="seoDescription">
          <el-input 
            type="textarea" 
            v-model="tagForm.seoDescription" 
            maxlength="250" 
            show-word-limit 
            placeholder="請輸入用於標籤頁面的搜索引擎優化描述" 
            rows="3" 
          />
        </el-form-item>
      </el-form>
      <div class="flex justify-center" style="margin-top: 26px;">
        <el-button @click="tagDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTagForm">確定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const tabList = ['all','article','ad','media','faq','vote'] as const
type TabName = typeof tabList[number]

const activeTab = ref<TabName>('all')
const page = reactive<Record<TabName, number>>({ all: 1, article: 1, ad: 1, media: 1, faq: 1, vote: 1 })
const pageSize = ref(10)
const total = reactive<Record<TabName, number>>({ all: 2, article: 1, ad: 1, media: 0, faq: 0, vote: 0 })
const loading = reactive<Record<TabName, boolean>>({ all: false, article: false, ad: false, media: false, faq: false, vote: false })
const tableData = reactive<Record<TabName, any[]>>({
  all: [
    { id: 'TAG001', displayName: '財經新聞', refName: 'finance_news', category: '文章標籤', creator: 'Admin', createTime: '2023-01-15 10:00', count: 120, status: 1 },
    { id: 'TAG002', displayName: '促銷活動', refName: 'promo_campaign', category: '廣告標籤', creator: 'Marketing', createTime: '2023-02-20 14:30', count: 55, status: 0 }
  ],
  article: [
    { id: 'TAG001', displayName: '財經新聞', refName: 'finance_news', category: '文章標籤', creator: 'Admin', createTime: '2023-01-15 10:00', count: 120, status: 1 }
  ],
  ad: [
    { id: 'TAG002', displayName: '促銷活動', refName: 'promo_campaign', category: '廣告標籤', creator: 'Marketing', createTime: '2023-02-20 14:30', count: 55, status: 0 }
  ],
  media: [],
  faq: [],
  vote: []
})

const filterForm = reactive({
  tagIdRange: [],
  name: '',
  creator: '',
  status: '',
  dateRange: []
})

const filterDialog = ref(false)

const tagDialogVisible = ref(false)
const tagDialogTitle = ref('新增標籤')
const tagFormRef = ref()
const tagForm = reactive({
  id: '',
  displayName: '',
  refName: '',
  desc: '',
  pageStatus: 0,
  category: '',
  keywords: '',
  pageTemplate: '',
  seoDescription: ''
})
const tagRules = {
  displayName: [
    { required: true, message: '請輸入標籤顯示名稱', trigger: 'blur' },
    { min: 1, max: 40, message: '長度1-40字', trigger: 'blur' }
  ],
  refName: [
    { required: true, message: '請輸入唯一標識名稱', trigger: 'blur' },
    { min: 1, max: 50, message: '長度1-50字', trigger: 'blur' }
  ],
  desc: [
    { max: 200, message: '最多200字', trigger: 'blur' }
  ],
  pageStatus: [
    { required: true, message: '請選擇標籤頁面狀態', trigger: 'change' }
  ],
  category: [
    { max: 30, message: '最多30字', trigger: 'blur' }
  ],
  keywords: [
    { max: 200, message: '最多200字', trigger: 'blur' }
  ],
  pageTemplate: [
    { 
      message: '請選擇標籤頁面模板', 
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (tagForm.pageStatus === 1 && !value) {
          callback(new Error('請選擇標籤頁面模板'))
        } else {
          callback()
        }
      }
    }
  ],
  seoDescription: [
    { max: 250, message: '最多250字', trigger: 'blur' }
  ]
}

function onTabChange(tab: { paneName?: string; name?: string }) {
  const name = (tab.paneName || tab.name || activeTab.value) as TabName
  if (!tabList.includes(name)) return
  loading[name] = true
  tableData[name] = []
  setTimeout(() => {
    if (name === 'all') {
      tableData.all = [
        { id: 'TAG001', displayName: '財經新聞', refName: 'finance_news', category: '文章標籤', creator: 'Admin', createTime: '2023-01-15 10:00', count: 120, status: 1 },
        { id: 'TAG002', displayName: '促銷活動', refName: 'promo_campaign', category: '廣告標籤', creator: 'Marketing', createTime: '2023-02-20 14:30', count: 55, status: 0 }
      ]
    } else if (name === 'article') {
      tableData.article = [
        { id: 'TAG001', displayName: '財經新聞', refName: 'finance_news', category: '文章標籤', creator: 'Admin', createTime: '2023-01-15 10:00', count: 120, status: 1 }
      ]
    } else if (name === 'ad') {
      tableData.ad = [
        { id: 'TAG002', displayName: '促銷活動', refName: 'promo_campaign', category: '廣告標籤', creator: 'Marketing', createTime: '2023-02-20 14:30', count: 55, status: 0 }
      ]
    } else {
      tableData[name] = []
    }
    loading[name] = false
  }, 300)
}
function onPageChange(val: number) {
  page[activeTab.value] = val
  // 分页切换时如需请求数据可在此处理
}
function onPageSizeChange(val: number) {
  // 分页大小切换
  // 如需请求数据可在此处理
}
function applyFilter() {
  // TODO: 实现筛选逻辑
  ElMessage.info('篩選功能開發中')
}
function resetFilter() {
  filterForm.tagIdRange = []
  filterForm.name = ''
  filterForm.creator = ''
  filterForm.status = ''
  filterForm.dateRange = []
}
function openAddDialog() {
  tagDialogTitle.value = '新增標籤'
  Object.assign(tagForm, { 
    id: '', 
    displayName: '', 
    refName: '', 
    desc: '', 
    pageStatus: 0, 
    category: '',
    keywords: '',
    pageTemplate: '',
    seoDescription: ''
  })
  tagDialogVisible.value = true
  nextTick(() => tagFormRef.value?.clearValidate?.())
}
function editTag(row: any) {
  tagDialogTitle.value = '編輯標籤'
  Object.assign(tagForm, row)
  tagDialogVisible.value = true
  nextTick(() => tagFormRef.value?.clearValidate?.())
}
function deleteTag(row: any) {
  ElMessageBox.confirm(
    `確定要刪除標籤「${row.displayName || row.name || row.id}」嗎？此操作不可恢復！`,
    '警告',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // TODO: 实际开发时调用删除接口
    ElMessage.success('刪除成功')
    getTagList()
  }).catch(() => {
    ElMessage.info('已取消刪除')
  })
}
function copyTag(row: any) {
  ElMessage.info('複製功能開發中')
}
function submitTagForm() {
  tagFormRef.value.validate((valid: boolean) => {
    if (!valid) return
    if (!tagForm.id) {
      // 新增逻辑
      ElMessage.success('新增成功')
      tagDialogVisible.value = false
      getTagList()
    } else {
      // 编辑逻辑
      ElMessage.success('編輯成功')
      tagDialogVisible.value = false
      getTagList()
    }
  })
}
// 监听页面状态变化，当关闭时清空模板选择
watch(() => tagForm.pageStatus, (newVal) => {
  if (newVal === 0) {
    tagForm.pageTemplate = ''
  }
})

// 预留刷新方法
function getTagList() {
  // TODO: 实际开发时用http.get('/admin/tag/list')拉取
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .tabs-bar {
    background: #fff;
    border-radius: 10px 10px 0 0;
    padding:0 5px 0 15px;
    margin-bottom: 0;
  }
  .module-con {
    .box {
      padding-top: 0;
      border-radius: 0 0 10px 10px;
    }
  }
}
</style>
