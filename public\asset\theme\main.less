@import "./variable.less";

.bwms-page {
  .page-content {
    margin-top: 10px;

    .df();

    .row {
      padding-bottom: 10px;
      height: 100%;
      width: 100%;
    }

    .side {
      margin-right: 10px;
      border-radius: 10px;
      padding: 0;
      background-color: #fff;
      width: 25%;
      height: 100%;
      overflow-y: auto;

      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #B3B3B3;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
        border: none;
      }

      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
        background-color: transparent;
      }

      ul {
        li {
          font-size: 14px;
          color: @bodyColor;
          line-height: 2.85;
          transition: .35s ease-in-out;

          .li {
            padding: 10px 30px;
            font-size: 14px;
            color: @bodyColor;
            line-height: 2.85;
            .df(center, space-between);
            transition: .35s ease-in-out;
            position: relative;

            span {
              font-size: 14px;
              color: @bodyColor;
              line-height: 2.85;
              transition: .35s ease-in-out;
            }

            .iconfont {
              font-size: 12px;
              transition: .35s ease-in-out;
            }

            &::after {
              content: "";
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              height: 100%;
              z-index: 1;
              background-color: @themeColor;
              width: 4px;
              opacity: 0;
              transition: .35s ease-in-out;
            }

            &:hover {
              background-color: #eceffa;
              color: @themeColor;

              span {
                color: @themeColor;
              }

              &::after {
                opacity: 1;
              }
            }

            &.open {
              .iconfont {
                transform: rotate(90deg);
              }
            }
          }

          &.active {
            color: @themeColor;

            &>.li {
              background-color: #eceffa;
              color: @themeColor;

              span {
                color: @themeColor;
              }

              &::after {
                opacity: 1;
              }
            }
          }

          .sub-li {
            height: 0;
            overflow: hidden;
            transition: height .35s ease-in-out;

            ul {
              li {
                line-height: 1.4;
  
                .li {
                  padding-left: 38px;
                  line-height: 1.4;
                }
              }
            }
          }
        }
      }
    }

    .main-content {
      width: calc(75% - 10px);
      height: 100%;

      .white-box {
        margin-bottom: 10px;
        border-radius: 10px;
        padding: 10px;
        background-color: #fff;
        overflow-y: auto;
      }

      .breadcrumbs {
        padding: 20px 10px;

        ul {
          .df(center);

          li {
            font-size: 14px;
            color: @bodyColor;

            &.iconfont {
              margin: 0 10px;
              font-size: 10px;
            }

            &:last-child {
              color: @themeColor;
            }
          }
        }
      }

      .document-list {
        .document-item {
          .df(center);

          padding: 20px;

          .round {
            margin-right: 10px;
            width: 4px;
            height: 4px;
            background-color: @themeColor;
          }

          .iconfont {
            margin-right: 10px;
            font-size: 16px;
            color: @bodyColor;
          }

          .document-tit {
            font-size: 16px;
            color: @bodyColor;
            line-height: 2;

            a {
              font-size: 16px;
              color: @bodyColor;
              line-height: 2;
            }
          }
        }
      }

      .tac {
        text-align: center !important;
      }

      .mb10 {
        margin-bottom: 10px !important;
        line-height: 2.14;
      }
      
      .tit {
        margin-bottom: 10px;
        color: @bodyColor;
        font-size: 20px;
        font-weight: bold;
      }

      h1.tit {
        border-bottom: 1px solid #eee;
        padding: 30px 0 20px;
        font-size: 30px;
      }

      h3 {
        margin-bottom: 10px;
        margin-top: 20px;
      }

      p {
        color: @bodyColor;
        font-size: 14px;
        line-height: 1.4;
      }

      .more-a {
        margin-bottom: 10px;
        padding: 0 10px;
        line-height: 1;

        a {
          color: @bodyColor;
        }

        &:first-child {
          margin-top: 10px;
        }
      }

      .qr-code {
        margin: 0 auto;
        width: 120px;
      }

      .sort-ul {
        margin: 20px 0;

        li {
          margin-top: 10px;
          color: @bodyColor;
          font-size: 14px;
          .df(center);

          &::before {
            margin-right: 8px;
            content: "";
            display: block;
            width: 2px;
            height: 2px;
            background-color: @bodyColor;
          }
        }
      }
    }
  }
}