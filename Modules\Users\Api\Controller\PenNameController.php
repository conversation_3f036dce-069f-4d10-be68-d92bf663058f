<?php

namespace Modules\Users\Api\Controller;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Modules\Users\Models\PenName;
use Bingo\Exceptions\BizException;
use Illuminate\Routing\Controller;

/**
 * 笔名管理控制器
 * 负责笔名的增删改查等操作
 */
class PenNameController extends Controller
{
    /**
     * 获取笔名列表
     * 支持分页、筛选
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $limit = (int) $request->input('limit', 15);
        $page = (int) $request->input('page', 1);

        // 构建查询
        $query = PenName::query();

        // 按笔名筛选
        if ($request->filled('pen_name')) {
            $query->where('pen_name', 'like', '%' . $request->input('pen_name') . '%');
        }

        // 按类型筛选
        if ($request->filled('pen_name_type')) {
            $query->where('pen_name_type', $request->input('pen_name_type'));
        }

        // 按状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // 按创建者筛选
        if ($request->filled('created_by')) {
            $query->where('created_by', $request->input('created_by'));
        }

        // 排序
        $query->orderByDesc('id');

        // 分页获取数据
        $data = $query->paginate($limit, ['*'], 'page', $page);

        // 对分页数据进行处理，添加注释，便于前端理解字段含义
        // 遍历每个笔名，增加类型文本等扩展字段
        $items = [];
        foreach ($data->items() as $item) {
            // 将模型转为数组
            $row = $item->toArray();
            // 增加笔名类型文本
            $row['pen_name_type_text'] = $item->getPenNameTypeTextAttribute();
            // 增加状态文本
            $row['status_text'] = $item->status === 1 ? '启用' : '禁用';
            // 增加创建者信息
            $row['creator'] = $item->creator ? $item->creator->toArray() : null;
            $items[] = $row;
        }
        // 用处理后的items替换原有items
        $data->setCollection(collect($items));

        return [
            'items' => $data->items(),
            'total' => $data->total(),
            'page' => $data->currentPage(),
            'limit' => $data->perPage(),
        ];
    }

    /**
     * 创建笔名
     * @param Request $request
     * @return array 返回操作结果数组
     */
    public function store(Request $request)
    {

        $validated = $request->validate(
            [
                'pen_name'      => ['required', 'string', 'max:100', 'unique:pen_names,pen_name'],
                'pen_name_type' => ['required', 'integer', Rule::in([1, 2, 3, 4])], // 1: 真名, 2: 假名, 3: 自家来源, 4: 其他
                'description'   => 'nullable|string|max:255',
                'status'        => ['required', 'integer', Rule::in([0, 1])], // 0: 禁用, 1: 启用
            ]
        );

        // 创建笔名
        $penName = PenName::create([
            ...$validated,
            'created_by'    =>  0, // 获取当前用户ID
        ]);

        // 返回成功响应，直接返回数组
        return $penName->toArray();
    }

    /**
     * 获取单个笔名详情
     * @param int $id
     * @return array 返回操作结果数组
     */
    public function show($id)
    {
        // 查询笔名
        $penName = PenName::find($id);

        if (!$penName) {
            // 未找到笔名，直接返回数组
            return [];
        }

        // 返回笔名详情，直接返回数组
        return $penName->toArray();
    }

    /**
     * 更新笔名
     * @param Request $request
     * @param int $id
     * @return array 返回操作结果数组
     */
    public function update(Request $request, $id)
    {
        // 查询笔名
        $penName = PenName::findOrFail($id);

        $validated = $request->validate(
            [
                'pen_name'      => ['required', 'string', 'max:100', Rule::unique('pen_names', 'pen_name')->ignore($id)], // 忽略当前笔名
                'pen_name_type' => ['required', 'integer', Rule::in([1, 2, 3, 4])], // 1: 真名, 2: 假名, 3: 自家来源, 4: 其他
                'description'   => 'nullable|string|max:255',
                'status'        => ['required', 'integer', Rule::in([0, 1])], // 0: 禁用, 1: 启用
            ]
        );

        // 更新笔名信息
        $penName->pen_name      = $validated['pen_name'];
        $penName->pen_name_type = $validated['pen_name_type'];
        $penName->description   = $validated['description'];
        $penName->status        = $validated['status'];
        $penName->save();

        return $penName->toArray();
    }

    /**
     * 删除笔名（软删除）
     * @param int $id
     * @return array 返回操作结果数组
     */
    public function destroy($id)
    {
        // 查询笔名
        $penName = PenName::findOrFail($id);

        // 检查笔名是否正在被使用
        if ($penName->isInUse()) {
            throw new BizException('该笔名正在被使用，无法删除');
        }

        // 软删除笔名
        $penName->delete();

        return [];
    }

    /**
     * 切换笔名启用/禁用状态（不需要请求参数status，直接切换数据库中的status字段）
     * @param int $id
     * @return array 返回操作结果数组
     */
    public function switch($id)
    {
        // 查询笔名
        $penName = PenName::findOrFail($id);

        // 计算目标状态：如果当前为1（启用），则切换为0（禁用）；如果当前为0，则切换为1
        $targetStatus = $penName->status == 1 ? 0 : 1;

        // 如果目标状态为禁用，且笔名正在被使用，则抛出异常
        if ($targetStatus == 0 && method_exists($penName, 'isInUse') && $penName->isInUse()) {
            // 抛出业务异常，提示笔名正在被使用，无法禁用
            throw new BizException('该笔名正在被使用，无法禁用');
        }

        // 更新笔名状态
        $penName->status = $targetStatus;
        $penName->save();

        // 返回切换结果
        return [
            'id' => $penName->id,
            'status' => $penName->status,
            'message' => $penName->status == 1 ? '已启用' : '已禁用'
        ];
    }
}
