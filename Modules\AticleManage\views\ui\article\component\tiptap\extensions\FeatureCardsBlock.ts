import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { featureCardsTemplate } from '../templates/featureCards.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    featureCardsBlock: {
      insertFeatureCardsBlock: () => ReturnType
    }
  }
}

export const FeatureCardsBlock = Node.create({
  name: 'featureCardsBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  content: '',  // 明确指定为叶子节点

  parseHTML() {
    return [
      {
        tag: 'div.feature-cards',
      },
      {
        tag: 'div[data-bs-component="feature-cards"]',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-bs-component': 'feature-cards',
      'class': 'feature-cards-block py-5 text-white bg-dark'
    })]
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          
          return {
            class: attributes.class
          }
        }
      }
    }
  },

  addCommands() {
    return {
      insertFeatureCardsBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(featureCardsTemplate)
        },
    }
  },
}) 