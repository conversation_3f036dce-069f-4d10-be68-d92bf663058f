import { DirectiveBinding } from 'vue'
import React from 'react'
import ReactDOM from 'react-dom/client'

console.warn = () => {}
console.error = () => {}

export const reactComponent = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { component, props } = binding.value as {
      component: React.ComponentType<any>
      props: any
    }

    // 创建一个新的容器元素
    const container = document.createElement('div')
    container.style.height = '100%'
    el.appendChild(container)

    // 创建 React 根容器
    const root = ReactDOM.createRoot(container)

    // 包装 props
    const wrappedProps = wrapEventHandlers(props)

    // 确保消息渲染正确
    if (wrappedProps.messages) {
      wrappedProps.messages = wrappedProps.messages.map((msg: any) => ({
        ...msg,
        _id: msg._id || Date.now().toString(),
      }))
    }

    // 渲染组件
    root.render(React.createElement(component, wrappedProps))

    // 保存引用
    ;(el as any).__reactRoot__ = root
    ;(el as any).__reactProps__ = props
    ;(el as any).__reactContainer__ = container
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { component, props } = binding.value as {
      component: React.ComponentType<any>
      props: any
    }

    const root = (el as any).__reactRoot__
    const oldProps = (el as any).__reactProps__

    // 包装新的 props
    const wrappedProps = wrapEventHandlers(props)

    // 检查 props 是否发生变化
    if (!propsAreEqual(oldProps, props)) {
      // 更新组件
      root.render(React.createElement(component, wrappedProps))
      // 更新保存的 props
      ;(el as any).__reactProps__ = props
    }
  },

  unmounted(el: HTMLElement) {
    const root = (el as any).__reactRoot__
    if (root) {
      root.unmount()
      // 移除容器
      if ((el as any).__reactContainer__) {
        el.removeChild((el as any).__reactContainer__)
      }
      // 清理引用
      delete (el as any).__reactRoot__
      delete (el as any).__reactProps__
      delete (el as any).__reactContainer__
    }
  },
}

// 改进的 props 比较函数
function propsAreEqual(oldProps: any, newProps: any): boolean {
  if (oldProps === newProps) return true
  if (!oldProps || !newProps) return false

  const oldKeys = Object.keys(oldProps)
  const newKeys = Object.keys(newProps)

  if (oldKeys.length !== newKeys.length) return false

  for (const key of oldKeys) {
    if (key === 'messages') {
      // 对消息数组进行特殊比较
      if (!messagesAreEqual(oldProps[key], newProps[key])) {
        return false
      }
    } else if (typeof oldProps[key] === 'function' && typeof newProps[key] === 'function') {
      // 跳过函数比较
      continue
    } else if (oldProps[key] !== newProps[key]) {
      return false
    }
  }

  return true
}

// 消息数组比较函数
function messagesAreEqual(oldMessages: any[], newMessages: any[]): boolean {
  if (!Array.isArray(oldMessages) || !Array.isArray(newMessages)) return false
  if (oldMessages.length !== newMessages.length) return false

  return oldMessages.every((oldMsg, index) => {
    const newMsg = newMessages[index]
    return oldMsg._id === newMsg._id && oldMsg.type === newMsg.type && oldMsg.position === newMsg.position && JSON.stringify(oldMsg.content) === JSON.stringify(newMsg.content)
  })
}

// 改进的事件处理函数包装器
function wrapEventHandlers(props: any) {
  const wrappedProps = { ...props }

  Object.keys(props).forEach(key => {
    if (typeof props[key] === 'function') {
      if (key === 'renderMessageContent') {
        // 特殊处理 renderMessageContent
        wrappedProps[key] = (msg: any) => {
          try {
            return React.createElement(React.Fragment, null, typeof props[key] === 'function' ? props[key](msg) : null)
          } catch (error) {
            console.error('Error in renderMessageContent:', error)
            return null
          }
        }
      } else {
        // 包装其他事件处理函数
        wrappedProps[key] = (...args: any[]) => {
          try {
            // 确保在下一个事件循环中执行，以避免 React 的合成事件问题
            setTimeout(() => {
              props[key](...args)
            }, 0)
          } catch (error) {
            console.error(`Error in ${key}:`, error)
          }
        }
      }
    }
  })

  return wrappedProps
}

export default reactComponent
