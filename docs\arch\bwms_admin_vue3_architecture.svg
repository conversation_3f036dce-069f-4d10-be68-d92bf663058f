<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1280" height="1200" xmlns="http://www.w3.org/2000/svg" version="1.1">
  <defs>
    <style type="text/css">
      .layer-title { font-size: 20px; font-weight: bold; text-anchor: middle; }
      .section-title { font-size: 18px; font-weight: bold; text-anchor: middle; }
      .component-text { font-size: 13px; text-anchor: middle; dominant-baseline: middle; }
      .component-box { stroke: #333; stroke-width: 1; }
      .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
      .data-flow { stroke: #6366F1; stroke-width: 2; marker-end: url(#blueArrow); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="blueArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6366F1" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1280" height="1200" fill="white"/>
  
  <!-- 标题 -->
  <text x="640" y="40" font-size="24" font-weight="bold" text-anchor="middle">BWMS 管理后台架构图</text>
  <text x="640" y="65" font-size="16" font-style="italic" text-anchor="middle">Vue 3 + TypeScript + BWMS Admin API</text>

  <!-- 管理员访问层 -->
  <rect x="90" y="90" width="1100" height="80" fill="#FFF7ED" stroke="#EA580C" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="115" class="section-title">管理员访问层</text>
  <g transform="translate(140, 135)">
    <rect x="0" y="0" width="200" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="100" y="13" class="component-text" fill="white">超级管理员</text>
    
    <rect x="220" y="0" width="200" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="320" y="13" class="component-text" fill="white">系统管理员</text>
    
    <rect x="440" y="0" width="200" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="540" y="13" class="component-text" fill="white">内容管理员</text>
    
    <rect x="660" y="0" width="200" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="760" y="13" class="component-text" fill="white">运营人员</text>
    
    <rect x="880" y="0" width="160" height="25" fill="#EA580C" class="component-box" rx="5" ry="5"/>
    <text x="960" y="13" class="component-text" fill="white">客服人员</text>
  </g>

  <!-- 前端管理层 - Vue3 Admin -->
  <rect x="90" y="200" width="1100" height="200" fill="#F0F4FF" stroke="#6366F1" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="225" class="section-title">管理后台前端层 - Vue 3 企业级技术栈</text>
  
  <!-- Vue3 管理后台核心技术栈 -->
  <g transform="translate(110, 250)">
    <rect x="0" y="0" width="200" height="35" fill="#6366F1" class="component-box" rx="5" ry="5"/>
    <text x="100" y="18" class="component-text" fill="white">Vue 3.3+ 框架</text>
    
    <rect x="220" y="0" width="200" height="35" fill="#3178C6" class="component-box" rx="5" ry="5"/>
    <text x="320" y="18" class="component-text" fill="white">TypeScript 5.0+</text>
    
    <rect x="440" y="0" width="200" height="35" fill="#646CFF" class="component-box" rx="5" ry="5"/>
    <text x="540" y="18" class="component-text" fill="white">Vite 4.0+ 构建</text>
    
    <rect x="660" y="0" width="200" height="35" fill="#409EFF" class="component-box" rx="5" ry="5"/>
    <text x="760" y="18" class="component-text" fill="white">Element Plus UI</text>
    
    <rect x="880" y="0" width="140" height="35" fill="#67C23A" class="component-box" rx="5" ry="5"/>
    <text x="950" y="18" class="component-text" fill="white">权限控制</text>
  </g>
  
  <!-- 管理后台生态系统 -->
  <g transform="translate(110, 300)">
    <rect x="0" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="75" y="15" class="component-text" fill="white">Vue Router 4</text>
    
    <rect x="170" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="245" y="15" class="component-text" fill="white">Pinia Store</text>
    
    <rect x="340" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="415" y="15" class="component-text" fill="white">Vue I18n</text>
    
    <rect x="510" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="585" y="15" class="component-text" fill="white">Axios HTTP</text>
    
    <rect x="680" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="755" y="15" class="component-text" fill="white">ECharts 图表</text>
    
    <rect x="850" y="0" width="150" height="30" fill="#8B5CF6" class="component-box" rx="5" ry="5"/>
    <text x="925" y="15" class="component-text" fill="white">Monaco 编辑器</text>
  </g>
  
  <!-- 管理功能模块 -->
  <g transform="translate(110, 345)">
    <rect x="0" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="70" y="13" class="component-text" fill="white" font-size="11">用户权限</text>
    
    <rect x="160" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="230" y="13" class="component-text" fill="white" font-size="11">内容管理</text>
    
    <rect x="320" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="390" y="13" class="component-text" fill="white" font-size="11">数据分析</text>
    
    <rect x="480" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="550" y="13" class="component-text" fill="white" font-size="11">系统监控</text>
    
    <rect x="640" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="710" y="13" class="component-text" fill="white" font-size="11">日志审计</text>
    
    <rect x="800" y="0" width="140" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="870" y="13" class="component-text" fill="white" font-size="11">工作流</text>
    
    <rect x="960" y="0" width="80" height="25" fill="#4338CA" class="component-box" rx="5" ry="5"/>
    <text x="1000" y="13" class="component-text" fill="white" font-size="11">配置</text>
  </g>

  <!-- BWMS Admin API 层 -->
  <rect x="90" y="430" width="1100" height="130" fill="#ECFDF5" stroke="#059669" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="455" class="section-title">BWMS Admin API 接口层</text>
  
  <g transform="translate(130, 480)">
    <rect x="0" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="80" y="13" class="component-text" fill="white">管理员认证</text>
    
    <rect x="180" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="260" y="13" class="component-text" fill="white">权限管理</text>
    
    <rect x="360" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="440" y="13" class="component-text" fill="white">内容管理</text>
    
    <rect x="540" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="620" y="13" class="component-text" fill="white">用户管理</text>
    
    <rect x="720" y="0" width="160" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="800" y="13" class="component-text" fill="white">系统配置</text>
    
    <rect x="900" y="0" width="120" height="25" fill="#059669" class="component-box" rx="5" ry="5"/>
    <text x="960" y="13" class="component-text" fill="white">数据统计</text>
  </g>
  
  <g transform="translate(130, 515)">
    <rect x="0" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="95" y="10" class="component-text" font-size="11">RBAC 权限控制</text>
    
    <rect x="200" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="295" y="10" class="component-text" font-size="11">操作日志记录</text>
    
    <rect x="400" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="495" y="10" class="component-text" font-size="11">数据验证</text>
    
    <rect x="600" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="695" y="10" class="component-text" fill="white">安全防护</text>
    
    <rect x="800" y="0" width="190" height="20" fill="#34D399" class="component-box" rx="3" ry="3"/>
    <text x="895" y="10" class="component-text" font-size="11">API 版本控制</text>
  </g>

  <!-- 业务模块层 -->
  <rect x="90" y="590" width="1100" height="130" fill="#FFF8E1" stroke="#F59E0B" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="615" class="section-title">BWMS 业务模块层</text>
  
  <g transform="translate(130, 645)">
    <rect x="0" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="67" y="13" class="component-text" fill="white">CMS 内容</text>
    
    <rect x="155" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="222" y="13" class="component-text" fill="white">用户系统</text>
    
    <rect x="310" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="377" y="13" class="component-text" fill="white">媒体管理</text>
    
    <rect x="465" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="532" y="13" class="component-text" fill="white">表单系统</text>
    
    <rect x="620" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="687" y="13" class="component-text" fill="white">工作流</text>
    
    <rect x="775" y="0" width="135" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="842" y="13" class="component-text" fill="white">AI 助手</text>
    
    <rect x="930" y="0" width="120" height="25" fill="#F59E0B" class="component-box" rx="5" ry="5"/>
    <text x="990" y="13" class="component-text" fill="white">系统配置</text>
  </g>
  
  <g transform="translate(130, 680)">
    <rect x="0" y="0" width="190" height="20" fill="#FCD34D" class="component-box" rx="3" ry="3"/>
    <text x="95" y="10" class="component-text" font-size="11">Laravel 服务层</text>
    
    <rect x="200" y="0" width="190" height="20" fill="#FCD34D" class="component-box" rx="3" ry="3"/>
    <text x="295" y="10" class="component-text" font-size="11">领域驱动设计</text>
    
    <rect x="400" y="0" width="190" height="20" fill="#FCD34D" class="component-box" rx="3" ry="3"/>
    <text x="495" y="10" class="component-text" font-size="11">仓储模式</text>
    
    <rect x="600" y="0" width="190" height="20" fill="#FCD34D" class="component-box" rx="3" ry="3"/>
    <text x="695" y="10" class="component-text" font-size="11">事件驱动</text>
    
    <rect x="800" y="0" width="190" height="20" fill="#FCD34D" class="component-box" rx="3" ry="3"/>
    <text x="895" y="10" class="component-text" font-size="11">队列任务</text>
  </g>

  <!-- 数据存储层 -->
  <rect x="90" y="750" width="1100" height="80" fill="#E6F0FF" stroke="#6699FF" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="775" class="section-title">数据存储层</text>
  <g transform="translate(160, 800)">
    <rect x="0" y="0" width="180" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="90" y="13" class="component-text" fill="white">MySQL 主库</text>
    
    <rect x="200" y="0" width="180" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="290" y="13" class="component-text" fill="white">Redis 缓存</text>
    
    <rect x="400" y="0" width="180" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="490" y="13" class="component-text" fill="white">文件存储</text>
    
    <rect x="600" y="0" width="180" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="690" y="13" class="component-text" fill="white">Elasticsearch</text>
    
    <rect x="800" y="0" width="120" height="25" fill="#6699FF" class="component-box" rx="5" ry="5"/>
    <text x="860" y="13" class="component-text" fill="white">监控日志</text>
  </g>

  <!-- 技术特点说明 -->
  <rect x="90" y="860" width="1100" height="300" fill="#F8F9FA" stroke="#6C757D" stroke-width="2" rx="10" ry="10"/>
  <text x="640" y="885" class="section-title">Vue3 管理后台架构特点</text>
  
  <g transform="translate(120, 910)">
    <circle cx="10" cy="10" r="5" fill="#28A745"/>
    <text x="25" y="15" font-size="14" font-weight="bold">企业级：</text>
    <text x="100" y="15" font-size="13">• 完整的权限管理系统 RBAC</text>
    <text x="320" y="15" font-size="13">• 多层级角色控制</text>
    <text x="490" y="15" font-size="13">• 操作日志审计</text>
    <text x="630" y="15" font-size="13">• 数据安全防护</text>
    <text x="780" y="15" font-size="13">• 系统监控告警</text>
    
    <circle cx="10" cy="45" r="5" fill="#007BFF"/>
    <text x="25" y="50" font-size="14" font-weight="bold">功能：</text>
    <text x="80" y="50" font-size="13">• 50+ 业务模块管理</text>
    <text x="250" y="50" font-size="13">• 实时数据统计分析</text>
    <text x="420" y="50" font-size="13">• 工作流引擎</text>
    <text x="550" y="50" font-size="13">• 内容管理系统</text>
    <text x="700" y="50" font-size="13">• AI 智能助手</text>
    <text x="850" y="50" font-size="13">• 多语言支持</text>
    
    <circle cx="10" cy="80" r="5" fill="#6F42C1"/>
    <text x="25" y="85" font-size="14" font-weight="bold">技术：</text>
    <text x="80" y="85" font-size="13">• Vue 3.3+ Composition API</text>
    <text x="290" y="85" font-size="13">• TypeScript 严格模式</text>
    <text x="480" y="85" font-size="13">• Element Plus 企业级UI</text>
    <text x="680" y="85" font-size="13">• Vite 极速开发</text>
    <text x="830" y="85" font-size="13">• Pinia 状态管理</text>
    
    <circle cx="10" cy="115" r="5" fill="#FFC107"/>
    <text x="25" y="120" font-size="14" font-weight="bold">优势：</text>
    <text x="80" y="120" font-size="13">• 类型安全的接口调用</text>
    <text x="260" y="120" font-size="13">• 组件化可复用开发</text>
    <text x="430" y="120" font-size="13">• 响应式数据流</text>
    <text x="570" y="120" font-size="13">• 模块化架构</text>
    <text x="700" y="120" font-size="13">• 高性能渲染</text>
    <text x="830" y="120" font-size="13">• 开发体验优秀</text>
    
    <circle cx="10" cy="150" r="5" fill="#DC3545"/>
    <text x="25" y="155" font-size="14" font-weight="bold">安全：</text>
    <text x="80" y="155" font-size="13">• JWT Token 认证</text>
    <text x="240" y="155" font-size="13">• CSRF/XSS 防护</text>
    <text x="390" y="155" font-size="13">• 接口权限验证</text>
    <text x="540" y="155" font-size="13">• 数据加密传输</text>
    <text x="690" y="155" font-size="13">• SQL 注入防护</text>
    <text x="840" y="155" font-size="13">• 操作行为追踪</text>
    
    <circle cx="10" cy="185" r="5" fill="#17A2B8"/>
    <text x="25" y="190" font-size="14" font-weight="bold">适用场景：</text>
    <text x="120" y="190" font-size="13">• 大型企业管理平台</text>
    <text x="290" y="190" font-size="13">• 多租户 SaaS 系统</text>
    <text x="450" y="190" font-size="13">• 复杂业务流程管理</text>
    <text x="610" y="190" font-size="13">• 数据驱动决策平台</text>
    <text x="780" y="190" font-size="13">• 内容创作平台</text>
    
    <circle cx="10" cy="220" r="5" fill="#6C757D"/>
    <text x="25" y="225" font-size="14" font-weight="bold">限制：</text>
    <text x="80" y="225" font-size="13">• 开发学习曲线较陡</text>
    <text x="250" y="225" font-size="13">• 对团队技术要求高</text>
    <text x="420" y="225" font-size="13">• 项目初期成本较高</text>
    <text x="590" y="225" font-size="13">• 需要完善的开发规范</text>
    <text x="780" y="225" font-size="13">• SEO 优化需要额外处理</text>
  </g>

  <!-- 数据流箭头 -->
  <line x1="640" y1="170" x2="640" y2="200" class="arrow"/>
  <line x1="640" y1="400" x2="640" y2="430" class="arrow"/>
  <line x1="640" y1="560" x2="640" y2="590" class="arrow"/>
  <line x1="640" y1="720" x2="640" y2="750" class="arrow"/>
  
  <!-- 权限验证数据流 -->
  <line x1="240" y1="370" x2="240" y2="480" class="data-flow"/>
  <text x="250" y="420" font-size="12" fill="#6366F1">权限验证</text>
  
  <!-- API 响应数据流 -->
  <line x1="1040" y1="480" x2="1040" y2="370" class="data-flow"/>
  <text x="950" y="420" font-size="12" fill="#6366F1">数据响应</text>
  
  <!-- 状态管理数据流 -->
  <line x1="440" y1="370" x2="540" y2="480" class="data-flow"/>
  <text x="470" y="430" font-size="11" fill="#6366F1">状态同步</text>
</svg> 