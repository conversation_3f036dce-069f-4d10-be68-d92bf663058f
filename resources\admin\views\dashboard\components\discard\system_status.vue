<template>
  <div class="protect piece-box activity-log-box mt-2.5" style="padding: 20px">
    <div class="chartsInfo">
      <el-row :gutter="10">
        <!-- CPU 信息显示 -->
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
          <el-popover placement="bottom" :width="loadWidth()" trigger="hover" v-if="chartsOption.cpu">
            <div>
              <el-tooltip effect="dark" :content="baseInfo.cpuModelName" v-if="baseInfo.cpuModelName" placement="top">
                <el-tag class="cpuModeTag">
                  {{ baseInfo.cpuModelName.substring(0, 40) + '...' }}
                </el-tag>
              </el-tooltip>
            </div>
            <el-tag class="cpuDetailTag">{{ $t('CFM.protect.core') }} *{{ baseInfo.cpuCores }}</el-tag>
            <el-tag class="cpuDetailTag">{{ $t('CFM.protect.logicCore') }} *{{ baseInfo.cpuLogicalCores }}</el-tag>
            <br />
            <div v-for="(item, index) of currentInfo.cpuPercent" :key="index">
              <el-tag v-if="cpuShowAll || (!cpuShowAll && index < 32)" class="tagCPUClass"> CPU-{{ index }}: {{ formatNumber(item) }}% </el-tag>
            </div>

            <div v-if="currentInfo.cpuPercent.length > 32" class="float-right mt-1">
              <el-button v-if="!cpuShowAll" @click="cpuShowAll = true" link type="primary" size="small">
                {{ $t('CFM.protect.showAll') }}
                <el-icon><DArrowRight /></el-icon>
              </el-button>
              <el-button v-if="cpuShowAll" @click="cpuShowAll = false" link type="primary" size="small">
                {{ $t('CFM.protect.hideSome') }}
                <el-icon><DArrowLeft /></el-icon>
              </el-button>
            </div>
            <template #reference>
              <v-chart height="160px" id="cpu" type="pie" :option="chartsOption.cpu" v-if="chartsOption.cpu" />
            </template>
          </el-popover>
          <span class="input-help"> ( {{ formatNumber(currentInfo.cpuUsed) }} / {{ currentInfo.cpuTotal }} ) {{ $t('CFM.protect.units.core') }} </span>
        </el-col>

        <!-- 内存信息显示 -->
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
          <el-popover placement="bottom" :width="loadWidth()" trigger="hover" v-if="chartsOption.memory">
            <el-tag style="font-weight: 500">{{ $t('CFM.protect.memory') }}:</el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.total') }}: {{ formatNumber(currentInfo.memoryTotal / 1024 / 1024) }} MB </el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.used') }}: {{ formatNumber(currentInfo.memoryUsed / 1024 / 1024) }} MB </el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.free') }}: {{ formatNumber(currentInfo.swapMemoryAvailable / 1024 / 1024) }} MB </el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.percent') }}: {{ formatNumber(currentInfo.memoryUsedPercent) }}% </el-tag>
            <div v-if="currentInfo.swapMemoryTotal" class="mt-2">
              <el-tag style="font-weight: 500">{{ $t('CFM.protect.swapMem') }}:</el-tag>
              <el-tag class="tagClass"> {{ $t('CFM.protect.total') }}: {{ formatNumber(currentInfo.swapMemoryTotal / 1024 / 1024) }} MB </el-tag>
              <el-tag class="tagClass"> {{ $t('CFM.protect.used') }}: {{ formatNumber(currentInfo.swapMemoryUsed / 1024 / 1024) }} MB </el-tag>
              <el-tag class="tagClass"> {{ $t('CFM.protect.free') }}: {{ formatNumber(currentInfo.swapMemoryAvailable / 1024 / 1024) }} MB </el-tag>
              <el-tag class="tagClass"> {{ $t('CFM.protect.percent') }}: {{ formatNumber(currentInfo.swapMemoryUsedPercent) }}% </el-tag>
            </div>
            <template #reference>
              <v-chart height="160px" id="memory" type="pie" :option="chartsOption.memory" v-if="chartsOption.memory" />
            </template>
          </el-popover>
          <span class="input-help"> ( {{ formatNumber(currentInfo.memoryUsed / 1024 / 1024) }} / {{ formatNumber(currentInfo.memoryTotal / 1024 / 1024) }} ) MB </span>
        </el-col>

        <!-- 负载信息显示 -->
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
          <el-popover placement="bottom" :width="loadWidth()" trigger="hover" v-if="chartsOption.load">
            <el-tag class="tagClass"> {{ $t('CFM.protect.loadAverage', [1]) }}: {{ formatNumber(currentInfo.load1) }} </el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.loadAverage', [5]) }}: {{ formatNumber(currentInfo.load5) }} </el-tag>
            <el-tag class="tagClass"> {{ $t('CFM.protect.loadAverage', [15]) }}: {{ formatNumber(currentInfo.load15) }} </el-tag>
            <template #reference>
              <v-chart height="160px" id="load" type="pie" :option="chartsOption.load" v-if="chartsOption.load" />
            </template>
          </el-popover>
          <span class="input-help">{{ $t(loadStatus(currentInfo.loadUsagePercent)) }}</span>
        </el-col>

        <!-- 磁盘信息显示 -->
        <template v-for="(item, index) of currentInfo.diskData" :key="index">
          <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center" v-if="showMore || index < 4">
            <el-popover placement="bottom" :width="loadWidth()" trigger="hover" v-if="chartsOption.disk[`disk${index}`]">
              <el-row :gutter="5">
                <el-tag style="font-weight: 500">{{ $t('CFM.protect.baseInfo') }}:</el-tag>
              </el-row>
              <el-row :gutter="5">
                <el-tag class="nameTag">{{ $t('CFM.protect.mount') }}: {{ item.path }}</el-tag>
              </el-row>
              <el-row :gutter="5">
                <el-tag class="tagClass">{{ $t('CFM.protect.type') }}: {{ item.type }}</el-tag>
              </el-row>
              <el-row :gutter="5">
                <el-tag class="tagClass">{{ $t('CFM.protect.fileSystem') }}: {{ item.device }}</el-tag>
              </el-row>
              <el-row :gutter="5">
                <el-col :span="12">
                  <div><el-tag class="tagClass" style="font-weight: 500">Inode:</el-tag></div>
                  <el-tag class="tagClass">{{ $t('CFM.protect.total2') }}: {{ item.inodesTotal }}</el-tag>
                  <el-tag class="tagClass">{{ $t('CFM.protect.used') }}: {{ item.inodesUsed }}</el-tag>
                  <el-tag class="tagClass">{{ $t('CFM.protect.free') }}: {{ item.inodesFree }}</el-tag>
                  <el-tag class="tagClass"> {{ $t('CFM.protect.percent') }}: {{ formatNumber(item.inodesUsedPercent) }}% </el-tag>
                </el-col>

                <el-col :span="12">
                  <div>
                    <el-tag style="margin-top: 3px; font-weight: 500">{{ $t('CFM.protect.disk') }}:</el-tag>
                  </div>
                  <el-tag class="tagClass">{{ $t('CFM.protect.total2') }}: {{ computeSize(item.total) }}</el-tag>
                  <el-tag class="tagClass">{{ $t('CFM.protect.used') }}: {{ computeSize(item.used) }}</el-tag>
                  <el-tag class="tagClass">{{ $t('CFM.protect.free') }}: {{ computeSize(item.free) }}</el-tag>
                  <el-tag class="tagClass"> {{ $t('CFM.protect.percent') }}: {{ formatNumber(item.usedPercent) }}% </el-tag>
                </el-col>
              </el-row>
              <template #reference>
                <v-chart height="160px" :id="`disk${index}`" type="pie" :option="chartsOption.disk[`disk${index}`]" v-if="chartsOption.disk[`disk${index}`]" />
              </template>
            </el-popover>
            <span class="input-help">{{ computeSize(item.used) }} / {{ computeSize(item.total) }}</span>
          </el-col>
        </template>
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
          <el-button v-if="!showMore" link type="primary" @click="showMore = true" class="buttonClass">
            {{ $t('CFM.protect.more') }}
            <el-icon><Bottom /></el-icon>
          </el-button>
          <el-button v-if="showMore && currentInfo.diskData.length > 5" type="primary" link @click="showMore = false" class="buttonClass">
            {{ $t('CFM.protect.hide') }}
            <el-icon><Top /></el-icon>
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import VChart from '../v-charts/index.vue'
import http from '/admin/support/http'
interface DiskInfo {
  device: string
  type: string
  total: number
  used: number
  free: number
  usedPercent: number
  path: string
  inodesTotal: number
  inodesUsed: number
  inodesFree: number
  inodesUsedPercent: number
}

interface SystemInfo {
  cpuUsed: number
  cpuTotal: number
  memoryTotal: number
  memoryUsed: number
  memoryUsedPercent: number
  loadUsagePercent: number
  load1: number
  load5: number
  load15: number
  cpuPercent: number[]
  swapMemoryTotal: number
  swapMemoryUsed: number
  swapMemoryAvailable: number
  swapMemoryUsedPercent: number
  diskData: DiskInfo[]
  gpuData: any[]
}

interface BaseInfo {
  cpuModelName: string
  cpuCores: number
  cpuLogicalCores: number
}

interface ChartsOption {
  cpu: any | null
  memory: any | null
  load: any | null
  disk: { [key: string]: any | null }
}

const { system_overview } = defineProps({
  system_overview: Object,
})
const systemOverview = ref(system_overview)
const currentInfo = ref<SystemInfo>({
  cpuUsed: 0,
  cpuTotal: 0,
  memoryTotal: 0,
  memoryUsed: 0,
  memoryUsedPercent: 0,
  loadUsagePercent: 0,
  load1: 0,
  load5: 0,
  load15: 0,
  cpuPercent: [],
  swapMemoryTotal: 0,
  swapMemoryUsed: 0,
  swapMemoryAvailable: 0,
  swapMemoryUsedPercent: 0,
  diskData: [],
  gpuData: [],
})

const baseInfo = ref<BaseInfo>({
  cpuModelName: '',
  cpuCores: 0,
  cpuLogicalCores: 0,
})

const chartsOption = ref<ChartsOption>({
  cpu: null,
  memory: null,
  load: null,
  disk: {},
})

const cpuShowAll = ref(false)
const showMore = ref(true)

watch(
  () => systemOverview.value,
  (newValue) => {
    if (newValue) {
      updateCurrentInfo(newValue)
    }
  },
  { deep: true, immediate: true }
)
function updateCurrentInfo(data: any) {
  currentInfo.value.cpuUsed = data.cpuUsed ?? 0
  currentInfo.value.cpuTotal = data.cpuTotal ?? 0
  currentInfo.value.memoryUsed = data.memoryUsed ?? 0
  currentInfo.value.memoryTotal = data.memoryTotal ?? 0
  currentInfo.value.memoryUsedPercent = data.memoryUsedPercent ?? 0
  currentInfo.value.loadUsagePercent = data.loadUsagePercent ?? 0
  currentInfo.value.load1 = data.load1 ?? 0
  currentInfo.value.load5 = data.load5 ?? 0
  currentInfo.value.load15 = data.load15 ?? 0
  currentInfo.value.cpuPercent = data.cpuPercent ?? []
  currentInfo.value.swapMemoryTotal = data.swapMemoryTotal ?? 0
  currentInfo.value.swapMemoryUsed = data.swapMemoryUsed ?? 0
  currentInfo.value.swapMemoryAvailable = data.swapMemoryAvailable ?? 0
  currentInfo.value.swapMemoryUsedPercent = data.swapMemoryUsedPercent ?? 0
  currentInfo.value.diskData =
    data.diskData?.map((disk: DiskInfo) => ({
      device: disk.device ?? '',
      type: disk.type ?? '',
      total: disk.total ?? 0,
      used: disk.used ?? 0,
      free: disk.free ?? 0,
      usedPercent: disk.usedPercent ?? 0,
      path: disk.path ?? '',
      inodesTotal: disk.inodesTotal ?? 0,
      inodesUsed: disk.inodesUsed ?? 0,
      inodesFree: disk.inodesFree ?? 0,
      inodesUsedPercent: disk.inodesUsedPercent ?? 0,
    })) ?? []
  currentInfo.value.gpuData = data.gpuData ?? []

  baseInfo.value.cpuModelName = data.cpuModelName ?? ''
  baseInfo.value.cpuCores = data.cpuCores ?? 0
  baseInfo.value.cpuLogicalCores = data.cpuLogicalCores ?? 0

  chartsOption.value.cpu = {
    title: 'CFM.protect.CPU',
    data: formatNumber(currentInfo.value.cpuPercent.reduce((acc, cur) => acc + cur, 0) / currentInfo.value.cpuPercent.length),
  }
  chartsOption.value.memory = {
    title: 'CFM.protect.memory',
    data: formatNumber(currentInfo.value.memoryUsedPercent),
  }
  chartsOption.value.load = {
    title: 'CFM.protect.load',
    data: formatNumber(currentInfo.value.loadUsagePercent),
  }
  currentInfo.value.diskData.forEach((disk, index) => {
    chartsOption.value.disk[`disk${index}`] = {
      title: 'CFM.protect.swapMemory',
      data: formatNumber(disk.usedPercent),
    }
  })
}

function loadStatus(val: number): string {
  if (val < 30) {
    return 'CFM.protect.runningSmoothly'
  }
  if (val < 70) {
    return 'CFM.protect.runningNormally'
  }
  if (val < 80) {
    return 'CFM.protect.runningSlowly'
  }
  return 'CFM.protect.runningCongested'
}

function formatNumber(val: number | undefined): number {
  return val !== undefined ? Number(val.toFixed(2)) : 0
}

function computeSize(val: number): string {
  return val > 1024 ? `${(val / 1024).toFixed(2)} GB` : `${val.toFixed(2)} MB`
}

function loadWidth() {
  if (!cpuShowAll.value || currentInfo.value.cpuPercent.length < 32) {
    return 310
  }
  let line = Math.floor(currentInfo.value.cpuPercent.length / 16)
  return line * 141 + 28
}

let timer: number | null = null;

// 定义从服务器获取系统信息的方法
const fetchSystemStatus = async () => {
  try {
    const response = await http.get('/system/widgets', { params: { type: 'system_status' } });
    const data = response.data.data.find((item: any) => item.alias === 'system_status');
    if (data) {
     systemOverview.value = data.data.system_overview;
    }
  } catch (error) {
    console.error('Error fetching system status:', error);
  }
};

onMounted(() => {
  timer = window.setInterval(fetchSystemStatus, 10000);
});

onUnmounted(() => {
  // 清除定时器
  if (timer) {
    clearInterval(timer);
  }
});

</script>

<style scoped lang="scss">
.chartsInfo {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background-color: white;
}
.cpuModeTag {
  justify-content: flex-start !important;
  text-align: left !important;
  width: 280px;
}
.cpuDetailTag {
  justify-content: flex-start !important;
  text-align: left !important;
  width: 140px;
  margin-top: 3px;
  margin-left: 1px;
}
.tagClass {
  margin-top: 3px;
  display: block;
  line-height: 20px;
}

.tagCPUClass {
  justify-content: flex-start !important;
  text-align: left !important;
  float: left;
  margin-top: 3px;
  margin-left: 1px;
  width: 140px;
}

.buttonClass {
  margin-top: 28%;
  font-size: 14px;
}
.nameTag {
  margin-top: 3px;
  height: auto;
  display: inline-block;
  white-space: normal;
  line-height: 1.8;
}
</style>
