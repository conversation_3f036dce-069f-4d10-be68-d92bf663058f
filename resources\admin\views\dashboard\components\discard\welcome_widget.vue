<template>
  <div class="protect piece-box activity-log-box mt-2.5">
    <div class="protectInfo">
      <div>
        <div class="fontTop">{{ $t('CFM.protect.yourWebsite') }}</div>
        <div class="fontTop">BINGO SECURITY SYSTEM {{ $t('CFM.protect.protect') }}</div>
        <div class="fontBot">{{ $t('CFM.protect.nextTakeCare') }}:{{ nextYear }}</div>
      </div>
      <div class="protectBottom">
        <div class="bottomItem">
          <div class="fontt">{{ today }}</div>
          <div class="fontb">{{ $t('CFM.protect.today') }}</div>
        </div>
        <div class="bottomItem">
          <div class="fontt">{{ total }}</div>
          <div class="fontb">{{ $t('CFM.protect.total') }}</div>
        </div>
        <div class="bottomItem">
          <div class="fontt">{{ scanning }}</div>
          <div class="fontb">{{ $t('CFM.protect.scanning') }}</div>
        </div>
        <div class="bottomItem">
          <div class="fontt">0</div>
          <div class="fontb">{{ $t('CFM.protect.danger') }}</div>
        </div>
      </div>
    </div>
    <div class="video">
      <video style="width: 100%; height: 100%" controls playsinline muted autoplay>
        <source src="https://www.hk-bingo.com/video/bingo.mp4" />
      </video>
    </div>
  </div>
</template>
  
  <script lang="ts" setup>
import { ref, onMounted } from 'vue'

const nextYear = ref('')
const today = ref(0)
const total = ref(0)
const scanning = ref(0)

function updateNextYear() {
  const date = new Date()
  const year = date.getFullYear() + 1
  const month = String(date.getMonth() + 1).padStart(2, '0')
  nextYear.value = `${month}/${year}`
}

function getTodayDateString() {
  const date = new Date()
  return date.toISOString().split('T')[0]
}

function updateToday() {
  const currentDate = getTodayDateString()
  const storedDate = localStorage.getItem('protect_today_date')
  if (storedDate !== currentDate) {
    const newTodayValue = Math.floor(Math.random() * 10) + 1
    today.value = newTodayValue
    total.value = 78 + newTodayValue
    localStorage.setItem('protect_today_date', currentDate)
    localStorage.setItem('protect_today_value', newTodayValue.toString())
  } else {
    today.value = parseInt(localStorage.getItem('protect_today_value') || '0', 10)
    total.value = 78 + today.value
  }
}

function updateScanning() {
  scanning.value = Math.floor(Math.random() * 15) + 1
}

onMounted(() => {
  updateNextYear()
  updateToday()
  updateScanning()
  setInterval(updateScanning, 2000)
})
</script>
  
  

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

.protect {
  width: 100%;
  height: 500px;
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #f0f2f5;
  font-family: 'Roboto', sans-serif;

  .protectInfo {
    width: 47%;
    height: 90%;
    margin: auto;
    background-color: #fff;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-self: start;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    .fontTop {
      font-size: 2em;
      color: #333;
      font-weight: 700;
      line-height: 1.2;
    }

    .fontBot {
      font-size: 1em;
      color: #666;
      font-weight: 400;
      line-height: 1.5;
      margin-bottom: 20px;
    }

    .protectBottom {
      width: 100%;
      height: auto;
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .bottomItem {
        width: 22%;
        height: 115px;
        cursor: pointer;
        background-color: #fff;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s, box-shadow 0.3s, background-color 0.3s;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
          background-color: #f9f9f9;
        }

        .fontt {
          font-size: 2em;
          color: #000;
          font-weight: 700;
          width: 100%;
          text-align: center;
          margin-top: 20px;
        }

        .fontb {
          font-size: 1em;
          color: #666;
          font-weight: 400;
          width: 100%;
          text-align: center;
          margin-top: 10px;
        }
      }
    }
  }

  .video {
    width: 50%;
    height: 90%;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;

    video {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }
}
</style>
