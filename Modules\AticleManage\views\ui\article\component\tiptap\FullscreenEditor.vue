<template>
  <div class="fullscreen-editor-container">
    <!-- 顶部导航栏 -->
    <div class="editor-header">
      <div class="header-left">
        <el-button class="exit-btn" text @click="handleExit">{{ t('Editor.fullscreenEditor.exit') }}</el-button>
        <el-select
          v-model="selectedTemplateType"
          :placeholder="t('Editor.fullscreenEditor.select_template_type')"
          size="small"
          class="template-type-select"
          :popper-class="'template-type-popper'"
        >
          <el-option v-for="type in templateTypes" :key="type.value" :label="type.label" :value="type.value">
            <!-- 简化只显示分类名 -->
            <span>{{ type.label }}</span>
          </el-option>
        </el-select>
        <!-- <el-dropdown trigger="click">
          <el-button class="dropdown-btn" text>
            文件 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>选项1</el-dropdown-item>
              <el-dropdown-item>选项2</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown trigger="click">
          <el-button class="dropdown-btn" text>
            编辑 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>选项1</el-dropdown-item>
              <el-dropdown-item>选项2</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown trigger="click">
          <el-button class="dropdown-btn" text>
            查看 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>版本历史记录</el-dropdown-item>
              <el-dropdown-item>页面详细信息</el-dropdown-item>
              <el-dropdown-item>预览 URL</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown trigger="click">
          <el-button class="dropdown-btn" text>
            设置 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>常规</el-dropdown-item>
              <el-dropdown-item>特色图像</el-dropdown-item>
              <el-dropdown-item>模板</el-dropdown-item>
              <el-dropdown-item>语言</el-dropdown-item>
              <el-dropdown-item disabled>
                受访访问 <el-icon><lock /></el-icon>
              </el-dropdown-item>
              <el-dropdown-item>高级</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown trigger="click">
          <el-button class="dropdown-btn" text>
            帮助 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>开始教程</el-dropdown-item>
              <el-dropdown-item>
                知识库 <el-icon><link /></el-icon>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </div>
      <div class="header-center">
        <div class="title-container">
          <div class="title" @click="openTitleDialog">{{ title || t('Editor.fullscreenEditor.no_title') }}</div>
          <el-button class="edit-title-btn" text @click="openTitleDialog">
            <el-icon><edit /></el-icon>
          </el-button>
        </div>
        <div class="draft-status">{{ statusText }}</div>
      </div>
      <div class="header-right">
        <!-- 添加编辑状态显示 -->
        <!-- <div class="editing-status" v-if="currentEditor">
          <el-tooltip 
            :content="'开始编辑时间: ' + formatTime(editStartTime)" 
            placement="bottom"
          >
            <div class="editor-info">
              <el-avatar 
                :size="24" 
                :src="currentEditor.avatar" 
                class="editor-avatar"
              >
                {{ getAvatarText(currentEditor.name) }}
              </el-avatar>
              <span class="editor-name">{{ currentEditor.name }}</span>
              <span class="editing-text">{{ $t('Editor.fullscreenEditor.editing') }}</span>
            </div>
          </el-tooltip>
        </div> -->
        <el-button class="preview-btn" type="success" @click="handlePreview">
          {{ t('Editor.fullscreenEditor.preview') }}
        </el-button>
        <!-- 添加模板类型下拉选择框 -->
       
        <el-button class="save-template-btn" type="warning" @click="handleSaveTemplate">
          <el-icon><document /></el-icon>
          {{ saveTemplateButtonText }}
        </el-button>
        <el-button type="primary" plain @click="handlePublish">{{ publishButtonText }}</el-button>
      </div>
    </div>

    <!-- 设备切换工具栏 -->
    <div class="device-toolbar">
      <div class="device-btns">
        <el-button-group>
                      <el-button :class="{ 'is-active': activeDevice === 'desktop' }" @click="activeDevice = 'desktop'" :title="t('Editor.fullscreenEditor.desktop_preview')">
              <el-icon><monitor /></el-icon>
            </el-button>
            <el-button :class="{ 'is-active': activeDevice === 'tablet' }" @click="activeDevice = 'tablet'" :title="t('Editor.fullscreenEditor.tablet_preview')">
              <el-icon><document /></el-icon>
            </el-button>
                         <el-button :class="{ 'is-active': activeDevice === 'mobile' }" @click="activeDevice = 'mobile'" :title="t('Editor.fullscreenEditor.mobile_preview')">
              <el-icon><cellphone /></el-icon>
            </el-button>
        </el-button-group>
      </div>
              <div class="toolbar-actions">
          <el-button class="action-btn" text>
            <el-tooltip :content="t('Editor.fullscreenEditor.editor_settings')" placement="bottom">
              <el-icon><setting /></el-icon>
            </el-tooltip>
          </el-button>
          <el-button class="action-btn" text @click="handleRefresh">
            <el-tooltip :content="t('Editor.fullscreenEditor.refresh_content')" placement="bottom">
              <el-icon><refresh /></el-icon>
            </el-tooltip>
          </el-button>
          <el-button class="action-btn" text @click="handleUndo" :disabled="!canUndo">
            <el-tooltip :content="t('Editor.fullscreenEditor.undo') + ' (Ctrl+Z)'" placement="bottom">
              <el-icon><back /></el-icon>
            </el-tooltip>
          </el-button>
          <el-button class="action-btn" text @click="handleRedo" :disabled="!canRedo">
            <el-tooltip :content="t('Editor.fullscreenEditor.redo') + ' (Ctrl+Y 或 Ctrl+Shift+Z)'" placement="bottom">
              <el-icon><right /></el-icon>
            </el-tooltip>
          </el-button>
        </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧边栏 - 根据是否选中块显示不同内容 -->
      <div class="sidebar-container">
        <!-- 模块选择侧边栏 - 当没有选中块时显示 -->
        <div class="module-sidebar" v-if="!selectedBlock">
          <div class="sidebar-header">
            <div class="sidebar-title">{{ t('Editor.fullscreenEditor.add_to_page') }}</div>
            <div class="search-container">
              <input v-model="searchQuery" type="text" class="search-input" :placeholder="t('Editor.fullscreenEditor.search')" />
            </div>
          </div>

          <div class="sidebar-tabs">
            <div class="tab-item" :class="{ active: activeTab === 'modules' }" @click="activeTab = 'modules'">{{ t('Editor.fullscreenEditor.modules') }}</div>
            <div class="tab-item" :class="{ active: activeTab === 'sections' }" @click="activeTab = 'sections'">{{ t('Editor.fullscreenEditor.sections') }}</div>
            <div class="tab-item" :class="{ active: activeTab === 'layouts' }" @click="activeTab = 'layouts'">{{ t('Editor.fullscreenEditor.layouts') }}</div>
          </div>

          <div class="module-list">
            <!-- 模块标签页 -->
            <div v-if="activeTab === 'modules'" class="tab-content">
              <div v-for="(category, index) in moduleCategories" :key="index" class="module-category">
                <div class="category-header" @click="category.expanded = !category.expanded">
                  <span class="expand-icon">{{ category.expanded ? '▼' : '▶' }}</span>
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-count">({{ category.count }})</span>
                </div>

                <div v-if="category.expanded" class="category-content">
                  <div class="module-grid">
                    <BootstrapModuleItem
                      v-for="(module, moduleIndex) in category.items"
                      :key="moduleIndex"
                      :module="module"
                      :isActive="selectedModule === module.type"
                      @select="selectModule"
                      @drag="handleModuleDrag"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 部分标签页 -->
            <div v-if="activeTab === 'sections'" class="tab-content">
              <div class="module-grid">
                <BootstrapModuleItem
                  v-for="(section, index) in filteredSections"
                  :key="index"
                  :module="{ ...section, name: section.name, description: section.description }"
                  :isActive="selectedModule === section.type"
                  @select="selectModule"
                  @drag="handleModuleDrag"
                />
              </div>
            </div>

            <!-- 布局标签页 -->
            <div v-if="activeTab === 'layouts'" class="tab-content">
              <div class="module-grid">
                <BootstrapModuleItem
                  v-for="(layout, index) in filteredLayouts"
                  :key="index"
                  :module="layout"
                  :isActive="selectedModule === layout.type"
                  @select="selectModule"
                  @drag="handleModuleDrag"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 块编辑侧边栏 - 当选中块时显示 -->
        <ModuleEditSidebar
          v-else
          :block-type="selectedBlockType"
          :block-element="selectedBlockElement"
          :block-styles="blockStyles"
          @close="closeBlockEdit"
          @update-block="updateSelectedBlock"
          @update-styles="updateBlockStyles"
        />
      </div>

      <div class="editor-content" :class="`${activeDevice}-preview`">
        <EditorContent v-if="editor" :editor="editor" class="editor-content-area bootstrap-enabled" />
        <div v-if="selectedModule" class="debug-info">{{ t('Editor.fullscreenEditor.current_selected_module') }}: {{ selectedModule }}</div>
      </div>
    </div>

    <!-- AI优化侧边栏，从editor-layout外部移出，放到fullscreen-editor-container内 -->
    <AIOptimizeSidebar
      v-if="showAiOptimizeSidebar"
      :visible="showAiOptimizeSidebar"
      :block-type="selectedBlockType"
      :block-element="selectedBlockElement"
      :block-content="selectedBlockContent"
      @close="showAiOptimizeSidebar = false"
      @apply-change="applyAiOptimizedContent"
      @update:visible="showAiOptimizeSidebar = $event"
    />

    <!-- AI优化悬浮按钮 -->
    <div class="ai-optimize-button" v-if="selectedBlock && !showAiOptimizeSidebar" @click="optimizeBlockWithAI">
      <el-tooltip :content="t('Editor.fullscreenEditor.ai_optimize_module')" placement="left">
        <el-icon><MagicStick /></el-icon>
      </el-tooltip>
    </div>
  </div>

  <!-- AI 菜单 -->
  <div v-if="aiMenuVisible" class="ai-dropdown-menu">
    <div class="ai-menu-header">{{ t('Editor.fullscreenEditor.ai_formatting_assistant') }}</div>
    <div class="ai-menu-item" @click="processAIRequest(AI_ACTIONS.SUMMARIZE)">{{ t('Editor.fullscreenEditor.ai_summarize') }}</div>
    <div class="ai-menu-item" @click="processAIRequest(AI_ACTIONS.IMPROVE)">{{ t('Editor.fullscreenEditor.ai_improve') }}</div>
    <div class="ai-menu-item" @click="processAIRequest(AI_ACTIONS.REWRITE)">{{ t('Editor.fullscreenEditor.ai_rewrite') }}</div>
    <div class="ai-menu-item" @click="processAIRequest(AI_ACTIONS.CORRECT)">{{ t('Editor.fullscreenEditor.ai_correct') }}</div>
    <div class="ai-menu-item" @click="showCustomPromptDialog">{{ t('Editor.fullscreenEditor.ai_custom_prompt') }}</div>
  </div>

  <!-- 自定义提示词对话框 -->
  <div v-if="customPromptDialogVisible" class="custom-prompt-dialog">
    <div class="custom-prompt-header">
      <h3>{{ t('Editor.fullscreenEditor.custom_ai_prompt') }}</h3>
      <button class="close-button" @click="customPromptDialogVisible = false">×</button>
    </div>
    <div class="custom-prompt-body">
      <textarea v-model="customPrompt" :placeholder="t('Editor.fullscreenEditor.enter_custom_prompt')" class="custom-prompt-textarea"></textarea>
    </div>
    <div class="custom-prompt-footer">
      <button @click="customPromptDialogVisible = false" class="cancel-button">{{ t('Editor.fullscreenEditor.cancel') }}</button>
      <button @click="processCustomAIPrompt" class="submit-button">{{ t('Editor.fullscreenEditor.submit') }}</button>
    </div>
  </div>

  <!-- AI 处理加载指示器 -->
  <div v-if="isAIProcessing" class="ai-processing-overlay">
    <div class="ai-processing-content">
      <div class="ai-loader"></div>
      <div class="ai-processing-text">{{ t('Editor.fullscreenEditor.ai_processing') }}</div>
    </div>
  </div>

  <!-- 块控制按钮 -->
  <BlockControls
    v-if="showBlockControls"
    :position="blockControlsPosition"
    @copy="copySelectedBlock"
    @delete="deleteSelectedBlock"
    @move-up="moveBlockUp"
    @move-down="moveBlockDown"
    @ai-optimize="showAiOptimize"
  />

  <!-- 预览抽屉 -->
  <el-drawer v-model="previewDrawerVisible" :title="$t('Editor.fullscreenEditor.content_preview')" direction="rtl" size="80%" destroy-on-close>
    <template #header>
      <div class="drawer-header">
        <h4 class="drawer-title">{{ t('Editor.fullscreenEditor.content_preview') }}</h4>
      </div>
    </template>
    <IframeRenderer :content="previewContent" width="100%" height="100%" />
  </el-drawer>

  <!-- 标题编辑弹窗 -->
  <el-dialog v-model="titleDialogVisible" :title="$t('Editor.fullscreenEditor.edit_title')" width="500px" :close-on-click-modal="false" :show-close="true" custom-class="title-dialog" append-to-body destroy-on-close>
    <el-form :model="titleForm" ref="titleFormRef" @submit.prevent label-position="top" class="title-form">
      <el-form-item
        :label="t('Editor.fullscreenEditor.page_title')"
        prop="title"
        :rules="[
          { required: true, message: t('Editor.fullscreenEditor.enter_title'), trigger: 'blur' },
          { min: 1, max: 50, message: t('Editor.fullscreenEditor.title_length_error'), trigger: 'blur' },
        ]"
      >
        <el-input v-model="titleForm.title" :placeholder="t('Editor.fullscreenEditor.enter_page_title')" maxlength="50" show-word-limit ref="titleInputRef" @keyup.enter="saveTitleDialog" clearable :autofocus="true">
          <template #prefix>
            <el-icon><edit-pen /></el-icon>
          </template>
        </el-input>
        <div class="form-item-tip">{{ t('Editor.fullscreenEditor.title_tip') }}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer" style="width: 100%; display: flex">
        <el-button @click="titleDialogVisible = false">{{ t('Editor.fullscreenEditor.cancel') }}</el-button>
        <el-button @click="saveTitleDialog">{{ t('Editor.fullscreenEditor.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, defineProps, inject, defineOptions, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Editor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/vue-3'
import type { Editor as EditorType } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import Underline from '@tiptap/extension-underline'
import Strike from '@tiptap/extension-strike'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Placeholder from '@tiptap/extension-placeholder'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import Highlight from '@tiptap/extension-highlight'
import FontFamily from '@tiptap/extension-font-family'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Typography from '@tiptap/extension-typography'
import Image from '@tiptap/extension-image'

import { ElMessage, ElDrawer, ElMessageBox, ElInput, ElDialog, ElForm, ElFormItem } from 'element-plus'
import {
  View,
  Back,
  Upload,
  Setting,
  ArrowDown,
  Edit,
  Search,
  Lock,
  Link as LinkIcon,
  Monitor,
  Cellphone,
  Refresh,
  Right,
  Document,
  Picture,
  Minus,
  Operation,
  Postcard,
  Timer,
  Menu,
  DataAnalysis,
  Money,
  Bell,
  Share,
  User,
  Flag,
  Grid,
  List,
  VideoCamera,
  EditPen,
  MagicStick,
} from '@element-plus/icons-vue'

// 使用动态导入解决模块导入错误
const BootstrapModuleItem = defineAsyncComponent(() => import('./components/BootstrapModuleItem.vue'))
const BlockControls = defineAsyncComponent(() => import('./components/BlockControls.vue'))
const ModuleEditSidebar = defineAsyncComponent(() => import('./components/ModuleEditSidebar.vue'))
const AIOptimizeSidebar = defineAsyncComponent(() => import('./components/AIOptimizeSidebar.vue'))
const IframeRenderer = defineAsyncComponent(() => import('./components/ContentRenderer.vue'))

import http from '/admin/support/http'
import { getTemplateByType } from './templates'
import {
  GridBlock,
  CtaBlock,
  ImageTextBlock,
  HeadlineBlock,
  HeadingBlock,
  BootstrapComponent,
  RichTextNode,
  AllowAttributes,
  ButtonBlock,
  LayoutBlock,
  CountdownBlock,
  FeatureListBlock,
  MetricsBlock,
  PricingBlock,
  RichTextBlock,
  SocialFlowBlock,
  TestimonialSliderBlock,
  TimelineBlock,
  StatsCardBlock,
  NavbarBlock,
  HeroBlock,
  FeatureCardsBlock,
  InfoSectionBlock,
  FooterBlock,
  ImageBlock,
  UlLiBlock,
  // 添加Bootstrap组件
  bootstrapComponents,
  BootstrapAlert,
  BootstrapCard,
  BootstrapContainer,
  BootstrapRow,
  BootstrapCol,
  BootstrapBadge,
  BootstrapBreadcrumb,
  BootstrapButton,
  BootstrapNav,
  BootstrapNavbar,
  BootstrapPagination,
  BootstrapTab,
  BootstrapForm,
  BootstrapInputGroup,
  BootstrapDropdown,
  BootstrapModal,
  BootstrapTooltip,
  BootstrapPopover,
  BootstrapProgress,
  BootstrapSpinner,
  BootstrapListGroup,
  BootstrapAccordion,
  BootstrapCarousel,
  BootstrapJumbotron,
  BootstrapMedia,
  BootstrapTable,
  BootstrapToast,
} from './extensions'

import { pageTemplates } from './pageTemplate' // 导入页面模板

// 添加缺失的导入
import { defineAsyncComponent } from 'vue'

// 初始化国际化
const { t } = useI18n()

// 类型定义
interface TemplateType {
  value: string
  label: string
  count: number
}

interface ContentDataItem {
  type: string
  attrs?: {
    content?: string
  }
}

interface ContentData {
  type: string
  content: ContentDataItem[]
}

// 定义props
const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '开始编辑内容...',
  },
})

// 添加bootstrap类型声明
declare global {
  interface Window {
    bootstrap: any
  }
}

// 添加当前激活的设备状态
const activeDevice = ref('desktop')

// 添加撤销/重做状态
const canUndo = ref(false)
const canRedo = ref(false)

const router = useRouter()
const route = useRoute()
const editor = ref<EditorType | null>(null)
const selectedModule = ref('')
const searchQuery = ref('')
const activeTab = ref('modules')
const aiMenuVisible = ref(false)
const customPromptDialogVisible = ref(false)
const customPrompt = ref('')
const isAIProcessing = ref(false)
const previewDrawerVisible = ref(false)
const previewContent = ref('')

// 获取路由参数中的模板ID
const templateId = computed(() => route.query.id as string | undefined)
// 根据是否有模板ID，显示不同的按钮文本
const saveTemplateButtonText = computed(() => (templateId.value ? '更新模板' : '存储为模板'))

// 添加CMS相关的路由参数获取
const cmsModelId = computed(() => route.query.model_id ? Number(route.query.model_id) : null)
const cmsContentId = computed(() => route.query.content_id ? Number(route.query.content_id) : null)
const cmsPageId = computed(() => route.query.page_id ? Number(route.query.page_id) : null)

// 计算发布按钮文本
const publishButtonText = computed(() => {
  if (cmsPageId.value && cmsPageId.value > 0) {
    return t('Editor.fullscreenEditor.update')
  }
  return t('Editor.fullscreenEditor.publish')
})

// 页面详情数据
const pageDetail = ref<any>(null)
const isEditMode = computed(() => cmsPageId.value && cmsPageId.value > 0)

// 计算状态文本
const statusText = computed(() => {
  if (isEditMode.value) {
    return t('Editor.fullscreenEditor.editing')
  }
  return t('Editor.fullscreenEditor.draft')
})

const aiButtonRef = ref<HTMLElement | null>(null)

// 选中块相关状态
const selectedBlock = ref<string | null>(null)
const selectedBlockType = ref('')
const selectedBlockElement = ref<HTMLElement | null>(null)
const showBlockControls = ref(false)
const blockControlsPosition = ref({ top: 0, left: 0 })
const blockEditTab = ref('content') // 'content', 'style', 'advanced'

// 按钮组数据
interface ButtonItem {
  text: string
  style: string
}

const buttonItems = ref<ButtonItem[]>([
  { text: t('Editor.fullscreenEditor.left'), style: 'primary' },
  { text: t('Editor.fullscreenEditor.center'), style: 'secondary' },
  { text: t('Editor.fullscreenEditor.right'), style: 'success' },
])

// 提示框数据
const alertContent = ref(t('Editor.fullscreenEditor.message.alert_content'))
const alertStyle = ref('primary')
const alertDismissible = ref(true)

// 卡片数据
const cardTitle = ref(t('Editor.fullscreenEditor.message.card_title'))
const cardContent = ref(t('Editor.fullscreenEditor.message.card_content'))
const cardImageUrl = ref('https://via.placeholder.com/300x150')
const cardButtonText = ref(t('Editor.fullscreenEditor.message.card_button_text'))
const cardButtonStyle = ref('primary')
const cardStyle = ref('default')

// 块样式
const blockStyles = ref({
  width: 'col-12 col-md-10 col-lg-8', // 使用Bootstrap类控制宽度
  textAlign: 'left',
  marginTop: 0,
  marginRight: 0,
  marginBottom: 0,
  marginLeft: 0,
  paddingTop: 0,
  paddingRight: 0,
  paddingBottom: 0,
  paddingLeft: 0,
})

// 高级设置
const advancedSettings = ref({
  className: '',
  id: '',
  customStyle: '',
})

// 定义模块项接口
interface ModuleItem {
  type: string
  name: string
  category?: string
  subCategory?: string
  iconClass?: string
  icon?: any // 添加icon属性支持Element Plus图标组件
  description?: string
  isLayoutModule?: boolean
}

// 定义模块分类接口
interface ModuleCategory {
  name: string
  expanded: boolean
  items: ModuleItem[]
  count?: number
}

// Bootstrap模块列表
const bootstrapModules: ModuleItem[] = [
  { type: 'bootstrap-alert', name: t('Editor.fullscreenEditor.alert'), category: 'modules', icon: Bell },
  { type: 'bootstrap-card', name: t('Editor.fullscreenEditor.card'), category: 'modules', icon: Postcard },
  { type: 'bootstrap-button-group', name: t('Editor.fullscreenEditor.button_group'), category: 'modules', icon: Operation },
  { type: 'bootstrap-nav', name: t('Editor.fullscreenEditor.navigation'), category: 'modules', icon: Menu },
  { type: 'bootstrap-form', name: t('Editor.fullscreenEditor.form'), category: 'modules', icon: Document },
  { type: 'bootstrap-carousel', name: t('Editor.fullscreenEditor.carousel'), category: 'modules', icon: Picture },
  { type: 'bootstrap-table', name: t('Editor.fullscreenEditor.table'), category: 'modules', icon: Grid },
  {
    type: 'bootstrap-accordion',
    name: t('Editor.fullscreenEditor.accordion'),
    category: t('Editor.fullscreenEditor.theme'),
    icon: Menu,
    description: t('Editor.fullscreenEditor.accordion_description'),
  },
  { type: 'bootstrap-a-class', name: t('Editor.fullscreenEditor.a_class_module'), category: 'modules', icon: DataAnalysis },
]

// 部分列表
const sections: ModuleItem[] = []

// 添加页面模板
const pageTemplateItems: ModuleItem[] = pageTemplates.map(template => ({
  type: template.id,
  name: template.name, // 直接用 index.ts 的 name 字段
  category: 'sections',
  icon: Document,
  description: template.description, // 直接用 index.ts 的 description 字段
}))

// 根据搜索过滤部分
const filteredSections = computed(() => {
  // 确保包含页面模板和sections
  if (!searchQuery.value) return [...sections, ...pageTemplateItems]
  return [...sections, ...pageTemplateItems].filter(section => section.name.toLowerCase().includes(searchQuery.value.toLowerCase()))
})

// 修改布局列表
const layouts: ModuleItem[] = [
  { type: 'layout-single', name: t('Editor.fullscreenEditor.single_column_layout'), category: 'layouts', icon: Document, isLayoutModule: true },
  { type: 'layout-two-column', name: t('Editor.fullscreenEditor.two_column_layout'), category: 'layouts', icon: Document, isLayoutModule: true },
  { type: 'layout-three-column', name: t('Editor.fullscreenEditor.three_column_layout'), category: 'layouts', icon: Document, isLayoutModule: true },
  { type: 'layout-1-3-2-3', name: t('Editor.fullscreenEditor.left_1_3_right_2_3'), category: 'layouts', icon: Document, isLayoutModule: true },
  { type: 'layout-2-3-1-3', name: t('Editor.fullscreenEditor.left_2_3_right_1_3'), category: 'layouts', icon: Document, isLayoutModule: true },
  { type: 'layout-four-column', name: t('Editor.fullscreenEditor.four_column_layout'), category: 'layouts', icon: Document, isLayoutModule: true },
]

// 修改模块列表数据结构
const moduleCategories = ref<ModuleCategory[]>(
  [
    {
      name: t('Editor.fullscreenEditor.theme'),
      expanded: false,
      items: [
        {
          type: 'richTextBlock',
          name: t('Editor.fullscreenEditor.rich_text'),
          category: t('Editor.fullscreenEditor.theme'),
          subCategory: t('Editor.fullscreenEditor.theme'),
          icon: Document,
        },
        { type: 'bootstrap-heading', name: t('Editor.fullscreenEditor.heading'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Document },
        { type: 'bootstrap-accordion', name: t('Editor.fullscreenEditor.accordion'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Menu },
        { type: 'bootstrap-divider', name: t('Editor.fullscreenEditor.divider'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Minus },
        { type: 'bootstrap-button', name: t('Editor.fullscreenEditor.button'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Operation },
        { type: 'bootstrap-card', name: t('Editor.fullscreenEditor.card'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Postcard },
        { type: 'bootstrap-countdown', name: t('Editor.fullscreenEditor.countdown'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Timer },
        { type: 'bootstrap-feature-list', name: t('Editor.fullscreenEditor.feature_list'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Menu },
        { type: 'bootstrap-metrics', name: t('Editor.fullscreenEditor.metrics'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: DataAnalysis },
        { type: 'bootstrap-pricing', name: t('Editor.fullscreenEditor.pricing'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Money },
        { type: 'bootstrap-cta', name: t('Editor.fullscreenEditor.cta'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Bell },
        { type: 'socialFlowBlock', name: t('Editor.fullscreenEditor.social_media'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Share },
        { type: 'testimonialSliderBlock', name: t('Editor.fullscreenEditor.customer_testimonial'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: User },
        { type: 'timelineBlock', name: t('Editor.fullscreenEditor.timeline'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Timer },
        { type: 'statsCardBlock', name: t('Editor.fullscreenEditor.stats_card'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: DataAnalysis },
        { type: 'navbarBlock', name: t('Editor.fullscreenEditor.navbar'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Menu },
        { type: 'heroBlock', name: t('Editor.fullscreenEditor.hero_section'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Flag },
        { type: 'featureCardsBlock', name: t('Editor.fullscreenEditor.feature_cards'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Grid },
        { type: 'infoSectionBlock', name: t('Editor.fullscreenEditor.info_section'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: List },
        { type: 'footerBlock', name: t('Editor.fullscreenEditor.footer'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: List },
        { type: 'bootstrap-image', name: t('Editor.fullscreenEditor.image'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Picture },
        { type: 'team-block', name: t('Editor.fullscreenEditor.team_showcase'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: User },
        { type: 'partners', name: t('Editor.fullscreenEditor.partners'), category: t('Editor.fullscreenEditor.theme'), subCategory: t('Editor.fullscreenEditor.theme'), icon: Share },
      ],
    },
    { name: t('Editor.fullscreenEditor.text'), expanded: true, items: [] },
    { name: t('Editor.fullscreenEditor.commerce'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.design'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.functionality'), expanded: true, items: [] },
    { name: t('Editor.fullscreenEditor.forms_and_buttons'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.body_content'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.media'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.blog'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.social'), expanded: false, items: [] },
    { name: t('Editor.fullscreenEditor.all'), expanded: false, items: [] },
  ].map(category => ({
    ...category,
    count: category.items.length,
  })),
)

// 原有的模块分类列表保留，但不再直接使用
// 替换模块筛选逻辑
const filteredModules = computed(() => {
  if (!searchQuery.value) {
    // 返回所有与当前activeTab相关的模块
    return bootstrapModules.filter(module => module.category === activeTab.value)
  }
  return bootstrapModules.filter(module => module.category === activeTab.value && module.name.toLowerCase().includes(searchQuery.value.toLowerCase()))
})

// 根据搜索过滤布局
const filteredLayouts = computed(() => {
  if (!searchQuery.value) return layouts
  return layouts.filter(layout => layout.name.toLowerCase().includes(searchQuery.value.toLowerCase()))
})

// 选择模块
const selectModule = (moduleType: string) => {
  selectedModule.value = moduleType

  // 获取对应的HTML模板
  let html = null
  // 检查是否为页面模板
  const pageTemplate = pageTemplates.find(template => template.id === moduleType)
  if (pageTemplate) {
    html = pageTemplate.template
  } else {
    html = getTemplateByType(moduleType)
  }

  if (html) {
    // 如果需要立即插入模块，可以调用insertBootstrapModule函数
    // insertBootstrapModule(moduleType)
  } else {
    console.warn(t('Editor.fullscreenEditor.template_not_found') + ':', moduleType)
  }
}

// 处理模块拖拽
const handleModuleDrag = (moduleType: string) => {
  // 设置选中的模块，这样当拖放到编辑器时可以直接使用
  selectedModule.value = moduleType

  // 不在这里插入HTML内容，而是等待drop事件
}

// AI 操作类型
const AI_ACTIONS = {
  SUMMARIZE: 'summarize',
  IMPROVE: 'improve',
  REWRITE: 'rewrite',
  CORRECT: 'correct',
  CUSTOM: 'custom',
}

const closeAIMenu = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const isAIButton = target.closest('.ai-button')
  const isAIMenu = target.closest('.ai-dropdown-menu')

  if (!isAIButton && !isAIMenu) {
    aiMenuVisible.value = false
    document.removeEventListener('click', closeAIMenu)
  }
}

const processAIRequest = async (action: string, prompt?: string) => {
  aiMenuVisible.value = false
  customPromptDialogVisible.value = false

  if (!editor.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.editor_not_initialized'))
    return
  }

  // 获取处理内容
  let content = ''
  let contentType = 'text'

  const selection = editor.value.state.selection

  if (!selection.empty) {
    // 文本选择
    const { from, to } = selection
    content = editor.value.state.doc.textBetween(from, to, ' ')
    contentType = 'text'
  } else {
    ElMessage.warning(t('Editor.ai.no_text_selected'))
    return
  }

  if (!content || content.trim() === '') {
    ElMessage.warning(t('Editor.fullscreenEditor.message.selected_content_empty'))
    return
  }

  try {
    // 构建请求参数
    const requestData = {
      content: content,
      contentType: contentType, // 新增，标识内容类型
      style: {
        action: action,
        // 只有当action是自定义(custom)时才设置prompt
        ...(action === AI_ACTIONS.CUSTOM && prompt ? { prompt } : {}),
      },
      lang: localStorage.getItem('activeName') || 'zh_CN',
    }

    isAIProcessing.value = true

    // 调用接口 - 可能需要根据内容类型使用不同的端点
    const endpoint = '/seo/format/block'

    const response = await http.post(endpoint, requestData)

    if (response.data) {
      // 处理文本响应 - 替换选中的内容
      const { from, to } = selection
      editor.value.chain().focus().deleteRange({ from, to }).run()
      editor.value.chain().focus().insertContent(response.data.data.content).run()

      ElMessage.success(t('Editor.fullscreenEditor.message.ai_apply_success'))
    } else {
      throw new Error(response.data?.message || t('Editor.fullscreenEditor.message.processing_failed'))
    }
  } catch (error) {
    console.error('AI 处理错误:', error)
    ElMessage.error(`${t('Editor.fullscreenEditor.message.ai_apply_failed')}: ${error instanceof Error ? error.message : t('Editor.preview.unknown_error')}`)
  } finally {
    isAIProcessing.value = false
  }
}

const showCustomPromptDialog = () => {
  aiMenuVisible.value = false
  customPromptDialogVisible.value = true
  customPrompt.value = ''

  nextTick(() => {
    const textarea = document.querySelector('.custom-prompt-textarea') as HTMLTextAreaElement
    if (textarea) {
      textarea.focus()
    }
  })
}

const processCustomAIPrompt = () => {
  if (!customPrompt.value.trim()) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.please_enter_custom_prompt'))
    return
  }

  processAIRequest(AI_ACTIONS.CUSTOM, customPrompt.value)
}

// 预览编辑器内容
const handlePreview = () => {
  if (!editor.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.editor_not_initialized'))
    return
  }
  console.log('editor.getHTML()', JSON.stringify(editor.value.getJSON()))
  // 不再直接使用editor.getHTML()获取内容
  // 而是从DOM中获取实际渲染的内容
  const editorDOM = document.querySelector('.editor-content-area')
  if (!editorDOM) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.content_empty', { action: t('Editor.fullscreenEditor.preview') }))
    return
  }

  // 获取编辑器实际渲染的HTML内容
  const content = editorDOM.innerHTML

  // 克隆内容用于预览，保留所有Bootstrap属性和类
  const previewDOM = document.createElement('div')
  previewDOM.innerHTML = content

  // 移除编辑状态相关的类和属性
  const selectedElements = previewDOM.querySelectorAll('.bs-component-selected')
  selectedElements.forEach(el => {
    el.classList.remove('bs-component-selected')
  })

  // 移除调试信息
  const debugInfos = previewDOM.querySelectorAll('.debug-info')
  debugInfos.forEach(el => {
    el.remove()
  })

  // 移除编辑器工具和控件
  const editorControls = previewDOM.querySelectorAll('.draggable-handle, .width-control, .responsive-preview-indicator')
  editorControls.forEach(el => {
    el.remove()
  })

  // 移除布局容器辅助标记
  const layoutContainers = previewDOM.querySelectorAll('[data-layout-container]')
  layoutContainers.forEach(container => {
    // 保留data-layout-container属性，但移除可能的临时样式
    container.classList.remove('bg-light', 'border')
    container.removeAttribute('data-bg-removed')
    container.removeAttribute('style')
  })

  // 处理所有Bootstrap组件，移除编辑状态相关属性
  const bsComponents = previewDOM.querySelectorAll('[data-bs-component]')
  bsComponents.forEach(component => {
    // 移除编辑相关属性但保留组件类型标记
    component.removeAttribute('data-bs-processed')

    // 移除内联编辑样式，但保留必要的Bootstrap样式
    if (component.hasAttribute('style')) {
      const style = component.getAttribute('style') || ''
      const newStyle = style
        .replace(/border.*?;/g, '')
        .replace(/outline.*?;/g, '')
        .replace(/box-shadow.*?;/g, '')

      if (newStyle.trim()) {
        component.setAttribute('style', newStyle)
      } else {
        component.removeAttribute('style')
      }
    }
  })

  // 收集页面中所有样式并添加到预览中
  const allStyles = document.querySelectorAll('style')
  const styleContainer = document.createElement('div')

  allStyles.forEach(styleTag => {
    const styleContent = styleTag.textContent || ''
    // 只复制与Bootstrap组件相关的样式
    if (styleContent.includes('[data-bs-component]') || styleContent.includes('.navbar-section') || styleContent.includes('.marketing-landing-page') || styleContent.includes('.bootstrap-')) {
      const newStyle = document.createElement('style')
      newStyle.textContent = styleContent
      styleContainer.appendChild(newStyle)
    }
  })

  // 将样式添加到预览内容的前面
  previewContent.value = styleContainer.innerHTML + previewDOM.innerHTML

  // 打开预览抽屉
  previewDrawerVisible.value = true
}

// 创建和更新Bootstrap容器的函数
const createOrUpdateBootstrapContainer = (container: Element) => {
  // 获取所有Bootstrap组件
  const bootstrapComponents = container.querySelectorAll('[data-bs-component]')

  // 如果没有Bootstrap组件，不需要特殊处理
  if (bootstrapComponents.length === 0) {
    return null
  }

  // 确保编辑器容器有bootstrap类，这样可以确保Bootstrap样式正确应用
  container.classList.add('bootstrap')

  // 直接处理原始组件，不再创建容器和克隆
  bootstrapComponents.forEach(component => {
    // 确保组件有正确的属性
    component.setAttribute('data-bs-processed', 'true')

    // 找到所有布局容器并检查它们是否已经被处理过
    if (component.getAttribute('data-bs-component') === 'layout') {
      const layoutContainers = component.querySelectorAll('[data-layout-container]')
      layoutContainers.forEach(layoutContainer => {
        // 如果布局容器已被标记为移除背景色，则确保它保持无背景色状态
        if (layoutContainer.getAttribute('data-bg-removed') === 'true') {
          layoutContainer.classList.remove('bg-light', 'border')
        }
      })
    }

    // 不添加任何内联样式，让Bootstrap CSS完全控制样式
    // 只添加点击事件监听器
    component.addEventListener('click', e => {
      // 阻止事件冒泡，避免触发编辑器的选择
      e.stopPropagation()
    })
  })

  // 手动初始化Bootstrap组件
  if (typeof window !== 'undefined' && window.bootstrap) {
    setTimeout(() => {
      try {
        // 初始化所有tooltips
        const tooltipTriggerList = container.querySelectorAll('[data-bs-toggle="tooltip"]')
        if (tooltipTriggerList.length > 0) {
          Array.from(tooltipTriggerList).forEach(tooltipTriggerEl => {
            new window.bootstrap.Tooltip(tooltipTriggerEl)
          })
        }

        // 初始化所有popovers
        const popoverTriggerList = container.querySelectorAll('[data-bs-toggle="popover"]')
        if (popoverTriggerList.length > 0) {
          Array.from(popoverTriggerList).forEach(popoverTriggerEl => {
            new window.bootstrap.Popover(popoverTriggerEl)
          })
        }

        // 初始化所有可关闭的提示框
        const alertElements = container.querySelectorAll('.alert.alert-dismissible')
        alertElements.forEach(alert => {
          const closeBtn = alert.querySelector('.btn-close')
          if (closeBtn) {
            closeBtn.addEventListener('click', e => {
              e.preventDefault()
              e.stopPropagation()
              alert.classList.remove('show')
              setTimeout(() => {
                if (alert.parentNode) {
                  alert.parentNode.removeChild(alert)
                }
              }, 150)
            })
          }
        })
      } catch (error) {
        console.error('初始化Bootstrap组件时出错:', error)
      }
    }, 50)
  }

  return null // 不再返回容器，因为我们直接处理原始组件
}

// 监听编辑器内容变化，重新初始化Bootstrap组件
const handleEditorContentChange = () => {
  // 延迟执行，确保DOM已更新
  setTimeout(() => {
    initBootstrapComponents()
    // 更新撤销/重做状态
    updateHistoryState()
  }, 100)
}

// 更新历史状态
const updateHistoryState = () => {
  if (!editor.value) return

  // 检查是否可以撤销
  canUndo.value = editor.value.can().undo()

  // 检查是否可以重做
  canRedo.value = editor.value.can().redo()
}

// 处理撤销操作
const handleUndo = () => {
  if (!editor.value) return

  editor.value.chain().focus().undo().run()
  updateHistoryState()
  ElMessage.info(t('Editor.fullscreenEditor.message.undo_info'))
}

// 处理重做操作
const handleRedo = () => {
  if (!editor.value) return

  editor.value.chain().focus().redo().run()
  updateHistoryState()
  ElMessage.info(t('Editor.fullscreenEditor.message.redo_info'))
}

// 处理刷新操作
const handleRefresh = () => {
  if (!editor.value) return

  // 使用Element Plus的确认对话框
  ElMessageBox.confirm(t('Editor.fullscreenEditor.refresh_confirm'), t('Editor.fullscreenEditor.refresh_confirm_title'), {
    confirmButtonText: t('Editor.fullscreenEditor.confirm'),
    cancelButtonText: t('Editor.fullscreenEditor.cancel'),
    type: 'warning',
  })
    .then(() => {
      // 重新初始化编辑器内容
      if (editor.value) {
        editor.value.commands.clearContent()

        // 如果有初始内容，则恢复初始内容
        if (props.content && editor.value) {
          editor.value.commands.setContent(props.content)
        }
      }

      // 更新历史状态
      updateHistoryState()
      ElMessage.success(t('Editor.fullscreenEditor.refresh_success'))
    })
    .catch(() => {
      // 用户取消操作
      ElMessage.info(t('Editor.fullscreenEditor.refresh_canceled'))
    })
}

// 确保Bootstrap CSS已加载
const ensureBootstrapCssLoaded = () => {
  // 检查是否已加载Bootstrap CSS
  const bootstrapCssExists = document.querySelector('link[href*="bootstrap"]')
  if (!bootstrapCssExists) {
    const cssLink = document.createElement('link')
    cssLink.rel = 'stylesheet'
    cssLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css'
    cssLink.integrity = 'sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9'
    cssLink.crossOrigin = 'anonymous'
    document.head.appendChild(cssLink)
  }

  // 检查是否已加载Bootstrap JS
  if (typeof window.bootstrap === 'undefined') {
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js'
    script.integrity = 'sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm'
    script.crossOrigin = 'anonymous'
    document.head.appendChild(script)
  }
}

// 插入Bootstrap模块
const insertBootstrapModule = (moduleType: string) => {
  if (!editor.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.editor_not_initialized'))
    return
  }

  // 首先检查是否为页面模板
  let html = null
  let isPageTemplate = false

  // 检查是否为整页模板
  const pageTemplate = pageTemplates.find(template => template.id === moduleType)
  if (pageTemplate) {
    html = pageTemplate.template
    isPageTemplate = true
  } else {
    // 否则从常规模板中获取
    html = getTemplateByType(moduleType)
  }

  if (!html) {
    ElMessage.warning(t('Editor.fullscreenEditor.template_not_found'))
    return
  }

  // 检查是否为布局模块
  const allModules = [...bootstrapModules, ...sections, ...layouts]
  const moduleInfo = allModules.find(m => m.type === moduleType)
  const isLayoutModule = moduleInfo?.isLayoutModule || false

  if (isPageTemplate) {
    // 如果是页面模板，直接替换整个编辑器内容
    editor.value.chain().focus().setContent(html).run()
    ElMessage.success(t('Editor.fullscreenEditor.add_success', { module: getModuleName(moduleType) }))
  } else if (isLayoutModule) {
    // 如果是布局模块，使用特殊的布局块命令
    editor.value.chain().focus().insertLayoutBlock({ html }).run()
    ElMessage.success(t('Editor.fullscreenEditor.add_success', { module: getModuleName(moduleType) }))
  } else if (moduleType.startsWith('bootstrap-')) {
    // 如果是Bootstrap组件，使用特殊的Bootstrap组件命令
    editor.value.chain().focus().insertBootstrapComponent({ html }).run()
    ElMessage.success(t('Editor.fullscreenEditor.add_success', { module: getModuleName(moduleType) }))
  } else {
    // 其他类型直接插入内容
    editor.value.chain().focus().insertContent(html).run()
      ElMessage.success(t('Editor.fullscreenEditor.add_success', { module: getModuleName(moduleType) }))
  }

  // 初始化Bootstrap组件
  setTimeout(() => {
    initBootstrapComponents()
  }, 100)
}

// 插入表格
const insertTable = () => {
  if (!editor.value) return
  editor.value.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

// 表格操作函数
const addColumnBefore = () => {
  if (!editor.value) return
  editor.value.chain().focus().addColumnBefore().run()
}

const addColumnAfter = () => {
  if (!editor.value) return
  editor.value.chain().focus().addColumnAfter().run()
}

const deleteColumn = () => {
  if (!editor.value) return
  editor.value.chain().focus().deleteColumn().run()
}

const addRowBefore = () => {
  if (!editor.value) return
  editor.value.chain().focus().addRowBefore().run()
}

const addRowAfter = () => {
  if (!editor.value) return
  editor.value.chain().focus().addRowAfter().run()
}

const deleteRow = () => {
  if (!editor.value) return
  editor.value.chain().focus().deleteRow().run()
}

const deleteTable = () => {
  if (!editor.value) return
  editor.value.chain().focus().deleteTable().run()
}

const mergeCells = () => {
  if (!editor.value) return
  editor.value.chain().focus().mergeCells().run()
}

const splitCell = () => {
  if (!editor.value) return
  editor.value.chain().focus().splitCell().run()
}

const toggleHeaderCell = () => {
  if (!editor.value) return
  editor.value.chain().focus().toggleHeaderCell().run()
}

// 更新内容
const updateContent = () => {
  if (!editor.value) return
  const html = editor.value.getHTML()
  // 触发更新事件
  emit('update:modelValue', html)
  emit('update:content', html)
}

// 文件上传处理
const handleFileUpload = async (files: File[]) => {
  if (!editor.value || !files.length) return

  for (const file of files) {
    if (file.type.startsWith('image/')) {
      // 处理图片文件
      try {
        // 创建一个临时URL以便立即显示图片
        const url = URL.createObjectURL(file)

        // 插入图片到编辑器
        editor.value.chain().focus().setImage({ src: url, alt: file.name }).run()

        // 这里可以添加实际的文件上传逻辑
        // const formData = new FormData()
        // formData.append('file', file)
        // const response = await http.post('/upload', formData)
        // const imageUrl = response.data.url
        // 然后可以更新图片的URL
      } catch (error) {
        console.error('图片上传失败:', error)
          ElMessage.error(t('Editor.fullscreenEditor.image_upload_failed'))
      }
    } else {
      ElMessage.warning(t('Editor.fullscreenEditor.unsupported_file_type', { type: file.type }))
    }
  }
}

// 拖放处理
const handleDrop = (event: DragEvent) => {
  if (!editor.value || !event.dataTransfer) return

  // 处理文件拖放
  if (event.dataTransfer.files.length > 0) {
    const files = Array.from(event.dataTransfer.files)
    handleFileUpload(files)
    return
  }

  // 输出所有可用数据类型
  const availableTypes = Array.from(event.dataTransfer.types || [])

  // 尝试从多种数据类型中获取模块类型
  let moduleType = ''

  // 优先使用自定义数据类型
  if (availableTypes.includes('application/bootstrap-component')) {
    moduleType = event.dataTransfer.getData('application/bootstrap-component')
  }
  // 其次使用text/plain
  else if (availableTypes.includes('text/plain')) {
    moduleType = event.dataTransfer.getData('text/plain')
  }

  // 如果找到了模块类型
  if (moduleType) {
    // 检查是否为页面模板
    let isPageTemplate = false
    let html = null

    // 检查是否为整页模板
    const pageTemplate = pageTemplates.find(template => template.id === moduleType)
    if (pageTemplate) {
      html = pageTemplate.template
      isPageTemplate = true
    } else {
      // 否则从常规模板中获取
      html = getTemplateByType(moduleType)
    }

    if (html) {
      // 检查是否为页面模板
      if (isPageTemplate) {
        // 如果是页面模板，直接替换整个编辑器内容
        editor.value.chain().focus().setContent(html).run()
        ElMessage.success(`已添加${getModuleName(moduleType)}`)

        // 初始化Bootstrap组件
        setTimeout(() => {
          initBootstrapComponents()

          // 特殊处理页面模板中的导航栏
          const navbars = document.querySelectorAll('nav[data-bs-component="navbar"]')
          if (navbars.length > 0) {
            navbars.forEach(navbar => {
              // 检查导航栏是否有内部样式
              const hasInternalStyle = navbar.querySelector('style') !== null

              // 如果没有内部样式，检查页面中的样式
              if (!hasInternalStyle) {
                // 查找页面中所有style标签
                const allStyles = document.querySelectorAll('style')

                // 提取所有与导航栏相关的样式规则
                let navbarStyleContent = ''

                // 遍历所有style标签，查找与navbar相关的样式
                allStyles.forEach(styleTag => {
                  const styleContent = styleTag.textContent || ''

                  // 检查样式是否包含导航栏相关的选择器
                  if (styleContent.includes('.navbar-section') || styleContent.includes('[data-bs-component="navbar"]') || styleContent.includes('nav[data-bs-component="navbar"]')) {
                    // 提取所有与导航栏相关的CSS规则
                    const cssRules = styleContent.match(/\.navbar-section[^}]+\}|nav\[data-bs-component="navbar"\][^}]+\}|\.navbar-section\s+[^}]+\}|\.navbar-section\.[^}]+\}/g)

                    if (cssRules && cssRules.length > 0) {
                      cssRules.forEach(rule => {
                        navbarStyleContent += rule + '\n'
                      })
                    }
                  }

                  // 检查是否有包含在页面模板中的导航栏样式
                  if (styleContent.includes('.marketing-landing-page .navbar-section') || styleContent.includes('.marketing-landing-page nav[data-bs-component="navbar"]')) {
                    // 提取所有与页面模板中导航栏相关的CSS规则
                    const cssRules = styleContent.match(/\.marketing-landing-page\s+\.navbar-section[^}]+\}|\.marketing-landing-page\s+nav\[data-bs-component="navbar"\][^}]+\}/g)

                    if (cssRules && cssRules.length > 0) {
                      cssRules.forEach(rule => {
                        // 移除页面模板前缀，使样式直接应用于导航栏
                        const modifiedRule = rule.replace(/\.marketing-landing-page\s+/g, '')
                        navbarStyleContent += modifiedRule + '\n'
                      })
                    }
                  }

                  // 检查是否有导航栏子元素的样式
                  const subSelectors = [
                    '.navbar-wrapper',
                    '.navbar-brand',
                    '.navbar-links',
                    '.nav-link',
                    '.navbar-section .btn',
                    '.navbar-section .btn-primary',
                    '.navbar-section .btn-outline-primary',
                  ]

                  subSelectors.forEach(selector => {
                    const regex = new RegExp(
                      `\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}|\\.marketing-landing-page\\s+\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}`,
                      'g',
                    )
                    const matches = styleContent.match(regex)

                    if (matches && matches.length > 0) {
                      matches.forEach(match => {
                        // 移除页面模板前缀，保留导航栏前缀
                        const modifiedRule = match.replace(/\.marketing-landing-page\s+/g, '')
                        navbarStyleContent += modifiedRule + '\n'
                      })
                    }
                  })
                })

                // 如果找到了相关样式，添加到导航栏中
                if (navbarStyleContent) {
                  const styleElement = document.createElement('style')
                  styleElement.textContent = navbarStyleContent
                  navbar.appendChild(styleElement)
                }
              }
            })
          }
        }, 100)
        return
      }

      // 检查是否拖放到了布局模块内部
      let targetLayoutElement = null
      let dropContainer = null

      // 获取拖放目标元素
      let target = event.target as HTMLElement

      // 向上查找直到找到布局容器
      while (target && !targetLayoutElement) {
        if (target.hasAttribute && target.hasAttribute('data-layout-container')) {
          dropContainer = target
          // 继续向上查找布局容器的父级
          let parent = target.parentElement
          while (parent) {
            if (parent.hasAttribute && parent.hasAttribute('data-bs-component') && parent.getAttribute('data-bs-component') === 'layout') {
              targetLayoutElement = parent
              break
            }
            parent = parent.parentElement
          }
        } else if (target.parentElement) {
          target = target.parentElement
        } else {
          break
        }
      }

      // 检查是否为布局模块
      const allModules = [...bootstrapModules, ...sections, ...layouts]
      const moduleInfo = allModules.find(m => m.type === moduleType)
      const isLayoutModule = moduleInfo?.isLayoutModule || false

      if (isLayoutModule) {
        // 如果是布局模块，使用layoutBlock命令
        editor.value.chain().focus().insertLayoutBlock({ html }).run()
        // 不在这里移除背景色，等待用户向布局中添加内容时才移除
      } else if (dropContainer) {
        // 创建临时div来保存HTML内容
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = html

        // 取出内部元素并添加到布局容器
        while (tempDiv.firstChild) {
          dropContainer.appendChild(tempDiv.firstChild)
        }

        // 当向布局容器中添加内容后，移除该容器的背景色
        dropContainer.classList.remove('bg-light', 'border')
        // 添加标记，表示该容器已经处理过背景色
        dropContainer.setAttribute('data-bg-removed', 'true')

        // 更新编辑器内容
        updateEditorContent()
      } else {
        // 正常插入Bootstrap组件
        if (moduleType.startsWith('bootstrap-')) {
          editor.value.chain().focus().insertBootstrapComponent({ html }).run()
        } else {
          // 其他类型的内容直接插入
          editor.value.chain().focus().insertContent(html).run()
        }
      }

      // 初始化Bootstrap组件
      setTimeout(() => {
        initBootstrapComponents()
      }, 100)

      // 显示成功消息
      ElMessage.success(`已插入${getModuleName(moduleType)}`)
    } else {
      ElMessage.warning(t('Editor.fullscreenEditor.template_not_found') + ': ' + moduleType)
    }
  } else {
    // 处理普通拖放
    // ...默认拖放处理逻辑
  }
}

// 更新编辑器内容
const updateEditorContent = () => {
  if (!editor.value) return

  try {
    // 获取编辑器DOM元素
    const editorContent = document.querySelector('.editor-content-area')
    if (editorContent) {
      // 获取当前编辑器内容
      const html = editorContent.innerHTML

      // 延迟更新，确保DOM变更已完成
      setTimeout(() => {
        // 使用命令设置内容，false参数表示不合并历史记录
        editor.value?.commands.setContent(html, false)

        // 通知更新
        emit('update:modelValue', html)
        emit('update:content', html)
      }, 10)
    }
  } catch (error) {
    console.error('更新编辑器内容时出错:', error)
  }
}

// 处理粘贴事件
const handlePaste = (pasteEvent: any) => {
  if (!editor.value) return

  // 从Tiptap事件中获取原始ClipboardEvent
  const event = pasteEvent.event as ClipboardEvent
  if (!event || !event.clipboardData) return

  // 检查是否有文件
  const items = Array.from(event.clipboardData.items || [])
  const files: File[] = []

  for (const item of items) {
    if (item.kind === 'file') {
      const file = item.getAsFile()
      if (file) files.push(file)
    }
  }

  if (files.length > 0) {
    handleFileUpload(files)
    event.preventDefault()
  }
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 处理特殊键盘快捷键
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        editor.value?.chain().focus().toggleBold().run()
        break
      case 'i':
        event.preventDefault()
        editor.value?.chain().focus().toggleItalic().run()
        break
      case 'u':
        event.preventDefault()
        editor.value?.chain().focus().toggleUnderline().run()
        break
      case 'z':
        // 撤销操作 (Ctrl+Z)
        if (!event.shiftKey) {
          event.preventDefault()
          if (editor.value?.can().undo()) {
            handleUndo()
          }
        }
        // 重做操作 (Ctrl+Shift+Z)
        else {
          event.preventDefault()
          if (editor.value?.can().redo()) {
            handleRedo()
          }
        }
        break
      case 'y':
        // 重做操作 (Ctrl+Y)
        event.preventDefault()
        if (editor.value?.can().redo()) {
          handleRedo()
        }
        break
    }
  }
}

// 初始化Bootstrap组件
const initBootstrapComponents = () => {
  nextTick(() => {
    try {
      // 获取所有编辑器容器
      const editorContainers = document.querySelectorAll('.editor-content-area.bootstrap-enabled')
      if (editorContainers.length === 0) {
        return
      }
      // 确保Bootstrap CSS已加载
      ensureBootstrapCssLoaded()
      // 为每个编辑器容器处理Bootstrap组件
      editorContainers.forEach(container => {
        createOrUpdateBootstrapContainer(container)

        // 为所有Bootstrap组件添加点击事件
        const bsComponents = container.querySelectorAll('[data-bs-component]')
        if (bsComponents.length > 0) {
          bsComponents.forEach(comp => {
            // 获取组件类型
            const componentType = comp.getAttribute('data-bs-component')

            // 移除可能已存在的事件监听器
            if (componentType === 'layout') {
              comp.removeEventListener('click', handleLayoutClick as EventListener)
              comp.addEventListener('click', handleLayoutClick as EventListener)

              // 设置布局容器的拖拽监听
              const layoutContainers = comp.querySelectorAll('[data-layout-container]')
              layoutContainers.forEach(layoutContainer => {
                // 确保layoutContainer是HTMLElement类型
                const container = layoutContainer as HTMLElement

                container.removeEventListener('dragover', handleLayoutDragOver as EventListener)
                container.removeEventListener('dragleave', handleLayoutDragLeave as EventListener)
                container.removeEventListener('drop', handleLayoutDrop as EventListener)

                container.addEventListener('dragover', handleLayoutDragOver as EventListener)
                container.addEventListener('dragleave', handleLayoutDragLeave as EventListener)
                container.addEventListener('drop', handleLayoutDrop as EventListener)

                // 确保已处理过的容器保持无背景色状态
                if (container.getAttribute('data-bg-removed') === 'true') {
                  container.classList.remove('bg-light', 'border')
                }

                // 确保没有内联背景色样式（清理拖拽过程中可能残留的样式）
                container.style.backgroundColor = ''
              })
            } else if (componentType === 'navbar') {
              // 特殊处理导航栏组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 检查导航栏是否有内部样式
              const hasInternalStyle = comp.querySelector('style') !== null

              // 如果没有内部样式，检查是否需要从页面样式中提取
              if (!hasInternalStyle) {
                // 查找页面中所有style标签
                const allStyles = document.querySelectorAll('style')

                // 提取所有与导航栏相关的样式规则
                let navbarStyleContent = ''

                // 遍历所有style标签，查找与navbar相关的样式
                allStyles.forEach(styleTag => {
                  const styleContent = styleTag.textContent || ''

                  // 检查样式是否包含导航栏相关的选择器
                  if (styleContent.includes('.navbar-section') || styleContent.includes('[data-bs-component="navbar"]') || styleContent.includes('nav[data-bs-component="navbar"]')) {
                    // 提取所有与导航栏相关的CSS规则
                    const cssRules = styleContent.match(/\.navbar-section[^}]+\}|nav\[data-bs-component="navbar"\][^}]+\}|\.navbar-section\s+[^}]+\}|\.navbar-section\.[^}]+\}/g)

                    if (cssRules && cssRules.length > 0) {
                      cssRules.forEach(rule => {
                        navbarStyleContent += rule + '\n'
                      })
                    }
                  }

                  // 检查是否有包含在页面模板中的导航栏样式
                  if (styleContent.includes('.marketing-landing-page .navbar-section') || styleContent.includes('.marketing-landing-page nav[data-bs-component="navbar"]')) {
                    // 提取所有与页面模板中导航栏相关的CSS规则
                    const cssRules = styleContent.match(/\.marketing-landing-page\s+\.navbar-section[^}]+\}|\.marketing-landing-page\s+nav\[data-bs-component="navbar"\][^}]+\}/g)

                    if (cssRules && cssRules.length > 0) {
                      cssRules.forEach(rule => {
                        // 移除页面模板前缀，使样式直接应用于导航栏
                        const modifiedRule = rule.replace(/\.marketing-landing-page\s+/g, '')
                        navbarStyleContent += modifiedRule + '\n'
                      })
                    }
                  }

                  // 检查是否有导航栏子元素的样式
                  const subSelectors = [
                    '.navbar-wrapper',
                    '.navbar-brand',
                    '.navbar-links',
                    '.nav-link',
                    '.navbar-section .btn',
                    '.navbar-section .btn-primary',
                    '.navbar-section .btn-outline-primary',
                  ]

                  subSelectors.forEach(selector => {
                    const regex = new RegExp(
                      `\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}|\\.marketing-landing-page\\s+\\.navbar-section\\s+${selector.replace('.', '\\.')}[^}]+\\}`,
                      'g',
                    )
                    const matches = styleContent.match(regex)

                    if (matches && matches.length > 0) {
                      matches.forEach(match => {
                        // 移除页面模板前缀，保留导航栏前缀
                        const modifiedRule = match.replace(/\.marketing-landing-page\s+/g, '')
                        navbarStyleContent += modifiedRule + '\n'
                      })
                    }
                  })
                })

                // 如果找到了相关样式，添加到导航栏中
                if (navbarStyleContent) {
                  // 检查是否已经有相同的样式标签
                  let styleExists = false
                  const existingStyles = comp.querySelectorAll('style')
                  existingStyles.forEach(existingStyle => {
                    if (existingStyle.textContent === navbarStyleContent) {
                      styleExists = true
                    }
                  })

                  // 如果样式不存在，添加到导航栏中
                  if (!styleExists) {
                    const styleElement = document.createElement('style')
                    styleElement.textContent = navbarStyleContent
                    comp.appendChild(styleElement)
                  }
                }
              }
            }
            // 新增特殊组件处理
            else if (componentType === 'carousel') {
              // 特殊处理轮播图组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 初始化轮播图
              if (typeof window !== 'undefined' && window.bootstrap) {
                try {
                  new window.bootstrap.Carousel(comp)
                } catch (error) {
                  console.error('初始化轮播图失败:', error)
                }
              }
            } else if (componentType === 'accordion') {
              // 特殊处理手风琴组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 初始化所有折叠面板
              if (typeof window !== 'undefined' && window.bootstrap) {
                try {
                  const collapseElements = comp.querySelectorAll('.collapse')
                  if (collapseElements.length > 0) {
                    collapseElements.forEach(el => {
                      new window.bootstrap.Collapse(el, { toggle: false })
                    })
                  }
                } catch (error) {
                  console.error('初始化手风琴失败:', error)
                }
              }
            } else if (componentType === 'modal') {
              // 特殊处理模态框组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 初始化模态框
              if (typeof window !== 'undefined' && window.bootstrap) {
                try {
                  new window.bootstrap.Modal(comp)
                } catch (error) {
                  console.error('初始化模态框失败:', error)
                }
              }
            } else if (componentType === 'dropdown') {
              // 特殊处理下拉菜单组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 初始化下拉菜单
              if (typeof window !== 'undefined' && window.bootstrap) {
                try {
                  const dropdownElements = comp.querySelectorAll('[data-bs-toggle="dropdown"]')
                  if (dropdownElements.length > 0) {
                    dropdownElements.forEach(el => {
                      new window.bootstrap.Dropdown(el)
                    })
                  }
                } catch (error) {
                  console.error('初始化下拉菜单失败:', error)
                }
              }
            } else if (componentType === 'tab') {
              // 特殊处理标签页组件
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)

              // 初始化标签页
              if (typeof window !== 'undefined' && window.bootstrap) {
                try {
                  const tabElements = comp.querySelectorAll('[data-bs-toggle="tab"]')
                  if (tabElements.length > 0) {
                    tabElements.forEach(el => {
                      new window.bootstrap.Tab(el)
                    })
                  }
                } catch (error) {
                  console.error('初始化标签页失败:', error)
                }
              }
            } else {
              comp.removeEventListener('click', handleBlockClick as EventListener)
              comp.addEventListener('click', handleBlockClick as EventListener)
            }
          })
        }
      })
    } catch (error) {
      console.error('初始化Bootstrap组件时出错:', error)
    }
  })
}

// 处理布局模块点击
const handleLayoutClick = (event: Event) => {
  const mouseEvent = event as MouseEvent
  let layoutElement = mouseEvent.currentTarget as HTMLElement

  if (!layoutElement) return

  // 阻止事件冒泡以防止编辑器获取焦点
  event.stopPropagation()

  // 清除所有已有的选中状态
  document.querySelectorAll('.bs-component-selected').forEach(el => {
    el.classList.remove('bs-component-selected')
  })

  // 标记当前布局为选中
  layoutElement.classList.add('bs-component-selected')

  // 记录选中的布局信息
  selectedBlockType.value = 'bootstrap-layout'
  selectedBlock.value = layoutElement.getAttribute('data-bs-layout') || 'layout'
  selectedBlockElement.value = layoutElement

  // 提取布局样式信息
  blockStyles.value = {
    width: 'container', // 布局通常使用container
    textAlign: 'left',
    marginTop: parseInt(layoutElement.style.marginTop) || 0,
    marginRight: parseInt(layoutElement.style.marginRight) || 0,
    marginBottom: parseInt(layoutElement.style.marginBottom) || 0,
    marginLeft: parseInt(layoutElement.style.marginLeft) || 0,
    paddingTop: parseInt(layoutElement.style.paddingTop) || 0,
    paddingRight: parseInt(layoutElement.style.paddingRight) || 0,
    paddingBottom: parseInt(layoutElement.style.paddingBottom) || 0,
    paddingLeft: parseInt(layoutElement.style.paddingLeft) || 0,
  }
}

// 处理布局容器的拖拽经过
const handleLayoutDragOver = (event: Event) => {
  const dragEvent = event as DragEvent

  // 阻止默认行为以允许放置
  dragEvent.preventDefault()

  // 设置放置效果
  if (dragEvent.dataTransfer) {
    dragEvent.dataTransfer.dropEffect = 'copy'
  }

  // 高亮显示目标容器
  const container = dragEvent.currentTarget as HTMLElement
  if (container) {
    container.style.backgroundColor = 'rgba(0, 123, 255, 0.1)'
  }
}

// 处理布局容器的拖拽离开
const handleLayoutDragLeave = (event: Event) => {
  const dragEvent = event as DragEvent

  // 移除高亮效果
  const container = dragEvent.currentTarget as HTMLElement
  if (container) {
    container.style.backgroundColor = ''
  }
}

// 处理布局容器的放置
const handleLayoutDrop = (event: Event) => {
  const dragEvent = event as DragEvent

  // 阻止默认行为
  dragEvent.preventDefault()
  dragEvent.stopPropagation() // 停止事件冒泡

  // 移除高亮效果
  const container = dragEvent.currentTarget as HTMLElement
  if (container) {
    container.style.backgroundColor = ''
  }

  // 如果没有dataTransfer，直接返回
  if (!dragEvent.dataTransfer) return

  // 获取模块类型
  let moduleType = ''

  if (dragEvent.dataTransfer.types.includes('application/bootstrap-component')) {
    moduleType = dragEvent.dataTransfer.getData('application/bootstrap-component')
  } else if (dragEvent.dataTransfer.types.includes('text/plain')) {
    moduleType = dragEvent.dataTransfer.getData('text/plain')
  }

  if (!moduleType) {
    console.warn('未能从拖拽事件中获取到模块类型')
    return
  }

  // 检查模块是否为布局模块
  const allModules = [...bootstrapModules, ...sections, ...layouts]
  const moduleInfo = allModules.find(m => m.type === moduleType)

  if (moduleInfo?.isLayoutModule) {
    // 不允许将布局模块放入另一个布局模块
    ElMessage.warning(t('Editor.fullscreenEditor.layout_module_not_allowed'))
    return
  }

  // 获取HTML模板
  const html = getTemplateByType(moduleType)

  if (!html) {
    console.warn('未找到对应的模板:', moduleType)
    return
  }

  // 创建临时div来保存HTML内容
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html

  // 取出内部元素并添加到布局容器
  while (tempDiv.firstChild) {
    container.appendChild(tempDiv.firstChild)
  }

  // 当向布局容器中添加内容后，移除该容器的背景色
  container.classList.remove('bg-light', 'border')
  // 添加标记，表示该容器已经处理过背景色
  container.setAttribute('data-bg-removed', 'true')

  // 更新编辑器内容
  updateEditorContent()

  // 重新初始化Bootstrap组件
  setTimeout(() => {
    initBootstrapComponents()
  }, 100)

  // 显示成功消息
  ElMessage.success(t('Editor.fullscreenEditor.add_success', { module: getModuleName(moduleType) }))
}

// 定义emit
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:content', value: string): void
}>()

// 获取模块名称
const getModuleName = (moduleType: string): string => {
  // 先检查是否为页面模板
  const pageTemplate = pageTemplates.find(template => template.id === moduleType)
  if (pageTemplate) {
    return pageTemplate.name
  }

  // 处理按钮类型
  if (moduleType === 'button') {
    return '按钮'
  }

  // 查找所有模块列表，包括moduleCategories中的项目
  const allModules = [...bootstrapModules, ...sections, ...layouts]
  let module = allModules.find(m => m.type === moduleType)

  // 如果在上面没找到，尝试在moduleCategories中查找
  if (!module) {
    // 遍历所有分类中的模块项
    for (const category of moduleCategories.value) {
      const found = category.items.find(item => item.type === moduleType)
      if (found) {
        module = found
        break
      }
    }
  }

  return module ? module.name : t('Editor.fullscreenEditor.unknown_module')
}

// 确保Bootstrap资源已加载
const ensureBootstrapResources = () => {
  if (!document.querySelector('link[href*="bootstrap.min.css"]')) {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css'
    link.integrity = 'sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9'
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  }

  // 检查Bootstrap JS是否已加载
  if (typeof window !== 'undefined' && typeof window.bootstrap === 'undefined') {
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js'
    script.integrity = 'sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm'
    script.crossOrigin = 'anonymous'
    document.head.appendChild(script)
  }
}

// 处理编辑器中的块点击
const handleBlockClick = (event: Event) => {
  const mouseEvent = event as MouseEvent

  // 查找点击的组件块
  let target = mouseEvent.target as HTMLElement
  let bsComponent: HTMLElement | null = null

  // 向上查找直到找到具有data-bs-component属性的元素
  while (target && !bsComponent) {
    // 检查元素是否有data-bs-component属性
    if (target.hasAttribute && target.hasAttribute('data-bs-component')) {
      bsComponent = target
    } else if (target.parentElement) {
      target = target.parentElement
    } else {
      break
    }
  }

  // 如果找到了组件块
  if (bsComponent) {
    event.preventDefault()
    event.stopPropagation()

    // 清除所有已有的选中状态
    document.querySelectorAll('[data-bs-component]').forEach(el => {
      el.classList.remove('bs-component-selected')
    })

    // 设置新的选中状态
    bsComponent.classList.add('bs-component-selected')

    // 保存选中块信息
    const componentType = bsComponent.getAttribute('data-bs-component')
    selectedBlock.value = componentType

    // 扩展模块类型映射
    if (componentType) {
      switch (componentType) {
        case 'rich-text':
          selectedBlockType.value = 'richTextBlock'
          break
        case 'social-flow':
          selectedBlockType.value = 'socialFlowBlock'
          break
        case 'stats-card':
          selectedBlockType.value = 'statsCardBlock'
          break
        case 'feature-cards':
          selectedBlockType.value = 'featureCardsBlock'
          break
        case 'button':
          selectedBlockType.value = 'bootstrap-button'
          break
        case 'heading':
          selectedBlockType.value = 'bootstrap-heading'
          break
        case 'alert':
          selectedBlockType.value = 'bootstrap-alert'
          break
        case 'card':
          selectedBlockType.value = 'bootstrap-card'
          break
        case 'carousel':
          selectedBlockType.value = 'bootstrap-carousel'
          break
        case 'accordion':
          selectedBlockType.value = 'bootstrap-accordion'
          break
        case 'nav':
          selectedBlockType.value = 'bootstrap-nav'
          break
        case 'form':
          selectedBlockType.value = 'bootstrap-form'
          break
        case 'table':
          selectedBlockType.value = 'bootstrap-table'
          break
        case 'image':
          selectedBlockType.value = 'bootstrap-image'
          break
        case 'countdown':
          selectedBlockType.value = 'bootstrap-countdown'
          break
        case 'feature-list':
          selectedBlockType.value = 'bootstrap-feature-list'
          break
        case 'metrics':
          selectedBlockType.value = 'bootstrap-metrics'
          break
        case 'pricing':
          selectedBlockType.value = 'bootstrap-pricing'
          break
        case 'cta':
          selectedBlockType.value = 'bootstrap-cta'
          break
        case 'testimonial-slider':
          selectedBlockType.value = 'bootstrap-testimonial-slider'
          break
        case 'timeline':
          selectedBlockType.value = 'bootstrap-timeline'
          break
        case 'navbar':
          selectedBlockType.value = 'bootstrap-navbar'
          break
        case 'hero':
          selectedBlockType.value = 'bootstrap-hero'
          break
        case 'info-section':
          selectedBlockType.value = 'bootstrap-info-section'
          break
        case 'footer':
          selectedBlockType.value = 'bootstrap-footer'
          break
        default:
          selectedBlockType.value = `bootstrap-${componentType}`
      }
    } else {
      selectedBlockType.value = 'unknown'
    }

    selectedBlockElement.value = bsComponent

    // 提取当前块的样式 - 处理响应式结构
    const extractStyles = () => {
      if (!bsComponent) return

      // 查找列元素，它包含了我们需要的宽度类信息
      const colElement = bsComponent.querySelector('.row > div[class^="col-"]')

      if (colElement) {
        // 提取列宽类 (如 col-12 col-md-10 col-lg-8)
        const colClasses = Array.from(colElement.classList)
          .filter((cls: string) => cls.startsWith('col-'))
          .join(' ')

        // 检查文本对齐类
        const textAlignClass = Array.from(colElement.classList).find((cls: string) => cls.startsWith('text-'))

        let textAlign = 'left'
        if (textAlignClass) {
          if (textAlignClass === 'text-center') textAlign = 'center'
          else if (textAlignClass === 'text-right') textAlign = 'right'
        }

        // 计算样式对象
        blockStyles.value = {
          width: colClasses || 'col-12 col-md-10 col-lg-8',
          textAlign: textAlign,
          marginTop: parseInt(bsComponent.style.marginTop) || 0,
          marginRight: parseInt(bsComponent.style.marginRight) || 0,
          marginBottom: parseInt(bsComponent.style.marginBottom) || 0,
          marginLeft: parseInt(bsComponent.style.marginLeft) || 0,
          paddingTop: parseInt(bsComponent.style.paddingTop) || 0,
          paddingRight: parseInt(bsComponent.style.paddingRight) || 0,
          paddingBottom: parseInt(bsComponent.style.paddingBottom) || 0,
          paddingLeft: parseInt(bsComponent.style.paddingLeft) || 0,
        }
      } else {
        // 回退到默认样式
        blockStyles.value = {
          width: 'col-12 col-md-10 col-lg-8',
          textAlign: 'left',
          marginTop: bsComponent ? parseInt(bsComponent.style.marginTop) || 0 : 0,
          marginRight: bsComponent ? parseInt(bsComponent.style.marginRight) || 0 : 0,
          marginBottom: bsComponent ? parseInt(bsComponent.style.marginBottom) || 0 : 0,
          marginLeft: bsComponent ? parseInt(bsComponent.style.marginLeft) || 0 : 0,
          paddingTop: bsComponent ? parseInt(bsComponent.style.paddingTop) || 0 : 0,
          paddingRight: bsComponent ? parseInt(bsComponent.style.paddingRight) || 0 : 0,
          paddingBottom: bsComponent ? parseInt(bsComponent.style.paddingBottom) || 0 : 0,
          paddingLeft: bsComponent ? parseInt(bsComponent.style.paddingLeft) || 0 : 0,
        }
      }
    }

    extractStyles()

    // 提取高级设置
    advancedSettings.value = {
      className: Array.from(bsComponent.classList)
        .filter((cls: string) => cls.startsWith('custom-'))
        .map((cls: string) => cls.replace('custom-', ''))
        .join(' '),
      id: bsComponent.id || '',
      customStyle: bsComponent.getAttribute('style') || '',
    }

    // 根据块类型提取特定内容
    if (selectedBlockType.value === 'bootstrap-button-group') {
      const buttons = bsComponent.querySelectorAll('button')
      if (buttons.length) {
        buttonItems.value = Array.from(buttons).map((btn: Element) => ({
          text: btn.textContent || '',
          style:
            Array.from(btn.classList)
              .find((cls: string) => cls.startsWith('btn-'))
              ?.replace('btn-', '') || 'primary',
        }))
      }
    } else if (selectedBlockType.value === 'bootstrap-alert') {
      // 获取提示框内容和样式
      const alertEl = bsComponent
      const btnClose = alertEl.querySelector('.btn-close')

      alertDismissible.value = !!btnClose

      // 移除可能的关闭按钮后获取内容
      let content = alertEl.innerHTML
      if (btnClose) {
        content = content.replace(btnClose.outerHTML, '').trim()
      }
      alertContent.value = content

      // 获取样式
      const styleClass = Array.from(alertEl.classList).find((cls: string) => cls.startsWith('alert-'))
      if (styleClass) {
        alertStyle.value = (styleClass as string).replace('alert-', '')
      } else {
        alertStyle.value = 'primary'
      }
    } else if (selectedBlockType.value === 'bootstrap-card') {
      // 获取卡片内容
      const cardEl = bsComponent

      // 获取标题
      const titleEl = cardEl.querySelector('.card-title')
      if (titleEl) {
        cardTitle.value = titleEl.textContent || '卡片标题'
      }

      // 获取内容
      const contentEl = cardEl.querySelector('.card-text')
      if (contentEl) {
        cardContent.value = contentEl.textContent || '这是卡片内容'
      }

      // 获取图片
      const imgEl = cardEl.querySelector('.card-img-top')
      if (imgEl && imgEl instanceof HTMLImageElement) {
        cardImageUrl.value = imgEl.src
      } else {
        cardImageUrl.value = ''
      }

      // 获取按钮
      const btnEl = cardEl.querySelector('.btn')
      if (btnEl) {
        cardButtonText.value = btnEl.textContent || '了解更多'

        // 获取按钮样式
        const btnStyleClass = Array.from(btnEl.classList).find((cls: string) => cls.startsWith('btn-'))
        if (btnStyleClass) {
          cardButtonStyle.value = (btnStyleClass as string).replace('btn-', '')
        }
      } else {
        cardButtonText.value = ''
      }

      // 获取卡片样式
      if (cardEl.classList.contains('border-primary')) {
        cardStyle.value = 'primary'
      } else if (cardEl.classList.contains('border-secondary')) {
        cardStyle.value = 'secondary'
      } else if (cardEl.classList.contains('border-success')) {
        cardStyle.value = 'success'
      } else if (cardEl.classList.contains('border-danger')) {
        cardStyle.value = 'danger'
      } else if (cardEl.classList.contains('border-warning')) {
        cardStyle.value = 'warning'
      } else if (cardEl.classList.contains('border-info')) {
        cardStyle.value = 'info'
      } else if (cardEl.classList.contains('border-light')) {
        cardStyle.value = 'light'
      } else if (cardEl.classList.contains('border-dark')) {
        cardStyle.value = 'dark'
      } else {
        cardStyle.value = 'default'
      }
    }

    // 显示块控制按钮
    showBlockControls.value = true

    // 计算控制按钮位置
    const rect = bsComponent.getBoundingClientRect()
    blockControlsPosition.value = {
      top: rect.top - 40, // 在块的上方
      left: rect.right - 150, // 在块的右上角
    }

    // 滚动到可见位置
    bsComponent.scrollIntoView({ behavior: 'smooth', block: 'center' })
  } else {
    // 如果点击了非组件块区域，取消选中
    closeBlockEdit()
  }
}

// 关闭块编辑
const closeBlockEdit = () => {
  if (selectedBlockElement.value) {
    selectedBlockElement.value.classList.remove('bs-component-selected')
  }
  selectedBlock.value = null
  selectedBlockType.value = ''
  selectedBlockElement.value = null
  showBlockControls.value = false
  blockEditTab.value = 'content' // 重置为内容标签页
}

// 设置MutationObserver监听DOM变化
let observer: MutationObserver | null = null
const setupMutationObserver = (target: Node) => {
  // 如果已经有观察者，先断开连接
  if (observer) {
    observer.disconnect()
  }

  // 创建新的观察者
  observer = new MutationObserver(mutations => {
    // 检查是否有新添加的节点
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // 遍历新添加的节点
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement

            // 检查新添加的元素是否有data-bs-component属性
            if (element.hasAttribute && element.hasAttribute('data-bs-component')) {
              // 为新元素添加点击事件
              element.removeEventListener('click', handleBlockClick as EventListener)
              element.addEventListener('click', handleBlockClick as EventListener)
            }

            // 递归检查子元素
            const components = element.querySelectorAll('[data-bs-component]')
            if (components.length > 0) {
              components.forEach(comp => {
                comp.removeEventListener('click', handleBlockClick as EventListener)
                comp.addEventListener('click', handleBlockClick as EventListener)
              })
            }
          }
        })
      }
    })
  })

  // 开始观察
  observer.observe(target, {
    childList: true, // 观察子节点的添加或删除
    subtree: true, // 观察所有后代节点
    attributes: true, // 观察属性变化
    attributeFilter: ['data-bs-component'], // 只关注data-bs-component属性
  })
}

const updateSelectedBlock = (update: { html?: string; styles?: any }) => {
  if (!selectedBlockElement.value || !editor.value || !update.html) return

  try {
    // 记录原始父元素，用于后续替换
    const parentElement = selectedBlockElement.value.parentNode
    if (!parentElement) {
      console.error('无法更新块：selectedBlockElement没有父节点')
      return
    }

    // 记录原始元素位置索引，用于后续插入到正确位置
    const childNodes = Array.from(parentElement.childNodes)
    const originalIndex = childNodes.indexOf(selectedBlockElement.value)

    // 记录原始元素类型和ID属性（如果有的话）
    const originalTagName = selectedBlockElement.value.tagName
    const originalId = selectedBlockElement.value.id
    const originalClassNames = selectedBlockElement.value.className

    // 记录原始元素的data-bs-component属性，这是很重要的类型标识
    const originalDataBsComponent = selectedBlockElement.value.getAttribute('data-bs-component')

    // 移除原始元素
    parentElement.removeChild(selectedBlockElement.value)

    // 解析新的HTML内容
    const tempContainer = document.createElement('div')
    tempContainer.innerHTML = update.html.trim()

    // 获取解析后的元素
    let newElement: HTMLElement

    // 如果新HTML只有一个根元素，直接使用它
    if (tempContainer.children.length === 1) {
      newElement = tempContainer.children[0] as HTMLElement

      // 确保保留原始元素的类型标识
      if (originalDataBsComponent && !newElement.hasAttribute('data-bs-component')) {
        newElement.setAttribute('data-bs-component', originalDataBsComponent)
      }
    } else {
      // 如果有多个根元素，创建一个包装器并将它们全部移入
      newElement = document.createElement(originalTagName || 'div')

      // 保留原始ID和类名，确保CSS样式能正确应用
      if (originalId) newElement.id = originalId
      if (originalClassNames) newElement.className = originalClassNames

      // 保留原始元素的类型标识
      if (originalDataBsComponent) {
        newElement.setAttribute('data-bs-component', originalDataBsComponent)
      }

      // 将内容移到新元素中
      newElement.innerHTML = tempContainer.innerHTML
    }

    // 特殊处理h1-h6标签：如果新元素是裸标题，但原始元素是heading类型，需要重新包装
    if (originalDataBsComponent === 'heading' && newElement.tagName.match(/^H[1-6]$/i) && !newElement.hasAttribute('data-bs-component')) {
      // 创建标题块结构
      const wrapper = document.createElement('div')
      wrapper.setAttribute('data-bs-component', 'heading')
      wrapper.className = 'bootstrap-heading'

      // 创建容器结构
      const container = document.createElement('div')
      container.className = 'container-fluid p-0'

      const row = document.createElement('div')
      row.className = 'row justify-content-center'

      const col = document.createElement('div')
      col.className = 'col-12 col-md-10 col-lg-8'

      // 按照层次结构组装
      col.appendChild(newElement.cloneNode(true))
      row.appendChild(col)
      container.appendChild(row)
      wrapper.appendChild(container)

      // 替换为包装后的元素
      newElement = wrapper
    }

    // 在原位置插入新元素
    if (originalIndex >= 0 && originalIndex < parentElement.childNodes.length) {
      parentElement.insertBefore(newElement, parentElement.childNodes[originalIndex])
    } else {
      parentElement.appendChild(newElement)
    }

    // 更新选中元素引用
    selectedBlockElement.value = newElement

    // 更新编辑器内容
    updateContent()

    // 重新初始化Bootstrap组件
    setTimeout(() => {
      initBootstrapComponents()
    }, 100)

    // 关闭块编辑面板
    closeBlockEdit()

    ElMessage.success(t('Editor.fullscreenEditor.message.update_success'))
  } catch (error) {
    console.error('更新块内容时出错:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.update_failed'))
  }
}

// 复制选中的块
const copySelectedBlock = () => {
  if (!selectedBlockElement.value) return

  const clonedElement = selectedBlockElement.value.cloneNode(true) as HTMLElement

  // 确保没有选中状态
  clonedElement.classList.remove('bs-component-selected')

  // 插入到当前选中块之后
  if (selectedBlockElement.value.parentNode) {
    selectedBlockElement.value.parentNode.insertBefore(clonedElement, selectedBlockElement.value.nextSibling)
  }

  // 更新编辑器内容
  updateContent()

  // 显示成功消息
  ElMessage.success(t('Editor.fullscreenEditor.message.block_copy_success'))
}

// 删除选中的块
const deleteSelectedBlock = () => {
  if (!selectedBlockElement.value) return

  try {
    // 获取元素类型信息
    const elementType = selectedBlockElement.value.getAttribute('data-bs-component')
    // 检查是否为特殊的模块类型（如CTA、Heading等）
    const isSpecialBlockType = ['cta', 'heading', 'rich-text', 'hero', 'navbar', 'footer', 'stats-card', 'feature-cards', 'info-section'].includes(elementType || '')

    // 检查是否为占位区域
    const isPlaceholder = selectedBlockElement.value.classList.contains('content-placeholder') || selectedBlockElement.value.getAttribute('data-bs-component') === 'placeholder'

    // 检查父元素，有些模块可能包含在特定的结构内
    let parentElement = selectedBlockElement.value.parentNode
    if (!parentElement) {
      console.error('删除失败：找不到父元素')
      ElMessage.error('删除失败：找不到父元素')
      return
    }

    // 检查是否是特殊的嵌套结构
    // 有些模块可能被容器包装，需要向上找到合适的父容器
    let targetElement = selectedBlockElement.value
    let containerToRemove = null

    // 检查是否在布局容器内
    const isInLayoutContainer = Boolean(
      parentElement && 
      'getAttribute' in parentElement && 
      (parentElement as Element).getAttribute('data-layout-container')
    )

    // 处理特殊的容器包装情况
    const containerSearch = (el: HTMLElement): HTMLElement | null => {
      // 向上查找到合适的容器级别
      let current = el
      let found = false
      let maxLevels = 5 // 防止无限循环

      while (current && maxLevels > 0 && !found) {
        // 检查是否是包装容器
        if (
          current.classList.contains('row') ||
          current.classList.contains('container') ||
          current.classList.contains('container-fluid') ||
          current.classList.contains('responsive-block') ||
          current.hasAttribute('data-bs-wrapper')
        ) {
          found = true
          return current
        }

        if (current.parentElement) {
          current = current.parentElement
        } else {
          break
        }

        maxLevels--
      }

      return null
    }

    // 对于特殊块，尝试查找外层容器
    if (isSpecialBlockType) {
      const container = containerSearch(targetElement)
      if (container) {
        containerToRemove = container
      }
    }

    // 保存原始块的尺寸和样式信息（用于创建占位符）
    const originalStyles = {
      width: (containerToRemove || targetElement).style.width || 'auto',
      height: (containerToRemove || targetElement).offsetHeight ? `${(containerToRemove || targetElement).offsetHeight}px` : 'auto',
      margin: (containerToRemove || targetElement).style.margin || '',
      padding: (containerToRemove || targetElement).style.padding || '',
      display: (containerToRemove || targetElement).style.display || 'block',
    }

    // 获取原始块的类名，保留响应式类
    const originalClasses = Array.from((containerToRemove || targetElement).classList)
      .filter((className: string) => className.startsWith('col-') || className.startsWith('row') || className.startsWith('container') || className.startsWith('d-') || className.startsWith('text-'))
      .join(' ')

    // 如果是占位区域，直接删除不需要创建新的占位区域
    if (isPlaceholder) {
      const elementToRemove = containerToRemove || targetElement
      if (elementToRemove.parentNode) {
        elementToRemove.parentNode.removeChild(elementToRemove)
      }

      // 关闭编辑面板
      closeBlockEdit()

      // 更新编辑器内容
      updateContent()

      // 显示成功消息
      ElMessage.success(t('Editor.fullscreenEditor.message.placeholder_delete_success'))
      return
    }

    // 确定要删除的元素
    const elementToRemove = containerToRemove || targetElement
    const parentOfElementToRemove = elementToRemove.parentNode

    // 创建占位区域代替直接删除
    const placeholderElement = document.createElement('div')

    // 检测父元素背景色来决定使用哪种样式
    let backgroundClass = 'dark-background'
    if (parentOfElementToRemove && parentOfElementToRemove instanceof Element) {
      const parentBgColor = getComputedStyle(parentOfElementToRemove as Element).backgroundColor
      // 简单判断是否为深色背景
      if (parentBgColor.includes('rgb(')) {
        const rgb = parentBgColor.match(/\d+/g)
        if (rgb && rgb.length >= 3) {
          // 计算亮度 - 简化版
          const brightness = (parseInt(rgb[0]) * 299 + parseInt(rgb[1]) * 587 + parseInt(rgb[2]) * 114) / 1000
          if (brightness > 125) {
            backgroundClass = 'light-background'
          }
        }
      }
    }

    // 设置占位符类和属性
    placeholderElement.className = `content-placeholder ${originalClasses} ${backgroundClass}`
    placeholderElement.setAttribute('data-bs-component', 'placeholder')
    placeholderElement.setAttribute('data-placeholder-id', `placeholder-${Date.now()}-${Math.floor(Math.random() * 1000)}`)

    // 应用原始块的样式以保持布局
    Object.assign(placeholderElement.style, {
      width: originalStyles.width,
      height: originalStyles.height,
      margin: originalStyles.margin,
      padding: originalStyles.padding,
      display: originalStyles.display,
    })

    // 如果在布局容器内，添加额外的类和样式以匹配布局
    if (isInLayoutContainer) {
      // 对布局容器内的元素添加特定的样式，确保占位符能够正确显示
      placeholderElement.style.minHeight = '80px'
      placeholderElement.style.width = '100%'

      // 如果元素被包含在列中，确保占位符可以填充整个列宽
      if (parentOfElementToRemove && 'classList' in parentOfElementToRemove) {
        const parentCol = Array.from((parentOfElementToRemove as Element).classList).find((cls: unknown) => 
          typeof cls === 'string' && cls.startsWith('col-')
        )

        if (parentCol && typeof parentCol === 'string') {
          placeholderElement.classList.add(parentCol)
        }
      }
    }

    // 设置可拖放属性
    placeholderElement.setAttribute('draggable', 'false')

    // 添加更清晰的HTML内容
    placeholderElement.innerHTML = `
      <div class="placeholder-content">
        <div class="placeholder-icon">+</div>
        <div class="placeholder-text">拖拽內容到此區域或點擊添加</div>
      </div>
    `

    // 使用直接属性绑定事件，避免addEventListener可能的问题
    placeholderElement.onclick = function (e: MouseEvent) {
      e.preventDefault()
      e.stopPropagation()
      openPlaceholderMenu(placeholderElement as HTMLElement)
    }

    // 使用on属性直接绑定拖拽事件
    placeholderElement.ondragover = function (e: DragEvent) {
      e.preventDefault()
      e.stopPropagation()
      if (e.dataTransfer) {
        e.dataTransfer.dropEffect = 'copy'
      }
      placeholderElement.classList.add('dragover')
    }

    placeholderElement.ondragleave = function (e: DragEvent) {
      e.preventDefault()
      e.stopPropagation()
      placeholderElement.classList.remove('dragover')
    }

    placeholderElement.ondrop = function (e: DragEvent) {
      e.preventDefault()
      e.stopPropagation()
      placeholderElement.classList.remove('dragover')

      handlePlaceholderDrop(e, placeholderElement as HTMLElement)
    }

    // 将占位符替换原块
    if (parentOfElementToRemove) {
      parentOfElementToRemove.replaceChild(placeholderElement, elementToRemove)
    }

    // 关闭编辑面板
    closeBlockEdit()

    // 更新编辑器内容
    updateContent()

    // 重新初始化Bootstrap组件
    setTimeout(() => {
      initBootstrapComponents()
    }, 100)

    // 显示成功消息
    ElMessage.success(t('Editor.fullscreenEditor.message.block_delete_success'))
  } catch (error) {
    console.error('删除块时出错:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.block_delete_failed', { msg: error instanceof Error ? error.message : t('Editor.preview.unknown_error') }))

    // 如果删除过程中出错，尝试直接删除选中元素而不替换为占位符
    try {
      if (selectedBlockElement.value && selectedBlockElement.value.parentNode) {
        selectedBlockElement.value.parentNode.removeChild(selectedBlockElement.value)
        closeBlockEdit()
        updateContent()
        ElMessage.success(t('Editor.fullscreenEditor.message.block_delete_forced'))
      }
    } catch (fallbackError) {
      console.error('强制删除也失败:', fallbackError)
      ElMessage.error(t('Editor.fullscreenEditor.message.block_forced_delete_failed', { msg: fallbackError instanceof Error ? fallbackError.message : t('Editor.preview.unknown_error') }))
    }
  }
}

// 替换整个handlePlaceholderDrop函数
const handlePlaceholderDrop = (event: DragEvent, placeholderElement: HTMLElement) => {
  // 安全检查
  if (!event.dataTransfer) return

  // 防止默认行为和冒泡
  event.preventDefault()
  event.stopPropagation()

  // 获取拖拽的模块类型
  let moduleType = ''
  const availableTypes = Array.from(event.dataTransfer.types)
  // 按优先级获取数据
  if (availableTypes.includes('application/bootstrap-component')) {
    moduleType = event.dataTransfer.getData('application/bootstrap-component')
  } else if (availableTypes.includes('text/plain')) {
    moduleType = event.dataTransfer.getData('text/plain')
  }

  if (!moduleType) {
    console.warn('未能获取拖拽的模块类型')
    return
  }

  // 获取HTML模板
  let html = null

  // 检查是否为页面模板
  const pageTemplate = pageTemplates.find(template => template.id === moduleType)
  if (pageTemplate) {
    html = pageTemplate.template
  } else {
    // 否则从常规模板中获取
    html = getTemplateByType(moduleType)
  }

  if (!html) {
    console.warn('未找到对应的模板:', moduleType)
    return
  }

  // 确保占位符在DOM中
  if (!document.body.contains(placeholderElement)) {
    console.warn('占位符不在DOM中')
    return
  }

  const parentNode = placeholderElement.parentNode
  if (!parentNode) {
    console.warn('占位符没有父节点')
    return
  }

  try {
    // 创建新元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html.trim()

    // 获取第一个子元素
    const newElement = tempDiv.firstElementChild
    if (!newElement) {
      console.warn('模板未能生成有效元素')
      return
    }

    // 直接替换占位符
    parentNode.replaceChild(newElement, placeholderElement)

    // 确保使用异步操作进行后续处理
    setTimeout(() => {
      // 初始化Bootstrap组件
      initBootstrapComponents()
      // 更新编辑器内容
      updateEditorContent()
    }, 100)

    // 显示成功消息
    ElMessage.success(`已添加${getModuleName(moduleType)}`)
  } catch (error) {
    console.error('替换占位符时出错:', error)
    ElMessage.error('添加组件失败')
  }
}

// 打开占位符菜单
const openPlaceholderMenu = (placeholderElement: HTMLElement) => {
  // 创建菜单元素
  const menuElement = document.createElement('div')
  menuElement.className = 'placeholder-menu'

  // 获取首选模块列表显示在菜单中
  const popularModules = [
    { type: 'richTextBlock', name: '富文本' },
    { type: 'bootstrap-heading', name: '大标题' },
    { type: 'bootstrap-button', name: '按钮' },
    { type: 'bootstrap-card', name: '卡片' },
    { type: 'bootstrap-divider', name: '分隔线' },
  ]

  // 生成菜单HTML
  let menuHTML = '<div class="menu-header">添加内容</div><div class="menu-items">'
  popularModules.forEach(module => {
    menuHTML += `<div class="menu-item" data-type="${module.type}">${module.name}</div>`
  })
  menuHTML += '<div class="menu-item menu-more">更多...</div></div>'

  menuElement.innerHTML = menuHTML

  // 为占位符添加选中状态
  placeholderElement.classList.add('placeholder-active')

  // 修改菜单定位方式 - 使用fixed定位和相对于视口计算位置
  // 计算菜单位置 - 使用offsetLeft和offsetTop而不是getBoundingClientRect
  const rect = placeholderElement.getBoundingClientRect()

  // 使用fixed定位，基于视口位置
  menuElement.style.position = 'fixed'
  menuElement.style.top = `${rect.top + 30}px` // 向下偏移一点
  menuElement.style.left = `${rect.left + rect.width / 2}px`
  menuElement.style.transform = 'translateX(-50%)'
  menuElement.style.zIndex = '10000' // 确保显示在其他元素之上

  // 添加到编辑器内部而不是body
  const editorContent = document.querySelector('.editor-content-area')
  if (editorContent) {
    editorContent.appendChild(menuElement)
  } else {
    // 兼容处理，如果找不到编辑器区域则加到body
    document.body.appendChild(menuElement)
  }

  // 添加菜单项点击事件
  const menuItems = menuElement.querySelectorAll('.menu-item')
  menuItems.forEach(item => {
    item.addEventListener('click', () => {
      if (item.classList.contains('menu-more')) {
        // 处理"更多"选项 - 显示侧边栏
        activeTab.value = 'modules' // 激活模块标签页
        // 移除占位符选中状态和菜单
        placeholderElement.classList.remove('placeholder-active')
        menuElement.parentNode?.removeChild(menuElement)
        return
      }

      const moduleType = item.getAttribute('data-type')
      if (moduleType) {
        // 获取HTML模板
        const html = getTemplateByType(moduleType)

        if (html && placeholderElement.parentNode) {
          // 创建新元素
          const tempDiv = document.createElement('div')
          tempDiv.innerHTML = html

          // 获取第一个元素并确保不为null
          const newElement = tempDiv.firstElementChild
          if (newElement) {
            // 替换占位符
            placeholderElement.parentNode.replaceChild(newElement, placeholderElement)

            // 初始化组件
            setTimeout(() => {
              initBootstrapComponents()
            }, 100)

            // 更新编辑器内容
            updateContent()
          }
        }
      }

      // 移除菜单
      menuElement.parentNode?.removeChild(menuElement)
    })
  })

  // 添加点击事件监听器关闭菜单 - 修改为只监听编辑器区域内的点击
  const closeMenu = (e: MouseEvent) => {
    if (!menuElement.contains(e.target as Node) && e.target !== placeholderElement) {
      placeholderElement.classList.remove('placeholder-active')
      menuElement.parentNode?.removeChild(menuElement)
      document.removeEventListener('click', closeMenu)
    }
  }

  // 延迟添加事件监听器，避免立即触发
  setTimeout(() => {
    document.addEventListener('click', closeMenu)
  }, 10)
}

// 块上移
const moveBlockUp = () => {
  if (!selectedBlockElement.value) return

  const previous = selectedBlockElement.value.previousElementSibling
  if (previous && selectedBlockElement.value.parentNode) {
    selectedBlockElement.value.parentNode.insertBefore(selectedBlockElement.value, previous)

    // 更新编辑器内容
    updateContent()
  }
}

// 块下移
const moveBlockDown = () => {
  if (!selectedBlockElement.value) return

  const next = selectedBlockElement.value.nextElementSibling
  if (next && selectedBlockElement.value.parentNode) {
    selectedBlockElement.value.parentNode.insertBefore(next, selectedBlockElement.value)

    // 更新编辑器内容
    updateContent()
  }
}

// 监听模板ID变化
watch(templateId, newId => {
  if (newId) {
    // 如果有ID但还没有获取到模板数据，先使用默认标题
    if (title.value === '无标题') {
      title.value = defaultTitle.value
    }
  } else {
    // 如果ID被清除，重置为默认标题
    title.value = '无标题'
  }
})

// 获取模板数据
const fetchTemplateData = async () => {
  if (templateId.value) {
    try {
      ElMessage.info(t('Editor.fullscreenEditor.message.loading_template_data'))
      const response = await http.get(`/editor/templates/${templateId.value}`)

      if (response.data && response.data.code === 200 && response.data.data) {
        // 设置标题
        if (response.data.data.name) {
          title.value = response.data.data.name
        }

        // 如果编辑器已初始化且有内容数据
        if (editor.value && response.data.data.content) {
          try {
            // 检查内容格式
            const contentData = response.data.data.content

            // 如果是字符串，直接设置
            if (typeof contentData === 'string') {
              editor.value.commands.setContent(contentData)
            }
            // 如果是对象格式（包含type和content字段的结构）
            else if (typeof contentData === 'object' && contentData.type === 'doc' && Array.isArray(contentData.content)) {
              // 处理内容数组，将其转换为HTML
              let html = ''

              // 遍历内容数组，提取HTML
              contentData.content.forEach((item: ContentDataItem) => {
                if (item.type === 'bootstrapComponent' && item.attrs && item.attrs.content) {
                  html += item.attrs.content
                }
              })

              // 设置内容
              if (html) {
                editor.value.commands.setContent(html)
              }
            }

            // 初始化Bootstrap组件
            setTimeout(() => {
              initBootstrapComponents()
            }, 100)

            ElMessage.success(t('Editor.fullscreenEditor.message.template_load_success'))
          } catch (parseError) {
            console.error('解析模板内容失败:', parseError)
            ElMessage.error(t('Editor.fullscreenEditor.message.template_parse_failed'))
          }
        }
      } else {
        ElMessage.error(response.data?.message || t('Editor.fullscreenEditor.message.template_load_failed'))
      }
    } catch (error) {
      console.error('获取模板数据失败:', error)
      ElMessage.error(t('Editor.fullscreenEditor.message.template_load_failed'))
    }
  }
}

onMounted(() => {
  // 加载Bootstrap资源
  ensureBootstrapResources()

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)

  // 初始化编辑器
  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: 'tiptap-bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'tiptap-ordered-list',
          },
        },
        // 启用HTML解析，允许原始HTML
        codeBlock: {
          HTMLAttributes: {
            class: 'tiptap-code-block',
          },
        },
      }),
      TextStyle,
      Color,
      Underline,
      Strike,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'tiptap-link',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
      }),
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'tiptap-table',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'tiptap-table-row',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'tiptap-table-cell',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'tiptap-table-header',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'tiptap-task-list',
        },
      }),
      TaskItem.configure({
        nested: true,
      }),
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily,
      Subscript,
      Superscript,
      Typography,
      Image.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'tiptap-image',
        },
      }),
      // 自定义区块扩展
      GridBlock,
      CtaBlock,
      ImageTextBlock,
      HeadlineBlock,
      HeadingBlock,
      BootstrapComponent,
      ButtonBlock,
      RichTextBlock,
      LayoutBlock,
      RichTextNode,
      AllowAttributes,
      CountdownBlock,
      FeatureListBlock,
      MetricsBlock,
      PricingBlock,
      SocialFlowBlock,
      TestimonialSliderBlock,
      TimelineBlock,
      StatsCardBlock,
      NavbarBlock,
      HeroBlock,
      FeatureCardsBlock,
      InfoSectionBlock,
      FooterBlock,
      ImageBlock,
      UlLiBlock,
      ...bootstrapComponents,
      BootstrapAlert,
      BootstrapCard,
      BootstrapContainer,
      BootstrapRow,
      BootstrapCol,
      BootstrapBadge,
      BootstrapBreadcrumb,
      BootstrapButton,
      BootstrapNav,
      BootstrapNavbar,
      BootstrapPagination,
      BootstrapTab,
      BootstrapForm,
      BootstrapInputGroup,
      BootstrapDropdown,
      BootstrapModal,
      BootstrapTooltip,
      BootstrapPopover,
      BootstrapProgress,
      BootstrapSpinner,
      BootstrapListGroup,
      BootstrapAccordion,
      BootstrapCarousel,
      BootstrapJumbotron,
      BootstrapMedia,
      BootstrapTable,
      BootstrapToast,
    ],
    content: props.content || props.modelValue || '',
    editorProps: {
      attributes: {
        class: 'tiptap-editor',
        spellcheck: 'false',
      },
      handleDrop: (_, event) => {
        // 处理拖放
        if (event) {
          // 首先检查是否为拖拽文件
          if (event.dataTransfer?.files?.length) {
            handleDrop(event)
            return true
          }

          // 然后检查是否为拖拽Bootstrap组件
          const types = Array.from(event.dataTransfer?.types || [])
          if (types.includes('application/bootstrap-component') || (types.includes('text/plain') && event.dataTransfer?.getData('text/plain')?.startsWith('bootstrap-'))) {
            // 这是我们自定义的拖拽，应该完全自己处理
            handleDrop(event)
            return true
          }
        }

        // 返回false，让默认的拖拽处理继续进行
        return false
      },
    },
    onUpdate: ({ editor }) => {
      // 更新内容
      updateContent()

      // 更新历史状态
      updateHistoryState()

      // 内容更新时初始化Bootstrap组件，但使用延迟确保DOM已完全更新
      setTimeout(() => {
        initBootstrapComponents()
      }, 100)
    },
  })

  // 添加编辑器内容变化的监听
  if (editor.value) {
    editor.value.on('update', handleEditorContentChange)
  }

  // 初始选择第一个模块
  if (bootstrapModules.length > 0) {
    selectedModule.value = bootstrapModules[0].type
  }
  addPlaceholderStyles()
  
  // 如果有模板ID，加载模板内容
  if (templateId.value) {
    loadTemplateContent()
  }

  // 如果是编辑模式，加载页面详情
  if (isEditMode.value) {
    // 延迟一点时间，确保编辑器完全初始化
    nextTick(() => {
      fetchPageDetail()
    })
  }
})

onBeforeUnmount(() => {
  // 销毁编辑器实例
  if (editor.value) {
    editor.value.destroy()
  }

  // 清除事件监听
  const editorContainer = document.querySelector('.editor-content-area')
  if (editorContainer) {
    editorContainer.removeEventListener('dragover', () => {})
    editorContainer.removeEventListener('dragleave', () => {})
    editorContainer.removeEventListener('drop', () => {})
    // 移除点击事件监听
    editorContainer.removeEventListener('click', handleBlockClick as EventListener)
  }

  // 移除ProseMirror点击事件监听
  const proseMirror = document.querySelector('.ProseMirror')
  if (proseMirror) {
    proseMirror.removeEventListener('click', handleBlockClick as EventListener)
  }

  // 断开MutationObserver连接
  if (observer) {
    observer.disconnect()
    observer = null
  }
})

// 替换addPlaceholderStyles函数
const addPlaceholderStyles = () => {
  if (!document.getElementById('placeholder-styles')) {
    const styleElement = document.createElement('style')
    styleElement.id = 'placeholder-styles'
    styleElement.textContent = `
      .content-placeholder {
        border: 3px dashed #dcdfe6;
        border-radius: 6px;
        padding: 25px;
        margin: 20px 0;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        background-color: rgba(240, 240, 240, 0.5);
        box-sizing: border-box;
        position: relative;
        z-index: 1000;
      }
      
      .content-placeholder:hover {
        border-color: #409eff;
        background-color: rgba(236, 245, 255, 0.6);
        transform: translateY(-2px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      .content-placeholder.dragover {
        border: 3px dashed #409eff;
        background-color: rgba(64, 158, 255, 0.15);
        box-shadow: 0 0 15px rgba(64, 158, 255, 0.3);
        transform: translateY(-2px);
      }
      
      .content-placeholder .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        width: 100%;
        height: 100%;
      }
      
      .content-placeholder .placeholder-icon {
        font-size: 36px;
        font-weight: 300;
        margin-bottom: 10px;
        color: #409eff;
      }
      
      .content-placeholder .placeholder-text {
        font-size: 16px;
        text-align: center;
        color: #606266;
        font-weight: 500;
        line-height: 1.5;
      }
      
      .placeholder-menu {
        position: fixed;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.15);
        z-index: 10000;
        min-width: 180px;
        transform: translateX(-50%);
        border: 1px solid #ebeef5;
      }
      
      .placeholder-menu .menu-header {
        padding: 10px 16px;
        font-weight: 500;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        font-size: 16px;
      }
      
      .placeholder-menu .menu-items {
        padding: 5px 0;
      }
      
      .placeholder-menu .menu-item {
        padding: 10px 16px;
        cursor: pointer;
        color: #303133;
        font-size: 14px;
        transition: all 0.2s;
      }
      
      .placeholder-menu .menu-item:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }
      
      .placeholder-menu .menu-more {
        color: #409eff;
        font-weight: 500;
        border-top: 1px solid #ebeef5;
        margin-top: 5px;
      }
    `
    document.head.appendChild(styleElement)
  }
}

// 监听设备类型变化
watch(activeDevice, newDevice => {
  ElMessage.info(`已切换到${newDevice === 'desktop' ? '桌面端' : newDevice === 'tablet' ? '平板端' : '手机端'}预览模式`)

  // 更新编辑器视图
  nextTick(() => {
    if (editor.value) {
      // 不直接使用view.update(view.state)，而是使用commands.focus()触发视图更新
      editor.value.commands.focus()
    }
  })
})

// 在 updateSelectedBlock 函数后添加
const updateBlockStyles = (styles: any) => {
  if (!selectedBlockElement.value) return

  try {
    // 获取当前块的容器结构
    const container = selectedBlockElement.value.querySelector('.container, .container-fluid')
    const row = container?.querySelector('.row')
    const col = row?.querySelector('[class*="col-"]')

    if (container && row && col) {
      // 更新宽度类
      const currentColClasses = Array.from(col.classList).filter(cls => !cls.startsWith('col-') && !cls.startsWith('text-'))
      col.className = `${currentColClasses.join(' ')} ${styles.width} text-${styles.textAlign}`

      // 更新外边距
      selectedBlockElement.value.style.marginTop = styles.marginTop ? `${styles.marginTop}px` : ''
      selectedBlockElement.value.style.marginRight = styles.marginRight ? `${styles.marginRight}px` : ''
      selectedBlockElement.value.style.marginBottom = styles.marginBottom ? `${styles.marginBottom}px` : ''
      selectedBlockElement.value.style.marginLeft = styles.marginLeft ? `${styles.marginLeft}px` : ''

      // 更新内边距
      selectedBlockElement.value.style.paddingTop = styles.paddingTop ? `${styles.paddingTop}px` : ''
      selectedBlockElement.value.style.paddingRight = styles.paddingRight ? `${styles.paddingRight}px` : ''
      selectedBlockElement.value.style.paddingBottom = styles.paddingBottom ? `${styles.paddingBottom}px` : ''
      selectedBlockElement.value.style.paddingLeft = styles.paddingLeft ? `${styles.paddingLeft}px` : ''

      // 更新编辑器内容
      updateContent()

      // 重新初始化 Bootstrap 组件
      setTimeout(() => {
        initBootstrapComponents()
      }, 100)

      ElMessage.success(t('Editor.fullscreenEditor.message.style_update_success'))
    } else {
      // 如果找不到完整的容器结构，直接应用到选中元素
      selectedBlockElement.value.style.marginTop = styles.marginTop ? `${styles.marginTop}px` : ''
      selectedBlockElement.value.style.marginRight = styles.marginRight ? `${styles.marginRight}px` : ''
      selectedBlockElement.value.style.marginBottom = styles.marginBottom ? `${styles.marginBottom}px` : ''
      selectedBlockElement.value.style.marginLeft = styles.marginLeft ? `${styles.marginLeft}px` : ''
      selectedBlockElement.value.style.paddingTop = styles.paddingTop ? `${styles.paddingTop}px` : ''
      selectedBlockElement.value.style.paddingRight = styles.paddingRight ? `${styles.paddingRight}px` : ''
      selectedBlockElement.value.style.paddingBottom = styles.paddingBottom ? `${styles.paddingBottom}px` : ''
      selectedBlockElement.value.style.paddingLeft = styles.paddingLeft ? `${styles.paddingLeft}px` : ''

      // 更新文本对齐方式
      const currentClasses = Array.from(selectedBlockElement.value.classList).filter(cls => !cls.startsWith('text-'))
      selectedBlockElement.value.className = `${currentClasses.join(' ')} text-${styles.textAlign}`

      // 更新编辑器内容
      updateContent()

      ElMessage.success(t('Editor.fullscreenEditor.message.style_update_success'))
    }

    // 更新本地样式状态
    blockStyles.value = { ...styles }
  } catch (error) {
    console.error('更新样式时出错:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.style_update_failed'))
  }
}

// 在 script setup 部分添加 handleExit 方法
const handleExit = () => {
  router.back()
}

// 获取页面详情
const fetchPageDetail = async () => {
  if (!cmsPageId.value || cmsPageId.value <= 0) {
    return
  }

  try {
    ElMessage.info('正在加载页面详情...')
    const response = await http.get(`/editor/pages/${cmsPageId.value}`)
    
    if (response.data && response.data.code === 200) {
      pageDetail.value = response.data.data
      
      // 设置页面标题
      if (pageDetail.value.title) {
        title.value = pageDetail.value.title
      }
      
      // 设置编辑器内容
      if (pageDetail.value.content && editor.value) {
        let content = pageDetail.value.content
        try {
          // 尝试解析JSON格式的内容
          if (typeof content === 'string' && content.startsWith('"') && content.endsWith('"')) {
            content = JSON.parse(content)
          }
        } catch (e) {
          // 如果解析失败，直接使用原内容
        }
        
        editor.value.commands.setContent(content)
        
        // 等待内容加载完成后初始化Bootstrap组件
        nextTick(() => {
          initBootstrapComponents()
        })
      }
      
      ElMessage.success(t('Editor.fullscreenEditor.message.fetch_page_detail_success'))
    } else {
      ElMessage.error(response.data?.message || '获取页面详情失败')
    }
  } catch (error) {
    console.error('获取页面详情失败:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.fetch_page_detail_failed', { msg: error instanceof Error ? error.message : '未知错误' }))
  }
}

// 在 script setup 部分添加以下代码
const defaultTitle = computed(() => (templateId.value ? '编辑模板' : '无标题'))
const title = ref(defaultTitle.value)
const titleDialogVisible = ref(false)
const titleForm = ref({
  title: '',
})
const titleFormRef = ref<InstanceType<typeof ElForm> | null>(null)
const titleInputRef = ref<InstanceType<typeof ElInput> | null>(null)

// 自定义 v-focus 指令
const vFocus = {
  mounted: (el: HTMLElement) => el.focus(),
}

// 打开标题编辑弹窗
const openTitleDialog = () => {
  titleForm.value.title = title.value
  titleDialogVisible.value = true
  nextTick(() => {
    titleInputRef.value?.focus()
  })
}

// 保存标题
const saveTitleDialog = async () => {
  if (!titleFormRef.value) return

  try {
    await titleFormRef.value.validate()

    if (titleForm.value.title.trim()) {
      title.value = titleForm.value.title.trim()
      titleDialogVisible.value = false
      ElMessage.success(t('Editor.fullscreenEditor.message.update_success'))
    } else {
      ElMessage.warning(t('Editor.fullscreenEditor.message.title_empty'))
    }
  } catch (error) {
    console.error('标题验证失败:', error)
  }
}

// 在data区域添加AI侧边栏状态 - 大约在line 467附近
const showAiOptimizeSidebar = ref(false)
const selectedBlockContent = ref('')

// 在BlockControls添加AI优化按钮 - 添加AI优化处理函数 - 大约在line 2650附近
// 将模块内容转换为HTML字符串
const getSelectedBlockContent = () => {
  if (!selectedBlockElement.value) return ''

  // 克隆节点以避免修改原节点
  const cloneNode = selectedBlockElement.value.cloneNode(true) as HTMLElement

  // 移除所有控制类名和属性
  const cleanNode = (node: HTMLElement) => {
    const classList = Array.from(node.classList || [])
    classList.forEach(cls => {
      if (cls.includes('bs-component-selected') || cls.includes('hovered')) {
        node.classList.remove(cls)
      }
    })

    // 递归处理子节点
    Array.from(node.children).forEach(child => {
      cleanNode(child as HTMLElement)
    })
  }

  cleanNode(cloneNode)

  // 返回HTML字符串
  return cloneNode.outerHTML
}

// 用AI优化块内容
const optimizeBlockWithAI = () => {
  if (!selectedBlockElement.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.select_block_first'))
    return
  }

  // 获取选中块的内容
  selectedBlockContent.value = getSelectedBlockContent()

  // 显示AI侧边栏
  showAiOptimizeSidebar.value = true
}

// 应用AI优化的内容
const applyAiOptimizedContent = (optimizedContent: string) => {
  if (!selectedBlockElement.value || !optimizedContent) return

  // 创建临时元素来解析HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = optimizedContent

  // 获取第一个子元素
  const newElement = tempDiv.firstElementChild

  // 确保有有效的新元素
  if (newElement && selectedBlockElement.value.parentNode) {
    // 替换旧元素
    selectedBlockElement.value.parentNode.replaceChild(newElement, selectedBlockElement.value)

    // 更新选中的元素
    selectedBlockElement.value = newElement as HTMLElement

    // 初始化Bootstrap组件
    setTimeout(() => {
      initBootstrapComponents()
    }, 100)

    // 更新编辑器内容
    updateContent()

    // 提示用户
    ElMessage.success(t('Editor.fullscreenEditor.message.ai_apply_success'))
  } else {
    ElMessage.error(t('Editor.fullscreenEditor.message.ai_apply_failed'))
  }
}


const showAiOptimize = () => {
  if (selectedBlockElement.value) {
    optimizeBlockWithAI()
  }
}

// 添加发布相关函数
const handlePublish = async () => {
  if (!editor.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.editor_not_initialized'))
    return
  }

  try {
    // 检查编辑器内容是否为空
    if (!editor.value || !editor.value.getHTML() || editor.value.getHTML().trim() === '') {
      ElMessageBox.alert(t('Editor.fullscreenEditor.message.content_empty', { action: t('Editor.fullscreenEditor.publish') }), t('Editor.fullscreenEditor.message.content_empty'), {
        confirmButtonText: t('Editor.fullscreenEditor.confirm'),
        type: 'warning',
      })
      return
    }

    // 检查标题是否为空
    if (!title.value || title.value === t('Editor.fullscreenEditor.no_title')) {
      const result = await ElMessageBox.prompt(t('Editor.fullscreenEditor.message.title_empty'), t('Editor.fullscreenEditor.message.title_empty'), {
        confirmButtonText: t('Editor.fullscreenEditor.confirm'),
        cancelButtonText: t('Editor.fullscreenEditor.cancel'),
        inputValue: title.value === t('Editor.fullscreenEditor.no_title') ? '' : title.value,
        inputValidator: value => {
          return value.trim() !== '' || t('Editor.fullscreenEditor.message.title_empty')
        },
      }).catch(() => null)

      if (!result) return // 用户取消了输入
      title.value = result.value
    }

    const actionText = isEditMode.value ? t('Editor.fullscreenEditor.update') : t('Editor.fullscreenEditor.publish')
    ElMessage.info(t('Editor.fullscreenEditor.message.processing', { action: actionText }))

    // 准备请求数据
    const requestData = {
      title: title.value,
      model_id: cmsModelId.value,
      content_id: cmsContentId.value,
      content: JSON.stringify(editor.value?.getHTML()),
    }

    let response
    if (isEditMode.value) {
      // 更新模式 - 使用PUT请求
      response = await http.put(`/editor/pages/${cmsPageId.value}`, requestData)
    } else {
      // 发布模式 - 使用POST请求
      response = await http.post('/editor/pages', requestData)
    }

    if (response.data && response.data.code === 200) {
      ElMessage.success(t('Editor.fullscreenEditor.message.' + (isEditMode.value ? 'update_success' : 'publish_success')))

      // 可以根据需求导航到列表页或者其他页面
      if (response.data.data && response.data.data.id) {
        // 记录页面ID
        const pageId = response.data.data.id

        // 如果是新发布，更新路由参数以便后续操作变为更新模式
        if (!isEditMode.value) {
          // 更新当前路由，添加page_id参数
          router.replace({
            ...route,
            query: {
              ...route.query,
              page_id: pageId
            }
          })
        }

        // 弹窗询问是否继续编辑
        ElMessageBox.confirm(t('Editor.fullscreenEditor.message.return_list'), t('Editor.fullscreenEditor.message.' + (isEditMode.value ? 'update_success' : 'publish_success')), {
          confirmButtonText: t('Editor.fullscreenEditor.message.return_list'),
          cancelButtonText: t('Editor.fullscreenEditor.message.continue_editing'),
          type: 'success',
        })
          .then(() => {
            // 用户选择返回列表
            router.push('/cms/cmsList?model_id=5')
          })
          .catch(() => {
            // 用户选择继续编辑
            ElMessage.info(t('Editor.fullscreenEditor.message.continue_editing'))
          })
      }
    } else {
      ElMessage.error(response.data?.message || t('Editor.fullscreenEditor.message.' + (isEditMode.value ? 'update_failed' : 'publish_failed')))
    }
  } catch (error) {
    console.error(`${isEditMode.value ? '更新' : '发布'}失败:`, error)
    ElMessage.error(t('Editor.fullscreenEditor.message.' + (isEditMode.value ? 'update_failed' : 'publish_failed'), { msg: error instanceof Error ? error.message : t('Editor.preview.unknown_error') }))
  }
}

// 添加存储为模板相关函数，放在handlePublish函数附近
const handleSaveTemplate = async () => {
  if (!editor.value) {
    ElMessage.warning(t('Editor.fullscreenEditor.message.editor_not_initialized'))
    return
  }

  try {
    // 检查编辑器内容是否为空
    if (!editor.value.getHTML()?.trim()) {
      ElMessage.warning(t('Editor.fullscreenEditor.message.content_empty', { action: t('Editor.fullscreenEditor.save_as_template') }))
      return
    }

    // 检查模板类型是否选择
    if (!templateId.value && !selectedTemplateType.value) {
      ElMessage.warning(t('Editor.fullscreenEditor.message.template_type_empty'))
      await fetchTemplateTypes()
      templateTypeDialogVisible.value = true
      return
    }

    // 检查标题是否为空
    let templateName = title.value
    if (!templateName || templateName === t('Editor.fullscreenEditor.no_title')) {
      const result = await ElMessageBox.prompt(t('Editor.fullscreenEditor.message.title_empty'), t('Editor.fullscreenEditor.message.title_empty'), {
        confirmButtonText: t('Editor.fullscreenEditor.confirm'),
        cancelButtonText: t('Editor.fullscreenEditor.cancel'),
        inputValue: templateName === t('Editor.fullscreenEditor.no_title') ? '' : templateName,
        inputValidator: value => value.trim() !== '' || t('Editor.fullscreenEditor.message.title_empty'),
      }).catch(() => null)

      if (!result) return
      templateName = result.value
    }

    // 如果是更新模板，添加确认对话框
    if (templateId.value) {
      try {
        await ElMessageBox.confirm(t('Editor.fullscreenEditor.message.template_confirm_update', { name: templateName }), t('Editor.fullscreenEditor.message.template_confirm_update_title'), {
          confirmButtonText: t('Editor.fullscreenEditor.message.template_confirm_update_confirm'),
          cancelButtonText: t('Editor.fullscreenEditor.message.template_confirm_update_cancel'),
          type: 'warning',
        })
      } catch (e) {
        return
      }
    }

    ElMessage.info(t('Editor.fullscreenEditor.message.processing', { action: templateId.value ? t('Editor.fullscreenEditor.update') : t('Editor.fullscreenEditor.save_as_template') }))

    // 构建模板数据
    const templateData = {
      name: templateName,
      content: JSON.stringify(editor.value.getHTML()),
      category: selectedTemplateType.value , 
    }
    const response = await (templateId.value 
      ? http.put(`/editor/templates/${templateId.value}`, templateData)
      : http.post('/editor/templates', templateData))

    if (response.data?.code === 200) {
      ElMessage.success(t('Editor.fullscreenEditor.message.' + (templateId.value ? 'template_update_success' : 'template_save_success')))
      selectedTemplateType.value = ''
      templateTypeDialogVisible.value = false
    } else {
      throw new Error(response.data?.message || t('Editor.fullscreenEditor.message.' + (templateId.value ? 'template_update_failed' : 'template_save_failed')))
    }
  } catch (error) {
    console.error(`${templateId.value ? '更新' : '保存'}模板失败:`, error)
    ElMessage.error(t('Editor.fullscreenEditor.message.' + (templateId.value ? 'template_update_failed' : 'template_save_failed'), { msg: error instanceof Error ? error.message : t('Editor.preview.unknown_error') }))
  }
}

// 在 script setup 部分添加以下代码
const templateTypes = ref([])
const selectedTemplateType = ref('')
const templateTypeDialogVisible = ref(false)

// 获取模版类型
const fetchTemplateTypes = async () => {
  try {
    const response = await http.get('/editor/templates/categories')
    if (response.data && response.data.code === 200) {
      templateTypes.value = response.data.data.map((type: any) => ({
        value: type.value,
        label: type.label,
        count: type.count,
      }))
    }
  } catch (error) {
    console.error('获取模版类型失败:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.fetch_template_type_failed'))
  }
}



// 添加编辑状态相关的数据
interface EditorUser {
  id: number
  name: string
  avatar?: string
}

const currentEditor = ref<EditorUser | null>({
  id: 1,
  name: '张三',
  avatar: ''
})
const editStartTime = ref(new Date())

// 格式化时间的方法
const formatTime = (time: Date) => {
  return time.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取头像文本的方法(当没有头像时显示)
const getAvatarText = (name: string) => {
  return name.charAt(0).toUpperCase()
}

// 模拟获取当前编辑者信息
const getCurrentEditor = async () => {
  try {
    // 这里应该调用后端API获取当前编辑者信息
    // const response = await http.get('/editor/current-editor')
    // if (response.data?.code === 200) {
    //   currentEditor.value = response.data.data
    //   editStartTime.value = new Date()
    // }
    currentEditor.value = {
      id: 1,
      name: '张三',
      avatar: ''
    }
    editStartTime.value = new Date()
  } catch (error) {
    console.error('获取编辑者信息失败:', error)
  }
}

// 在组件挂载时获取编辑者信息
onMounted(() => {
  getCurrentEditor()
  fetchTemplateTypes()
})

// 添加加载CMS内容的方法


// 添加加载模板内容的方法
const loadTemplateContent = async () => {
  try {
    const response = await http.get(`/editor/templates/${templateId.value}`)
    if (response.data && response.data.code === 200) {
      const template = response.data.data
      
      // 设置模板标题
      if (template.name) {
        title.value = template.name
      }
      
      // 设置模板类型 - 根据返回的 category 字段
      if (template.category) {
        selectedTemplateType.value = template.category
      }
      
      // 处理模板内容
      if (template.content && editor.value) {
        let templateHtml = ''
        
        try {
          // 内容是一个被转义的 JSON 字符串，需要先解析
          if (typeof template.content === 'string') {
            // 如果内容以引号开始，说明是被 JSON 编码的字符串，需要解析
            if (template.content.startsWith('"') && template.content.endsWith('"')) {
              // 解析 JSON 字符串
              templateHtml = JSON.parse(template.content)
            } else {
              // 直接使用字符串内容
              templateHtml = template.content
            }
          } else {
            // 如果是对象，尝试转换为字符串
            templateHtml = String(template.content)
          }
          
          // 设置编辑器内容
          if (templateHtml) {
            editor.value.commands.setContent(templateHtml)
            
            // 初始化 Bootstrap 组件
            setTimeout(() => {
              initBootstrapComponents()
            }, 100)
          }
          
          ElMessage.success(t('Editor.fullscreenEditor.message.template_load_success'))
        } catch (parseError) {
          console.error('解析模板内容失败:', parseError)
          ElMessage.error(t('Editor.fullscreenEditor.message.template_parse_failed'))
        }
      }
    } else {
      ElMessage.error(response.data?.message || t('Editor.fullscreenEditor.message.template_load_failed'))
    }
  } catch (error) {
    console.error('加载模板内容失败:', error)
    ElMessage.error(t('Editor.fullscreenEditor.message.template_load_failed'))
  }
}
</script>

<style lang="scss" scoped>
.fullscreen-editor-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f7fa;
}

/* 顶部导航栏样式 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 55px; /* 增加高度 */
  padding: 0 12px;
  background-color: #2d333b;
  color: white;
  border-bottom: 1px solid #222;
}

.header-left {
  display: flex;
  align-items: center;
  height: 100%;

  .exit-btn,
  .dropdown-btn {
    color: white;
    font-size: 14px;
    padding: 0 12px;
    height: 100%;
    border-radius: 0;
    transition: background-color 0.2s;

    &:hover {
      background-color: #414a53;
    }
  }

  .exit-btn {
    padding-left: 4px;
    padding-right: 14px;
    margin-right: 2px;
  }
}

.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  .title-container {
    display: flex;
    align-items: center;
    height: 28px; /* 增加高度 */

    .title {
      font-size: 16px;
      font-weight: 500;
      color: white;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .edit-title-btn {
      color: rgba(255, 255, 255, 0.7);
      padding: 6px; /* 增加内边距 */
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

  
  }

  .draft-status {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
  }
}
  /* 模板类型下拉框样式 */
  .template-type-select {
      margin-left: 10px;
      width: 140px;
      :deep( .el-select__wrapper ) {
       
        height: 35px;
        min-height: 35px !important;
        line-height: 35px !important;
      }

      :deep(.el-input__inner) {
        color: white;
        font-size: 13px;
        line-height: 32px !important; /* 增加行高 */
        height: 32px !important;
      }

      :deep(.el-select__caret) {
        color: rgba(255, 255, 255, 0.7);
        line-height: 32px; /* 增加行高 */
      }
    }
.header-right {
  display: flex;
  align-items: center;

  /* 添加编辑状态样式 */
  .editing-status {
    margin-right: 16px;
    padding: 4px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;

    .editor-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }

    .editor-avatar {
      border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .editor-name {
      color: #fff;
      font-size: 13px;
      font-weight: 500;
    }

    .editing-text {
      color: #67c23a;
      font-size: 12px;
      background: rgba(103, 194, 58, 0.1);
      padding: 2px 6px;
      border-radius: 10px;
    }
  }

  .auto-save-text {
    font-size: 13px;
    color: #a0a0a0;
    margin-right: 10px;
  }

  .search-btn {
    color: white;
    margin-right: 8px;

    &:hover {
      color: white;
      background-color: #414a53;
    }
  }
}

/* 设备切换工具栏 */
.device-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 45px; /* 增加高度 */
  padding: 0 12px; /* 增加左右内边距 */
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
}

.device-btns {
  .el-button-group {
    .el-button {
      padding: 8px 15px; /* 增加按钮内边距 */
      height: 35px; /* 增加按钮高度 */

      &.is-active {
        background-color: #ecf5ff;
        color: #409eff;
        border-color: #b3d8ff;
      }
    }
  }
}

.toolbar-actions {
  display: flex;
  gap: 5px;

  .action-btn {
    padding: 8px 10px; /* 增加按钮内边距 */
    height: 35px; /* 增加按钮高度 */

    &:hover {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }
}

/* 其余原有样式保持不变 */
.editor-layout {
  display: flex;
  width: 100%;
  height: calc(100% - 100px); /* 调整为顶部导航栏和工具栏的总高度 */
  overflow: hidden;
  position: relative;
}

.sidebar-container {
  width: 300px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.module-sidebar,
.block-edit-sidebar {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  transition: all 0.3s ease;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  line-height: 1;

  &:hover {
    color: #f56c6c;
  }
}

.sidebar-title {

  font-size: 16px;
  font-weight: 600;
  color: #303133;
}



.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: #409eff;
  }
}

.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
}

.tab-item {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  cursor: pointer;
  font-size: 16px;
  color: #303133;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;

  &:hover {
    color: #409eff;
  }

  &.active {
    color: #409eff;
    border-bottom-color: #409eff;
    font-weight: 500;
  }
}

.module-list,
.block-edit-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.tab-content,
.edit-tab-content {
  height: 100%;
}

.edit-section {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #606266;
  }

  input[type='text'],
  input[type='number'],
  select,
  textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;

    &:focus {
      border-color: #409eff;
    }
  }

  input[type='checkbox'] {
    margin-right: 5px;
  }
}

.button-items {
  .button-item {
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #f8f9fa;

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .item-title {
        font-weight: 500;
        color: #303133;
      }

      .remove-button {
        background: none;
        border: none;
        color: #f56c6c;
        cursor: pointer;
        padding: 0;
        font-size: 16px;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .add-button {
    width: 100%;
    padding: 8px 0;
    background-color: #ecf5ff;
    color: #409eff;
    border: 1px dashed #b3d8ff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;

    &:hover {
      background-color: #d9ecff;
      border-color: #409eff;
    }
  }
}

.align-options {
  display: flex;
  gap: 5px;

  .align-option {
    flex: 1;
    padding: 8px 0;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #ecf5ff;
      color: #409eff;
      border-color: #b3d8ff;
    }
  }
}

.margin-inputs,
.padding-inputs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .margin-input,
  .padding-input {
    display: flex;
    flex-direction: column;

    span {
      font-size: 12px;
      color: #909399;
      margin-bottom: 5px;
    }

    input {
      width: 100%;
    }
  }
}

.placeholder-text {
  color: #909399;
  font-style: italic;
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.module-grid {
 display: flex;
 flex-wrap: wrap;
 justify-content: space-between;
 align-items: center;
 gap: 12px;
 margin-bottom: 16px;
}

.show-more-container {
  text-align: center;
  margin-top: 16px;
}

.show-more-button {
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;

  &:hover {
    text-decoration: underline;
  }
}

.show-more-icon {
  margin-right: 4px;
  font-size: 16px;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  height: 100%;
}

.toolbar-container {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  overflow-x: auto;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

button {
  padding: 5px 10px;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 32px;
  white-space: nowrap;

  &:hover {
    background-color: #f5f7fa;
  }

  &.is-active {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #b3d8ff;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.insert-button {
  background-color: #409eff;
  color: white;
  border-color: #409eff;

  &:hover {
    background-color: #66b1ff;
  }
}

.editor-content-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #fff;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  :deep(.ProseMirror) {
    min-height: 100%;
    padding-bottom: 100px; /* 确保底部有足够空间 */
    outline: none;

    &:focus {
      outline: none;
    }

    p {
      margin: 1em 0;
    }

    ul,
    ol {
      padding-left: 2em;
      margin: 1em 0;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      line-height: 1.1;
      margin-top: 1.5rem;
      margin-bottom: 1rem;
    }

    blockquote {
      border-left: 3px solid #dcdfe6;
      padding-left: 1rem;
      margin: 1rem 0;
    }

    code {
      background-color: #f5f7fa;
      border-radius: 3px;
      padding: 0.2em 0.4em;
    }

    pre {
      background-color: #f5f7fa;
      padding: 0.75rem 1rem;
      border-radius: 5px;
      overflow-x: auto;
    }

    hr {
      border: none;
      border-top: 1px solid #dcdfe6;
      margin: 2rem 0;
    }

    img {
      max-width: 100%;
      height: auto;
    }

    table {
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
      margin: 1rem 0;
      overflow: hidden;

      td,
      th {
        border: 1px solid #dcdfe6;
        padding: 8px;
        position: relative;
        min-width: 100px;
        vertical-align: top;
      }

      th {
        background-color: #f5f7fa;
        font-weight: bold;
      }
    }
  }

  &.dragover {
    background-color: rgba(64, 158, 255, 0.1);
    border: 2px dashed #409eff;
  }
}

/* 适配Bootstrap组件样式 */
:deep([data-bs-component]) {
  margin: 1em 0;
  max-width: 100%;
  position: relative;
  transition: all 0.3s ease;
  border: 1px dashed transparent;

  &:hover {
    border-color: #c0c4cc;
    cursor: pointer;
  }

  &.bs-component-selected {
    border: 2px solid #409eff;
    box-shadow: 0 0 0 1px #409eff;

    &::before {
      content: attr(data-bs-component);
      position: absolute;
      top: -22px;
      left: 0;
      background-color: #409eff;
      color: white;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 3px 3px 0 0;
      z-index: 1;
    }
  }
}

/* 拖拽功能相关样式 */
:deep(.draggable-handle) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 20px;
  cursor: move;
  background-color: rgba(0, 0, 0, 0.05);
  display: none;
  align-items: center;
  justify-content: center;

  &::after {
    content: '⋮⋮';
    color: #909399;
  }
}

:deep([data-bs-component]:hover .draggable-handle) {
  display: flex;
}

:deep([data-bs-component].bs-component-dragging) {
  opacity: 0.5;
  outline: 2px dashed #409eff;
  z-index: 1000;
}

/* AI 菜单样式 */
.ai-button {
  background-color: #6f42c1;
  color: white;
  border-color: #6f42c1;
  position: relative;

  &:hover {
    background-color: #5a32a3;
  }
}

.ai-dropdown-menu {
  position: absolute;
  top: 50px;
  left: 10px;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10000;
  width: 200px;
  padding: 5px 0;
}

.ai-menu-header {
  padding: 8px 16px;
  font-weight: bold;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 5px;
}

.ai-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
    color: #409eff;
  }
}

/* 自定义提示词对话框 */
.custom-prompt-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10001;
  width: 500px;
  max-width: 90vw;
}

.custom-prompt-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 20px;
    color: #909399;
    cursor: pointer;
    padding: 0;
    line-height: 1;
  }
}

.custom-prompt-body {
  padding: 20px;
}

.custom-prompt-textarea {
  width: 100%;
  height: 150px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: #409eff;
  }
}

.custom-prompt-footer {
  padding: 10px 20px 20px;
  text-align: right;

  button {
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .cancel-button {
    background-color: white;
    border: 1px solid #dcdfe6;
    color: #606266;
    margin-right: 10px;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }

  .submit-button {
    background-color: #409eff;
    border: 1px solid #409eff;
    color: white;

    &:hover {
      background-color: #66b1ff;
    }
  }
}

/* AI 处理加载指示器 */
.ai-processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10002;
}

.ai-processing-content {
  background-color: white;
  padding: 20px 30px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ai-loader {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.ai-processing-text {
  font-size: 16px;
  color: #606266;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 气泡菜单和浮动菜单样式 */
:global(.bubble-menu) {
  display: flex;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10000;
}

:global(.bubble-menu-container) {
  display: flex;
  align-items: center;
}

:global(.bubble-button) {
  padding: 5px 10px;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  height: 30px;
  white-space: nowrap;
}

:global(.bubble-button:hover) {
  background-color: #f5f7fa;
}

:global(.bubble-dropdown) {
  position: relative;
}

:global(.bubble-dropdown-content) {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10001;
  min-width: 150px;
  padding: 5px 0;
}

:global(.bubble-dropdown-item) {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  white-space: nowrap;
}

:global(.bubble-dropdown-item:hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

:global(.bubble-dropdown-submenu) {
  position: relative;
}

:global(.bubble-dropdown-submenu-content) {
  display: none;
  position: absolute;
  top: 0;
  left: 100%;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10002;
  min-width: 150px;
  padding: 5px 0;
}

:global(.floating-menu) {
  display: flex;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 10000;
}

:global(.floating-menu-container) {
  display: flex;
  flex-direction: column;
  align-items: center;
}
:global(.floating-button) {
  padding: 5px 10px;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 2px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  height: 30px;
  white-space: nowrap;
}

:global(.floating-button:hover) {
  background-color: #f5f7fa;
}

.debug-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  font-size: 12px;
  color: #6c757d;
  z-index: 10;
}

.module-category {
  margin-bottom: 12px;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  color: #303133;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
}

.expand-icon {
  margin-right: 8px;
  font-size: 10px;
  color: #606266;
}

.category-name {
  flex: 1;
}

.category-count {
  color: #909399;
  font-size: 12px;
}

.category-content {
  padding: 8px 0 ;
}

.module-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* 响应式块的样式 */
:deep(.responsive-block) {
  margin: 2rem 0;
  padding: 10px 0;
  position: relative;
  transition: all 0.2s ease-in-out;
  border: 1px dashed transparent;

  &:hover {
    border-color: #c0c4cc;
    background-color: rgba(64, 158, 255, 0.05);
  }

  &.bs-component-selected {
    border: 2px solid #409eff;
    background-color: rgba(64, 158, 255, 0.05);

    &::before {
      content: attr(data-bs-component);
      position: absolute;
      top: -26px;
      left: 0;
      background-color: #409eff;
      color: white;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 3px 3px 0 0;
      z-index: 100;
    }

    /* 编辑控件 */
    &::after {
      content: '';
      position: absolute;
      top: -12px;
      right: 10px;
      display: flex;
      gap: 5px;
    }
  }

  /* 容器控制 */
  .container-fluid {
    transition: all 0.3s ease;
  }

  /* 使块内组件固有宽度恢复自然 */
  .card {
    width: auto;
    max-width: 100%;
  }

  /* 为HubSpot风格的编辑体验添加垂直间距 */
  .row {
    min-height: 40px; /* 确保空块也有最小高度 */
  }
}

/* 可拖动区域指示器 */
:deep(.responsive-block:hover::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to bottom, rgba(64, 158, 255, 0.2), transparent);
  z-index: 1;
  cursor: move;
}

/* 响应式宽度调节控件 - 仿HubSpot样式 */
:deep(.responsive-block.bs-component-selected) {
  .width-control {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 15px;
    padding: 2px 8px;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 100;

    .width-option {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;

      &:hover {
        background-color: #f0f2f5;
      }

      &.active {
        background-color: #ecf5ff;
        color: #409eff;
      }
    }
  }
}

/* 自适应设备预览指示器 */
:deep(.responsive-preview-indicator) {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;

  .device-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    &:hover,
    &.active {
      background: #409eff;
      color: white;
    }
  }
}

/* 布局模块样式 */
.bootstrap-layout {
  border: 1px dashed #ccc;
  position: relative;
  padding-top: 2rem;
  margin-bottom: 2rem;

  &::before {
    content: attr(data-bs-layout);
    position: absolute;
    top: 0;
    left: 0;
    background: #f8f9fa;
    padding: 2px 8px;
    font-size: 12px;
    color: #666;
    border-right: 1px dashed #ccc;
    border-bottom: 1px dashed #ccc;
    z-index: 10;
  }

  [data-layout-container] {
    min-height: 100px;
    border: 1px dashed #ddd;
    padding: 10px;
    position: relative;

    &::before {
      content: attr(data-layout-container);
      position: absolute;
      top: 0;
      right: 0;
      background: #f8f9fa;
      padding: 2px 8px;
      font-size: 11px;
      color: #999;
      border-left: 1px dashed #ddd;
      border-bottom: 1px dashed #ddd;
      z-index: 10;
    }

    &:hover {
      background-color: rgba(0, 123, 255, 0.05);
    }

    .text-muted.small {
      opacity: 0.6;
    }
  }

  &:hover {
    border-color: #80bdff;
  }
}

// 当布局模块被选中时的样式
.bs-component-selected.bootstrap-layout,
div[data-bs-component='layout'].bs-component-selected {
  border: 2px solid #007bff !important;

  &::before {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
}

/* 预览按钮样式 */
.preview-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #67c23a;
  color: white;
}

.preview-button:hover {
  background-color: #85ce61;
}

/* 存储为模板按钮样式 */
.save-template-btn {
  margin-right: 8px;
  display: flex;
  align-items: center;
  color: #e6a23c;

  .el-icon {
    margin-right: 4px;
  }
}

/* 抽屉头部样式 */
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.drawer-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* AI处理加载样式 */
.ai-processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.ai-processing-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.ai-loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.preview-btn {
  margin-right: 8px;
  display: flex;
  align-items: center;
  color: #007bff;
  .el-icon {
    margin-right: 4px;
  }
}

/* 设备预览样式 */
.editor-content {
  display: flex;
  justify-content: center;
  padding: 20px;
  transition: all 0.3s ease;
}

.editor-content-area {
  width: 100%;
  transition: all 0.3s ease;
  background-color: #fff;
}

/* 桌面设备预览 */
.desktop-preview .editor-content-area {
  max-width: 100%;
}

/* 平板设备预览 */
.tablet-preview .editor-content-area {
  max-width: 768px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 手机设备预览 */
.mobile-preview .editor-content-area {
  max-width: 375px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 设备切换按钮样式 */
.device-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.device-btns .el-button-group .el-button {
  padding: 8px 12px;
}

.device-btns .el-button-group .el-button.is-active {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}

/* AI优化悬浮按钮样式 */
.ai-optimize-button {
  position: fixed;
  right: 20px;
  bottom: 80px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #8957e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s;

  .el-icon {
    font-size: 24px;
  }

  &:hover {
    background-color: #9c67ff;
    transform: scale(1.05);
  }
}

/* 模板类型选择对话框 */
.template-type-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10001;
  width: 600px;
  max-width: 90vw;
}

.template-type-content {
  padding: 20px;
}

.template-type-item {
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  &:hover {
    background-color: #ecf5ff;
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background-color: #ecf5ff;
    border-color: #409eff;
    position: relative;

    &::after {
      content: '✓';
      position: absolute;
      top: 10px;
      right: 10px;
      color: #409eff;
      font-weight: bold;
    }
  }

  .type-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
  }

  .type-count {
    font-size: 12px;
    color: #909399;
  }
}

.dialog-footer {
  padding: 10px 20px 20px;
  text-align: right;

  button {
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .cancel-button {
    background-color: white;
    border: 1px solid #dcdfe6;
    color: #606266;
    margin-right: 10px;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }

  .confirm-button {
    background-color: #409eff;
    border: 1px solid #409eff;
    color: white;

    &:hover {
      background-color: #66b1ff;
    }
  }
}

/* 添加模板类型下拉选项样式 */
:deep(.template-type-popper) {
  .el-select-dropdown__item {
    padding: 8px 15px;
    font-size: 13px;
  }
}
</style>

