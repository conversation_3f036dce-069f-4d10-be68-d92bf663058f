function receiveMessage(e){try{var data=typeof e.data==="string"||e.data instanceof String?JSON.parse(e.data):e.data;switch(data.action){case"view":console.log("form","view");break;case"fill":console.log("form","fill");break;case"nextStep":console.log("form","nextStep");break;case"previousStep":console.log("form","previousStep");break;case"beforeSubmit":console.log("form","beforeSubmit");break;case"error":console.log("form","error",data.data);break;case"success":console.log("form","success",data.data,data.completionTime)}}catch(e){return false}}window.addEventListener("message",receiveMessage);