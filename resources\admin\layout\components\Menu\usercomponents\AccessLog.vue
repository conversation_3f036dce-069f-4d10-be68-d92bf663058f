<template>
  <div class="bwms-module">
    <div class="module-header">
      <el-button @click="filterDialog = true">
        <el-icon><Filter /></el-icon>
        <span>{{ t('dashboard.accessLog.filter.button') }}</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table 
          :data="logs" 
          style="width: 100%"
          v-loading="loading"
        >
          <el-table-column 
            prop="loginTime" 
            :label="t('dashboard.accessLog.table.loginTime')" 
            width="180" 
          />
          <el-table-column 
            prop="loginUser" 
            :label="t('dashboard.accessLog.table.loginUser')" 
            width="120"
          >
            <template #default="scope">
              <el-tag size="small" type="info">{{ scope.row.loginUser }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="ipAddress" 
            :label="t('dashboard.accessLog.table.ipAddress')" 
            width="140" 
          />
          <el-table-column 
            prop="status" 
            :label="t('dashboard.accessLog.table.status')" 
            width="80"
          >
            <template #default="scope">
              <el-tag 
                :type="scope.row.status === t('dashboard.accessLog.table.statusSuccess') ? 'success' : 'danger'" 
                size="small"
              >
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="browser" 
            :label="t('dashboard.accessLog.table.browser')" 
            width="100" 
          />
          <el-table-column 
            prop="location" 
            :label="t('dashboard.accessLog.table.location')" 
          />
        </el-table>

        <div class="box-footer">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <el-dialog 
      v-model="filterDialog" 
      :title="t('dashboard.accessLog.filter.title')" 
      width="570"
    >
      <el-form :model="filterParams" label-position="top">
        <el-form-item :label="t('dashboard.accessLog.filter.ipAddress')">
          <el-input 
            v-model="filterParams.clientIp" 
            :placeholder="t('dashboard.accessLog.filter.ipPlaceholder')" 
            size="large" 
          />
        </el-form-item>
        <el-form-item :label="t('dashboard.accessLog.filter.dateRange')">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            :start-placeholder="t('dashboard.accessLog.filter.startDate')"
            :end-placeholder="t('dashboard.accessLog.filter.endDate')"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59)
            ]"
            size="large"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="justify-end btn-list">
          <el-button @click="resetFilter">
            <el-icon><RefreshLeft /></el-icon>
            <span>{{ t('dashboard.accessLog.filter.reset') }}</span>
          </el-button>
          <el-button type="primary" @click="handleFilter">
            <el-icon><Search /></el-icon>
            <span>{{ t('dashboard.accessLog.filter.apply') }}</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { UserCenterService } from '../application/UserCenterService'
import { ElMessage } from 'element-plus'
import { Filter, Search, RefreshLeft } from '@element-plus/icons-vue'

const { t } = useI18n()
const userCenterService = new UserCenterService()
const filterDialog = ref(false)
const logs = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const appCode = ref(localStorage.getItem('app_code') || 'admin')
const dateRange = ref<Date[]>([])

const filterParams = ref({
  clientIp: '',
})

const fetchLoginHistory = async () => {
  loading.value = true
  try {
    const response = await userCenterService.getLoginHistory({
      app_code: appCode.value,
      clientIp: filterParams.value.clientIp,
      success: true,
      start: dateRange.value[0] ? dateRange.value[0].getTime() : undefined,
      end: dateRange.value[1] ? dateRange.value[1].getTime() : undefined,
      page: currentPage.value,
      limit: pageSize.value,
    })
    logs.value = response.data.map((item: any) => ({
      loginTime: new Date(item.timestamp).toLocaleString(),
      loginUser: item.username,
      ipAddress: item.ip,
      status: item.success ? t('dashboard.accessLog.table.statusSuccess') : t('dashboard.accessLog.table.statusFailed'),
      browser: item.userAgent,
      location: item.location || '-',
    }))
    total.value = response.total
  } catch (error) {
    console.error('获取登录历史失败', error)
    ElMessage.error(t('dashboard.accessLog.error.fetchFailed'))
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchLoginHistory()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchLoginHistory()
}

const handleFilter = () => {
  currentPage.value = 1
  fetchLoginHistory()
  filterDialog.value = false
}

const resetFilter = () => {
  filterParams.value.clientIp = ''
  dateRange.value = []
  currentPage.value = 1
  fetchLoginHistory()
  filterDialog.value = false
}

onMounted(() => {
  fetchLoginHistory()
})
</script>

<style lang="scss" scoped>
.box-footer {
  margin-top: 20px;
  padding: 20px 0;
  display: flex;
  justify-content: flex-end;
}

.btn-list {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 25px;
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 4px;
  .el-icon {
    margin: 0;
  }
}
</style>
