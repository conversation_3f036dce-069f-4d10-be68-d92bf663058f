/**
 * 表单模板
 */

export const formTemplate = `
<div data-bs-component="form" class="form-block responsive-block">
  <div class="container-fluid p-0">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <form>
          <div class="mb-3">
            <label for="exampleInputEmail1" class="form-label">邮箱地址</label>
            <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
            <div id="emailHelp" class="form-text">我们不会与其他人分享您的邮箱。</div>
          </div>
          <div class="mb-3">
            <label for="exampleInputPassword1" class="form-label">密码</label>
            <input type="password" class="form-control" id="exampleInputPassword1">
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="exampleCheck1">
            <label class="form-check-label" for="exampleCheck1">记住我</label>
          </div>
          <button type="submit" class="btn btn-primary">提交</button>
        </form>
      </div>
    </div>
  </div>
</div>
`;

export default formTemplate; 