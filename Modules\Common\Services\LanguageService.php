<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use Modules\Multilingual\Models\Multilingual;
use Modules\Multilingual\Domain\FlagLang;
use Illuminate\Support\Facades\Cache;

/**
 * 语言服务类
 * 
 * 提供统一的语言获取接口，避免硬编码语言列表
 */
final readonly class LanguageService
{
    public function __construct(
        private FlagLang $flagLang
    ) {}

    /**
     * 获取系统支持的所有语言
     * 
     * @return array 格式: ['zh_CN' => '简体中文', 'en' => 'English', ...]
     */
    public function getSupportedLanguages(): array
    {
        return Cache::remember('system_supported_languages', 3600, function () {
            try {
                // 优先从数据库获取已启用的语言
                $dbLanguages = $this->getEnabledLanguagesFromDb();
                if (!empty($dbLanguages)) {
                    return $dbLanguages;
                }
                
                // 如果数据库没有数据，使用预定义的语言列表
                return $this->getDefaultLanguages();
            } catch (\Exception $e) {
                // 异常情况下返回默认语言
                return $this->getDefaultLanguages();
            }
        });
    }

    /**
     * 获取当前站点启用的语言
     * 
     * @return array
     */
    public function getEnabledLanguages(): array
    {
        return Cache::remember('enabled_languages', 1800, function () {
            try {
                return $this->getEnabledLanguagesFromDb();
            } catch (\Exception $e) {
                return $this->getDefaultLanguages();
            }
        });
    }

    /**
     * 获取语言代码列表
     * 
     * @return array ['zh_CN', 'en', 'zh_HK', ...]
     */
    public function getLanguageCodes(): array
    {
        return array_keys($this->getSupportedLanguages());
    }

    /**
     * 检查语言是否被支持
     * 
     * @param string $languageCode
     * @return bool
     */
    public function isLanguageSupported(string $languageCode): bool
    {
        return array_key_exists($languageCode, $this->getSupportedLanguages());
    }

    /**
     * 获取语言的显示名称
     * 
     * @param string $languageCode
     * @return string
     */
    public function getLanguageName(string $languageCode): string
    {
        $languages = $this->getSupportedLanguages();
        return $languages[$languageCode] ?? $languageCode;
    }

    /**
     * 清除语言缓存
     * 
     * @return void
     */
    public function clearCache(): void
    {
        Cache::forget('system_supported_languages');
        Cache::forget('enabled_languages');
    }

    /**
     * 从数据库获取已启用的语言
     * 
     * @return array
     */
    private function getEnabledLanguagesFromDb(): array
    {
        $languages = [];
        
        // 从multilingual表获取启用的语言
        $multilingualRecords = Multilingual::where('status', 1)
            ->orderBy('sort_order')
            ->get(['mark', 'title']);
            
        foreach ($multilingualRecords as $record) {
            $languages[$record->mark] = $record->title;
        }
        
        return $languages;
    }

    /**
     * 获取默认语言列表
     * 
     * @return array
     */
    private function getDefaultLanguages(): array
    {
        try {
            // 使用FlagLang获取所有语言
            $flagLanguages = $this->flagLang->AllLanguage();
            $languages = [];
            
            foreach ($flagLanguages as $lang) {
                $languages[$lang['mark']] = $lang['name'];
            }
            
            return $languages;
        } catch (\Exception $e) {
            // 最后的备用语言列表
            return [
                'zh_CN' => '简体中文',
                'zh_HK' => '繁體中文(香港)',
                'zh_TW' => '繁體中文(台灣)',
                'en' => 'English',
                'ja' => '日本語',
                'ko' => '한국어',
                'th' => 'ไทย',
                'vi' => 'Tiếng Việt',
                'id' => 'Bahasa Indonesia',
                'ms' => 'Bahasa Melayu',
                'es' => 'Español',
                'fr' => 'Français',
                'de' => 'Deutsch',
                'it' => 'Italiano',
                'pt' => 'Português',
                'ru' => 'Русский',
                'ar' => 'العربية',
            ];
        }
    }
} 