<!-- 块控制组件 -->
<template>
  <div class="block-controls" :style="controlsStyle">
    <div class="block-controls-buttons">
      <button class="control-button" :title="$t('Editor.blockControls.copy')" @click="handleCopy">
        <el-icon><DocumentCopy /></el-icon>
      </button>
      <button class="control-button" :title="$t('Editor.blockControls.delete')" @click="handleDelete">
        <el-icon><Delete /></el-icon>
      </button>
      <button class="control-button" :title="$t('Editor.blockControls.up')" @click="handleMoveUp">
        <el-icon><ArrowUp /></el-icon>
      </button>
      <button class="control-button" :title="$t('Editor.blockControls.down')" @click="handleMoveDown">
        <el-icon><ArrowDown /></el-icon>
      </button>
      <button class="control-button ai-optimize-button" :title="$t('Editor.blockControls.aiOptimize')" @click="handleAiOptimize">
        <el-icon><MagicStick /></el-icon>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import { DocumentCopy, Delete, ArrowUp, ArrowDown, MagicStick } from '@element-plus/icons-vue'

// 为了解决TypeScript类型问题，添加默认导出
defineOptions({
  name: 'BlockControls'
})

const props = defineProps({
  position: {
    type: Object,
    required: true,
    default: () => ({ top: 0, left: 0 })
  }
})

const emit = defineEmits(['copy', 'delete', 'move-up', 'move-down', 'ai-optimize'])

const { t } = useI18n()

// 计算控制按钮样式
const controlsStyle = computed(() => {
  return {
    top: `${props.position.top}px`,
    left: `${props.position.left}px`
  }
})

// 处理复制
const handleCopy = () => {
  emit('copy')
}

// 处理删除
const handleDelete = () => {
  emit('delete')
}

// 处理上移
const handleMoveUp = () => {
  emit('move-up')
}

// 处理下移
const handleMoveDown = () => {
  emit('move-down')
}

// 处理AI优化
const handleAiOptimize = () => {
  emit('ai-optimize')
}
</script>

<style lang="scss" scoped>
.block-controls {
  position: absolute;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 999;
  padding: 4px;
  display: flex;
  border: 1px solid #ebeef5;
}

.block-controls-buttons {
  display: flex;
  gap: 4px;
}

.control-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;

  &:hover {
    color: #409eff;
    background-color: #ecf5ff;
    border-color: #c6e2ff;
  }
  
  &.ai-optimize-button {
    color: #8957e5;
    
    &:hover {
      color: #9c67ff;
      background-color: #f5f0ff;
      border-color: #d3c6ff;
    }
  }
}
</style>