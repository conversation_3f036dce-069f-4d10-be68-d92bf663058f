<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button class="button-no-border back-btn" @click="router.push(`/config/settings`)">
          <el-icon><ArrowLeft /></el-icon>
          <span>返回</span>
        </el-button>
      </div>
    </div>
    <div class="module-con">
      <ConfigList />
      <div class="box config-info">
        <transition name="slide-up" mode="out-in">
          <AMisRenderer :key="route.path" :amis-json="thisPage.pageJson" v-if="!thisPage.error && thisPage.pageJson" />
        </transition>
        <div v-if="thisPage.error && !thisPage.loading">
          <el-alert :closable="false" type="error" show-icon>{{ thisPage.errorMessage }}</el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { onMounted, watch } from 'vue'
import AMisRenderer from '/admin/components/amis/AMisRenderer.vue'
import { storeToRefs } from 'pinia'
import { usePagesStore } from '/admin/stores/pages'
import { ElAlert } from 'element-plus'
import ConfigList from '@/module/Config/views/components/configList.vue'

const { thisPage } = storeToRefs(usePagesStore())
const { getPageJson } = usePagesStore()
const route = useRoute()
const router = useRouter()

// 监听路由变化，更新页面 JSON 数据
watch(
  () => route.fullPath,
  async (fullPath: string) => {
    await getPageJson(fullPath)
  },
)

onMounted(async () => {
  await getPageJson(route.fullPath)
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    flex-direction: row;

    .box {
      border-radius: 10px;
      padding: 20px;
      background-color: #fff;
      overflow-y: auto;
      flex-grow: 0;

      &.config-info {
        margin-left: 20px;
        flex-grow: 1;

        &::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }

        &::-webkit-scrollbar-button {
          display: none;
        }

        &::-webkit-scrollbar-corner {
          background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgb(0, 126, 229, 0.3);
          border-radius: 5px;
          background-clip: content-box;
        }
      }
    }
  }
}
</style>
