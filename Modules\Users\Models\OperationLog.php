<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 操作日志模型
 *
 * @property int $log_id 日志ID
 * @property int $admin_id 操作人ID
 * @property string $module 操作模块
 * @property string $action 操作类型
 * @property string|null $resource_type 资源类型
 * @property string|null $resource_id 资源ID
 * @property string|null $description 操作描述
 * @property array|null $request_data 请求数据
 * @property array|null $response_data 响应数据
 * @property string|null $ip_address IP地址
 * @property string|null $user_agent 用户代理
 * @property int $status 操作状态: 0-失败, 1-成功
 * @property string|null $error_message 错误信息
 * @property int|null $execution_time 执行时间(毫秒)
 * @property \Carbon\Carbon $created_at 创建时间
 */
class OperationLog extends Model
{
    protected $table = 'operation_logs';
    protected $primaryKey = 'log_id';

    protected $fillable = [
        'admin_id',
        'module',
        'action',
        'resource_type',
        'resource_id',
        'description',
        'request_data',
        'response_data',
        'ip_address',
        'user_agent',
        'status',
        'error_message',
        'execution_time'
    ];

    protected $casts = [
        'admin_id' => 'integer',
        'resource_id' => 'string',
        'request_data' => 'array',
        'response_data' => 'array',
        'status' => 'integer',
        'execution_time' => 'integer',
        'created_at' => 'datetime'
    ];

    /**
     * 获取操作的管理员
     *
     * @return BelongsTo
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'admin_id');
    }

    /**
     * 检查操作是否成功
     *
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查操作是否失败
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === 0;
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute(): string
    {
        return $this->isSuccess() ? '成功' : '失败';
    }

    /**
     * 获取执行时间文本
     *
     * @return string
     */
    public function getExecutionTimeTextAttribute(): string
    {
        if ($this->execution_time === null) {
            return '未知';
        }

        if ($this->execution_time < 1000) {
            return $this->execution_time . 'ms';
        }

        return round($this->execution_time / 1000, 2) . 's';
    }

    /**
     * 获取操作类型文本
     *
     * @return string
     */
    public function getActionTextAttribute(): string
    {
        $actions = [
            'view' => '查看',
            'create' => '创建',
            'edit' => '编辑',
            'update' => '更新',
            'delete' => '删除',
            'publish' => '发布',
            'review' => '审核',
            'pin' => '置顶',
            'archive' => '归档',
            'upload' => '上传',
            'download' => '下载',
            'export' => '导出',
            'import' => '导入',
            'login' => '登录',
            'logout' => '登出',
            'reset_password' => '重置密码',
            'assign_role' => '分配角色',
            'assign_permission' => '分配权限',
            'enable' => '启用',
            'disable' => '禁用'
        ];

        return $actions[$this->action] ?? $this->action;
    }

    /**
     * 获取模块文本
     *
     * @return string
     */
    public function getModuleTextAttribute(): string
    {
        $modules = [
            'user' => '用户管理',
            'role' => '角色管理',
            'permission' => '权限管理',
            'menu' => '菜单管理',
            'article' => '文章管理',
            'video' => '视频管理',
            'category' => '分类管理',
            'tag' => '标签管理',
            'pen_name' => '笔名管理',
            'push' => '推送管理',
            'voting' => '投票管理',
            'system' => '系统管理',
            'auth' => '认证管理'
        ];

        return $modules[$this->module] ?? $this->module;
    }

    /**
     * 作用域：成功的操作
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：失败的操作
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：按管理员筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $adminId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAdmin($query, int $adminId)
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * 作用域：按模块筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $module
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * 作用域：按操作类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 作用域：按资源类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $resourceType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByResourceType($query, string $resourceType)
    {
        return $query->where('resource_type', $resourceType);
    }

    /**
     * 作用域：按时间范围筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：按IP地址筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $ipAddress
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByIpAddress($query, string $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * 作用域：慢查询（执行时间超过阈值）
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $threshold 阈值（毫秒）
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSlowQuery($query, int $threshold = 1000)
    {
        return $query->where('execution_time', '>', $threshold);
    }
}
