<?php

namespace Modules\Common\Provider\HomePage;

use Bingo\Core\Config\BingoConfig;
use Bingo\Core\Util\AgentUtil;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

class HomePageProvider
{
    /**
     * @var AbstractHomePageProvider[]
     */
    private static array $instances = [
        DefaultHomePageProvider::class,
        DefaultMobileHomePageProvider::class,
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    public static function registerQuick($title, $action, $type = [AbstractHomePageProvider::TYPE_PC, AbstractHomePageProvider::TYPE_MOBILE]): void
    {
        self::register(
            QuickHomePageProvider::make($title, $action, $type)
        );
    }

    /**
     * @return AbstractHomePageProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function call($contextMethod, $defaultAction)
    {
        $controller = null;
        if (bingostart_config('HomePage_Enable', BingoConfig::DEFAULT_LANG, false)) {
            if (AgentUtil::isMobile()) {
                $controller = bingostart_config('HomePage_HomeMobile');
            }
            if (empty($controller)) {
                $controller = bingostart_config('HomePage_Home');
            }
        }
        if (empty($controller)) {
            $controller = $defaultAction;
        }
        BizException::throwsIfEmpty($controller, Code::FAILED, '首页不存在');
        list($c, $a) = explode('@', $controller);
        list($contextC, $contextA) = explode('::', $contextMethod);
        if (! starts_with($contextC, '\\')) {
            $contextC = '\\'.$contextC;
        }
        if ($contextC == $c && $contextA == $a) {
            list($c, $a) = explode('@', $defaultAction);
        }
        $c = app($c);
        return app()->call([$c, $a]);
    }
}
