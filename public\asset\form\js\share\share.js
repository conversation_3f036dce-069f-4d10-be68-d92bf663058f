document.addEventListener('DOMContentLoaded', function() {
    // PopUpp Margin
    var popupMarginEl = document.getElementById('popupMarginEl')
    var popupMarginSlider = noUiSlider.create(popupMarginEl, {
        tooltips: true,
        step: 1,
        start: 60,
        range: { min: 0, max: 120 },
    })
    var popupMarginField = document.getElementById('popupform-popup_margin')
    popupMarginSlider.on('update', function(values, handle) {
        popupMarginField.value = values[handle]
    })
    // PopUp Padding
    var popupPaddingEl = document.getElementById('popupPaddingEl')
    var popupPaddingSlider = noUiSlider.create(popupPaddingEl, {
        tooltips: true,
        step: 1,
        start: 20,
        range: { min: 0, max: 60 },
    })
    var popupPaddingField = document.getElementById('popupform-popup_padding')
    popupPaddingSlider.on('update', function(values, handle) {
        popupPaddingField.value = values[handle]
    })
    // PopUp Width
    var popupWidthEl = document.getElementById('popupWidthEl')
    var popupWidthSlider = noUiSlider.create(popupWidthEl, {
        tooltips: true,
        step: 1,
        start: 60,
        range: { min: 20, max: 90 },
    })
    var popupWidthField = document.getElementById('popupform-popup_width')
    popupWidthSlider.on('update', function(values, handle) {
        popupWidthField.value = values[handle]
    })
    // PopUp Radius
    var popupRadiusEl = document.getElementById('popupRadiusEl')
    var popupRadiusSlider = noUiSlider.create(popupRadiusEl, {
        tooltips: true,
        step: 1,
        start: 10,
        range: { min: 0, max: 60 },
    })
    var popupRadiusField = document.getElementById('popupform-popup_radius')
    popupRadiusSlider.on('update', function(values, handle) {
        popupRadiusField.value = values[handle]
    })
})
$(document).ready(function() {
    $('#showEmbed').on('click', function(e) {
        e.preventDefault()
        history.pushState({}, '', this.href)
        location.hash = 'embed'
        $('#embed').show()
        $('#popUp').hide()
        $('#link').hide()
        $('#qr').hide()
        $('#download').hide()
        $('#showEmbed').addClass('active')
        $('#showPopUp').removeClass('active')
        $('#showLink').removeClass('active')
        $('#showQrCode').removeClass('active')
        $('#showHtml').removeClass('active')
    })
    $('#showPopUp').on('click', function(e) {
        e.preventDefault()
        history.pushState({}, '', this.href)
        location.hash = 'popUp'
        $('#embed').hide()
        $('#popUp').show()
        $('#link').hide()
        $('#qr').hide()
        $('#download').hide()
        $('#showEmbed').removeClass('active')
        $('#showPopUp').addClass('active')
        $('#showLink').removeClass('active')
        $('#showQrCode').removeClass('active')
        $('#showHtml').removeClass('active')
    })
    $('#showLink').on('click', function(e) {
        e.preventDefault()
        history.pushState({}, '', this.href)
        location.hash = 'link'
        $('#embed').hide()
        $('#popUp').hide()
        $('#link').show()
        $('#qr').hide()
        $('#download').hide()
        $('#showEmbed').removeClass('active')
        $('#showPopUp').removeClass('active')
        $('#showLink').addClass('active')
        $('#showQrCode').removeClass('active')
        $('#showHtml').removeClass('active')
    })
    $('#showQrCode').on('click', function(e) {
        e.preventDefault()
        history.pushState({}, '', this.href)
        location.hash = 'qr'
        $('#embed').hide()
        $('#popUp').hide()
        $('#link').hide()
        $('#qr').show()
        $('#download').hide()
        $('#showEmbed').removeClass('active')
        $('#showPopUp').removeClass('active')
        $('#showLink').removeClass('active')
        $('#showQrCode').addClass('active')
        $('#showHtml').removeClass('active')
    })
    $('#showHtml').on('click', function(e) {
        e.preventDefault()
        history.pushState({}, '', this.href)
        location.hash = 'download'
        $('#embed').hide()
        $('#popUp').hide()
        $('#link').hide()
        $('#qr').hide()
        $('#download').show()
        $('#showEmbed').removeClass('active')
        $('#showPopUp').removeClass('active')
        $('#showLink').removeClass('active')
        $('#showQrCode').removeClass('active')
        $('#showHtml').addClass('active')
    })
    $('#generateCode').on('click', function(e) {
        e.preventDefault()
        fetch('/form/popup-code?id=8', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: $('form#popup-form').serialize(),
        })
            .then(response => response.text())
            .then(function(data) {
                $('#generatedCode').val(data)
            })
            .catch(function(error) {
                console.error('Error:', error)
            })
    })
    $('#withoutDesign').change(function() {
        if ($(this).is(':checked')) {
            $('#formUrl').val($('#formUrl').val() + '&t=0')
        } else {
            $('#formUrl').val($('#formUrl').val().replace('&t=0', ''))
        }
    })
    $('#withoutBox').change(function() {
        if ($(this).is(':checked')) {
            $('#formUrl').val($('#formUrl').val() + '&b=0')
        } else {
            $('#formUrl').val($('#formUrl').val().replace('&b=0', ''))
        }
    })
    $('#withoutCustomJS').change(function() {
        if ($(this).is(':checked')) {
            $('#formUrl').val($('#formUrl').val() + '&js=0')
        } else {
            $('#formUrl').val($('#formUrl').val().replace('&js=0', ''))
        }
    })
    $('#showForm').on('submit', function(e) {
        e.preventDefault()
        window.open($('#formUrl').val())
    })

    $('#withoutBoxAlt').change(function() {
        if ($(this).is(':checked')) {
            $('#formUrlAlt').val($('#formUrlAlt').val() + '/0')
        } else {
            $('#formUrlAlt').val($('#formUrlAlt').val().slice(0, -2))
        }
    })
    $('#showFormAlt').on('submit', function(e) {
        e.preventDefault()
        window.open($('#formUrlAlt').val())
    })
    $('#downloadWithoutJS').change(function() {
        var link = $('#downloadHtmlCode')
        if ($(this).is(':checked')) {
            link.attr('href', link.attr('href') + '&js=0')
        } else {
            link.attr('href', link.attr('href').replace('&js=0', ''))
        }
    })
    $('#formEndpoint').on('submit', function(e) {
        e.preventDefault()
        /* Get the text field */
        var copyText = document.getElementById('formEndpointUrl')
        copyText.select()
        document.execCommand('copy')
        alert('Copied')
    })
    // Show panel by url hash embed, popUp, link
    var hash = window.location.hash
    if (hash === '#popUp') {
        $('#showPopUp').trigger('click')
    } else if (hash === '#link') {
        $('#showLink').trigger('click')
    } else if (hash === '#embed') {
        $('#showEmbed').trigger('click')
    } else if (hash === '#qr') {
        $('#showQrCode').trigger('click')
    } else if (hash === '#download') {
        $('#showHtml').trigger('click')
    }
})



jQuery(function($) {
    jQuery && jQuery.pjax && (jQuery.pjax.defaults.maxCacheLength = 0)
    kvInitHtml5('#popupform-popup_color', '#popupform-popup_color-source')
    if (jQuery('#popupform-popup_color').data('spectrum')) {
        jQuery('#popupform-popup_color').spectrum('destroy')
    }
    jQuery.when(jQuery('#popupform-popup_color-source').spectrum(spectrum_08419e68)).done(function() {
        jQuery('#popupform-popup_color-source').spectrum('set', jQuery('#popupform-popup_color').val())
        jQuery('#popupform-popup_color-cont').removeClass('kv-center-loading')
    })

    kvInitHtml5('#popupform-overlay_color', '#popupform-overlay_color-source')
    if (jQuery('#popupform-overlay_color').data('spectrum')) {
        jQuery('#popupform-overlay_color').spectrum('destroy')
    }
    jQuery.when(jQuery('#popupform-overlay_color-source').spectrum(spectrum_08419e68)).done(function() {
        jQuery('#popupform-overlay_color-source').spectrum('set', jQuery('#popupform-overlay_color').val())
        jQuery('#popupform-overlay_color-cont').removeClass('kv-center-loading')
    })

    jQuery('#popup-form').yiiActiveForm([{
        'id': 'popupform-button_text',
        'name': 'button_text',
        'container': '.field-popupform-button_text',
        'input': '#popupform-button_text',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '按钮文字不能为空。' })
        },
    }, {
        'id': 'popupform-button_placement',
        'name': 'button_placement',
        'container': '.field-popupform-button_placement',
        'input': '#popupform-button_placement',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '按钮放置不能为空。' })
        },
    }, {
        'id': 'popupform-button_color',
        'name': 'button_color',
        'container': '.field-popupform-button_color',
        'input': '#popupform-button_color',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '按钮颜色不能为空。' })
        },
    }, {
        'id': 'popupform-popup_margin',
        'name': 'popup_margin',
        'container': '.field-popupform-popup_margin',
        'input': '#popupform-popup_margin',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '弹出式保证金不能为空。' })
        },
    }, {
        'id': 'popupform-popup_padding',
        'name': 'popup_padding',
        'container': '.field-popupform-popup_padding',
        'input': '#popupform-popup_padding',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '弹出式填充不能为空。' })
        },
    }, {
        'id': 'popupform-popup_width',
        'name': 'popup_width',
        'container': '.field-popupform-popup_width',
        'input': '#popupform-popup_width',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '弹出宽度不能为空。' })
        },
    }, {
        'id': 'popupform-popup_radius',
        'name': 'popup_radius',
        'container': '.field-popupform-popup_radius',
        'input': '#popupform-popup_radius',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '弹出半径不能为空。' })
        },
    }, {
        'id': 'popupform-animation_effect',
        'name': 'animation_effect',
        'container': '.field-popupform-animation_effect',
        'input': '#popupform-animation_effect',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '动画效果不能为空。' })
        },
    }, {
        'id': 'popupform-animation_duration',
        'name': 'animation_duration',
        'container': '.field-popupform-animation_duration',
        'input': '#popupform-animation_duration',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '动画时长不能为空。' })
        },
    }, {
        'id': 'popupform-popup_color',
        'name': 'popup_color',
        'container': '.field-popupform-popup_color',
        'input': '#popupform-popup_color',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '弹出式颜色不能为空。' })
        },
    }, {
        'id': 'popupform-overlay_color',
        'name': 'overlay_color',
        'container': '.field-popupform-overlay_color',
        'input': '#popupform-overlay_color',
        'validate': function(attribute, value, messages, deferred, $form) {
            yii.validation.required(value, messages, { 'message': '覆盖颜色不能为空。' })
        },
    }], [])
})



