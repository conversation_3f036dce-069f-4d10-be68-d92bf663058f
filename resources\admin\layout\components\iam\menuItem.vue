<template>
    <el-menu-item :index="item.id.toString()" @click="goPage(item)">
      <el-icon><component :is="item.icon" /></el-icon>
      <span>{{ item.name }}</span>
    </el-menu-item>
  </template>
  
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAppStore } from '/admin/stores/modules/app'

interface NavItem {
  children?: NavItem[]
  icon: string
  id: number
  name: string
  path: string
}

const router = useRouter()
const appStore = useAppStore()
const { item } = defineProps<{ item: NavItem }>()

function goPage(item: NavItem) {
  appStore.setPageName(item.name)
  router.push(item.path)
}
</script>
  
<style lang="scss" scoped>
:deep(.el-menu-item) {
  border-radius: 50px;
  padding: 0 12px;
  line-height: 42px;
  height: 42px;
  font-size: 16px;
  font-weight: 500;

  span {
    white-space: normal;
    word-break: break-word;
    display: inline-block;
    max-width: 130px;
  }

  .el-icon {
    text-align: left;
    justify-content: flex-start;
    font-size: 16px;
    width: 16px;
    color: #232323;
  }

  // 激活状态样式
  &.is-active {
    background-color: #409eff !important;
    color: white !important;
    
    .el-icon {
      color: white !important;
    }
  }

  // 悬停状态
  &:hover:not(.is-active) {
    background-color: rgba(230,242,252,0.467);
  }
}

/* 菜单图标图片样式 */
.menu-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
  vertical-align: middle;
}
</style>
