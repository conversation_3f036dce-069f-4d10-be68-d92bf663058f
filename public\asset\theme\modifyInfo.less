@import "./variable.less";

.bwms-page {
  background-color: #F7F7F7;
  height: 100vh;

  .df(stretch, flex-start, column);

  .container {
    flex-grow: 1;
    margin-top: 20px;
    .df(flex-start);

    .left-side {
      background-color: #fff;
      width: 200px;
      flex-shrink: 0;
      min-height: 650px;

      .ul {
        padding: 15px 48px;

        .li {
          padding: 8px 0;

          &.th {
            font-size: 16px;
            color: #222;
          }

          a {
            font-size: 14px;
            color: #666;
            transition: color .35s ease-in-out;
          }

          &.active,
          &:hover {
            a {
              color: #ff9600;
            }
          }
        }
      }
    }

    .right-content {
      padding-left: 20px;
      flex-grow: 1;

      .userinfo {
        background-color: #fff;
        padding: 25px;
        min-height: 650px;

        .df(flex-start, flex-start, column);

        .tit {
          margin-bottom: 14px;
          font-size: 18px;
          color: #757575;
        }

        form {
          .df(center, flex-start, column);

          .input-box {
            padding:8px 0;
            width: 100%;
            .df(center);

            label {
              margin-right: 10px;
              min-width: 114px;
              color: #666;
              font-size: 15px;
              text-align: right;
              line-height: 2.66;
            }
            
            .text {
              font-size: 15px;
              color: #333;
            }

            input {
              border-radius: 4px;
              border: 1px solid #DCDFE6;
              padding: 0 15px;
              background-color: #fff;
              width: 100%;
              min-width: 430px;
              font-size: 15px;
              line-height: 2.66;
              color: #606266;
            }

            .pic {
              border-radius: 50%;
              width: 90px;
              height: 90px;
              overflow: hidden;
              position: relative;

              &.upload {
                cursor: pointer;
              }

              .mask {
                padding: 4px 0;
                background-color: rgba(0, 0, 0, .4);
                text-align: center;

                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1;

                i {
                  color: #fff;
                }
              }
            }
          }

          button {
            margin-top: 8px;
            .btn-radius(0, 16px, 68px, #fff, #ff6700, #ff9600);
          }
        }
      }
    }
  }
}