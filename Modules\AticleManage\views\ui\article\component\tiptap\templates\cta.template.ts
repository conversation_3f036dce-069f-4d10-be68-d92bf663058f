export const ctaTemplate = `
<div data-bs-component="cta" class="py-5 text-white bootstrap-cta responsive-block bg-primary">
  <div class="container">
    <div class="row justify-content-center">
      <div class="text-center col-12 col-md-10 col-lg-8">
        <h2 class="mb-4 display-6 fw-bold" data-bs-component="rich-text">立即行动，抓住机会！</h2>
        <button data-bs-component="button" class="px-4 py-2 bootstrap-button btn btn-light rounded-pill">立即联系</button>
      </div>
    </div>
  </div>

  <style>
    .bootstrap-cta {
      position: relative;
      overflow: hidden;
    }

    .bootstrap-cta h2 {
      font-size: 2.5rem;
      line-height: 1.2;
      margin-bottom: 1.5rem;
    }

    .bootstrap-cta .bootstrap-button {
      font-size: 1.1rem;
      padding: 0.75rem 2rem;
      transition: all 0.3s ease;
    }

    .bootstrap-cta .bootstrap-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* 移动端样式 - 响应容器宽度 */
    .mobile-preview .bootstrap-cta h2,
    .tablet-preview .bootstrap-cta h2 {
      font-size: 2rem;
      padding: 0 15px;
    }

    .mobile-preview .bootstrap-cta .bootstrap-button,
    .tablet-preview .bootstrap-cta .bootstrap-button {
      font-size: 1rem;
      padding: 0.5rem 1.5rem;
      width: calc(100% - 30px);
      max-width: 300px;
      margin: 0 auto;
    }

    .mobile-preview .bootstrap-cta,
    .tablet-preview .bootstrap-cta {
      padding: 3rem 0 !important;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .bootstrap-cta {
        padding: 3rem 0;
      }

      .bootstrap-cta h2 {
        font-size: 2rem;
        padding: 0 15px;
      }

      .bootstrap-cta .bootstrap-button {
        font-size: 1rem;
        padding: 0.5rem 1.5rem;
        width: calc(100% - 30px);
        max-width: 300px;
        margin: 0 auto;
      }
    }

    /* 平板样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .bootstrap-cta {
        padding: 4rem 0;
      }

      .bootstrap-cta h2 {
        font-size: 2.2rem;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .bootstrap-cta {
        padding: 5rem 0;
      }

      .bootstrap-cta h2 {
        font-size: 2.5rem;
      }

      .bootstrap-cta .bootstrap-button {
        min-width: 200px;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .bootstrap-cta {
      padding: 5rem 0;
    }

    .desktop-preview .bootstrap-cta h2 {
      font-size: 2.5rem;
    }

    .desktop-preview .bootstrap-cta .bootstrap-button {
      min-width: 200px;
      font-size: 1.1rem;
      padding: 0.75rem 2rem;
    }
  </style>
</div>
`

// 导出默认模板
export default ctaTemplate; 