export const statsCardTemplate = `<div data-bs-component="stats-card" class="stats-card-block responsive-block">
  <div class="container py-5">
    <div class="row g-4 align-items-center">
      <!-- 左侧图片 -->
      <div class="col-12 col-md-6 image-column">
        <div class="feature-image-wrapper">
          <img src="https://via.placeholder.com/600x500" alt="产品特性展示" class="rounded-lg shadow feature-image img-fluid">
          
          <!-- 浮动徽章 - 可以在编辑器中定制或移除 -->
          <div class="floating-badge customer-badge">
            <div class="badge-icon">
              <i class="fas fa-heart text-danger"></i>
            </div>
            <div class="badge-content">
              <div class="badge-value">25,317</div>
              <div class="badge-label">满意客户</div>
            </div>
          </div>
          
          <div class="floating-badge stats-badge">
            <div class="badge-content">
              <i class="fas fa-chart-line text-primary me-2"></i>
              <span>增长 10倍</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div class="col-12 col-md-6 content-column">
        <div class="text-center content-wrapper text-md-start">
          <h2 class="feature-title">增加触达和互动效果</h2>
          <p class="feature-description">
            通过数据驱动的方法，我们帮助您的品牌覆盖更广泛的受众，并提高用户参与度。我们的解决方案能够实现精准定位、个性化内容推送和智能分析，为您的业务带来持续增长。
          </p>
          
          <div class="mt-4 feature-cta">
            <a href="#" class="btn btn-primary btn-lg rounded-pill">立即探索</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <style>
  .stats-card-block {
    padding: 40px 0;
    background-color: #f8f9fa;
    overflow: hidden;
    width: 100%;
  }
  
  .feature-image-wrapper {
    position: relative;
    padding: 20px;
    transition: all 0.3s ease;
  }
  
  .feature-image {
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    transition: transform 0.3s ease;
  }
  
  .floating-badge {
    position: absolute;
    background: white;
    padding: 10px 15px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    z-index: 2;
    transition: all 0.3s ease;
  }
  
  .customer-badge {
    top: 30px;
    left: 20px;
    padding: 12px 18px;
  }
  
  .stats-badge {
    bottom: 30px;
    right: 20px;
  }
  
  .badge-icon {
    margin-right: 10px;
    font-size: 20px;
  }
  
  .badge-content {
    display: flex;
    align-items: center;
  }
  
  .badge-value {
    font-weight: 700;
    font-size: 18px;
    color: #333;
    line-height: 1.2;
  }
  
  .badge-label {
    font-size: 14px;
    color: #666;
  }
  
  .content-wrapper {
    padding: 20px;
  }
  
  .feature-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }
  
  .feature-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
  }
  
  .feature-cta .btn {
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .feature-cta .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
  }

  /* 移动端样式 */
  @media (max-width: 767.98px) {
    .stats-card-block {
      padding: 20px 0;
    }

    .feature-image-wrapper {
      margin-bottom: 1rem;
      padding: 10px;
    }
    
    .floating-badge {
      transform: scale(0.85);
    }
    
    .feature-title {
      font-size: 1.75rem;
      margin-bottom: 1rem;
    }

    .feature-description {
      font-size: 1rem;
      margin-bottom: 1.5rem;
    }

    .content-wrapper {
      padding: 15px;
    }

    .customer-badge {
      top: 15px;
      left: 10px;
      padding: 8px 12px;
    }

    .stats-badge {
      bottom: 15px;
      right: 10px;
      padding: 8px 12px;
    }

    .badge-value {
      font-size: 16px;
    }

    .badge-label {
      font-size: 12px;
    }

    .feature-cta .btn {
      padding: 10px 25px;
      font-size: 1rem;
    }
  }

  /* 平板端样式 */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .stats-card-block {
      padding: 30px 0;
    }

    .feature-image-wrapper {
      padding: 15px;
    }

    .feature-title {
      font-size: 2rem;
    }

    .feature-description {
      font-size: 1.05rem;
    }

    .floating-badge {
      transform: scale(0.9);
    }

    .content-wrapper {
      padding: 15px;
    }
  }

  /* 桌面端样式 */
  @media (min-width: 992px) {
    .stats-card-block {
      padding: 40px 0;
    }

    .feature-image-wrapper {
      padding: 20px;
    }

    .feature-image:hover {
      transform: translateY(-5px);
    }

    .floating-badge:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }
  }

  /* 移动端预览模式样式 */
  .mobile-preview .stats-card-block {
    padding: 1rem;
  }

  .mobile-preview [class*="col-"] {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }

  .mobile-preview .feature-image-wrapper {
    margin-bottom: 1rem;
    padding: 10px;
  }

  .mobile-preview .floating-badge {
    transform: scale(0.85);
  }

  .mobile-preview .feature-title {
    font-size: 1.75rem;
  }

  .mobile-preview .feature-description {
    font-size: 1rem;
  }

  .mobile-preview .content-wrapper {
    padding: 15px;
    text-align: center;
  }

  .mobile-preview .feature-cta {
    text-align: center;
  }

  /* 桌面预览模式样式 */
  .desktop-preview .stats-card-block {
    padding: 2rem;
  }

  .desktop-preview .feature-image-wrapper {
    padding: 20px;
    margin: 0;
  }

  .desktop-preview .content-wrapper {
    padding: 20px;
    text-align: left;
  }

  /* 容器大小响应式 */
  .container-sm .container {
    max-width: 540px;
  }

  .container-md .container {
    max-width: 720px;
  }

  .container-lg .container {
    max-width: 960px;
  }

  .container-xl .container {
    max-width: 1140px;
  }

  .container-xxl .container {
    max-width: 1320px;
  }
  </style>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</div>` 