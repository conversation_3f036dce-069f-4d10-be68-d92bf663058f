<!DOCTYPE html>
<html>
<head>
    <title>Pusher Demo</title>
    <meta charset="UTF-8" />
    <!-- 引入依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://js.pusher.com/7.0/pusher.min.js"></script>
</head>
<body>
<h1>Pusher Channel Demo</h1>

<div class="container">
    <div class="section">
        <h2>Presence Channel</h2>
        <div id="online-users"></div>
        <div id="presence-messages"></div>
    </div>

    <div class="section">
        <h2>Private Channel</h2>
        <div id="private-messages"></div>
        <button id="send-private">发送私有消息</button>
    </div>
</div>

<script>
    // 初始化 Pusher
    const pusher = new Pusher('e057d7a91a99c62ed780', {
        cluster: 'ap3',
        authEndpoint: '/api/chat/pusher/auth', // 后端鉴权端点
        auth: {
            headers: {
                'Authorization': 'Bearer H4BfmaHEZ3xJUfzQypsdr7KS0GiLiVrPQgzMOb5cwvp2reYLyBD25uymKLbh',
            },
        }
    });

    // 连接状态监听
    pusher.connection.bind('connected', () => {
        console.log('Connected to Pusher!');
        $('#status').text('已连接').css('color', 'green');
    });

    pusher.connection.bind('error', (err) => {
        console.error('Connection error:', err);
        $('#status').text('连接错误').css('color', 'red');
    });

    // ===== PRESENCE CHANNEL =====
    // 订阅存在频道
    const presenceChannel = pusher.subscribe('presence-my-channel');

    // 监听成员加入事件
    presenceChannel.bind('pusher:subscription_succeeded', (members) => {
        console.log('Presence channel subscription succeeded');
        updateMemberList(members.members);
    });

    presenceChannel.bind('pusher:subscription_error', (error) => {
        console.error('Presence channel subscription error:', error);
        // alert('Presence 通道订阅失败: ' + JSON.stringify(error));
    });

    presenceChannel.bind('pusher:member_added', (member) => {
        updateMemberList(presenceChannel.members.members);
    });

    presenceChannel.bind('pusher:member_removed', (member) => {
        updateMemberList(presenceChannel.members.members);
    });

    // 监听新消息事件
    presenceChannel.bind('new-message', (data) => {
        alert("2222")
        $('#presence-messages').append(`
            <div class="message">
                [${new Date().toLocaleTimeString()}] ${data.message}
            </div>
        `);
    });

    // ===== PRIVATE CHANNEL =====
    // 订阅私有频道
    const privateChannel = pusher.subscribe('private-user-86');

    // 监听私有通道订阅成功
    privateChannel.bind('pusher:subscription_succeeded', () => {
        console.log('Private channel subscription succeeded');
        // alert('Private 通道订阅成功');
    });

    // 监听私有通道订阅错误
    privateChannel.bind('pusher:subscription_error', (error) => {
        console.error('Private channel subscription error:', error);
        // alert('Private 通道订阅失败: ' + JSON.stringify(error));
    });

    // 监听私有消息
    privateChannel.bind('new-message', (data) => {
        $('#private-messages').append(`
            <div class="message private">
                [${new Date().toLocaleTimeString()}] 私有消息: ${data.message}
            </div>
        `);
    });

    // 发送私有消息按钮
    $('#send-private').click(() => {
        // 通常这个请求会发送到你的后端，然后由后端触发Pusher事件
        $.ajax({
            url: '/api/chat/pusher/newMsgTrigger',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer H4BfmaHEZ3xJUfzQypsdr7KS0GiLiVrPQgzMOb5cwvp2reYLyBD25uymKLbh',
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                channel: 'private-user-86',
                event: 'new-message',
                message: '这是一条私有消息 - ' + new Date().toLocaleTimeString()
            }),
            success: function(response) {
                console.log('Message sent successfully', response);
            },
            error: function(error) {
                console.error('Error sending message', error);
                alert('发送消息失败，请查看控制台');
            }
        });
    });

    // 更新在线用户列表
    function updateMemberList(members) {
        console.log("成员数据:", members);  // 调试
        let html = '<h3>在线用户:</h3>';
        Object.values(members).forEach(user => {
            console.log("当前用户:", user); // 调试
            html += `
                <div class="user">
                    <img src="${user.avatar || 'https://via.placeholder.com/30'}" width="30" height="30">
                    ${user.name || '匿名用户'}
                </div>
            `;
        });
        $('#online-users').html(html);
    }
</script>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    .container {
        display: flex;
        gap: 20px;
    }

    .section {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
    }

    .message {
        margin: 5px 0;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
    }

    .message.private {
        background-color: #e6f7ff;
    }

    .user {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 5px 0;
    }

    button {
        padding: 8px 16px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
    }

    button:hover {
        background-color: #45a049;
    }
</style>
</body>
</html>
