export const marketingLandingPageTemplate = `
<div class="marketing-landing-page responsive-block">
  <!-- 标题区 -->
  <div data-bs-component="bootstrap-heading" class="bootstrap-heading">
    <div class="py-5 text-center">
      <div data-bs-component="richTextBlock">
        <h1 class="mb-3 display-4 fw-bold">Pricing plans that elev<br>ate your business</h1>
        <p class="text-muted">Try Elevate free for 14 days, no credit card required.</p>
      </div>
      <div data-bs-component="bootstrap-button">
        <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Try for free</button>
      </div>
    </div>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 定价卡片 -->
  <div data-bs-component="pricing" class="bootstrap-pricing">
    <div class="container pb-5">
      <div class="row">
        <div class="col-md-4">
          <div class="p-4">
            <div data-bs-component="richTextBlock">
              <h2 class="mb-0 fw-bold">Starter</h2>
              <p class="text-muted small">Perfect for small businesses or startups looking to kickstart their digital marketing efforts</p>
              <h2 class="mb-0 fw-bold">$79<span class="text-muted fw-normal">/month</span></h2>
            </div>
          </div>
          <div class="p-4 border-top">
            <div data-bs-component="richTextBlock">
              <h5 class="mb-3 fw-bold">Starter plan includes</h5>
              <ul class="mb-4 list-unstyled">
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Social media management</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Email marketing campaigns</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Basic SEO optimization</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Monthly performance reports</span>
                  </div>
                </li>
              </ul>
            </div>
            <div data-bs-component="bootstrap-button">
              <button class="py-2 bootstrap-button btn btn-outline-primary w-100 rounded-2">Get started</button>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4">
            <div data-bs-component="richTextBlock">
              <h2 class="mb-0 fw-bold">Pro</h2>
              <p class="text-muted small">Ideal for growing businesses that want to expand their online presence and reach a larger audience</p>
              <h2 class="mb-0 fw-bold">$129<span class="text-muted fw-normal">/month</span></h2>
            </div>
          </div>
          <div class="p-4 border-top">
            <div data-bs-component="richTextBlock">
              <h5 class="mb-3 fw-bold">Pro plan includes</h5>
              <ul class="mb-4 list-unstyled">
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Everything in Starter plan</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Advanced social media management</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Email marketing automation</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Basic SEO optimization</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Comprehensive SEO analysis and recommendations</span>
                  </div>
                </li>
              </ul>
            </div>
            <div data-bs-component="bootstrap-button">
              <button class="py-2 bootstrap-button btn btn-primary w-100 rounded-2">Get started</button>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4">
            <div data-bs-component="richTextBlock">
              <h2 class="mb-0 fw-bold">Enterprise</h2>
              <p class="text-muted small">Tailored for established businesses seeking a holistic digital marketing solution to boost brand visibility and engagement</p>
              <h2 class="mb-0 fw-bold">$599<span class="text-muted fw-normal">/month</span></h2>
            </div>
          </div>
          <div class="p-4 border-top">
            <div data-bs-component="richTextBlock">
              <h5 class="mb-3 fw-bold">Enterprise plan includes</h5>
              <ul class="mb-4 list-unstyled">
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Everything in Pro plan</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Customized social media strategies</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Personalized email marketing campaigns</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Advanced SEO implementation and monitoring</span>
                  </div>
                </li>
                <li class="mb-2">
                  <div class="d-flex align-items-center">
                    <i class="text-primary me-2">✓</i>
                    <span>Bi-weekly performance reports and strategy consultations</span>
                  </div>
                </li>
              </ul>
            </div>
            <div data-bs-component="bootstrap-button">
              <button class="py-2 bootstrap-button btn btn-primary w-100 rounded-2">Contact sales</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 营销特性展示 -->
  <div data-bs-component="stats-card" class="statsCardBlock">
    <div class="container py-5">
      <div class="row align-items-center">
        <div class="mb-4 col-md-6 mb-md-0">
          <div class="p-5 bg-dark rounded-4 position-relative" style="background-image: url('https://via.placeholder.com/800x600/0d6efd/ffffff?text=Marketing'); background-size: cover; min-height: 300px;">
            <div class="top-0 p-2 m-4 bg-white shadow-sm position-absolute start-0 rounded-3">
              <div class="d-flex align-items-center">
                <span class="fs-5 text-danger me-2">❤</span>
                <div>
                  <div class="fw-bold">25,317</div>
                  <div class="small text-muted">满意客户</div>
                </div>
              </div>
            </div>
            <div class="bottom-0 p-2 m-4 bg-white shadow-sm position-absolute end-0 rounded-3">
              <div class="d-flex align-items-center">
                <span class="fs-5 text-primary me-2">📈</span>
                <span>增长 10倍</span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div data-bs-component="bootstrap-heading">
            <h2 class="mb-3 display-6 fw-bold">All your marketing needs in one place</h2>
          </div>
          <div data-bs-component="richTextBlock">
            <p class="mb-4 text-muted">
              通过数据驱动的方法，我们帮助您的品牌覆盖更广泛的受众，并提高用户参与度。我们的解决方案能够实现精准定位、个性化内容推送和智能分析，为您的业务带来持续增长。
            </p>
          </div>
          <div data-bs-component="bootstrap-button">
            <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Start free trial</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 客户推荐轮播 -->
  <div data-bs-component="testimonial-slider" class="testimonialSliderBlock">
    <div class="container py-4">
      <div class="mb-4 text-center">
        <div data-bs-component="bootstrap-heading">
          <h2 class="fw-bold">Trusted by leading companies</h2>
        </div>
        <div class="mt-4 d-flex justify-content-center align-items-center">
          <div class="logo-slider">
            <div class="logo-track">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/zyntellix-dark.webp" alt="客户Logo" class="client-logo">
              <img data-bs-component="bootstrap-image" src="https://dev-bwms.bingo-test.com//Vendor/Dashboard/Asset/logos.png" alt="客户Logo" class="client-logo">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/zyntellix-dark.webp" alt="客户Logo" class="client-logo">
              <img data-bs-component="bootstrap-image" src="https://dev-bwms.bingo-test.com//Vendor/Dashboard/Asset/logos.png" alt="客户Logo" class="client-logo">
              <img data-bs-component="bootstrap-image" src="https://new-bwms.bingo-test.com/tiptap/zyntellix-dark.webp" alt="客户Logo" class="client-logo">
              <img data-bs-component="bootstrap-image" src="https://dev-bwms.bingo-test.com//Vendor/Dashboard/Asset/logos.png" alt="客户Logo" class="client-logo">
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      .testimonialSliderBlock .logo-slider {
        width: 100%;
        overflow: hidden;
        position: relative;
        padding: 20px 0;
      }
      
      .testimonialSliderBlock .logo-track {
        display: flex;
        align-items: center;
        gap: 40px;
        animation: testimonialSlide 20s linear infinite;
      }
      
      .testimonialSliderBlock .client-logo {
        height: 40px;
        width: auto;
        opacity: 0.7;
        transition: opacity 0.3s ease;
        flex-shrink: 0;
      }
      
      .testimonialSliderBlock .client-logo:hover {
        opacity: 1;
      }
      
      @keyframes testimonialSlide {
        0% {
          transform: translateX(0);
        }
        100% {
          transform: translateX(-50%);
        }
      }
      
      /* 移动端样式适配 */
      @media (max-width: 768px) {
        .testimonialSliderBlock .logo-track {
          gap: 20px;
        }
        
        .testimonialSliderBlock .client-logo {
          height: 30px;
        }
      }
    </style>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 常见问题 -->
  <div data-bs-component="bootstrap-accordion" class="bootstrap-accordion">
    <div class="container py-4">
      <div data-bs-component="bootstrap-heading">
        <h2 class="mb-4 text-center fw-bold">Frequently asked questions</h2>
      </div>
      <div class="accordion" id="faqAccordion">
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
              What services does your digital marketing agency offer?
            </button>
          </h2>
          <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              We offer a comprehensive suite of digital marketing services including social media management, email marketing, SEO optimization, content creation, and performance analytics.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
              How can your digital marketing agency help my business grow?
            </button>
          </h2>
          <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency helps businesses increase their online visibility, attract more qualified leads, engage with their target audience effectively, and convert prospects into loyal customers.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
              Do you provide customized solutions for different businesses?
            </button>
          </h2>
          <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Yes, we tailor our digital marketing strategies to match your specific business goals, target audience, and industry requirements, ensuring you receive personalized solutions.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
              Can you track the performance of digital marketing campaigns?
            </button>
          </h2>
          <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Absolutely! We provide comprehensive analytics and reporting that track key performance indicators, conversion rates, engagement metrics, and ROI for all your marketing campaigns.
            </div>
          </div>
        </div>
        <div class="mb-3 rounded border accordion-item">
          <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
              What sets your digital marketing agency apart from others in the industry?
            </button>
          </h2>
          <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
            <div class="accordion-body">
              Our agency stands out through our data-driven approach, transparent communication, industry expertise, customized strategies, and measurable results that help our clients achieve sustainable growth.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分隔线 -->
  <div class="py-3">
    <div class="container">
      <hr class="border-1 border-secondary-subtle">
    </div>
  </div>

  <!-- 行动号召 -->
  <div data-bs-component="bootstrap-cta" class="py-5 text-white bg-primary">
    <div class="container text-center">
      <div data-bs-component="richTextBlock">
        <h2 class="mb-4 display-6 fw-bold">Ready to elevate your business?</h2>
      </div>
      <div data-bs-component="bootstrap-button">
        <button class="px-4 py-2 bootstrap-button btn btn-light rounded-pill">Try for free</button>
      </div>
    </div>
  </div>
</div>

<style>
/* 基础样式 */
.marketing-landing-page {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 全局动效和过渡 */
.marketing-landing-page .col-md-6,
.marketing-landing-page .col-md-4,
.marketing-landing-page .bootstrap-pricing,
.marketing-landing-page .statsCardBlock,
.marketing-landing-page .testimonialSliderBlock,
.marketing-landing-page .bootstrap-accordion {
  transition: transform 0.5s ease, opacity 0.5s ease, box-shadow 0.3s ease;
}

/* 按钮样式增强 */
.marketing-landing-page .btn {
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
}

.marketing-landing-page .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 客户Logo样式 */
.logo-slider {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 20px 0;
}

.logo-track {
  display: flex;
  align-items: center;
  gap: 40px;
  animation: slide 20s linear infinite;
}

.client-logo {
  height: 40px;
  width: auto;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  flex-shrink: 0;
}

.client-logo:hover {
  opacity: 1;
}

@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 移动端预览模式样式 */
.mobile-preview .marketing-landing-page {
  padding: 20px 0;
}

.mobile-preview .marketing-landing-page .display-4 {
  font-size: 2rem;
}

.mobile-preview .marketing-landing-page .display-6 {
  font-size: 1.5rem;
}

.mobile-preview .marketing-landing-page .py-5 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

.mobile-preview .marketing-landing-page .row {
  flex-direction: column;
}

.mobile-preview .marketing-landing-page .col-md-3,
.mobile-preview .marketing-landing-page .col-md-4,
.mobile-preview .marketing-landing-page .col-md-6 {
  width: 100% !important;
  max-width: 100% !important;
  flex: 0 0 100% !important;
  margin-bottom: 20px;
}

/* 桌面预览模式样式 */
.desktop-preview .marketing-landing-page {
  padding: 40px 0;
}

.desktop-preview .marketing-landing-page .py-5 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}

/* 响应式媒体查询 */
@media screen and (max-width: 767.98px) {
  .marketing-landing-page {
    padding: 20px 0;
  }
  
  .marketing-landing-page .display-4 {
    font-size: 2rem;
  }
  
  .marketing-landing-page .display-6 {
    font-size: 1.5rem;
  }
  
  .marketing-landing-page .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  
  .marketing-landing-page .row {
    flex-direction: column;
  }
  
  .marketing-landing-page .col-md-3,
  .marketing-landing-page .col-md-4,
  .marketing-landing-page .col-md-6 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    margin-bottom: 20px;
  }
  
  .marketing-landing-page .client-logo {
    max-width: 100px;
    margin: 10px;
  }
  
  .marketing-landing-page .bootstrap-pricing .p-4 {
    padding: 1.5rem !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 991.98px) {
  .marketing-landing-page .display-4 {
    font-size: 2.5rem;
  }
  
  .marketing-landing-page .display-6 {
    font-size: 2rem;
  }
  
  .marketing-landing-page .py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  
  .marketing-landing-page .client-logo {
    max-width: 120px;
    margin: 15px;
  }
}

@media screen and (min-width: 992px) {
  .marketing-landing-page .bootstrap-pricing,
  .marketing-landing-page .statsCardBlock {
    height: 100%;
  }
  
  .marketing-landing-page .client-logo {
    max-width: 150px;
    margin: 20px;
  }
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // 获取页面元素
  const landingPage = document.querySelector('.marketing-landing-page');
  const pricingCards = document.querySelectorAll('.bootstrap-pricing .col-md-4');
  const statsCard = document.querySelector('.statsCardBlock');
  const testimonials = document.querySelector('.testimonialSliderBlock');
  
  // 初始化淡入效果的元素
  const fadeElements = [
    ...pricingCards,
    statsCard,
    testimonials,
    ...document.querySelectorAll('.col-md-6')
  ];
  
  // 设置初始状态
  fadeElements.forEach(element => {
    if (element) {
      element.style.opacity = '0';
      element.style.transform = 'translateY(30px)';
      element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    }
  });
  
  // 检查元素是否在视口中并应用动画
  function checkScroll() {
    fadeElements.forEach((element, index) => {
      if (!element) return;
      
      const elementTop = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if (elementTop < windowHeight * 0.85) {
        setTimeout(() => {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0)';
        }, index * 100);
      }
    });
  }
  
  // 响应式调整
  function handleResize() {
    const windowWidth = window.innerWidth;
    const isMobile = windowWidth < 768;
    const isTablet = windowWidth >= 768 && windowWidth < 992;
    
    landingPage.classList.remove('mobile-view', 'tablet-view', 'desktop-view');
    
    if (isMobile) {
      landingPage.classList.add('mobile-view');
    } else if (isTablet) {
      landingPage.classList.add('tablet-view');
    } else {
      landingPage.classList.add('desktop-view');
    }
  }
  
  // 初始检查
  handleResize();
  checkScroll();
  
  // 监听事件
  window.addEventListener('scroll', checkScroll);
  window.addEventListener('resize', handleResize);
  
  // 为定价卡片添加悬停效果
  pricingCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-10px)';
      this.style.transition = 'transform 0.3s ease';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });
});
</script>
` 