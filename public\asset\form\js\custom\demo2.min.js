$(document).ready(function(){$.when($.getScript("//maps.googleapis.com/maps/api/js?key=AIzaSyArxFB4l9kyT2ZxmYQPLE_6ATsJUecQ5zw&sensor=false&libraries=places"),$("head").append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-star-rating/4.0.1/css/star-rating.min.css" type="text/css" />'),$.getScript("//cdnjs.cloudflare.com/ajax/libs/bootstrap-star-rating/4.0.1/js/star-rating.min.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){var map=new google.maps.Map(document.getElementById("map"),{center:{lat:-33.8688,lng:151.2195},zoom:13,mapTypeId:google.maps.MapTypeId.ROADMAP});var input=document.getElementById("text_1");var searchBox=new google.maps.places.SearchBox(input);map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);map.addListener("bounds_changed",function(){searchBox.setBounds(map.getBounds())});var markers=[];searchBox.addListener("places_changed",function(){var places=searchBox.getPlaces();if(places.length==0){return}markers.forEach(function(marker){marker.setMap(null)});markers=[];var bounds=new google.maps.LatLngBounds;places.forEach(function(place){var icon={url:place.icon,size:new google.maps.Size(71,71),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(17,34),scaledSize:new google.maps.Size(25,25)};markers.push(new google.maps.Marker({map:map,icon:icon,title:place.name,position:place.geometry.location}));if(place.geometry.viewport){bounds.union(place.geometry.viewport)}else{bounds.extend(place.geometry.location)}});map.fitBounds(bounds)});$(".star-rating").rating({min:1,max:10,step:2,size:"sm"})})});