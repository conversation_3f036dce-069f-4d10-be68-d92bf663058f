$(document).ready(function() {
    const { createApp, ref, reactive } = Vue
    createApp({
        setup() {
            const ruleFormRef = ref(null)
            const form = ref({
                donorID: undefined,
                appellation: undefined,
                name: undefined,
                phone: undefined,
                mail: undefined,
                payMethod: 'Visa',
                purpose: '一般捐款',
                amount: undefined,
                isAgree: false,
            })

            const submitHandle = async (formEl) => {
                if (!formEl) return
                formEl.validate(async (valid) => {
                    if (valid) {
                        // 表单验证通过，可以提交表单
                        console.log('表单验证通过')

                        try {
                            // 先获取 activity_id
                            const activityResponse = await fetch('/api/donate/getLatestActivity', {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                            })

                            // 判断是否成功获取 `activity_id`，如果不成功显示错误提示
                            if (!activityResponse.ok) {
                                const errorData = await activityResponse.json() // 如果后端返回错误信息，提取出来
                                ElementPlus.ElMessage({
                                    message: errorData.message || '无法获取 activity_id',
                                    type: 'error',
                                })
                                return // 不继续进行后续的表单提交
                            }

                            const activityData = await activityResponse.json()
                            const activityId = activityData.data.id
                            const donate_type = activityData.data.type_id

                            // 准备提交的数据
                            const data = {
                                donorID: form.value.donorID,
                                appellation: form.value.appellation,
                                name: form.value.name,
                                phone: form.value.phone,
                                mail: form.value.mail,
                                payMethod: form.value.payMethod,
                                purpose: form.value.purpose,
                                amount: form.value.amount,
                                isAgree: form.value.isAgree,
                                activity_id: activityId,
                                donor_type: donate_type
                            }

                            // 使用 fetch 提交数据到 /api/donate
                            const donateResponse = await fetch('/api/donate/donate', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(data),
                            })

                            if (!donateResponse.ok) {
                                const errorData = await donateResponse.json() // 提取错误信息
                                ElementPlus.ElMessage({
                                    message: errorData.message || '提交失败，请检查输入。',
                                    type: 'error',
                                })
                                return // 不继续执行后续逻辑
                            }

                            const donateResult = await donateResponse.json()
                            console.log('提交成功:', donateResult)
                            ElementPlus.ElMessage({
                                message: '提交成功！',
                                type: 'success',
                            })
                            // 在此处处理服务器返回的数据

                        } catch (error) {
                            console.error('提交错误:', error)
                            ElementPlus.ElMessage({
                                message: error.message || '提交失败，请检查输入。',
                                type: 'error',
                            })
                        }

                    } else {
                        // 表单验证失败
                        console.log('表单验证失败')
                        ElementPlus.ElMessage({
                            message: '表单验证失败，请检查您的输入。',
                            type: 'error',
                        })
                        return false
                    }
                })
            }


            const rules = reactive({
                name: {
                    required: true,
                    message: '请输入您的姓名',
                },
                mail: {
                    required: true,
                    message: '请输入您的邮箱',
                },
                amount: {
                    required: true,
                    message: '请选择其中一项捐款金额',
                },
                isAgree: {
                    required: true,
                    message: '请勾选上述条文',
                },
                payMethod: {
                    required: true,
                    message: '请选择其中一项',
                },
                purpose: {
                    required: true,
                    message: '请选择其中一项',
                },
            })

            return { form, rules, submitHandle, ruleFormRef }
        },
    }).use(ElementPlus).mount('#donation-form')
})
