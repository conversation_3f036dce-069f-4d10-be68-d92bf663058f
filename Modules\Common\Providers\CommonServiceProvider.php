<?php

namespace Modules\Common\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Modules\Common\Command\ScheduleRunAllCommand;
use Modules\Common\Command\ScheduleRunnerCommand;
use Modules\Common\Provider\Schedule\ScheduleBiz;
use Modules\Common\Schedule\DataTempCleanScheduleBiz;
use Modules\Common\Schedule\TempFileCleanScheduleBiz;

class CommonServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        $this->commands([
            ScheduleRunnerCommand::class,
            ScheduleRunAllCommand::class,
        ]);
        if (class_exists(DataTempCleanScheduleBiz::class)) {
            ScheduleBiz::register(DataTempCleanScheduleBiz::class);
            ScheduleBiz::register(TempFileCleanScheduleBiz::class);
        }

        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Common'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Common');
    }
    /**
     * route path
     *
     * @return string|array
     */
    public function moduleName(): string|array
    {
        // TODO: Implement path() method.
        return 'common';
    }

    protected function navigation(): array
    {
        return [];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [];
    }
}
