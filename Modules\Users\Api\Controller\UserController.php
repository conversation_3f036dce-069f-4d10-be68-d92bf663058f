<?php
// ========================================
// 用户管理控制器
// ========================================

namespace Modules\Users\Api\Controller;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Modules\Users\Models\Admin;
use Modules\Users\Models\PenName;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Modules\Users\Services\RBACService;
use Illuminate\Support\Facades\Validator;

/**
 * 用户管理控制器
 */
class UserController extends Controller
{
    protected $rbacService;

    public function __construct(RBACService $rbacService)
    {
        $this->rbacService = $rbacService;
    }

    /**
     * 用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Admin::with(['roles', 'penNames']);

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('real_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // 角色筛选
        if ($request->filled('role_id')) {
            $roleId = $request->input('role_id');
            $query->whereHas('roles', function ($q) use ($roleId) {
                $q->where('roles.role_id', $roleId);
            });
        }

        $users = $query->orderBy('created_at', 'desc')
                      ->paginate($request->input('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * 创建用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:admins',
            'real_name' => 'required|string|max:100',
            'email' => 'required|email|unique:admins',
            'role_ids' => 'required|array',
            'role_ids.*' => 'integer|exists:roles,role_id',
            'pen_names' => 'nullable|array',
            'pen_names.*' => 'string|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // 生成随机密码
            $password = Str::random(12);

            // 创建用户
            $user = Admin::create([
                'username' => $request->input('username'),
                'real_name' => $request->input('real_name'),
                'email' => $request->input('email'),
                'password' => Hash::make($password),
                'status' => 0 // 未激活状态
            ]);

            // 分配角色
            $roleIds = $request->input('role_ids');
            foreach ($roleIds as $index => $roleId) {
                $this->rbacService->assignRole(
                    $user->admin_id,
                    $roleId,
                    auth()->id(),
                    $index === 0 // 第一个角色为主角色
                );
            }

            // 创建笔名
            if ($request->filled('pen_names')) {
                foreach ($request->input('pen_names') as $penName) {
                    $penNameModel = PenName::firstOrCreate(
                        ['pen_name' => $penName],
                        [
                            'pen_name_type' => 1,
                            'created_by' => auth()->id()
                        ]
                    );

                    $user->penNames()->attach($penNameModel->pen_name_id, [
                        'is_default' => $user->penNames()->count() === 0
                    ]);
                }
            }

            // 发送激活邮件
            $this->sendActivationEmail($user, $password);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '用户创建成功，激活邮件已发送',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '用户创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新用户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, int $id)
    {
        $user = Admin::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'real_name' => 'required|string|max:100',
            'email' => 'required|email|unique:admins,email,' . $id,
            'role_ids' => 'required|array',
            'role_ids.*' => 'integer|exists:roles,role_id',
            'pen_names' => 'nullable|array',
            'pen_names.*' => 'string|max:100',
            'status' => 'in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // 更新用户信息
            $user->update([
                'real_name' => $request->input('real_name'),
                'email' => $request->input('email'),
                'status' => $request->input('status', $user->status)
            ]);

            // 更新角色
            $user->roles()->detach();
            $roleIds = $request->input('role_ids');
            foreach ($roleIds as $index => $roleId) {
                $this->rbacService->assignRole(
                    $user->admin_id,
                    $roleId,
                    auth()->id(),
                    $index === 0
                );
            }

            // 更新笔名
            $user->penNames()->detach();
            if ($request->filled('pen_names')) {
                foreach ($request->input('pen_names') as $penName) {
                    $penNameModel = PenName::firstOrCreate(
                        ['pen_name' => $penName],
                        [
                            'pen_name_type' => 1,
                            'created_by' => auth()->id()
                        ]
                    );

                    $user->penNames()->attach($penNameModel->pen_name_id, [
                        'is_default' => $user->penNames()->count() === 0
                    ]);
                }
            }

            // 清除用户缓存
            $this->rbacService->clearUserCache($user->admin_id);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '用户更新成功',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '用户更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重置用户密码
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(int $id)
    {
        $user = Admin::findOrFail($id);

        try {
            $password = Str::random(12);
            $user->update(['password' => Hash::make($password)]);

            // 发送密码重置邮件
            $this->sendPasswordResetEmail($user, $password);

            return response()->json([
                'success' => true,
                'message' => '密码重置成功，新密码已发送到用户邮箱'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '密码重置失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重发激活邮件
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendActivationEmail(int $id)
    {
        $user = Admin::findOrFail($id);

        if ($user->status == 1) {
            return response()->json([
                'success' => false,
                'message' => '用户已激活，无需重发激活邮件'
            ], 400);
        }

        try {
            $password = Str::random(12);
            $user->update(['password' => Hash::make($password)]);

            $this->sendActivationEmail($user, $password);

            return response()->json([
                'success' => true,
                'message' => '激活邮件重发成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '激活邮件重发失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 发送激活邮件
     *
     * @param Admin $user
     * @param string $password
     * @return void
     */
    private function sendActivationEmail(Admin $user, string $password)
    {
        // 这里实现发送激活邮件的逻辑
        // 可以使用Laravel的Mail功能
    }

    /**
     * 发送密码重置邮件
     *
     * @param Admin $user
     * @param string $password
     * @return void
     */
    private function sendPasswordResetEmail(Admin $user, string $password)
    {
        // 这里实现发送密码重置邮件的逻辑
    }
}
