<?php

return [

    /*
    |--------------------------------------------------------------------------
    | 加载指定模块
    |--------------------------------------------------------------------------
    |
    | 指定在使用应用程序时应该注册哪些模块。
    |
    | LOAD_MODULES="System,Backend,Editor,Cms,Media"
    |
    */

    'load_modules' => env('LOAD_MODULES'),

    /*
    |--------------------------------------------------------------------------
    | 链接策略
    |--------------------------------------------------------------------------
    |
    | 控制如何在整个应用程序中生成 URL 链接。
    |
    | detect   - 检测主机名并使用当前协议
    | secure   - 检测主机名并强制使用 HTTPS 协议
    | insecure - 检测主机名并强制使用 HTTP 协议
    | force    - 使用 app.url 配置值强制指定主机名和协议
    |
    | 默认情况下，大多数链接使用其完全限定的 URL 或引用其 CDN 位置。
    | 在某些情况下，您可能更喜欢在可能的情况下使用相对链接，
    | 如果是这样，请将 relative_links 值设置为 true。
    |
    */

    'link_policy' => env('LINK_POLICY', 'detect'),

    'relative_links' => env('RELATIVE_LINKS', false),

    /*
    |--------------------------------------------------------------------------
    | 系统路径
    |--------------------------------------------------------------------------
    |
    | 指定核心系统路径的位置。如果本地路径没有前导斜杠，则为相对路径。
    | URL 可以相对于基本应用程序 URL，也可以指定完整的 URL 路径。
    |
    | PLUGINS_PATH="plugins"
    | PLUGINS_ASSET_URL="/plugins"
    |
    | THEMES_PATH="/absolute/path/to/themes"
    | THEMES_ASSET_URL="http://localhost/themes"
    |
    */

    'plugins_path' => env('PLUGINS_PATH'),

    'plugins_asset_url' => env('PLUGINS_ASSET_URL'),

    'themes_path' => env('THEMES_PATH'),

    'themes_asset_url' => env('THEMES_ASSET_URL'),

    'storage_path' => env('STORAGE_PATH'),

    'cache_path' => env('CACHE_PATH'),

    /*
    |--------------------------------------------------------------------------
    | 默认权限掩码
    |--------------------------------------------------------------------------
    |
    | 为系统路径中创建的文件和目录指定默认的文件和文件夹权限，以字符串形式（例如："755"）。
    | 建议使用文件权限为 "644"，文件夹权限为 "755"。
    |
    */

    'default_mask' => [
        'file' => env('DEFAULT_FILE_MASK'),
        'folder' => env('DEFAULT_FOLDER_MASK'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 跨站请求伪造（CSRF）保护
    |--------------------------------------------------------------------------
    |
    | 如果启用 CSRF 保护，所有 "postback" 和 AJAX 请求都会检查有效的安全令牌。
    |
    */

    'enable_csrf_protection' => env('ENABLE_CSRF', true),

    /*
    |--------------------------------------------------------------------------
    | 转换行尾
    |--------------------------------------------------------------------------
    |
    | 确定 Bwms CMS 是否应该将 Windows 风格的行尾 \r\n 转换为 Unix 风格的 \n。
    |
    */

    'convert_line_endings' => env('CONVERT_LINE_ENDINGS', false),

    /*
    |--------------------------------------------------------------------------
    | Cookie 加密
    |--------------------------------------------------------------------------
    |
    | Bwms CMS 默认会加密/解密 cookies。您可以在这里指定不应该被加密或解密的 cookies。
    | 这在某些情况下很有用，例如，当您想通过 cookies 在前端和服务器端后端之间传递数据时。
    |
    */

    'unencrypt_cookies' => env('UNENCRYPT_COOKIES', [
        // 'my_cookie',
    ]),

    /*
    |--------------------------------------------------------------------------
    | 自动镜像到公共目录
    |--------------------------------------------------------------------------
    |
    | 在 composer 更新后执行。
    |
    | true   - 自动将资源镜像到公共目录
    | false  - 永不将资源镜像到公共目录
    | null   - 仅在调试模式关闭时（在生产环境中）镜像资源
    |
    */

    'auto_mirror_public' => env('AUTO_MIRROR_PUBLIC', false),

    /*
    |--------------------------------------------------------------------------
    | 自动回滚插件
    |--------------------------------------------------------------------------
    |
    | 当使用 composer 卸载插件时，尝试自动反转数据库迁移。
    | 默认情况下，这是禁用的，以防止数据丢失。
    |
    */

    'auto_rollback_plugins' => env('AUTO_ROLLBACK_PLUGINS', false),

    /*
    |--------------------------------------------------------------------------
    | 基本目录限制
    |--------------------------------------------------------------------------
    |
    | 限制加载后端模板和配置文件到应用程序的基本目录内。
    | 例如，在使用 composer 的符号链接选项进行本地包时。
    |
    | 警告：出于安全原因，这在生产环境中不应该被禁用。
    |
    */

    'restrict_base_dir' => env('RESTRICT_BASE_DIR', true),

];
