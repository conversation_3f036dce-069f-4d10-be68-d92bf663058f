<?php

use Illuminate\Support\Facades\Route;
use Modules\Tags\Api\Controllers\TagsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('admin')->group(function () {
    Route::prefix('tag')->group(function () {
        Route::get('/', [TagsController::class, 'index'])->name('tag.index');
        Route::post('/', [TagsController::class, 'store'])->name('tag.store');
        Route::get('/{id}', [TagsController::class, 'show'])->name('tag.show');
        Route::put('/{id}', [TagsController::class, 'update'])->name('tag.update');
        Route::delete('/{id}', [TagsController::class, 'destroy'])->name('tag.destroy');
        Route::post('/switch/{id}', [TagsController::class, 'switch'])->name('tag.switch');
        Route::post('/copy/{id}', [TagsController::class, 'copy'])->name('tag.copy');
    });
});
