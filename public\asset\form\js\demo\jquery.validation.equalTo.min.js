$(document).ready(function(){$.when($.getScript("//cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.1/jquery.validate.min.js"),$.Deferred(function(deferred){$(deferred.resolve)})).done(function(){$.validator.messages.equalTo="Please enter the same value again.";$.validator.addClassRules("second_field",{equalTo:".first_field"});formEl.validate({validClass:"",errorElement:"div",errorClass:"help-block error-block",highlight:function(element,errorClass,validClass){var fieldGroup=$(element).parent(".form-group");fieldGroup.addClass("has-error")},unhighlight:function(element,errorClass,validClass){var fieldGroup=$(element).parent(".form-group");fieldGroup.removeClass("has-error");fieldGroup.find(".error-block").remove()}});$("button[type=submit]").on("click",function(e){e.preventDefault();if(formEl.valid()){formEl.submit()}})})});