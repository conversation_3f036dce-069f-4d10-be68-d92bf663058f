import { defineStore } from '../../index'
import { ref, computed } from 'vue'
import { UserCenterService } from '/admin/layout/components/Menu/application/UserCenterService'
import type { User } from '/admin/layout/components/Menu/domain/UserCenterDomain'

interface UserInfo {
  id?: string
  username?: string
  email?: string
  phone?: string
  name?: string
  title?: string
  address?: string
  gender?: string
  birthday?: string
  company?: string
  registerTime?: string
  idNumber?: string
  avatar?: string
}

export const useUserStore = defineStore('user', () => {
  const userCenterService = new UserCenterService()

  const userInfo = ref<any>({})

  const userPhone = computed(() => userInfo.value.phone)
  const userAvatarUrl = ref('')

  async function fetchUserInfo() {
    try {
      const info = await userCenterService.getUserInfo()
      userInfo.value = info
    } catch (error) {
      console.error('Failed to fetch user info:', error)
    }
  }

  function updateUserInfo(info: Partial<UserInfo>) {
    userInfo.value = { ...userInfo.value, ...info }
  }

  return {
    userInfo,
    userPhone,
    userAvatarUrl,
    fetchUserInfo,
    updateUserInfo,
  }
})
