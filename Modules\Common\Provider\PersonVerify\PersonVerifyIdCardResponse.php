<?php

namespace Modules\Common\Provider\PersonVerify;

class PersonVerifyIdCardResponse
{
    public const STATUS_SUCCESS = 'success';
    public const STATUS_FAIL = 'fail';

    public int $code;
    public string$msg;
    public $status;

    public static function build($code, $msg, $status = null): static
    {
        $instance = new static();
        $instance->code = $code;
        $instance->msg = $msg;
        $instance->status = $status;
        return $instance;
    }
}
