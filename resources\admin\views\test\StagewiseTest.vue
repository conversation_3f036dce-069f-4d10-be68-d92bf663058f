<template>
  <div class="stagewise-test-page">
    <div class="container mx-auto p-6">
      <h1 class="text-3xl font-bold mb-6">Stagewise 开发工具测试页面</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">测试区域 1</h2>
          <p class="text-gray-600 mb-4">
            这是一个测试区域，您可以使用 Stagewise 工具栏来选择和编辑这个元素。
          </p>
          <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            测试按钮
          </button>
        </div>
        
        <div class="bg-gray-50 p-6 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">测试区域 2</h2>
          <ul class="list-disc list-inside space-y-2">
            <li>列表项目 1</li>
            <li>列表项目 2</li>
            <li>列表项目 3</li>
          </ul>
        </div>
      </div>
      
      <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="text-lg font-medium text-yellow-800 mb-2">
          🚀 Stagewise 使用说明
        </h3>
        <p class="text-yellow-700">
          如果您在开发环境中看到了 Stagewise 工具栏，说明集成成功！
          您可以：
        </p>
        <ul class="list-disc list-inside mt-2 text-yellow-700 space-y-1">
          <li>选择页面上的任何元素</li>
          <li>添加评论和反馈</li>
          <li>让 AI 代理根据上下文进行修改</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('🧪 Stagewise 测试页面已加载')
  console.log('🔍 检查开发环境:', import.meta.env.MODE)
  
  // 检查 stagewise 工具栏是否存在
  setTimeout(() => {
    const toolbar = document.getElementById('stagewise-toolbar-container')
    if (toolbar) {
      console.log('✅ Stagewise 工具栏已找到')
    } else {
      console.log('❌ 未找到 Stagewise 工具栏')
    }
  }, 1000)
})
</script>

<style scoped>
.stagewise-test-page {
  min-height: 100vh;
  background-color: #f8fafc;
}
</style> 