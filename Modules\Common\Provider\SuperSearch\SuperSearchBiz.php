<?php

namespace Modules\Common\Provider\SuperSearch;

use Modules\Common\Provider\BizTrait;

class SuperSearchBiz
{
    use BizTrait;

    /**
     * @return AbstractSuperSearchBiz[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractSuperSearchBiz
     */
    public static function get($name): AbstractSuperSearchBiz
    {
        return self::getByName($name);
    }
}
