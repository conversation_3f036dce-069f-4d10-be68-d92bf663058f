.donation-container {
    --el-fill-color-blank: #EDEEF2;
    --el-border-color: transparent;
    --el-input-height: 50px;
    --el-input-focus-border-color: transparent;
    --el-button-text-color: #0072BC;
    --el-button-font-weight: bold;
    --el-input-border-radius: 4px;
    --el-button-size: 50px;
    --el-fill-color-light: #0072BC;
    --el-color-info: #fff;
}

.el-form .el-form-item__label {
    font-size: 16px;
    font-weight: bold;
    color: var(--bs-body-color);
}

.el-input {
    --el-input-height: 50px;
}

.el-radio-group {
    width: 100%;
}

.el-form-item.mx-25 .el-form-item__content .el-radio-group {
    justify-content: space-between;
}

.el-radio-button {
    margin-bottom: 30px;
    width: calc(50% - 25px);
}

.el-radio-button--large .el-radio-button__inner,
.el-radio-button:first-child .el-radio-button__inner {
    border-radius: var(--el-input-border-radius);
    border: none;
    font-size: 26px;
    width: 100%;
}
