# 关联关系模板

## 概述

关联关系定义了模型之间的关系，是 Laravel Eloquent ORM 的核心功能之一。本文档提供了关联关系的标准模板和最佳实践。

## 基本关联类型

### 一对一关联

```php
/**
 * 用户详情关联
 *
 * @return HasOne
 */
public function profile(): HasOne
{
    return $this->hasOne(Profile::class);
}

/**
 * 反向一对一关联
 *
 * @return BelongsTo
 */
public function user(): BelongsTo
{
    return $this->belongsTo(User::class);
}
```

### 一对多关联

```php
/**
 * 文章关联
 *
 * @return HasMany
 */
public function posts(): HasMany
{
    return $this->hasMany(Post::class);
}

/**
 * 反向一对多关联
 *
 * @return BelongsTo
 */
public function author(): BelongsTo
{
    return $this->belongsTo(User::class, 'user_id');
}
```

### 多对多关联

```php
/**
 * 角色关联
 *
 * @return BelongsToMany
 */
public function roles(): BelongsToMany
{
    return $this->belongsToMany(Role::class)
        ->withTimestamps()
        ->withPivot(['expires_at'])
        ->using(RoleUser::class);
}
```

### 远��一对多关联

```php
/**
 * 评论关联
 *
 * @return HasManyThrough
 */
public function comments(): HasManyThrough
{
    return $this->hasManyThrough(
        Comment::class,
        Post::class,
        'user_id',    // posts 表外键
        'post_id',    // comments 表外键
        'id',         // users 表本地键
        'id'          // posts 表本地键
    );
}
```

### 多态关联

```php
/**
 * 图片关联
 *
 * @return MorphMany
 */
public function images(): MorphMany
{
    return $this->morphMany(Image::class, 'imageable');
}

/**
 * 多态一对一关联
 *
 * @return MorphOne
 */
public function image(): MorphOne
{
    return $this->morphOne(Image::class, 'imageable');
}

/**
 * 多态多对多关联
 *
 * @return MorphToMany
 */
public function tags(): MorphToMany
{
    return $this->morphToMany(Tag::class, 'taggable');
}
```

## 关联查询

### 预加载关联

```php
// 预加载单个关联
$users = User::with('profile')->get();

// 预加载多个关联
$posts = Post::with(['author', 'comments'])->get();

// 预加载嵌套关联
$posts = Post::with('author.profile')->get();

// 预加载带条件的关联
$users = User::with(['posts' => function ($query) {
    $query->where('published', true);
}])->get();
```

### 延迟预加载

```php
// 获取模型集合后再加载关联
$books = Book::all();
$books->load('author', 'publisher');

// 带条件的延迟预加载
$books->load(['author' => function ($query) {
    $query->select('id', 'name');
}]);
```

### 关联约束查询

```php
// has 约束
$posts = Post::has('comments')->get();

// whereHas 约束
$posts = Post::whereHas('comments', function ($query) {
    $query->where('content', 'like', '%great%');
})->get();

// doesntHave 约束
$posts = Post::doesntHave('comments')->get();
```

## 最佳实践

1. 关联方法命名
```php
// 好的实践 - 清晰的方法名
public function author(): BelongsTo
{
    return $this->belongsTo(User::class);
}

// 不好的实践 - 模糊的方法名
public function relation(): BelongsTo
{
    return $this->belongsTo(User::class);
}
```

2. 关联预加载
```php
// 好的实践 - 使用预加载避免 N+1 问题
$posts = Post::with('author')->get();
foreach ($posts as $post) {
    echo $post->author->name;
}

// 不好的实践 - 导致 N+1 问题
$posts = Post::all();
foreach ($posts as $post) {
    echo $post->author->name;
}
```

3. 关联约束
```php
// 好的实践 - 使用关联约束
$users = User::whereHas('posts', function ($query) {
    $query->where('published', true);
})->get();

// 不好的实践 - 在循环中查询
$users = User::all();
$activeUsers = [];
foreach ($users as $user) {
    if ($user->posts()->where('published', true)->exists()) {
        $activeUsers[] = $user;
    }
}
```

## 常见问题

1. N+1 查询问题
```php
// 问题代码
$books = Book::all();
foreach ($books as $book) {
    echo $book->author->name;
}

// 解决方案
$books = Book::with('author')->get();
foreach ($books as $book) {
    echo $book->author->name;
}
```

2. 循环引用问题
```php
// 问题代码
class User extends Model
{
    public function posts()
    {
        return $this->hasMany(Post::class)->with('user');
    }
}

// 解决方案
class User extends Model
{
    public function posts()
    {
        return $this->hasMany(Post::class);
    }
}
```

## 注意事项

1. 合理使用预加载
2. 避免过深的关联嵌套
3. 注意关联查询性能
4. 使用适当的索引
5. 避免循环引用
6. 合理使用约束条件
