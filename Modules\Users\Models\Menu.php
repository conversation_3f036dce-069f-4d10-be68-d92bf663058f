<?php

namespace Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 菜单模型
 *
 * @property int $menu_id 菜单ID
 * @property int $parent_id 父级菜单ID
 * @property string $menu_name 菜单名称
 * @property string $menu_code 菜单代码
 * @property int $menu_type 菜单类型: 1-主菜单, 2-子菜单, 3-按钮
 * @property string|null $route_path 路由路径
 * @property string|null $component_path 组件路径
 * @property string|null $icon 菜单图标
 * @property int $sort_order 排序顺序
 * @property bool $is_visible 是否可见
 * @property bool $is_system 是否为系统菜单
 * @property int $status 状态: 0-禁用, 1-启用
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property \Carbon\Carbon|null $deleted_at 软删除时间
 */
class Menu extends Model
{
    use SoftDeletes;

    protected $table = 'menus';
    protected $primaryKey = 'menu_id';

    protected $fillable = [
        'parent_id',
        'menu_name',
        'menu_code',
        'menu_type',
        'route_path',
        'component_path',
        'icon',
        'sort_order',
        'is_visible',
        'is_system',
        'status'
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'menu_type' => 'integer',
        'sort_order' => 'integer',
        'is_visible' => 'boolean',
        'is_system' => 'boolean',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 获取父级菜单
     *
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Menu::class, 'parent_id', 'menu_id');
    }

    /**
     * 获取子菜单
     *
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(Menu::class, 'parent_id', 'menu_id');
    }

    /**
     * 获取所有子菜单（递归）
     *
     * @return HasMany
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 获取拥有此菜单的所有角色
     *
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_menus', 'menu_id', 'role_id')
                    ->withPivot(['granted_by', 'granted_at'])
                    ->withTimestamps();
    }

    /**
     * 检查菜单是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查菜单是否可见
     *
     * @return bool
     */
    public function isVisible(): bool
    {
        return $this->is_visible === true;
    }

    /**
     * 检查是否为系统菜单
     *
     * @return bool
     */
    public function isSystemMenu(): bool
    {
        return $this->is_system === true;
    }

    /**
     * 检查是否为顶级菜单
     *
     * @return bool
     */
    public function isTopLevel(): bool
    {
        return $this->parent_id === 0;
    }

    /**
     * 检查是否有子菜单
     *
     * @return bool
     */
    public function hasChildren(): bool
    {
        return $this->children()->count() > 0;
    }

    /**
     * 获取菜单类型文本
     *
     * @return string
     */
    public function getMenuTypeTextAttribute(): string
    {
        $types = [
            1 => '主菜单',
            2 => '子菜单',
            3 => '按钮'
        ];

        return $types[$this->menu_type] ?? '未知';
    }

    /**
     * 获取菜单层级路径
     *
     * @return string
     */
    public function getMenuPathAttribute(): string
    {
        $path = [$this->menu_name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->menu_name);
            $parent = $parent->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * 获取菜单完整路径
     *
     * @return string
     */
    public function getFullPathAttribute(): string
    {
        $path = [$this->route_path];
        $parent = $this->parent;

        while ($parent && $parent->route_path) {
            array_unshift($path, $parent->route_path);
            $parent = $parent->parent;
        }

        return implode('/', array_filter($path));
    }

    /**
     * 作用域：启用的菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：可见的菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * 作用域：顶级菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：按类型筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('menu_type', $type);
    }

    /**
     * 作用域：系统菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * 作用域：自定义菜单
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * 作用域：按菜单代码搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('menu_code', 'like', "%{$code}%");
    }

    /**
     * 作用域：按菜单名称搜索
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('menu_name', 'like', "%{$name}%");
    }

    /**
     * 作用域：按父级菜单筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $parentId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByParent($query, int $parentId)
    {
        return $query->where('parent_id', $parentId);
    }
}
