<template>
  <node-view-wrapper class="grid-block-component">
    <div 
      class="grid-block" 
      :class="gridClasses"
      data-type="grid-block"
      :data-layout="node.attrs.layout"
    >
      <div 
        v-for="(_, index) in columnCount" 
        :key="index" 
        class="grid-column"
        :style="getColumnStyle(index)"
      >
        <node-view-content :as="'div'" :index="index" />
      </div>
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NodeViewWrapper, NodeViewContent } from '@tiptap/vue-3'

const props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  updateAttributes: {
    type: Function,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
})

const columnCount = computed(() => {
  const layout = props.node.attrs.layout || '1'
  return layout.split(':').length
})

const gridClasses = computed(() => {
  const layout = props.node.attrs.layout || '1'
  const colCount = layout.split(':').length
  return {
    'single-column': colCount === 1,
    'two-columns': colCount === 2,
    'three-columns': colCount === 3,
    'four-columns': colCount === 4,
  }
})

const getColumnStyle = (index: number) => {
  const layout = props.node.attrs.layout || '1'
  const widthValues = layout.split(':')
  
  // 如果定义了具体的宽度比例，使用它们
  if (widthValues.length > index) {
    const width = widthValues[index]
    // 将比例转换为百分比
    const totalParts = widthValues.reduce((sum, part) => sum + Number(part), 0)
    const percentage = (Number(width) / totalParts) * 100
    return { width: `${percentage}%` }
  }
  
  // 否则平均分配
  return { width: `${100 / columnCount.value}%` }
}
</script>

<style scoped>
.grid-block-component {
  margin: 1.5em 0;
}

.grid-block {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
  border: 1px dashed #dcdfe6;
  padding: 16px;
  border-radius: 4px;
  background-color: rgba(245, 247, 250, 0.5);
}

.grid-column {
  min-width: 100px;
  min-height: 50px;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .grid-block {
    flex-direction: column;
  }
  
  .grid-column {
    width: 100% !important;
  }
}
</style> 