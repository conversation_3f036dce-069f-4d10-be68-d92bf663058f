<template>
  <div class="edit-section">
    <el-form label-position="top">
      <el-form-item label="内容">
        <el-input
          v-model="alertContent"
          type="textarea"
          :rows="3"
          @input="updateAlertContent"
        />
      </el-form-item>
      
      <el-form-item label="样式">
        <el-select v-model="alertStyle" @change="updateAlertContent" style="width: 100%">
          <el-option label="主要" value="primary" />
          <el-option label="次要" value="secondary" />
          <el-option label="成功" value="success" />
          <el-option label="危险" value="danger" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
          <el-option label="浅色" value="light" />
          <el-option label="深色" value="dark" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="可关闭">
        <el-switch v-model="alertDismissible" @change="updateAlertContent" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions } from 'vue'

// 定义组件名称
defineOptions({
  name: 'AlertEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 提示框数据
const alertContent = ref('这是一个提示框')
const alertStyle = ref('primary')
const alertDismissible = ref(true)

// 组件挂载时，从现有块元素中提取提示框数据
onMounted(() => {
  if (props.blockElement) {
    // 获取提示框内容和样式
    const alertEl = props.blockElement as HTMLElement
    const btnClose = alertEl.querySelector('.btn-close')

    alertDismissible.value = !!btnClose

    // 移除可能的关闭按钮后获取内容
    let content = alertEl.innerHTML
    if (btnClose) {
      content = content.replace(btnClose.outerHTML, '').trim()
    }
    alertContent.value = content

    // 获取样式
    const styleClass = Array.from(alertEl.classList)
      .find((cls: string) => cls.startsWith('alert-'))
    if (styleClass) {
      alertStyle.value = styleClass.replace('alert-', '')
    } else {
      alertStyle.value = 'primary'
    }
  }
})

// 更新提示框内容
const updateAlertContent = () => {
  const dismissibleClass = alertDismissible.value ? ' alert-dismissible fade show' : ''
  const closeButton = alertDismissible.value
    ? '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>'
    : ''

  const html = `
    <div data-bs-component="alert" class="alert alert-${alertStyle.value}${dismissibleClass}" role="alert">
      ${alertContent.value}
      ${closeButton}
    </div>
  `
  emit('update-block', { html })
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
}
</style> 