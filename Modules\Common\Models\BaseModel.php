<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class BaseModel extends Model
{
    protected $dates = [
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (in_array('created_by', $model->getFillable())) {
                $model->created_by = Auth::id() ?: 0;
            }
            if (in_array('updated_by', $model->getFillable())) {
                $model->updated_by = Auth::id() ?: 0;
            }
        });

        static::updating(function ($model) {
            if (in_array('updated_by', $model->getFillable())) {
                $model->updated_by = Auth::id() ?: 0;
            }
        });

        static::deleting(function ($model) {
            // 只对存在的字段进行操作
            if (in_array('is_deleted', $model->getFillable())) {
                $model->is_deleted = 1;
                $model->save();
            }
        });
    }

        /**
     * 批量软删除方法
     * 同时处理 Laravel SoftDeletes 和 is_deleted 字段
     *
     * @param array $ids 要删除的ID数组
     * @param string $field 字段名，默认为 'id'
     * @return int 更新的记录数
     */
    public static function softDeleteByField(array $ids, string $field = 'id'): int
    {
        if (empty($ids)) {
            return 0;
        }

        $model = new static();
        $table = $model->getTable();
        $updateData = [];

        // 只加存在的字段
        if (Schema::hasColumn($table, 'updated_at')) {
            $updateData['updated_at'] = now();
        }
        if (Schema::hasColumn($table, 'updated_by')) {
            $updateData['updated_by'] = Auth::id() ?: 0;
        }
        if (Schema::hasColumn($table, 'is_deleted')) {
            $updateData['is_deleted'] = 1;
        }
        if (Schema::hasColumn($table, 'deleted_at')) {
            $updateData['deleted_at'] = now();
        }

        if (empty($updateData)) {
            // 没有可更新的字段，直接返回
            return 0;
        }

        $count = static::whereIn($field, $ids)->update($updateData);

        return $count;
    }

    /**
     * 批量软删除方法（通过ID）
     * 同时处理 Laravel SoftDeletes 和 is_deleted 字段
     *
     * @param array $ids 要删除的ID数组
     * @return int 更新的记录数
     */
    public static function softDeleteByIds(array $ids): int
    {
        return static::softDeleteByField($ids, 'id');
    }
}

