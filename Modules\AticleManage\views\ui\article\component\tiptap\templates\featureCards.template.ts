export const featureCardsTemplate = `
<div class="py-5 text-white bootstrap-feature-cards responsive-block bg-dark" data-bs-component="feature-cards">
  <div class="container">
    <div class="row g-4 justify-content-center">
      <div class="col-12 col-sm-6 col-lg-4">
        <div class="text-center feature-card h-100">
          <div class="icon-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-play-circle" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
            </svg>
          </div>
          <h3>内容创作</h3>
          <p class="text-white-50">多样化的内容编辑工具，支持富文本、图片和视频等多种媒体格式。</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-4">
        <div class="text-center feature-card h-100">
          <div class="icon-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bar-chart" viewBox="0 0 16 16">
              <path d="M4 11H2v3h2v-3zm5-4H7v7h2V7zm5-5v12h-2V2h2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1h-2zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-3z"/>
            </svg>
          </div>
          <h3>数据分析</h3>
          <p class="text-white-50">强大的数据分析和报告功能，帮助您了解用户行为和内容效果。</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-4">
        <div class="text-center feature-card h-100">
          <div class="icon-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-graph-up" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
            </svg>
          </div>
          <h3>系统集成</h3>
          <p class="text-white-50">无缝集成各类第三方服务和API，扩展系统功能。</p>
        </div>
      </div>
    </div>
  </div>

  <style>
    .bootstrap-feature-cards {
      position: relative;
      overflow: hidden;
      background: linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
    }

    .feature-card {
      position: relative;
      padding: 2.5rem 2rem;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .feature-card:hover {
      transform: translateY(-8px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .feature-card .icon-wrapper {
      width: 80px;
      height: 80px;
      margin: 0 auto 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(5px);
      transition: all 0.4s ease;
    }

    .feature-card:hover .icon-wrapper {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1) rotate(5deg);
    }

    .feature-card h3 {
      font-size: 1.75rem;
      margin-bottom: 1.25rem;
      font-weight: 600;
      color: #fff;
    }

    .feature-card p {
      font-size: 1.1rem;
      line-height: 1.7;
      margin-bottom: 0;
      color: rgba(255, 255, 255, 0.7);
    }

    /* 移动端样式 - 响应容器宽度 */
    .mobile-preview .bootstrap-feature-cards {
      padding: 2rem 0;
    }

    .mobile-preview .feature-card {
      padding: 2rem 1.5rem;
      margin: 0.5rem;
    }

    .mobile-preview .feature-card h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    .mobile-preview .feature-card p {
      font-size: 1rem;
      line-height: 1.6;
    }

    .mobile-preview .feature-card .icon-wrapper {
      width: 64px;
      height: 64px;
      margin-bottom: 1.5rem;
    }

    .mobile-preview .feature-card .icon-wrapper svg {
      width: 28px;
      height: 28px;
    }

    /* 平板预览样式 */
    .tablet-preview .bootstrap-feature-cards {
      padding: 3rem 0;
    }

    .tablet-preview .feature-card {
      padding: 2.25rem 1.75rem;
      margin: 0.75rem;
    }

    .tablet-preview .feature-card h3 {
      font-size: 1.6rem;
    }

    .tablet-preview .feature-card .icon-wrapper {
      width: 72px;
      height: 72px;
    }

    /* 响应式布局 */
    @media (max-width: 575.98px) {
      .bootstrap-feature-cards {
        padding: 2rem 0;
      }

      .feature-card {
        padding: 2rem 1.5rem;
        margin: 0.5rem auto;
        max-width: 320px;
      }

      .feature-card h3 {
        font-size: 1.5rem;
      }

      .feature-card p {
        font-size: 1rem;
      }

      .feature-card .icon-wrapper {
        width: 64px;
        height: 64px;
        margin-bottom: 1.5rem;
      }

      .feature-card .icon-wrapper svg {
        width: 28px;
        height: 28px;
      }
    }

    @media (min-width: 576px) and (max-width: 991.98px) {
      .bootstrap-feature-cards {
        padding: 3rem 0;
      }

      .feature-card {
        padding: 2.25rem 1.75rem;
        margin: 0.75rem auto;
        max-width: none;
      }

      .row {
        margin: 0 -0.75rem;
      }

      [class*="col-"] {
        padding: 0 0.75rem;
      }
    }

    @media (min-width: 992px) {
      .bootstrap-feature-cards {
        padding: 5rem 0;
      }

      .feature-card {
        padding: 2.5rem 2rem;
      }

      .row {
        margin: 0 -1rem;
      }

      [class*="col-"] {
        padding: 0 1rem;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .bootstrap-feature-cards {
      padding: 5rem 0;
    }

    .desktop-preview .feature-card {
      padding: 2.5rem 2rem;
    }

    .desktop-preview .feature-card h3 {
      font-size: 1.75rem;
    }

    .desktop-preview .feature-card .icon-wrapper {
      width: 80px;
      height: 80px;
    }

    .desktop-preview .feature-card .icon-wrapper svg {
      width: 32px;
      height: 32px;
    }
  </style>
</div>
` 