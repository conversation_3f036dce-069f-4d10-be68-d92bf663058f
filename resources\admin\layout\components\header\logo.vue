<template>
  <div class="header-toolbar flex items-center justify-between py-2 pl-8 pr-5" style="--el-text-color-regular: #fff;">
    <div class="logo-bg flex items-center">
        <img src="https://www.hk-bingo.com/images/website/logo/logo.png" style="height:24px; filter: brightness(0) invert(1);" />
        <div class="text-md logo-text" v-if="store.isExpand">BWMS</div>
    </div>
    <div class="flex items-center">
      <!-- 首页 -->
      <router-link :to="{ path: '/dashboard' }" class="w-10 h-10 flex items-center justify-center">
        <el-icon size="18" color="#fff"><House /></el-icon>
      </router-link>
      <!-- 设置 -->
      <router-link :to="{ path: '/config/config' }" class="w-10 h-10 flex items-center justify-center">
        <el-icon size="18" color="#fff"><Setting /></el-icon>
      </router-link>
      <!-- 多语言 -->
      <Lang />
      <!-- 暗黑主题 -->
      <Theme />
      <!-- <Notification />-->
      <Profile />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '/admin/stores/modules/app'

const store = useAppStore();
const goHome = () => {
}
</script>
<style scoped lang="scss">
.header-toolbar {
  background-color: var(--bg-color);
  // background-color: var(--header-logo-bg-color);
}

.logo-text {
  color: #fff;
}
</style>
