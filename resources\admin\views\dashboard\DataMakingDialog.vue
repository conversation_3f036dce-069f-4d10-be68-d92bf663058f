<template>
  <div>
    <el-dialog 
      :model-value="visible" 
      :title="title" 
      width="996px"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="true"
      align-center
      @close="handleCancel"
      @update:model-value="handleDialogUpdate"
      class="el-dialog-common-cls-2"
    >
      <!-- 监控设置区域 -->
      <div class="monitor-settings">
        <div class="settings-header">
          <h3>監控設定</h3>
          <div class="edit-btn">
            <el-icon size="14" color="#486FB2"><EditPen /></el-icon>
            <span>編輯</span>
          </div>
        </div>

        <div class="settings-form">
          <div class="form-row">
            <div class="form-left">
              <div class="form-item">
                <label>監控網址</label>
                <span>{{ monitorUrl }}</span>
              </div>

              <div class="form-item">
                <label>監控區塊</label>
                <span>{{ monitorSection }}</span>
              </div>
            </div>

            <div class="form-right">
              <div class="form-item frequency">
                <label>監控頻率</label>
                <el-select v-model="frequency" placeholder="請選擇">
                  <el-option label="每小時" value="1hour" />
                  <el-option label="每天" value="1day" />
                  <el-option label="每週" value="1week" />
                </el-select>
              </div>

              <div class="form-item notification">
                <label>通知方式</label>
                <div class="checkbox-group">
                  <el-checkbox style="margin-left: 8px;" v-model="notifications.email">Email</el-checkbox>
                  <el-checkbox v-model="notifications.sms">SMS</el-checkbox>
                  <el-checkbox v-model="notifications.push">通知</el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新异常区域 -->
      <div class="latest-exceptions">
        <h3>最新異常</h3>
        <div class="exception-item">
          <div class="exception-type">
            <h4>異常類型</h4>
            <span class="time">11/14 上午 09:23</span>
          </div>

          <div class="content-changes">
            <h4>內容更新</h4>
            <div class="changes-comparison">
              <div class="before">
                <h5>異常前</h5>
                <div class="content-box">
                  <span>無專題頁面</span>
                </div>
              </div>
              <div class="after">
                <h5>異常後</h5>
                <div class="content-box">
                  <span>新增完整專題頁，包含：</span>
                  <ul>
                    <li>申請資格核查工具</li>
                    <li>流程圖解(4個步驟)</li>
                    <li>常見問題(12條)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI建议区域 -->
      <div class="ai-suggestions">
        <div class="section-header">
          <h3>AI 建議</h3>
          <div class="impact-level">
            <span class="label">影響範圍：</span>
            <span class="level high">高影響</span>
          </div>
        </div>
        
        <div class="suggestions-content">
          <div class="analysis-section">
            <h4>AI 分析</h4>
            <div class="analysis-items">
              <div class="analysis-item">
                <span class="bullet">●</span>
                <span>該專題頁採用「問題導向」結構，直接突出「申請難點解決」模塊</span>
              </div>
              <div class="analysis-item">
                <span class="bullet">●</span>
                <span>嵌入 3 個用戶案例視頻（平均時長 1 分 30 秒），增強說服力</span>
              </div>
              <div class="analysis-item">
                <span class="bullet">●</span>
                <span>頁面加載速度：1.2 秒（優於行業平均 2.3 秒）</span>
              </div>
            </div>
          </div>

          <div class="suggestions-section">
            <div class="suggestions-section-header">
              <h4>建議行動</h4>
              <div class="accept-btn-container">
                <el-button type="primary" class="accept-btn">採納建議</el-button>
              </div>
            </div>
            <div class="suggestion-items" style="margin-left: 14px;">
              <div class="suggestion-item">
                <span class="label">a.</span>
                <span>建議 HKTE 參考其「分步驟流程圖」設計，優化本網站「優秀人才入境計劃」申請頁</span>
              </div>
              <div class="suggestion-item">
                <span class="label">b.</span>
                <span>建議新增「成功案例視頻」模塊，放置於申請指南頁首屏</span>
              </div>
              <div class="suggestion-item">
                <span class="label">c.</span>
                <span>建議核查本網站類似專題頁加載速度，目標壓縮至 1.5 秒以內</span>
              </div>
            </div>
            
          </div>
        </div>
      </div>

      <!-- 异常历史区域 -->
      <div class="exception-history">
        <div class="history-header">
          <h3>異常歷史</h3>
          <div class="view-more-btn">
            查看更多異常歷史
          </div>
        </div>

        <div class="history-timeline">
          <div class="timeline-item">
            <div class="timeline-dot active"></div>
            <div class="timeline-content">
              <div class="timeline-date">2024/11/10 下午 02:15</div>
              <div class="timeline-desc-container">
                <div class="timeline-desc">更新「全球人才積分制」計算器算法</div>
                <el-button type="primary" class="detail-btn">查看異常詳情</el-button>
              </div>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-dot"></div>
            <div class="timeline-content">
              <div class="timeline-date">2024/11/05 上午 11:30</div>
              <div class="timeline-desc-container">
                <div class="timeline-desc">調整網站導航欄，將「人才居留」提至首級菜單</div>
                <el-button type="primary" class="detail-btn">查看異常詳情</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { EditPen } from '@element-plus/icons-vue'

// Props 定义
interface Props {
  visible: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '詳細動態分析'
})

// Emits 定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'cancel'): void
}>()

// 表单数据
const monitorUrl = ref('https://www.singapore-talents.gov.sg/tech-pass')
const monitorSection = ref('div.tech-pass > article.content-section')
const frequency = ref('1hour')
const notifications = ref({
  email: true,
  sms: true,
  push: true
})

// 方法
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

const handleDialogUpdate = (value: boolean) => {
  emit('update:visible', value)
}
</script>

<style lang="scss" scoped>

.monitor-settings {
  border: 1px solid #DFDFDF;
  border-radius: 5px;
  background: #F8F8F8;
  padding: 17px 16px 17px 17px;

  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 31px;

    h3 {
      font-size: 16px;
      font-weight: bold;
      color: #242424;
      margin: 0;
      line-height: 21px;
    }

    .edit-btn {
      display: flex;
      align-items: center;
      gap: 9px;
      color: #486FB2;
      font-size: 14px;
      font-weight: normal;
      
    }
  }

  .settings-form {

    .form-row {
      display: flex;
      justify-content: space-between;

    }

    .form-left, .form-right {
      flex: 1;
    }

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        width: 80px;
        color: #7F7F7F;
        font-size: 14px;
        flex-shrink: 0;
        line-height: 33px;
      }

      span {
        color: #303030;
        font-size: 14px;
        flex: 1;
        line-height: 33px;
      }

      .el-select {
        flex: 1;
      }

      &.frequency {
        display: flex;
        justify-content: space-between;

        :deep(.el-select) {
          max-width: 120px;
          height: 33px;
          border-radius: 5px;
          .el-select__wrapper {
            line-height: 33px;
            min-height: 33px;
          }
        }
      }

      &.notification {
        display: flex;
        justify-content: space-between;
        
        label {
          width: max-content;
        }
        
        .checkbox-group {
          
        }
      }
    }
  }
}

// 最新异常区域样式
.latest-exceptions {
  margin-top: 29px;

  h3 {
    font-size: 18px;
    font-weight: bold;
    color: #232323;
    margin: 0 0 12px 0;
  }

  .exception-item {
    box-shadow: 0px 1px 1px #0000000D;
    border: 1px solid #DEDEDE;
    border-radius: 5px;
    padding: 22px 20px 24px 20px;
  }

  .exception-type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #D2D2D2;

    h4 {
      font-size: 16px;
      color: #232323;
      margin: 0;
      font-weight: bold;
      line-height: 21px;
    }

    .time {
      color: #9A9A9A;
      font-size: 14px;
    }
  }

  .content-changes {
    h4 {
      font-size: 16px;
      color: #232323;
      margin: 0 0 18px 0;
      font-weight: bold;
      line-height: 21px;
    }

    .changes-comparison {
      display: flex;
      gap: 24px;
      
      .before, .after {
        flex: 1;
        box-shadow: 0px 1px 1px #0000000D;
        border-radius: 5px;
        background: #F8F8F8;
        padding: 23px 28px 27px 28px;

        h5 {
          font-size: 18px;
          color: #232323;
          margin: 0 0 19px 0;
          line-height: 24px;
        }

        .content-box {
          background: #fff;
          border-radius: 5px;
          box-shadow: 0px 1px 1px #0000000D;
          border: 2px solid #DEDEDE;
          padding: 18px;
          min-height: 180px;

          span {
            color: #303030;
            font-size: 14px;
            line-height: 19px;
          }

          ul {
            margin: 10px 0 0 0;
            padding-left: 20px;

            li {
              color: #303030;
              font-size: 14px;
              margin-bottom: 8px;
              position: relative;
              &::before {
                content: '';
                position: absolute;
                left: -18px;
                top: 8px;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #A7A7A7;
                display: inline-block;
              }
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// AI建议区域样式
.ai-suggestions {
  margin-top: 33px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 13px;

    h3 {
      font-size: 18px;
      font-weight: bold;
      color: #232323;
      margin: 0;
      line-height: 24px;
    }

    .impact-level {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        color: #646464;
        font-size: 14px;
      }

      .level {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        
        &.high {
          background: #FDECEC;
          color: #F05657;
        }
      }
    }
  }

  .suggestions-content {
    display: flex;
    box-shadow: 0px 1px 1px #0000000D;
    border: 1px solid #DEDEDE;
    border-radius: 5px;
    padding: 19px 17px 44px 18px;

    .suggestions-section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #D2D2D2;
      margin-bottom: 19px;

    .accept-btn-container {
        text-align: right;
        margin-top: -20px;

        .accept-btn {
          background: #007EE5;
          border-color: #007EE5;
          border-radius: 5px;
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }
    

    .analysis-section, .suggestions-section {
      flex: 1;

      h4 {
        font-size: 18px;
        font-weight: bold;
        color: #232323;
        padding-bottom: 19px;
        margin: 0 0 19px 0;
        line-height: 24px;
        
      }
    }
    .analysis-section {
      h4 {
        border-bottom: 1px solid #D2D2D2;
      }
    }
    .suggestions-section {
      h4 {
        border-bottom: none;
        margin: 0;
      }
    }

    .analysis-items, .suggestion-items {
      .analysis-item, .suggestion-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        gap: 11px;

        &:last-child {
          margin-bottom: 0;
        }

        .bullet {
          color: #A7A7A7;
          font-size: 11px;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .label {
          color: #232323;
          font-size: 16px;
          flex-shrink: 0;
          margin-top: 0px;
          line-height: 19px;
        }

        span:last-child {
          color: #303030;
          font-size: 16px;
          line-height: 19px;
        }
      }
    }

  }
}

// 异常历史区域样式
.exception-history {
  margin-top: 32px;

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      font-size: 18px;
      font-weight: bold;
      color: #232323;
      margin: 0;
      line-height: 24px;
    }

    .view-more-btn {
      color: #007EE5;
      font-size: 14px;
      border-bottom: 1px solid #007EE5;
      
      &:hover {
        color: #0056b3;
      }
    }
  }

  .history-timeline {
    .timeline-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 35px;
      gap: 9px;

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-dot {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #FFFFFF;
        border: 1px solid #9A9A9A;
        flex-shrink: 0;
        margin-top: 6px;
        position: relative;
        &::before {
          content: '';
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: #9A9A9A;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

                &.active {
          border: 1px solid #007EE5;
          &::before {
            background: #007EE5;
          }
        }
      }

      &:not(:last-child) {
        .timeline-dot::after {
          content: '';
          position: absolute;
          left: 50%;
          top: 26px;
          width: 1px;
          height: 118px;
          background: #87C9FF;
          transform: translateX(-50%);
        }
      }

      .timeline-content {
        flex: 1;

        .timeline-desc-container {
          background: #E9F3FF;
          border-radius: 5px;
          box-shadow: 0px 1px 1px #0000000D;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 23px 18px 19px 23px;
        }

        .timeline-date {
          color: #007EE5;
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .timeline-desc {
          color: #232323;
          font-size: 16px;
          line-height: 19px;

        }

        .detail-btn {
          width: 114px;
          font-size: 14px;
        }
      }
    }
  }
}

</style>

<style lang="scss">
.el-dialog-common-cls-2 {
  background: #fff;
  padding: 0;

  .el-dialog__header {
    height: 62px;
    padding: 4px 28px 0 23px;
    border-bottom: 1px solid #D2D2D2;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-dialog__title {
      color: #202020;
      font-size: 20px;
      font-weight: normal;
    }
  }

  .el-dialog__body {
    padding: 23px 24px 26px 23px;
  }
}
// .frequency {
//   :deep(.el-select) {
//     max-width: 120px;
//     height: 33px;
//     border-radius: 5px;
//     .el-select__wrapper {
//       line-height: 33px;
//       min-height: 33px;
//     }
//   }
// }
</style>
