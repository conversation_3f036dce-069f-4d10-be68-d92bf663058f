export const infoSectionTemplate = `
<div class="py-5 info-section responsive-block" data-bs-component="info-section">
  <div class="container">
    <div class="row align-items-center">
      <div class="mb-4 col-lg-6 mb-lg-0 info-image-wrapper">
        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/analytics-dashboard.png" class="img-fluid rounded-4 info-image" alt="Feature image">
      </div>
      <div class="col-lg-6 info-content">
        <div data-bs-component="rich-text" class="info-text">
          <h2 class="mb-3 fw-bold info-title">Collaborate seamlessly</h2>
          <p class="text-muted info-description">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
        </div>
        <div class="mt-3 info-button" data-bs-component="button">
          <a href="#" class="explore-more">Explore more</a>
        </div>
      </div>
    </div>
  </div>

  <style>
    .info-section {
      position: relative;
      overflow: hidden;
    }

    .info-image-wrapper {
      position: relative;
      transition: transform 0.3s ease;
    }

    .info-image {
      width: 100%;
      height: auto;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .info-content {
      position: relative;
      z-index: 1;
    }

    .info-title {
      font-size: 2.5rem;
      color: #2d3748;
      line-height: 1.2;
    }

    .info-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: #4a5568;
      margin-bottom: 2rem;
    }

    .explore-more {
      display: inline-block;
      color: #4e73df;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      position: relative;
      padding-bottom: 5px;
      transition: all 0.3s ease;
    }

    .explore-more::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #4e73df;
      transform: scaleX(0);
      transform-origin: right;
      transition: transform 0.3s ease;
    }

    .explore-more:hover::after {
      transform: scaleX(1);
      transform-origin: left;
    }

    /* 移动端预览模式样式 */
    .mobile-preview .info-section {
      padding: 2rem 0;
    }

    .mobile-preview .info-image-wrapper {
      margin-bottom: 2rem;
      padding: 0 1rem;
    }

    .mobile-preview .info-content {
      text-align: center;
      padding: 0 1rem;
    }

    .mobile-preview .info-title {
      font-size: 1.75rem;
      margin-bottom: 1rem;
    }

    .mobile-preview .info-description {
      font-size: 1rem;
      margin-bottom: 1.5rem;
    }

    .mobile-preview .info-button {
      text-align: center;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .info-section {
        padding: 2rem 0;
      }

      .info-image-wrapper {
        margin-bottom: 2rem;
        padding: 0 1rem;
      }

      .info-content {
        text-align: center;
        padding: 0 1rem;
      }

      .info-title {
        font-size: 1.75rem;
        margin-bottom: 1rem;
      }

      .info-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
      }

      .info-button {
        text-align: center;
      }
    }

    /* 平板端样式 */
    @media (min-width: 768px) and (max-width: 991.98px) {
      .info-section {
        padding: 3rem 0;
      }

      .info-title {
        font-size: 2rem;
      }

      .info-description {
        font-size: 1.05rem;
      }
    }

    /* 桌面端样式 */
    @media (min-width: 992px) {
      .info-section {
        padding: 5rem 0;
      }

      .info-title {
        font-size: 2.5rem;
      }

      .info-description {
        font-size: 1.125rem;
      }
    }

    /* 桌面预览模式覆盖样式 */
    .desktop-preview .info-section {
      padding: 5rem 0;
    }

    .desktop-preview .info-content {
      text-align: left;
      padding: 0;
    }

    .desktop-preview .info-title {
      font-size: 2.5rem;
    }

    .desktop-preview .info-description {
      font-size: 1.125rem;
    }

    .desktop-preview .info-button {
      text-align: left;
    }
  </style>
</div>
`

export const infoSectionAlternateTemplate = `
<div class="py-5 bg-light info-section responsive-block" data-bs-component="info-section">
  <div class="container">
    <div class="row align-items-center">
      <div class="mb-4 col-lg-6 order-lg-2 mb-lg-0 info-image-wrapper">
        <img src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/multichannel-campaign.png" class="img-fluid rounded-4 info-image" alt="Multichannel campaign">
      </div>
      <div class="col-lg-6 order-lg-1 info-content">
        <div data-bs-component="rich-text" class="info-text">
          <h2 class="mb-3 fw-bold info-title">Manage multi-channel campaigns</h2>
          <p class="text-muted info-description">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
        </div>
        <div class="mt-3 info-button" data-bs-component="button">
          <a href="#" class="explore-more">Explore more</a>
        </div>
      </div>
    </div>
  </div>

  <style>
    .info-section.bg-light {
      background-color: #f8f9fa;
    }

    /* 移动端预览模式样式 */
    .mobile-preview .info-section .order-lg-2 {
      order: -1;
    }

    .mobile-preview .info-section .order-lg-1 {
      order: 1;
    }

    /* 移动端样式 - 响应屏幕宽度 */
    @media (max-width: 767.98px) {
      .info-section .order-lg-2 {
        order: -1;
      }

      .info-section .order-lg-1 {
        order: 1;
      }
    }
  </style>
</div>
` 