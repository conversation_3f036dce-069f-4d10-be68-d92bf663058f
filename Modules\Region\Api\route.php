<?php

use Illuminate\Support\Facades\Route;
use Modules\Region\Api\Controllers\RegionController;
use Modules\Region\Api\Controllers\ChannelController;
use Modules\Common\Middleware\LanguageMiddleware;

/*
|--------------------------------------------------------------------------
| Region API Routes
|--------------------------------------------------------------------------
|
| 区域管理相关的API路由
|
*/

Route::prefix('region')->middleware([LanguageMiddleware::class])->group(function () {
    
    // 区域列表
    Route::get('/', [RegionController::class, 'index'])->name('region.index');
    
    // 创建区域
    Route::post('/', [RegionController::class, 'store'])->name('region.store');
    
    // 获取单个区域
    Route::get('/{id}', [RegionController::class, 'show'])->name('region.show');
    
    // 更新区域
    Route::put('/{id}', [RegionController::class, 'update'])->name('region.update');
    
    // 删除区域
    Route::delete('/{id}', [RegionController::class, 'destroy'])->name('region.destroy');
    
    // 启用/禁用区域
    Route::patch('/{id}/status', [RegionController::class, 'updateStatus'])->name('region.status');
    
    // 批量操作
    Route::post('/batch', [RegionController::class, 'batchAction'])->name('region.batch');
    
});

// 频道管理路由
Route::prefix('channel')->middleware([LanguageMiddleware::class])->group(function () {
    
    // 频道列表
    Route::get('/', [ChannelController::class, 'index'])->name('channel.index');
    
    // 创建频道
    Route::post('/', [ChannelController::class, 'store'])->name('channel.store');
    
    // 获取单个频道
    Route::get('/{id}', [ChannelController::class, 'show'])->name('channel.show');
    
    // 更新频道
    Route::put('/{id}', [ChannelController::class, 'update'])->name('channel.update');
    
    // 删除频道
    Route::delete('/{id}', [ChannelController::class, 'destroy'])->name('channel.destroy');
    
    // 启用/禁用频道
    Route::patch('/{id}/status', [ChannelController::class, 'updateStatus'])->name('channel.status');
    
    // 删除频道与区域的关联关系
    Route::delete('/{channelId}/region/{regionId}', [ChannelController::class, 'deleteRegionAssociation'])->name('channel.deleteRegionAssociation');
    
    // 获取频道关联的区域列表
    Route::get('/{id}/regions', [ChannelController::class, 'getChannelRegions'])->name('channel.regions');
    
    // 批量操作
    Route::post('/batch', [ChannelController::class, 'batchAction'])->name('channel.batch');
    
}); 