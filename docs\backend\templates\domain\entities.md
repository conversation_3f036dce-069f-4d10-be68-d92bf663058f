# 领域实体模板

## 概述

领域实体是领域驱动设计中的核心概念,代表了业务领域中的重要概念。本文档提供了领域实体的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

namespace Modules\YourModule\Domain\Entities;

use Modules\YourModule\Domain\ValueObjects\Money;
use Modules\YourModule\Domain\Enums\YourModuleStatus;
use Modules\YourModule\Domain\Events\YourModuleCreatedEvent;
use Modules\YourModule\Domain\Exceptions\YourModuleException;
use Modules\Shared\Domain\Traits\HasTimestamps;
use DateTime;

final class YourEntity
{
    use HasTimestamps;

    private int $id;
    private string $title;
    private string $description;
    private YourModuleStatus $status;
    private Money $amount;
    private array $items = [];
    private array $events = [];

    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getStatus(): YourModuleStatus
    {
        return $this->status;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function addItem(YourItem $item): void
    {
        $this->items[] = $item;
        $this->recalculateAmount();
    }

    public function removeItem(YourItem $item): void
    {
        $this->items = array_filter($this->items, fn($i) => $i->getId() !== $item->getId());
        $this->recalculateAmount();
    }

    public function activate(): void
    {
        if ($this->status !== YourModuleStatus::PENDING) {
            BizException::throws(YourModuleErrorCode::CANNOT_ACTIVATE, '只有待处理的记录可以激活');
        }

        $this->status = YourModuleStatus::ACTIVE;
        $this->addEvent(new YourModuleActivatedEvent($this));
    }

    public function deactivate(): void
    {
        if ($this->status !== YourModuleStatus::ACTIVE) {
            BizException::throws(YourModuleErrorCode::CANNOT_DEACTIVATE, '只有已激活的记录可以停用');
        }

        $this->status = YourModuleStatus::INACTIVE;
        $this->addEvent(new YourModuleDeactivatedEvent($this));
    }

    private function recalculateAmount(): void
    {
        $this->amount = array_reduce(
            $this->items,
            fn(Money $carry, YourItem $item) => $carry->add($item->getAmount()),
            new Money(0)
        );
    }

    private function fill(array $attributes): void
    {
        if (isset($attributes['id'])) {
            $this->id = $attributes['id'];
        }

        if (isset($attributes['title'])) {
            $this->title = $attributes['title'];
        }

        if (isset($attributes['description'])) {
            $this->description = $attributes['description'];
        }

        if (isset($attributes['status'])) {
            $this->status = is_object($attributes['status'])
                ? $attributes['status']
                : YourModuleStatus::from($attributes['status']);
        }

        if (isset($attributes['amount'])) {
            $this->amount = is_object($attributes['amount'])
                ? $attributes['amount']
                : new Money($attributes['amount']);
        }

        if (isset($attributes['items'])) {
            foreach ($attributes['items'] as $item) {
                $this->items[] = is_object($item)
                    ? $item
                    : new YourItem($item);
            }
        }

        if (isset($attributes['created_at'])) {
            $this->createdAt = is_object($attributes['created_at'])
                ? $attributes['created_at']
                : new DateTime($attributes['created_at']);
        }

        if (isset($attributes['updated_at'])) {
            $this->updatedAt = is_object($attributes['updated_at'])
                ? $attributes['updated_at']
                : new DateTime($attributes['updated_at']);
        }
    }

    private function addEvent(object $event): void
    {
        $this->events[] = $event;
    }

    public function releaseEvents(): array
    {
        $events = $this->events;
        $this->events = [];
        return $events;
    }
}
```

## 规范要求

1. 命名规范
   - 类名：使用大驼峰命名法
   - 方法名：使用小驼峰命名法
   - 属性名：使用小驼峰命名法
   - 常量名：使用大写下划线

2. 属性定义
   - 使用私有属性
   - 类型声明
   - 值对象封装
   - 合理默认值

3. 方法设计
   - 行为语义化
   - 职责单一
   - 参数类型声明
   - 返回值类型声明

4. 领域规则
   - 业务规则封装
   - 状态转换控制
   - 异常处理
   - 事件发布

## 最佳实践

1. 值对象使用
```php
final class Money
{
    private float $amount;
    private string $currency;

    public function __construct(float $amount, string $currency = 'CNY')
    {
        if ($amount < 0) {
            BizException::throws(YourModuleErrorCode::INVALID_AMOUNT, '金额不能为负');
        }
        $this->amount = $amount;
        $this->currency = $currency;
    }

    public function add(Money $other): self
    {
        if ($this->currency !== $other->currency) {
            BizException::throws(YourModuleErrorCode::CURRENCY_MISMATCH, '货币类型不匹配');
        }
        return new self($this->amount + $other->amount, $this->currency);
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }
}
```

2. 枚举定义
```php
enum YourModuleStatus: string
{
    case PENDING = 'pending';
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';

    public function label(): string
    {
        return match($this) {
            self::PENDING => '待处理',
            self::ACTIVE => '已激活',
            self::INACTIVE => '已停用',
        };
    }

    public function canTransitionTo(self $status): bool
    {
        return match($this) {
            self::PENDING => in_array($status, [self::ACTIVE]),
            self::ACTIVE => in_array($status, [self::INACTIVE]),
            default => false,
        };
    }
}
```

3. 事件定义
```php
final class YourModuleCreatedEvent
{
    public function __construct(
        private readonly YourEntity $entity
    ) {
    }

    public function getEntity(): YourEntity
    {
        return $this->entity;
    }
}
```

## 注意事项

1. 实体必须有唯一标识
2. 保持实体的不变性
3. 封装内部实现
4. 使用值对象
5. 实现领域规则
6. 发布领域事件
7. 处理并发问题
8. 注意性能影响
9. 保持代码整洁
10. 实现可测试性
