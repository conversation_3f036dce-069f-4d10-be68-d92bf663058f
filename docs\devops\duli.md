# BWMS 独立主机部署指南

本文档旨在指导用户在独立主机环境（如物理服务器、VPS）上成功部署 BWMS 系统。推荐使用 Ubuntu 20.04 LTS 或更高版本，CentOS 8+ 或 RedHat 企业版 Linux 8+ 也是支持的选项。

## 1. 概述

独立主机环境提供了完整的服务器控制权限，可以进行系统级配置和优化。本指南将详细介绍如何在独立主机上部署 BWMS，包括环境配置、依赖安装、应用部署和安全加固等步骤。

## 2. 先决条件

在开始部署之前，请确保您的环境满足以下要求：

### 2.1. 硬件要求

*   **CPU**: 最低 2 核心，推荐 4 核心或更多
*   **内存**: 最低 4GB RAM，推荐 8GB 或更多
*   **存储**: 最低 20GB 可用空间，推荐 SSD 存储
*   **网络**: 稳定的互联网连接

### 2.2. 软件要求

*   **PHP 版本**: **强制要求 PHP 8.2 或更高版本**。PHP 8.2 为当前稳定且性能优化的版本，支持最新语法特性及安全修复，能有效提升系统执行效率与安全性。
*   **PHP 扩展**: 确保已启用以下关键扩展（完整列表请参照 `public/check.php` 或 `prod.sh`）：
    *   `bcmath`, `bz2`, `calendar`, `Core`, `ctype`, `curl`, `date`, `dba`, `dom`, `exif`
    *   `FFI`, `fileinfo`, `filter`, `ftp`, `gd`, `gettext`, `gmp`, `hash`, `iconv`
    *   `imagick`, `intl`, `json`, `ldap`, `libxml`, `mbstring`, `mysqli`, `mysqlnd`
    *   `odbc`, `openssl`, `pcntl`, `pcre`, `PDO`, `pdo_dblib`, `pdo_mysql`, `pdo_odbc`
    *   `pdo_pgsql`, `pdo_sqlite`, `pgsql`, `Phar`, `posix`, `pspell`, `random`
    *   `readline`, `Reflection`, `session`, `shmop`, `SimpleXML`, `soap`, `sockets`
    *   `sodium`, `SPL`, `sqlite3`, `standard`, `sysvmsg`, `sysvsem`, `sysvshm`, `tidy`
    *   `tokenizer`, `xml`, `xmlreader`, `xmlwriter`, `xsl`, `Zend OPcache`, `zip`, `zlib`
*   **数据库**: MySQL 5.7+ 或 MariaDB 10.2+ (推荐使用 MySQL 8.0 或更高版本以保证高效性能与安全特性)
*   **Web 服务器**: Nginx (推荐) 或 Apache
*   **Node.js**: 16.x 或更高版本 (用于前端资源编译)
*   **Composer**: PHP 依赖管理工具
*   **Git**: 版本控制工具

## 3. 环境准备

### 3.1. 操作系统选择与更新

*   **推荐：** Ubuntu 20.04 LTS 或更高版本
*   **其他支持：** CentOS 8+, RedHat Enterprise Linux 8+

首先，请确保您的操作系统是最新的：

**对于 Ubuntu/Debian 系统：**

```bash
sudo apt update
sudo apt upgrade -y
```

**对于 CentOS/RHEL 系统：**

```bash
sudo yum update -y
# 或者对于较新版本
sudo dnf update -y
```

### 3.2. 安装基础工具

```bash
# Ubuntu/Debian
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# CentOS/RHEL
sudo yum install -y curl wget git unzip epel-release
# 或者
sudo dnf install -y curl wget git unzip epel-release
```

### 3.3. 安装 PHP 8.2

**Ubuntu/Debian 系统：**

```bash
# 添加 PHP 仓库
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

# 安装 PHP 8.2 及必要扩展
sudo apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common php8.2-mysql \
    php8.2-zip php8.2-gd php8.2-mbstring php8.2-curl php8.2-xml php8.2-bcmath \
    php8.2-json php8.2-intl php8.2-soap php8.2-ldap php8.2-imagick php8.2-redis \
    php8.2-opcache php8.2-tokenizer php8.2-fileinfo php8.2-pdo php8.2-sqlite3 \
    php8.2-pgsql php8.2-iconv php8.2-simplexml php8.2-dom php8.2-xmlwriter \
    php8.2-xmlreader php8.2-xsl php8.2-sodium php8.2-posix php8.2-sockets
```

**CentOS/RHEL 系统：**

```bash
# 安装 Remi 仓库
sudo yum install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm
sudo yum module reset php -y
sudo yum module enable php:remi-8.2 -y

# 安装 PHP 8.2 及扩展
sudo yum install -y php php-fpm php-cli php-common php-mysqlnd php-zip \
    php-gd php-mbstring php-curl php-xml php-bcmath php-json php-intl \
    php-soap php-ldap php-imagick php-redis php-opcache php-tokenizer \
    php-fileinfo php-pdo php-sqlite3 php-pgsql php-iconv php-simplexml \
    php-dom php-xmlwriter php-xmlreader php-xsl php-sodium php-posix php-sockets
```

### 3.4. 安装 Composer

```bash
# 下载并安装 Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# 验证安装
composer --version
```

### 3.5. 安装 Node.js

```bash
# 使用 NodeSource 仓库安装 Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 或者使用 NVM (推荐)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

### 3.6. 安装数据库

**安装 MySQL 8.0：**

```bash
# Ubuntu/Debian
sudo apt install -y mysql-server mysql-client

# CentOS/RHEL
sudo yum install -y mysql-server mysql
sudo systemctl enable mysqld
sudo systemctl start mysqld
```

**安全配置 MySQL：**

```bash
sudo mysql_secure_installation
```

### 3.7. 安装 Web 服务器

**安装 Nginx (推荐)：**

```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo yum install -y nginx

# 启动并启用服务
sudo systemctl enable nginx
sudo systemctl start nginx
```

**或者安装 Apache：**

```bash
# Ubuntu/Debian
sudo apt install -y apache2

# CentOS/RHEL
sudo yum install -y httpd

# 启动并启用服务
sudo systemctl enable apache2  # Ubuntu
sudo systemctl enable httpd    # CentOS
sudo systemctl start apache2   # Ubuntu
sudo systemctl start httpd     # CentOS
```

## 4. 部署步骤

### 步骤 1: 获取应用代码

```bash
# 创建应用目录
sudo mkdir -p /var/www/bwms
cd /var/www/bwms

# 克隆代码仓库
sudo git clone https://github.com/your-repo/bwms.git .

# 或者下载并解压发布包
# sudo wget https://github.com/your-repo/bwms/archive/main.zip
# sudo unzip main.zip
# sudo mv bwms-main/* .
```

### 步骤 2: 安装 PHP 依赖

```bash
cd /var/www/bwms

# 安装 Composer 依赖
sudo composer install --no-dev --optimize-autoloader

# 设置正确的文件所有者
sudo chown -R www-data:www-data /var/www/bwms
```

### 步骤 3: 安装前端依赖并编译

```bash
# 安装 npm 依赖
sudo npm install

# 编译前端资源
sudo npm run build

# 或者使用 yarn
# sudo yarn install
# sudo yarn build
```

### 步骤 4: 创建数据库

```bash
# 登录 MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE bwms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'bwms_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON bwms_db.* TO 'bwms_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 步骤 5: 配置环境文件

```bash
cd /var/www/bwms

# 复制环境配置文件
sudo cp .env.example .env

# 编辑配置文件
sudo nano .env
```

更新 `.env` 文件中的关键配置：

```env
APP_NAME="BWMS"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bwms_db
DB_USERNAME=bwms_user
DB_PASSWORD=your_strong_password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### 步骤 6: 生成应用密钥

```bash
cd /var/www/bwms
sudo php artisan key:generate
```

### 步骤 7: 运行环境检查

```bash
# 临时启用调试模式
sudo sed -i 's/APP_DEBUG=false/APP_DEBUG=true/' .env

# 通过浏览器访问 https://yourdomain.com/check.php
# 或者使用命令行检查
php public/check.php

# 检查完成后禁用调试模式
sudo sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' .env
```

### 步骤 8: 设置文件权限

```bash
cd /var/www/bwms

# 设置目录权限
sudo find . -type d -exec chmod 755 {} \;
sudo find . -type f -exec chmod 644 {} \;

# 设置特殊目录权限
sudo chmod -R 775 storage bootstrap/cache
sudo chown -R www-data:www-data storage bootstrap/cache

# 确保 artisan 可执行
sudo chmod +x artisan
```

### 步骤 9: 运行数据库迁移

```bash
cd /var/www/bwms

# 运行数据库迁移
sudo php artisan migrate --force

# 运行数据库种子 (如果需要)
sudo php artisan db:seed --force
```

### 步骤 10: 创建存储链接

```bash
cd /var/www/bwms
sudo php artisan storage:link
```

### 步骤 11: 配置 Web 服务器

**Nginx 配置示例：**

创建 Nginx 站点配置文件：

```bash
sudo nano /etc/nginx/sites-available/bwms
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/bwms/public;
    index index.php index.html index.htm;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 隐藏 Nginx 版本
    server_tokens off;

    # 处理 Laravel 路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 处理 PHP 文件
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 拒绝访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/bwms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

**Apache 配置示例：**

创建 Apache 虚拟主机配置：

```bash
sudo nano /etc/apache2/sites-available/bwms.conf
```

添加以下配置：

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/bwms/public

    <Directory /var/www/bwms/public>
        AllowOverride All
        Require all granted
    </Directory>

    # 安全头
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "no-referrer-when-downgrade"

    # 隐藏服务器信息
    ServerTokens Prod
    ServerSignature Off

    ErrorLog ${APACHE_LOG_DIR}/bwms_error.log
    CustomLog ${APACHE_LOG_DIR}/bwms_access.log combined
</VirtualHost>
```

启用站点和必要模块：

```bash
sudo a2enmod rewrite headers
sudo a2ensite bwms.conf
sudo apache2ctl configtest
sudo systemctl reload apache2
```

### 步骤 12: 安装 SSL 证书

使用 Let's Encrypt 获取免费 SSL 证书：

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx  # Nginx
# 或者
sudo apt install -y certbot python3-certbot-apache  # Apache

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com  # Nginx
# 或者
sudo certbot --apache -d yourdomain.com -d www.yourdomain.com  # Apache

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 步骤 13: 配置 Redis (可选但推荐)

```bash
# 安装 Redis
sudo apt install -y redis-server

# 配置 Redis
sudo nano /etc/redis/redis.conf

# 修改以下配置：
# bind 127.0.0.1
# requirepass your_redis_password

# 重启 Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 步骤 14: 配置队列处理 (可选)

创建队列工作进程的 Supervisor 配置：

```bash
sudo apt install -y supervisor

sudo nano /etc/supervisor/conf.d/bwms-worker.conf
```

添加以下配置：

```ini
[program:bwms-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/bwms/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/bwms/storage/logs/worker.log
stopwaitsecs=3600
```

启动 Supervisor：

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start bwms-worker:*
```

### 步骤 15: 最终测试

```bash
# 清除应用缓存
cd /var/www/bwms
sudo php artisan config:cache
sudo php artisan route:cache
sudo php artisan view:cache

# 测试应用
curl -I https://yourdomain.com
```

## 5. 故障排除

### 5.1. 常见问题

*   **500 Internal Server Error**: 
    - 检查 `.env` 文件配置
    - 查看错误日志：`sudo tail -f /var/log/nginx/error.log` 或 `/var/log/apache2/error.log`
    - 检查 Laravel 日志：`sudo tail -f /var/www/bwms/storage/logs/laravel.log`
    - 验证文件权限

*   **404 Not Found**: 
    - 确认 Web 服务器配置正确
    - 检查 URL 重写规则
    - 验证文档根目录设置

*   **数据库连接错误**: 
    - 验证数据库服务状态：`sudo systemctl status mysql`
    - 检查数据库连接信息
    - 确认数据库用户权限

*   **PHP 扩展缺失**: 
    - 运行 `php -m` 查看已安装扩展
    - 安装缺失的扩展
    - 重启 PHP-FPM：`sudo systemctl restart php8.2-fpm`

### 5.2. 性能优化

```bash
# 启用 OPcache
sudo nano /etc/php/8.2/fpm/php.ini

# 添加或修改以下配置：
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1

# 重启 PHP-FPM
sudo systemctl restart php8.2-fpm
```

## 6. 部署后维护与安全

### 6.1. 核心安全设置

*   **防火墙配置**:
    ```bash
    sudo ufw enable
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'  # 或 'Apache Full'
    sudo ufw allow mysql
    ```

*   **定期安全更新**:
    ```bash
    # 创建自动更新脚本
    sudo nano /etc/cron.daily/security-updates
    
    #!/bin/bash
    apt update && apt upgrade -y
    
    sudo chmod +x /etc/cron.daily/security-updates
    ```

*   **SSH 安全加固**:
    ```bash
    sudo nano /etc/ssh/sshd_config
    
    # 修改以下配置：
    PermitRootLogin no
    PasswordAuthentication no
    PubkeyAuthentication yes
    Port 2222  # 更改默认端口
    
    sudo systemctl restart sshd
    ```

### 6.2. 监控与日志

*   **日志轮转配置**:
    ```bash
    sudo nano /etc/logrotate.d/bwms
    
    /var/www/bwms/storage/logs/*.log {
        daily
        missingok
        rotate 52
        compress
        delaycompress
        notifempty
        create 644 www-data www-data
    }
    ```

*   **系统监控**:
    ```bash
    # 安装监控工具
    sudo apt install -y htop iotop nethogs
    
    # 设置磁盘空间监控
    echo "df -h | mail -s 'Disk Space Report' <EMAIL>" | sudo tee /etc/cron.daily/disk-check
    sudo chmod +x /etc/cron.daily/disk-check
    ```

### 6.3. 备份策略

创建自动备份脚本：

```bash
sudo nano /usr/local/bin/bwms-backup.sh
```

```bash
#!/bin/bash

# 配置变量
BACKUP_DIR="/backup/bwms"
APP_DIR="/var/www/bwms"
DB_NAME="bwms_db"
DB_USER="bwms_user"
DB_PASS="your_strong_password"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# 备份应用文件
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C $APP_DIR .

# 删除 7 天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

设置定时备份：

```bash
sudo chmod +x /usr/local/bin/bwms-backup.sh
sudo crontab -e

# 添加每日凌晨 2 点备份
0 2 * * * /usr/local/bin/bwms-backup.sh >> /var/log/bwms-backup.log 2>&1
```

### 6.4. 性能监控与优化

*   **安装性能监控工具**:
    ```bash
    # 安装 New Relic (可选)
    curl -Ls https://download.newrelic.com/php_agent/scripts/newrelic-install | sudo bash
    
    # 或者使用开源监控
    sudo apt install -y netdata
    sudo systemctl enable netdata
    sudo systemctl start netdata
    ```

*   **数据库优化**:
    ```bash
    # 安装 MySQL Tuner
    sudo apt install -y mysqltuner
    
    # 运行优化建议
    sudo mysqltuner
    ```

## 7. 高可用性部署 (可选)

对于生产环境，建议考虑以下高可用性配置：

### 7.1. 负载均衡

使用 Nginx 作为负载均衡器：

```nginx
upstream bwms_backend {
    server ************:80;
    server ************:80;
    server ************:80;
}

server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://bwms_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 7.2. 数据库集群

配置 MySQL 主从复制或使用 Galera 集群。

### 7.3. 共享存储

使用 NFS 或对象存储服务来共享 `storage` 目录。

## 8. 容器化部署 (Docker)

提供 Docker 部署选项：

```dockerfile
# Dockerfile 示例
FROM php:8.2-fpm

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# 安装 PHP 扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# 安装 Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 设置工作目录
WORKDIR /var/www

# 复制应用代码
COPY . /var/www

# 安装依赖
RUN composer install --no-dev --optimize-autoloader

# 设置权限
RUN chown -R www-data:www-data /var/www

EXPOSE 9000
CMD ["php-fpm"]
```

---

希望这份完善的独立主机部署指南能帮助您成功部署 BWMS 系统！如果在部署过程中遇到问题，请参考故障排除部分或查阅相关文档。
