<?php

namespace Modules\Common\Repository\Options;

use Bingo\BingoStart;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class Components implements OptionInterface
{
    /**
     * @var array|string[]
     */
    protected array $components = [
        [
            'label' => 'layout',
            'value' => '/admin/layout/index.vue'
        ],
        [
            'label' => 'AMisPage',
            'value' => '/admin/views/AMisPage.vue'
        ],
        [
            'label' => 'LaravelPage',
            'value' => '/admin/views/LaravelPage.vue'
        ]
    ];

    public function get(): array
    {
        try {
            if ($module = request()->get('module')) {
                $components = File::glob(BingoStart::getModuleViewsPath($module).'*'.DIRECTORY_SEPARATOR.'*.vue');

                foreach ($components as $component) {
                    $_component = Str::of($component)
                        ->replace(BingoStart::moduleRootPath(), '')
                        ->explode(DIRECTORY_SEPARATOR);
                    $_component->shift(2);

                    $this->components[] = [
                        'label' => Str::of($_component->implode('/'))->replace('.vue', ''),

                        'value' => Str::of($component)->replace(BingoStart::moduleRootPath(), '')->prepend('/')
                    ];
                }
            }

            return $this->components;
        } catch (\Throwable $exception) {
            return [];
        }
    }
}
