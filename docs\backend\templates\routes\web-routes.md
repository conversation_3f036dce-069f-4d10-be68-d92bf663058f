# Web 路由模板

## 概述

Web 路由用于定义模块的 Web 页面路由。本文档提供了 Web 路由的标准模板和最佳实践。

## 基本结构

```php
<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\YourModule\Web\Controllers\YourController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| 模块的 Web 路由定义
|
*/

Route::prefix('your-module')->middleware(['web', 'auth'])->group(function () {
    // 页面路由
    Route::get('/', [YourController::class, 'index'])->name('your-module.index');
    Route::get('/create', [YourController::class, 'create'])->name('your-module.create');
    Route::get('/{id}', [YourController::class, 'show'])->name('your-module.show');
    Route::get('/{id}/edit', [YourController::class, 'edit'])->name('your-module.edit');
    
    // 表单处理路由
    Route::post('/', [YourController::class, 'store'])->name('your-module.store');
    Route::put('/{id}', [YourController::class, 'update'])->name('your-module.update');
    Route::delete('/{id}', [YourController::class, 'destroy'])->name('your-module.destroy');
    
    // 数据处理路由
    Route::prefix('data')->group(function () {
        Route::post('import', [YourController::class, 'import'])->name('your-module.import');
        Route::get('export', [YourController::class, 'export'])->name('your-module.export');
    });
});
```

## 规范要求

1. 路由分组
   - 使用模块前缀
   - 使用适当的中间件
   - 按功能分组路由
   - 使用资源路由

2. 路由命名
   - 使用模块名称前缀
   - 使用连字符分隔
   - 动词在前，名词在后
   - 保持命名一致性

3. 中间件使用
   - Web 中间件
   - 认证中间件
   - 权限中间件
   - CSRF 中间件

4. 参数处理
   - 使用路由参数
   - 参数验证
   - 参数绑定
   - 参数约束

## 最佳实践

1. 资源路由定义
```php
// 完整资源路由
Route::resource('posts', PostController::class);

// 部分资源路由
Route::resource('comments', CommentController::class)
    ->only(['index', 'store', 'destroy']);

// 嵌套资源路由
Route::resource('posts.comments', PostCommentController::class)
    ->shallow();
```

2. 路由参数约束
```php
// 使用正则约束
Route::get('user/{id}', [UserController::class, 'show'])
    ->where('id', '[0-9]+');

// 使用全局约束
Route::pattern('id', '[0-9]+');

// 使用模型绑定
Route::get('posts/{post}', [PostController::class, 'show']);
```

## 常见问题

1. 路由分组
```php
// 好的实践 - 按功能分组
Route::prefix('admin')->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index']);
    Route::resource('users', UserController::class);
});

// 不好的实践 - 路由分散
Route::get('admin/dashboard', [DashboardController::class, 'index']);
Route::resource('admin/users', UserController::class);
```

2. 路由命名
```php
// 好的实践 - 一致的命名规范
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('users', [UserController::class, 'index'])->name('users.index');
    Route::get('users/create', [UserController::class, 'create'])->name('users.create');
});

// 不好的实践 - 不一致的命名
Route::get('admin/users', [UserController::class, 'index'])->name('list_users');
Route::get('admin/users/new', [UserController::class, 'create'])->name('user_create');
```

## 注意事项

1. 避免路由冲突
2. 合理使用路由缓存
3. 注意路由参数安全
4. 保持命名一致性
5. 使用适当的 HTTP 方法
6. 实现必要的路由中间件
